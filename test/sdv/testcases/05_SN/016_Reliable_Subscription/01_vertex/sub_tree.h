extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

#define MAX_NAME_LENGTH 128
#define SUBCHANNEL

GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL, *conn = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sync2 = NULL, *g_stmt_sync3 = NULL, *g_stmt_sync4 = NULL,
         *g_stmt_sync5 = NULL, *g_stmt_sub = NULL, *stmt = NULL;
void *g_label = NULL, *g_label2 = NULL, *g_label3 = NULL, *g_label4 = NULL, *g_label5 = NULL;
char *g_schema = NULL, *g_schema1 = NULL, *g_schema2 = NULL, *g_schema3 = NULL, *g_schema4 = NULL, *g_schema5 = NULL;
char *g_sub_info = NULL;
char *g_sub_info_1 = NULL;
GmcConnT *g_subChan = NULL;
char *g_edgeLabelJson1 = NULL;
char g_label_config[] = "{\"max_record_count\":3000000}";
char g_edgeLabel_config[] = "{\"max_record_count\":3000000}";

char g_labelName1[] = "TEST_T0";
char g_labelName2[] = "TEST_T2";
char g_labelName3[] = "TEST_T3";
char g_labelName4[] = "TEST_T4";
char g_labelName5[] = "TEST_T5";
char g_lableName_PK1[] = "TEST_PK";
char g_lableName_SK1[] = "localhash_key";

char g_labelName8[] = "BIGDATA";
char g_lableName8_PK[] = "pk";

char g_labelName6[] = "T20_all_type";
char g_labelName7[] = "T20_all_type_1";
char g_lableName_PK6[] = "T20_PK";
char g_lableName_PK7[] = "T20_PK_1";

char g_lableName_SK2[] = "localhash_key_non_uniq";
const char *g_edgeLabelName = "edge_1";
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
const char *g_subName_1 = "subTreeLabel";
#define MAX_VERTEX_NUM 10000
#define MAX_NAME_LENGTH 128
int g_subIndex = 0;

AsyncUserDataT asyncUserData = {0};

int g_start_num = 0;
int g_end_num = 100;
int g_array_num = 3;
int g_vector_num = 3;
int affectRows;
unsigned int len;
int g_thread_wait = 0;
int g_thr_num_sn = 10;
int g_thr_num_dml = 1;
pthread_mutex_t g_threadLock;

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *resPoolTestName = "resource_pool_test";

void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        // ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    // printf("f12_value : %c\n", f12_value);
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    // printf("f13_value : %c\n", f13_value);
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        // ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    uint64_t f1_value;

    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f20_value);

    if (isBitmap) {
        // uint8_t bits[128/8];
        // memset(bits, 0, 128/8); // test bitmap value
        // bits[128 / 8 - 1] = '\0';
        // ret = GmcNodeGetPropertySizeByName(node, "F22", &propSize);
        // AW_MACRO_EXPECT_EQ_INT(propSize, 128);
        // ret = GmcNodeGetPropertyByName(node, "F22", bits, propSize, &isNull);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT((uint32_t)0, isNull);
        // for(int i = 0; i < 128/8 - 1; i++) {
        //    AW_MACRO_EXPECT_EQ_INT(0x0, *(bits + i));
        //}
    }
}

void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P17", &f17_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P18", &f18_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P19", &f19_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P20", &f20_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f20_value);

    if (isBitmap) {
        // uint8_t bits[128/8];
        // memset(bits, 0, 128/8); // test bitmap value
        // bits[128 / 8 - 1] = '\0';
        // ret = GmcNodeGetPropertySizeByName(node, "F22", &propSize);
        // AW_MACRO_EXPECT_EQ_INT(propSize, 128);
        // ret = GmcNodeGetPropertyByName(node, "F22", bits, propSize, &isNull);
        // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT((uint32_t)0, isNull);
        // for(int i = 0; i < 128/8 - 1; i++) {
        //    AW_MACRO_EXPECT_EQ_INT(0x0, *(bits + i));
        //}
    }
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;

    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    AW_MACRO_EXPECT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", string_value, propSize, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", string_value, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    uint8_t f21_value = (i + g_end_num * index) & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value, isBitmap);
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入array节点
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
}

void TestGmcInsertVertexWithRes(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    uint8_t f21_value = (i + g_end_num * index) & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value, isBitmap);
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入array节点
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
}
/*
void TestGmcInsertVertexBatch(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num, int
vector_num)
{
    int32_t ret = 0;
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);

    uint8_t f21_value = index & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }

    ret = GmcBatchAddVertexDML(stmt,GMC_CMD_INSERT_VERTEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
*/
void TestGmcInsertVertexAsync(
    GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num, int vector_num, char *label_name)
{
    int32_t ret = 0;
    AsyncUserDataT asyncUserData = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);

    uint8_t f21_value = (i + g_end_num * index) & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncUserData;
    ret = GmcExecuteAsync(stmt, &insertRequestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncUserData.status);
    AW_MACRO_EXPECT_EQ_INT(1, asyncUserData.affectRows);
}

void TestGmcInsertVertexSuperfiled(
    GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int array_num, int vector_num, char *label_name)
{
    int32_t ret = 0;
    void *label = NULL;
    char *sp_1 = (char *)malloc(26);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, index);
    TestGmcNodeSetPropertyByName_R(root, index, bool_value, f14_value);

    uint8_t f21_value = index & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // set superfiled by name
    char *temp = sp_1;
    *(int64_t *)(temp) = index;
    *(uint64_t *)(temp + 8) = index;
    *(int32_t *)(temp + 16) = index;
    *(uint32_t *)(temp + 20) = index;
    *(int16_t *)(temp + 24) = index & 0x7FFF;

    ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = index & 0xFFFF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = index & 0x7F;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = index & 0xFF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float f9_value = (float)index;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double f10_value = (double)index;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = index;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f12_value = 'a' + (index & 0x1A);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (index & 0x1A);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f17_value = index & 0xF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f18_value = index & 0xF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f19_value = index & 0xFF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f20_value = index & 0xFFFF;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
    uint8_t bits[128 / 8];
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P22", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f5_value = index & 0xFFFF;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f6_value = index & 0x7F;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f7_value = index & 0xFF;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f8_value = bool_value;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f9_value = index;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f10_value = index;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f11_value = index;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f12_value = 'a' + (index & 0x1A);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f13_value = 'A' + (index & 0x1A);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeGetNextElement(t2, &t2);
    }

    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f5_value = index & 0xFFFF;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f6_value = index & 0x7F;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f7_value = index & 0xFF;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f8_value = bool_value;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f9_value = index;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f10_value = index;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f11_value = index;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f12_value = 'a' + (index & 0x1A);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        f13_value = 'A' + (index & 0x1A);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sp_1);
}

void TestGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    //插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (0 != affectRows) {
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    } else {
        printf("affectRows = 0,update failed\n");
    }
}

/*
void TestGmcUpdateVertexByIndexKeyBatch(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
        int array_num, int vector_num, char *keyName)
{
    int32_t ret = 0;
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt,keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddVertexDML(stmt,GMC_CMD_UPDATE_VERTEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

*/
void TestGmcUpdateVertexByIndexKeyAsync(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    AsyncUserDataT asyncUserData = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_R(root, i * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(t1, i * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i * index, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i * index, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &asyncUserData;
    ret = GmcExecuteAsync(stmt, &updateRequestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&asyncUserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, asyncUserData.status);
    AW_MACRO_EXPECT_EQ_INT(1, asyncUserData.affectRows);
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num, bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(isFinish, true);
        } else if (read_num == true) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            GmcNodeT *root, *t1, *t2, *t3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &t1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &t3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);
            TestGmcNodeGetPropertyByName_p(t1, i + g_end_num * index, bool_value, f14_value, isBitmap);
            // 读取array节点
            ret = GmcNodeGetChild(t1, "T2", &t2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(t2, j, &t2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
            }
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(t3, j, &t3);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcMergeVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);

    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        ret = GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(affect_rows, affectRows);
}

void TestGmcReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);

    uint8_t f21_value = (i + g_end_num * index) & 0xF;
    ret = GmcNodeSetPropertyByName(root, (char *)"F21", GMC_DATATYPE_PARTITION, &f21_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, i + g_end_num * index, bool_value, f14_value);
        ret = GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(affect_rows, affectRows);
}

void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3, vector_num = 3;
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }

                    //读old
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }

                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_truncate(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3, vector_num = 3;
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
        }
    }
}

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void test_setVertexProperty(GmcStmtT *stmt, int pk = 0)
{
    int ret = 0;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_setVertexProperty_updt_nonpk(GmcStmtT *stmt, int pk = 0, bool is_union_pk = 0, bool is_bytes_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!is_union_pk) {
        int32_t value6 = pk;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (!is_bytes_union_pk) {
        char teststr15[10] = "bytes";
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void sn_callback_relsub_memory(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{

    int pk, i, ret;
    uint32_t PK = 0;  // F7是pk
    unsigned int sizeValue = 0;
    bool isNull;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    // printf("[INFO] vrtxLabelNum is %d\r\n", vrtxLabelNum);

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                // AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                printf("[INFO] <---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            // printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", i, labelName);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[g_subIndex];
                    if (pk == 1) {
                        sleep(30);
                    }
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", pk);
                    // test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
        }
    }
}

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            free(jStr);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    json_decref(data_json);
}
