#include "OldSnInitScanCommon.h"

class OldSubInitialScanKv : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
        AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        EXPECT_EQ(GMERR_OK, ret);
    }
};

// kv表，订阅initLoadScan事件类型，不指定msgType, 默认推送newObj
TEST_F(OldSubInitialScanKv, SN_057_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NO_MSG;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNoMsgType;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 02 kv表，订阅initial_scan事件类型,指定msgType为key
TEST_F(OldSubInitialScanKv, SN_057_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanKey;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 40 kv表，订阅initial_scan事件类型,指定msgType为oldObj
TEST_F(OldSubInitialScanKv, SN_057_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_OLD;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanOld;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041 kv表，订阅initial_scan事件类型,指定msgType为newObj
TEST_F(OldSubInitialScanKv, SN_057_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNew;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042 kv表，订阅initial_scan事件类型,指定msgType为newObj + oldObj
TEST_F(OldSubInitialScanKv, SN_057_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNewOld;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043 kv表，订阅initial_scan事件类型,指定msgType为oldObj + key
TEST_F(OldSubInitialScanKv, SN_057_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_OLD_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanOldKey;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044 kv表，订阅initial_scan事件类型,指定msgType为newObj + key
TEST_F(OldSubInitialScanKv, SN_057_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNewKey;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045 kv表，订阅initial_scan事件类型,指定msgType为newObj + oldObj + key
TEST_F(OldSubInitialScanKv, SN_057_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNewOldKey;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 46 订阅initial_scan事件类型和initia_load_eof
TEST_F(OldSubInitialScanKv, SN_057_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvInitialScanAndEof";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanAndEof;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 47 kv表，订阅initial_scan事件类型和initia_load
TEST_F(OldSubInitialScanKv, SN_057_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvInitialScanAndLoad";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanAndLoad;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class InitScanSubCbInSameThreadKv : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = testEnvClean();
        EXPECT_EQ(GMERR_OK, ret);
    }

    virtual void SetUp()
    {
        int ret = testGmcConnect(&g_connSync, &g_stmtSync);
        ASSERT_EQ(GMERR_OK, ret);

        AW_CHECK_LOG_BEGIN();
        AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        int ret = testGmcDisconnect(g_connSync, g_stmtSync);
        EXPECT_EQ(GMERR_OK, ret);
    }
};

// 48 kv表，订阅initial_scan事件类型和set、delete组合
TEST_F(InitScanSubCbInSameThreadKv, SN_057_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD_KEY;
    const char *subName = "subKvAll";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, addVal);
    while ((userData->kvSetNum != RECORD_COUNT)) {
        int32_t fdCount =
            TEST_EPOLL_WAIT(g_epollData.userEpollFd, g_epollData.events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        for (int32_t i = 0; i < fdCount; i++) {
            GmcHandleRWEvent(g_epollData.events[i].data.fd, g_epollData.events[i].events);
        }
    }
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_SET, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestRemoveKV(g_stmtSync, kvName, RECORD_COUNT, userData, addVal);
    while ((userData->deleteNum != RECORD_COUNT)) {
        int32_t fdCount =
            TEST_EPOLL_WAIT(g_epollData.userEpollFd, g_epollData.events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        for (int32_t i = 0; i < fdCount; i++) {
            GmcHandleRWEvent(g_epollData.events[i].data.fd, g_epollData.events[i].events);
        }
    }
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_KV_REMOVE, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 49 kv表，订阅initial_scan事件类型，is_reliable为True, 订阅回调消费慢
TEST_F(InitScanSubCbInSameThreadKv, SN_057_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 100000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, 100000, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD_KEY;
    const char *subName = "subKvIsReliableTrue";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvIsReliableTrue;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCbUsleep10000, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 50 kv表，订阅initial_scan事件类型，persist为false
TEST_F(InitScanSubCbInSameThreadKv, SN_057_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NO_DATA);
    int ret = system("./CliInitScanExit 2 > CliInitScanPersistFalse.log");

    TestWaitProExit("CliInitScanExit");
    const char *kvName = "KvTable057";
    TestWatiRecycleSubInfo(kvName);
    CheckSystemPrcLog("CliInitScanPersistFalse.log");
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表，订阅initial_scan事件类型，条件订阅equal指定值
TEST_F(InitScanSubCbInSameThreadKv, SN_057_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, userData, 0, true);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NO_MSG;
    const char *subName = "subKvCondition";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitScanCondition;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个订阅链接，订阅多个kv 的initial_scan, 线程并发
TEST_F(InitScanSubCbInSameThreadKv, SN_057_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestOnlySetKV(g_stmtSync, kvName, RECORD_COUNT);

    pthread_t thrSubInitialScan[10];
    uint32_t thrId[10] = {0};
    for (uint32_t i = 0; i < 10; i++) {
        thrId[i] = i;
        ret = pthread_create(&thrSubInitialScan[i], NULL, ThrSubKvFunc, &thrId[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 10; i++) {
        ret = pthread_join(thrSubInitialScan[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);

    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表，kv表先写数据，再订阅initial_scan，然后提交事务，需用同一conn，不然会报错
TEST_F(InitScanSubCbInSameThreadKv, SN_057_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTxConfigT txCfg;
    txCfg.readOnly = false;
    txCfg.transMode = GMC_TRANS_USED_IN_CS;
    txCfg.type = GMC_TX_ISOLATION_COMMITTED;
    txCfg.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_connSync, &txCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(stmt, kvName, RECORD_COUNT, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NO_MSG;
    const char *subName = "subKvCondition";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNoMsgType;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(g_connSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表先写数据，先提交事务，再订阅initial_scan
TEST_F(InitScanSubCbInSameThreadKv, SN_057_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTxConfigT txCfg;
    txCfg.readOnly = false;
    txCfg.transMode = GMC_TRANS_USED_IN_CS;
    txCfg.type = GMC_TX_ISOLATION_COMMITTED;
    txCfg.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &txCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(stmt, kvName, RECORD_COUNT, userData, 0);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NO_MSG;
    const char *subName = "subKvCondition";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNoMsgType;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表先写数据，先提交事务，再订阅initial_scan
TEST_F(InitScanSubCbInSameThreadKv, SN_057_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcTxConfigT txCfg;
    txCfg.readOnly = false;
    txCfg.transMode = GMC_TRANS_USED_IN_CS;
    txCfg.type = GMC_TX_ISOLATION_COMMITTED;
    txCfg.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &txCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(stmt, kvName, RECORD_COUNT, userData, 0);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NO_MSG;
    const char *subName = "subKvCondition";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNoMsgType;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 56 kv表，订阅initial_scan事件类型，反复创建，取消
TEST_F(InitScanSubCbInSameThreadKv, SN_057_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 100000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, 100000, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNewOldKey;
    for (int repeat = 0; repeat < 10; repeat++) {
        AW_FUN_Log(LOG_INFO, "repeat %d", repeat);
        userData->subIndex = 0;
        ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 100000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 取消订阅
        ret = GmcUnSubscribe(g_stmtSync, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    testSnFreeUserData(userData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 57 kv表预10w数据，创建initial_scan订阅过程中，断开通道
TEST_F(InitScanSubCbInSameThreadKv, SN_057_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动epoll
    g_epollData.userEpollFd = TEST_EPOLL_CREATE(MAX_EPOLL_EVENT_COUNT);
    if (g_epollData.userEpollFd < 0) {
        AW_FUN_Log(LOG_ERROR, "TEST_EPOLL_CREATE failed, epollFd : %d.", g_epollData.userEpollFd);
        return;
    }
    g_epollData.events = (TEST_EPOLL_EVENT *)malloc(sizeof(TEST_EPOLL_EVENT) * MAX_EPOLL_EVENT_COUNT);

    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *userData;
    ret = testSnMallocUserData(&userData, 100000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, 100000, userData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvInitLoadScanConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sem_init(&g_sem, 0, 0);
    ThreadCtx2 ctx = {.conn = connSub, .stmt = stmtSub};
    pthread_t thrDisSubConn;
    ret = pthread_create(&thrDisSubConn, NULL, ThreadDisSubConnFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_testSubType = TEST_SUB_NEW_OLD_KEY;
    const char *subName = "subKvInitialScan";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvInitialScanNewOldKey;
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, KvOldSnCallBack, userData);
    sem_post(&g_sem);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(thrDisSubConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    testSnFreeUserData(userData);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 关闭epoll
    TEST_EPOLL_CLOSE(g_epollData.userEpollFd);
    free(g_epollData.events);
    g_epollData.userEpollFd = -1;
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 58 kv表预10w数据，创建initial_scan订阅过程中，客户端SIGKILL退出
TEST_F(InitScanSubCbInSameThreadKv, SN_057_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_NO_DATA);
    int ret = system("./CliInitScanExit 1 > CliInitScanExit.log");

    TestWaitProExit("CliInitScanExit");
    const char *kvName = "KvTable057";
    TestWatiRecycleSubInfo(kvName);
    CheckSystemPrcLog("CliInitScanExit.log");
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 60 GmcSubfetch传入非substmt，传入建表的stmt
TEST_F(InitScanSubCbInSameThreadKv, SN_057_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char *outValue = NULL;
    uint32_t outValueLen = 0;
    ret = GmcKvGetFromStmt(g_stmtSync, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
    TEST_EXPECT_INT32(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 60 GmcSubfetch传入非substmt，传入开表的stmt
TEST_F(InitScanSubCbInSameThreadKv, SN_057_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);

    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 60 GmcSubfetch传入非substmt，传入开表后，Set数据的stmt
TEST_F(InitScanSubCbInSameThreadKv, SN_057_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
    AddWhiteList(GMERR_NO_DATA);
    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *key = "key01";
    const char *val = "val01";
    ret = GmcKvSet(g_stmtSync, key, strlen(key), (const void *)val, strlen(val) + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 62 GmcSubfetch传入非substmt，传入开表后，get数据的stmt
TEST_F(InitScanSubCbInSameThreadKv, SN_057_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);

    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *key = "key01";
    const char *val = "val01";
    ret = GmcKvSet(g_stmtSync, key, strlen(key), (const void *)val, strlen(val) + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char getVal[32] = {0};  // key长度用数组接收
    uint32_t valLen = strlen(val) + 1;
    ret = GmcKvGet(g_stmtSync, key, strlen(key), (void *)getVal, &valLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 63 GmcSubfetch传入非substmt，传入scan 的stmt
TEST_F(InitScanSubCbInSameThreadKv, SN_057_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
    AddWhiteList(GMERR_NO_DATA);

    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = "key01";
    const char *val = "val01";
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvSet(g_stmtSync, key, strlen(key), (const void *)val, strlen(val) + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t limitCount = 0;
    ret = GmcKvScan(g_stmtSync, limitCount);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 64 GmcSubfetch传入非substmt，传入异步的stmt
TEST_F(OldSubInitialScanKv, SN_057_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
    AddWhiteList(GMERR_NO_DATA);

    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    int ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSubSetFetchMode(stmtAsync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(stmtAsync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(stmtAsync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(stmtAsync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char *outValue = NULL;
    uint32_t outValueLen = 0;
    ret = GmcKvGetFromStmt(stmtAsync, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 65 GmcSubfetch传入非substmt，GmcKvInputToStmt后的stmt
TEST_F(OldSubInitialScanKv, SN_057_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AddWhiteList(GMERR_WRONG_STMT_OBJECT);
    AddWhiteList(GMERR_NO_DATA);

    // 建表后的stmt
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":100000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = "key01";
    const char *val = "val01";
    ret = GmcKvPrepareStmtByLabelName(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvInputToStmt(g_stmtSync, key, strlen(key), val, strlen(val) + 1);

    ret = GmcSubSetFetchMode(g_stmtSync, GMC_SUB_FETCH_NEW);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    ret = GmcSubGetLabelName(g_stmtSync, 0, labelName, &labelNameLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    const char *outKey = 0;
    uint32_t outKeyLen = MAX_NAME_LENGTH;
    ret = GmcSubGetKey(g_stmtSync, (const void **)&outKey, &outKeyLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint32_t msgType = 0;
    ret = GmcSubGetMsgType(g_stmtSync, &msgType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076 kv订阅，initial_scan和增量，触发initial_scan过程中，同时触发增量订阅，回调中加全局锁，测callback是否可以重入
TEST_F(OldSubInitialScanKv, SN_057_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *snData;
    ret = testSnMallocUserData(&snData, RECORD_COUNT_10000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT_10000, snData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvAll";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll;
    TestUserDefineDataT userData = {snData, NULL, NULL, 0, NULL};
    ThreadCtx3 ctx = {.begin = 0, .end = RECORD_COUNT_10000, .addVal = RECORD_COUNT_10000, .data = &userData};
    pthread_t thrDml;
    ret = pthread_create(&thrDml, NULL, ThreadUpdateKvFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCbKv, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(snData, GMC_SUB_EVENT_INITIAL_LOAD, RECORD_COUNT_10000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(thrDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WAIT_WHILE_TIMEOUT(snData->kvSetNum != RECORD_COUNT_10000, 5);
    EXPECT_LE(snData->kvSetNum, RECORD_COUNT_10000);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(snData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 077 kv订阅initial_scan和增量，触发initial_scan过程中，同时触发增量订阅,回调中在消费部分数据后，取消订阅
TEST_F(OldSubInitialScanKv, SN_057_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *snData;
    ret = testSnMallocUserData(&snData, RECORD_COUNT_10000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT_10000, snData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvAll";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUserDefineDataT userData = {snData, syncConn, syncStmt, 0, subName};
    ThreadCtx3 ctx = {.begin = 0, .end = RECORD_COUNT_10000, .addVal = RECORD_COUNT_10000, .data = &userData};
    pthread_t thrDml;
    ret = pthread_create(&thrDml, NULL, ThreadUpdateKvFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCbKvUnsub, &userData);
    WAIT_WHILE_TIMEOUT(snData->scanNum < 8000, 5);
    EXPECT_GE(snData->scanNum, 8000);

    ret = pthread_join(thrDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(snData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 078 kv订阅initial_scan和增量，触发initial_scan过程中，同时触发增量订阅,回调中获取substmt，写入其他kv表
TEST_F(OldSubInitialScanKv, SN_057_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *kvName2 = "Kv2Table057";
    ret = GmcKvCreateTable(g_stmtSync, kvName2, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *snData;
    ret = testSnMallocUserData(&snData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, snData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvAll";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUserDefineDataT userData = {snData, syncConn, syncStmt, 0, subName};
    ThreadCtx3 ctx = {.begin = 0, .end = RECORD_COUNT, .addVal = RECORD_COUNT, .data = &userData};
    pthread_t thrDml;
    ret = pthread_create(&thrDml, NULL, ThreadUpdateKvFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCbWriteKv, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WAIT_WHILE_TIMEOUT(snData->scanNum != RECORD_COUNT, 5);
    EXPECT_LE(snData->scanNum, RECORD_COUNT);

    ret = pthread_join(thrDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WAIT_WHILE_TIMEOUT(snData->kvSetNum != RECORD_COUNT, 5);
    EXPECT_LE(snData->kvSetNum, RECORD_COUNT);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(snData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestScanKV(g_stmtSync, kvName2, RECORD_COUNT, RECORD_COUNT);
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTruncateTable(g_stmtSync, kvName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 079 kv订阅initial_load和增量，触发initial_load过程中，同时触发增量订阅,回调中在消费部分数据后，取消订阅
TEST_F(OldSubInitialScanKv, SN_057_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *snData;
    ret = testSnMallocUserData(&snData, RECORD_COUNT_10000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT_10000, snData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvAll1";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll1;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUserDefineDataT userData = {snData, syncConn, syncStmt, 8000, subName};
    ThreadCtx3 ctx = {.begin = 0, .end = RECORD_COUNT_10000, .addVal = RECORD_COUNT_10000, .data = &userData};
    pthread_t thrDml;
    ret = pthread_create(&thrDml, NULL, ThreadUpdateKvFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCbKvUnsub, &userData);
    WAIT_WHILE_TIMEOUT(snData->scanNum < 8000, 5);
    EXPECT_GE(snData->scanNum, 8000);

    ret = pthread_join(thrDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(snData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 080 kv订阅initial_load和增量，触发initial_load过程中，同时触发增量订阅,回调中获取substmt，写入其他kv表
TEST_F(OldSubInitialScanKv, SN_057_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
    const char *kvName = "KvTable057";
    const char *kvConfig = "{\"max_record_count\":10000}";
    int ret = GmcKvCreateTable(g_stmtSync, kvName, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *kvName2 = "Kv2Table057";
    ret = GmcKvCreateTable(g_stmtSync, kvName2, kvConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t addVal = 0;
    SnUserDataT *snData;
    ret = testSnMallocUserData(&snData, RECORD_COUNT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    TestSetKV(g_stmtSync, kvName, RECORD_COUNT, snData, 0);

    // 创建老订阅
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    const char *subConnName = "subKvConn";
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subName = "subKvAll1";
    GmcSubConfigT subInfo;
    subInfo.subsName = subName;
    subInfo.configJson = g_subKvAll1;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    ret = testGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUserDefineDataT userData = {snData, syncConn, syncStmt, 0, subName};
    ThreadCtx3 ctx = {.begin = 0, .end = RECORD_COUNT, .addVal = RECORD_COUNT, .data = &userData};
    pthread_t thrDml;
    // initial_load和set 并发，initial_load 可能拿到老数据，然后被丢弃掉，导致initial load退送次数没有10次
    ret = pthread_create(&thrDml, NULL, ThreadUpdateKvFunc, &ctx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSubscribe(g_stmtSync, &subInfo, connSub, OldSnCbWriteKv, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WAIT_WHILE_TIMEOUT(snData->scanNum != RECORD_COUNT, 5);
    EXPECT_LE(snData->scanNum, RECORD_COUNT);
    WAIT_WHILE_TIMEOUT(snData->kvSetNum != RECORD_COUNT, 5);
    EXPECT_LE(snData->kvSetNum, RECORD_COUNT);

    ret = pthread_join(thrDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(snData, GMC_SUB_EVENT_KV_SET, RECORD_COUNT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmtSync, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(snData);
    // 断开订阅通道
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestScanKV(g_stmtSync, kvName2, RECORD_COUNT, RECORD_COUNT);
    ret = GmcKvTruncateTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvTruncateTable(g_stmtSync, kvName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmtSync, kvName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
