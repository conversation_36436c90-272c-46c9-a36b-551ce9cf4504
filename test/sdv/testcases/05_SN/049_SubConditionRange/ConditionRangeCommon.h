/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: ConditionRangeCommon.h
 * Author: gwx620465
 * Create: 2024-02-18
 */
#ifndef CONDITION_RANGE_COMMON_H
#define CONDITION_RANGE_COMMON_H

#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
#define FILED_FIX_SIZE 5
#define FILED_STRING_SIZE 10
#define FILED_BYTES_SIZE 10
#define FILED_BITMAP_SIZE 16
#define MAX_NAME_LENGTH 128
#define PARTITION_NUM 10

uint32_t g_recordCount = 200;

pthread_barrier_t g_barrier;
GmcConnT *g_connSync = NULL;
GmcStmtT *g_stmtSync = NULL;
GmcConnT *g_connNewSub = NULL;
GmcStmtT *g_stmtNewSub = NULL;
GmcConnT *g_connOldSub = NULL;
GmcStmtT *g_stmtOldSub = NULL;
const char *g_schemaPath = "schemaFile/AllTypeSchemaV1.gmjson";
const char *g_labelName = "SN049AllTypeV1";
char g_labelConfig[] = "{\"max_record_count\":1000000, \"status_merge_sub\":true, \"auto_increment\":1}";
char g_lablePk[] = "pk";
char g_generalName[MAX_NAME_LENGTH] = "new_sub_general";
char g_specialName[MAX_NAME_LENGTH] = "new_sub_special";
char g_generalNameConfig[] = "{\"defragmentation\":false, \"status_merge_sub\": true}";
char g_specialNameConfig[] = "{\"defragmentation\":false, \"status_merge_sub\": true, \"auto_increment\": 1}";
static const char *g_resourceSchemaJson =
    R"([{
        "type":"record",
        "name":"SN049ResV2",
        "subs_type":"status_merge",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"SN049ResV2",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

void TestSetVertexPkProperty(GmcStmtT *stmt, int pk)
{
    int ret;
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestSetVertexOtherProperty(GmcStmtT *stmt, int pk, int addVal = 0)
{
    int ret;
    const char *string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwx";
    EXPECT_EQ(50, strlen(string));
    char teststr0 = string[(pk + addVal) % strlen(string)];
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = string[(pk + addVal) % strlen(string)];
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = (1 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = (10 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = (100 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = (pk + addVal) % 2;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)(pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk + addVal;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[FILED_STRING_SIZE + 1] = {0};
    ret = snprintf_s(teststr14, FILED_STRING_SIZE + 1, FILED_STRING_SIZE, "d%09d", (pk + addVal) % 10);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk + addVal);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t value18 = (1000 + pk) % PARTITION_NUM;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_PARTITION, &value18, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value20 = (1000 + pk) % 1024;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_BITFIELD16, &value20, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f21Bits[2] = {0xfe, 0xff};
    GmcBitMapT value21 = {0};
    value21.beginPos = 0;
    value21.endPos = FILED_BITMAP_SIZE - 1;
    value21.bits = f21Bits;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_BITMAP, &value21, sizeof(value21));
    EXPECT_EQ(GMERR_OK, ret);
}

void TestCheckVertexOtherProperty(GmcStmtT *stmt, int pk, int addVal = 0)
{
    int ret;
    const char *string = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwx";
    char teststr0 = string[(pk + addVal) % strlen(string)];
    unsigned char teststr1 = string[(pk + addVal) % strlen(string)];
    int8_t value2 = (1 + pk + addVal);
    uint8_t value3 = (10 + pk + addVal);
    int16_t value4 = (100 + pk + addVal);
    uint16_t value5 = (1000 + pk + addVal);
    int32_t value6 = pk + addVal;
    bool value8 = (pk + addVal) % 2;
    int64_t value9 = 1000 + pk + addVal;
    uint64_t value10 = 1000 + pk + addVal;
    float value11 = (float)1.2 + (float)(pk + addVal);
    double value12 = 10.86 + pk + addVal;
    uint64_t value13 = 1000 + pk + addVal;
    char teststr14[FILED_STRING_SIZE + 1] = {0};
    ret = snprintf_s(teststr14, FILED_STRING_SIZE + 1, FILED_STRING_SIZE, "d%09d", (pk + addVal) % 10);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    uint32_t value17 = (1000 + pk + addVal);

    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    uint8_t value18 = (1000 + pk) % PARTITION_NUM;
    uint16_t value20 = (1000 + pk) % 1024;
    uint8_t f21Bits[2] = {0xfe, 0xff};
    GmcBitMapT value21 = {0};
    value21.beginPos = 0;
    value21.endPos = FILED_BITMAP_SIZE - 1;
    value21.bits = f21Bits;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_PARTITION, &value18);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_BITFIELD16, &value20);
    EXPECT_EQ(GMERR_OK, ret);
}

int TestCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    EXPECT_EQ(GMERR_OK, ret);
    if (affect != expect) {
        AW_FUN_Log(LOG_INFO, "checke effect rows failed, expect is %d, actual is %d", expect, affect);
        return -1;
    }
    return ret;
}

void TestScanAndCheckVertexProperty(GmcStmtT *stmt, const char *label, const char *lablePk, int pk, int addVal = 0)
{
    int ret;

    uint32_t value7 = pk;  // F7是pk
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, lablePk);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        TestCheckVertexOtherProperty(stmt, value7, addVal);
    }
}

void TestCheckSubStmt(GmcStmtT *subStmt, GmcSubFetchModeE mode, SnUserDataT *userData)
{
    SnUserDataT *data = (SnUserDataT *)userData;

    bool isNull = false;
    int ret = 0;
    uint32_t f7Val = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "F7", &f7Val, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    AW_FUN_Log(LOG_INFO, "F7 %d", f7Val);

    int pk = 0;
    if (mode == GMC_SUB_FETCH_NEW) {
        pk = ((int *)data->new_value)[f7Val];
    } else {
        pk = ((int *)data->old_value)[f7Val];
    }

    int addVal = data->subIndex;
    TestCheckVertexOtherProperty(subStmt, pk, addVal);
}

void NewSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, data);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_OLD, data);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, data);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void TestSetPkValueTabelBig(GmcStmtT *stmt, uint32_t index)
{
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &index, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetOtherValueTabelBig(GmcStmtT *stmt, uint32_t indexVal, uint32_t addVal = 0)
{
    int ret = 0;
    char stringVal[65536] = {0};
    ret = snprintf_s(stringVal, 65536, 65535, "d%065534d", indexVal + addVal);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, stringVal, 65535);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f16Val[32769] = {0};
    ret = snprintf_s(f16Val, 32769, 32768, "d%032767d", indexVal + addVal);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_STRING, f16Val, 32768);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCheckOtherPropertyTableBig(GmcStmtT *stmt, int pk, int addVal = 0)
{
    int ret;
    char stringVal[65536] = {0};
    ret = snprintf_s(stringVal, 65536, 65535, "d%065534d", pk + addVal);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    char f16Val[32769] = {0};
    ret = snprintf_s(f16Val, 32769, 32768, "d%032767d", pk + addVal);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_STRING, stringVal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_STRING, f16Val);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestCheckSubStmtTableBig(GmcStmtT *subStmt, GmcSubFetchModeE mode, SnUserDataT *userData)
{
    SnUserDataT *data = (SnUserDataT *)userData;

    bool isNull = false;
    int ret = 0;
    uint32_t f0Val = 0;
    ret = GmcGetVertexPropertyByName(subStmt, "F0", &f0Val, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    AW_FUN_Log(LOG_INFO, "F0 %d", f0Val);

    int pk = 0;
    if (mode == GMC_SUB_FETCH_NEW) {
        pk = ((int *)data->new_value)[f0Val];
    } else {
        pk = ((int *)data->old_value)[f0Val];
    }

    int addVal = data->subIndex;
    TestCheckOtherPropertyTableBig(subStmt, pk, addVal);
}

void NewSnCallBackTableBig(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmtTableBig(subStmt, GMC_SUB_FETCH_NEW, data);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmtTableBig(subStmt, GMC_SUB_FETCH_OLD, data);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmtTableBig(subStmt, GMC_SUB_FETCH_NEW, data);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d  .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

#define GENERAL_NAME_FIELD_FIXED_LEN 6
#define GENERAL_MACADDRESS_FIXED_LEN 6
#define GENERAL_DESC_FIXED_LEN 6
#define GENERAL_IPV6_FIXED_LEN 16
#define GENERAL_V3_FIXED_LEN 16
#define GENERAL_V4_STRING_LEN 10
#define GENERAL_T1_V_ITEM_NUM 3
#define GENERAL_UPDATE_VALUE 200

char g_generalPkName[] = "ifindex_pk";
char g_generalLocalName[] = "local_key";
char g_generalLocalhashName[] = "vrid_localhash";
char g_generalHashclusterName[] = "name_hashcluster";
char g_generalLpm4Name[] = "lpm4_key";

void TestSetGeneralNodeRoot(GmcNodeT *node, uint32_t i)
{
    uint32_t ret = 0;

    uint32_t ifindexValue = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"ifindex", GMC_DATATYPE_UINT32, &ifindexValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char nameValue[GENERAL_NAME_FIELD_FIXED_LEN + 1] = {0};
    ret =
        snprintf_s(nameValue, GENERAL_NAME_FIELD_FIXED_LEN + 1, GENERAL_NAME_FIELD_FIXED_LEN, "n%05d", (i + 1) % 99999);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"name", GMC_DATATYPE_FIXED, nameValue, GENERAL_NAME_FIELD_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t vridValue = i + 2;
    ret = GmcNodeSetPropertyByName(node, (char *)"vrid", GMC_DATATYPE_UINT32, &vridValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t ifTypeValue = i + 3;
    ret = GmcNodeSetPropertyByName(node, (char *)"if_type", GMC_DATATYPE_UINT32, &ifTypeValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t shutdownValue = i + 4;
    ret = GmcNodeSetPropertyByName(node, (char *)"shutdown", GMC_DATATYPE_UINT32, &shutdownValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t linkupValue = i + 5;
    ret = GmcNodeSetPropertyByName(node, (char *)"linkup", GMC_DATATYPE_UINT32, &linkupValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t tbtpValue = i + 6;
    ret = GmcNodeSetPropertyByName(node, (char *)"tbtp", GMC_DATATYPE_UINT32, &tbtpValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t tbValue = i + 7;
    ret = GmcNodeSetPropertyByName(node, (char *)"tb", GMC_DATATYPE_UINT32, &tbValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t tpValue = i + 8;
    ret = GmcNodeSetPropertyByName(node, (char *)"tp", GMC_DATATYPE_UINT32, &tpValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t portSwitchValue = i + 9;
    ret =
        GmcNodeSetPropertyByName(node, (char *)"port_switch", GMC_DATATYPE_UINT32, &portSwitchValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t forwardTypeValue = i + 10;
    ret =
        GmcNodeSetPropertyByName(node, (char *)"forwardType", GMC_DATATYPE_UINT32, &forwardTypeValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char macAddressValue[GENERAL_MACADDRESS_FIXED_LEN + 1] = {0};
    ret = snprintf_s(
        macAddressValue, GENERAL_MACADDRESS_FIXED_LEN + 1, GENERAL_MACADDRESS_FIXED_LEN, "m%05d", (i + 11) % 10000);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        node, (char *)"macAddress", GMC_DATATYPE_FIXED, macAddressValue, GENERAL_MACADDRESS_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint16_t ipv4MtuValue = (i + 12) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv4_mtu", GMC_DATATYPE_UINT16, &ipv4MtuValue, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint16_t ipv6MtuValue = (i + 13) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv6_mtu", GMC_DATATYPE_UINT16, &ipv6MtuValue, sizeof(uint16_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t onboardValue = i + 14;
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &onboardValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t lagidValue = i + 15;
    ret = GmcNodeSetPropertyByName(node, (char *)"lagid", GMC_DATATYPE_UINT32, &lagidValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t vrid = 2;
    ret = GmcNodeSetPropertyByName(node, (char *)"vr_id", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t vrfIndexValue = 2;
    ret = GmcNodeSetPropertyByName(node, (char *)"vrf_index", GMC_DATATYPE_UINT32, &vrfIndexValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t destIpAddrValue = ((i + 18) << 8);
    ret =
        GmcNodeSetPropertyByName(node, (char *)"dest_ip_addr", GMC_DATATYPE_UINT32, &destIpAddrValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint8_t maskLenValue = ((24) & 0xff);
    ret = GmcNodeSetPropertyByName(node, (char *)"mask_len", GMC_DATATYPE_UINT8, &maskLenValue, sizeof(uint8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestSetGeneralNodeIfm(GmcNodeT *node, uint32_t index)
{
    uint32_t ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    uint32_t value6 = 7 * index;

    ret = GmcNodeSetPropertyByName(node, (char *)"simple_name", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    char descriptionValue[GENERAL_DESC_FIXED_LEN + 1] = {0};
    ret = snprintf_s(descriptionValue, GENERAL_DESC_FIXED_LEN + 1, GENERAL_DESC_FIXED_LEN, "d%05d", index % 100);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        node, (char *)"description", GMC_DATATYPE_FIXED, descriptionValue, GENERAL_DESC_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"is_configure", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"main_ifindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_max_num", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_curr_num", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"error_down", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"statistic", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsys_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"zone_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_up_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_down_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestSetGeneralNodeDev(GmcNodeT *node, uint32_t index)
{
    uint32_t ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"dev_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"chassis_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"slot_id", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"card_id", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"unit_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"port_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestSetGeneralNodeL2(GmcNodeT *node, uint32_t index)
{
    uint32_t ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"trunk_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vlan_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"l2_portindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsi", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tunnel_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestSetGeneralNodePort(GmcNodeT *node, uint32_t index)
{
    uint32_t ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"speed", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"duplex", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"flow_control", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"jumbo", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"baud", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"rmon", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"phy_link", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"if_mib", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestSetGeneralNodeT1V(GmcNodeT *node, uint32_t index)
{
    uint32_t ret = 0;
    uint32_t value = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
    char v3Value[GENERAL_V3_FIXED_LEN + 1] = {0};
    ret = snprintf_s(v3Value, GENERAL_V3_FIXED_LEN + 1, GENERAL_V3_FIXED_LEN, "d%015d", index % 100);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_FIXED, &v3Value, GENERAL_V3_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
    char v4Value[GENERAL_V4_STRING_LEN + 1] = {0};
    ret = snprintf_s(v4Value, GENERAL_V4_STRING_LEN + 1, GENERAL_V4_STRING_LEN, "d%09d", index % 100);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_STRING, &v4Value, GENERAL_V4_STRING_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void TestGetGeneralAllNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **ifmN, GmcNodeT **devN, GmcNodeT **l2N,
    GmcNodeT **portN, GmcNodeT **t1VN)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL, *node4 = NULL, *node5 = NULL;
    *root = NULL;
    *ifmN = NULL;
    *devN = NULL;
    *l2N = NULL;
    *portN = NULL;
    *t1VN = NULL;
    // 获取根节点与子节点
    uint32_t ret = GmcGetRootNode(stmt, &Root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "ifm", &node1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "dev", &node2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "l2", &node3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "port", &node4);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1_V", &node5);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    *root = Root;
    *ifmN = node1;
    *devN = node2;
    *l2N = node3;
    *portN = node4;
    *t1VN = node5;
}

void TestSetGeneralStmt(GmcStmtT *stmt, uint32_t i)
{
    uint32_t ret = 0;

    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    TestGetGeneralAllNode(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
    TestSetGeneralNodeRoot(root, i);
    TestSetGeneralNodeIfm(ifmN, i);
    TestSetGeneralNodeDev(devN, i);
    TestSetGeneralNodeL2(l2N, i);
    TestSetGeneralNodePort(portN, i);

    uint32_t item_num = GENERAL_T1_V_ITEM_NUM;
    GmcNodeT *itemN = NULL;
    for (uint32_t j = 0; j < item_num; j++) {
        ret = GmcNodeAppendElement(T1_VN, &itemN);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TestSetGeneralNodeT1V(itemN, j);
    }
}

void TestSetGeneralUpdateField(GmcStmtT *stmt, uint32_t i, uint32_t updateValue)
{
    uint32_t ret = 0;
    GmcNodeT *root_node = NULL;

    ret = GmcGetRootNode(stmt, &root_node);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    uint32_t tbtpValue = i + 6 + updateValue;
    ret = GmcNodeSetPropertyByName(root_node, (char *)"tbtp", GMC_DATATYPE_UINT32, &tbtpValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t tbValue = i + 7 + updateValue;
    ret = GmcNodeSetPropertyByName(root_node, (char *)"tb", GMC_DATATYPE_UINT32, &tbValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t tpValue = i + 8 + updateValue;
    ret = GmcNodeSetPropertyByName(root_node, (char *)"tp", GMC_DATATYPE_UINT32, &tpValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t port_switchValue = i + 9 + updateValue;
    ret = GmcNodeSetPropertyByName(
        root_node, (char *)"port_switch", GMC_DATATYPE_UINT32, &port_switchValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t forwardTypeValue = i + 10 + updateValue;
    ret = GmcNodeSetPropertyByName(
        root_node, (char *)"forwardType", GMC_DATATYPE_UINT32, &forwardTypeValue, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    char macAddressValue[GENERAL_MACADDRESS_FIXED_LEN + 1] = {0};
    ret = snprintf_s(macAddressValue, GENERAL_MACADDRESS_FIXED_LEN + 1, GENERAL_MACADDRESS_FIXED_LEN, "m%05d",
        (i + 11 + updateValue) % 10000);
    if (ret != -1) {
        ret = GMERR_OK;
    }
    TEST_ASSERT_INT32(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(
        root_node, (char *)"macAddress", GMC_DATATYPE_FIXED, macAddressValue, GENERAL_MACADDRESS_FIXED_LEN);
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

#define SPECIAL_BITMAP_LEN 8
#define SPECIAL_FIXED_LEN6 6
#define SPECIAL_STRING_LEN6 6
#define SPECIAL_STRING_LEN1024 1024
#define SPECIAL_BYTES_LEN6 6
#define SPECIAL_BYTES_MAX_LEN (64 * 1024)
#define SPECIAL_T2_ITEM_NUM 3
#define SPECIAL_T3_ITEM_NUM 3
#define SPECIAL_T1_V_ITEM_NUM 3
#define SPECIAL_UPDATE_VALUE 200

#pragma pack(1)
typedef struct {
    uint32_t a1;
    uint16_t a2Len;
    uint8_t *a2;
} SpecialNodet3T;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint16_t a1;
    uint64_t a2;
    float a3;
    int8_t a4[SPECIAL_BITMAP_LEN / 8];
    uint16_t a5Len;
    uint8_t *a5;
    uint16_t t3Count;
    SpecialNodet3T *t3;
} SpecialNodet2T;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint64_t r1;
    int64_t r2;
    int8_t r3[SPECIAL_FIXED_LEN6];
    uint16_t t2Count;
    SpecialNodet2T *t2;
} SpecialNodet1T;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint32_t v1;
    uint32_t v2;
    int8_t v3[SPECIAL_BITMAP_LEN / 8];
    uint16_t v4Len;
    uint8_t *v4;
} SpecialNodet1vT;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint64_t f1;
    int8_t f2[SPECIAL_FIXED_LEN6];
    uint8_t f3;  // 分区字段
    int32_t f4;
    uint32_t f5;  // 自增列
    int8_t f6;
    uint32_t f7;
    uint32_t f8;
    uint32_t f9;
    uint8_t f10;
    bool f11;
    float f12;
    double f13;
    uint64_t f14;
    uint8_t f15[SPECIAL_BITMAP_LEN / 8];
    uint32_t f16;
    uint16_t f17Len;
    uint8_t *f17;
    uint16_t t1Flag;
    SpecialNodet1T *t1;
    uint16_t t1vCount;
    SpecialNodet1vT *t1v;
} SpecialRootT;
#pragma pack()

void TestSpecialMalloc(SpecialRootT *root, SpecialNodet1T *t1, SpecialNodet2T *t2, SpecialNodet3T *t3,
    SpecialNodet1vT *t1v, uint16_t t2Count, uint16_t t3Count, uint16_t t1vCount)
{
    // array T2 的变长字段 分配内存
    for (int i = 0; i < t2Count; ++i) {
        t2[i].a5Len = SPECIAL_STRING_LEN1024;
        t2[i].a5 = (uint8_t *)malloc(SPECIAL_STRING_LEN1024 + 1);
        EXPECT_NE((void *)NULL, t2[i].a5);
        t2[i].t3 = &t3[i * t3Count];
        // array T3 的变长字段 分配内存
        for (int j = 0; j < t3Count; j++) {
            t3[i * t2Count + j].a2Len = SPECIAL_BYTES_LEN6;
            t3[i * t2Count + j].a2 = (uint8_t *)malloc(SPECIAL_BYTES_LEN6 + 1);
            AW_MACRO_EXPECT_NOTNULL(t3[i * t2Count + j].a2);
        }
    }

    // vector 的变长字段 分配内存
    for (int i = 0; i < t1vCount; ++i) {
        t1v[i].v4Len = SPECIAL_STRING_LEN6;
        t1v[i].v4 = (uint8_t *)malloc(SPECIAL_STRING_LEN6 + 1);
        AW_MACRO_EXPECT_NOTNULL(t1v[i].v4);
    }

    uint8_t *f17Vlaue = (uint8_t *)malloc(SPECIAL_BYTES_LEN6 + 1);
    if (f17Vlaue == NULL) {
        return;
    };
    t1->t2 = &t2[0];
    root->t1 = t1;
    root->t1v = t1v;
    root->f17 = f17Vlaue;
}

void TestSpecialFree(SpecialRootT *root, uint16_t t2Count, uint16_t t3Count, uint16_t t1vCount)
{
    for (int i = 0; i < t2Count; i++) {
        for (int j = 0; j < t3Count; j++) {
            if (root->t1->t2[i].t3[j].a2) {
                free(root->t1->t2[i].t3[j].a2);
            }
        }
        if (root->t1->t2[i].a5) {
            free(root->t1->t2[i].a5);
        }
    }

    for (int i = 0; i < t1vCount; ++i) {
        if (root->t1v[i].v4) {
            free(root->t1v[i].v4);
        }
    }

    free(root->f17);
}

void TestSetSpecialStruct(SpecialRootT *root, uint32_t index, uint16_t t2Count, uint16_t t3Count, uint16_t t1vCount)
{
    uint8_t bitmapValue = 0xaa;
    char stringLen6[SPECIAL_STRING_LEN6] = "strst";
    char fixedLen6[SPECIAL_FIXED_LEN6] = "hello";
    char bytesLen6[SPECIAL_BYTES_LEN6] = "bytes";

    root->f1 = (uint64_t)index + 0xFFFFFFFF;                    // uint64
    memcpy(root->f2, (int8_t *)fixedLen6, SPECIAL_FIXED_LEN6);  // fixed
    root->f3 = (index % 16) & 0xFF;  // 分区字段                               // partition
    root->f4 = index;                // int32
    root->f6 = (index % 8) & 0xFF;   // uint8
    root->f7 = 3;
    root->f8 = 3;
    root->f9 = ((index + 18) << 8);
    root->f10 = ((24) & 0xff);  // uint8
    root->f11 = (bool)index % 2;
    root->f12 = (float)index * 1.1;
    root->f13 = (double)index * 1.1;
    root->f14 = (uint64_t)20211228 + 0xFFFFFFFF;              // time
    memcpy(root->f15, &bitmapValue, SPECIAL_BITMAP_LEN / 8);  // bitmap
    root->f16 = index;
    root->f17Len = SPECIAL_BYTES_LEN6;
    memcpy(root->f17, (uint8_t *)bytesLen6, SPECIAL_BYTES_LEN6);  // 64 * 1024

    // T1节点
    root->t1Flag = 1;
    root->t1->r1 = (uint64_t)index + 0xFFFFFFFF;
    root->t1->r2 = (int64_t)index;
    memcpy(root->t1->r3, (int8_t *)fixedLen6, SPECIAL_FIXED_LEN6);

    // T2 节点写值
    root->t1->t2Count = t2Count;
    for (int i = 0; i < t2Count; i++) {
        root->t1->t2[i].a1 = i & 0xFFFF;
        root->t1->t2[i].a2 = (uint64_t)20211228 + 0xFFFFFFFF;
        root->t1->t2[i].a3 = (float)i * 1.1;
        memcpy(root->t1->t2[i].a4, &bitmapValue, SPECIAL_BITMAP_LEN / 8);
        root->t1->t2[i].a5Len = SPECIAL_STRING_LEN6;
        memcpy(root->t1->t2[i].a5, (uint8_t *)stringLen6, SPECIAL_STRING_LEN6);  // 1024

        // T3节点写值
        root->t1->t2[i].t3Count = t3Count;
        for (int k = 0; k < t3Count; k++) {
            root->t1->t2[i].t3[k].a1 = i;
            root->t1->t2[i].t3[k].a2Len = SPECIAL_BYTES_LEN6;
            memcpy(root->t1->t2[i].t3[k].a2, (uint8_t *)bytesLen6, SPECIAL_BYTES_LEN6);
        }
    }
    // vecotor节点写值
    root->t1vCount = t1vCount;
    for (int i = 0; i < t1vCount; i++) {
        root->t1v[i].v1 = i;
        root->t1v[i].v2 = i;
        memcpy(root->t1v[i].v3, &bitmapValue, SPECIAL_BITMAP_LEN / 8);
        root->t1v[i].v4Len = SPECIAL_STRING_LEN6;
        memcpy(root->t1v[i].v4, (uint8_t *)stringLen6, SPECIAL_STRING_LEN6);
    }
}

void TestSetSpecialStmt(GmcStmtT *stmt, uint32_t i)
{
    uint32_t ret = 0;
    SpecialRootT specialObj = (SpecialRootT){0};
    SpecialNodet1T t1 = (SpecialNodet1T){0};
    SpecialNodet2T t2[SPECIAL_T2_ITEM_NUM] = {0};
    SpecialNodet3T t3[SPECIAL_T2_ITEM_NUM * SPECIAL_T3_ITEM_NUM] = {0};
    SpecialNodet1vT t1v[SPECIAL_T1_V_ITEM_NUM] = {0};
    TestLabelInfoT labelInfo = {g_specialName, 0, g_testNameSpace};

    TestSpecialMalloc(&specialObj, &t1, t2, t3, t1v, SPECIAL_T2_ITEM_NUM, SPECIAL_T3_ITEM_NUM, SPECIAL_T1_V_ITEM_NUM);
    TestSetSpecialStruct(&specialObj, i, SPECIAL_T2_ITEM_NUM, SPECIAL_T3_ITEM_NUM, SPECIAL_T1_V_ITEM_NUM);
    ret = testStructSetVertexWithBuf(stmt, &specialObj, &labelInfo);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TestSpecialFree(&specialObj, SPECIAL_T2_ITEM_NUM, SPECIAL_T3_ITEM_NUM, SPECIAL_T1_V_ITEM_NUM);
}

void TestSetSpecialPk(GmcStmtT *stmt, uint32_t i)
{
    uint32_t ret = 0;
    SpecialRootT specialObj = (SpecialRootT){0};
    TestLabelInfoT labelInfo = {g_specialName, 0, g_testNameSpace};

    specialObj.f1 = (uint64_t)i + 0xFFFFFFFF;  // uint64
    memcpy(specialObj.f2, (int8_t *)"hello", SPECIAL_FIXED_LEN6);
    ret = testStructSetIndexKeyWithBuf(stmt, &specialObj, 0, NULL, &labelInfo);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}

void TestSetSpecialUpdateField(GmcStmtT *stmt, uint32_t index, uint32_t updateValue)
{
    uint32_t ret = 0;

    int8_t f6Value = ((index + updateValue) % 8) & 0xFF;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_INT8, &f6Value, sizeof(int8_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    bool f11Value = (bool)(index + updateValue) % 2;
    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_BOOL, &f11Value, sizeof(bool));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    float f12Value = (float)index * 1.1 + updateValue;
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_FLOAT, &f12Value, sizeof(float));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    double f13Value = (double)index * 1.1 + updateValue;
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_DOUBLE, &f13Value, sizeof(double));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint64_t f14Value = (uint64_t)20211228 + updateValue + 0xFFFFFFFF;
    ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_TIME, &f14Value, sizeof(uint64_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint8_t f15Value = (index + updateValue) & 0xff;
    GmcBitMapT f15Bitmap = {0};
    f15Bitmap.beginPos = 0;
    f15Bitmap.endPos = 8 - 1;
    f15Bitmap.bits = &f15Value;
    ret = GmcSetVertexProperty(stmt, (char *)"F15", GMC_DATATYPE_BITMAP, &f15Bitmap, sizeof(f15Bitmap));
    TEST_ASSERT_INT32(GMERR_OK, ret);

    uint32_t f16Value = index + updateValue;
    ret = GmcSetVertexProperty(stmt, (char *)"F16", GMC_DATATYPE_UINT32, &f16Value, sizeof(uint32_t));
    TEST_ASSERT_INT32(GMERR_OK, ret);
}

void NewSnCallBackTree(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_MODIFY: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull = false;
                    if (strcmp(labelName, g_generalName) == 0) {
                        uint32_t ifindexVal = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "ifindex", &ifindexVal, sizeof(uint32_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "modify ifindex %llu", ifindexVal);
                    } else {
                        uint64_t f1Val = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F1", &f1Val, sizeof(uint64_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "modify F1 %llu", f1Val);
                    }

                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull = false;
                    if (strcmp(labelName, g_generalName) == 0) {
                        uint32_t ifindexVal = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "ifindex", &ifindexVal, sizeof(uint32_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "load ifindex %d", ifindexVal);
                    } else {
                        uint64_t f1Val = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F1", &f1Val, sizeof(uint64_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "load F1 %d", f1Val);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_MODIFY: {
                data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

void OldSnCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            user_data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            ret = GmcSubGetLabelName(subStmt, i, labelName, &(labelNameLen = sizeof(labelName)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_OLD, user_data);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                case GMC_SUB_EVENT_AGED: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestCheckSubStmt(subStmt, GMC_SUB_FETCH_NEW, user_data);
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__,
                        info->eventType);
                    break;
                }
            }
            break;
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                AW_FUN_Log(
                    LOG_INFO, "default: [%s] [%d] invalid eventType %d.\n", __FUNCTION__, __LINE__, info->eventType);
                break;
            }
        }
    }
}

void OldSnCallBackTree(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            data->scanEofNum++;
            break;
        }

        ret = GmcFetch(subStmt, &eof);  // fetch 1 次
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        /* 多表情景下不好保证推送得到的表顺序是恒定的 */
        for (int i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            uint32_t labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull = false;
                    if (strcmp(labelName, g_generalName) == 0) {
                        uint32_t ifindexVal = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "ifindex", &ifindexVal, sizeof(uint32_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "modify ifindex %llu", ifindexVal);
                    } else if (strcmp(labelName, "SN049BigV3") == 0) {
                        TestCheckSubStmtTableBig(subStmt, GMC_SUB_FETCH_NEW, data);
                    } else {
                        uint64_t f1Val = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F1", &f1Val, sizeof(uint64_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "modify F1 %llu", f1Val);
                    }

                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    bool isNull = false;
                    if (strcmp(labelName, g_generalName) == 0) {
                        uint32_t ifindexVal = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "ifindex", &ifindexVal, sizeof(uint32_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "load ifindex %d", ifindexVal);
                    } else if (strcmp(labelName, "SN049BigV3") == 0) {
                        TestCheckSubStmtTableBig(subStmt, GMC_SUB_FETCH_NEW, data);
                    } else {
                        uint64_t f1Val = 0;
                        ret = GmcGetVertexPropertyByName(subStmt, "F1", &f1Val, sizeof(uint64_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
                        AW_FUN_Log(LOG_INFO, "load F1 %d", f1Val);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_INFO, "default: invalid eventType %d .", info->eventType);
                    break;
                }
            }
        }

        switch (info->eventType) {
            case GMC_SUB_EVENT_UPDATE: {
                data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                data->agedNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                data->scanNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_INFO, "default: invalid eventType.");
                break;
            }
        }
    }
}

#endif
