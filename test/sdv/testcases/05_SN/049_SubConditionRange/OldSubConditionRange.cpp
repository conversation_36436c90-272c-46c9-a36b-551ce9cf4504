#include "ConditionRangeCommon.h"

class OldSubConditionRange : public testing::Test {
public:
    SnUserDataT *oldSubData;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void OldSubConditionRange::SetUpTestCase()
{
    system("${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "SetUpTestCase.");
}

void OldSubConditionRange::TearDownTestCase()
{
    AW_FUN_Log(LOG_STEP, "TearDownTestCase.");
}

void OldSubConditionRange::SetUp()
{
    testEnvInit();
    create_epoll_thread();

    // 创建同步连接
    int ret = 0;
    g_connSync = NULL;
    g_stmtSync = NULL;
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int chanRingLen = 256;
    g_connOldSub = NULL;
    g_stmtOldSub = NULL;
    const char *oldSubConnName = "oldSubConn049";
    ret = testSubConnect(&g_connOldSub, &g_stmtOldSub, 1, g_epoll_reg_info, oldSubConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcCreateVertexLabel(g_stmtSync, g_schemaPath, g_labelName, 0, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testSnMallocUserData(&oldSubData, g_recordCount);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void OldSubConditionRange::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    ret = GmcDropVertexLabel(g_stmtSync, g_labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(g_connOldSub, g_stmtOldSub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(oldSubData);
    close_epoll_thread();
    testEnvClean();
}

// 老订阅范围条件订阅，范围条件json中cmp_type为1，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":1, "value": "A"},
                        {"property": "F6", "cmp_type":1, "value": 100},
                        {"property": "F10", "cmp_type":1, "value": 1100},
                        {"property": "F12", "cmp_type":1, "value": 110.86},
                        {"property": "F14", "cmp_type":1, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为1，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":1, "value": "A"},  
                        {"property": "F6", "cmp_type":1, "value": 200},
                        {"property": "F10", "cmp_type":1, "value": 1200},
                        {"property": "F12", "cmp_type":1, "value": 210.86},
                        {"property": "F14", "cmp_type":1, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为2，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":2, "value": "A"},
                        {"property": "F6", "cmp_type":2, "value": 100},
                        {"property": "F10", "cmp_type":2, "value": 1100},
                        {"property": "F12", "cmp_type":2, "value": 110.86},
                        {"property": "F14", "cmp_type":2, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为2，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":2, "value": "A"},  
                        {"property": "F6", "cmp_type":2, "value": 100},
                        {"property": "F10", "cmp_type":2, "value": 1100},
                        {"property": "F12", "cmp_type":2, "value": 110.86},
                        {"property": "F14", "cmp_type":2, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // replace写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE, g_recordCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为3，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":3, "value": "A"},
                        {"property": "F6", "cmp_type":3, "value": 100},
                        {"property": "F10", "cmp_type":3, "value": 1100},
                        {"property": "F12", "cmp_type":3, "value": 110.86},
                        {"property": "F14", "cmp_type":3, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, g_recordCount / 2 - 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为3，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":3, "value": "A"},  
                        {"property": "F6", "cmp_type":3, "value": 100},
                        {"property": "F10", "cmp_type":3, "value": 1100},
                        {"property": "F12", "cmp_type":3, "value": 110.86},
                        {"property": "F14", "cmp_type":3, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // merge写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE, g_recordCount / 2 - 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // replace更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE, g_recordCount - 20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为4，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":4, "value": "B"},
                        {"property": "F6", "cmp_type":4, "value": 100},
                        {"property": "F10", "cmp_type":4, "value": 1100},
                        {"property": "F12", "cmp_type":4, "value": 110.86},
                        {"property": "F14", "cmp_type":4, "value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为4，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":4, "value": "B"},  
                        {"property": "F6", "cmp_type":4, "value": 200},
                        {"property": "F10", "cmp_type":4, "value": 1200},
                        {"property": "F12", "cmp_type":4, "value": 210.86},
                        {"property": "F14", "cmp_type":4, "value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INSERT, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为5，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":5, "value": "A"},
                        {"property": "F6", "cmp_type":5, "value": 100},
                        {"property": "F10", "cmp_type":5, "value": 1100},
                        {"property": "F12", "cmp_type":5, "value": 110.86},
                        {"property": "F14", "cmp_type":5, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为5，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace insert", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge update", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":5, "value": "A"},  
                        {"property": "F6", "cmp_type":5, "value": 200},
                        {"property": "F10", "cmp_type":5, "value": 1200},
                        {"property": "F12", "cmp_type":5, "value": 210.86},
                        {"property": "F14", "cmp_type":5, "value": "d000000000"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE_INSERT, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE_UPDATE, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为6，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":6, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":6, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":6, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":6, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":6, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为6，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":6, "left_value": "A", "right_value": "x"},  
                        {"property": "F6", "cmp_type":6, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":6, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":6, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":6, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INSERT, 31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 31);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为7，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":7, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":7, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":7, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":7, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":7, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为7，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":7, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":7, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":7, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":7, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":7, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为8，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":8, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":8, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":8, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":8, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":8, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为8，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace update", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge insert", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":8, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":8, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":8, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":8, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":8, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE_INSERT, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE_UPDATE, 25);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅，范围条件json中cmp_type为9，订阅inition load事件和eof事件;
TEST_F(OldSubConditionRange, SN_049_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":9, "left_value": "A", "right_value": "x"},
                        {"property": "F6", "cmp_type":9, "left_value": 100, "right_value": 150},
                        {"property": "F10", "cmp_type":9, "left_value": 1100, "right_value": 1150},
                        {"property": "F12", "cmp_type":9, "left_value": 110.86, "right_value": 160.86},
                        {"property": "F14", "cmp_type":9, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  老订阅范围条件订阅，范围条件json中cmp_type为9，订阅insert、update、delete、age事件
TEST_F(OldSubConditionRange, SN_049_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅 F14: "d%09d", (pk + addVal) % 10
    // F0:(pk + addVal) % 50; F6:pk + addVal; F10:1000 + pk + addVal;F12:10.86 + pk + addVal;
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":9, "left_value": "A", "right_value": "x"},  
                        {"property": "F6", "cmp_type":9, "left_value": 200, "right_value": 250},
                        {"property": "F10", "cmp_type":9, "left_value": 1200, "right_value": 1250},
                        {"property": "F12", "cmp_type":9, "left_value": 210.86, "right_value": 260.86},
                        {"property": "F14", "cmp_type":9, "left_value": "d000000000", "right_value": "d000000005"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 20);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_CHAR类型
TEST_F(OldSubConditionRange, SN_049_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":2, "value": "A"},  
                        {"property": "F0", "cmp_type":5, "value": "C"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_UCHAR类型
TEST_F(OldSubConditionRange, SN_049_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F1", "cmp_type":6, "left_value": "A", "right_value": "C"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_INT8类型
TEST_F(OldSubConditionRange, SN_049_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F2", "cmp_type":3, "value": -127},  
                        {"property": "F2", "cmp_type":4, "value": -120}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_UINT8类型
TEST_F(OldSubConditionRange, SN_049_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F3", "cmp_type":6, "left_value": 120, "right_value": 130}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_INT16类型
TEST_F(OldSubConditionRange, SN_049_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F4", "cmp_type":2, "value": -127},  
                        {"property": "F4", "cmp_type":4, "value": -120}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_UINT16类型
TEST_F(OldSubConditionRange, SN_049_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F5", "cmp_type":7, "left_value": 1120, "right_value": 1130}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_INT32类型
TEST_F(OldSubConditionRange, SN_049_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace update", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F6", "cmp_type":3, "value": 200},  
                        {"property": "F6", "cmp_type":5, "value": 300}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE_UPDATE, 99);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_UINT32类型
TEST_F(OldSubConditionRange, SN_049_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge update", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F7", "cmp_type":8, "left_value": 195, "right_value": 300}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 2;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE_UPDATE, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_INT64类型
TEST_F(OldSubConditionRange, SN_049_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F9", "cmp_type":3, "value": 200},  
                        {"property": "F9", "cmp_type":2, "value": 1190}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 110);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅GMC_DATATYPE_UINT64类型
TEST_F(OldSubConditionRange, SN_049_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F10", "cmp_type":9, "left_value": 195, "right_value": 1010}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅float类型
TEST_F(OldSubConditionRange, SN_049_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace insert", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F11", "cmp_type":4, "value": 200},  
                        {"property": "F11", "cmp_type":5, "value": 1.2}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_REPLACE_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅double类型
TEST_F(OldSubConditionRange, SN_049_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F12", "cmp_type":6, "left_value": 309.86, "right_value": 309.86}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅string类型
TEST_F(OldSubConditionRange, SN_049_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F14", "cmp_type":6, "left_value": "d0", "right_value": "d000000009"},
                        {"property": "F14", "cmp_type":9, "left_value": "d000000006", "right_value": "d000000009"},
                        {"property": "F14", "cmp_type":7, "left_value": "d000000006", "right_value": "d1"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 40);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅json中多个条件，同一个字段的多个条件互斥
TEST_F(OldSubConditionRange, SN_049_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F14", "cmp_type":2, "value": "d1"},
                        {"property": "F14", "cmp_type":5, "value": "d0"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅json中多个条件，不同字段多个条件
TEST_F(OldSubConditionRange, SN_049_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge insert", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F11", "cmp_type":6, "left_value": 101.2, "right_value": 120.2},
                        {"property": "F12", "cmp_type":5, "value": 120.86},
                        {"property": "F14", "cmp_type":5, "value": "d1"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_MERGE_INSERT, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅json中多个条件，不同字段多个条件逆序，过滤到的结果相同
TEST_F(OldSubConditionRange, SN_049_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F14", "cmp_type":5, "value": "d1"},
                        {"property": "F12", "cmp_type":5, "value": 120.86},
                        {"property": "F11", "cmp_type":6, "left_value": 101.2, "right_value": 120.2}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅json中多个条件指定"operator_type":"or",实际上还是按照“and”匹配
TEST_F(OldSubConditionRange, SN_049_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {"property": "F14", "cmp_type":2, "value": "d0"},
                        {"property": "F12", "cmp_type":3, "value": 110.86},
                        {"property": "F11", "cmp_type":7, "left_value": 101.2, "right_value": 120.2}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 18);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅json中多个条件，包含全匹配字段，等值匹配、范围匹配
TEST_F(OldSubConditionRange, SN_049_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {"property": "F14"},
                        {"property": "F12", "cmp_type":1, "value": 110.86},
                        {"property": "F11", "cmp_type":9, "left_value": 11.2, "right_value": 120.2}
                    ]
            }
    })";

    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅10个条件
TEST_F(OldSubConditionRange, SN_049_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPkProperty(g_stmtSync, i);
        TestSetVertexOtherProperty(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049AllTypeV1",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {"property": "F0", "cmp_type":2, "value": "A"},
                        {"property": "F3", "cmp_type":3, "value": 50},
                        {"property": "F4", "cmp_type":4, "value": 400},
                        {"property": "F7", "cmp_type":5, "value": 1200},
                        {"property": "F10", "cmp_type":6, "left_value": 1100, "right_value":10000},
                        {"property": "F11", "cmp_type":7, "left_value": 100, "right_value":10000},
                        {"property": "F14", "cmp_type":8, "left_value": "d0000", "right_value":"d000000005"},
                        {"property": "F5", "cmp_type":9, "left_value": 100, "right_value":10000},
                        {"property": "F19", "cmp_type":9, "left_value": 0, "right_value":9000},
                        {"property": "F2", "cmp_type":6, "left_value": -128, "right_value":127}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBack, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 60);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexOtherProperty(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestScanAndCheckVertexProperty(g_stmtSync, g_labelName, g_lablePk, i, addVal);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 90);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    // 开启分区对账
    ret = GmcBeginCheck(g_stmtSync, g_labelName, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmtSync, g_labelName, 9, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_AGED, g_recordCount / 2 / PARTITION_NUM);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅大对象
TEST_F(OldSubConditionRange, SN_049_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *schemaPath = "schemaFile/SchemaBig.gmjson";
    const char *labelName = "SN049BigV3";
    ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, labelName, 0, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    uint32_t bigObjCount = 20;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < bigObjCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetPkValueTabelBig(g_stmtSync, i);
        TestSetOtherValueTabelBig(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        if (ret == GMERR_OUT_OF_MEMORY) {
            bigObjCount = i;
            AW_FUN_Log(LOG_INFO, "ret : %d, i : %d.", ret, i);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"SN049BigV3",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {"property": "F1", "cmp_type":2, "value": "c"},
                        {"property": "F1", "cmp_type":5, "value": "e"}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBackTree, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, bigObjCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    uint32_t updateBigObjCount = 20;
    for (int i = 0; i < bigObjCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetOtherValueTabelBig(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        if (ret == GMERR_OUT_OF_MEMORY) {
            updateBigObjCount = i;
            AW_FUN_Log(LOG_INFO, "update ret : %d, i : %d.", ret, i);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, updateBigObjCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    uint32_t deleteBigObjCount = 20;
    for (int i = 0; i < bigObjCount; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, g_lablePk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        if (ret == GMERR_OUT_OF_MEMORY) {
            deleteBigObjCount = i;
            AW_FUN_Log(LOG_INFO, "delete ret : %d, i : %d.", ret, i);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, labelName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(bigObjCount - deleteBigObjCount, count);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, deleteBigObjCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅一般复杂表，不同深度不同类型节点下的字段组合
TEST_F(OldSubConditionRange, SN_049_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *schemaPath = "schemaFile/new_sub_general.gmjson";
    ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_generalName, 0, g_generalNameConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_generalName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetGeneralStmt(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"new_sub_general",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {"property": "tbtp", "cmp_type":2, "value": 10},
                        {"property": "ifm/is_configure", "cmp_type":5, "value": 30},
                        {"property": "dev/slot_id", "cmp_type":9, "left_value": 10, "right_value": 40},
                        {"property": "T1_V/V2", "cmp_type":9, "left_value": 0, "right_value": 10}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBackTree, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_generalName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetGeneralUpdateField(g_stmtSync, i, addVal);
        ret = GmcSetIndexKeyName(g_stmtSync, "ifindex_pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_generalName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtSync, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtSync, "ifindex_pk");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_generalName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_generalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_generalName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 老订阅范围条件订阅特殊复杂表，不同深度不同类型节点下的字段组合
TEST_F(OldSubConditionRange, SN_049_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    const char *schemaPath = "schemaFile/new_sub_special.gmjson";
    ret = testGmcCreateVertexLabel(g_stmtSync, schemaPath, g_specialName, 0, g_specialNameConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 写数据
    int addVal = 0;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_specialName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetSpecialStmt(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 新建老订阅条件范围订阅
    const char *oldSubJson = R"({
        "label_name":"new_sub_special",
        "comment":"old condition range sub",
        "events":
            [
                {"type": "initial_load", "msgTypes":["new object"]},
                {"type":"insert", "msgTypes":["new object"]},
                {"type":"replace", "msgTypes":["new object", "old object"]},
                {"type":"update", "msgTypes":["new object", "old object"]},
                {"type":"merge", "msgTypes":["new object", "old object"]},
                {"type":"delete", "msgTypes":["old object"]},
                {"type":"age", "msgTypes":["old object"]}
            ],
        "constraint":
            {
                "operator_type":"or",
                "conditions":
                    [
                        {"property": "F12", "cmp_type":2, "value": 10},
                        {"property": "T1/R2", "cmp_type":5, "value": 20},
                        {"property": "T1/T2/A5", "cmp_type":9, "left_value": "a", "right_value": "z"},
                        {"property": "T1/T2/T3/A1", "cmp_type":6, "left_value": 0, "right_value": 10},
                        {"property": "T1_V/V2", "cmp_type":6, "left_value": 0, "right_value": 10}
                    ]
            }
    })";
    GmcSubConfigT oldSubInfo;
    oldSubInfo.subsName = "SN049OldSubinfo";
    oldSubInfo.configJson = oldSubJson;
    ret = GmcSubscribe(g_stmtSync, &oldSubInfo, g_connOldSub, OldSnCallBackTree, oldSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新数据
    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount; i++) {
        ((int *)(oldSubData->new_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_specialName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetSpecialPk(g_stmtSync, i);
        TestSetSpecialUpdateField(g_stmtSync, i, addVal);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
    }
    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_UPDATE, 21);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    addVal = 100;
    oldSubData->subIndex = addVal;
    for (int i = 0; i < g_recordCount / 2; i++) {
        ((int *)(oldSubData->old_value))[i] = i;
        ret = testGmcPrepareStmtByLabelName(g_stmtSync, g_specialName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetSpecialPk(g_stmtSync, i);
        ret = GmcExecute(g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t expectAffectRows = 1;
        ret = TestCheckAffectRows(g_stmtSync, expectAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    uint64_t count = 0;
    ret = GmcGetVertexCount(g_stmtSync, g_specialName, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_recordCount / 2, count);

    ret = testWaitSnRecv(oldSubData, GMC_SUB_EVENT_DELETE, g_recordCount / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmtSync, "SN049OldSubinfo");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(g_stmtSync, g_specialName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmtSync, g_specialName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
