extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

#define MAX_NAME_LENGTH 128

// global value
GmcConnT *g_conn_sync = NULL, *g_conn_sub = NULL;  // conn 句柄
GmcStmtT *g_stmt_sync = NULL, *g_stmt_sub = NULL;  // stmt 句柄
void *g_label = NULL;
char *g_schema = NULL;

char g_labelName1[] = "ArrayVectorConditionalSubscription_Tree";
char g_labelName32Deep[] = "ArrayVectorConditionalSubscription_32DeepTree";
char g_lableName_PK1[] = "ArrayVectorConditionalSubscription_Tree_PK";
char g_lableName_32DeepPK[] = "ArrayVectorConditionalSubscription_32DeepTree_PK";
char g_label_config[] = "{\"max_record_count\":3000000}";

char *g_sub_info = NULL;

const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";

AsyncUserDataT asyncUserData = {0};  //用于判断推送的数据

int g_start_num = 0;
int g_end_num = 100;
int g_array_num = 3;
int g_vector_num = 3;
int affectRows;
unsigned int len;
int g_thread_wait = 0;
int g_thr_num_sn = 10;
int g_thr_num_dml = 1;
pthread_mutex_t g_threadLock;

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    char *g_schema_ = NULL;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    printf("data_json is %s", data_json);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
}

// set 主键字段
void TestGmcNodeSetPropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

// set 根节点中的非主键字段
void TestGmcNodeSetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"F20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        // ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        // ASSERT_EQ(GMERR_OK, ret);
    }
}

// set 普通子节点中的字段
void TestGmcNodeSetPropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    // printf("f12_value : %c\n", f12_value);
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    // printf("f13_value : %c\n", f13_value);
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        // ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        // ASSERT_EQ(GMERR_OK, ret);
    }
}

// set 普通子节点中的字段
void TestGmcNodeSetPropertyBySuperfiled_P(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    char *sp_1 = (char *)malloc(26);
    char *temp = sp_1;
    *(int64_t *)(temp) = i;
    *(uint64_t *)(temp + 8) = i;
    *(int32_t *)(temp + 16) = i;
    *(uint32_t *)(temp + 20) = i;
    *(int16_t *)(temp + 24) = i & 0x7FFF;
    ret = GmcNodeSetSuperfieldByName(node, (char *)"superfield0", sp_1, 26);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    if (isBitmap) {
        GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
        uint8_t bits[128 / 8];
        memset(bits, 0, 128 / 8);
        bits[128 / 8 - 1] = '\0';
        bitMap.bits = bits;
        // ret = GmcNodeSetPropertyByName(node, (char *)"F22", GMC_DATATYPE_BITMAP, (void*)&bitMap, sizeof(bitMap));
        // ASSERT_EQ(GMERR_OK, ret);
    }
}

// set array节点中的字段
void TestGmcNodeSetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

// set vector节点中的字段
void TestGmcNodeSetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f1_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = i & 0x7FFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    ret = GmcNodeSetPropertyByName(node, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    ret = GmcNodeSetPropertyByName(node, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    ret = GmcNodeSetPropertyByName(node, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
}

// get 根节点中的数据
void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    uint64_t f1_value = 0;

    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"F16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F17", &f17_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F18", &f18_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F19", &f19_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F20", &f20_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f20_value);

    if (isBitmap) {
        // uint8_t bits[128/8];
        // memset(bits, 0, 128/8); // test bitmap value
        // bits[128 / 8 - 1] = '\0';
        // ret = GmcNodeGetPropertySizeByName(node, "F22", &propSize);
        // EXPECT_EQ(propSize, 128);
        // ret = GmcNodeGetPropertyByName(node, "F22", bits, propSize, &isNull);
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ((uint32_t)0, isNull);
        // for(int i = 0; i < 128/8 - 1; i++) {
        //    EXPECT_EQ(0x0, *(bits + i));
        //}
    }
}

// get 普通子节点中的数据
void TestGmcNodeGetPropertyByName_p(GmcNodeT *node, int i, bool bool_value, char *f14_value, bool isBitmap = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"P16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    uint8_t f17_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P17", &f17_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f17_value);

    uint16_t f18_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P18", &f18_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xF, f18_value);

    uint32_t f19_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P19", &f19_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFF, f19_value);

    uint64_t f20_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P20", &f20_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f20_value);

    if (isBitmap) {
        // uint8_t bits[128/8];
        // memset(bits, 0, 128/8); // test bitmap value
        // bits[128 / 8 - 1] = '\0';
        // ret = GmcNodeGetPropertySizeByName(node, "F22", &propSize);
        // EXPECT_EQ(propSize, 128);
        // ret = GmcNodeGetPropertyByName(node, "F22", bits, propSize, &isNull);
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ((uint32_t)0, isNull);
        // for(int i = 0; i < 128/8 - 1; i++) {
        //    EXPECT_EQ(0x0, *(bits + i));
        //}
    }
}

// get array节点中的数据
void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"A16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

// get vector节点中的数据
void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;

    ret = GmcNodeGetPropertyByName(node, (char *)"V0", &f0_value, sizeof(int64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    // printf("V0 IS %d \n",f0_value);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V1", &f1_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V2", &f2_value, sizeof(int32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V3", &f3_value, sizeof(uint32_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V4", &f4_value, sizeof(int16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0x7FFF, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V5", &f5_value, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i & 0xFFFF, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V6", &f6_value, sizeof(int8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((int8_t)(i & 0x7F), f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V7", &f7_value, sizeof(uint8_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((uint8_t)(i & 0xFF), f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V9", &f9_value, sizeof(float), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V10", &f10_value, sizeof(double), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V12", &f12_value, sizeof(char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ((char)('a' + (i & 0x1A)), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    unsigned char k = (unsigned char)('A' + (i & 0x1A));
    ASSERT_EQ(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"V14", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"V14", string_value, propSize, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V15", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(node, (char *)"V16", string_value, 7, &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(string_value, f14_value), 0);
}

// 向表中insert 数据封装函数
void TestGmcInsertVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    // printf("i + g_end_num * index  is %d \n",(i + g_end_num * index)) ;
    TestGmcNodeSetPropertyByName_PK(root, i);  //插入的值都是 插入的数据条数i
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    // 插入普通子节点
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入array节点
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 1; j <= array_num; j++) {

        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }

    // 插入vector节点
    for (uint32_t j = 1; j <= vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j, bool_value, f14_value);
    }

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 向表中insert 数据封装函数(插入相邻节点的数据 只插入 普通子节点的数据 其他的不插入)
void TestGmcInsertAdjacentNodeVertex(
    GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    // printf("i + g_end_num * index  is %d \n",(i + g_end_num * index)) ;
    TestGmcNodeSetPropertyByName_PK(root, i);  //插入的值都是 插入的数据条数i
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    // 插入普通子节点
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}
// 向表中insert 数据封装函数(对vector 不做操作)
void TestGmcInsertAppendNodeVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int vector_num,
    char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    // printf("i + g_end_num * index  is %d \n",(i + g_end_num * index)) ;
    TestGmcNodeSetPropertyByName_PK(root, i);  //插入的值都是 插入的数据条数i
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    // 插入普通子节点
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 向表中insert 数据封装函数(32Deep)
void TestGmcInsert32DeepVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T31, *M31;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    // 通过stmt 句柄 直接获取 指定层级的node节点
    // vector 型node
    ret = GmcGetChildNode(stmt,
        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/"
                "T28/T29/T30/T31",
        &T31);
    EXPECT_EQ(GMERR_OK, ret);
    // fixed_array 型node
    ret = GmcGetChildNode(stmt,
        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/T26/T27/"
                "T28/T29/T30/M31",
        &M31);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点

    TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);

    // 插入array节点
    for (uint32_t j = 0; j < array_num; j++) {
        int64_t f0_value = i + g_end_num * index;
        ret = GmcNodeSetPropertyByName(M31, (char *)"A31", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        // TestGmcNodeSetPropertyByName_A(T31, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(M31, &M31);
    }

    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T31, &T31);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t f0_value = i + g_end_num * index;
        ret = GmcNodeSetPropertyByName(T31, (char *)"D31", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        // TestGmcNodeSetPropertyByName_V(T3, i + g_end_num * index, bool_value, f14_value);
    }

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 向表中insert 数据封装函数 (使用superfiled 接口set 字段)
void TestGmcInsertVertex_Superfiled(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    // printf("i + g_end_num * index  is %d \n",(i + g_end_num * index)) ;
    TestGmcNodeSetPropertyByName_PK(root, i);  //插入的值都是 插入的数据条数i
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

    // 插入普通子节点
    TestGmcNodeSetPropertyBySuperfiled_P(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入array节点
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 1; j <= array_num; j++) {
        // i +gend_num * index 用来决定 每个主键值的不同
        // printf(" *************** (i + g_end_num * index)*array_num+j is %d*************\n",(i + g_end_num *
        // index)*array_num+j);
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }

    // 插入vector节点
    for (uint32_t j = 1; j <= vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j, bool_value, f14_value);
        // vector 所在node 的index为0- vector_num 里面的值分别是 0- vector_num
    }

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

void TestGmcInsertVertexSuperfiled(
    GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num, int vector_num, char *label_name)
{
    int32_t ret = 0;
    void *label = NULL;
    char *sp_1 = (char *)malloc(26);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i, bool_value, f14_value);

    // set superfiled by name
    char *temp = sp_1;
    *(int64_t *)(temp) = i;
    *(uint64_t *)(temp + 8) = i;
    *(int32_t *)(temp + 16) = i;
    *(uint32_t *)(temp + 20) = i;
    *(int16_t *)(temp + 24) = i & 0x7FFF;

    ret = GmcNodeSetSuperfieldByName(t1, (char *)"superfiled0", sp_1, 26);
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = i & 0xFFFF;
    printf("P5 value is %d \n", f5_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = i & 0x7F;
    printf("P6 value is %d \n", f6_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = i & 0xFF;
    printf("P7 value is %d \n", f7_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = (float)i;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = (double)i;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = i;
    printf("P11_value is %d \n", f11_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 'a' + (i & 0x1A);
    printf("P12_value is %c \n", f12_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 'A' + (i & 0x1A);
    printf("P13_value is %c \n", f13_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(t1, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f17_value = i & 0xF;
    printf("P17_value is %d \n", f17_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P17", GMC_DATATYPE_BITFIELD8, &f17_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f18_value = i & 0xF;
    printf("P18_value is %s \n", f18_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P18", GMC_DATATYPE_BITFIELD16, &f18_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f19_value = i & 0xFF;
    printf("P19_value is %s \n", f19_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P19", GMC_DATATYPE_BITFIELD32, &f19_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f20_value = i & 0xFFFF;
    printf("P20_value is %s \n", f20_value);
    ret = GmcNodeSetPropertyByName(t1, (char *)"P20", GMC_DATATYPE_BITFIELD64, &f20_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    GmcBitMapT bitMap = {0, 127, NULL};  // 两个int类型，存储起始位共8个字节，一个字节存储结束符
    uint8_t bits[128 / 8];
    memset(bits, 0, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcNodeSetPropertyByName(t1, (char *)"P22", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    ASSERT_EQ(GMERR_OK, ret);

    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 1; j <= array_num; j++) {
        char *temp = sp_1;
        *(int64_t *)(temp) = ((i + g_end_num * index) * array_num + j);
        *(uint64_t *)(temp + 8) = ((i + g_end_num * index) * array_num + j);
        *(int32_t *)(temp + 16) = ((i + g_end_num * index) * array_num + j);
        *(uint32_t *)(temp + 20) = ((i + g_end_num * index) * array_num + j);
        *(int16_t *)(temp + 24) = ((i + g_end_num * index) * array_num + j) & 0x7FFF;
        ret = GmcNodeSetSuperfieldByName(t2, (char *)"superfiled1", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);

        f5_value = ((i + g_end_num * index) * array_num + j) & 0xFFFF;
        printf("A5_value is %d \n", f5_value);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        ASSERT_EQ(GMERR_OK, ret);

        f6_value = ((i + g_end_num * index) * array_num + j) & 0x7F;
        printf("A6_value is %d \n", f6_value);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
        ASSERT_EQ(GMERR_OK, ret);

        f7_value = ((i + g_end_num * index) * array_num + j) & 0xFF;
        printf("A7_value is %d \n", f7_value);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);

        f8_value = bool_value;
        ret = GmcNodeSetPropertyByName(t2, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
        ASSERT_EQ(GMERR_OK, ret);

        f9_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
        ASSERT_EQ(GMERR_OK, ret);

        f10_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
        ASSERT_EQ(GMERR_OK, ret);

        f11_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);

        f12_value = 'a' + (((i + g_end_num * index) * array_num + j) & 0x1A);
        printf("a12_value is %c \n", f12_value);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
        ASSERT_EQ(GMERR_OK, ret);

        f13_value = 'A' + (((i + g_end_num * index) * array_num + j) & 0x1A);
        printf("a13_value is %c \n", f13_value);
        ret = GmcNodeSetPropertyByName(t2, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t2, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
        ASSERT_EQ(GMERR_OK, ret);

        GmcNodeGetNextElement(t2, &t2);
    }

    // 插入vector节点
    for (uint32_t j = 1; j <= vector_num; j++) {
        ret = GmcNodeAppendElement(t3, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        char *temp = sp_1;
        *(int64_t *)(temp) = ((i + g_end_num * index) * array_num + j);
        *(uint64_t *)(temp + 8) = ((i + g_end_num * index) * array_num + j);
        *(int32_t *)(temp + 16) = ((i + g_end_num * index) * array_num + j);
        *(uint32_t *)(temp + 20) = ((i + g_end_num * index) * array_num + j);
        *(int16_t *)(temp + 24) = ((i + g_end_num * index) * array_num + j) & 0x7FFF;
        uint16_t f5_value = ((i + g_end_num * index) * array_num + j) & 0xFFFF;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
        ASSERT_EQ(GMERR_OK, ret);

        f6_value = ((i + g_end_num * index) * array_num + j) & 0x7F;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
        ASSERT_EQ(GMERR_OK, ret);

        f7_value = ((i + g_end_num * index) * array_num + j) & 0xFF;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);

        f8_value = bool_value;
        ret = GmcNodeSetPropertyByName(t3, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
        ASSERT_EQ(GMERR_OK, ret);

        f9_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
        ASSERT_EQ(GMERR_OK, ret);

        f10_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
        ASSERT_EQ(GMERR_OK, ret);

        f11_value = ((i + g_end_num * index) * array_num + j);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
        ASSERT_EQ(GMERR_OK, ret);

        f12_value = 'a' + (((i + g_end_num * index) * array_num + j) & 0x1A);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
        ASSERT_EQ(GMERR_OK, ret);

        f13_value = 'A' + (((i + g_end_num * index) * array_num + j) & 0x1A);
        ret = GmcNodeSetPropertyByName(t3, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(t3, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcNodeSetSuperfieldByName(t3, (char *)"superfiled2", sp_1, 26);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    free(sp_1);
}

// 向表中insert 数据封装函数 (批量 写入数据)
void TestGmcInsertVertexBatch(GmcConnT *conn, GmcStmtT *stmt, SnUserDataT *user_data, int insert_sum, int index,
    bool bool_value, char *f14_value, int array_num, int vector_num, char *label_name, bool isBitmap = true)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    int32_t userDataIdx = 0;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < insert_sum; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        GmcNodeT *root, *T1, *T2, *T3;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 插入顶点
        // printf("i + g_end_num * index  is %d \n",(i + g_end_num * index)) ;
        TestGmcNodeSetPropertyByName_PK(root, i);  //插入的值都是 插入的数据条数i
        TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);

        // 插入普通子节点
        TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);

        // 插入array节点
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 1; j <= array_num; j++) {
            // i +gend_num * index 用来决定 每个主键值的不同
            // printf(" *************** (i + g_end_num * index)*array_num+j is %d*************\n",(i + g_end_num *
            // index)*array_num+j);
            TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j, bool_value, f14_value);
            GmcNodeGetNextElement(T2, &T2);
        }

        // 插入vector节点
        for (uint32_t j = 1; j <= vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j, bool_value, f14_value);
            // vector 所在node 的index为0- vector_num 里面的值分别是 0- vector_num
        }
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcResetVertex(stmt, true);
        EXPECT_EQ(GMERR_OK, ret);
    }

    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(insert_sum, totalNum);
    EXPECT_EQ(insert_sum, successNum);
    GmcBatchDestroy(batch);
}

// 读取表中的数据
void TestGmcDirectFetchVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num, bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            // ASSERT_EQ(NO_DATA, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);

            GmcNodeT *root, *T1, *T2, *T3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);
            TestGmcNodeGetPropertyByName_p(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
            // 读取array节点
            ret = GmcNodeGetChild(T1, "T2", &T2);
            ASSERT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(T2, j, &T2);
                ASSERT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(
                    T2, ((i + g_end_num * index) * array_num + j + 1), bool_value, f14_value);
            }
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(T3, j, &T3);
                ASSERT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(
                    T3, ((i + g_end_num * index) * vector_num + j + 1), bool_value, f14_value);
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 读取表中的数据  (没有array node)
void TestGmcDirectFetchVertexNoarray(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num,
    bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            // ASSERT_EQ(NO_DATA, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *T1, *T2, *T3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);
            TestGmcNodeGetPropertyByName_p(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(T3, j, &T3);
                ASSERT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_V(
                    T3, ((i + g_end_num * index) * vector_num + j + 1), bool_value, f14_value);
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 读取表中的数据  (没有vector node)
void TestGmcDirectFetchVertexNoVector(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num,
    bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            // ASSERT_EQ(NO_DATA, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *T1, *T2, *T3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);
            TestGmcNodeGetPropertyByName_p(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
            // 读取array节点
            ret = GmcNodeGetChild(T1, "T2", &T2);
            ASSERT_EQ(GMERR_OK, ret);
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(T2, j, &T2);
                ASSERT_EQ(GMERR_OK, ret);
                TestGmcNodeGetPropertyByName_A(
                    T2, ((i + g_end_num * index) * array_num + j + 1), bool_value, f14_value);
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 读取表中的数据(读取insertAdjacentNode封装函数中的数据)
void TestGmcDirectFetchAdjacentNodeVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, const char *labelName, const char *keyName, bool read_num, bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        printf(" FetchAdjacentNode index is %d \n", f0_value);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            // ASSERT_EQ(NO_DATA, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *T1, *T2, *T3;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &T3);
            EXPECT_EQ(GMERR_OK, ret);

            TestGmcNodeGetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value, isBitmap);
            TestGmcNodeGetPropertyByName_p(T1, i + g_end_num * index, bool_value, f14_value, isBitmap);
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 读取表中的数据(32Deep)
void TestGmcDirectFetch32DeepVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num,
    int end_num, int array_num, int vector_num, const char *labelName, const char *keyName, bool read_num,
    bool isBitmap = true)
{
    int32_t ret = 0;
    void *label = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i + g_end_num * index;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (read_num == false) {
            // ASSERT_EQ(NO_DATA, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ(isFinish, true);
        } else if (read_num == true) {
            ASSERT_EQ(GMERR_OK, ret);
            bool isFinish = true;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            GmcNodeT *root, *T1, *T31, *M31;
            ret = GmcGetRootNode(stmt, &root);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &T1);
            EXPECT_EQ(GMERR_OK, ret);
            // 通过stmt 句柄 直接获取 指定层级的node节点
            // vector 型node
            ret = GmcGetChildNode(stmt,
                (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/"
                        "T26/T27/T28/T29/T30/T31",
                &T31);
            EXPECT_EQ(GMERR_OK, ret);
            // fixed_array 型node
            ret = GmcGetChildNode(stmt,
                (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/T24/T25/"
                        "T26/T27/T28/T29/T30/M31",
                &M31);
            EXPECT_EQ(GMERR_OK, ret);

            // 读取 根节点上的值
            bool isNull;
            uint64_t f0_value;

            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(uint64_t), &isNull);
            ASSERT_EQ(GMERR_OK, ret);
            ASSERT_EQ((unsigned int)0, isNull);
            ASSERT_EQ(i + g_end_num * index, f0_value);

            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(M31, j, &M31);
                ASSERT_EQ(GMERR_OK, ret);
                int64_t A31_value;
                ret = GmcNodeGetPropertyByName(M31, (char *)"A31", &A31_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(i + g_end_num * index, A31_value);
            }
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetElementByIndex(T31, j, &T31);
                ASSERT_EQ(GMERR_OK, ret);
                int64_t D31_value;
                ret = GmcNodeGetPropertyByName(T31, (char *)"D31", &D31_value, sizeof(int64_t), &isNull);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ((unsigned int)0, isNull);
                ASSERT_EQ(i + g_end_num * index, D31_value);
            }
        }
        GmcFreeIndexKey(stmt);
    }
    ret = GmcResetVertex(stmt, false);
    ASSERT_EQ(GMERR_OK, ret);
}

// 更新表中的数据
void TestGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i );
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }
    //插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(T3, j, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
        // printf("V0 is %d \n",(i + g_end_num * index)*vector_num+j+1);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

void TestGmcUpdateVertexByIndexKeyBatch(
    GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num, int vector_num, char *keyName)
{
    int32_t ret = 0;
    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(t1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(t1, "T2", &t2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(t2, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
        GmcNodeGetNextElement(t2, &t2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(t3, j, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(t3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 更新表中的数据 (对vector node 做 set操作)
void TestGmcAppendUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    // 对于主键 还是 i 值
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i );
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }
    //插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        // printf("update vector node index  is %d \n",j);
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    printf("ret is %d \n", ret);
    ret = testGmcGetLastError(NULL);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 更新表中的数据
void TestGmcUpdateAdjacentNodeVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i + g_end_num * index);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, i + g_end_num * index, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }
    //插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        // ret = GmcNodeGetElementByIndex(T3, j, &T3);
        // ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, i + g_end_num * index, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 更新表中的数据 (remove 操作测试  remove vector index 为2 的那组数据  )
void TestGmcRemoveUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    // 对于主键 还是 i 值
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i );
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }

    ret = GmcNodeRemoveElementByIndex(T3, 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 更新表中的数据  (clear 操作 删除 array 类型的node数据)
void TestGmcClearUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    // 对于主键 还是 i 值
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i );
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);

    // clear   array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T2);
    ASSERT_EQ(GMERR_OK, ret);

    //插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        // 更新的时候 通过 vactor的index 来确定 size  index 从0 开始
        printf("update vector node index  is %d \n", j);
        ret = GmcNodeGetElementByIndex(T3, j, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
    }
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    printf("ret is %d \n", ret);
    ret = testGmcGetLastError(NULL);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 更新表中的数据  (clear 操作删除 vector类型的node)
void TestClearVctorGmcUpdateVertexByIndexKey(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value,
    int array_num, int vector_num, char *keyName, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 更新顶点
    // 对于主键 还是 i 值
    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    // TestGmcNodeSetPropertyByName_PK(root, i );
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        GmcNodeGetNextElement(T2, &T2);
    }
    // 清空 vector节点
    ret = GmcNodeClear(T3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    printf("ret is %d \n", ret);
    ret = testGmcGetLastError(NULL);

    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(1, affectRows);
}

// 替换表中的数据
void TestGmcReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        ret = GmcNodeGetNextElement(T2, &T2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}

// 替换表中的数据 Clear Vector Node 节点
void TestClearVectorGmcReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    // printf("*************** i + g_end_num * index  is %d *************** \n",i + g_end_num * index);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        // 每个 array 类型的node节点 index为 0——array_num 中的字段都设置为 0——array_num
        ret = GmcNodeGetNextElement(T2, &T2);
    }
    // clear Vector Node
    ret = GmcNodeClear(T3);
    printf(" clear T3 ret is %d \n", ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}
// 替换表中的数据
void TestClearGmcReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    // printf("*************** i + g_end_num * index  is %d *************** \n",i + g_end_num * index);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeClear(T2);
    printf(" ret is %d \n", ret);
    // for (uint32_t j = 0; j < array_num; j++) {
    //     TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index)*array_num+j+1, bool_value, f14_value);
    //     // 每个 array 类型的node节点 index为 0——array_num 中的字段都设置为 0——array_num
    //     ret = GmcNodeGetNextElement(T2, &T2);
    // }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
        // 每个 vector类型 的node节点 index为 0——vector_num 中的字段都设置为 0——vector_num
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}

// 替换表中的数据 (对vector node 做 set操作)
void TestGmcAppendReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    // printf("*************** i + g_end_num * index  is %d *************** \n",i + g_end_num * index);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        // 每个 array 类型的node节点 index为 0——array_num 中的字段都设置为 0——array_num
        ret = GmcNodeGetNextElement(T2, &T2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeAppendElement(T3, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
        // 每个 vector类型 的node节点 index为 0——vector_num 中的字段都设置为 0——vector_num
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}

// 替换表中的数据 (remove 操作测试  remove vector index 为2 的那组数据)
void TestGmcRemoveReplaceVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);
    // printf("*************** i + g_end_num * index  is %d *************** \n",i + g_end_num * index);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        // 每个 array 类型的node节点 index为 0——array_num 中的字段都设置为 0——array_num
        ret = GmcNodeGetNextElement(T2, &T2);
    }

    // ret = GmcNodeRemoveElementByIndex(T3,0);
    // ASSERT_EQ(GMERR_OK, ret);
    // printf("ret is %d \n",ret);
    // ret=testGmcGetLastError(NULL);

    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}

// 合并表中的数据
void TestGmcMergeVertex(GmcStmtT *stmt, int i, int index, bool bool_value, char *f14_value, int array_num,
    int vector_num, int affect_rows, char *label_name)
{
    int32_t ret = 0;
    int affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    int64_t f0_value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    EXPECT_EQ(GMERR_OK, ret);
    char PKName_[] = "ArrayVectorConditionalSubscription_Tree_PK";
    ret = GmcSetIndexKeyName(stmt, PKName_);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *root, *T1, *T2, *T3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入顶点
    TestGmcNodeSetPropertyByName_PK(root, i);
    TestGmcNodeSetPropertyByName_R(root, i + g_end_num * index, bool_value, f14_value);

    TestGmcNodeSetPropertyByName_P(T1, i + g_end_num * index, bool_value, f14_value);
    // 插入array节点
    ret = GmcNodeGetChild(T1, "T2", &T2);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t j = 0; j < array_num; j++) {
        TestGmcNodeSetPropertyByName_A(T2, (i + g_end_num * index) * array_num + j + 1, bool_value, f14_value);
        // 每个 array 类型的node节点 index为 0——array_num 中的字段都设置为 0——array_num
        ret = GmcNodeGetNextElement(T2, &T2);
    }
    // 插入vector节点
    for (uint32_t j = 0; j < vector_num; j++) {
        ret = GmcNodeGetElementByIndex(T3, j, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeSetPropertyByName_V(T3, (i + g_end_num * index) * vector_num + j + 1, bool_value, f14_value);
        // 每个 vector类型 的node节点 index为 0——vector_num 中的字段都设置为 0——vector_num
    }
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(affect_rows, affectRows);
}
void sn_callback_del(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }

                    //读old
                    bool_value = 0;  // 这里的bool 值写死了 对于旧值 来说 都是 0 对于新值都是 1
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_MERGE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }

                    //读old
                    bool_value = 0;  // 这里的bool 值写死了 对于旧值 来说 都是 0 对于新值都是 1
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_MERGE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_MERGE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数 (读取insertAdjacentNode封装函数中的数据)
void sn_callback_AdjacentNode(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{

    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // // 读取array节点
                    // for (uint32_t j = 0; j < array_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    // }

                    // for (uint32_t j = 0; j < vector_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    // }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);

                    //读old
                    bool_value = 0;  // 这里的bool 值写死了 对于旧值 来说 都是 0 对于新值都是 1
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    // printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // // 读取array节点
                    // for (uint32_t j = 0; j < array_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    // }

                    // for (uint32_t j = 0; j < vector_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    // }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数 (32Deep)
void sn_callback_32Deep(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                bool isNull;
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *T1, *T31, *M31;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &T1);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 通过stmt 句柄 直接获取 指定层级的node节点
                    // vector 型node
                    ret = GmcGetChildNode(subStmt,
                        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/"
                                "T24/T25/T26/T27/T28/T29/T30/T31",
                        &T31);
                    EXPECT_EQ(GMERR_OK, ret);
                    // fixed_array 型node
                    ret = GmcGetChildNode(subStmt,
                        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/"
                                "T24/T25/T26/T27/T28/T29/T30/M31",
                        &M31);
                    EXPECT_EQ(GMERR_OK, ret);

                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(M31, j, &M31);
                        ASSERT_EQ(GMERR_OK, ret);
                        int64_t A31_value;
                        ret = GmcNodeGetPropertyByName(M31, (char *)"A31", &A31_value, sizeof(int64_t), &isNull);
                        ASSERT_EQ(GMERR_OK, ret);
                        ASSERT_EQ((unsigned int)0, isNull);
                        ASSERT_EQ(index, A31_value);
                    }
                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(T31, j, &T31);
                        ASSERT_EQ(GMERR_OK, ret);
                        int64_t D31_value;
                        ret = GmcNodeGetPropertyByName(T31, (char *)"D31", &D31_value, sizeof(int64_t), &isNull);
                        ASSERT_EQ(GMERR_OK, ret);
                        ASSERT_EQ((unsigned int)0, isNull);
                        ASSERT_EQ(index, D31_value);
                    }

                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    // GmcNodeT *root, *t1, *t2, *t3;
                    // ret = GmcGetRootNode(subStmt, &root);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // ret = GmcNodeGetChild(root, "T1", &t1);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // ret = GmcNodeGetChild(root, "T3", &t3);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    // TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    // ret = GmcNodeGetChild(t1, "T2", &t2);
                    // EXPECT_EQ(GMERR_OK, ret);
                    // // 读取array节点
                    // for (uint32_t j = 0; j < array_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    // }

                    // for (uint32_t j = 0; j < vector_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t3, j, &t3);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    // }

                    GmcNodeT *root, *T1, *T31, *M31;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &T1);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 通过stmt 句柄 直接获取 指定层级的node节点
                    // vector 型node
                    ret = GmcGetChildNode(subStmt,
                        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/"
                                "T24/T25/T26/T27/T28/T29/T30/T31",
                        &T31);
                    EXPECT_EQ(GMERR_OK, ret);
                    // fixed_array 型node
                    ret = GmcGetChildNode(subStmt,
                        (char *)"T1/T2/T3/T4/T5/T6/T7/T8/T9/T10/T11/T12/T13/T14/T15/T16/T17/T18/T19/T20/T21/T22/T23/"
                                "T24/T25/T26/T27/T28/T29/T30/M31",
                        &M31);
                    EXPECT_EQ(GMERR_OK, ret);

                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(M31, j, &M31);
                        ASSERT_EQ(GMERR_OK, ret);
                        int64_t A31_value;
                        ret = GmcNodeGetPropertyByName(M31, (char *)"A31", &A31_value, sizeof(int64_t), &isNull);
                        ASSERT_EQ(GMERR_OK, ret);
                        ASSERT_EQ((unsigned int)0, isNull);
                        ASSERT_EQ(index, A31_value);
                    }
                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(T31, j, &T31);
                        ASSERT_EQ(GMERR_OK, ret);
                        int64_t D31_value;
                        ret = GmcNodeGetPropertyByName(T31, (char *)"D31", &D31_value, sizeof(int64_t), &isNull);
                        ASSERT_EQ(GMERR_OK, ret);
                        ASSERT_EQ((unsigned int)0, isNull);
                        ASSERT_EQ(index, D31_value);
                    }

                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }

                    //读old
                    bool_value = 0;  // 这里的bool 值写死了 对于旧值 来说 都是 0 对于新值都是 1
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, index, bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, index, bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数  append 操作 update replace 事件触发推送
void sn_callbackAppend(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数  remove 操作 update replace 事件触发推送
void sn_callbackRemove(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 2;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数
void sn_callbackfixed_arraySize(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 2,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }

                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // printf("************** user_data->subIndex is %d ***********\n",user_data->subIndex);
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数  clearArray  操作 update replace 事件触发推送
void sn_callbackClearArray(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, array_num = 3,
                       vector_num = 3;  //这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14_value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    bool_value = 0;
                    strcpy(f14_value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < array_num; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * array_num + j + 1), bool_value, f14_value);
                    }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    bool_value = 1;
                    strcpy(f14_value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // // 读取array节点
                    // for (uint32_t j = 0; j < array_num; j++) {
                    //     ret = GmcNodeGetElementByIndex(t2, j, &t2);
                    //     EXPECT_EQ(GMERR_OK, ret);
                    //     TestGmcNodeGetPropertyByName_A(t2, (index*array_num+j+1), bool_value, f14_value);
                    // }

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14_value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14_value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14_value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14_value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);

                    for (uint32_t j = 0; j < vector_num; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vector_num + j + 1), bool_value, f14_value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//推送 回调函数
void sn_callbackClearVector(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, arrayNum = 3,
                       vectorNum = 3;  // 这个回调函数 验证 数量 与在 写数据的时候 的array 和 vector的数量 要保持一直
    bool bool_value;
    char f14Value[256] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    bool_value = 0;
                    strcpy(f14Value, "string1");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    // 一共写 100 条数据 （0-99） index  就是对应的字段的值
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14Value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14Value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < arrayNum; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * arrayNum + j + 1), bool_value, f14Value);
                    }

                    for (uint32_t j = 0; j < vectorNum; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vectorNum + j + 1), bool_value, f14Value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    bool_value = 1;
                    strcpy(f14Value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14Value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14Value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < arrayNum; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * arrayNum + j + 1), bool_value, f14Value);
                    }

                    for (uint32_t j = 0; j < vectorNum; j++) {
                        ret = GmcNodeGetElementByIndex(t3, j, &t3);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_V(t3, (index * vectorNum + j + 1), bool_value, f14Value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    bool_value = 1;
                    strcpy(f14Value, "1234567");
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    // printf("************user_data->subIndex is %d ****************\n",user_data->subIndex);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14Value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14Value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < arrayNum; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * arrayNum + j + 1), bool_value, f14Value);
                    }
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        bool_value = 0;
                        strcpy(f14Value, "string1");
                    } else {
                        bool_value = 1;
                        strcpy(f14Value, "1234567");
                    }
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root, *t1, *t2, *t3;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T1", &t1);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcNodeGetChild(root, "T3", &t3);
                    EXPECT_EQ(GMERR_OK, ret);
                    TestGmcNodeGetPropertyByName_R(root, index, bool_value, f14Value);
                    TestGmcNodeGetPropertyByName_p(t1, index, bool_value, f14Value);
                    ret = GmcNodeGetChild(t1, "T2", &t2);
                    EXPECT_EQ(GMERR_OK, ret);
                    // 读取array节点
                    for (uint32_t j = 0; j < arrayNum; j++) {
                        ret = GmcNodeGetElementByIndex(t2, j, &t2);
                        EXPECT_EQ(GMERR_OK, ret);
                        TestGmcNodeGetPropertyByName_A(t2, (index * arrayNum + j + 1), bool_value, f14Value);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }

        user_data->subIndex++;  // 这里的 index 就是前面用到的 userDataIdx 如果前面一个事件订阅结束以后没有 将index
                                // 重置为0 这里的index 也要一直加

        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE: {
                user_data->mergeNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_Age(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{

    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    int ret;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                if (0 == user_data->insertNum % 10000) {
                    printf("[info]--user_data->insertNum-->%d\n", user_data->insertNum);
                }
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                if (0 == user_data->deleteNum % 2000) {
                    printf("[info]--user_data->deleteNum-->%d\n", user_data->deleteNum);
                }
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                if (0 == user_data->updateNum % 2000) {
                    printf("[info]--user_data->updateNum-->%d\n", user_data->updateNum);
                }
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);
                uint32_t F7Value;
                bool isNull;
                ret = GmcGetVertexPropertyByName(subStmt, "F7", &F7Value, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                if (!isNull) {
                    printf("F7Value  :  %d\n", F7Value);
                }
                if (0 == user_data->replaceNum % 2000) {
                    printf("[info]--user_data->replaceNum-->%d\n", user_data->replaceNum);
                }
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                if (0 == user_data->agedNum % 1000) {
                    printf("[info]--user_data->agedNum-->%d\n", user_data->agedNum);
                }
                break;
            }
            default:
                break;
        }
    }
}
