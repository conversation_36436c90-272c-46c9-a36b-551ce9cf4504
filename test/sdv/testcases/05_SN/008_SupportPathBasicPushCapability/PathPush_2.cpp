/*****************************************************************************
 Description  : 009
FIB路径上，所有表项已创建，创建PATH订阅关系，开启事物，开启批量操作，写入超过buffer大小的数据，DB有错误码返回 Notes :
010 订阅FIB PATH，指定获取7#、8#表中的特定字段数据，DB中构造数据写入顺序为1条
7#，1条9#，1条10#，1条42#，1条8#，数据都关联到最后插入的这条7#表记录，客户端可正常消费所有数据. 011 订阅FIB
PATH，指定获取7#、8#表中的特定字段数据，DB中构造数据写入(批量操作)顺序为1条
7#，1条9#，1条10#，1条42#，1条8#，10条数据都关联到最后插入的这条7#表记录，客户端可正常消费所有数据. 012
FIB表下，APP订阅path，label42表插入一条数据，正确推送变更path.(迭代五支持) 013 订阅FIB
PATH，指定获取7#、8#表中的特定字段数据，DB中构造数据写入顺序为1条
7#，1条9#，1条10#，1条42#，1条8#，10条数据都关联到最后插入的这条7#表记录，客户端可正常消费所有数据. History      :
 Author       : jiangdingshan/jwx992802
 Modification :
 Date         : 2020/12/4
*****************************************************************************/
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "sub_path.h"

using namespace std;

#define DM_MAX_NAME_LENGTH 1024
#define DM_MIX_NAME_LENGTH 512
const char *g_subName = "subPath";
const char *g_subConnName = "testSubPushPathConnName1";

GmcConnT *syncConn = NULL;
GmcStmtT *stmt = NULL;
int ret;

const char *subLabelNameT7 = "T7";
const char *subLabelCfgJsonT7 = R"({"max_record_count":4096000})";
char *subLabelJsonT7 = NULL;

const char *subLabelNameT8 = "T8";
const char *subLabelCfgJsonT8 = R"({"max_record_count":4096000})";
char *subLabelJsonT8 = NULL;

const char *subLabelNameT9 = "T9";
const char *subLabelCfgJsonT9 = R"({"max_record_count":4096000})";
char *subLabelJsonT9 = NULL;

const char *subLabelNameT10 = "T10";
const char *subLabelCfgJsonT10 = R"({"max_record_count":1000})";
char *subLabelJsonT10 = NULL;

const char *subLabelNameT42 = "T42";
const char *subLabelCfgJsonT42 = R"({"max_record_count":4096000})";
char *subLabelJsonT42 = NULL;

const char *edgeLabelNameF78 = (char *)"from_7_to_8";
const char *edgeCfgJson78 = (char *)"{\"max_record_count\":4096000}";
char *edgeLabelJson78 = NULL;

const char *edgeLabelNameF89 = "from_8_to_9";
const char *edgeCfgJson89 = R"({"max_record_count":4096000})";
char *edgeLabelJson89 = NULL;

const char *edgeLabelNameF910 = (char *)"from_9_to_10";
const char *edgeCfgJson910 = (char *)"{\"max_record_count\":4096000}";
char *edgeLabelJson910 = NULL;

const char *edgeLabelNameF1042 = (char *)"from_10_to_42";
const char *edgeCfgJson1042 = (char *)"{\"max_record_count\":4096000}";
char *edgeLabelJson1042 = NULL;

const char *subPathJsonT7 = (char *)R"({
    "name":"subPath",
    "label_name":"T7",
    "comment":"path subscription",
    "type":"before_commit",
    "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]}
        ],
    "is_path":true,
    "retry":true,
    "cypher":"MATCH path = (node1:T7{vr_id:$id and vrf_index:$id and dest_ip_addr:$id and mask_len:$id})-[:from_7_to_8]->(node2:T8)-[:from_8_to_9]->(node3:T9)-[:from_9_to_10]->(node4:T10)-[:from_10_to_42]->(node5:T42) return path"
    })";

const char *subPathJsonT10 = (char *)R"({
    "name":"subPath",
    "label_name":"T10",
    "comment":"path subscription",
    "type":"before_commit",
    "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]}
        ],
    "is_path":true,
    "retry":true,
    "cypher":"MATCH path = (node1:T10{nhp_index:$id})-[:from_10_to_42]->(node2:T42) return path"
    })";
class SupportPathBasicPushCapability : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;

    virtual void SetUp();
    virtual void TearDown();
};

void SupportPathBasicPushCapability::SetUp()
{
    printf("SupportPathBasicPushCapability Start.\n");

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    user_data->new_value = (int *)malloc(sizeof(int) * 100 * 10 * 3);
    memset(user_data->new_value, 0, sizeof(int) * 100 * 10 * 3);
    user_data->old_value = (int *)malloc(sizeof(int) * 100 * 10 * 3);
    memset(user_data->old_value, 0, sizeof(int) * 100 * 10 * 3);

    //创建同步连接
    ret = testGmcConnect(&syncConn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    //创建label1
    readJanssonFile("schema_file_2/ip4forward.gmjson", &subLabelJsonT7);
    ASSERT_NE((void *)NULL, subLabelJsonT7);
    ret = GmcCreateVertexLabel(stmt, subLabelJsonT7, subLabelCfgJsonT7);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/nhp_group.gmjson", &subLabelJsonT8);
    ASSERT_NE((void *)NULL, subLabelJsonT8);
    ret = GmcCreateVertexLabel(stmt, subLabelJsonT8, subLabelCfgJsonT8);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/nhp_group_node.gmjson", &subLabelJsonT9);
    ASSERT_NE((void *)NULL, subLabelJsonT9);
    ret = GmcCreateVertexLabel(stmt, subLabelJsonT9, subLabelCfgJsonT9);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/nhp.gmjson", &subLabelJsonT10);
    ASSERT_NE((void *)NULL, subLabelJsonT10);
    ret = GmcCreateVertexLabel(stmt, subLabelJsonT10, subLabelCfgJsonT10);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/nhp_std.gmjson", &subLabelJsonT42);
    ASSERT_NE((void *)NULL, subLabelJsonT42);
    ret = GmcCreateVertexLabel(stmt, subLabelJsonT42, subLabelCfgJsonT42);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/edge_label_78json.gmjson", &edgeLabelJson78);
    ASSERT_NE((void *)NULL, edgeLabelJson78);
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson78, edgeCfgJson78);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/edge_label_89json.gmjson", &edgeLabelJson89);
    ASSERT_NE((void *)NULL, edgeLabelJson89);
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson89, edgeCfgJson89);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/edge_label_910json.gmjson", &edgeLabelJson910);
    ASSERT_NE((void *)NULL, edgeLabelJson910);
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson910, edgeCfgJson910);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file_2/edge_label_1042json.gmjson", &edgeLabelJson1042);
    ASSERT_NE((void *)NULL, edgeLabelJson1042);
    ret = GmcCreateEdgeLabel(stmt, edgeLabelJson1042, edgeCfgJson1042);
    ASSERT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
}
void SupportPathBasicPushCapability::TearDown()
{
    AW_CHECK_LOG_END();
    // 释放订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropEdgeLabel(stmt, edgeLabelNameF78);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, edgeLabelNameF89);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, edgeLabelNameF910);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, edgeLabelNameF1042);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelNameT7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelNameT8);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelNameT9);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelNameT10);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelNameT42);
    ASSERT_EQ(GMERR_OK, ret);

    free(subLabelJsonT7);
    free(subLabelJsonT8);
    free(subLabelJsonT9);
    free(subLabelJsonT10);
    free(subLabelJsonT42);
    free(edgeLabelJson78);
    free(edgeLabelJson89);
    free(edgeLabelJson910);
    free(edgeLabelJson1042);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data);

    // 同步连接释放
    ret = testGmcDisconnect(syncConn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("SupportPathBasicPushCapability End.\n");
}

TEST_F(SupportPathBasicPushCapability, SN_008_009)
{

    int MAX_SIZE = 1024 * 7;  // 7k
    char str[MAX_SIZE];
    memset(str, 0x00, MAX_SIZE * sizeof(char));
    for (int n = 0; n < MAX_SIZE - 1; n++) {
        str[n] = 'a' + n % 26;
    }
    str[MAX_SIZE - 1] = '\0';

    // 创建订阅请求
    GmcSubConfigT tmp_subPathJsonT7;
    tmp_subPathJsonT7.subsName = (char *)"subPath";
    tmp_subPathJsonT7.configJson = subPathJsonT7;
    ret = GmcSubscribe(stmt, &tmp_subPathJsonT7, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T42", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 先行插入path中第5个表数据

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        uint32_t F1Value = 7;
        ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F1Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F2Value = 2;
        ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &F2Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F3Value = 3;
        ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &F3Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchReset(batch);

    void *vertexLabel2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T10", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        uint32_t F16Value = 7;
        ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F16Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel3 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T9", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_STRING, str, (strlen(str)));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F9Value = 8;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F9Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F10Value = 6;
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &F10Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F11Value = 7;
        ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &F11Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F12Value = 8;
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &F12Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F13Value = 9;
        ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &F13Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F14Value = 10;
        ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &F14Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F15Value = 11;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F15Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchReset(batch);

    void *vertexLabel4 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T8", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1; i++) {
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_STRING, str, (strlen(str)));
        ASSERT_EQ(GMERR_OK, ret);
        // printf("-----|%s|------  \n", str);
        uint32_t F8Value = 8;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F8Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F81Value = 81;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F81Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchReset(batch);

    void *vertexLabel5 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T7", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(syncConn, &config);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(syncConn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i < 2; i++) {
        // 插入path中第一个表数据
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_STRING, str, (strlen(str)));
        ASSERT_EQ(GMERR_OK, ret);
        // printf("-----|%s|------  \n", str);
        uint32_t F7Value = 8;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F7Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F71Value = 12;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F71Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F72Value = 5;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &F72Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F73Value = 9;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &F73Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t F74Value = 12;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &F74Value, sizeof(F74Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);

    // 提交事务生成推送订阅数据
    ret = GmcTransCommit(syncConn);
    ASSERT_EQ(GMERR_OK, ret);

    //等待epoll中所有推送消息接收完毕
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    ASSERT_EQ(GMERR_OK, ret);
    //取消订阅
    ret = GmcUnSubscribe(stmt, "subPath");
    ASSERT_EQ(GMERR_OK, ret);

    memset(str, 0, sizeof(str));
}

TEST_F(SupportPathBasicPushCapability, SN_008_010)
{

    // 创建订阅请求
    GmcSubConfigT tmp_subPathJsonT7;
    tmp_subPathJsonT7.subsName = (char *)"subPath";
    tmp_subPathJsonT7.configJson = subPathJsonT7;
    ret = GmcSubscribe(stmt, &tmp_subPathJsonT7, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T42", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 先行插入path中第5个表数据

    uint32_t F1Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F1Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &F2Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = 3;
    ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &F3Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T10", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F16Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F16Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel3 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T9", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F9Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F9Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F10Value = 6;
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &F10Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F11Value = 7;
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &F11Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F12Value = 8;
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &F12Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F13Value = 9;
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &F13Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F14Value = 10;
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &F14Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F15Value = 11;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F15Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel4 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T8", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F8Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F8Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F81Value = 81;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F81Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel5 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T7", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(syncConn, &config);
    for (int i = 1; i < 2; i++) {
        // 插入path中第一个表数据
        uint32_t F7Value = 7 + i;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F7Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F71Value = 71;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F71Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F72Value = 72;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &F72Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F73Value = 73;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &F73Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t F74Value = 2;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &F74Value, sizeof(F74Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ASSERT_EQ(GMERR_OK, ret);

    // 提交事务生成推送订阅数据
    ret = GmcTransCommit(syncConn);
    ASSERT_EQ(GMERR_OK, ret);

    //等待epoll中所有推送消息接收完毕
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    ASSERT_EQ(GMERR_OK, ret);

    //取消订阅
    ret = GmcUnSubscribe(stmt, "subPath");
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(SupportPathBasicPushCapability, SN_008_011)
{

    // 创建订阅请求
    GmcSubConfigT tmp_subPathJsonT7;
    tmp_subPathJsonT7.subsName = (char *)"subPath";
    tmp_subPathJsonT7.configJson = subPathJsonT7;
    ret = GmcSubscribe(stmt, &tmp_subPathJsonT7, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T42", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 先行插入path中第5个表数据

    uint32_t F1Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F1Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &F2Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = 3;
    ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &F3Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T10", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F16Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F16Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel3 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T9", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F9Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F9Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F10Value = 6;
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &F10Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F11Value = 7;
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &F11Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F12Value = 8;
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &F12Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F13Value = 9;
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &F13Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F14Value = 10;
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &F14Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F15Value = 11;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F15Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel4 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T8", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F8Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F8Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F81Value = 81;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F81Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel5 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T7", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(syncConn, NULL, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i < 2; i++) {
        // 插入path中第一个表数据
        uint32_t F7Value = 8;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F7Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F71Value = i;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F71Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F72Value = 72;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &F72Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F73Value = 73;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &F73Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t F74Value = 2;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &F74Value, sizeof(F74Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    printf("--totalNum--0-- =%d,--successNum--=%d\n", totalNum, successNum);
    ASSERT_EQ(GMERR_OK, ret);

    //等待epoll中所有推送消息接收完毕
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    ASSERT_EQ(GMERR_OK, ret);

    //取消订阅
    ret = GmcUnSubscribe(stmt, "subPath");
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(SupportPathBasicPushCapability, SN_008_012)
{

    // 创建订阅请求
    GmcSubConfigT tmp_subPathJsonT10;
    tmp_subPathJsonT10.subsName = (char *)"subPath";
    tmp_subPathJsonT10.configJson = subPathJsonT10;
    ret = GmcSubscribe(stmt, &tmp_subPathJsonT10, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T42", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 先行插入path中第2个表数据

    uint32_t F1Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F1Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &F2Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = 3;
    ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &F3Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T10", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F16Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F16Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    //等待epoll中所有推送消息接收完毕
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    ASSERT_EQ(GMERR_OK, ret);

    //取消订阅
    ret = GmcUnSubscribe(stmt, "subPath");
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(SupportPathBasicPushCapability, SN_008_013)
{

    // 创建订阅请求
    GmcSubConfigT tmp_subPathJsonT7;
    tmp_subPathJsonT7.subsName = (char *)"subPath";
    tmp_subPathJsonT7.configJson = subPathJsonT7;
    ret = GmcSubscribe(stmt, &tmp_subPathJsonT7, g_conn_sub, sn_callback_not_cmp, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T42", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // 先行插入path中第5个表数据

    uint32_t F1Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F1Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "next_hop", GMC_DATATYPE_UINT32, &F2Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = 3;
    ret = GmcSetVertexProperty(stmt, "out_if_index", GMC_DATATYPE_UINT32, &F3Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T10", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F16Value = 7;
    ret = GmcSetVertexProperty(stmt, "nhp_index", GMC_DATATYPE_UINT32, &F16Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel3 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T9", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F9Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F9Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F10Value = 6;
    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &F10Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F11Value = 7;
    ret = GmcSetVertexProperty(stmt, "primary_nhp_id", GMC_DATATYPE_UINT32, &F11Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F12Value = 8;
    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &F12Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F13Value = 9;
    ret = GmcSetVertexProperty(stmt, "backup_nhp_id", GMC_DATATYPE_UINT32, &F13Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F14Value = 10;
    ret = GmcSetVertexProperty(stmt, "backup_label", GMC_DATATYPE_UINT32, &F14Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F15Value = 11;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F15Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel4 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T8", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t F8Value = 8;
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F8Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F81Value = 81;
    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F81Value, 4);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, ret);

    void *vertexLabel5 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "T7", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(syncConn, &config);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 1; i < 2; i++) {
        // 插入path中第一个表数据
        uint32_t F7Value = 8;
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &F7Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F71Value = i;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &F71Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F72Value = 72;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &F72Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t F73Value = 73;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &F73Value, 4);
        ASSERT_EQ(GMERR_OK, ret);
        uint8_t F74Value = 2;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &F74Value, sizeof(F74Value));
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ASSERT_EQ(GMERR_OK, ret);
    // 提交事务生成推送订阅数据
    ret = GmcTransCommit(syncConn);
    ASSERT_EQ(GMERR_OK, ret);

    //等待epoll中所有推送消息接收完毕
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    ASSERT_EQ(GMERR_OK, ret);

    //取消订阅
    ret = GmcUnSubscribe(stmt, "subPath");
    ASSERT_EQ(GMERR_OK, ret);
}
