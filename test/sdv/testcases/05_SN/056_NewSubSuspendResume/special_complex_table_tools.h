//
// Created by w00495442 on 2021/11/2.
//

#ifndef __SPECIAL_COMPLEX_TABLE_TOOLS_H__
#define __SPECIAL_COMPLEX_TABLE_TOOLS_H__
extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "special_complex_table_struct.h"

#define MAX_CMD_SIZE 1024
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_schema = NULL;
int affectRows;
char g_special_complex_table_name[] = "TEST_SC_T1", g_special_complex_table_name2[] = "TEST_SC_T2",
     g_special_complex_table_name3[] = "TEST_SC_T3", g_special_complex_table_name4[] = "TEST_SC_T4",
     g_special_complex_table_name5[] = "TEST_SC_T5", g_special_complex_table_name6[] = "TEST_SC_T6",
     g_special_complex_table_name7[] = "TEST_SC_T7", g_special_complex_table_name8[] = "TEST_SC_T8",
     g_special_complex_table_name9[] = "TEST_SC_T9", g_special_complex_table_name10[] = "TEST_SC_T10";
char g_pk_name[] = "TEST_PK";
char g_command[MAX_CMD_SIZE] = {0};
int g_data_num = RECORD_NUM_003;
pthread_mutex_t g_threadLock;
const char *g_subName = "subVertexLabel";
GmcCheckInfoT *checkInfo, *checkInfo2;
GmcCheckStatusE checkStatus, checkStatus2;
bool isAbnormal = false;
#define FULLTABLE 0xff

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *resPoolTestName = "resource_pool_test";

using namespace std;

void db_test_struct_write_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}
void db_test_struct_write_special_complex_table_no_child_node(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value_no_child_node(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_replace_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        // TEST_INFO("STRUCT INSERT", i, 10000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_replace_special_complex_table_root2(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value_root2(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_write_special_complex_table_batch(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT BATCH INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, data_num);
    EXPECT_EQ(successNum, data_num);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_num, count);

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table_batch(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT BATCH INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, data_num);
    EXPECT_EQ(successNum, data_num);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_num, count);

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_replace_special_complex_table_batch(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT BATCH INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, data_num);
    EXPECT_EQ(successNum, data_num);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_num, count);

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void CompareVertexPropertyValue_R(TEST_SC_T1_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[11] = {0};
    (void)snprintf((char *)F16Value, sizeof(F16Value), "b%08d", index);
    char F17Value[21] = {0};
    (void)snprintf((char *)F17Value, sizeof(F17Value), "ABCDEFGHIJKL%08d", index);

    // root
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F16Value, d->F16, sizeof(F16Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F17Value, d->F17, sizeof(F17Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void CompareVertexPropertyValue_P(TEST_SC_T1_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[11] = {0};
    (void)snprintf((char *)F15Value, sizeof(F15Value), "b%08d", index);
    char F16Value[21] = {0};
    (void)snprintf((char *)F16Value, sizeof(F16Value), "ABCDEFGHIJKL%08d", index);

    // record 子节点
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->t1->P0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->t1->P1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->t1->P2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->t1->P3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->t1->P4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->t1->P5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->t1->P6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->t1->P7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->t1->P8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->t1->P9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->t1->P10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->t1->P11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->t1->P12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->t1->P13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->t1->P14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F15Value, d->t1->P15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F16Value, d->t1->P16, sizeof(F16Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void CompareVertexPropertyValue_A(TEST_SC_T1_struct_t *d, int index, bool bool_value)
{
    int ret;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    (void)snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[11] = {0};
    (void)snprintf((char *)A15Value, sizeof(A15Value), "b%08d", index);
    char A16Value[21] = {0};
    (void)snprintf((char *)A16Value, sizeof(A16Value), "ABCDEFGHIJKL%08d", index);

    // array
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &A0Value, &d->t2[index].A0, sizeof(A0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &A1Value, &d->t2[index].A1, sizeof(A1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &A2Value, &d->t2[index].A2, sizeof(A2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &A3Value, &d->t2[index].A3, sizeof(A3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &A4Value, &d->t2[index].A4, sizeof(A4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &A5Value, &d->t2[index].A5, sizeof(A5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &A6Value, &d->t2[index].A6, sizeof(A6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &A7Value, &d->t2[index].A7, sizeof(A7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &A8Value, &d->t2[index].A8, sizeof(A8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &A9Value, &d->t2[index].A9, sizeof(A9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &A10Value, &d->t2[index].A10, sizeof(A10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &A11Value, &d->t2[index].A11, sizeof(A11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &A12Value, &d->t2[index].A12, sizeof(A12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &A13Value, &d->t2[index].A13, sizeof(A13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, A14Value, d->t2[index].A14, sizeof(A14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &A15Value, d->t2[index].A15, sizeof(A15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &A16Value, d->t2[index].A16, sizeof(A16Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void CompareVertexPropertyValue_V(TEST_SC_T1_struct_t *d, int index, bool bool_value)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    (void)snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    char V15Value[11] = {0};
    (void)snprintf((char *)V15Value, sizeof(V15Value), "b%08d", index);
    char V16Value[21] = {0};
    (void)snprintf((char *)V16Value, sizeof(V16Value), "ABCDEFGHIJKL%08d", index);

    // vector
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V0Value, &d->t3[index].V0, sizeof(V0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &V1Value, &d->t3[index].V1, sizeof(V1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &V2Value, &d->t3[index].V2, sizeof(V2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &V3Value, &d->t3[index].V3, sizeof(V3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &V4Value, &d->t3[index].V4, sizeof(V4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &V5Value, &d->t3[index].V5, sizeof(V5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &V6Value, &d->t3[index].V6, sizeof(V6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &V7Value, &d->t3[index].V7, sizeof(V7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &V8Value, &d->t3[index].V8, sizeof(V8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &V9Value, &d->t3[index].V9, sizeof(V9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &V10Value, &d->t3[index].V10, sizeof(V10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &V11Value, &d->t3[index].V11, sizeof(V11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &V12Value, &d->t3[index].V12, sizeof(V12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &V13Value, &d->t3[index].V13, sizeof(V13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, V14Value, d->t3[index].V14, sizeof(V14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &V15Value, d->t3[index].V15, sizeof(V15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &V16Value, d->t3[index].V16, sizeof(V16Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_struct_read_TEST_SC_T1(
    TEST_SC_T1_struct_t *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R(d, index, bool_value);
    CompareVertexPropertyValue_P(d, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        CompareVertexPropertyValue_A(d, i, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        CompareVertexPropertyValue_V(d, i, bool_value);
    }
}

void test_struct_read_TEST_SC_T1_root2(
    TEST_SC_T1_struct_t *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R(d, index, bool_value);
    CompareVertexPropertyValue_P(d, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        CompareVertexPropertyValue_A(d, i, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        CompareVertexPropertyValue_V(d, i, bool_value);
    }
}

void test_struct_read_TEST_SC_T1_no_child_node(
    TEST_SC_T1_struct_t *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R(d, index, bool_value);
}

void db_test_struct_read_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T1(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_special_complex_table_no_child_node(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T1_no_child_node(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_special_complex_table_concurrency(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        // TEST_INFO("STRUCT SCAN", i, 10000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T1(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}
void db_test_struct_scan_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        test_struct_read_TEST_SC_T1(&obj, cnt, false, T2_count, T3_count);
        cnt++;
    }
    if (read_num == false) {
        EXPECT_EQ(0, cnt);
    } else if (read_num == true) {
        EXPECT_EQ(data_num, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_scan_special_complex_table_concurrency(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
}

void db_test_struct_read_special_complex_table_root2(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T1_root2(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_delete_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        // TEST_INFO("DELETE", i, 10000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_struct_delete_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        // TEST_INFO("STRUCT DELETE", i, 10000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_struct_delete_special_complex_table_batch(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, data_num);
    EXPECT_EQ(successNum, data_num);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
}

void TestGmcNodeGetPropertyByName_R(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[11] = {0};
    (void)snprintf((char *)F16Value, sizeof(F16Value), "b%08d", index);
    char F17Value[21] = {0};
    (void)snprintf((char *)F17Value, sizeof(F17Value), "ABCDEFGHIJKL%08d", index);

    // root
    ret = queryNodePropertyAndCompare(node, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F16", GMC_DATATYPE_STRING, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F17", GMC_DATATYPE_BYTES, F17Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_P(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[11] = {0};
    (void)snprintf((char *)F15Value, sizeof(F15Value), "b%08d", index);
    char F16Value[21] = {0};
    (void)snprintf((char *)F16Value, sizeof(F16Value), "ABCDEFGHIJKL%08d", index);

    // record子节点
    ret = queryNodePropertyAndCompare(node, "P0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P15", GMC_DATATYPE_STRING, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P16", GMC_DATATYPE_BYTES, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_A(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    (void)snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[11] = {0};
    (void)snprintf((char *)A15Value, sizeof(A15Value), "b%08d", index);
    char A16Value[21] = {0};
    (void)snprintf((char *)A16Value, sizeof(A16Value), "ABCDEFGHIJKL%08d", index);

    // array
    ret = queryNodePropertyAndCompare(node, "A0", GMC_DATATYPE_INT64, &A0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A1", GMC_DATATYPE_UINT64, &A1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A2", GMC_DATATYPE_INT32, &A2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A3", GMC_DATATYPE_UINT32, &A3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A4", GMC_DATATYPE_INT16, &A4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A5", GMC_DATATYPE_UINT16, &A5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A6", GMC_DATATYPE_INT8, &A6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A7", GMC_DATATYPE_UINT8, &A7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A8", GMC_DATATYPE_BOOL, &A8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A9", GMC_DATATYPE_FLOAT, &A9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A10", GMC_DATATYPE_DOUBLE, &A10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A11", GMC_DATATYPE_TIME, &A11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A12", GMC_DATATYPE_CHAR, &A12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A13", GMC_DATATYPE_UCHAR, &A13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A14", GMC_DATATYPE_FIXED, A14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A15", GMC_DATATYPE_STRING, A15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A16", GMC_DATATYPE_BYTES, A16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_V(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    (void)snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    char V15Value[11] = {0};
    (void)snprintf((char *)V15Value, sizeof(V15Value), "b%08d", index);
    char V16Value[21] = {0};
    (void)snprintf((char *)V16Value, sizeof(V16Value), "ABCDEFGHIJKL%08d", index);

    // vector
    ret = queryNodePropertyAndCompare(node, "V0", GMC_DATATYPE_INT64, &V0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V1", GMC_DATATYPE_UINT64, &V1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V2", GMC_DATATYPE_INT32, &V2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V3", GMC_DATATYPE_UINT32, &V3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V4", GMC_DATATYPE_INT16, &V4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V5", GMC_DATATYPE_UINT16, &V5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V6", GMC_DATATYPE_INT8, &V6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V7", GMC_DATATYPE_UINT8, &V7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V8", GMC_DATATYPE_BOOL, &V8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V9", GMC_DATATYPE_FLOAT, &V9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V10", GMC_DATATYPE_DOUBLE, &V10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V11", GMC_DATATYPE_TIME, &V11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V12", GMC_DATATYPE_CHAR, &V12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V13", GMC_DATATYPE_UCHAR, &V13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V14", GMC_DATATYPE_FIXED, V14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V15", GMC_DATATYPE_STRING, V15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V16", GMC_DATATYPE_BYTES, V16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_read_special_complex_table(
    GmcStmtT *stmt, int index, bool bool_value, char *label_name, uint16_t T2_count, uint16_t T3_count)
{
    int ret, i;

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeGetPropertyByName_R(root, index, bool_value);
    TestGmcNodeGetPropertyByName_P(t1, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        ret = GmcNodeGetElementByIndex(t2, i, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A(t2, i, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        ret = GmcNodeGetElementByIndex(t3, i, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(t3, i, bool_value);
    }
}

void db_test_read_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_special_complex_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

// ########################################### big obj table ###########################################

void db_test_struct_write_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T2_struct_t *obj = (TEST_SC_T2_struct_t *)malloc(sizeof(TEST_SC_T2_struct_t));
    if (obj == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(obj, 0, sizeof(TEST_SC_T2_struct_t));
    record_T1_SC *t1 = (record_T1_SC *)malloc(sizeof(record_T1_SC));
    if (t1 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t1, 0, sizeof(record_T1_SC));
    fixed_array_T2_big_obj *t2 = (fixed_array_T2_big_obj *)malloc(sizeof(fixed_array_T2_big_obj) * T2_count);
    if (t2 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t2, 0, sizeof(fixed_array_T2_big_obj) * T2_count);
    vector_T3_SC *t3 = (vector_T3_SC *)malloc(sizeof(vector_T3_SC) * T3_count);
    if (t3 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t3, 0, sizeof(vector_T3_SC) * T3_count);
    test_malloc_TEST_SC_T2(obj, t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T2_value(obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T2(obj, T2_count, T3_count);

    free(t1);
    free(t2);
    free(t3);
    free(obj);
}

void TestGmcNodeGetPropertyByName_A_big_obj(GmcNodeT *node, int index, bool bool_value)
{
    int ret, i;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    (void)snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[11] = {0};
    (void)snprintf((char *)A15Value, sizeof(A15Value), "b%08d", index);
    char A16Value[21] = {0};
    (void)snprintf((char *)A16Value, sizeof(A16Value), "ABCDEFGHIJKL%08d", index);

    // array
    ret = queryNodePropertyAndCompare(node, "A0", GMC_DATATYPE_INT64, &A0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A1", GMC_DATATYPE_UINT64, &A1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A2", GMC_DATATYPE_INT32, &A2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A3", GMC_DATATYPE_UINT32, &A3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A4", GMC_DATATYPE_INT16, &A4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A5", GMC_DATATYPE_UINT16, &A5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A6", GMC_DATATYPE_INT8, &A6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A7", GMC_DATATYPE_UINT8, &A7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A8", GMC_DATATYPE_BOOL, &A8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A9", GMC_DATATYPE_FLOAT, &A9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A10", GMC_DATATYPE_DOUBLE, &A10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A11", GMC_DATATYPE_TIME, &A11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A12", GMC_DATATYPE_CHAR, &A12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A13", GMC_DATATYPE_UCHAR, &A13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A14", GMC_DATATYPE_FIXED, A14Value);
    EXPECT_EQ(GMERR_OK, ret);

    char field_value[STRING_MAX_SIZE] = {0};
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };
    ret = queryNodePropertyAndCompare(node, "A15", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A16", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A17", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A18", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A19", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A20", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A21", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A22", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A23", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A24", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A25", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A26", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A27", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A28", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A29", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A30", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A31", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A32", GMC_DATATYPE_STRING, A15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A33", GMC_DATATYPE_BYTES, A16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_read_big_obj_table(
    GmcStmtT *stmt, int index, bool bool_value, char *label_name, uint16_t T2_count, uint16_t T3_count)
{
    int ret, i;

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeGetPropertyByName_R(root, index, bool_value);
    TestGmcNodeGetPropertyByName_P(t1, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        ret = GmcNodeGetElementByIndex(t2, i, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A_big_obj(t2, i, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        ret = GmcNodeGetElementByIndex(t3, i, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(t3, i, bool_value);
    }
}

void db_test_read_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_big_obj_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void CompareVertexPropertyValue_A_big_obj(TEST_SC_T2_struct_t *d, int index, bool bool_value)
{
    int ret, i;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    (void)snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[11] = {0};
    (void)snprintf((char *)A15Value, sizeof(A15Value), "b%08d", index);
    char A16Value[21] = {0};
    (void)snprintf((char *)A16Value, sizeof(A16Value), "ABCDEFGHIJKL%08d", index);

    // array
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &A0Value, &d->t2[index].A0, sizeof(A0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &A1Value, &d->t2[index].A1, sizeof(A1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &A2Value, &d->t2[index].A2, sizeof(A2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &A3Value, &d->t2[index].A3, sizeof(A3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &A4Value, &d->t2[index].A4, sizeof(A4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &A5Value, &d->t2[index].A5, sizeof(A5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &A6Value, &d->t2[index].A6, sizeof(A6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &A7Value, &d->t2[index].A7, sizeof(A7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &A8Value, &d->t2[index].A8, sizeof(A8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &A9Value, &d->t2[index].A9, sizeof(A9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &A10Value, &d->t2[index].A10, sizeof(A10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &A11Value, &d->t2[index].A11, sizeof(A11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &A12Value, &d->t2[index].A12, sizeof(A12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &A13Value, &d->t2[index].A13, sizeof(A13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, A14Value, d->t2[index].A14, sizeof(A14Value));
    EXPECT_EQ(GMERR_OK, ret);

    char field_value[STRING_MAX_SIZE] = {0};
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A15, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A16, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A17, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A18, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A19, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A20, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A21, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A22, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A23, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A24, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A25, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A26, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A27, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A28, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A29, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A30, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->t2[index].A31, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &A15Value, d->t2[index].A32, sizeof(A15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &A16Value, d->t2[index].A33, sizeof(A16Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_struct_read_TEST_SC_T2(
    TEST_SC_T2_struct_t *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    // array
    for (i = 0; i < T2_count; ++i) {
        CompareVertexPropertyValue_A_big_obj(d, i, bool_value);
    }
}

void db_test_struct_read_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T2_struct_t obj = (TEST_SC_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T2_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T2(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_delete_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T2_struct_t obj = (TEST_SC_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 500, 0);
        test_set_TEST_SC_T2_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_struct_merge_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T2_struct_t *obj = (TEST_SC_T2_struct_t *)malloc(sizeof(TEST_SC_T2_struct_t));
    if (obj == NULL) {    // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(obj, 0, sizeof(TEST_SC_T2_struct_t));
    record_T1_SC *t1 = (record_T1_SC *)malloc(sizeof(record_T1_SC));
    if (t1 == NULL) {    // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t1, 0, sizeof(record_T1_SC));
    fixed_array_T2_big_obj *t2 = (fixed_array_T2_big_obj *)malloc(sizeof(fixed_array_T2_big_obj) * T2_count);
    if (t2 == NULL) {    // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t2, 0, sizeof(fixed_array_T2_big_obj) * T2_count);
    vector_T3_SC *t3 = (vector_T3_SC *)malloc(sizeof(vector_T3_SC) * T3_count);
    if (t3 == NULL) {    // 符合: 对返回值进行合法性检查
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t3, 0, sizeof(vector_T3_SC) * T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, 0);
        test_malloc_TEST_SC_T2(obj, t1, t2, t3, T2_count, T3_count);
        test_set_TEST_SC_T2_value(obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
        test_free_TEST_SC_T2(obj, T2_count, T3_count);
    }
    //    test_free_TEST_SC_T2(obj, T2_count, T3_count);

    free(t1);
    free(t2);
    free(t3);
    free(obj);
}

class StructObjBase {
public:
    void *structObj;
    StructObjBase()
    {
        structObj = NULL;
    };
    ~StructObjBase()
    {
        objFree();
    }
    void objSetValue(int priKeySeed)
    {}
    int objAlloc()
    {
        return 0;
    }
    void objFree()
    {}
};
#define FREE_SET_NULL(ptr) \
    do {                   \
        if (ptr) {         \
            free(ptr);     \
            (ptr) = NULL;  \
        }                  \
    } while (0)

class StructObj_TEST_SC_T2 : public StructObjBase {
private:
    uint16_t _T2_count;
    uint16_t _T3_count;
    bool _bool_value;
    record_T1_SC *_t1;
    fixed_array_T2_big_obj *_t2;
    vector_T3_SC *_t3;

public:
    StructObj_TEST_SC_T2(uint16_t T2_count, uint16_t T3_count)
    {
        _T2_count = T2_count;
        _T3_count = T3_count;
        _bool_value = false;
    }
    int objAlloc()
    {
        structObj = (TEST_SC_T2_struct_t *)malloc(sizeof(TEST_SC_T2_struct_t));
        memset(structObj, 0, sizeof(TEST_SC_T2_struct_t));

        _t1 = (record_T1_SC *)malloc(sizeof(record_T1_SC));
        memset(_t1, 0, sizeof(record_T1_SC));

        _t2 = (fixed_array_T2_big_obj *)malloc(sizeof(fixed_array_T2_big_obj) * _T2_count);
        memset(_t2, 0, sizeof(fixed_array_T2_big_obj) * _T2_count);

        _t3 = (vector_T3_SC *)malloc(sizeof(vector_T3_SC) * _T3_count);
        memset(_t3, 0, sizeof(vector_T3_SC) * _T3_count);

        test_malloc_TEST_SC_T2((TEST_SC_T2_struct_t *)structObj, _t1, _t2, _t3, _T2_count, _T3_count);
        return 0;  // failed return -1
    }
    void objSetValue(int priKeySeed)
    {
        test_set_TEST_SC_T2_value((TEST_SC_T2_struct_t *)structObj, priKeySeed, _bool_value, _T2_count, _T3_count);
    }
    void objFree()
    {
        test_free_TEST_SC_T2((TEST_SC_T2_struct_t *)structObj, _T2_count, _T3_count);
        FREE_SET_NULL(structObj);
        FREE_SET_NULL(_t1);
        FREE_SET_NULL(_t2);
        FREE_SET_NULL(_t3);
    }
};

template <typename StructObjT>
void db_test_struct_replace(GmcStmtT *stmt, int data_num, char *label_name, StructObjT *obj, int expected_affectRows)
{
    int ret, i, _affectRows;
    unsigned int _len;
    obj->objAlloc();
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT REPLACE", i, 1000, 0);
        obj->objSetValue(i);
        ret = testStructSetVertexWithBuf(stmt, obj->structObj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &_affectRows, sizeof(_affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, _affectRows);
    }
    obj->objFree();
}

void db_test_struct_replace_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T2_struct_t *obj = (TEST_SC_T2_struct_t *)malloc(sizeof(TEST_SC_T2_struct_t));
    if (obj == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(obj, 0, sizeof(TEST_SC_T2_struct_t));

    record_T1_SC *t1 = (record_T1_SC *)malloc(sizeof(record_T1_SC));
    if (t1 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t1, 0, sizeof(record_T1_SC));

    fixed_array_T2_big_obj *t2 = (fixed_array_T2_big_obj *)malloc(sizeof(fixed_array_T2_big_obj) * T2_count);
    if (t2 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t2, 0, sizeof(fixed_array_T2_big_obj) * T2_count);

    vector_T3_SC *t3 = (vector_T3_SC *)malloc(sizeof(vector_T3_SC) * T3_count);
    if (t3 == NULL) {
        AW_FUN_Log(LOG_INFO, "malloc failed");
    }
    memset(t3, 0, sizeof(vector_T3_SC) * T3_count);

    test_malloc_TEST_SC_T2(obj, t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT REPLACE", i, 1000, 0);
        test_set_TEST_SC_T2_value(obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T2(obj, T2_count, T3_count);

    free(t1);
    free(t2);
    free(t3);
    free(obj);
}

// ########################################### async ###########################################

void db_test_struct_write_special_complex_table_async(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table_async(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT MERGE ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_replace_special_complex_table_async(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_SC t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT REPLACE ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_delete_special_complex_table_async(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }
}

// ########################################### bitfield table ###########################################

void db_test_struct_write_bitfield_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T3_struct_t obj = (TEST_SC_T3_struct_t){0};
    record_T1_SC t1 = (record_T1_SC){0};
    fixed_array_T2_SC t2[T2_count];
    vector_T3_bitfield t3[T3_count];
    test_malloc_TEST_SC_T3(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T3_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T3(&obj, T2_count, T3_count);
}

void TestGmcNodeGetPropertyByName_R_bitfield(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    uint16_t F16Value = index & 0xF;
    uint16_t F17Value = index & 0xF;
    uint32_t F18Value = index & 0xFF;
    uint64_t F19Value = index & 0xFFFF;

    GmcBitMapT bitMap = {0, 1023, NULL};
    uint8_t F20Value[BITMAP_SIZE];
    (void)snprintf((char *)F20Value, sizeof(F20Value),
        "%08dABCDEFGHBCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABC"
        "DEFGH%08d",
        index, index);
    bitMap.bits = F20Value;

    uint8_t F21Value = index & 0xF;
    char F22Value[11] = {0};
    (void)snprintf((char *)F22Value, sizeof(F22Value), "b%08d", index);
    char F23Value[21] = {0};
    (void)snprintf((char *)F23Value, sizeof(F23Value), "ABCDEFGHIJKL%08d", index);

    // root
    ret = queryNodePropertyAndCompare(node, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_BITFIELD8, &F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F16", GMC_DATATYPE_BITFIELD16, &F16Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F17", GMC_DATATYPE_BITFIELD16, &F17Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F18", GMC_DATATYPE_BITFIELD32, &F18Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F19", GMC_DATATYPE_BITFIELD64, &F19Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "F20", GMC_DATATYPE_BITMAP, F20Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "F21", GMC_DATATYPE_PARTITION, &F21Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F22", GMC_DATATYPE_STRING, F22Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F23", GMC_DATATYPE_BYTES, F23Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void TestGmcNodeGetPropertyByName_V_bitfield(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    (void)snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    uint8_t V15Value = index & 0xF;
    uint16_t V16Value = index & 0xF;
    uint16_t V17Value = index & 0xF;
    uint32_t V18Value = index & 0xFF;
    uint64_t V19Value = index & 0xFFFF;

    GmcBitMapT bitMap = {0, 1023, NULL};
    uint8_t F20Value[BITMAP_SIZE];
    (void)snprintf((char *)F20Value, sizeof(F20Value),
        "%08dABCDEFGHBCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABC"
        "DEFGH%08d",
        index, index);
    bitMap.bits = F20Value;

    char V21Value[11] = {0};
    (void)snprintf((char *)V21Value, sizeof(V21Value), "b%08d", index);
    char V22Value[21] = {0};
    (void)snprintf((char *)V22Value, sizeof(V22Value), "ABCDEFGHIJKL%08d", index);

    // vector
    ret = queryNodePropertyAndCompare(node, "V0", GMC_DATATYPE_INT64, &V0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V1", GMC_DATATYPE_UINT64, &V1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V2", GMC_DATATYPE_INT32, &V2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V3", GMC_DATATYPE_UINT32, &V3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V4", GMC_DATATYPE_INT16, &V4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V5", GMC_DATATYPE_UINT16, &V5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V6", GMC_DATATYPE_INT8, &V6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V7", GMC_DATATYPE_UINT8, &V7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V8", GMC_DATATYPE_BOOL, &V8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V9", GMC_DATATYPE_FLOAT, &V9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V10", GMC_DATATYPE_DOUBLE, &V10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V11", GMC_DATATYPE_TIME, &V11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V12", GMC_DATATYPE_CHAR, &V12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V13", GMC_DATATYPE_UCHAR, &V13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V14", GMC_DATATYPE_FIXED, V14Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "V15", GMC_DATATYPE_BITFIELD8, &V15Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V16", GMC_DATATYPE_BITFIELD16, &V16Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V17", GMC_DATATYPE_BITFIELD16, &V17Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V18", GMC_DATATYPE_BITFIELD32, &V18Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V19", GMC_DATATYPE_BITFIELD64, &V19Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "V20", GMC_DATATYPE_BITMAP, F20Value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryNodePropertyAndCompare(node, "V21", GMC_DATATYPE_STRING, V21Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V22", GMC_DATATYPE_BYTES, V22Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_read_bitfield_table(
    GmcStmtT *stmt, int index, bool bool_value, char *label_name, uint16_t T2_count, uint16_t T3_count)
{
    int ret, i;

    GmcNodeT *root, *t1, *t2, *t3;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeGetPropertyByName_R_bitfield(root, index, bool_value);
    TestGmcNodeGetPropertyByName_P(t1, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        ret = GmcNodeGetElementByIndex(t2, i, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A(t2, i, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        ret = GmcNodeGetElementByIndex(t3, i, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V_bitfield(t3, i, bool_value);
    }
}

void db_test_read_bitfield_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_bitfield_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void CompareVertexPropertyValue_R_bitfield(TEST_SC_T3_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    uint16_t F16Value = index & 0xF;
    uint16_t F17Value = index & 0xF;
    uint32_t F18Value = index & 0xFF;
    uint64_t F19Value = index & 0xFFFF;

    GmcBitMapT bitMap = {0, 1023, NULL};
    uint8_t F20Value[BITMAP_SIZE];
    (void)snprintf((char *)F20Value, sizeof(F20Value),
        "%08dABCDEFGHBCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABCDEFGHABC"
        "DEFGH%08d",
        index, index);
    bitMap.bits = F20Value;

    uint8_t F21Value = index & 0xF;
    char F22Value[11] = {0};
    (void)snprintf((char *)F22Value, sizeof(F22Value), "b%08d", index);
    char F23Value[21] = {0};
    (void)snprintf((char *)F23Value, sizeof(F23Value), "ABCDEFGHIJKL%08d", index);

    // root
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(F15Value, d->F15);
    EXPECT_EQ(F16Value, d->F16);
    EXPECT_EQ(F17Value, d->F17);
    EXPECT_EQ(F18Value, d->F18);
    EXPECT_EQ(F19Value, d->F19);

    ret = CompareVertexPropertyValue(GMC_DATATYPE_BITMAP, F20Value, d->F20, sizeof(F20Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F21Value, &d->F21, sizeof(F21Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F22Value, d->F22, sizeof(F22Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F23Value, d->F23, sizeof(F23Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_struct_read_TEST_SC_T3(
    TEST_SC_T3_struct_t *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    CompareVertexPropertyValue_R_bitfield(d, index, bool_value);
}

void db_test_struct_read_bitfield_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T3_struct_t obj = (TEST_SC_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T3_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T3(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_delete_bitfield_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T3_struct_t obj = (TEST_SC_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 1000, 0);
        test_set_TEST_SC_T3_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

// ########################################### multi-layer table ###########################################

void db_test_struct_write_multi_layer_table(GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count,
    uint16_t T3_count, uint16_t T4_count, uint16_t T5_count, uint16_t T6_count, int expected_affectRows)
{
    int ret, i, affectRows;
    GtTestScT9VertexT obj = (GtTestScT9VertexT){0};
    GtTestScT9T1VertexT t1 = (GtTestScT9T1VertexT){0};
    GtTestScT9T2VertexT t2[T2_count];
    GtTestScT9T3VertexT t3[T3_count];
    GtTestScT9T4VertexT t4[T3_count * T4_count];
    GtTestScT9T5VertexT t5[T3_count * T4_count * T5_count];
    GtTestScT9T6VertexT t6[T3_count * T4_count * T5_count * T6_count];
    test_malloc_TEST_SC_T9(&obj, &t1, t2, t3, t4, t5, t6, T2_count, T3_count, T4_count, T5_count, T6_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T9_value(&obj, i, false, T2_count, T3_count, T4_count, T5_count, T6_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T9(&obj, T2_count, T3_count);
}

void CompareVertexPropertyValue_R_multi_layer(GtTestScT9VertexT *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    (void)snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[11] = {0};
    (void)snprintf((char *)F16Value, sizeof(F16Value), "b%08d", index);
    char F17Value[21] = {0};
    (void)snprintf((char *)F17Value, sizeof(F17Value), "ABCDEFGHIJKL%08d", index);

    // root
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F16Value, d->F16, sizeof(F16Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F17Value, d->F17, sizeof(F17Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void CompareVertexPropertyValue_V_multi_layer_T6(GtTestScT9T5VertexT *d, int index, bool bool_value)
{
    int ret;
    int64_t V60Value = (int64_t)index;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V60Value, &d->T6[index].V60, sizeof(V60Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void CompareVertexPropertyValue_V_multi_layer_T5(GtTestScT9T4VertexT *d, int index, bool bool_value, uint16_t T6_count)
{
    int ret;
    int64_t V50Value = (int64_t)index;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V50Value, &d->T5[index].V50, sizeof(V50Value));
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < T6_count; ++i) {
        CompareVertexPropertyValue_V_multi_layer_T6(&d->T5[i], i, bool_value);
    }
}

void CompareVertexPropertyValue_V_multi_layer_T4(
    GtTestScT9T3VertexT *d, int index, bool bool_value, uint16_t T5_count, uint16_t T6_count)
{
    int ret;
    int64_t V40Value = (int64_t)index;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V40Value, &d->T4[index].V40, sizeof(V40Value));
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < T5_count; ++i) {
        CompareVertexPropertyValue_V_multi_layer_T5(&d->T4[i], i, bool_value, T6_count);
    }
}

void CompareVertexPropertyValue_V_multi_layer(
    GtTestScT9VertexT *d, int index, bool bool_value, uint16_t T4_count, uint16_t T5_count, uint16_t T6_count)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    (void)snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    char V15Value[11] = {0};
    (void)snprintf((char *)V15Value, sizeof(V15Value), "b%08d", index);
    char V16Value[21] = {0};
    (void)snprintf((char *)V16Value, sizeof(V16Value), "ABCDEFGHIJKL%08d", index);

    // vector
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V0Value, &d->t3[index].V0, sizeof(V0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &V1Value, &d->t3[index].V1, sizeof(V1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &V2Value, &d->t3[index].V2, sizeof(V2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &V3Value, &d->t3[index].V3, sizeof(V3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &V4Value, &d->t3[index].V4, sizeof(V4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &V5Value, &d->t3[index].V5, sizeof(V5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &V6Value, &d->t3[index].V6, sizeof(V6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &V7Value, &d->t3[index].V7, sizeof(V7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &V8Value, &d->t3[index].V8, sizeof(V8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &V9Value, &d->t3[index].V9, sizeof(V9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &V10Value, &d->t3[index].V10, sizeof(V10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &V11Value, &d->t3[index].V11, sizeof(V11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &V12Value, &d->t3[index].V12, sizeof(V12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &V13Value, &d->t3[index].V13, sizeof(V13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, V14Value, d->t3[index].V14, sizeof(V14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &V15Value, d->t3[index].V15, sizeof(V15Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &V16Value, d->t3[index].V16, sizeof(V16Value));
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < T4_count; ++i) {
        CompareVertexPropertyValue_V_multi_layer_T4(&d->t3[i], i, bool_value, T5_count, T6_count);
    }
}

void test_struct_read_TEST_SC_T9(GtTestScT9VertexT *d, int index, bool bool_value, uint16_t T2_count, uint16_t T3_count,
    uint16_t T4_count, uint16_t T5_count, uint16_t T6_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R_multi_layer(d, index, bool_value);

    // vector
    for (i = 0; i < T3_count; ++i) {
        CompareVertexPropertyValue_V_multi_layer(d, i, bool_value, T4_count, T5_count, T6_count);
    }
}

void db_test_struct_read_multi_layer_table(GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count,
    uint16_t T3_count, uint16_t T4_count, uint16_t T5_count, uint16_t T6_count, bool read_num)
{
    int ret, i;
    GtTestScT9VertexT obj = (GtTestScT9VertexT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T9_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T9(&obj, i, false, T2_count, T3_count, T4_count, T5_count, T6_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void test_read_multi_layer_table(GmcStmtT *stmt, int index, bool bool_value, char *label_name, uint16_t T2_count,
    uint16_t T3_count, uint16_t T4_count, uint16_t T5_count, uint16_t T6_count)
{
    int ret, i, j, k, m;

    GmcNodeT *root, *t1, *t2, *t3, *t4, *t5, *t6;
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &t1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &t2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &t3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcNodeGetPropertyByName_R(root, index, bool_value);
    TestGmcNodeGetPropertyByName_P(t1, index, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        ret = GmcNodeGetElementByIndex(t2, i, &t2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_A(t2, i, bool_value);
    }

    // vector t3
    for (i = 0; i < T3_count; ++i) {
        ret = GmcNodeGetElementByIndex(t3, i, &t3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcNodeGetPropertyByName_V(t3, i, bool_value);

        /************************* T4 *************************/
        ret = GmcNodeGetChild(t3, "T4", &t4);
        EXPECT_EQ(GMERR_OK, ret);
        for (j = 0; j < T4_count; ++j) {
            ret = GmcNodeGetElementByIndex(t4, j, &t4);
            ASSERT_EQ(GMERR_OK, ret);

            int64_t V40Value = (int64_t)j;
            ret = queryNodePropertyAndCompare(t4, "V40", GMC_DATATYPE_INT64, &V40Value);
            EXPECT_EQ(GMERR_OK, ret);

            /************************* T5 *************************/
            ret = GmcNodeGetChild(t4, "T5", &t5);
            EXPECT_EQ(GMERR_OK, ret);
            for (k = 0; k < T5_count; ++k) {
                ret = GmcNodeGetElementByIndex(t5, k, &t5);
                ASSERT_EQ(GMERR_OK, ret);

                int64_t V50Value = (int64_t)k;
                ret = queryNodePropertyAndCompare(t5, "V50", GMC_DATATYPE_INT64, &V50Value);
                EXPECT_EQ(GMERR_OK, ret);

                /************************* T6 *************************/
                ret = GmcNodeGetChild(t5, "T6", &t6);
                EXPECT_EQ(GMERR_OK, ret);
                for (m = 0; m < T6_count; ++m) {
                    ret = GmcNodeGetElementByIndex(t6, m, &t6);
                    ASSERT_EQ(GMERR_OK, ret);

                    int64_t V60Value = (int64_t)m;
                    ret = queryNodePropertyAndCompare(t6, "V60", GMC_DATATYPE_INT64, &V60Value);
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
        }
    }
}

void db_test_read_multi_layer_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count,
    uint16_t T3_count, uint16_t T4_count, uint16_t T5_count, uint16_t T6_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_multi_layer_table(stmt, i, false, label_name, T2_count, T3_count, T4_count, T5_count, T6_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_struct_delete_multi_layer_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    GtTestScT9VertexT obj = (GtTestScT9VertexT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 1000, 0);
        test_set_TEST_SC_T9_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_read_special_complex_table_concurrency(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        // TEST_INFO("SCAN", i, 10000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_special_complex_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
    }
}

void db_test_scan_special_complex_table(
    GmcStmtT *stmt, int data_num, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("SCAN", i, 1000, 0);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        test_read_special_complex_table(stmt, cnt, false, label_name, T2_count, T3_count);
        cnt++;
    }
    if (read_num == false) {
        EXPECT_EQ(0, cnt);
    } else if (read_num == true) {
        EXPECT_EQ(data_num, cnt);
    }
}

void db_test_update_special_complex_table_localhash(GmcStmtT *stmt, int data_num, char *label_name, char *keyName,
    uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i;

    for (i = 0; i < data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("LOCALHASH UPDATE", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root;
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f2_value = i;
        ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_read_special_complex_table_localhash(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_special_complex_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_read_special_complex_table_hashcluster(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int32_t f2_value = (int32_t)i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_special_complex_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_delete_special_complex_table_localkey(GmcStmtT *stmt, int data_num, char *label_name, char *keyName,
    uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("LOCALKEY DELETE", i, 500, 0);

    unsigned int l_val_del = 0;
    unsigned int r_val_del = data_num;
    unsigned int arrLen = 1;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    // 设置删除扫描
    ret = GmcSetKeyRange(stmt, items, arrLen);

    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected_affectRows, affectRows);
}

#endif
