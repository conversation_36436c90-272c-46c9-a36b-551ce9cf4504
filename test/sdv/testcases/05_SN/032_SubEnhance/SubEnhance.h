/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :基于推送的订阅能力增强与加固测试公共头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/02/13
**************************************************************************** */
#ifndef SUB_ENHANCE_H
#define SUB_ENHANCE_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcConnT *g_connSub = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmtSub = NULL;
const char *g_subName = "vertexLabelSub";
char *g_labelName = (char *)"simpleLabel";
char *g_labelConfig = (char *)R"({"supportUndeterminedLength":true, "isFastReadUncommitted": false})";
pthread_barrier_t g_barrier;
uint32_t conNotifyCnt = 0;
uint32_t g_startSub = 0;
char g_viewName[32] = "V\\$DRT_CONN_SUBS_STAT";
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define SIMPLE_LABEL_FIXED_SIZE   9


int TestCreateVertexLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestCreateEdgeLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropEdgeLabel(stmt, labelName);
    ret = GmcCreateEdgeLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create edgeLabel %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *nsName = g_testNameSpace)
{
    // gmimport工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(cmd, 512, "%s/gmddl  -c alter -u online -f %s -ns %s", g_toolPath, schemaPath, nsName);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

void TestSimpleTSetPk(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleTPkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleTHashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleTSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleTUpdateSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

void TestSimpleTInsertOrReplace(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                                GmcOperationTypeE operationType, bool isDefaultValue = true, int32_t expectValue = 1)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTSetPk(stmt, i);
        TestSimpleTSetProperty(stmt, i, isDefaultValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expectValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleTMergeOrUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                              GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0,
                              int32_t expectValue = GMERR_OK, int32_t expectAffactRows = 1)
{
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleTPkIndexSet(stmt, i);
            TestSimpleTSetProperty(stmt, i, isDefaultValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(expectValue, ret);
            ret = TestGetAffactRows(stmt, expectAffactRows);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    } else {
            for (int i = startValue; i < endValue; i++) {
            TestSimpleTHashclusterIndexSet(stmt, i);
            TestSimpleTUpdateSetProperty(stmt, i + updateValue, isDefaultValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(expectValue, ret);
            ret = TestGetAffactRows(stmt, expectAffactRows);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestSimpleTUpdateGetPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestSimpleTGetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void *LabelInsertThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 100;
    int64_t endValue2 = 200;
    int flag = 0;
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = endValue; i < endValue2; i++) {
        TestSimpleTSetPk(g_stmt, i);
        TestSimpleTSetProperty(g_stmt, i, true);
        if (!flag) {
            flag = 1;
            pthread_barrier_wait(&g_barrier);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return NULL;
}

void *LabelInsertThread2(void *args)
{
    int ret = 0;
    int64_t startValue = 1000;
    int64_t endValue = 1200;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrier);
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTSetPk(stmt, i);
        TestSimpleTSetProperty(stmt, i, true);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_LOCK_NOT_AVAILABLE) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *DisconnectSub(void *args)
{
    pthread_barrier_wait(&g_barrier);
    // 释放订阅连接
    int ret = testSubDisConnect(g_connSub, g_stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *LabelMergeThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t updateValue = 2000;
    int64_t endValue = 200;
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTPkIndexSet(g_stmt, i);
        TestSimpleTUpdateSetProperty(g_stmt, i + updateValue, false);
        if (i == endValue / 2) {
            pthread_barrier_wait(&g_barrier);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return NULL;
}


void *LabelUpdateThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t updateValue = 2000;
    int64_t endValue = 200;
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTHashclusterIndexSet(g_stmt, i);
        TestSimpleTUpdateSetProperty(g_stmt, i + updateValue, false);
        if (i == endValue / 2) {
            pthread_barrier_wait(&g_barrier);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return NULL;
}

void *LabelReplaceThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 100;
    int64_t endValue2 = 200;
    int flag = 0;
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = endValue; i < endValue2; i++) {
        TestSimpleTSetPk(g_stmt, i);
        TestSimpleTSetProperty(g_stmt, i, true);
        if (!flag) {
            flag = 1;
            pthread_barrier_wait(&g_barrier);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return NULL;
}

void *LabelDeleteThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 200;

    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTPkIndexSet(g_stmt, i);
        if (i == endValue / 2) {
            pthread_barrier_wait(&g_barrier);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return NULL;
}

void *LabelInsertTransThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 200;

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTSetPk(g_stmt, i);
        TestSimpleTSetProperty(g_stmt, i, true);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "LabelInsertTransThread i = %d\n", i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    pthread_barrier_wait(&g_barrier);
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *LabelInsertTransBatchThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 1000;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = endValue - startValue;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleTSetPk(g_stmt, i);
        TestSimpleTSetProperty(g_stmt, i, true);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    pthread_barrier_wait(&g_barrier);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchDestroy(batch);
    return NULL;
}

int CheckAccountStatus(GmcStmtT *stmt, const char *labelName)
{
    GmcCheckInfoT *checkInfo = NULL;
    int ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(checkStatus, GMC_CHECK_STATUS_NORMAL);
    return ret;
}

void *LabelAgeThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 2000;
    bool isAbnormal = false;

    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_labelName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&g_barrier);
    ret=GmcEndCheck(g_stmt, g_labelName, 0xff, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckAccountStatus(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *LabelAgeTransThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 2000;
    int64_t updateValue = 2000;
    bool isAbnormal = false;
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);

    // 开启对账
    ret = GmcBeginCheck(g_stmt, g_labelName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < 200; i++) {
        TestSimpleTHashclusterIndexSet(g_stmt, i);
        TestSimpleTUpdateSetProperty(g_stmt, i + updateValue, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcTransCommit(g_conn);
    pthread_barrier_wait(&g_barrier);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret=GmcEndCheck(g_stmt, g_labelName, 0xff, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckAccountStatus(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void *LabelTruncateThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 2000;

    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);
    ret = GmcTruncateVertexLabel(g_stmt, g_labelName);
    pthread_barrier_wait(&g_barrier);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}


void *LabelDeleteAllFastThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 2000;

    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    usleep(10 * 1000);
    pthread_barrier_wait(&g_barrier);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

void SubVertexLabelCallBack(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 2000;
    int updateValue = 2000;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                AW_FUN_Log(LOG_INFO, "<---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "<---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_INITIAL_LOAD: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",  info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value + updateValue, false);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value + updateValue, false);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value + updateValue, false);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTUpdateGetPropertyByName(subStmt, f0Value, true);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void SubVertexLabelCallBackWait(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 5000;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    usleep(300 * 1000);
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                AW_FUN_Log(LOG_INFO, "<---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "<---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_INITIAL_LOAD: {
                            // 读new object
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",  info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            if (f0Value > count) {
                                AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleTGetLpmProperty(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void *LabelInitalLoadThread(void *args)
{
    SnUserDataT userData;
    char *subInfo = NULL;
    readJanssonFile("schemaFile/subEnhanceT2.gmjson", &subInfo);
    EXPECT_NE((void *)NULL, subInfo);
    memset(&userData, 0, sizeof(SnUserDataT));
    GmcSubConfigT tmpSubInfo;
    tmpSubInfo.subsName = g_subName;
    tmpSubInfo.configJson = subInfo;
    int ret = GmcSubscribe(g_stmt, &tmpSubInfo, g_connSub, SubVertexLabelCallBack, &userData);
    usleep(10 * 1000);
    pthread_barrier_wait(&g_barrier);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfo);
    return NULL;
}

static void SubFailedNotifyCallback(GmcStmtT *stmt, void *arg)
{
    unsigned int *callCnt = (unsigned int *)arg;
    (*callCnt)++;
}

void SubVertexLabelCallBackSlow(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 2000;
    int updateValue = 2000;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    if (g_startSub == 0) {
        usleep(10 * 1000);
    }
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
                AW_FUN_Log(LOG_INFO, "<---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                AW_FUN_Log(LOG_INFO, "<---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof == true) {
            break;
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

int32_t GetSubNum(const char *subConnName)
{
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024,
        "%s/gmsysview -q %s -f NODE_NAME=%s | grep -E 'SUB_SEND_SUC_CNT' |awk -F '[:,]' '{print ""$2}'",
        g_toolPath, g_viewName, subConnName);
    system(cmd);
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
    }
    char numSize[64] = {0};
    char *p = fgets(numSize, 64, pf);
    if (p == NULL) {
        printf("fgets(%s) error.\n", cmd);
        pclose(pf);
        return -1;
    }
    numSize[strlen(numSize) - 1] = '\0';
    pclose(pf);
    memset(cmd, 0, sizeof(cmd));
    return atoi(numSize);
}

long long TestGetLocalTime()
{
    struct timeval time1;
    gettimeofday(&time1, NULL);
    long long startTime = (long long)time1.tv_sec * 1000 + (long long)time1.tv_usec / 1000;
    return startTime;
}

void *GetServerCpuUsAge(void *args)
{
    char usAge[32] = {0};
    int cycleTime = 10;
    int32_t cpuUsArray[cycleTime];
    memset(cpuUsArray, 0x00, cycleTime * sizeof(int32_t));
    int32_t cnt = 0;
    int32_t avg = 0;
    char numSize[32] = {0};
#if defined RUN_INDEPENDENT
    char cmd[256] = "top -n 1 -H -p $(pidof gmserver) | grep 'DATA_PLANE_0' | awk '{print $9}'";
#else
    char cmd[256] = "hpecli task | grep 'DATA_PLANE_0' | awk '{print $8}'";
#endif
    long long startTime = 0;
    long long endTime = 0;
    memset(cpuUsArray, 0, sizeof(cpuUsArray));
    pthread_barrier_wait(&g_barrier);
    usleep(20);
    startTime = TestGetLocalTime();
    for (int i = 0; i < cycleTime; i++) {
        FILE *pf = popen(cmd, "r");
        if (pf == NULL) {
            AW_FUN_Log(LOG_DEBUG, "popen(%s) error.\n", cmd);
        }
        uint64_t length;
        char tmpBuff[128];
        char str[2][128];
        if (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
            length = strlen(tmpBuff);
            if (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
                tmpBuff[length - 1] = '\0';
                -length;
                }
                (void)sscanf(tmpBuff, "%s.%s", str[0], str[1]);
                if (str[0][0] == ' ' || str[1][0] == '\0') {
                    continue;
                }
                cpuUsArray[i] = atoi(str[0]);
            }
        pclose(pf);
        usleep(200 * 1000);
    }
    endTime = TestGetLocalTime();
    AW_FUN_Log(LOG_INFO, "time-consuming:%lld(ms)\n", endTime - startTime);

    for (int i = 0; i < cycleTime; i++) {
        if (cpuUsArray[i] > 50) {
            cnt++;
        }
        avg += cpuUsArray[i];
    }
    avg = avg / cycleTime;
    if (avg > 50 || cnt == cycleTime) {
        AW_FUN_Log(LOG_DEBUG, "server cpu_usage average is larger 50 avg=%d ||\r\n", avg);
        AW_FUN_Log(LOG_DEBUG, "server cpu_usage %d times larger 50\r\n", cnt);
        AW_MACRO_EXPECT_EQ_INT(1, 0);
    }
    return NULL;
}

void *LabelDmlThread(void *args)
{
    int ret = 0;
    int64_t startValue = 0;
    int64_t endValue = 200;
    int64_t updateValue = endValue * 3;
    long long startTime = 0;
    long long endTime = 0;
    pthread_barrier_wait(&g_barrier);
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, startValue, endValue, GMC_OPERATION_INSERT, true, 1);
    TestSimpleTInsertOrReplace(g_stmt, g_labelName, endValue, endValue * 2, GMC_OPERATION_REPLACE, false, 1);
    TestSimpleTMergeOrUpdate(g_stmt, g_labelName, endValue * 2, endValue * 3, GMC_OPERATION_MERGE, true,
                             0, GMERR_OK, 1);
    TestSimpleTMergeOrUpdate(g_stmt, g_labelName, startValue, endValue * 3, GMC_OPERATION_UPDATE, false,
                             updateValue, GMERR_OK, 1);
    ret = GmcDeleteAllFast(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}

#endif
