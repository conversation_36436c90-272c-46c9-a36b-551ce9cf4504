/*****************************************************************************
 Description  : 在一个订阅关系中下发所有事件
 Notes        : 注释如下
 History      :
 Author       : jwx992802
 Modification :
 Date         : 2021/09/17
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include <sys/time.h>
#include "sub_path.h"

using namespace std;

class pathSub : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        //创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void pathSub::SetUp()
{
    printf("pathSub Start.\n");
    g_conn_sync = NULL;
    g_stmt_sync = NULL;
    g_sub_info_insert = NULL;
    g_sub_info_delete = NULL;
    int ret;
    int chanRingLen = 256;

    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_data_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_data_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_data_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_data_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_data_num * 10);

    //创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    //创建#7
    readJanssonFile("schema_file/ip4forward.gmjson", &vertexLabelJsonT7);
    EXPECT_NE((void *)NULL, vertexLabelJsonT7);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT7, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#8
    readJanssonFile("schema_file/nhp_group.gmjson", &vertexLabelJsonT8);
    EXPECT_NE((void *)NULL, vertexLabelJsonT8);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT8, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#9
    readJanssonFile("schema_file/nhp_group_node.gmjson", &vertexLabelJsonT9);
    EXPECT_NE((void *)NULL, vertexLabelJsonT9);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT9, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#10
    readJanssonFile("schema_file/nhp.gmjson", &vertexLabelJsonT10);
    EXPECT_NE((void *)NULL, vertexLabelJsonT10);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT10, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建#42
    readJanssonFile("schema_file/nhp_std.gmjson", &vertexLabelJsonT42);
    EXPECT_NE((void *)NULL, vertexLabelJsonT42);
    ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJsonT42, vertexLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_7_8
    readJanssonFile("schema_file/edge_7_8.gmjson", &edgeLabelJson_7_8);
    EXPECT_NE((void *)NULL, edgeLabelJson_7_8);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_7_8, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_8_9
    readJanssonFile("schema_file/edge_8_9.gmjson", &edgeLabelJson_8_9);
    EXPECT_NE((void *)NULL, edgeLabelJson_8_9);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_8_9, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_9_10
    readJanssonFile("schema_file/edge_9_10.gmjson", &edgeLabelJson_9_10);
    EXPECT_NE((void *)NULL, edgeLabelJson_9_10);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_9_10, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);
    //创建edge_10_42
    readJanssonFile("schema_file/edge_10_42.gmjson", &edgeLabelJson_10_42);
    EXPECT_NE((void *)NULL, edgeLabelJson_10_42);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson_10_42, edgeLabelCfg);
    EXPECT_EQ(GMERR_OK, ret);

    g_specialDisConnect = 0;

    AW_CHECK_LOG_BEGIN();
}
void pathSub::TearDown()
{
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AW_CHECK_LOG_END();
    int ret;

    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_7_8);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_8_9);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_9_10);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    ret = GmcDropEdgeLabel(g_stmt_sync, edgeLabelName_10_42);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    if (!g_specialDisConnect) {
        test_close_and_drop_label(g_stmt_sync, vertexLabelNameT7);
        test_close_and_drop_label(g_stmt_sync, vertexLabelNameT8);
        test_close_and_drop_label(g_stmt_sync, vertexLabelNameT9);
        test_close_and_drop_label(g_stmt_sync, vertexLabelNameT10);
        test_close_and_drop_label(g_stmt_sync, vertexLabelNameT42);
    } else {
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT7);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT8);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT9);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT10);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelNameT42);
        EXPECT_EQ(GMERR_OK, ret);
    }

    //断开订阅连接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    //断开同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    free(vertexLabelJsonT7);
    free(vertexLabelJsonT8);
    free(vertexLabelJsonT9);
    free(vertexLabelJsonT10);
    free(vertexLabelJsonT42);
    free(edgeLabelJson_7_8);
    free(edgeLabelJson_8_9);
    free(edgeLabelJson_9_10);
    free(edgeLabelJson_10_42);
    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
    printf("pathSub End.\n");
}

//创建path订阅指定insert事件，对触发表(#7)进行insert动作触发订阅
TEST_F(pathSub, SN_021_024)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定insert事件，在一个事务内，插入path路径上的数据，最后insert触发表(#7)数据触发订阅
TEST_F(pathSub, SN_021_025)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定insert事件，在一个事务内，插入path路径上的数据，最先insert触发表(#7)数据触发订阅
TEST_F(pathSub, SN_021_026)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定delete事件，对触发表(#7)进行delete动作触发订阅
TEST_F(pathSub, SN_021_027)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#7的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_delete);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定delete事件，在一个事务内，删除path路径上的数据，最后delete触发表(#7)数据触发订阅
TEST_F(pathSub, SN_021_028)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#7的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_delete);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定delete事件，在一个事务内，删除path路径上的数据，最先delete触发表(#7)数据触发订阅
TEST_F(pathSub, SN_021_029)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#7的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_delete);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    ret = GmcTransStart(g_conn_sync, &config);
    EXPECT_EQ(GMERR_OK, ret);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = GmcTransCommit(g_conn_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, g_data_num);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

// Path订阅，不手动开启事务，最先insert 触发表(#7)，写路径表数据,无推送
TEST_F(pathSub, SN_021_030)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //全表扫描#7
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_7(g_stmt_sync, labelT7, 0, g_data_num);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#8
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_8(g_stmt_sync, labelT8, 0, g_data_num);

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#9
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_9(g_stmt_sync, labelT9, 0, g_data_num);

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#10
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_10(g_stmt_sync, labelT10, 0, g_data_num);

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    //全表扫描#42
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    test_scan_42(g_stmt_sync, labelT42, 0, g_data_num);

    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定update事件,报错
TEST_F(pathSub, SN_021_031)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_update.gmjson", &g_sub_info_update);
    EXPECT_NE((void *)NULL, g_sub_info_update);

    //创建订阅关系
    GmcSubConfigT tmp_g_sub_info_update;
    tmp_g_sub_info_update.subsName = g_subName_update;
    tmp_g_sub_info_update.configJson = g_sub_info_update;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_update, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(g_sub_info_update);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_update);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定replace事件,报错
TEST_F(pathSub, SN_021_032)
{
    int ret;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_replace.gmjson", &g_sub_info_replace);
    EXPECT_NE((void *)NULL, g_sub_info_replace);

    //创建订阅关系
    GmcSubConfigT tmp_g_sub_info_replace;
    tmp_g_sub_info_replace.subsName = g_subName_replace;
    tmp_g_sub_info_replace.configJson = g_sub_info_replace;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_replace, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    free(g_sub_info_replace);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_replace);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建path订阅指定age事件,报错（path 最新支持age和delete一样）
TEST_F(pathSub, SN_021_033)
{
    int ret;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_age.gmjson", &g_sub_info_age);
    EXPECT_NE((void *)NULL, g_sub_info_age);

    //创建订阅关系
    GmcSubConfigT tmp_g_sub_info_age;
    tmp_g_sub_info_age.subsName = g_subName_age;
    tmp_g_sub_info_age.configJson = g_sub_info_age;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_age, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_age);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_age);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建fib表（#7/#8/#9/#10/#42）建边edgelabel，插入数据各10万满足建边条件，订阅#7表insert，并计算吞吐速率。(实际3000条)
TEST_F(pathSub, SN_021_034)
{
    struct timeval start, end;
    double timeUsed;
    int ret, i;
    int userDataIdx = 0;

    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    //记录开始时间
    gettimeofday(&start, NULL);
    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        //准备好推送的上下文
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;

        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        if (i % 500 == 0) {
            ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 500);
            EXPECT_EQ(GMERR_OK, ret);
            printf("[info]--path num-->%d\n", i);
        }
    }

    gettimeofday(&end, NULL);
    timeUsed = (end.tv_sec - start.tv_sec) + (end.tv_usec - start.tv_usec) / 1000000.0;
    EXPECT_GE(8 * 60, timeUsed);  // val1 >= val2则成功，否则失败

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
}

//创建fib表（#7/#8/#9/#10/#42）建边edgelabel，插入数据各10万满足建边条件，订阅#7表delete。(实际3000条)
TEST_F(pathSub, SN_021_035)
{
    int ret, i;
    int userDataIdx = 0;
    readJanssonFile("schema_file/fib_path_7_subinfo_delete.gmjson", &g_sub_info_delete);
    EXPECT_NE((void *)NULL, g_sub_info_delete);

    //订阅#7的delete
    GmcSubConfigT tmp_g_sub_info_delete;
    tmp_g_sub_info_delete.subsName = g_subName_delete;
    tmp_g_sub_info_delete.configJson = g_sub_info_delete;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info_delete, g_conn_sub, sn_callback, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_delete);

    //写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_8(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_9(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#10的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_10(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#42的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_42(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //写#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        test_setVertexProperty_7(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //删除#7的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, vertexLabelNameT7, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= BIG_DATA; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
        uint8_t value_u8 = i;
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_u8, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync, lableT7_PK_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);

        if (i % 500 == 0) {
            ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, 500);
            EXPECT_EQ(GMERR_OK, ret);
            printf("[info]--path num-->%d\n", i);
        }
    }

    // ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_DELETE, BIG_DATA);
    // EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName_delete);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_sn_8(void *args)
{
    int ret, i;
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;

    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    //异步写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT8, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 7282; i <= 7282; i++) {
        test_setVertexProperty_8(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }

    pthread_mutex_lock(&g_subLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_subLock);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_sn_9(void *args)
{
    int ret, i;
    GmcConnT *conn = NULL, *g_conn_async = NULL;
    GmcStmtT *stmt = NULL, *g_stmt_async = NULL;

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 1) {
        sleep(1);
    }
    //同步写#9的数据
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT9, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= 10; i++) {
        test_setVertexProperty_9(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //异步更新#9的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT9, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= 10; i++) {
        uint32_t value_u32 = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 1, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 2, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 3, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 4, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 5, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 6, GMC_DATATYPE_UINT32, &value_u32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        test_setVertexProperty_9(g_stmt_async, i + 7272);

        ret = GmcSetIndexKeyName(g_stmt_async, lableT9_PK_name);
        EXPECT_EQ(GMERR_OK, ret);

        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }

    pthread_mutex_lock(&g_subLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_subLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_sn_10(void *args)
{
    int ret, i;
    GmcStmtT *stmt = 0;
    GmcConnT *conn = 0;
    int affectRows;
    unsigned int len;

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 2) {
        sleep(1);
    }
    //写#10的数据(96*10923=1048576)大于1M
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT10, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 7282; i <= 7282 + 10923; i++) {
        test_setVertexProperty_10(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_subLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_subLock);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_sn_42(void *args)
{
    int ret, i;
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;

    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_thread_wait < 3) {
        sleep(1);
    }
    //异步写#8的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexLabelNameT42, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 7282 - 1024; i <= 7282; i++) {
        test_setVertexProperty_42(g_stmt_async, i);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        ret = testWaitAsyncRecv(&asyncUserData);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserData.status);
        EXPECT_EQ(1, asyncUserData.affectRows);
    }

    pthread_mutex_lock(&g_subLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_subLock);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_sn_7(void *args)
{
    int ret, i;
    GmcStmtT *stmt = 0;
    GmcConnT *conn = 0;
    int affectRows;
    unsigned int len;
    SnUserDataT *user_data_0;
    int userDataIdx = 0;

    //创建同步连接
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    user_data_0 = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data_0, 0, sizeof(SnUserDataT));
    user_data_0->new_value = (int *)malloc(sizeof(int) * 10000);
    memset(user_data_0->new_value, 0, sizeof(int) * 10000);

    readJanssonFile("schema_file/fib_path_7_subinfo_insert.gmjson", &g_sub_info_insert);
    EXPECT_NE((void *)NULL, g_sub_info_insert);

    //订阅#7的insert
    GmcSubConfigT tmp_g_sub_info_insert;
    tmp_g_sub_info_insert.subsName = g_subName_insert;
    tmp_g_sub_info_insert.configJson = g_sub_info_insert;
    ret = GmcSubscribe(stmt, &tmp_g_sub_info_insert, g_conn_sub, sn_callback, user_data_0);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info_insert);

    while (g_thread_wait < 4) {
        sleep(1);
    }
    //写#7的数据(144*7282=1048576)大于1M
    ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelNameT7, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 1; i <= 7282; i++) {
        //准备好推送的上下文
        ((int *)(user_data_0->new_value))[userDataIdx] = 7282;
        userDataIdx++;

        test_setVertexProperty_7(stmt, i);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    ret = testWaitSnRecv(user_data_0, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data_0->new_value);
    free(user_data_0);

    system("gmsysview record ip4forward -e RTOS -s usocket:/run/verona/unix_emserver");
    ret = GmcUnSubscribe(stmt, g_subName_insert);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

//建表，创建订阅关系，线程一同步写#7表1M，线程二异步写#8表一条，线程三同步写，异步更新#9表（满足建边），线程四同步写入#10
//1M,线程五异步写入#42 1024条，触发订阅，并查询个表record
TEST_F(pathSub, SN_021_036)
{
    int ret;
    pthread_t thr_arr[1000];
    void *thr_ret[1000];
    int index[1000] = {0};
    g_thread_wait = 0;

    pthread_create(&thr_arr[0], NULL, thread_sn_8, NULL);
    pthread_create(&thr_arr[1], NULL, thread_sn_9, NULL);
    pthread_create(&thr_arr[2], NULL, thread_sn_10, NULL);
    pthread_create(&thr_arr[3], NULL, thread_sn_42, NULL);
    pthread_create(&thr_arr[4], NULL, thread_sn_7, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    pthread_join(thr_arr[4], NULL);
}
