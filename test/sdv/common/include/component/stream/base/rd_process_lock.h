/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 通过文件锁实现进程锁
 * Author: guopanpan
 * Create: 2024-07-12
 */
#ifndef RD_PROCESS_LOCK_H
#define RD_PROCESS_LOCK_H 1
#ifdef FEATURE_STREAM

#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/file.h>

#define RD_PROCESS_LOCK_FAILED (-1)

#ifdef RD_LOG_H
#define RD_LOCK_ERROR(format, ...) RD_ERROR(format, ##__VA_ARGS__)
#define RD_LOCK_WARN(format, ...) RD_WARN(format, ##__VA_ARGS__)
#define RD_LOCK_INFO(format, ...) RD_INFO(format, ##__VA_ARGS__)
#define RD_LOCK_DEBUG(format, ...) RD_DEBUG(format, ##__VA_ARGS__)
#else
#define RD_LOCK_ERROR(format, ...) printf("[ERROR] " format "\n", ##__VA_ARGS__)
#define RD_LOCK_WARN(format, ...) printf("[WARN] " format "\n", ##__VA_ARGS__)
#define RD_LOCK_INFO(format, ...) printf("[INFO] " format "\n", ##__VA_ARGS__)
#define RD_LOCK_DEBUG(format, ...) printf("[DEBUG] " format "\n", ##__VA_ARGS__)
#endif

/**
 * @brief 加进程锁
 *
 * @param lockName 锁名称
 * @param timeoutSec 超时时间，如果超过该时间未抢到进程锁，则加锁失败
 * @return int32_t 加锁成功返回fd，加锁失败返回-1
 */
int32_t RdProcessLock(const char *lockName, uint32_t timeoutSec)
{
    // 如果lock目录不存在则创建，创建目录无锁保护，可能因为其他进程先一步创建成功导致此处失败，属于正常现象，打印告警日志即可
    const char *lockDir = "./lock";
    struct stat statBuf;
    bool dirExist = (stat(lockDir, &statBuf) == 0 && S_ISDIR(statBuf.st_mode));
    if (!dirExist) {
        if (mkdir(lockDir, S_IRWXU) != 0) {
            RD_LOCK_WARN("Unable to create lock dir, %s.", strerror(errno));
        }
    }
    char lockFile[128] = {0};
    int32_t len = snprintf(lockFile, sizeof(lockFile), "%s/%s.lock", lockDir, lockName);
    if (len <= 0) {
        RD_LOCK_ERROR("Unable to generate file name of table (%s), %s.", lockName, strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }
    int32_t fd = open(lockFile, O_RDWR | O_CREAT, S_IRWXU);
    if (fd == -1) {
        RD_LOCK_ERROR("Unable to open lock file (%s), %s.", lockFile, strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }

    uint32_t sleepMs = 100000; // 每次等待100毫秒，再重试
    uint32_t maxTryCnt = timeoutSec * 10;
    for (uint32_t i = 0; i <= maxTryCnt; i++) {
        if (flock(fd, LOCK_EX | LOCK_NB) == 0) {
            return fd;
        }
        usleep(sleepMs);
    }
    return RD_PROCESS_LOCK_FAILED;
}

/**
 * @brief 解进程锁
 *
 * @param lock 锁fd
 * @return int32_t 解锁成功返回0，解锁失败返回-1
 */
int32_t RdProcessUnlock(int32_t lock)
{
    int32_t ret = flock(lock, LOCK_UN);
    if (close(lock) != 0) {
        RD_LOCK_ERROR("Unable to close lock file, lock fd is %d, %s.", lock, strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }
    return ret;
}

typedef enum {
    RD_TABLE_NEED_PREPARE = 0,
    RD_TABLE_PREPARED = 1,
    RD_TABLE_CLEANED = 2,
} RdTableStatusE;

/**
 * @brief 设置锁文件对应的表状态
 *
 * @param lock 锁句柄，由调用者保证已获取到正确的锁句柄
 * @param status 表状态
 * @return int32_t 执行成功返回0
 */
int32_t RdProcessLockSetTableStatus(int32_t lock, RdTableStatusE status)
{
    int32_t ret = ftruncate(lock, sizeof(status));
    if (ret == -1) {
        RD_LOCK_ERROR("Unable to truncate lock file, %s", strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }
    __off_t offset = lseek(lock, 0, SEEK_SET);
    if (offset == -1) {
        RD_LOCK_ERROR("Unable to seek lock file, %s", strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }
    ssize_t size = write(lock, &status, sizeof(status));
    if (size == -1) {
        RD_LOCK_ERROR("Unable to write lock file, %s", strerror(errno));
        return RD_PROCESS_LOCK_FAILED;
    }
    return 0;
}

/**
 * @brief 获取锁文件对应的表状态
 *
 * @param lock 锁句柄，由调用者保证已获取到正确的锁句柄
 * @return RdTableStatusE 返回锁文件记录的表状态，如果锁文件未设置表状态，返回RD_TABLE_NEED_PREPARE
 */
RdTableStatusE RdProcessLockGetTableStatus(int32_t lock)
{
    RdTableStatusE status;
    __off_t offset = lseek(lock, 0, SEEK_SET);
    if (offset == -1) {
        RD_LOCK_ERROR("Unable to seek lock file, %s", strerror(errno));
        return RD_TABLE_NEED_PREPARE;
    }
    ssize_t size = read(lock, &status, sizeof(status));
    if (size == -1) {
        RD_LOCK_ERROR("Unable to read status from lock file, %s.", strerror(errno));
        return RD_TABLE_NEED_PREPARE;
    }
    return status;
}

#endif /* FEATURE_STREAM */
#endif /* RD_PROCESS_LOCK_H */
