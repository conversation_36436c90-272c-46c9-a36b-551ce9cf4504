/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: t_rd_common.c
 * Author: lushiguang
 * Create: 2023-06-15
 */

#include "t_rd_common.h"

int g_runStat = RUN_STAT_FREE;

int g_connRequest = 0;
int g_connOnline = 0;
bool g_needRestartWhenFailed = true;
bool g_needCheckWhenSucc = true;
char g_hpeLibDir[LOG_PATH_MAX_LEN] = "/usr/local/lib";
char g_sysGMDBCfgPath[LOG_PATH_MAX_LEN] = "/usr/local/file";

/* for sysconfig.ini */
#if defined RUN_INDEPENDENT
char g_connServer[64] = "usocket:/run/verona/unix_emserver";
char g_connServerTsdb[64] = "usocket:/run/verona/unix_emserver_tsdb";
char g_connServerSlave[64] = "usocket:/run/verona/unix_emserver_2";
char g_sysGMDBCfg[256] = "/usr/local/file/gmserver.ini";
char g_sysTsGMDBCfg[256] = "/usr/local/file/gmserver_ts.ini";
char g_toolPath[256] = "${GMDB_PATH}/bin";
char g_testNameSpace[32] = "public";
#elif defined ENV_RTOSV2X
char g_connServer[64] = "channel:";
char g_connServerSlave[64] = "";
char g_connServerTsdb[64] = "";
char g_sysGMDBCfg[256] = "/file/gmserver.ini";
char g_sysTsGMDBCfg[256] = "";
char g_toolPath[256] = "/bin";
char g_testNameSpace[32] = "gmdbtest";
#else // defined ENV_RTOSV2
char g_connServer[64] = "channel:";
char g_connServerTsdb[64] = "";
char g_connServerSlave[64] = "";
char g_sysGMDBCfg[256] = "/usr/local/file/gmserver.ini";
char g_sysTsGMDBCfg[256] = "";
char g_toolPath[256] = "/usr/local/bin";
char g_testNameSpace[32] = "gmdbtest";
#endif
char g_runEnv[3][12] = {"RTOS", "DAP", "HARMONY"};
char g_userName[32] = "gmdbv5";
char g_passwd[64] = "passwd";
bool g_clientServerSameProcess = 0;

bool g_isReadConfig = false;
int g_flowCnt = 0;
bool g_tcpType = false;

pthread_mutex_t g_connConcurrent;
pthread_mutex_t g_connLock;
int32_t (*g_hpeVpollCreate)(int32_t size);
int32_t (*g_hpeVpollCtl)(int32_t vpId, int32_t op, int32_t fd, VpollEvent *event);
int32_t (*g_hpeVpollWait)(int32_t vpId, VpollEvent *events, int32_t maxEvents, int32_t timeout);
int32_t (*g_hpeVpollDestroy)(int32_t vpId);
TestParseSchemaCtxT g_parseSchemaCtx = {0};

EpollThreadDataT g_epAsync[MAX_EP_ASYNC_NUM] = {0};
bool g_isOneThreadEpoll = false;
EpollThreadDataT g_epollData = {0}, g_epollDataOneThread = {0}, g_timeoutEpollData = {0};
pthread_mutex_t g_timeoutEpollLock = PTHREAD_MUTEX_INITIALIZER;
GmcEpollRegWithUserDataT g_epoll_reg_info = epollRegWithUserdata;
GmcEpollRegWithUserDataT g_epollRegInfoOneThread = epollRegWithUserdata;

int epoll_ctrl(int32_t fd, GmcEpollCtlTypeE type, uint32_t events, void *userData, const GmcConnT *conn)
{
    int ret = 0;
    TEST_EPOLL_EVENT event;
    event.data.fd = fd;
    event.events = events;
    int *epollFd = (int *)(userData);
    switch (type) {
        case GMC_EPOLL_ADD:
            ret = TEST_EPOLL_CTL(*epollFd, EPOLL_CTL_ADD, fd, &event);
            if (ret != 0) {
                const char *errInfo = strerror(errno);
                printf("TEST_EPOLL_CTL:epollFd=%d, fd=%d type:[%d:GMC_EPOLL_ADD], Error:[%d:%s]\n", *epollFd, fd,
                    (int)type, errno, errInfo);
            }
            break;
        case GMC_EPOLL_MOD:
            ret = TEST_EPOLL_CTL(*epollFd, EPOLL_CTL_MOD, fd, &event);
            if (ret != 0) {
                const char *errInfo = strerror(errno);
                printf("TEST_EPOLL_CTL:epollFd=%d, fd=%d type:[%d:GMC_EPOLL_MOD], Error:[%d:%s]\n", *epollFd, fd,
                    (int)type, errno, errInfo);
            }
            break;
        case GMC_EPOLL_DEL:
            ret = TEST_EPOLL_CTL(*epollFd, EPOLL_CTL_DEL, fd, NULL);
            break;
        default:
            ret = FAILED;
            printf("TEST_EPOLL_CTL: type is unexpected, epollFd=%d, fd=%d type=%d \n", *epollFd, fd, (int)type);
    }
    return ret;
}

int epollRegWithNoPara(int32_t fd, GmcEpollCtlTypeE type)
{
    return epoll_ctrl(fd, type, EPOLLIN, &g_epollData.userEpollFd);
}

int epollRegWithUserdata(int32_t fd, GmcEpollCtlTypeE type, uint32_t event, void *userData)
{
    return epoll_ctrl(fd, type, event, userData);
}

int epollRegWithConn(int32_t fd, GmcEpollCtlTypeE type, uint32_t event, const GmcConnT *conn, void *userData)
{
    return epoll_ctrl(fd, type, event, userData, conn);
}

int epollRegTimeout(int32_t fd, GmcEpollCtlTypeE type)
{
    return epoll_ctrl(fd, type, EPOLLIN, &g_timeoutEpollData.userEpollFd);
}

int epollRegOneThreadHeartbeat(int32_t fd, GmcEpollCtlTypeE type)
{
    return epoll_ctrl(fd, type, EPOLLIN, &g_epollDataOneThread.userEpollFd);
}

void TestEpollDoEvent(int32_t fd, uint32_t events)
{
    GmcHandleRWEvent(fd, events);
}

int testGmcGetLastError(const char *expect, bool isFullMatch)
{
    const char *lastErrInfo = NULL;
    lastErrInfo = GmcGetLastError();
    // expect 为空时不校验last error信息，仅打印该信息
    if (expect == NULL) {
        if (lastErrInfo != NULL) {
            printf("Info: lastErrInfo = \"%s\"\n", lastErrInfo);
        }
        return GMERR_OK;
    }
    if (lastErrInfo == NULL) {
        printf("Error: lastErrInfo is NULL\n");
        return FAILED;
    }
    // isFullMatch取值含义：true -> 预期lastErrInfo与expect完全一致；false -> 预期lastErrInfo中包含expect
    if (isFullMatch) {
        if (strcmp(expect, lastErrInfo) != 0) {
            printf("Error: failed to check lastErrInfo for full match:\n  expect: \"%s\"\n  actual: \"%s\"\n", expect,
                lastErrInfo);
            return FAILED;
        }
    } else {
        if (strstr(lastErrInfo, expect) == NULL) {
            printf("Error: failed to check lastErrInfo for partial match:\n  expect: \"%s\"\n  actual: \"%s\"\n",
                expect, lastErrInfo);
            return FAILED;
        }
    }
    return GMERR_OK;
}

void getSysConfig()
{
    if (g_isReadConfig) {
        return;
    }
    g_isReadConfig = true;
    char *p = getenv("TEST_HOME");
    if (p == NULL) {
        return;
    }
    char fileName[256];
    snprintf(fileName, sizeof(fileName), "%s/cfg/sysconfig.ini", p);
    FILE *pf = fopen(fileName, "r");
    if (pf == NULL) {
        return;
    }
    int length;
    char tmpBuff[512];
    char str[2][512];
    char whiteListStr[1024] = {0};
    int32_t printLogLevel = -1;
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        if (tmpBuff[0] == '#') {
            continue;
        }
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        sscanf(tmpBuff, "%[^=]= %[^\n]", str[0], str[1]);
        if (str[1][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strstr(str[0], "sysGmdbCfg")) {
            snprintf(g_sysGMDBCfg, sizeof(g_sysGMDBCfg), "%s", str[1]);
        } else if (strstr(str[0], "toolPath")) {
            snprintf(g_toolPath, sizeof(g_toolPath), "%s", str[1]);
        } else if (strstr(str[0], "serverLocator")) {
            snprintf(g_connServer, sizeof(g_connServer), "%s", str[1]);
#if defined(FEATURE_TS) || defined(FEATURE_MULTI_TS)
        } else if (strstr(str[0], "tsServerLocator")) {
            snprintf(g_connServerTsdb, sizeof(g_connServerTsdb), "%s", str[1]);
        } else if (strstr(str[0], "sysTsGmdbCfg")) {
            snprintf(g_sysTsGMDBCfg, sizeof(g_sysTsGMDBCfg), "%s", str[1]);
#endif
#if defined(FEATURE_REPLICATION)
        } else if (strstr(str[0], "slaveServerLocator")) {
            snprintf(g_connServerSlave, sizeof(g_connServerSlave), "%s", str[1]);
#endif
        } else if (strstr(str[0], "userName")) {
            snprintf(g_userName, sizeof(g_userName), "%s", str[1]);
        } else if (strstr(str[0], "password")) {
            snprintf(g_passwd, sizeof(g_passwd), "%s", str[1]);
        } else if (strstr(str[0], "checkLogLevel")) {
            g_checkLogDefault = atoi(str[1]);
        } else if (strstr(str[0], "printLogLevel")) {
            printLogLevel = atoi(str[1]);
            if (printLogLevel < 0 || printLogLevel >= LOG_BUTT) {
                printf("sysconfig get value error, %s:%d\n", str[0], printLogLevel);
                continue;
            }
            g_logLevelWillPrint = printLogLevel;
        } else if (strstr(str[0], "truncationWhiteList")) {
            snprintf(whiteListStr, sizeof(whiteListStr), "%s", str[1]);
            if (strcmp(whiteListStr, "null") == 0 || strcmp(whiteListStr, "") == 0) {
                g_truncationWhiteList.globalListNum = 0;
            } else {
                char *token;
                // 获取第一个子字符串
                const char *split = "|";
                g_truncationWhiteList.globalListNum = 0;
                token = strtok(whiteListStr, split);
                if (token == NULL) {
                    snprintf(g_truncationWhiteList.globalList[0], sizeof(g_truncationWhiteList.globalList[0]), "%s", whiteListStr);
                    g_truncationWhiteList.globalListNum = 1;
                } else {
                    /* 继续获取其他的子字符串 */
                    while (token != NULL) {
                        int index = g_truncationWhiteList.globalListNum;
                        snprintf(g_truncationWhiteList.globalList[index], sizeof(g_truncationWhiteList.globalList[index]), "%s",
                            token);
                        g_truncationWhiteList.globalListNum = g_truncationWhiteList.globalListNum + 1;
                        token = strtok(NULL, split);
                    }
                }
            }
        } else if (strstr(str[0], "errorsWhiteList")) {
            snprintf(whiteListStr, sizeof(whiteListStr), "%s", str[1]);
            if (strcmp(whiteListStr, "null") == 0 || strcmp(whiteListStr, "") == 0) {
                g_errWhiteList.globalListNum = 0;
                g_errWhiteList.localListNum = 0;
            } else {
                char *token;
                // 获取第一个子字符串
                const char *split = "|";
                g_errWhiteList.globalListNum = 0;
                token = strtok(whiteListStr, split);
                if (token == NULL) {
                    snprintf(g_errWhiteList.globalList[0], sizeof(g_errWhiteList.globalList[0]), "%s", whiteListStr);
                    g_errWhiteList.globalListNum = 1;
                } else {
                    /* 继续获取其他的子字符串 */
                    while (token != NULL) {
                        int index = g_errWhiteList.globalListNum;
                        snprintf(g_errWhiteList.globalList[index], sizeof(g_errWhiteList.globalList[index]), "%s",
                            token);
                        g_errWhiteList.globalListNum = g_errWhiteList.globalListNum + 1;
                        token = strtok(NULL, split);
                    }
                }
            }
        } else if (strstr(str[0], "foldWhiteList")) {
            snprintf(whiteListStr, sizeof(whiteListStr), "%s", str[1]);
            if (strcmp(whiteListStr, "null") == 0 || strcmp(whiteListStr, "") == 0) {
                g_foldWhiteList.globalListNum = 0;
            } else {
                char *token;
                // 获取第一个子字符串
                const char *split = "|";
                g_foldWhiteList.globalListNum = 0;
                token = strtok(whiteListStr, split);
                if (token == NULL) {
                    snprintf(g_foldWhiteList.globalList[0], sizeof(g_foldWhiteList.globalList[0]), "%s", whiteListStr);
                    g_foldWhiteList.globalListNum = 1;
                } else {
                    /* 继续获取其他的子字符串 */
                    while (token != NULL) {
                        int index = g_foldWhiteList.globalListNum;
                        snprintf(g_foldWhiteList.globalList[index], sizeof(g_foldWhiteList.globalList[index]), "%s",
                            token);
                        g_foldWhiteList.globalListNum = g_foldWhiteList.globalListNum + 1;
                        token = strtok(NULL, split);
                    }
                }
            }
#ifdef FEATURE_PERSISTENCE
            int index = g_foldWhiteList.globalListNum;
            snprintf(g_foldWhiteList.globalList[index], sizeof(g_foldWhiteList.globalList[index]), "client connect succeed");
            g_foldWhiteList.globalListNum = g_foldWhiteList.globalListNum + 1;
#endif
        } else if (strstr(str[0], "clientServerSameProcess")) {
            g_clientServerSameProcess = atoi(str[1]);
        } else if (strstr(str[0], "TcpMode")) {
            g_tcpType = atoi(str[1]);
        }
    }
    fclose(pf);

    // 获取日志配置
    int ret = TestGetConfigValueInt("enableLogFold", &g_enableLogFold);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "get config enableLogFold error\n");
        g_checkLogCurrent = 0;
    }

    ret = TestGetConfigValueStr("logFoldRule", g_serLogFoldRule, FOLD_RULE_MAX_LEN);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "get config logFoldRule error\n");
        g_checkLogCurrent = 0;
    }
}

int testWaitAsyncRecv(void *userData, int expRecvNum, int timeout, bool isAutoReset)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    if (g_epollData.createBgThread == false) {
        AW_FUN_Log(LOG_ERROR, "The epoll is not created by \"create_epoll_thread\", perhaps you should use "
                              "\"testWaitAsyncRecvOneThread\" instead of \"testWaitAsyncRecv\".");
        return FAILED;
    }
    gettimeofday(&start, NULL);
    AsyncUserDataT *uData = (AsyncUserDataT *)userData;
    while (uData->recvNum != expRecvNum) {
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 110 * 1000 * 1000) {  // 部分用例的回调有另外sleep, 导致80s超时不够用, 临时调整超时时间为110s, 待接口整改完再使用timeout参数
            printf("[INFO] Recv Timeout %lf s, all OpNum : %d, actually recived num : %d, history recived num : %d\n",
                (double)duration / 1000000, expRecvNum, uData->recvNum, uData->historyRecvNum);
            if (isAutoReset) {
                uData->recvNum = 0;
            }
            return FAILED;  // 接收超时
        }
    }
    if (isAutoReset) {
        uData->recvNum = 0;
    }
    return 0;
}
int testWaitAsyncRecvOneThread(void *userData, int expRecvNum, bool isAutoReset, int timeout, int32_t epollFd)
{
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    if (g_epollDataOneThread.createInCurThread == false) {
        AW_FUN_Log(LOG_ERROR, "The epoll is not created by \"createEpollOneThread\", perhaps you should use "
                              "\"testWaitAsyncRecv\" instead of \"testWaitAsyncRecvOneThread\".");
        return FAILED;
    }
    gettimeofday(&start, NULL);
    TEST_EPOLL_EVENT events[MAX_EPOLL_EVENT_COUNT];
    AsyncUserDataT *uData = (AsyncUserDataT *)userData;
    uData->status = -1;
    while (uData->recvNum != expRecvNum) {
        int fdCount = TEST_EPOLL_WAIT(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
        if (fdCount < 0) {
            continue;
        }
        while (fdCount > 0) {
            --fdCount;
            TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
        }
        usleep(10);
        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;  // duration单位为us
        if (duration >= 180 * 1000 * 1000) {  // 80s超时, 先不使用timeout参数, 观察对用例的影响
            AW_FUN_Log(LOG_INFO,
                "[INFO] Recv Timeout %lf s, all OpNum : %d, actually recived num : %d, history recived num : %d\n",
                (double)duration / 1000000, expRecvNum, uData->recvNum, uData->historyRecvNum);
            if (isAutoReset) {
                uData->recvNum = 0;
            }
            return FAILED;  // 接收超时
        }
    }
    if (isAutoReset) {
        uData->recvNum = 0;
    }
    return 0;
}

void ConnCtionFlowCtrlNotice(void *args, GmcDbFlowCtrlLevelE flowCtrllevel)
{
    ++g_flowCnt;
    return;
}

int testPrepareNameSpace(void)
{
#if (defined(RUN_DATACOM_DAP) || defined(RUN_DATACOM_HPE)) && (!defined(FEATURE_TS) || defined(FEATURE_MULTI_TS))
    GmcConnOptionsT *connOptionsInner;
    int ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        printf("[testPrepareNameSpace] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }
    ret = GmcConnOptionsSetServerLocator(connOptionsInner, g_connServer);
    if (ret != GMERR_OK) {
        printf("[testPrepareNameSpace] GmcConnOptionsSetServerLocator failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    do {
        ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            printf("[testPrepareNameSpace] GmcConnect failed, ret = %d.\n", ret);
            break;
        }
        ret = GmcAllocStmt(conn, &stmt);
        if (ret != GMERR_OK) {
            printf("[testPrepareNameSpace] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        ret = GmcCreateNamespace(stmt, g_testNameSpace, g_userName);
        if (ret != GMERR_OK && ret != GMERR_DUPLICATE_OBJECT) {
            printf("[testPrepareNameSpace] GmcCreateNamespace(%s) failed, ret = %d.\n", g_testNameSpace, ret);
            break;
        }
    } while (0);
    if (conn) {
        ret = GmcDisconnect(conn);
        if (ret != 0) {
            printf("[testPrepareNameSpace] GmcDisconnect failed, ret = %d.\n", ret);
        }
    }
    GmcConnOptionsDestroy(connOptionsInner);
    return ret;
#endif
    return 0;
}

void useNameSpaceCallback(void *userData, int32_t status, const char *errMsg)
{
    AsyncUserDataT *uData = (AsyncUserDataT *)userData;
    uData->status = status;
    uData->recvNum++;
}

int testSetNameSpace(GmcConnT *conn, int syncMode, int *epollFd, char *nameSpace, YangConnOptionT *connOptions)
{
#if (defined(RUN_DATACOM_DAP) || defined(RUN_DATACOM_HPE)) && (!defined(FEATURE_TS) || defined(FEATURE_MULTI_TS))
    if (syncMode == 2) {
        return 0;
    }
    GmcStmtT *stmt;
    int ret = GmcAllocStmt(conn, &stmt);
    if (ret != GMERR_OK) {
        printf("[testSetNameSpace] GmcAllocStmt failed, ret = %d.\n", ret);
        return ret;
    }
    const char *newName = nameSpace;
    if (newName == NULL) {
        newName = g_testNameSpace;
    }

    if (syncMode == 0) {
        ret = GmcUseNamespace(stmt, newName);
    } else {
        AsyncUserDataT data = { 0 };
        ret = GmcUseNamespaceAsync(stmt, newName, useNameSpaceCallback, &data);
        if (ret != GMERR_OK) {
            printf("[testSetNameSpace] GmcUseNamespace/GmcUseNamespaceAsync failed, ret = %d.\n", ret);
            return ret;
        }
        if (g_isOneThreadEpoll || (connOptions != NULL && connOptions->isOneThreadEpoll)) {
            ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, *epollFd);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(GMERR_OK, data.status);
        } else {
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt);
#endif
    return 0;
}

int OpenEpollFunFromHPE()
{
#if defined RUN_DATACOM_HPE
    int ret = -1;
    do {
        void *hpeAdapter = dlopen("libehlibc.so", RTLD_NOW | RTLD_GLOBAL);
        if (hpeAdapter == NULL) {
            printf("[testEnvInit] dlopen(libehlibc.so) failed, errMsg:%s.\n", dlerror());
            break;
        }
        *(void **)(&g_hpeVpollCreate) = dlsym(hpeAdapter, "HpeVpollCreate");
        if (g_hpeVpollCreate == NULL) {
            printf("[testEnvInit] dlsym(g_hpeVpollCreate) failed, errMsg:%s\n", dlerror());
            break;
        }
        *(void **)(&g_hpeVpollCtl) = dlsym(hpeAdapter, "HpeVpollCtl");
        if (g_hpeVpollCtl == NULL) {
            printf("[testEnvInit] dlsym(g_hpeVpollCtl) failed, errMsg:%s\n", dlerror());
            break;
        }
        *(void **)(&g_hpeVpollWait) = dlsym(hpeAdapter, "HpeVpollWait");
        if (g_hpeVpollWait == NULL) {
            printf("[testEnvInit] dlsym(g_hpeVpollWait) failed, errMsg:%s\n", dlerror());
            break;
        }
        *(void **)(&g_hpeVpollDestroy) = dlsym(hpeAdapter, "HpeVpollDestroy");
        if (g_hpeVpollDestroy == NULL) {
            printf("[testEnvInit] dlsym(g_hpeVpollWait) failed, errMsg:%s\n", dlerror());
            break;
        }
        ret = 0;
    } while (0);
    if (ret != 0) {
        g_runStat = RUN_STAT_FAIL;
    }
    return ret;
#endif
    return 0;
}

int GrantGmsysviewPrivs()
{
    char cmd[512] = {0};
    (void)snprintf(cmd, 512, "grep -w \"userPolicyMode =\" %s | awk '{print $3}'", g_sysGMDBCfg);
    int userPolicyMode;
    int ret = TestGetResultCommand(cmd, &userPolicyMode);
    if (ret != 0) {
        printf("[GrantGmsysviewPrivs] get config userPolicyMode, ret = %d.\n", ret);
        return ret;
    }
    if (userPolicyMode == 2) {
        char cmd_tmp[1024];
        (void)snprintf(cmd_tmp, 1024, "gmrule -c import_allowlist -f ${TEST_HOME}/cfg/gmsysview_allow_list.gmuser -s %s", g_connServer);
        system(cmd_tmp);
        (void)snprintf(cmd_tmp, 1024, "gmrule -c import_policy -f ${TEST_HOME}/cfg/gmsysviewPolicy.gmpolicy -s %s", g_connServer);
        system(cmd_tmp);
    }
    return 0;
}

int testEnvInit(int runMode, bool needStart)
{
    if (g_runStat == RUN_STAT_SUCC) {
        return 0;
    }
    int ret = 0;
    if (g_runStat == RUN_STAT_INIT) {
        while (ret < 500 && g_runStat == RUN_STAT_INIT) {
            ++ret;
            usleep(10000);
        }
        return 0;
    }
    g_runStat = RUN_STAT_INIT;
    pthread_mutex_init(&g_logLockTest, NULL);
    pthread_mutex_init(&g_logLockClient, NULL);
    pthread_mutex_init(&g_connLock, NULL);
    pthread_mutex_init(&g_connConcurrent, NULL);
    memset(g_logFilter, 0, sizeof(g_logFilter));
    pthread_mutex_init(&g_parseSchemaCtx.threadLock, NULL);
    if (g_isReadConfig == false) {
        getSysConfig();
    }
#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
    if (needStart) {
#ifdef  FEATURE_PERSISTENCE
        system("for k in `ipcs -m | grep -v \"Shared\" | grep -v \"shmid\" | awk {'print $2'}`;do ipcrm -m $k;done");
        system("ipcs");
#endif
        char *cmdString[4] = {(char *)"gmserver", (char *)"-p", (char *)g_sysGMDBCfg};
        ret = GmsServerMain(3, cmdString);
        if (ret != 0) {
            printf("[testEnvInit] GmsServerMain failed, ret = %d.\n", ret);
            return ret;
        }
        ret = GrantGmsysviewPrivs();
        if (ret != 0) {
            printf("[testEnvInit] GrantGmsysviewPrivs failed, ret = %d.\n", ret);
            return ret;
        }
    }
#endif
#ifdef  FEATURE_PERSISTENCE
    if (g_tcpType) {
        // tcp通信模式注册
        int32_t expectRetParam = 0;
        TestRegAdaptFuncsProcTCP(expectRetParam);
        // 新增dfx功能，定位DBA异常配置问题
        char g_Dba[LOG_PATH_MAX_LEN] = {0};
        ret = TestGetConfigValueStr("DBA", g_Dba, LOG_PATH_MAX_LEN);
        AW_FUN_Log(LOG_INFO, "get config DBA = %s\n", g_Dba);
    }
#endif
    // 获取日志配置
    ret = TestGetConfigValueInt("enableLogFold", &g_enableLogFold);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "get config enableLogFold error\n");
        g_checkLogCurrent = 0;
    }

    ret = TestGetConfigValueStr("logFoldRule", g_serLogFoldRule, FOLD_RULE_MAX_LEN);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "get config logFoldRule error\n");
        g_checkLogCurrent = 0;
    }

    if (runMode != -1) {
        g_runMode = runMode;
    }
    if (g_runMode == 1) {
        GmcLogAdptFuncsT log;
        log.userWriteFunc = testcaseWriteLog;
        log.handle = &g_logCtx;
        g_logCtx.fd = NULL;
        ret = GmcRegAdaptFuncs(GMC_ADPT_LOG, &log);
        if (ret != 0) {
            printf("[testEnvInit] GmcRegAdaptFuncs failed, ret = %d.\n", ret);
        }
    }
    ret = OpenEpollFunFromHPE();
    if (ret != 0) {
        printf("[testEnvInit] OpenEpollFunFromHPE failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = GmcInit();
    if (ret != 0) {
        printf("[testEnvInit] GmcInit failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }
    ret = testPrepareNameSpace();
    if (ret != 0) {
        printf("[testEnvInit] testPrepareNameSpace failed, ret = %d.\n", ret);
        g_runStat = RUN_STAT_FAIL;
        return FAILED;
    }

    ret = TestTryRegisterSignal(TestCrashHandler);
    if(ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testEnvInit] TestTryRegisterSignal RegisterSignal failed, ret = %d.\n", ret);
    };
    g_runStat = RUN_STAT_SUCC;
    return 0;
}

void CheckMultiProcess()
{
    FILE *fp;
    char buffer[256];
    int pid = -1;
    int selfPid = getpid();  // 获取当前进程的PID

    // grep -v tee: ci构建会使用tee重定向, 会新增进程, 此处需要一并过滤
    fp = popen("ps -ef | grep gtest_filter | grep -v grep | grep -v gdb | grep -v tee | awk '{print $2}'", "r");
    if (fp == NULL) {
        return;
    }
    while (fgets(buffer, sizeof(buffer), fp)) {
        pid = atoi(buffer);
        if (pid != selfPid) {
            g_needCheckWhenSucc = false;  // 用例为多进程
            AW_FUN_Log(LOG_INFO, "This is a multi-process testcase");
            break;
        }
    }
    pclose(fp);
    return;
}

void FreeDynMemOfVertexLabelArrays(TestArrayT *c);
int testEnvClean()
{
#if defined RUN_INDEPENDENT
    (void)system("sh $TEST_HOME/tools/log_backup.sh");
    if (g_needRestartWhenFailed) {
        RestartWhenFailed();
    }
#if !defined FEATURE_TS && !defined FEATURE_STREAM && !defined FEATURE_RSMEM && !defined FEATURE_PERSISTENCE
    CheckMultiProcess();
    if (g_needCheckWhenSucc) {
        int ret = CheckWhenSucc();
        RETURN_IFERR(ret);
    }
#endif
#endif
    int ret = GmcUnInit();
    if (ret != 0) {
        printf("[testEnvClean] GmcUnInit() failed, ret = %d.\n", ret);
    }
    if (g_logCtx.fd) {
        fclose(g_logCtx.fd);
        g_logCtx.fd = NULL;
    }
    if (g_logSelf.fd) {
        fclose(g_logSelf.fd);
        g_logSelf.fd = NULL;
    }
    g_runStat = RUN_STAT_FREE;
    pthread_mutex_lock(&g_parseSchemaCtx.threadLock);
    FreeDynMemOfVertexLabelArrays(&g_parseSchemaCtx.array);
    pthread_mutex_unlock(&g_parseSchemaCtx.threadLock);
#if defined ENV_RTOSV2X
    // 查看diag.log用户组
    if (!g_isAnosEnv) {
        (void)GtExecSystemCmd("ls -l %s", g_serverLog);
    }
#endif
#ifdef FEATURE_PERSISTENCE
    if (g_tcpType) {
        // 新增dfx功能，定位DBA异常配置问题
        char g_Dba[LOG_PATH_MAX_LEN] = {0};
        ret = TestGetConfigValueStr("DBA", g_Dba, LOG_PATH_MAX_LEN);
        AW_FUN_Log(LOG_INFO, "get config DBA = %s\n", g_Dba);
    }
#endif
    return ret;
}


int testMallocConnOptions(ConnOptionT **connOptions, const char *serverLocator, const char *userName,
    const char *passwd, uint32_t *requestTimeout, uint32_t *msgReadTimeout, uint32_t *msgWriteTimeout,
    GmcSubFailedCallbackT subFailedCb, void *subFailedData, bool *useReservedConn, uint32_t *requestConnWeight,
    uint32_t *subCallBackTimeout, uint32_t *subCallBackAvgTimeout, uint32_t *subMsgRingSize)
{
    *connOptions = NULL;
    ConnOptionT *connOptionInner = (ConnOptionT *)malloc(sizeof(ConnOptionT));
    if (!connOptionInner) {
        return FAILED;
    }
    memset(connOptionInner, 0, sizeof(ConnOptionT));
    if (serverLocator) {
        connOptionInner->serverLocator = serverLocator;
    }
    if (requestTimeout) {
        connOptionInner->requestTimeout = *requestTimeout;
    }
    if (msgReadTimeout) {
        connOptionInner->msgReadTimeout = *msgReadTimeout;
    }
    if (msgWriteTimeout) {
        connOptionInner->msgWriteTimeout = *msgWriteTimeout;
    }
    if (subFailedCb) {
        connOptionInner->subFailedCb = subFailedCb;
    }
    if (subFailedData) {
        connOptionInner->subFailedData = subFailedData;
    }
    if (useReservedConn) {
        connOptionInner->useReservedConn = *useReservedConn;
    }
    if (requestConnWeight) {
        connOptionInner->requestConnWeight = *requestConnWeight;
    }
    if (subCallBackTimeout) {
        connOptionInner->subCallBackTimeout = *subCallBackTimeout;
    }
    if (subCallBackAvgTimeout) {
        connOptionInner->subCallBackAvgTimeout = *subCallBackAvgTimeout;
    }
    if (subMsgRingSize) {
        connOptionInner->subMsgRingSize = *subMsgRingSize;
    }
    *connOptions = connOptionInner;
    return 0;
}

void testFreeConnOptions(ConnOptionT *connOptions)
{
    free(connOptions);
}

int testSetAsyncQueueSize(GmcConnOptionsT *connOptions, uint32_t *value)
{
    if (value == NULL) {
        return 0;
    }
#if defined(RUN_DATACOM_DAP) || defined(RUN_DATACOM_HPE) || defined(RUN_SIMULATE)
    int ret = GmcConnOptionsSetMsgQueueSize(connOptions, *(uint32_t *)value);
    if (ret != GMERR_OK) {
        printf("[testSetAsyncQueueSize] GmcConnOptionsSetMsgQueueSize failed, msgQueueSize = %d, ret = %d.\n",
            *(uint32_t *)value, ret);
    }
    return ret;
#endif
    return 0;
}

// syncMode:   0|sync  1|async 2|sub
int testGmcConnect(GmcConnT **connOut, GmcStmtT **stmt, int syncMode, bool needEpoll,
    EpollRegFunctionT epollReg, const char *connName, const void *chanRingLen,
    ConnOptionT *connOptions, const int32_t *packShrinkThresholdSize, int runMode, int csMode,
    int *epollFd, bool isBigObj, const char *userName)
{
    int ret = testEnvInit(runMode);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] testEnvInit failed, ret = %d.\n", ret);
        return FAILED;
    }
    *connOut = NULL;
    GmcConnT *conn;

    GmcConnOptionsT *connOptionsInner;
    ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }

    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = g_passwd;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInner, p1);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsSetServerLocator failed, serverLocator = %s, ret = %d.\n", p1, ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInner, connName);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n", connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (csMode) {
        ret = GmcConnOptionsSetCSRead(connOptionsInner);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n", connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInner, epollReg, epollFd);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n", ret);
            return ret;
        }
    }

    if (packShrinkThresholdSize) {
        ret = GmcConnOptionsSetPackShrinkThreshold(connOptionsInner, *packShrinkThresholdSize);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetPackShrinkThreshold failed, packShrinkThresholdSize = %d, ret = "
                "%d.\n",
                *packShrinkThresholdSize, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subMsgRingSize) {
        ret = GmcConnOptionsSetSubMsgRingSize(connOptionsInner, connOptions->subMsgRingSize);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetRquestTimeout failed, subMsgRingSize = %d, ret = %d.\n",
                connOptions->subMsgRingSize, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
 
    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetRequestTimeout failed, requestTimeout = %d, ret = %d.\n",
                connOptions->requestTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetMsgReadTimeout failed, msgReadTimeout = %d, ret = %d.\n",
                connOptions->msgReadTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetMsgWriteTimeout failed, msgWriteTimeout = %d, ret = %d.\n",
                connOptions->msgWriteTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subFailedCb) {
        ret =
            GmcConnOptionsSetSubFailedCallback(connOptionsInner, connOptions->subFailedCb, connOptions->subFailedData);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetSubFailedCallback failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subCallBackTimeout) {
        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->subCallBackTimeout, true);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetCallbackTimeout failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subCallBackAvgTimeout) {
        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->subCallBackAvgTimeout, false);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetCallbackTimeout failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptionsInner, ConnCtionFlowCtrlNotice, NULL);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    ret = testSetAsyncQueueSize(connOptionsInner, (uint32_t *)chanRingLen);
    if (ret != GMERR_OK) {
        printf("[testGmcConnect] testSetAsyncQueueSize failed, ret = %d.\n", ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connOptions && connOptions->useReservedConn) {
        ret = GmcConnOptionsSetReservedFlag(connOptionsInner, connOptions->useReservedConn);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetReservedFlag failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    if (connOptions && connOptions->requestConnWeight) {
        ret = GmcConnOptionsSetRequestWeight(connOptionsInner, connOptions->requestConnWeight);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetRequestWeight failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    if (isBigObj == true) {
        GmcConnOptionsSetBigMessage(connOptionsInner);
    }
    // NERT场景强鉴权模式必现设置userName
    if (userName) {
        ret = GmcConnOptionsSetUserName(connOptionsInner, userName);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnOptionsSetUserName failed, userName = %s, ret = %d.\n", userName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }
    int n = 500;
    if (g_envType == 0) {
        pthread_mutex_lock(&g_connConcurrent);
        ++g_connRequest;
        while (g_connRequest > g_connOnline && g_connRequest - g_connOnline > 128 && n > 0) {
            usleep(10000);
            --n;
        }
        pthread_mutex_unlock(&g_connConcurrent);
    }

    GmcStmtT *st;
    do {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcConnect failed, serverLocator = %s, passwd = %s, ret = %d.\n", p1, p3, ret);
            break;
        }
        *connOut = conn;
        if (stmt == NULL) {
            break;
        }
        ret = GmcAllocStmt(conn, &st);
        if (ret != GMERR_OK) {
            printf("[testGmcConnect] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        *stmt = st;
    } while (0);
    GmcConnOptionsDestroy(connOptionsInner);
    if (ret == 0) {
        pthread_mutex_lock(&g_connLock);
        ++g_connOnline;
        pthread_mutex_unlock(&g_connLock);
        ret = testSetNameSpace(conn, syncMode, epollFd);
    } else {
        pthread_mutex_lock(&g_connConcurrent);
        --g_connRequest;
        pthread_mutex_unlock(&g_connConcurrent);
    }
    return ret;
}

// 20230328 重构建连接口, 暂时命名为TestYangGmcConnect, 后续统一整改完后, 再改回TestGmcConnect
// syncMode:   0|sync  1|async 2|sub
int TestYangGmcConnect(GmcConnT **connOut, GmcStmtT **stmt, int syncMode, YangConnOptionT *connOptions, int runMode)
{
    int ret = testEnvInit(runMode);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testGmcConnect] testEnvInit failed, ret = %d.", ret);
        return FAILED;
    }
    *connOut = NULL;
    GmcConnT *conn;
    int n, *epollFd;

    GmcConnOptionsT *connOptionsInner;
    ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsCreate failed, ret = %d.", ret);
        return ret;
    }

    const char *p1 = g_connServer, *p3 = g_passwd;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    GmcConnFlowCtrlLevelNoticeT flowCtrlNotice = ConnCtionFlowCtrlNotice;
    void *flowCtrlNoticeData = NULL;
    if (connOptions != NULL && connOptions->flowCtrlNoticeCb != NULL) {
        flowCtrlNotice = connOptions->flowCtrlNoticeCb;
        flowCtrlNoticeData = connOptions->flowCtrlNoticeData;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInner, p1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetServerLocator failed, serverLocator = %s, ret = %d.",
            p1, ret);
        goto END;
    }

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptionsInner, flowCtrlNotice, flowCtrlNoticeData);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.", ret);
        goto END;
    }

    if (connOptions == NULL || (connOptions != NULL && connOptions->isLocalAsyncTimeout)) {
        ret = GmcConnOptionsSetLocalAsyncTimeout(connOptionsInner);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetLocalAsyncTimeout failed, ret = %d.", ret);
            goto END;
        }
    }

    if (connOptions != NULL) {
        if (connOptions->srvMemCtxLimit > 0) {
            ret = GmcConnOptionsSetServerMemCtxLimit(connOptionsInner, connOptions->srvMemCtxLimit);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR,
                    "[testGmcConnect] GmcConnOptionsSetConnName failed, srvMemCtxLimit = %d, ret = %d.",
                    connOptions->srvMemCtxLimit, ret);
                goto END;
            }
        }
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetRequestTimeout failed, requestTimeout = %d, ret = %d.",
                connOptions->requestTimeout, ret);
            goto END;
        }

        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetMsgReadTimeout failed, msgReadTimeout = %d, ret = %d.",
                connOptions->msgReadTimeout, ret);
            goto END;
        }

        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetMsgWriteTimeout failed, msgWriteTimeout = %d, ret = %d.",
                connOptions->msgWriteTimeout, ret);
            goto END;
        }

        ret = GmcConnOptionsSetRequestWeight(connOptionsInner, connOptions->requestConnWeight);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetRequestWeight failed, ret = %d.", ret);
            goto END;
        }

        if (connOptions->subFailedCb != NULL) {
            ret = GmcConnOptionsSetSubFailedCallback(connOptionsInner, connOptions->subFailedCb,
                connOptions->subFailedData);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetSubFailedCallback failed, ret = %d.", ret);
                goto END;
            }
        }

        ret = GmcConnOptionsSetReservedFlag(connOptionsInner, connOptions->useReservedConn);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetReservedFlag failed, ret = %d.", ret);
            goto END;
        }

        if (connOptions->connName != NULL) {
            ret = GmcConnOptionsSetConnName(connOptionsInner, connOptions->connName);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.",
                    connOptions->connName, ret);
                goto END;
            }
        }

        ret = testSetAsyncQueueSize(connOptionsInner, (uint32_t *)&connOptions->msgQueueSize);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] testSetAsyncQueueSize failed, ret = %d.", ret);
            goto END;
        }

        if (connOptions->packShrinkThreshold != UINT32_MAX) {
            ret = GmcConnOptionsSetPackShrinkThreshold(connOptionsInner, connOptions->packShrinkThreshold);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR,
                    "[testGmcConnect] GmcConnOptionsSetPackShrinkThreshold failed, packShrinkThreshold = %d, ret = %d.",
                    connOptions->packShrinkThreshold, ret);
                goto END;
            }
        }

        ret = GmcConnOptionsSetMaxStmtCount(connOptionsInner, connOptions->maxStmtCount);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetMaxStmtCount failed, maxStmtCount = %d, ret = %d.",
                connOptions->maxStmtCount, ret);
            goto END;
        }

        if (connOptions->openDirectRead) {
            ret = GmcConnOptionsSetCSRead(connOptionsInner);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetCSRead failed, ret = %d.", ret);
                goto END;
            }
        }

        ret = GmcConnOptionsSetTimeInterval(connOptionsInner, connOptions->timeInterval);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetTimeInterval failed, timeInterval = %d, ret = %d.",
                connOptions->timeInterval, ret);
            goto END;
        }

        ret = GmcConnOptionsSetSlowOpLogThreshold(connOptionsInner, connOptions->timeThreshold);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetSlowOpLogThresHold failed, timeThreshold = %lld, ret = %d.",
                connOptions->timeThreshold, ret);
            goto END;
        }

        ret = GmcConnOptionsSetBigObjThreshold(connOptionsInner, connOptions->bigObjectStand);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetBigObjThreshold failed, bigObjectStand = %lld, ret = %d.",
                connOptions->bigObjectStand, ret);
            goto END;
        }

        ret = GmcConnOptionsSetConnMemCtxLimit(connOptionsInner, connOptions->connMemCtxLimit);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetConnMemCtxLimit failed, connMemCtxLimit = %d, ret = %d.",
                connOptions->connMemCtxLimit, ret);
            goto END;
        }

        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->singleCallbackTimeoutMs, true);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetCallbackTimeoutMs failed, singleCallbackTimeoutMs = %d, ret = %d.",
                connOptions->singleCallbackTimeoutMs, ret);
            goto END;
        }

        ret = GmcConnOptionsSetCallbackTimeout(connOptionsInner, connOptions->avgcallbackTimeoutMs, false);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetCallbackTimeoutMs failed, avgcallbackTimeoutMs = %d, ret = %d.",
                connOptions->avgcallbackTimeoutMs, ret);
            goto END;
        }

        if (connOptions->isLobConn) {
            ret = GmcConnOptionsSetBigMessage(connOptionsInner);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetBigMessage failed, ret = %d.", ret);
                goto END;
            }
        }

        ret = GmcConnOptionsSetAsyncTimeoutThreshold(connOptionsInner, connOptions->timeoutMs);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetAsyncTimeoutThreshold failed, timeoutMs = %d, ret = %d.",
                connOptions->timeoutMs, ret);
            goto END;
        }

        ret = GmcConnOptionsSetTrxMonitorThreshold(connOptionsInner, connOptions->logThreshold,
            connOptions->rollBackThreshold);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetTrxMonitorThreshold failed, ret = %d,"
                "logThreshold = %d, rollBackThreshold = %d.",
                ret, connOptions->logThreshold, connOptions->rollBackThreshold);
            goto END;
        }

        ret = GmcConnOptionsSetSubMsgRingSize(connOptionsInner, connOptions->subMsgRingSize);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR,
                "[testGmcConnect] GmcConnOptionsSetSubMsgRingSize failed, subMsgRingSize = %d, ret = %d.",
                connOptions->subMsgRingSize, ret);
            goto END;
        }
        if (connOptions->isCsMode) {
            ret = GmcConnOptionsSetCSMode(connOptionsInner);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR,
                    "[testGmcConnect] GmcConnOptionsSetCSMode failed ret = %d.", ret);
                goto END;
            }
        }
        // NERT场景强鉴权模式必现设置userName
        if (connOptions->userName) {
            ret = GmcConnOptionsSetUserName(connOptionsInner, connOptions->userName);
            if (ret != GMERR_OK) {
                printf("[testGmcConnect] GmcConnOptionsSetUserName failed, userName = %s, ret = %d.\n", connOptions->userName, ret);
                GmcConnOptionsDestroy(connOptionsInner);
                return ret;
            }
        }
    }

    epollFd = &g_epollData.userEpollFd;
    if (connOptions != NULL) {
        epollFd = connOptions->epollFd;
        if (connOptions->needEpoll) {
            if (connOptions->epollRegType == TEST_EPOLLREG_WITH_CONN) {
                ret =
                    GmcConnOptionsSetEpollRegFuncWithConn(connOptionsInner, connOptions->epollRegWithConnFunc, epollFd);
            } else if (connOptions->epollRegType == TEST_EPOLLREG_WITH_USD) {
                ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInner, connOptions->epollRegWithUsDFunc,
                    epollFd);
            } else if (connOptions->epollRegType == TEST_EPOLLREG) {
                ret = GmcConnOptionsSetEpollRegFunc(connOptionsInner, connOptions->epollRegFunc);
            } else {
                ret = FAILED;
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] Invalid epollReg Type");
                goto END;
            }
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithConn failed, ret = %d.", ret);
                goto END;
            }
        }
    } else {
        // 默认使用EPOLLREG_WITH_CONN类型
        ret = GmcConnOptionsSetEpollRegFuncWithConn(connOptionsInner, epollRegWithConn, epollFd);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithConn failed, ret = %d.", ret);
            goto END;
        }
    }

    n = 500;
    if (g_envType == 0) {
        pthread_mutex_lock(&g_connConcurrent);
        ++g_connRequest;
        while (g_connRequest > g_connOnline && g_connRequest - g_connOnline > 128 && n > 0) {
            usleep(10000);
            --n;
        }
        pthread_mutex_unlock(&g_connConcurrent);
    }

    GmcStmtT *st;
    do {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcConnect failed, serverLocator = %s, passwd = %s, ret = %d.", p1,
                p3, ret);
            break;
        }
        *connOut = conn;
        if (stmt == NULL) {
            break;
        }
        ret = GmcAllocStmt(conn, &st);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[testGmcConnect] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        *stmt = st;
    } while (0);
    if (ret == 0) {
        pthread_mutex_lock(&g_connLock);
        ++g_connOnline;
        pthread_mutex_unlock(&g_connLock);
        ret = testSetNameSpace(conn, syncMode, epollFd, NULL, connOptions);
    } else {
        pthread_mutex_lock(&g_connConcurrent);
        --g_connRequest;
        pthread_mutex_unlock(&g_connConcurrent);
    }
END:
    GmcConnOptionsDestroy(connOptionsInner);
    return ret;
}

int testGmcDisconnect(GmcConnT *conn, GmcStmtT *stmt)
{
    if (stmt) {
        GmcFreeStmt(stmt);
    }
    int ret = GmcDisconnect(conn);
    if (ret != 0) {
        printf("[testGmcDisconnect] GmcDisconnect() failed, ret = %d.\n", ret);
    }
    pthread_mutex_lock(&g_connLock);
    --g_connOnline;
    pthread_mutex_unlock(&g_connLock);
    pthread_mutex_lock(&g_connConcurrent);
    --g_connRequest;
    pthread_mutex_unlock(&g_connConcurrent);
    return ret;
}

// 属性值插入值与查询值比较
int32_t CompareVertexPropertyValue(GmcDataTypeE ttype, void *insertValue, void *queryValue, unsigned int size)
{
    int32_t statusRet = 0;
    if (insertValue == NULL || queryValue == NULL) {
        return 1;
    }
    switch (ttype) {
        case GMC_DATATYPE_CHAR:
            if ((*(char *)insertValue) == (*(char *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 2;
                printf("insertValue:%c queryValue:%c\n", *(char *)insertValue, *(char *)queryValue);
            }
            break;
        case GMC_DATATYPE_UCHAR:
            if ((*(unsigned char *)insertValue) == (*(unsigned char *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 3;
                printf("insertValue:%c queryValue:%c\n", *(unsigned char *)insertValue, *(unsigned char *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT8:
            if ((*(int8_t *)insertValue) == (*(int8_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 4;
                printf("insertValue:%d queryValue:%d\n", *(int8_t *)insertValue, *(int8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT8:
            if ((*(uint8_t *)insertValue) == (*(uint8_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 5;
                printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT16:
            if ((*(int16_t *)insertValue == *(int16_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 6;
                printf("insertValue:%d queryValue:%d\n", *(int16_t *)insertValue, *(int16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT16:
            if ((*(uint16_t *)insertValue == *(uint16_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 7;
                printf("insertValue:%u queryValue:%u\n", *(uint16_t *)insertValue, *(uint16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_INT32:
            if ((*(int32_t *)insertValue) == (*(int32_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 8;
                printf("insertValue:%d queryValue:%d\n", *(int32_t *)insertValue, *(int32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT32:
            if ((*(uint32_t *)insertValue) == (*(uint32_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 9;
                printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BOOL:
            if ((*(bool *)insertValue) == (*(bool *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 10;
            }
            break;
        case GMC_DATATYPE_INT64:
            if ((*(int64_t *)insertValue) == (*(int64_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 11;
                printf("insertValue:%" PRIi64 " queryValue:%" PRIi64 "\n", *(int64_t *)insertValue, *(int64_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT64:
            if ((*(uint64_t *)insertValue) == (*(uint64_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 12;
                printf("insertValue:%" PRIu64 " queryValue:%" PRIu64 "\n", *(uint64_t *)insertValue, *(uint64_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_FLOAT:
            if (fabs((*(float *)insertValue) - (*(float *)queryValue)) < __FLT_EPSILON__) {
                statusRet = 0;
            } else {
                printf("insertValue:%lf queryValue:%lf\n", *(float *)insertValue, *(float *)queryValue);
                statusRet = 13;
            }
            break;
        case GMC_DATATYPE_DOUBLE:
            if (fabs((*(double *)insertValue) - (*(double *)queryValue)) < __DBL_EPSILON__) {
                statusRet = 0;
            } else {
                printf("insertValue:%lf queryValue:%lf\n", *(double *)insertValue, *(double *)queryValue);
                statusRet = 14;
            }
            break;
        case GMC_DATATYPE_TIME:
            if ((*(uint64_t *)insertValue) == (*(uint64_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 15;
                printf("insertValue:%" PRIu64 " queryValue:%" PRIu64 "\n", *(uint64_t *)insertValue, *(uint64_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD8:
            if ((*(uint8_t *)insertValue) == (*(uint8_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 16;
                printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD16:
            if ((*(uint16_t *)insertValue == *(uint16_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 17;
                printf("insertValue:%u queryValue:%u\n", *(uint16_t *)insertValue, *(uint16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD32:
            if ((*(uint32_t *)insertValue) == (*(uint32_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 18;
                printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BITFIELD64:
            if ((*(uint64_t *)insertValue) == (*(uint64_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 19;
                printf("insertValue:%u queryValue:%u\n", *(uint32_t *)insertValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_PARTITION:
            if ((*(uint8_t *)insertValue) == (*(uint8_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 20;
                printf("insertValue:%u queryValue:%u\n", *(uint8_t *)insertValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_STRING:
            statusRet = memcmp((char *)insertValue, (char *)queryValue, size);
            if (statusRet != 0) {
                printf("insertValue: %s, queryValue: %s\n", (char *)insertValue, (char *)queryValue);
                statusRet = 21;
            }
            break;
        case GMC_DATATYPE_BYTES:
            statusRet = memcmp((char *)insertValue, (char *)queryValue, size);
            if (statusRet != 0) {
                printf("insertValue:%s queryValue:%s size: %d\n", (char *)insertValue, (char *)queryValue, size);
                statusRet = 22;
            }
            break;
        case GMC_DATATYPE_FIXED:
            statusRet = memcmp((char *)insertValue, (char *)queryValue, size);
            if (statusRet != 0) {
                printf("insertValue:%s queryValue:%s\n", (char *)insertValue, (char *)queryValue);
                statusRet = 23;
            }
            break;
        case GMC_DATATYPE_BITMAP:
            statusRet = memcmp((char *)insertValue, (char *)queryValue, size / 8);
            if (statusRet != 0) {
                printf("insertValue:%s queryValue:%s\n", (char *)insertValue, (char *)queryValue);
                statusRet = 24;
            }
            break;
        default:
            printf("the type %d is not existence!\n", ttype);
            statusRet = 100;
            break;
    }
    return statusRet;
}

int testGmcGetStmtAttr(GmcStmtT *stmt, GmcStmtAttrTypeE attr, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, attr, &affect, sizeof(affect));
    RETURN_IFERR(ret);
    if (affect != expect) {
        printf("[testGmcGetStmtAttr] expect is %d, affect is %d\n", expect, affect);
        return FAILED;
    }
    return GMERR_OK;
}
int testGetConnNum(uint32_t *connNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    if (connNum == NULL) {
        return FAILED;
    }
    int ret = 0;
    uint32_t total_num = 0;
    char command[1024];
    *connNum = total_num;

    char const * view_name = "V\\$DRT_CONN_STAT";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    EXPECT_LT(0, ret);
    if (ret <= 0) {
        printf("snprintf, ret:%d\n", ret);
        return FAILED;
    }

    char buffer[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return FAILED;
    }

    while (fgets(buffer, 500, pf) != NULL) {
        if (strstr(buffer, "CONN_ID")) {
            total_num++;
        }
    }
    ret = pclose(pf);
    if (ret == -1) {
        printf("pclose failed, errno = %d.", errno);
        return FAILED;
    }
    pf = NULL;

    // 减去IOT环境视图查询的1个连接
    if (total_num <= 0) {
        printf("[DRT_CONN_STAT]something wrong with total conn num = %d.", total_num);
        return FAILED;
    }
    *connNum = (uint32_t)(total_num - 1);

    printf("[DRT_CONN_STAT]connect num: %u\n", *connNum);
    return 0;
#endif
}

int TestGetTableInfo(void)
{
#if defined RUN_DATACOM_HPE
    printf("[TestGetTableInfo] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    char command[1024];
    char const *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[%s]:no print\n", viewName);
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    // 无表
    if (strstr(buffer, "fetched all records, finish") != NULL) {
        printf("[%s]:no table left in server\n", viewName);
        if (pclose(pf) == -1) {
            perror("pclose fail");
            return FAILED;
        }
        return 0;
    }
    // 继续打印视图错误信息或者累计表数目
    while (fgets(buffer, 150, pf) != NULL) {
    }

    if (pclose(pf) == -1) {
        perror("pclose fail");
        return FAILED;
    } else {
        return 0;
    }
#endif
}

int testGetTableNum(uint32_t *tableNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    uint32_t isDeleted = 0;
    if (tableNum == NULL) {
        printf("[testGetTableNum]:null pointer error\n");
        return FAILED;
    } else {
        *tableNum = 0;
    }
    char command[1024];
    char const * view_name = "V\\$CATA_VERTEX_LABEL_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no print\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    // 无表
    if (strstr(buffer, "fetched all records, finish") != NULL) {
        printf("[CATA_VERTEX_LABEL_INFO]:no table left in server\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
            return FAILED;
        }
        return 0;
    }
    // 继续打印视图错误信息或者累计表数目
    while (fgets(buffer, 150, pf) != NULL) {
            if (strstr(buffer, "VERTEX_TYPE_NORMAL") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "VERTEX_TYPE_DATALOG") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "VERTEX_TYPE_YANG") != NULL) {
                (*tableNum)++;
            }
            if (strstr(buffer, "IS_DELETED: 1") != NULL) {
                isDeleted++;
            }
    }

    // 查询TBM_TABLE_NUM
    int tbmTableNum = 0;
    ret = TestGetResultCommand(
        "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"TBM_TABLE_NUM\" | awk '{print $2}'", &tbmTableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询MSG_NOTIFY_TABLE_NUM
    int msgTableNum = 0;
    ret = TestGetResultCommand(
        "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"MSG_NOTIFY_TABLE_NUM\" | awk '{print $2}'", &msgTableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    *tableNum = *tableNum + tbmTableNum + msgTableNum - isDeleted;

    printf("[CATA_VERTEX_LABEL_INFO]existTable_num: %d\n", *tableNum);
    if (pclose(pf) == -1) {
        perror("pclose fail");
        return FAILED;
    } else {
        return 0;
    }
#endif
}

int TestGetYangTableNum(uint32_t *tableNum)
{
#if defined RUN_DATACOM_HPE
    printf("[TestGetYangTableNum] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    if (tableNum == NULL) {
        printf("[TestGetYangTableNum]:null pointer error\n");
        return FAILED;
    } else {
        *tableNum = 0;
    }
    ret = TestGetTableInfo();
    if (ret != 0) {
        return ret;
    }

    bool innerError = false;
    char command[1024];
    char const *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s| grep VERTEX_TYPE_YANG | wc -l", g_toolPath, g_connServer, viewName);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[%s]:no print\n", viewName);
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    uint32_t cnt = 0;
    ret = sscanf(buffer, "%u", &cnt);
    if (ret <= 0) {
        innerError = true;
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
        return FAILED;
    }
    // 视图获取标记删除的yang表数量
    char const *filterName = "IS_DELETED: 1";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s -f VERTEX_LABEL_TYPE=VERTEX_TYPE_YANG | grep \"%s\""
                                  "| wc -l", g_toolPath, g_connServer, viewName, filterName);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer1[150] = {0};
    FILE *pf1 = popen(command, "r");
    if (pf1 == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp1 = fgets(buffer1, 150, pf1);
    // 无打印
    if (tmp1 == NULL) {
        printf("[%s]:no print\n", viewName);
        if (pclose(pf1) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    uint32_t cnt1 = 0;
    ret = sscanf(buffer1, "%u", &cnt1);
    if (ret <= 0) {
        innerError = true;
    }
    if (pclose(pf1) == -1) {
        perror("pclose fail");
        return FAILED;
    }
    *tableNum = cnt - cnt1;
    printf("[%s]existYangTable_num: %d\n", viewName, *tableNum);
    if (innerError) {
        return FAILED;
    } else {
        return 0;
    }
#endif
}

int testGetSubsNum(uint32_t *subsNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    int ret = 0;
    if (subsNum == NULL) {
        printf("[testGetSubsNum]:null pointer error\n");
        return FAILED;
    } else {
        *subsNum = 0;
    }
    char command[1024];
    char const * view_name = "V\\$CATA_LABEL_SUBS_INFO";
    ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    if (ret <= 0) {
        printf("snprintf error\n");
        return FAILED;
    }
    char buffer[150] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        perror("popen fail");
        return FAILED;
    }
    // 判断视图信息
    char *tmp = fgets(buffer, 150, pf);
    // 无打印
    if (tmp == NULL) {
        printf("[CATA_LABEL_SUBS_INFO]:no print\n");
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        return FAILED;
    }
    // 无sub
    if (strstr(buffer, "fetched all records, finish") != NULL) {
        printf("[CATA_LABEL_SUBS_INFO]:no subs left in server\n");
        if (pclose(pf) == -1) {
            perror("pclose fail\n");
            return FAILED;
        }
        return 0;
    }
    // 继续打印视图错误信息或者累计订阅数目
    while (fgets(buffer, 150, pf) != NULL) {
            if (strstr(buffer, "SUBS_NAME:") != NULL) {
                (*subsNum)++;
            }
            if (strstr(buffer, "IS_DELETED: 1") != NULL) {
                // 被标记删除的订阅关系不予累计
                (*subsNum)--;
            }
    }
    printf("[CATA_LABEL_SUBS_INFO]existSubs_num: %d\n", *subsNum);
    if (pclose(pf) == -1) {
        perror("pclose fail\n");
        return FAILED;
    } else {
        return 0;
    }
#endif
}

int TestGetResultCommand(const char *cmd, int *value, char *str, int strLen)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", cmd);
        return FAILED;
    }
    char tCmdOutput[64] = {0};
    char *cmdOutput = NULL;
    int len = 0;
    if (str) {
        if (strLen <= 0) {
            printf("strLen is %d ,input error.\n", strLen);
            return FAILED;
        }
        cmdOutput = str;
        len = strLen;
    } else {
        cmdOutput = tCmdOutput;
        len = 64;
    }

    while (fgets(cmdOutput, len, pf) != NULL) {
    }
    for (int i = 0; i < len; i++) {
        if (cmdOutput[i] == '\n') {
            cmdOutput[i] = '\0';
        }
        if (cmdOutput[i] == '\0') {
            break;
        }
    }
    if (value) {
        *value = atoi(cmdOutput);
    }
    if (pclose(pf) == -1) {
        perror("pclose fail");
    }
    pf = NULL;
    return 0;
#endif
}

int TestGetV5CfgValueInt(const char *configName, int *value)
{
    char command[1024] = {};
    int ret = snprintf(command, sizeof(command),
        "gmsysview -s %s -q V\\$CONFIG_PARAMETERS -f NAME=%s|"
        "grep \" VALUE\" |awk -F '[:]' '{print $2}' |sed 's/ //g'", g_connServer, 
        configName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int TestGetV5CfgValueStr(const char *configName, char *value, int len)
{
    char command[1024] = {};
    int ret = snprintf(command, sizeof(command),
        "gmsysview -s %s -q V\\$CONFIG_PARAMETERS -f NAME=%s|"
        "grep \" VALUE\" |awk -F ': ' '{print $2}' |sed 's/ //g'", g_connServer, 
        configName);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int TestGetTsCfgValueInt(const char *configName, int *value)
{
    char command[1024] = {};
    int ret = snprintf(command, sizeof(command),
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='%s'\" -s %s |grep \" VALUE\" |awk -F '[:]' "
        "'{print $2}' |sed 's/ //g'",
        configName, g_connServerTsdb);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, value);
    if (ret) {
        return FAILED;
    }
    return 0;
}
int TestGetTsCfgValueStr(const char *configName, char *value, int len)
{
    char command[1024] = {};
    int ret = snprintf(command, sizeof(command),
        "gmsysview -sql \"select * from 'V\\$CONFIG_PARAMETERS' where NAME='%s'\" -s %s  |grep \" VALUE\" |awk -F ': ' "
        "'{print $2}' |sed 's/ //g'",
        configName, g_connServerTsdb);
    if (ret <= 0) {
        return FAILED;
    }
    ret = TestGetResultCommand(command, NULL, value, len);
    if (ret) {
        return FAILED;
    }
    return 0;
}

int TestGetConfigValueInt(const char *configName, int *value)
{
    int ret;
#if defined(FEATURE_TS)
    ret = TestGetTsCfgValueInt(configName, value);
#else
    ret = TestGetV5CfgValueInt(configName, value);
#endif
    return ret;
}

int TestGetConfigValueStr(const char *configName, char *value, int len)
{
    int ret;
#if defined(FEATURE_TS)
    ret = TestGetTsCfgValueStr(configName, value, len);
#else
    ret = TestGetV5CfgValueStr(configName, value, len);
#endif
    return ret;
}

int GetViewValueByField(const char *view, const char *field)
{
    char cmd[1024];
    int value = 0;
    (void)snprintf(cmd, 1024, "gmsysview -s %s -q V\\$%s | grep \" %s\" |awk -F '[:]' '{print $2}' |sed 's/ //g'", g_connServer, view,
        field);
    (void)TestGetResultCommand(cmd, &value);
    return value;
}

int TestVertexLabelIsExit(const char *labelName, bool *isExit)
{
    char command[1024];
    char result[64];
    int count;
    int ret = snprintf(command, 1024, "%s/gmsysview -s %s -q %s -f table=\'%s\'|wc -l", g_toolPath, g_connServer,
        "V\\$STORAGE_VERTEX_COUNT", labelName);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return FAILED;
    }
    AW_FUN_Log(LOG_INFO, "%s\n", command);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (fgets(result, sizeof(result), pf) != NULL) {
    }
    ret = pclose(pf);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "pclose pf error./n");
        return FAILED;
    }
    count = atoi(result);
    if (count == 1) {
        AW_FUN_Log(LOG_INFO, "%s is no exit!\n", labelName);
        *isExit = false;
    } else {
        AW_FUN_Log(LOG_INFO, "%s is exit!\n", labelName);
        *isExit = true;
    }
    return 0;
}

/* **********************************************************************
 * 调用DB接口查询视图函数: testScanSysview
 * 功能: 低性能机器上面并发打开popen会失败；改函数解决了此问题
 * 1.函数检查视图当前只提供1对参数(视图属性名+属性值)
 * 2.当前函数检测可以直接捞取到数据，所以需要用户指定出整形或者字符串
 * 3.当前提供整形为最大整形，字符串size为最大，没提供布尔值，视图几乎没有布尔值
 * 4.函数可以选择性透过filter参数过滤多页，同时出现index = x即为多页
 * 5.filter或者去实现与门功能
 * 6.函数并发使用不能用同一个链接，故函数自身建连+使用视图建连
 * 请注意链接资源消耗a
 * 时间：2023-03-10
 * ********************************************************************** */
int testScanSysview(const char *sysname, const char *name1, const char *value1, const char *filter,
    bool checkvalue, bool checkfilte, const char *typs)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    if (stmt == NULL) {
        AW_FUN_Log(LOG_ERROR, "arg1(%s) error.\n", stmt);
        return FAILED;
    }
    if (sysname == NULL) {
        AW_FUN_Log(LOG_ERROR, "arg2(%s) error.\n", sysname);
        return FAILED;
    }

    uint32_t value = 0;
    ret = GmcPrepareStmtByLabelName(stmt, sysname, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (checkfilte) {
        ret = GmcSetFilter(stmt, filter);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    bool isNull;
    char varField1[4096] = {0};
    uint64_t varField2 = 10086;
    bool isFind = false;
    uint32_t propSize1 = 0;
    uint32_t propSize2 = 0;
    while (!isEof) {
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        if (typs == (char *)"string") {
            ret = GmcGetVertexPropertySizeByName(stmt, name1, &propSize1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, name1, &varField1, propSize1, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        } else if (typs == (char *)"uint64_t") {
            ret = GmcGetVertexPropertySizeByName(stmt, name1, &propSize2);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, name1, &varField2, propSize2, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (ret == 9001) {
            AW_FUN_Log(LOG_ERROR, "\n[testScanSysview]not find name %s\n", name1);
            return -1;
        }
        if (typs == (char *)"string") {
            if (strcmp((char *)value1, varField1) == 0 && checkvalue) {
                isFind = true;
                AW_FUN_Log(LOG_INFO, "\nthe value is:%s\n", varField1);
                break;
            }
        } else if (typs == (char *)"uint64_t") {
            if (varField2 == atoi(value1)) {
                isFind = true;
                AW_FUN_Log(LOG_INFO, "\nthe value is:%ld\n", varField2);
                break;
            }
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    if (isFind) {
        return 0;
    } else if (isFind == false && checkvalue == true) {
        AW_FUN_Log(LOG_ERROR, "\n[testScanSysview]not find value %s\n", value1);
        return -1;
    }
    return 0;
#endif
}


/* *********************************************************************************************
 * Description  : 获取视图一个参数的所有值 值类型为string
 * Input        : sysview:系统视图名  propname:视图的属性名称
 * dest：返回属性所有的value数组,count:value个数,filter：字符串过滤条件 Notes        :
 * 获取单个参数值且参数值类型为string History      : 2023-3-13
 *
 * ***************************************************************************************** */
int TestRDGetSysviewDataTypeString(const char *sysview, const char *propname, char dest[][100], int *count,
    const char *filter)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret, result;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 扫描系统视图 场景1：视图不存在
    result = GmcPrepareStmtByLabelName(stmt, sysview, GMC_OPERATION_SCAN);
    if (result != 0) {
        AW_FUN_Log(LOG_ERROR, "\nThe Sysview %s not exist\n", sysview);
        return result;
    }
    // 添加过滤条件
    const char *cond = (const char *)filter;
    if (filter != NULL) {
        ret = GmcSetFilter(stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
    }
    result = GmcExecute(stmt);
    if (GMERR_OK != ret) {
        AW_FUN_Log(LOG_ERROR, "\n执行错误 字段名称、filter是否准确！\n");
        return result;
    }
    bool isFinish = false, isNull;
    uint32_t propSize = 0;
    char varField[2048] = {0};
    int i = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(stmt, propname, &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, propname, &varField, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        strcpy(dest[i], varField);
        i++;
    }
    *count = i;
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return result;
}

/* *********************************************************************************************
 * Description  : 获取视图一个参数的值 值类型为int
 * Input        : sysview:系统视图名  propname:视图的属性名称
 * dest：返回属性int值,count:value个数，filter：字符串过滤条件 Notes        : 获取单个参数值且参数值类型为int History :
 * 2023-3-13
 * ***************************************************************************************** */
int TestRDGetSysviewDataTypeInt(const char *sysview, const char *propname, int *dest, int *count,
    const char *filter)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret, result;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 扫描系统视图 场景1：视图不存在
    result = GmcPrepareStmtByLabelName(stmt, sysview, GMC_OPERATION_SCAN);
    if (result != 0) {
        AW_FUN_Log(LOG_ERROR, "\nThe Sysview %s not exist\n", sysview);
        return result;
    }
    const char *cond = (const char *)filter;
    if (filter != NULL) {
        ret = GmcSetFilter(stmt, cond);
        EXPECT_EQ(GMERR_OK, ret);
    }
    result = GmcExecute(stmt);
    if (GMERR_OK != ret) {
        AW_FUN_Log(LOG_INFO, "\n执行错误 字段名称、filter是否准确！\n");
        return ret;
    }
    bool isFinish = false, isNull;
    int varField;
    uint32_t propSize = 0;
    int i = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(stmt, propname, &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(stmt, propname, &varField, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        *dest = varField;
        i++;
    }
    *count = i;
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return result;
}

/* **********************************************************************
 *
 * 函数: TestRDGetCfg(char *cfgName, void *value)
 * 功能: 在线读取配置项
 * 参数:cfgName:在线修改参数的key  value：获取到的value传给value
 * 时间：2023/03
 * Author: pwx860460
 *
 * ********************************************************************** */
int TestRDGetCfg(char *cfgName, void *value)
{
    int ret = 0;
    int result = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 场景1 get 的参数不存在
    result = GmcGetCfg(stmt, cfgName);
    if (result != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "查询参数 %s不存在!\n", cfgName);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
        return result;
    }
    // 获取参数值
    void *valueTemp = NULL;
    uint32_t valueSize = 0;
    GmcDataTypeE type = GMC_DATATYPE_NULL;
    result = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_CUR_VALUE, &type, &valueTemp, &valueSize);
    EXPECT_EQ(GMERR_OK, ret);
    // int|string均可用拷贝传出value
    memcpy(value, valueTemp, valueSize);
    // 传入字符串或int 打印类型不一致
    if (type == GMC_DATATYPE_STRING) {
        AW_FUN_Log(LOG_INFO, "查询参数 %s 为 %s 结果%d\n", cfgName, valueTemp, result);
    } else {
        AW_FUN_Log(LOG_INFO, "查询参数 %s 为 %d 结果%d\n", cfgName, *(int *)valueTemp, result);
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return result;
}

/* **********************************************************************
 *
 * 函数: TestRDUpdateCfg(char *Name, GmcDataTypeE type, void *Value)
 * 功能: 在线设置配置项
 * 参数:Name:在线修改参数的key  type:GMC_DATATYPE_INT32 or GMC_DATATYPE_STRING  Value:设置参数的值
 * 时间：2023/03
 * Author: pwx860460
 *
 * ********************************************************************** */
int TestRDUpdateCfg(char *cfgName, GmcDataTypeE type, void *value)
{
    int ret = 0;
    int result = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    switch (type) {
        case GMC_DATATYPE_STRING:
            result = GmcSetCfg(stmt, cfgName, GMC_DATATYPE_STRING, value, strlen((char *)value));
            AW_FUN_Log(LOG_INFO, "设置参数 %s 为 %s 结果%d\n", cfgName, value, result);
            break;
        case GMC_DATATYPE_INT32:
            result = GmcSetCfg(stmt, cfgName, GMC_DATATYPE_INT32, (int32_t *)value, sizeof(int32_t));
            AW_FUN_Log(LOG_INFO, "设置参数 %s 为 %d 结果%d\n", cfgName, *(int *)value, result);
            break;
        default:
            AW_FUN_Log(LOG_ERROR, "default: invalid type\r\n");
            break;
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return result;
}

/* *********************************************************************************************
 * Description  : 扫描某些条件下的视图
 * Input        : stmt:客户端stmt句柄 viewName:系统视图名 filter:过滤条件
 * Notes        : 1.filter过滤条件相关约束说明，请参见手册Filter过滤条件规格约束
                  2.条件支持括号、and、or，使用and或者or连接的条件总数不能超过8个
                  3.GmcSetFilter最新约束变更，过滤string类型需加上单引号，否则报错12000并滑屏
 * History      : 2023-3-13
 * ***************************************************************************************** */

int32_t TestRDScanSysview(GmcStmtT *stmt, const char *viewName, const char *filter)
{
    if (stmt == NULL) {
        AW_FUN_Log(LOG_ERROR, "\n[TestRDScanSysview]connect %s error.\n", stmt);
        return FAILED;
    }
    if (viewName == NULL) {
        AW_FUN_Log(LOG_ERROR, "\n[TestRDScanSysview]viewname %s error.\n", viewName);
        return FAILED;
    }
    // 扫描系统视图
    int ret = GmcPrepareStmtByLabelName(stmt, viewName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "\n[TestRDScanSysview]viewname %s error.\n", viewName);
        return FAILED;
    }
    ret = GmcSetIndexKeyName(stmt, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, filter);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}


#if !defined PRODUCT_USG_TS && !defined FEATURE_STREAM

/* *********************************************************************************************
 * Description  : 读取视图数据
 * Input        : stmt:客户端stmt句柄 num:待查询属性的数量 fieldName:属性名称
 * Output       : value:要获取的视图属性的值
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.该函数为可变函数，调用方法如下：TestRDGetSysviewData("V$DB_SERVEE",
                  "instanceID=0", 3, "fieldName1", &v1, "fieldName2", &v2, "fieldName3", &v3);
                  3.视图的属性类型需要调用函数前自定义，参考手册的数据类型
                  4.支持一次性返回多个属性值，支持自动转换到对应的数据类型，查询属性个数通过num控制
                  5.当有多条记录时，调用一次，返回第一条记录；再调用一次，返回第二条记录；当没有记录可
                  调用时，返回错误码
                  6.函数具体用法和测试用例参考testcases/TestRD sysview_test.cpp文件

 * History      : 2023-3-13
 * ***************************************************************************************** */
int32_t TestRDGetSysviewData(GmcStmtT *stmt, int num, ...)
{
    const char *fieldName;
    void *value;
    bool isFinish = false;
    bool isFind = false;
    bool isNull;
    uint32_t propSize = 0;
    GmcNodeT *root, *child, *node;
    char *fieldName1;
    int ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    if (isFinish) {
        AW_FUN_Log(LOG_ERROR, "\n[getSysviewData]No more record.\n");
        return FAILED;
    }
    ret = GmcGetRootNode(stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    va_list varList;
    va_start(varList, num);
    // 逐一访问参数
    for (int i = 0; i < num; i++) {
        fieldName = va_arg(varList, const char *);
        value = va_arg(varList, void *);
        // 根据 . 判断是否为子表的情况
        const char *dotPos = strchr(fieldName, '.');
        if (dotPos == nullptr) {
            // 没有子表的情况
            node = root;
        } else {
            // 有子表的情况
            size_t fieldName1Len = dotPos - fieldName;
            fieldName1 = new char[fieldName1Len + 1];
            strncpy(fieldName1, fieldName, fieldName1Len);
            fieldName1[fieldName1Len] = '\0';
            ret = GmcNodeGetChild(root, fieldName1, &child);
            EXPECT_EQ(GMERR_OK, ret);
            fieldName = dotPos + 1;
            node = child;
        }
        ret = GmcNodeGetPropertySizeByName(node, fieldName, &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret == 9001) {
            AW_FUN_Log(LOG_ERROR, "\n[getSysviewData]not find propname %s \n", fieldName);
            return FAILED;
        }
        ret = GmcNodeGetPropertyByName(node, fieldName, value, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret == GMERR_OK) {
            isFind = true;
        } else {
            AW_FUN_Log(LOG_ERROR, "\n[getSysviewData]Failed to obtain the value of %s.\n", fieldName);
            return FAILED;
        }
    }
    va_end(varList);
    if (isFind) {
        return 0;
    }
    return FAILED;
}

/* *********************************************************************************************
 * Description  : 比对视图数据
 * Input        : proptype:视图的属性类型 checkValue:待检查的值 queryValue:查询视图获取的值
 * Return       : statusRet:是否一致
 * Notes        : 1.在调用TestRDGetSysviewData之后使用，用于比对结果
                  2.proptype属性的数据类型，请参见GmcDataTypeE结构体
                  3.checkValue是待检查的值，需提前设置

 * History      : 2023-3-20
 * ***************************************************************************************** */
int32_t TestRDCompareSysviewValue(GmcDataTypeE proptype, void *checkValue, void *queryValue)
{
    int32_t statusRet = 0;
    if (checkValue == NULL || queryValue == NULL) {
        return FAILED;
    }
    switch (proptype) {
        case GMC_DATATYPE_STRING:
            statusRet = strcmp((char *)checkValue, (char *)queryValue);
            if (statusRet != 0) {
                printf("checkValue: %s, queryValue: %s\n", (char *)checkValue, (char *)queryValue);
                statusRet = 1;
            }
            break;
        case GMC_DATATYPE_UINT8:
            if ((*(uint8_t *)checkValue) == (*(uint8_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 2;
                printf("checkValue:%u queryValue:%u\n", *(uint8_t *)checkValue, *(uint8_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT16:
            if ((*(uint16_t *)checkValue == *(uint16_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 3;
                printf("checkValue:%u queryValue:%u\n", *(uint16_t *)checkValue, *(uint16_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT32:
            if ((*(uint32_t *)checkValue) == (*(uint32_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 4;
                printf("checkValue:%u queryValue:%u\n", *(uint32_t *)checkValue, *(uint32_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_UINT64:
            if ((*(uint64_t *)checkValue) == (*(uint64_t *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 5;
                printf("checkValue:%lu queryValue:%lu\n", *(uint64_t *)checkValue, *(uint64_t *)queryValue);
            }
            break;
        case GMC_DATATYPE_BOOL:
            if ((*(bool *)checkValue) == (*(bool *)queryValue)) {
                statusRet = 0;
            } else {
                statusRet = 6;
            }
            break;
        default:
            printf("the type %d is not existence!\n", (int)proptype);
            statusRet = FAILED;
            break;
    }
    return statusRet;
}

/* *********************************************************************************************
 * Description  : 查询满足条件的视图数据记录数
 * Input        : stmt:客户端stmt句柄 succNum:查询到的全部记录数
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.不可与TestRDGetSysviewData和TestRDSumSysviewFieldName混用

 * History      : 2023-3-23
 * ***************************************************************************************** */
int TestRDGetSysviewRecordNum(GmcStmtT *stmt, int *succNum)
{
    int ret;
    bool isFinish = false;
    int count = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        count++;
    }
    *succNum = count;
    AW_FUN_Log(LOG_INFO, "\nThe number of sysview records is %d.\n", count);
    return ret;
}

/* *********************************************************************************************
 * Description  : 累加满足条件的视图数据字段值
 * Input        : stmt:客户端stmt句柄 succNum:查询到的全部记录数
 * Notes        : 1.先调用testRDScanSysview接口扫描和过滤系统视图
                  2.不可与TestRDGetSysviewData和TestRDGetSysviewRecordNum混用
 * History      : 2023-3-23
 * ***************************************************************************************** */
int32_t TestRDSumSysviewFieldValue(GmcStmtT *stmt, const char *fieldName, void *fieldValue, int *sumFieldValue)
{
    int ret;
    bool isFinish = false, isNull;
    GmcNodeT *root, *child, *node;
    char *fieldName1;
    const char *fieldName2;
    uint32_t propSize = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = GmcGetRootNode(stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        // 根据 . 判断是否为子表的情况
        const char *dotPos = strchr(fieldName, '.');
        if (dotPos == nullptr) {
            // 没有子表的情况
            node = root;
            fieldName2 = fieldName;
        } else {
            // 有子表的情况
            size_t fieldName1Len = dotPos - fieldName;
            fieldName1 = new char[fieldName1Len + 1];
            strncpy(fieldName1, fieldName, fieldName1Len);
            fieldName1[fieldName1Len] = '\0';
            ret = GmcNodeGetChild(root, fieldName1, &child);
            EXPECT_EQ(GMERR_OK, ret);
            fieldName2 = dotPos + 1;
            node = child;
        }
        ret = GmcNodeGetPropertySizeByName(node, fieldName2, &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(node, fieldName2, fieldValue, propSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        int value = *(int *)fieldValue;
        *sumFieldValue += value;
    }
    return ret;
}

/* *********************************************************************************************
 * Description  : 检查表（vertex，kv，edge）总数
 * Input        : stmt:客户端stmt句柄 type:表类型vertex/kv/edge count:表总数
 * History      : 2023-3-23
 * ***************************************************************************************** */
int32_t TestRDCheckLabelNum(GmcStmtT *stmt, const char *type, int *count)
{
    int ret = 0;
    if (type == (char *)"vertex") {
        ret = TestRDScanSysview(stmt, "V$STORAGE_VERTEX_LABEL_STAT", NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestRDGetSysviewRecordNum(stmt, count);
        EXPECT_EQ(GMERR_OK, ret);
        return 0;
    } else if (type == (char *)"kv") {
        ret = TestRDScanSysview(stmt, "V$STORAGE_KV_STAT", NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestRDGetSysviewRecordNum(stmt, count);
        EXPECT_EQ(GMERR_OK, ret);
        return 0;
    } else if (type == (char *)"edge") {
        ret = TestRDScanSysview(stmt, "V$STORAGE_EDGE_LABEL_STAT", NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = TestRDGetSysviewRecordNum(stmt, count);
        EXPECT_EQ(GMERR_OK, ret);
        return 0;
    } else {
        AW_FUN_Log(LOG_ERROR, "\n[TestRDCheckLabelNum]Failed to obtain the value of %s.\n", type);
        return FAILED;
    }
}

/* *********************************************************************************************
 * Description  : 检查vertex表总数
 * Input        : stmt:客户端stmt句柄 type:表类型vertex count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckVertexLabelNum(GmcStmtT *stmt, int *count)
{
    int ret = 0;
    ret = TestRDScanSysview(stmt, "V$STORAGE_VERTEX_LABEL_STAT", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewRecordNum(stmt, count);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

/* *********************************************************************************************
 * Description  : 检查KV表总数
 * Input        : stmt:客户端stmt句柄 type:表类型KV count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckKVLabelNum(GmcStmtT *stmt, int *count)
{
    int ret = 0;
    ret = TestRDScanSysview(stmt, "V$STORAGE_KV_STAT", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewRecordNum(stmt, count);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

/* *********************************************************************************************
 * Description  : 检查edge表总数
 * Input        : stmt:客户端stmt句柄 type:表类型edge count:表总数
 * History      : 2023-4-19
 * ***************************************************************************************** */
int32_t TestRDCheckEdgeLabelNum(GmcStmtT *stmt, int *count)
{
    int ret = 0;
    ret = TestRDScanSysview(stmt, "V$STORAGE_EDGE_LABEL_STAT", NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = TestRDGetSysviewRecordNum(stmt, count);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

/* *********************************************************************************************
 * 类型：char,uchar,uint8,int8,uint16,int16,uint32,int32,uint64,int64,float,double,bool,
 * time,string,bytes,fixed,bitmap,partition,resource,bit8,bit16,bit32,bit64
 * ***************************************************************************************** */
int GetSysDataType(char *dataType)
{
    if (strcmp(dataType, "char") == 0) {
        return GMC_DATATYPE_CHAR;
    }
    if (strcmp(dataType, "uchar") == 0) {
        return GMC_DATATYPE_UCHAR;
    }
    if (strcmp(dataType, "uint8") == 0) {
        return GMC_DATATYPE_UINT8;
    }
    if (strcmp(dataType, "int8") == 0) {
        return GMC_DATATYPE_INT8;
    }
    if (strcmp(dataType, "uint16") == 0) {
        return GMC_DATATYPE_UINT16;
    }
    if (strcmp(dataType, "int16") == 0) {
        return GMC_DATATYPE_INT16;
    }
    if (strcmp(dataType, "uint32") == 0) {
        return GMC_DATATYPE_UINT32;
    }
    if (strcmp(dataType, "int32") == 0) {
        return GMC_DATATYPE_INT32;
    }
    if (strcmp(dataType, "uint64") == 0) {
        return GMC_DATATYPE_UINT64;
    }
    if (strcmp(dataType, "int64") == 0) {
        return GMC_DATATYPE_INT64;
    }
    if (strcmp(dataType, "float") == 0) {
        return GMC_DATATYPE_FLOAT;
    }
    if (strcmp(dataType, "double") == 0) {
        return GMC_DATATYPE_DOUBLE;
    }
    if (strcmp(dataType, "bool") == 0) {
        return GMC_DATATYPE_BOOL;
    }
    if (strcmp(dataType, "time") == 0) {
        return GMC_DATATYPE_TIME;
    }
    if (strcmp(dataType, "string") == 0) {
        return GMC_DATATYPE_STRING;
    }
    if (strcmp(dataType, "bytes") == 0) {
        return GMC_DATATYPE_BYTES;
    }
    if (strcmp(dataType, "fixed") == 0) {
        return GMC_DATATYPE_FIXED;
    }
    if (strcmp(dataType, "bitmap") == 0) {
        return GMC_DATATYPE_BITMAP;
    }
    if (strcmp(dataType, "partition") == 0) {
        return GMC_DATATYPE_PARTITION;
    }
    if (strcmp(dataType, "resource") == 0) {
        return GMC_DATATYPE_RESOURCE;
    }
    if (strcmp(dataType, "bit8") == 0) {
        return GMC_DATATYPE_BITFIELD8;
    }
    if (strcmp(dataType, "bit16") == 0) {
        return GMC_DATATYPE_BITFIELD16;
    }
    if (strcmp(dataType, "bit32") == 0) {
        return GMC_DATATYPE_BITFIELD32;
    }
    if (strcmp(dataType, "bit64") == 0) {
        return GMC_DATATYPE_BITFIELD64;
    }
    return T_FAILED;
}

int GetFieldNodeDataType(VertexNodeT *node)
{
    VertexNodeT *temp = node;
    while (temp->nodeType != TEST_FIELD) {
        temp = temp->next;
    }
    return temp->dataType;
}

int ParseFieldValue(TFieldValueT *field, int maxFieldLen, const char *formatStr, int *parseCount)
{
    if (field == NULL || formatStr == NULL) {
        return T_FAILED;
    }
    int count = 0;
    // ;分割表达式
    int formatLen = strlen(formatStr);
    char tempFormatStr[formatLen + 1];
    (void)memcpy(tempFormatStr, formatStr, formatLen);
    tempFormatStr[formatLen] = '\0';
    char *s = strtok(tempFormatStr, ";");
    if (s == NULL) {
        s = tempFormatStr;
    }
    while (s) {
        char *tempStr = s;
        field[count].node = (VertexNodeT *)malloc(sizeof(VertexNodeT));
        if (field[count].node == NULL) {
            printf("[ ParseFieldValue ] malloc node failed.\n");
            return T_FAILED;
        }
        (void)memset(field[count].format, 0, sizeof(field[count].format));
        VertexNodeT *node = field[count].node;
        while (tempStr) {
            (void)memset(node->name, 0, sizeof(node->name));
            char *findRecord = strchr(tempStr, '.');
            char *findPlaceHolder = strchr(tempStr, '=');
            // type:field
            if (findRecord == NULL || (findPlaceHolder != NULL && findRecord > findPlaceHolder)) {
                char dataTypeStr[15];
                sscanf(tempStr, "%[^(](%[^)])=%s", node->name, dataTypeStr, field[count].format);
                node->dataType = (GmcDataTypeE)GetSysDataType(dataTypeStr);
                node->nodeType = TEST_FIELD;
                node->next = NULL;
                tempStr = NULL;
                continue;
            }
            char *findArray = strchr(tempStr, '[');
            // type:array
            if (findArray && findArray < findRecord) {
                // node type
                node->nodeType = T_ARRAY;
                // name
                memcpy(node->name, tempStr, (findArray - tempStr));
                node->name[findArray - tempStr] = '\0';
                // index
                tempStr = findArray + 1;
                char *right = strchr(tempStr, ']');
                if (right == NULL) {
                    printf("[ ParseFieldValue ] parse failed, '[' not match ']'\n");
                    return T_FAILED;
                }
                char indexStr[10];
                memcpy(indexStr, tempStr, (right - tempStr));
                indexStr[right - tempStr] = '\0';
                node->arrayIndex = atoi(indexStr);
                node->next = (VertexNodeT *)malloc(sizeof(VertexNodeT));
                if (node->next == NULL) {
                    printf("[ ParseFieldValue ] malloc node failed.\n");
                    return T_FAILED;
                }
                node = node->next;
                tempStr = right + 2;
            } else { // type:record
                // node type
                node->nodeType = T_RECORD;
                // name
                memcpy(node->name, tempStr, (findRecord - tempStr));
                node->name[findRecord - tempStr] = '\0';

                node->next = (VertexNodeT *)malloc(sizeof(VertexNodeT));
                if (node->next == NULL) {
                    printf("[ ParseFieldValue ] malloc node failed.\n");
                    return T_FAILED;
                }
                node = node->next;
                tempStr = findRecord + 1;
            }
        }
        count++;
        s = strtok(NULL, ";");
    }
    for (int i = 0; i < count; i++) {
        field[i].value = (char *)malloc(MAX_GEN_DATA_LEN);
        if (field[i].value == NULL) {
            printf("[ ParseFieldValue ] malloc value failed.\n");
            return T_FAILED;
        }
    }
    *parseCount = count;
    return T_OK;
}

void FreeVertexNode(VertexNodeT *node)
{
    if (node->next) {
        FreeVertexNode(node->next);
    }
    free(node);
    node = NULL;
}

void FreeFieldValue(TFieldValueT *field, int fieldCount)
{
    for (int i = 0; i < fieldCount; i++) {
        if (field[i].value) {
            free(field[i].value);
            field[i].value = NULL;
        }
        FreeVertexNode(field[i].node);
    }
}

void OprVertexCallback(void *userData, uint32_t affectedRows, Status status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->affectRows = affectedRows;
        user_data->historyRecvNum++;
        user_data->recvNum++;
        if (status == T_OK) {
            user_data->succNum++;
        }
    }
}

int TransformValue(GmcDataTypeE dateType, char *value, int *size)
{
    int ret = T_OK;
    switch (dateType) {
        case GMC_DATATYPE_CHAR: {
            *size = sizeof(char);
            if (IsDigitStr(value)) {
                int8_t temp = (int8_t)atoi(value);
                (void)memcpy(value, &temp, sizeof(int8_t));
            }
            break;
        }
        case GMC_DATATYPE_UCHAR: {
            *size = sizeof(unsigned char);
            if (IsDigitStr(value)) {
                uint8_t temp = (uint8_t)atoi(value);
                (void)memcpy(value, &temp, sizeof(uint8_t));
            }
            break;
        }
        case GMC_DATATYPE_INT8: {
            int8_t temp = (int8_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(int8_t));
            *size = sizeof(int8_t);
            break;
        }
        case GMC_DATATYPE_UINT8: {
            uint8_t temp = (uint8_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint8_t));
            *size = sizeof(uint8_t);
            break;
        }
        case GMC_DATATYPE_INT16: {
            int16_t temp = (int16_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(int16_t));
            *size = sizeof(int16_t);
            break;
        }
        case GMC_DATATYPE_UINT16: {
            uint16_t temp = (uint16_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint16_t));
            *size = sizeof(uint16_t);
            break;
        }
        case GMC_DATATYPE_INT32: {
            int32_t temp = (int32_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(int32_t));
            *size = sizeof(int32_t);
            break;
        }
        case GMC_DATATYPE_UINT32: {
            uint32_t temp = (uint32_t)HpeLibcAtoll(value);
            (void)memcpy(value, &temp, sizeof(uint32_t));
            *size = sizeof(uint32_t);
            break;
        }
        case GMC_DATATYPE_BOOL: {
            bool temp = (bool)atoi(value);
            (void)memcpy(value, &temp, sizeof(bool));
            *size = sizeof(bool);
            break;
        }
        case GMC_DATATYPE_INT64: {
            int64_t temp = (int64_t)HpeLibcAtoll(value);
            (void)memcpy(value, &temp, sizeof(int64_t));
            *size = sizeof(int64_t);
            break;
        }
        case GMC_DATATYPE_UINT64: {
            uint64_t temp = strtoull(value, NULL, 10);
            (void)memcpy(value, &temp, sizeof(uint64_t));
            *size = sizeof(uint64_t);
            break;
        }
        case GMC_DATATYPE_FLOAT: {
            float temp = (float)atof(value);
            (void)memcpy(value, &temp, sizeof(float));
            *size = sizeof(float);
            break;
        }
        case GMC_DATATYPE_DOUBLE: {
            double temp = (double)atof(value);
            (void)memcpy(value, &temp, sizeof(double));
            *size = sizeof(double);
            break;
        }
        case GMC_DATATYPE_TIME: {
            int64_t temp = (int64_t)atol(value);
            (void)memcpy(value, &temp, sizeof(int64_t));
            *size = sizeof(int64_t);
            break;
        }
        case GMC_DATATYPE_PARTITION: {
            uint8_t temp = (uint8_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint8_t));
            *size = sizeof(uint8_t);
            break;
        }
        case GMC_DATATYPE_STRING: {
            *size = strlen(value);
            break;
        }
        case GMC_DATATYPE_BYTES: {
            *size = strlen(value);
            break;
        }
        case GMC_DATATYPE_FIXED: {
            *size = strlen(value);
            break;
        }
        case GMC_DATATYPE_BITMAP: {
            GmcBitMapT temp = *(GmcBitMapT *)value;
            (void)memcpy(value, &temp, sizeof(GmcBitMapT));
            *size = sizeof(GmcBitMapT);
            break;
        }
        case GMC_DATATYPE_RESOURCE: {
            uint64_t temp = (uint64_t)atol(value);
            (void)memcpy(value, &temp, sizeof(uint64_t));
            *size = sizeof(uint64_t);
            break;
        }
        case GMC_DATATYPE_BITFIELD8: {
            uint8_t temp = (uint8_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint8_t));
            *size = sizeof(uint8_t);
            break;
        }
        case GMC_DATATYPE_BITFIELD16: {
            uint16_t temp = (uint16_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint16_t));
            *size = sizeof(uint16_t);
            break;
        }
        case GMC_DATATYPE_BITFIELD32: {
            uint32_t temp = (uint32_t)atoi(value);
            (void)memcpy(value, &temp, sizeof(uint32_t));
            *size = sizeof(uint32_t);
            break;
        }
        case GMC_DATATYPE_BITFIELD64: {
            uint64_t temp = (uint64_t)atol(value);
            (void)memcpy(value, &temp, sizeof(uint64_t));
            *size = sizeof(uint64_t);
            break;
        }
        default:
            printf("the type %d is not existence!\n", dateType);
            ret = T_FAILED;
            break;
    }
    return ret;
}

int SetIndexKeyValueBaseType(GmcStmtT *stmt, uint32_t index, GmcDataTypeE dateType, char *value)
{
    int ret;
    int size = 0;
    ret = TransformValue(dateType, value, &size);
    RETURN_IFERR(ret);
    ret = GmcSetIndexKeyValue(stmt, index, dateType, value, size);
    return ret;
}

int TestInsVertexSync(GmcStmtT *stmt, const char *tableName, char *setJson, int beginVal, int endVal, uint32_t versionId)
{
    AW_FUN_Log(LOG_INFO, "[TestInsVertexSync] tableName: %s format: %s range:(%d~%d)", tableName,
        ((setJson) ? setJson : "null"), beginVal, endVal);
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(setJson, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
    }
    return ret;
}

int TestInsVertexAsync(GmcStmtT *stmt, const char *tableName, char *setJson, int beginVal, int endVal, uint32_t versionId)
{
    AW_FUN_Log(LOG_INFO, "[TestInsVertexAsync] tableName: %s format: %s range:(%d~%d)", tableName,
        ((setJson) ? setJson : "null"), beginVal, endVal);
    json_t *dataJson;
    json_error_t dataJsonError;
    char setJsonGenValue[MAX_GEN_DATA_LEN] = {0};
    AsyncUserDataT data = { 0 };
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = OprVertexCallback;
    insertRequestCtx.userData = &data;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_INSERT);
    RETURN_IFERR(ret);
    for (int i = beginVal; i < endVal; i++) {
        ret = GenFormatDataByIndex(setJson, i, setJsonGenValue, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        dataJson = json_loads(setJsonGenValue, 0, &dataJsonError);
        char *jStr = json_dumps(dataJson, JSON_INDENT(0));
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        json_decref(dataJson);
        free(jStr);
        RETURN_IFERR(ret);
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        RETURN_IFERR(ret);
    }
    ret = testWaitAsyncRecv(&data, endVal - beginVal);
    RETURN_IFERR(ret);
    if (data.succNum != (endVal - beginVal)) {
        return T_FAILED;
    }
    return ret;
}

int TestdelVertexSync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int beginVal,
    int endVal, uint32_t versionId)
{
    AW_FUN_Log(LOG_INFO, "[TestdelVertexSync] tableName: %s indexName: %s cond: %s range:(%d~%d)", tableName,
        ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), beginVal, endVal);
    char genValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件删除
        // cond 为NULL，全表删除
        if (cond == NULL) {
            ret = GmcSetFilter(stmt, NULL);
            RETURN_IFERR(ret);
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
        } else {
            for (int i = beginVal; i < endVal; i++) {
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                RETURN_IFERR(ret);
                ret = GmcSetFilter(stmt, genValue);
                RETURN_IFERR(ret);
                ret = GmcExecute(stmt);
                RETURN_IFERR(ret);
            }
        }
    } else {
        // 索引删除
        TFieldValueT field[MAX_FORMAT_FIELDS];
        // 解析索引值
        int fieldCount = 0;
        ret = ParseFieldValue(field, MAX_FORMAT_FIELDS, cond, &fieldCount);
        RETURN_IFERR(ret);
        for (int i = beginVal; i < endVal; i++) {
            ret = GmcSetIndexKeyName(stmt, indexName);
            if (ret != T_OK) {
                FreeFieldValue(field, fieldCount);
                return ret;
            }
            // 设置索引值
            for (int n = 0; n < fieldCount; n++) {
                ret = GenFormatDataByIndex(field[n].format, i, field[n].value, MAX_GEN_DATA_LEN);
                if (ret != T_OK) {
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(field[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, field[n].value);
                if (ret != T_OK) {
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
            }
            ret = GmcExecute(stmt);
            if (ret != T_OK) {
                FreeFieldValue(field, fieldCount);
                return ret;
            }
        }
        FreeFieldValue(field, fieldCount);
    }
    return T_OK;
}

int TestdelVertexAsync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int beginVal,
    int endVal, uint32_t versionId)
{
    AW_FUN_Log(LOG_INFO, "[TestdelVertexAsync] tableName: %s indexName: %s cond: %s range:(%d~%d)", tableName,
        ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), beginVal, endVal);
    AsyncUserDataT data = { 0 };
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.insertCb = OprVertexCallback;
    deleteRequestCtx.userData = &data;
    char genValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_DELETE);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件删除
        // cond为NULL，全表删除
        if (cond == NULL) {
            ret = GmcSetFilter(stmt, NULL);
            RETURN_IFERR(ret);
            ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
            RETURN_IFERR(ret);
        } else {
            for (int i = beginVal; i < endVal; i++) {
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                RETURN_IFERR(ret);
                ret = GmcSetFilter(stmt, genValue);
                RETURN_IFERR(ret);
                ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
                RETURN_IFERR(ret);
            }
        }
    } else {
        // 索引删除
        TFieldValueT field[MAX_FORMAT_FIELDS];
        // 解析索引值
        int fieldCount = 0;
        ret = ParseFieldValue(field, MAX_FORMAT_FIELDS, cond, &fieldCount);
        RETURN_IFERR(ret);
        for (int i = beginVal; i < endVal; i++) {
            ret = GmcSetIndexKeyName(stmt, indexName);
            if (ret != T_OK) {
                FreeFieldValue(field, fieldCount);
                return ret;
            }
            // 设置索引值
            for (int n = 0; n < fieldCount; n++) {
                ret = GenFormatDataByIndex(field[n].format, i, field[n].value, MAX_GEN_DATA_LEN);
                if (ret != T_OK) {
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(field[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, field[n].value);
                if (ret != T_OK) {
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
            }
            ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
            if (ret != T_OK) {
                FreeFieldValue(field, fieldCount);
                return ret;
            }
        }
        FreeFieldValue(field, fieldCount);
    }
    int expectCount = 0;
    if (cond == NULL) {
        // 全表删除
        expectCount = 1;
    } else {
        expectCount = endVal - beginVal;
    }
    ret = testWaitAsyncRecv(&data, expectCount);
    RETURN_IFERR(ret);
    if (data.succNum != expectCount) {
        return T_FAILED;
    }
    return T_OK;
}

int TestSelVertexCount(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond, int expectCount,
    int beginVal, int endVal, uint32_t versionId)
{
    AW_FUN_Log(LOG_INFO, "[TestSelVertexCount] tableName: %s indexName: %s cond: %s expect count: %d range:(%d~%d)",
        tableName, ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), expectCount, beginVal, endVal);
    bool isFinish = false;
    int fetchCount = 0;
    char genValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件扫描
        // cond 为NULL,全表扫描
        if (cond == NULL) {
            ret = GmcSetFilter(stmt, NULL);
            RETURN_IFERR(ret);
            ret = GmcExecute(stmt);
            RETURN_IFERR(ret);
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
                if (isFinish) {
                    break;
                }
                fetchCount++;
            }
        } else {
            for (int i = beginVal; i < endVal; i++) {
                isFinish = false;
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                RETURN_IFERR(ret);
                ret = GmcSetFilter(stmt, genValue);
                RETURN_IFERR(ret);
                ret = GmcExecute(stmt);
                RETURN_IFERR(ret);
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    RETURN_IFERR(ret);
                    if (isFinish) {
                        break;
                    }
                    fetchCount++;
                }
            }
        }
    } else {
        // 索引扫描
        TFieldValueT field[MAX_FORMAT_FIELDS];
        // 解析索引值
        int fieldCount = 0;
        ret = ParseFieldValue(field, MAX_FORMAT_FIELDS, cond, &fieldCount);
        RETURN_IFERR(ret);
        for (int i = beginVal; i < endVal; i++) {
            isFinish = false;
            ret = GmcSetIndexKeyName(stmt, indexName);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "GmcSetIndexKeyName status: %d.", ret);
                FreeFieldValue(field, fieldCount);
                return ret;
            }
            // 设置索引值
            for (int n = 0; n < fieldCount; n++) {
                ret = GenFormatDataByIndex(field[n].format, i, field[n].value, MAX_GEN_DATA_LEN);
                if (ret != T_OK) {
                    AW_FUN_Log(LOG_STEP, "GenFormatDataByIndex status: %d.", ret);
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(field[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, field[n].value);
                if (ret != T_OK) {
                    AW_FUN_Log(LOG_STEP, "SetIndexKeyValueBaseType status: %d.", ret);
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
            }
            ret = GmcExecute(stmt);
            if (ret != T_OK) {
                AW_FUN_Log(LOG_STEP, "GmcExecute status: %d.", ret);
                FreeFieldValue(field, fieldCount);
                return ret;
            }
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                if (ret != T_OK) {
                    AW_FUN_Log(LOG_STEP, "GmcFetch status: %d.", ret);
                    FreeFieldValue(field, fieldCount);
                    return ret;
                }
                if (isFinish) {
                    break;
                }
                fetchCount++;
            }
        }
        FreeFieldValue(field, fieldCount);
    }
    if (fetchCount != expectCount) {
        printf("check failed, expect scan count: %d, actually count: %d\n", expectCount, fetchCount);
        return T_FAILED;
    }
    return T_OK;
}

int CheckTreeRecord(GmcStmtT *stmt, GmcNodeT *root, VertexNodeT *node, char *format, int formatIndex)
{
    if (node->nodeType == TEST_FIELD) {
        uint32_t size;
        int ret;
        bool isNull = false;
        ret = GmcNodeGetPropertySizeByName(root, node->name, &size);
        RETURN_IFERR(ret);
        char *expectBuf = (char *)malloc(MAX_GEN_DATA_LEN);
        if (expectBuf == NULL) {
            printf("[CheckTreeRecord] malloc failed.\n");
            return T_FAILED;
        }
        (void)memset(expectBuf, 0, MAX_GEN_DATA_LEN);
        ret = GenFormatDataByIndex(format, formatIndex, expectBuf, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        if (node->dataType >= GMC_DATATYPE_STRING && node->dataType <= GMC_DATATYPE_FIXED) {
            char *value = (char *)malloc(MAX_GEN_DATA_LEN);
            if (value == NULL) {
                free(expectBuf);
                printf("[CheckTreeRecord] malloc failed.\n");
                return T_FAILED;
            }
            (void)memset(value, 0, MAX_GEN_DATA_LEN);
            ret = GmcNodeGetPropertyByName(root, node->name, value, size, &isNull);
            if (ret != T_OK) {
                free(value);
                GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
            }
            ret = CompareVertexPropertyValue(node->dataType, expectBuf, value, size);
            if (ret != T_OK) {
                free(value);
                GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
            }
            free(value);
        } else if (node->dataType == GMC_DATATYPE_DOUBLE || node->dataType == GMC_DATATYPE_FLOAT) {
            double value;
            ret = GmcNodeGetPropertyByName(root, node->name, &value, size, &isNull);
            GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
            double expectValue = atof(expectBuf);
            ret = CompareVertexPropertyValue(node->dataType, &expectValue, &value, size);
            GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
        } else {
            uint64_t value;
            ret = GmcNodeGetPropertyByName(root, node->name, &value, size, &isNull);
            GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
            uint64_t expectValue = atol(expectBuf);
            ret = CompareVertexPropertyValue(node->dataType, &expectValue, &value, size);
            GOTO_TAG_IFERR(ret, FREE_BUF_TAG);
        }
    FREE_BUF_TAG:
        free(expectBuf);
        return ret;
    } else if (node->nodeType == T_RECORD || node->nodeType == T_ARRAY) {
        GmcNodeT *record;
        int ret = GmcNodeGetChild(root, node->name, &record);
        if (ret != T_OK) {
            return T_FAILED;
        }
        if (node->nodeType == T_RECORD) {
            ret = CheckTreeRecord(stmt, record, node->next, format, formatIndex);
            (void)GmcFreeNode(record);
            return ret;
        } else {
            GmcNodeT *element;
            ret = GmcNodeGetElementByIndex(record, node->arrayIndex, &element);
            if (ret != T_OK) {
                (void)GmcFreeNode(record);
                printf("[CheckTreeRecord] GmcNodeGetElementByIndex failed.\n");
                return T_FAILED;
            }
            ret = CheckTreeRecord(stmt, element, node->next, format, formatIndex);
            (void)GmcFreeNode(element);
            (void)GmcFreeNode(record);
            return ret;
        }
    }
    return T_OK;
}

int CheckVertexRecord(GmcStmtT *stmt, TFieldValueT *expectField, int expectFieldCount, int formatIndex)
{
    int ret;
    uint32_t size;
    bool isNull = false;
    for (int i = 0; i < expectFieldCount; i++) {
        TFieldValueT field = expectField[i];
        if (field.node->nodeType == TEST_FIELD) {
            ret = GmcGetVertexPropertySizeByName(stmt, field.node->name, &size);
            RETURN_IFERR(ret);
            ret = GenFormatDataByIndex(field.format, formatIndex, field.value, MAX_GEN_DATA_LEN);
            RETURN_IFERR(ret);
            if (field.node->dataType >= GMC_DATATYPE_STRING && field.node->dataType <= GMC_DATATYPE_FIXED) {
                char value[MAX_GEN_DATA_LEN];
                (void)memset(value, 0, MAX_GEN_DATA_LEN);
                ret = GmcGetVertexPropertyByName(stmt, field.node->name, value, size, &isNull);
                RETURN_IFERR(ret);
                ret = CompareVertexPropertyValue(field.node->dataType, field.value, value, size);
                RETURN_IFERR(ret);
            } else if (field.node->dataType == GMC_DATATYPE_DOUBLE) {
                double value;
                ret = GmcGetVertexPropertyByName(stmt, field.node->name, &value, size, &isNull);
                RETURN_IFERR(ret);
                double expectValue = atof(field.value);
                ret = CompareVertexPropertyValue(field.node->dataType, &expectValue, &value, size);
                RETURN_IFERR(ret);
            } else if (field.node->dataType == GMC_DATATYPE_FLOAT) {
                float value;
                ret = GmcGetVertexPropertyByName(stmt, field.node->name, &value, size, &isNull);
                RETURN_IFERR(ret);
                float expectValue = atof(field.value);
                ret = CompareVertexPropertyValue(field.node->dataType, &expectValue, &value, size);
                RETURN_IFERR(ret);
            } else {
                uint64_t value;
                ret = GmcGetVertexPropertyByName(stmt, field.node->name, &value, size, &isNull);
                RETURN_IFERR(ret);
                if (IsDigitStr(field.value)) {
                    uint64_t expectValue = atol(field.value);
                    ret = CompareVertexPropertyValue(field.node->dataType, &expectValue, &value, size);
                } else {
                    ret = CompareVertexPropertyValue(field.node->dataType, field.value, &value, size);
                }
                RETURN_IFERR(ret);
            }
        } else {
            // tree model
            GmcNodeT *root;
            int ret = GmcGetRootNode(stmt, &root);
            RETURN_IFERR(ret);
            ret = CheckTreeRecord(stmt, root, field.node, field.format, formatIndex);
            (void)GmcFreeNode(root);
            RETURN_IFERR(ret);
        }
    }
    return T_OK;
}

int TestSelVertexRecord(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *expect, int beginVal, int endVal, uint32_t versionId)
{
    if (expect == NULL) {
        return T_FAILED;
    }
    AW_FUN_Log(LOG_INFO, "[TestSelVertexRecord] tableName: %s indexName: %s cond: %s expect: %s range:(%d~%d)",
        tableName, ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), ((expect) ? expect : "null"), beginVal,
        endVal);
    bool isFinish = false;
    int fetchCount = 0;
    char genValue[MAX_GEN_DATA_LEN] = {0};
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_SCAN);
    RETURN_IFERR(ret);
    TFieldValueT expectField[MAX_FORMAT_FIELDS];
    int expectFieldCount = 0;
    ret = ParseFieldValue(expectField, MAX_FORMAT_FIELDS, expect, &expectFieldCount);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件扫描
        // cond 为NULL,全表扫描
        if (cond == NULL) {
            ret = GmcSetFilter(stmt, NULL);
            GOTO_TAG_IFERR(ret, FREE_TAG4);
            ret = GmcExecute(stmt);
            GOTO_TAG_IFERR(ret, FREE_TAG4);
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                GOTO_TAG_IFERR(ret, FREE_TAG4);
                if (isFinish) {
                    break;
                }
                ret = CheckVertexRecord(stmt, expectField, expectFieldCount, beginVal + fetchCount);
                GOTO_TAG_IFERR(ret, FREE_TAG4);
                fetchCount++;
            }
        } else {
            for (int i = beginVal; i < endVal; i++) {
                isFinish = false;
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG4);
                ret = GmcSetFilter(stmt, genValue);
                GOTO_TAG_IFERR(ret, FREE_TAG4);
                ret = GmcExecute(stmt);
                GOTO_TAG_IFERR(ret, FREE_TAG4);
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    GOTO_TAG_IFERR(ret, FREE_TAG4);
                    if (isFinish) {
                        break;
                    }
                    ret = CheckVertexRecord(stmt, expectField, expectFieldCount, i);
                    GOTO_TAG_IFERR(ret, FREE_TAG4);
                    fetchCount++;
                }
            }
        }
    } else {
        // 索引扫描
        TFieldValueT field[MAX_FORMAT_FIELDS];
        // 解析索引值
        int fieldCount = 0;
        ret = ParseFieldValue(field, MAX_FORMAT_FIELDS, cond, &fieldCount);
        GOTO_TAG_IFERR(ret, FREE_TAG4);
        for (int i = beginVal; i < endVal; i++) {
            isFinish = false;
            ret = GmcSetIndexKeyName(stmt, indexName);
            GOTO_TAG_IFERR(ret, FREE_TAG3);
            // 设置索引值
            for (int n = 0; n < fieldCount; n++) {
                ret = GenFormatDataByIndex(field[n].format, i, field[n].value, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG3);
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(field[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, field[n].value);
                GOTO_TAG_IFERR(ret, FREE_TAG3);
            }
            ret = GmcExecute(stmt);
            GOTO_TAG_IFERR(ret, FREE_TAG3);
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                GOTO_TAG_IFERR(ret, FREE_TAG3);
                if (isFinish) {
                    break;
                }
                ret = CheckVertexRecord(stmt, expectField, expectFieldCount, i);
                GOTO_TAG_IFERR(ret, FREE_TAG3);
                fetchCount++;
            }
        }
    FREE_TAG3:
        FreeFieldValue(field, fieldCount);
    }
FREE_TAG4:
    FreeFieldValue(expectField, expectFieldCount);
    return ret;
}

int SetTreeProperty(GmcStmtT *stmt, GmcNodeT *root, VertexNodeT *node, char *setValue)
{
    if (node->nodeType == TEST_FIELD) {
        int ret;
        int size = 0;
        ret = TransformValue(node->dataType, setValue, &size);
        RETURN_IFERR(ret);
        ret = GmcNodeSetPropertyByName(root, node->name, node->dataType, setValue, size);
        return ret;
    } else if (node->nodeType == T_RECORD || node->nodeType == T_ARRAY) {
        GmcNodeT *record;
        int ret = GmcNodeGetChild(root, node->name, &record);
        if (ret != T_OK) {
            (void)GmcFreeNode(root);
            return T_FAILED;
        }
        if (node->nodeType == T_RECORD) {
            ret = SetTreeProperty(stmt, record, node->next, setValue);
            (void)GmcFreeNode(record);
            return ret;
        } else {
            GmcNodeT *element;
            ret = GmcNodeGetElementByIndex(record, node->arrayIndex, &element);
            if (ret != T_OK) {
                (void)GmcFreeNode(record);
                return T_FAILED;
            }
            ret = SetTreeProperty(stmt, element, node->next, setValue);
            (void)GmcFreeNode(element);
            (void)GmcFreeNode(record);
            return ret;
        }
    }
    return T_OK;
}

int SetPropertyBaseFormat(GmcStmtT *stmt, TFieldValueT *field, int fieldCount, int formatIndex)
{
    int ret;
    for (int i = 0; i < fieldCount; i++) {
        ret = GenFormatDataByIndex(field[i].format, formatIndex, field[i].value, MAX_GEN_DATA_LEN);
        RETURN_IFERR(ret);
        if (field[i].node->nodeType == TEST_FIELD) {
            int size;
            ret = TransformValue(field[i].node->dataType, field[i].value, &size);
            RETURN_IFERR(ret);
            ret = GmcSetVertexProperty(stmt, field[i].node->name, field[i].node->dataType, field[i].value, size);
            RETURN_IFERR(ret);
        } else {
            GmcNodeT *root;
            int ret = GmcGetRootNode(stmt, &root);
            RETURN_IFERR(ret);
            ret = SetTreeProperty(stmt, root, field[i].node, field[i].value);
            (void)GmcFreeNode(root);
            RETURN_IFERR(ret);
        }
    }
    return T_OK;
}

int TestUpdVertexSync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *updateVal, int beginVal, int endVal, uint32_t versionId)
{
    if (updateVal == NULL) {
        return T_FAILED;
    }
    AW_FUN_Log(LOG_INFO, "[TestUpdVertexSync] tableName: %s indexName: %s cond: %s updateVal: %s range:(%d~%d)",
        tableName, ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), ((updateVal) ? updateVal : "null"),
        beginVal, endVal);
    char genValue[MAX_GEN_DATA_LEN] = {0};
    // 解析更新字段值表达式
    TFieldValueT updateField[MAX_FORMAT_FIELDS];
    int updateFieldCount = 0;
    int ret = ParseFieldValue(updateField, MAX_FORMAT_FIELDS, updateVal, &updateFieldCount);
    RETURN_IFERR(ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件更新
        // cond 为NULL，全表更新
        if (cond == NULL) {
            // 设置更新值
            ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, 0);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
            ret = GmcSetFilter(stmt, NULL);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
            ret = GmcExecute(stmt);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
        } else {
            for (int i = beginVal; i < endVal; i++) {
                // 设置更新值
                ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, i);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GmcSetFilter(stmt, genValue);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GmcExecute(stmt);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
            }
        }
    } else {
        // 索引更新
        // 解析索引字段值表达式
        TFieldValueT indexField[MAX_FORMAT_FIELDS];
        int indexFieldCount = 0;
        ret = ParseFieldValue(indexField, MAX_FORMAT_FIELDS, cond, &indexFieldCount);
        RETURN_IFERR(ret);
        for (int i = beginVal; i < endVal; i++) {
            ret = GmcSetIndexKeyName(stmt, indexName);
            GOTO_TAG_IFERR(ret, FREE_TAG1);
            // 生成并设置索引值
            for (int n = 0; n < indexFieldCount; n++) {
                ret = GenFormatDataByIndex(indexField[n].format, i, indexField[n].value, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG1);
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(indexField[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, indexField[n].value);
                GOTO_TAG_IFERR(ret, FREE_TAG1);
            }
            // 设置更新值
            ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, i);
            GOTO_TAG_IFERR(ret, FREE_TAG1);

            ret = GmcExecute(stmt);
            GOTO_TAG_IFERR(ret, FREE_TAG1);
        }
    FREE_TAG1:
        FreeFieldValue(indexField, indexFieldCount);
    }
FREE_TAG2:
    FreeFieldValue(updateField, updateFieldCount);
    return ret;
}

int TestUpdVertexAsync(GmcStmtT *stmt, const char *tableName, const char *indexName, const char *cond,
    const char *updateVal, int beginVal, int endVal, uint32_t versionId)
{
    if (updateVal == NULL) {
        return T_FAILED;
    }
    AsyncUserDataT data = { 0 };
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = OprVertexCallback;
    updateRequestCtx.userData = &data;
    AW_FUN_Log(LOG_INFO, "[TestUpdVertexAsync] tableName: %s indexName: %s cond: %s updateVal: %s range:(%d~%d)",
        tableName, ((indexName) ? indexName : "null"), ((cond) ? cond : "null"), ((updateVal) ? updateVal : "null"),
        beginVal, endVal);
    char genValue[MAX_GEN_DATA_LEN] = {0};
    // 解析更新字段值表达式
    TFieldValueT updateField[MAX_FORMAT_FIELDS];
    int updateFieldCount = 0;
    int ret = ParseFieldValue(updateField, MAX_FORMAT_FIELDS, updateVal, &updateFieldCount);
    RETURN_IFERR(ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tableName, versionId, GMC_OPERATION_UPDATE);
    RETURN_IFERR(ret);
    if (indexName == NULL) {
        // 条件更新
        // cond 为NULL，全表更新
        if (cond == NULL) {
            // 设置更新值
            ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, 0);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
            ret = GmcSetFilter(stmt, NULL);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
            ret = GmcExecute(stmt);
            GOTO_TAG_IFERR(ret, FREE_TAG2);
        } else {
            for (int i = beginVal; i < endVal; i++) {
                // 设置更新值
                ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, i);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GenFormatDataByIndex(cond, i, genValue, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GmcSetFilter(stmt, genValue);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
                ret = GmcExecuteAsync(stmt, &updateRequestCtx);
                GOTO_TAG_IFERR(ret, FREE_TAG2);
            }
        }
    } else {
        // 索引更新
        // 解析索引字段值表达式
        TFieldValueT indexField[MAX_FORMAT_FIELDS];
        int indexFieldCount = 0;
        ret = ParseFieldValue(indexField, MAX_FORMAT_FIELDS, cond, &indexFieldCount);
        RETURN_IFERR(ret);
        for (int i = beginVal; i < endVal; i++) {
            ret = GmcSetIndexKeyName(stmt, indexName);
            GOTO_TAG_IFERR(ret, FREE_TAG1);
            // 生成并设置索引值
            for (int n = 0; n < indexFieldCount; n++) {
                ret = GenFormatDataByIndex(indexField[n].format, i, indexField[n].value, MAX_GEN_DATA_LEN);
                GOTO_TAG_IFERR(ret, FREE_TAG1);
                GmcDataTypeE dataType = (GmcDataTypeE)GetFieldNodeDataType(indexField[n].node);
                ret = SetIndexKeyValueBaseType(stmt, n, dataType, indexField[n].value);
                GOTO_TAG_IFERR(ret, FREE_TAG1);
            }
            // 设置更新值
            ret = SetPropertyBaseFormat(stmt, updateField, updateFieldCount, i);
            GOTO_TAG_IFERR(ret, FREE_TAG1);

            ret = GmcExecuteAsync(stmt, &updateRequestCtx);
            GOTO_TAG_IFERR(ret, FREE_TAG1);
        }
    FREE_TAG1:
        FreeFieldValue(indexField, indexFieldCount);
    }
FREE_TAG2:
    FreeFieldValue(updateField, updateFieldCount);
    RETURN_IFERR(ret);
    int expectCount = 0;
    if (cond == NULL) {
        // 全表更新
        expectCount = 1;
    } else {
        expectCount = endVal - beginVal;
    }
    ret = testWaitAsyncRecv(&data, expectCount);
    RETURN_IFERR(ret);
    if (data.succNum != expectCount) {
        return T_FAILED;
    }
    return T_OK;
}

#endif  // PRODUCT_USG_TS FEATURE_STREAM

int ChangeGmserverCfg(char *item, char *value)
{
    char tempCmd[512] = {0};
    if (value) {
        snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh \"%s=%s\"", item, value);
    } else {
        snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh \"%s\"", item);
    }

    int ret = system(tempCmd);
    return WEXITSTATUS(ret);
}

int ChangeTsGmserverCfg(char *item, char *value)
{
    char tempCmd[512] = {0};
    if (value) {
        snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh -ts \"%s=%s\"", item, value);
    } else {
        snprintf(tempCmd, sizeof(tempCmd), "sh $TEST_HOME/tools/modifyCfg.sh -ts \"%s\"", item);
    }

    int ret = system(tempCmd);
    return WEXITSTATUS(ret);
}

/******************************************* 结构化 start *******************************************/
static inline uint32_t GetBasicDataTypeLength(GmcDataTypeE type)
{
    return G_TEST_BASIC_TYPE_SIZE[type].typeSize;
}

static int32_t ParseCmpBitDataType(const void *left, const void *right)
{
    return strcmp(((const TestDataTypePairT *)left)->strType, ((const TestDataTypePairT *)right)->strType);
}

static inline bool IsFixedType(GmcDataTypeE type)
{
    if (type == GMC_DATATYPE_STRING || type == GMC_DATATYPE_BYTES) {
        return false;
    } else {
        return true;
    }
}

static int ParseSchema(TestVertexLabelT *vertexLabel, const json_t *jsonSchema, TestSchemaT **schema, uint16_t level);

static int ParseBoolJson(const json_t *jsonRoot, const char *key, bool *value)
{
    json_t *jsonValue = json_object_get(jsonRoot, key);
    if (jsonValue == NULL) {
        return GMERR_OK;
    }
    json_type type = json_typeof(jsonValue);
    if (type == JSON_TRUE) {
        *value = true;
        return GMERR_OK;
    }
    if (type == JSON_FALSE) {
        *value = false;
        return GMERR_OK;
    }
    if (type == JSON_INTEGER) {
        json_int_t intVal = json_integer_value(jsonValue);
        if (intVal == 1) {
            *value = true;
            return GMERR_OK;
        }
        if (intVal == 0) {
            *value = false;
            return GMERR_OK;
        }
    }
    printf("The bool value should be true, false, 0 or 1.\n");
    return FAILED;
}

static int ParseUint32Json(const json_t *jsonRoot, const char *key, uint32_t *value, const bool isNull)
{
    json_t *jsonValue = json_object_get(jsonRoot, key);
    if (jsonValue == NULL) {
        if (isNull) {
            return GMERR_OK;
        }
        printf("%s is undefined.\n", key);
        return FAILED;
    }
    if (json_typeof(jsonValue) != JSON_INTEGER) {
        printf("Type of %s should be uint.\n", key);
        return FAILED;
    }
    json_int_t uint32Value = json_integer_value(jsonValue);
    *value = (uint32_t)uint32Value;
    return GMERR_OK;
}

static int ParseVarLenString(const json_t *jsonField, const char *key, char **valueStr, uint32_t *valueLen, bool isNull)
{
    json_t *jsonValue = json_object_get(jsonField, key);
    if (jsonValue == NULL) {
        if (isNull) {
            *valueLen = 0;
            *valueStr = NULL;
            return GMERR_OK;
        }
        return FAILED;
    }
    char *stringValue = (char *)json_string_value(jsonValue);
    if (stringValue == NULL) {
        printf("Type of %s should be string.\n", key);
        return FAILED;
    }
    uint32_t stringLen = (uint32_t)(strlen(stringValue) + 1);
    if (!isNull && stringLen - 1 == 0) {
        printf("The length of %s should be greater than 0.\n", key);
        return FAILED;
    }
    *valueStr = (char *)malloc(stringLen);
    if (*valueStr == NULL) {
        printf("%s alloc mem failed.\n", *valueStr);
        return FAILED;
    }

    errno_t err = strncpy_s(*valueStr, stringLen, stringValue, stringLen - 1);
    if (err != EOK) {
        if (*valueStr != NULL) {
            free(*valueStr);
        }
        printf("Fail to copy string %s when parse varlen string %s.\n", stringValue, key);
        return FAILED;
    }
    *valueLen = stringLen;
    return GMERR_OK;
}

static int ParseSingleVertexLabelName(TestVertexLabelT *vertexLabel, const json_t *jsonField)
{
    return ParseVarLenString(jsonField, "name", &vertexLabel->topRecordName, &vertexLabel->topRecordNameLen, false);
}

static int ParseGetFieldType(const json_t *jsonField, char **type)
{
    json_t *jsonType = json_object_get(jsonField, "type");
    if (jsonType == NULL) {
        printf("type is undefined.\n");
        return FAILED;
    }
    *type = (char *)json_string_value(jsonType);
    if (*type == NULL) {
        printf("Type of \"type\" should be string.\n");
        return FAILED;
    }
    if (strlen(*type) == 0) {
        printf("\"type\" cannot be \"\".\n");
        return FAILED;
    }
    return GMERR_OK;
}

static int SetNodeSchemaSize(const json_t *jsonNodeSchema, TestNodeSchemaT *node)
{
    if (node->nodeType == GMC_NODE_RECORD) {
        return GMERR_OK;
    }
    uint32_t size = 0;
    int ret = ParseUint32Json(jsonNodeSchema, "size", &size, true);
    RETURN_IFERR(ret);

    if (node->nodeType == GMC_NODE_VECTOR) {
        node->vectorInitSize = VECTOR_ARRAY_INIT_SIZE;
        node->vectorExtendSize = VECTOR_EXTEND_SIZE;
        if (size == 0) {
            node->vectorMaxSize = VECTOR_MAX_SIZE;
        } else {
            node->vectorMaxSize = size;
        }
    } else {
        if (size == 0) {
            node->arraySize = VECTOR_ARRAY_INIT_SIZE;
        } else {
            node->arraySize = size;
        }
    }
    return GMERR_OK;
}

static int ParseNodeSchemaConstraints(TestNodeSchemaT *node, const json_t *jsonNodeSchema)
{
    bool isVector = false;
    bool isArray = false;
    bool isFixedArray = false;
    node->nodeType = GMC_NODE_RECORD;

    int ret = ParseBoolJson(jsonNodeSchema, "vector", &isVector);
    RETURN_IFERR(ret);
    if (isVector) {
        node->nodeType = GMC_NODE_VECTOR;
    }

    ret = ParseBoolJson(jsonNodeSchema, "array", &isArray);
    RETURN_IFERR(ret);
    if (isArray) {
        node->nodeType = GMC_NODE_VECTOR;
    }

    ret = ParseBoolJson(jsonNodeSchema, "fixed_array", &isFixedArray);
    RETURN_IFERR(ret);
    if (isFixedArray) {
        node->nodeType = GMC_NODE_ARRAY;
    }

    return SetNodeSchemaSize(jsonNodeSchema, node);
}

static int ParseSetPropertyBitDataType(char *type, TestPropertySchemaT *property)
{
    char *nextToken = NULL;
    char *bitSize = strchr(type, ':');
    if (bitSize == NULL) {
        printf("Fail to get bitSize by delimiter when set property bit date type.\n");
        return FAILED;
    }
    property->size = (uint32_t)atoi(bitSize + 1);

    char *bitType = strtok_s(type, ":", &nextToken);
    if (bitType == NULL) {
        printf("Fail to get bitSize by delimiter when set property bit date type.\n");
        return FAILED;
    }
    TestDataTypePairT tmp = {.strType = (const char *)bitType, .dataType = GMC_DATATYPE_NULL};
    TestDataTypePairT *find = (TestDataTypePairT *)bsearch(&tmp, g_testBitDataType,
        sizeof(g_testBitDataType) / sizeof(TestDataTypePairT), sizeof(TestDataTypePairT), ParseCmpBitDataType);
    if (find == NULL) {
        printf("The type of %s is invalid.\n", property->name);
        return FAILED;
    }
    property->dataType = find->dataType;
    property->isFixed = IsFixedType(property->dataType);
    return GMERR_OK;
}

static int ParseSetPropertySize(TestVertexLabelT *vertexLabel, TestPropertySchemaT *property, const json_t *jsonField)
{
    int ret;
    switch (property->dataType) {
        case GMC_DATATYPE_FIXED:
        case GMC_DATATYPE_BITMAP: {
            ret = ParseUint32Json(jsonField, "size", &property->size, false);
            RETURN_IFERR(ret);
            break;
        }
        case GMC_DATATYPE_STRING:
        case GMC_DATATYPE_BYTES: {
            if (vertexLabel->vertexType != TREE_VERTEX) {
                vertexLabel->vertexType = FLAT_VERTEX;
            }
            property->size = MAX_VARIABLE_TYPE_LENGTH;
            ret = ParseUint32Json(jsonField, "size", &property->size, true);
            RETURN_IFERR(ret);
            if (property->dataType == GMC_DATATYPE_STRING) {
                property->size = property->size + 1;
            }
            break;
        }
        case GMC_DATATYPE_ENUM:
        case GMC_DATATYPE_IDENTITY: {
            property->size = sizeof(int32_t);
            break;
        }
        default: {
            property->size = GetBasicDataTypeLength(property->dataType);
            break;
        }
    }
    return GMERR_OK;
}

static int ParseSetPropertyDataType(
    const char *type, TestVertexLabelT *vertexLabel, TestPropertySchemaT *property, const json_t *jsonField)
{
    TestDataTypePairT tmp = {.strType = type, .dataType = GMC_DATATYPE_NULL};
    TestDataTypePairT *find = (TestDataTypePairT *)bsearch(&tmp, g_testDataType,
        sizeof(g_testDataType) / sizeof(TestDataTypePairT), sizeof(TestDataTypePairT), ParseCmpBitDataType);
    if (find == NULL) {
        printf("The type of %s is invalid.\n", property->name);
        return FAILED;
    }
    property->dataType = find->dataType;
    property->isFixed = IsFixedType(property->dataType);

    return ParseSetPropertySize(vertexLabel, property, jsonField);
}

static int ParseFieldToPropertyInner(const json_t *jsonField, const TestSchemaT *schema, TestPropertySchemaT *property)
{
    property->isResource = (property->dataType == GMC_DATATYPE_RESOURCE);

    bool nullableDefVal = (g_parseSchemaCtx.compatibleV3Value == 0) && (!property->isResource) &&
                          (property->dataType != GMC_DATATYPE_PARTITION);
    property->isNullable = nullableDefVal;
    return ParseBoolJson(jsonField, "nullable", &property->isNullable);
}

static inline bool IsBitFieldType(GmcDataTypeE type)
{
    if (GMC_DATATYPE_BITFIELD8 <= type && type <= GMC_DATATYPE_BITFIELD64) {
        return true;
    } else {
        return false;
    }
}

static int SetPropertybitfieldOffset(TestPropertySchemaT *property, uint8_t *bitOffset)
{
    uint32_t dataTypeSize = 0;
    switch (property->dataType) {
        case GMC_DATATYPE_BITFIELD8:
            dataTypeSize = UINT8_SIZE;
            break;
        case GMC_DATATYPE_BITFIELD16:
            dataTypeSize = UINT16_SIZE;
            break;
        case GMC_DATATYPE_BITFIELD32:
            dataTypeSize = UINT32_SIZE;
            break;
        case GMC_DATATYPE_BITFIELD64:
            dataTypeSize = UINT64_SIZE;
            break;
        default:
            break;
    }
    if (*bitOffset + property->size <= dataTypeSize) {
        property->bitfieldOffset = *bitOffset;
        *bitOffset += (uint8_t)property->size;
    } else {
        property->bitfieldOffset = 0;
        *bitOffset = (uint8_t)property->size;
    }
    return GMERR_OK;
}

static int ParseFieldToNode(
    TestVertexLabelT *vertexLabel, const json_t *jsonNodeSchema, TestSchemaT *schema, uint32_t index)
{
    TestNodeSchemaT *node = &schema->nodes[schema->nodeNum];
    schema->nodeNum++;
    int ret = ParseVarLenString(jsonNodeSchema, "name", &node->name, &node->nameLen, false);
    RETURN_IFERR(ret);

    ret = ParseNodeSchemaConstraints(node, jsonNodeSchema);
    RETURN_IFERR(ret);

    node->id = index;
    return ParseSchema(vertexLabel, jsonNodeSchema, &node->schema, schema->level + 1);
}

static int ParseFieldToProperty(
    TestVertexLabelT *vertexLabel, const json_t *jsonField, char *type, TestSchemaT *schema, uint32_t index)
{
    TestPropertySchemaT *property = &schema->properties[index];
    int ret = ParseVarLenString(jsonField, "name", &property->name, (uint32_t *)&property->nameLen, false);
    RETURN_IFERR(ret);
    property->isFixed = true;
    if (!property->isValid) {
        property->isNullable = true;
        return ParseFieldToNode(vertexLabel, jsonField, schema, index);
    }
    if (strchr(type, ':') != NULL) {
        ret = ParseSetPropertyBitDataType(type, property);
    } else {
        ret = ParseSetPropertyDataType(type, vertexLabel, property, jsonField);
    }
    RETURN_IFERR(ret);

    return ParseFieldToPropertyInner(jsonField, schema, property);
}

static int ParseFieldsInner(TestVertexLabelT *vertexLabel, const json_t *jsonFields, TestSchemaT *schema)
{
    int ret;
    uint8_t bitOffset = 0;
    GmcDataTypeE priorType = GMC_DATATYPE_NULL;
    json_t *jsonField = NULL;
    char *type = NULL;
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        jsonField = json_array_get(jsonFields, i);
        ret = ParseGetFieldType(jsonField, &type);
        RETURN_IFERR(ret);
        ret = ParseFieldToProperty(vertexLabel, jsonField, type, schema, i);
        RETURN_IFERR(ret);
        TestPropertySchemaT *property = &schema->properties[i];
        if (IsBitFieldType(property->dataType)) {
            if (property->dataType != priorType) {
                bitOffset = 0;
                priorType = property->dataType;
            }
            ret = SetPropertybitfieldOffset(property, &bitOffset);
            RETURN_IFERR(ret);
        } else {
            priorType = GMC_DATATYPE_NULL;
        }
    }
    return GMERR_OK;
}

static int ParseFields(TestVertexLabelT *vertexLabel, const json_t *jsonFields, TestSchemaT *schema)
{
    schema->propeNum = (uint32_t)json_array_size(jsonFields);
    uint32_t propeArrSize = (uint32_t)((schema->propeNum) * sizeof(TestPropertySchemaT));
    schema->properties = (TestPropertySchemaT *)malloc(propeArrSize);
    if (schema->properties == NULL) {
        printf("schema->properties alloc mem failed.\n");
        return FAILED;
    }
    memset(schema->properties, 0, propeArrSize);
    bool isFlat = true;
    json_t *jsonField = NULL;
    char *type = NULL;
    int ret;
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        jsonField = json_array_get(jsonFields, i);
        ret = ParseGetFieldType(jsonField, &type);
        RETURN_IFERR(ret);
        schema->properties[i].propeId = i;
        if (strcmp(type, "record") != 0 && strcmp(type, "container") != 0 && strcmp(type, "choice") != 0 &&
            strcmp(type, "case") != 0) {
            schema->properties[i].isValid = true;
        } else {
            isFlat = false;
            schema->properties[i].isValid = false;
            schema->nodeNum++;
        }
    }
    schema->isFlat = isFlat;
    if (schema->nodeNum != 0) {
        uint32_t size = (uint32_t)(schema->nodeNum * sizeof(TestNodeSchemaT));
        schema->nodes = (TestNodeSchemaT *)malloc(size);
        if (schema->nodes == NULL) {
            printf("schema->nodes alloc mem failed.\n");
            return FAILED;
        }
        memset(schema->nodes, 0, size);
        vertexLabel->vertexType = TREE_VERTEX;
        schema->nodeNum = 0;
    }

    return ParseFieldsInner(vertexLabel, jsonFields, schema);
}

static int ParseSchema(TestVertexLabelT *vertexLabel, const json_t *jsonSchema, TestSchemaT **schema, uint16_t level)
{
    json_t *jsonFields = json_object_get(jsonSchema, "fields");
    if (jsonFields == NULL) {
        printf("fields is undefined.\n");
        return FAILED;
    }
    *schema = (TestSchemaT *)malloc((uint32_t)sizeof(TestSchemaT));
    if (*schema == NULL) {
        printf("*schema alloc mem failed.\n");
        return FAILED;
    }
    memset(*schema, 0, (uint32_t)sizeof(TestSchemaT));
    (*schema)->level = level;
    return ParseFields(vertexLabel, jsonFields, *schema);
}

static int ExtractIndexLabelConstraints(const json_t *jsonIndexLabel, TestIndexLabelT *indexLabel, bool *isNull)
{
    bool isUnique = false;

    json_t *jsonConstraints = json_object_get(jsonIndexLabel, "constraints");
    if (jsonConstraints == NULL) {
        *isNull = true;
        indexLabel->indexConstraint = TEST_NON_UNIQUE;
        return GMERR_OK;
    }
    int ret = ParseBoolJson(jsonConstraints, "unique", &isUnique);
    RETURN_IFERR(ret);

    indexLabel->indexConstraint = isUnique ? TEST_UNIQUE : TEST_NON_UNIQUE;
    return GMERR_OK;
}

static void SetHashType(TestIndexLabelT *indexLabel)
{
    indexLabel->indexType = (indexLabel->isLabelLatchMode) ? TEST_CHAINED_HASH_INDEX : TEST_HASH_INDEX;
}

static void ExtractSecIndexHashCluster(TestIndexLabelT *indexLabel)
{
    indexLabel->indexType = TEST_ART_INDEX_HASHCLUSTER;
    if (indexLabel->indexConstraint == TEST_UNIQUE) {
        indexLabel->indexType = (indexLabel->isLabelLatchMode) ? TEST_CHAINED_HASH_INDEX : TEST_HASH_INDEX;
    }
}

#if defined FEATURE_HAC
static Status QryExtractSecIndexMultiHash(TestIndexLabelT *indexLabel)
{
    if (indexLabel->indexConstraint == TEST_NON_UNIQUE) {
        indexLabel->indexType = TEST_MULTI_HASH_INDEX;
    } else {
        indexLabel->indexType = TEST_HAC_HASH_INDEX; // 唯一索引时退化到HAC_HASH
    }
    return GMERR_OK;
}
#endif

static int ExtractSecIndexLabelTypeInner(const char *str, TestIndexLabelT *indexLabel)
{
    if (strcmp("local", str) == 0) {
        if (g_parseSchemaCtx.compatibleV3Value == 1) {
            indexLabel->indexConstraint = TEST_NON_UNIQUE;
        }
        indexLabel->indexType = TEST_ART_INDEX_LOCAL;
    } else if (strcmp("hashcluster", str) == 0) {
        if (g_parseSchemaCtx.compatibleV3Value == 1) {
            indexLabel->indexConstraint = TEST_NON_UNIQUE;
        }
        ExtractSecIndexHashCluster(indexLabel);
    } else if (strcmp("lpm4_tree_bitmap", str) == 0) {
        indexLabel->indexType = TEST_LPM4_INDEX;
    } else if (strcmp("lpm6_tree_bitmap", str) == 0) {
        indexLabel->indexType = TEST_LPM6_INDEX;
    } else if (strcmp("localhash", str) == 0) {
        if (indexLabel->indexConstraint == TEST_NON_UNIQUE) {
            indexLabel->indexType = TEST_HASH_LINKLIST_INDEX;
        } else {
            SetHashType(indexLabel);
        }
    } else if (strcmp("none", str) == 0) {
        indexLabel->indexType = TEST_NODE_MEMBER_INDEX;
    } 
#if defined FEATURE_HAC 
    else if (strcmp("multi_hash", str) == 0) {  // 硬件卸载的二级索引
        return QryExtractSecIndexMultiHash(indexLabel); 
    }
#endif
    else {
        printf("Type of index %s is %s, which is invalid.\n", indexLabel->indexName, str);
        return FAILED;
    }
    return GMERR_OK;
}

static int ExtractIndexLabelTypeInner(const char *str, TestIndexLabelT *indexLabel, bool constraintIsNull)
{
    int ret;
    if (strcmp("primary", str) == 0) {
        indexLabel->indexConstraint = TEST_PRIMARY;
        SetHashType(indexLabel);
        indexLabel->isNullable = false;
    } else {
        ret = ExtractSecIndexLabelTypeInner(str, indexLabel);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

static int ExtractIndexLabelType(const json_t *jsonIndexLabel, TestIndexLabelT *indexLabel, bool constraintIsNull)
{
    json_t *index = json_object_get(jsonIndexLabel, "index");
    if (index == NULL) {
        indexLabel->indexType = TEST_NODE_MEMBER_INDEX;
        return GMERR_OK;
    }
    json_t *indexType = json_object_get(index, "type");
    if (indexType == NULL) {
        printf("Type of index %s is undefined.\n", indexLabel->indexName);
        return FAILED;
    }
    const char *str = json_string_value(indexType);
    if (str == NULL) {
        printf("Type of index %s should be string.\n", indexLabel->indexName);
        return FAILED;
    }
    return ExtractIndexLabelTypeInner(str, indexLabel, constraintIsNull);
}

static int ParseIndexLabelBasicInfo(TestVertexLabelT *vertexLabel, const json_t *jsonIndexLabel, TestIndexLabelT *index)
{
    int ret = ParseVarLenString(jsonIndexLabel, "node", &index->sourceLabelName, &index->sourceLabelNameLen, true);
    RETURN_IFERR(ret);

    ret = ParseVarLenString(jsonIndexLabel, "name", &index->indexName, &index->indexNameLen, false);
    RETURN_IFERR(ret);

    bool constraintIsNull = false;
    ret = ExtractIndexLabelConstraints(jsonIndexLabel, index, &constraintIsNull);
    RETURN_IFERR(ret);
    index->isLabelLatchMode = false;  // 默认为非大表锁模式，后面读取表级别配置项后，再判断是否需要重新设置索引类型
    ret = ExtractIndexLabelType(jsonIndexLabel, index, constraintIsNull);
    RETURN_IFERR(ret);

    if (index->indexType == TEST_HASHCLUSTER_INDEX) {
        index->hcIndexId = vertexLabel->hcIndexNum;
        vertexLabel->hcIndexNum++;
    }
    return GMERR_OK;
}

static int SchemaGetPropeIdByName(const TestSchemaT *schema, const char *propeName, uint32_t *idx)
{
    for (uint32_t i = 0; i < schema->propeNum; i++) {
        if (schema->properties[i].isValid && strcmp(schema->properties[i].name, propeName) == 0) {
            *idx = i;
            return GMERR_OK;
        }
    }
    printf("can not find properties.\n");
    return FAILED;
}

static int SchemaGetPropeByName(TestSchemaT *schema, const char *propeName, TestPropertySchemaT **property)
{
    uint32_t idx;
    int ret = SchemaGetPropeIdByName(schema, propeName, &idx);
    RETURN_IFERR(ret);
    *property = &schema->properties[idx];  // SchemaGetPropeIdByName会保证idx是合法的
    return GMERR_OK;
}

static int DeepCopyTestPropertySchemaT(TestPropertySchemaT *dst, const TestPropertySchemaT *src)
{
    errno_t rc = memcpy_s(dst, sizeof(TestPropertySchemaT), src, sizeof(TestPropertySchemaT));
    if (rc != EOK) {
        printf("memcpy_s failed.\n");
        return FAILED;
    }
    size_t len = strlen(src->name) + 1;
    dst->name = (char *)malloc(len);
    if (dst->name == NULL) {
        printf("dst->name malloc failed.\n");
        return FAILED;
    }
    rc = strcpy_s(dst->name, len, src->name);
    if (rc != EOK) {
        free(dst->name);
        dst->name = NULL;
        printf("dst->name strcpy_s failed.\n");
        return FAILED;
    }
    return GMERR_OK;
}

static int ExtractIndexProperty(TestIndexLabelT *indexLabel, TestPropertySchemaT *property, uint32_t propertyIdx)
{
    if (indexLabel->indexType == TEST_LPM4_INDEX || indexLabel->indexType == TEST_LPM6_INDEX ||
        indexLabel->indexConstraint == TEST_PRIMARY) {
        property->isNullable = false;
    }
    if (property->isNullable) {
        indexLabel->isNullable = true;
    }
    if (IsFixedType(property->dataType)) {
        indexLabel->fixedPropeNum++;
    }
    return DeepCopyTestPropertySchemaT(&indexLabel->properties[propertyIdx], property);
}

static int ExtractIndexPropertiesInner(
    const json_t *jsonFields, TestSchemaT *schema, TestVertexLabelT *vertexLabel, TestIndexLabelT *indexLabel)
{
    int ret;
    for (uint32_t i = 0; i < indexLabel->propeNum; i++) {
        json_t *jsonField = json_array_get(jsonFields, i);
        const char *propeName = json_string_value(jsonField);
        if (propeName == NULL) {
            printf("Type of index property name should be string.\n");
            return FAILED;
        }
        TestPropertySchemaT *property = NULL;
        ret = SchemaGetPropeByName(schema, propeName, &property);
        RETURN_IFERR(ret);
        ret = ExtractIndexProperty(indexLabel, property, i);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

static int ExtractIndexProperties(
    TestVertexLabelT *vertexLabel, const json_t *jsonIndexLabel, TestSchemaT *schema, TestIndexLabelT *indexLabel)
{
    json_t *jsonFields = json_object_get(jsonIndexLabel, "fields");
    if (jsonFields == NULL) {
        printf("fields is undefined.\n");
    }
    size_t propeNum = json_array_size(jsonFields);
    indexLabel->propeNum = (uint32_t)propeNum;
    uint32_t propeArrSize = (uint32_t)(propeNum * sizeof(TestPropertySchemaT));
    indexLabel->properties = (TestPropertySchemaT *)malloc(propeArrSize);
    if (indexLabel->properties == NULL) {
        printf("indexLabel->properties alloc mem failed.\n");
        return FAILED;
    }
    memset(indexLabel->properties, 0, propeArrSize);
    int ret = ExtractIndexPropertiesInner(jsonFields, schema, vertexLabel, indexLabel);
    RETURN_IFERR_AND_FREE(ret, indexLabel->properties);
    return ret;
}

static int CopyIndex(
    TestVertexLabelT *vertexLabel, const uint32_t indexNum, TestIndexLabelT *index, TestNodeSchemaT *node)
{
    if (index->indexType == TEST_NODE_MEMBER_INDEX) {
        printf("Currently, framework not support memberKey.\n");
        return FAILED;
    }
    if (index->indexConstraint == TEST_PRIMARY) {
        TestIndexLabelT *pkIndex = vertexLabel->pkIndex;
        errno_t rc = memcpy_s(pkIndex, sizeof(TestIndexLabelT), index, sizeof(TestIndexLabelT));
        if (rc != EOK) {
            printf("Fail to copy pk index.\n");
            return FAILED;
        }
        vertexLabel->pkIndex->indexConstraint = TEST_PRIMARY;
        return GMERR_OK;
    }
    TestIndexLabelT *secIndex = &vertexLabel->secIndexes[vertexLabel->secIndexNum++];
    errno_t rc = memcpy_s(secIndex, sizeof(TestIndexLabelT), index, sizeof(TestIndexLabelT));
    if (rc != EOK) {
        printf("Fail to copy second index.\n");
        return FAILED;
    }
    return GMERR_OK;
}

static uint32_t GetMaxKeyBufLen(const TestIndexLabelT *indexLabel)
{
    uint32_t keyLen = 0;
    uint32_t keyPropeNum = indexLabel->propeNum;
    for (uint32_t i = 0; i < keyPropeNum; i++) {
        if (IsFixedType(indexLabel->properties[i].dataType)) {
            // 不支持在 bitfield 和 bitmap 类型字段上建立索引
            keyLen += indexLabel->properties[i].size;
        } else {
            // key中的len都没有进行压缩
            keyLen += (indexLabel->properties[i].size + (uint32_t)sizeof(uint32_t));
        }
    }
    keyLen += CALC_ALIGN_SIZE(keyPropeNum, BYTE_LENGTH) / BYTE_LENGTH;
    return keyLen;
}

static int ParseIndexLabel(TestVertexLabelT *vertexLabel, const json_t *jsonIndexLabel, uint32_t indexNum)
{
    TestIndexLabelT *index = (TestIndexLabelT *)malloc(sizeof(TestIndexLabelT));
    if (index == NULL) {
        printf("index alloc mem failed.\n");
        return FAILED;
    }
    memset(index, 0, (uint32_t)sizeof(TestIndexLabelT));
    int ret = ParseIndexLabelBasicInfo(vertexLabel, jsonIndexLabel, index);
    RETURN_IFERR_AND_FREE(ret, index);

    // Currently, indexing is only supported on the first layer property
    TestNodeSchemaT *node = NULL;
    TestSchemaT *schema = vertexLabel->schema;
    if (index->indexType == TEST_NODE_MEMBER_INDEX) {
        // memberkey暂无诉求, 不解析
        if (index != NULL) {
            if (index->sourceLabelName != NULL) {
                free(index->sourceLabelName);
            }
            if (index->indexName != NULL) {
                free(index->indexName);
            }
            free(index);
        }
        return ret;
    }

    ret = ExtractIndexProperties(vertexLabel, jsonIndexLabel, schema, index);
    RETURN_IFERR_AND_FREE(ret, index);

    index->maxKeyLen = GetMaxKeyBufLen(index);
    ret = CopyIndex(vertexLabel, indexNum, index, node);
    RETURN_IFERR_AND_FREE(ret, index);
    free(index);
    return ret;
}

static int ParseVertexIndexLabel(TestVertexLabelT *vertexLabel, const json_t *jsonRootLabel)
{
    json_t *jsonIndexes = json_object_get(jsonRootLabel, "keys");
    if (jsonIndexes == NULL) {
        return GMERR_OK;
    }
    size_t indexNum = json_array_size(jsonIndexes);
    uint32_t size = (uint32_t)(indexNum * sizeof(TestIndexLabelT));
    vertexLabel->secIndexes = (TestIndexLabelT *)malloc(size);
    if (vertexLabel->secIndexes == NULL) {
        printf("vertexLabel->secIndexes alloc mem failed.\n");
        return FAILED;
    }
    memset(vertexLabel->secIndexes, 0, size);
    vertexLabel->pkIndex = (TestIndexLabelT *)malloc(sizeof(TestIndexLabelT));
    if (vertexLabel->pkIndex == NULL) {
        printf("vertexLabel->pkIndex alloc mem failed.\n");
        return FAILED;
    }
    memset(vertexLabel->pkIndex, 0, sizeof(TestIndexLabelT));
    for (uint32_t i = 0; i < indexNum; i++) {
        json_t *jsonIndex = json_array_get(jsonIndexes, i);
        int ret = ParseIndexLabel(vertexLabel, jsonIndex, (uint32_t)indexNum);
        RETURN_IFERR(ret);
    }
    return GMERR_OK;
}

static int ParseSingleVertexLabelJson(TestVertexLabelT *vertexLabel, const json_t *jsonField)
{
    char *type = NULL;
    uint32_t typeLen;
    int ret = ParseVarLenString(jsonField, "type", &type, &typeLen, false);
    if (ret != GMERR_OK) {
        // 订阅json可能没有"type", 表定义的gmjson一定有"type"
        return T_FAILED2;
    }
    if (strcmp(type, "record") != 0) {
        // 非vertex表的gmjson不解析, 直接返回
        free(type);
        return T_FAILED2;
    }
    free(type);

    // 解析表名
    ret = ParseVarLenString(jsonField, "name", &vertexLabel->topRecordName, &vertexLabel->topRecordNameLen, false);
    RETURN_IFERR(ret);

    // 解析版本号
    ret = ParseUint32Json(jsonField, "schema_version", &vertexLabel->schemaVersion, true);
    RETURN_IFERR(ret);

    ret = ParseSchema(vertexLabel, jsonField, &(vertexLabel->schema), 1);
    RETURN_IFERR(ret);

    return ParseVertexIndexLabel(vertexLabel, jsonField);
}

static int ParseVertexLabelJson(TestVertexLabelT *vertexLabel, const char *labelJson)
{
    json_error_t jsonErrorLabel;
    int ret;

    json_t *jsonRootLabel = json_loads(labelJson, JSON_REJECT_DUPLICATES, &jsonErrorLabel);
    if (jsonRootLabel == NULL) {
        return FAILED;
    }
    if (json_is_object(jsonRootLabel)) {
        ret = ParseSingleVertexLabelJson(vertexLabel, jsonRootLabel);
        json_delete(jsonRootLabel);
        return ret;
    }
    uint32_t labelNum = (uint32_t)json_array_size(jsonRootLabel);
    if (labelNum == 0) {
        json_delete(jsonRootLabel);
        printf("Vertex label array num should not be zero.\n");
        return FAILED;
    }
    json_t *jsonField = NULL;
    for (uint32_t i = 0; i < labelNum; i++) {
        if(i > 0) {
            // 暂时只处理json中第一个表, 后续有诉求再进一步处理
            json_delete(jsonRootLabel);
            return T_FAILED2;
        }
        jsonField = json_array_get(jsonRootLabel, i);
        ret = ParseSingleVertexLabelJson(vertexLabel, jsonField);
        if (ret != GMERR_OK) {
            json_delete(jsonRootLabel);
            return ret;
        }
    }
    json_delete(jsonRootLabel);
    return ret;
}

static void ExtendDynMemOfArray(TestArrayT *c)
{
    if (c->memSize < c->memMax) {
        return;
    }
    uint32_t newSize;
    if (c->memMax == 0) {
        newSize = INIT_DYN_MEM_SIZE;
    } else {
        newSize = c->memMax * DYN_MEM_GROW_MULTIPLE;
    }
    void **newAddr = (void **)malloc(sizeof(void *) * newSize);
    if (newAddr == NULL) {
        OutOfMemoryPanicRaise("ExtendDynArray(from %u to %u) failed.\n", c->memMax, newSize);
    }
    memset(newAddr + c->memMax, 0, sizeof(void *) * c->memMax);
    if (c->memMax > 0) {
        memcpy(newAddr, c->memAddr, sizeof(void *) * c->memMax);
        free(c->memAddr);
    }
    c->memAddr = newAddr;
    c->memMax = newSize;
}

void FreeDynMemOfSchema(TestSchemaT *schema)
{
    if (schema == NULL) {
        return;
    }
    uint32_t propeNum = schema->propeNum;
    for (uint32_t i = 0; i < schema->nodeNum; i++) {
        if (schema->nodes[i].name != NULL) {
            free(schema->nodes[i].name);
            schema->nodes[i].name = NULL;
        }
        if (schema->nodes[i].schema != NULL) {
            FreeDynMemOfSchema(schema->nodes[i].schema);
            free(schema->nodes[i].schema);
            schema->nodes[i].schema = NULL;
        }
    }
    if (schema->nodes != NULL) {
        free(schema->nodes);
        schema->nodes = NULL;
    }
    for (uint32_t i = 0; i < propeNum; i++) {
        if (schema->properties[i].name != NULL) {
            free(schema->properties[i].name);
            schema->properties[i].name = NULL;
        }
    }
    if (schema->properties != NULL) {
        free(schema->properties);
        schema->properties = NULL;
    }
}

static void FreeDynMemOfindex(TestIndexLabelT **testIndex, uint32_t indexNum)
{
    if (*testIndex == NULL) {
        return;
    }
    for (uint32_t i = 0; i < indexNum; i++) {
        TestIndexLabelT *index = &((*testIndex)[i]);
        if (index != NULL) {
            if (index->indexName != NULL) {
                free(index->indexName);
                index->indexName = NULL;
            }
            if (index->sourceLabelName != NULL) {
                free(index->sourceLabelName);
                index->sourceLabelName = NULL;
            }
            for (uint32_t j = 0; j < index->propeNum; j++) {
                if (index->properties[j].name != NULL) {
                    free(index->properties[j].name);
                    index->properties[j].name = NULL;
                }
            }
            if (index->properties != NULL) {
                free(index->properties);
                index->properties = NULL;
            }
        }
    }
    free(*testIndex);
    *testIndex = NULL;
}

void FreeDynMemOfVertexLabel(TestVertexLabelT **testVertexLabel)
{
    TestVertexLabelT *vertexLabel = *testVertexLabel;
    if (vertexLabel == NULL) {
        return;
    }
    if (vertexLabel->topRecordName != NULL) {
        free(vertexLabel->topRecordName);
        vertexLabel->topRecordName = NULL;
    }
    if (vertexLabel->schema != NULL) {
        FreeDynMemOfSchema(vertexLabel->schema);
        free(vertexLabel->schema);
        vertexLabel->schema = NULL;
    }
    if (vertexLabel->pkIndex != NULL) {
        FreeDynMemOfindex(&vertexLabel->pkIndex, 1);
    }
    if (vertexLabel->secIndexes != NULL) {
        FreeDynMemOfindex(&vertexLabel->secIndexes, vertexLabel->secIndexNum);
    }
    free(vertexLabel);
    vertexLabel = NULL;
}

void FreeDynMemOfVertexLabelArrays(TestArrayT *c)
{
    if (c->memAddr == NULL) {
        c->memMax = 0;
        c->memSize = 0;
        return;
    }
    for (uint32_t i = 0; i < c->memSize; i++) {
        TestVertexLabelT *vertexLabel = (TestVertexLabelT *)(c->memAddr[i]);
        FreeDynMemOfVertexLabel(&vertexLabel);
        c->memAddr[i] = NULL;
    }
    c->memSize = 0;
    free(c->memAddr);
    c->memAddr = NULL;
    c->memMax = 0;
}

int TestGetVertexLabelFromSchema(char *schema, const char *nsName)
{
    TestVertexLabelT *vertexLabel = (TestVertexLabelT *)malloc(sizeof(TestVertexLabelT));
    if (vertexLabel == NULL) {
        printf("vertexLabel alloc mem failed.\n");
        return FAILED;
    }
    memset(vertexLabel, 0, (uint32_t)sizeof(TestVertexLabelT));
    vertexLabel->nsName = (char *)nsName;
    int ret = ParseVertexLabelJson(vertexLabel, schema);
    if (ret != GMERR_OK) {
        FreeDynMemOfVertexLabel(&vertexLabel);
        ret = (ret == T_FAILED2 ? GMERR_OK : ret);
        return ret;
    }

    pthread_mutex_lock(&g_parseSchemaCtx.threadLock);
    uint32_t i, pos = g_parseSchemaCtx.array.memSize;
    bool isHit = false;
    for (i = 0; i < g_parseSchemaCtx.array.memSize; i++) {
        TestVertexLabelT *tmpVrtxLabel = (TestVertexLabelT *)g_parseSchemaCtx.array.memAddr[i];
        if (strcmp(tmpVrtxLabel->topRecordName, vertexLabel->topRecordName) == 0 &&
            tmpVrtxLabel->schemaVersion == vertexLabel->schemaVersion &&
            strcmp(tmpVrtxLabel->nsName, vertexLabel->nsName) == 0) {
            isHit = true;
            pos = i;
            g_parseSchemaCtx.array.memSize++;
            ExtendDynMemOfArray(&g_parseSchemaCtx.array);
            // 当前的元数据不释放, 放最后(由于每次查找都是从前往后找, 所以找不到这个老的元数据)
            g_parseSchemaCtx.array.memAddr[g_parseSchemaCtx.array.memSize - 1] = tmpVrtxLabel;
            break;
        }
    }
    if (!isHit) {
        // 未遍历到 表名/版本号/ns名 都相同的元数据, 新增数据
        g_parseSchemaCtx.array.memSize++;
        ExtendDynMemOfArray(&g_parseSchemaCtx.array);
    }
    // 当前位置放最新的元数据
    g_parseSchemaCtx.array.memAddr[pos] = vertexLabel;
    pthread_mutex_unlock(&g_parseSchemaCtx.threadLock);
    return GMERR_OK;
}

int TestGetVertexLabelFromCtx(TestLabelInfoT *labelInfo, TestVertexLabelT **vertexLabel)
{
    // 索引类型后续待删除, 依赖CompatibleV3配置
    pthread_mutex_lock(&g_parseSchemaCtx.threadLock);
    for (uint32_t i = 0; i < g_parseSchemaCtx.array.memSize; i++) {
        TestVertexLabelT *label = (TestVertexLabelT *)(g_parseSchemaCtx.array.memAddr[i]);
        if (strcmp(label->topRecordName, labelInfo->labelName) == 0 &&
            label->schemaVersion == labelInfo->schemaVersion && strcmp(label->nsName, labelInfo->nsName) == 0) {
            *vertexLabel = label;
            pthread_mutex_unlock(&g_parseSchemaCtx.threadLock);
            return GMERR_OK;
        }
    }
    pthread_mutex_unlock(&g_parseSchemaCtx.threadLock);
    printf("can not find label(labelName : %s, schemaVersion : %u, nsName : %s)'s metadata.\n", labelInfo->labelName,
        labelInfo->schemaVersion, labelInfo->nsName);
    return FAILED;
}

void OutOfMemoryPanicRaise(const char *paincInfo, ...)
{
    // 当前结构化读写场景不构造客户端虚拟空间满,
    // 客户端不应该出现内存不足。否则应该是测试系统内存泄露、需要定位泄露原因！
    va_list args;
    va_start(args, paincInfo);
    (void)vprintf(paincInfo, args);
    va_end(args);
    (void)fflush(stdout);
    // 用例c++场景下, 可以考虑异常抛出. 外层处理. c场景默认assert, 用于快速发现问题
    assert(0);
}

// 流计算同时依赖时序和结构化写，此处的宏隔离范围需要缩小，最好的方式是将序列化的代码单独提取出来
#if defined FEATURE_STREAM || !defined PRODUCT_USG_TS

bool IsSmallEndium()
{
    union w {
        int a;
        char b;
    } c;
    c.a = 1;
    return (c.b == 1);  // 小端返回TRUE,大端返回FALSE
}

inline static Status ConvertUint32ToVarint(uint32_t input, uint32_t *output, uint32_t *byteNum)
{
    if (!IsSmallEndium()) {  // 大端
        if (input <= 0x3F) {
            *output = input << 24;  // 前24位都为0，左移以优化存储
            *byteNum = 1;           // 此时可以只使用1字节来存储数据
        } else if (input <= 0x3FFF) {
            *output = input << 16;  // 前16位都为0，左移以优化存储
            *output = *output | 0x40000000;
            *byteNum = 2;  // 此时可以只使用2字节来存储数据
        } else if (input <= 0x3FFFFF) {
            *output = input << 8;  // 前8位都为0，左移以优化存储
            *output = *output | 0x80000000;
            *byteNum = 3;  // 此时可以只使用3字节来存储数据
        } else if (input <= 0x3FFFFFFF) {
            *output = input | 0xC0000000;
            *byteNum = 4;  // 此时需要使用全部的4字节来存储数据
        } else {
            return GMERR_FIELD_OVERFLOW;
        }
    } else {  // 小端
        if (input <= 0x3F) {
            *output = input;
            *byteNum = 1;  // 此时可以只使用1字节来存储数据
        } else if (input <= 0x3FFF) {
            // 注意：移动运算与大小端无关，左移是往高位移动，右移是往低位移动
            // 小端在存储时是低到高，但是在运算时依然是高到低
            // 8: 将2字节的前后字节序逆转
            *output = ((input & 0x000000FF) << 8) | (input >> 8) | 0x00000040;
            *byteNum = 2;  // 此时可以只使用2字节来存储数据
        } else if (input <= 0x3FFFFF) {
            // 16: 将3字节的前后字节序逆转
            *output = ((input & 0x000000FF) << 16) | (input & 0x0000FF00) | (input >> 16) | 0x00000080;
            *byteNum = 3;  // 此时可以只使用3字节来存储数据
        } else if (input <= 0x3FFFFFFF) {
            // 24: 将4字节的前后字节序逆转
            *output = (input << 24) | (input & 0x00FFFF00) | (input >> 24) | 0x000000C0;
            *byteNum = 4;  // 此时需要使用全部的4字节来存储数据
        } else {
            return GMERR_FIELD_OVERFLOW;
        }
    }
    return GMERR_OK;
}

inline static void ConvertVarintToUint32(uint32_t input, uint32_t *output, uint32_t *byteNum)
{
    if (!IsSmallEndium()) {                 // 大端
        uint32_t varintType = input >> 30;  // input前2位表示varintType，右移30位取varintType
        if (varintType == 0) {              // type为0，表示占用1字节
            *output = input >> 24;          // 占用1字节，右移24位即可获得前1字节的数据
        } else if (varintType == 1) {       // type为1，表示占用2字节
            *output = input >> 16;          // 占用2字节，右移16位即可获得前2字节的数据
            *output = *output & 0x00003FFF;
        } else if (varintType == 2) {  // type为2，表示占用3字节
            *output = input >> 8;      // 占用3字节，右移8位即可获得前3字节的数据
            *output = *output & 0x003FFFFF;
        } else {  // (varintType == 3)
            *output = input & 0x3FFFFFFF;
        }
        *byteNum = varintType + 1;
    } else {  // 小端
        uint32_t varintType =
            (input & 0x000000C0) >> 6;  // 小端在存储时是低到高，但是在运算时依然是高到低，故右移6位即可
        if (varintType == 0) {
            *output = input & 0x0000003F;
        } else if (varintType == 1) {  // 移动运算与大小端无关，左移是往高位移动，右移是往低位移动
            // 不能优化((input & 0x0000FF00) >> 8) -> (input >> 8)，因为高两个字节序列化了数据
            *output = (((input & 0x000000FF) << 8) | ((input & 0x0000FF00) >> 8)) & 0x00003FFF;
        } else if (varintType == 2) {  // type为2，表示占用3字节
            // 不能优化((input & 0x00FF0000) >> 16) -> (input >> 16)，因为高一个字节序列化了数据
            *output = (((input & 0x000000FF) << 16) | (input & 0x0000FF00) | ((input & 0x00FF0000) >> 16)) & 0x003FFFFF;
        } else {  // (varintType == 3)
            // 24: 将4字节的前后字节序逆转
            *output = ((input << 24) | (input & 0x00FFFF00) | (input >> 24)) & 0x3FFFFFFF;
        }
        *byteNum = varintType + 1;
    }
}

/*
 * 虽然ConvertUint32ToVarint有该函数的功能，但该函数由于功能单一，因此更高效
 */
inline static Status GetVarintLength(uint32_t input, uint32_t *byteNum)
{
    if (input <= 0x3F) {
        *byteNum = 1;  // 此时可以只使用1字节来存储数据
    } else if (input <= 0x3FFF) {
        *byteNum = 2;  // 此时可以只使用2字节来存储数据
    } else if (input <= 0x3FFFFF) {
        *byteNum = 3;  // 此时可以只使用3字节来存储数据
    } else if (input <= 0x3FFFFFFF) {
        *byteNum = 4;  // 此时需要使用全部的4字节来存储数据
    } else {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

inline static void SetBitTrueIntoUint8Arr(uint8_t *buf, uint32_t index)
{
    // buf[index >> 3]：在uint8_t的buf数组中，选择一个合适的uint8_t元素来保存当前bit位
    // (index & 0x7)：index所表示的bit位，在当前uint8_t元素中的第几位
    buf[index >> 3] |= (uint8_t)(1 << (index & 0x07));
}

inline static void SetBitFalseIntoUint8Arr(uint8_t *buf, uint32_t index)
{
    // buf[index >> 3]：在uint8_t的buf数组中，选择一个合适的uint8_t元素来保存当前bit位
    // (index & 7)：index所表示的bit位，在当前uint8_t元素中的高第几位
    // 7 - (index & 7)：需要左移的步数，然后取反，左移之后对应位就被置为0了
    buf[index >> 3] &= (uint8_t) ~(uint8_t)(1 << (index & 0x07));
}
/*
 * 注释:参考函数SetBitTrueIntoUint8Arr
 * 最终&的结果指定位上为1, 则!=0为真返回true; 否则!=0为假返回false
 */
inline static bool GetBitValueFromUint8Arr(const uint8_t *buf, uint32_t index)
{
    return (buf[index >> 3] & (uint8_t)(1 << (index & 0x07))) !=
           0;  // buf[index >> 3]：在uint8_t的buf数组中，index右移3位
}

void ExtendLenStack(structTestCtx *c)
{
    if (c->totalSize == 0) {
        c->totalSize = INIT_STACK_SIZE;
        c->lenStack = c->initStack;
        return;
    }
    if (c->useSize < c->totalSize) {
        return;
    }
    uint32_t newSize;
    if (c->totalSize < STACK_GROW_METHOD_BOUNDARY) {
        newSize = c->totalSize * STACK_SMALL_CAPACITY_GROW_MULTIPLE;
    } else {
        newSize = c->totalSize + STACK_BIG_CAPACITY_GROW_STEP;
    }
    uint32_t *newVal = (uint32_t *)malloc(newSize * sizeof(uint32_t));
    if (newVal == NULL) {
        OutOfMemoryPanicRaise("ExtendLenStack(from %u to %u) failed.\n", c->totalSize, newSize);
    }
    memset(newVal, 0, sizeof(uint32_t) * newSize);
    memcpy(newVal, c->lenStack, sizeof(uint32_t) * c->totalSize);
    if (c->totalSize > INIT_STACK_SIZE) {  // 第一次扩展时, 使用的是栈变量, 不需要free
        free(c->lenStack);  // 释放上一次内存, 最后一次要用户手动调用deSeriFreeDynMem释放
    }
    c->lenStack = newVal;
    c->totalSize = newSize;
}

// ######## Entry function 1: release the dynamic memory when deSeri vertex
void deSeriFreeDynMem(structTestCtx *c, bool isAll)
{
    if (c->totalSize > INIT_STACK_SIZE) {
        if (c->lenStack) {
            free(c->lenStack);
        }
        c->lenStack = c->initStack;
        c->totalSize = INIT_STACK_SIZE;
    }
    if (c->useExternalMem) {
        return;
    }
    if (c->memAddr == NULL) {
        c->memMax = 0;
        c->memSize = 0;
        return;
    }
    for (uint32_t i = 0; i < c->memSize; ++i) {
        if (c->memAddr[i]) {
            free(c->memAddr[i]);
            c->memAddr[i] = NULL;
        }
    }
    c->memSize = 0;
    if (isAll) {
        free(c->memAddr);
        c->memAddr = NULL;
        c->memMax = 0;
    }
}

void ExtendDynArray(structTestCtx *c)
{
    if (c->memSize < c->memMax) {
        return;
    }
    uint32_t newSize;
    if (c->memMax == 0) {
        newSize = INIT_DYN_MEM_SIZE;
    } else {
        newSize = c->memMax * DYN_MEM_GROW_MULTIPLE;
    }
    void **newAddr = (void **)malloc(sizeof(void *) * newSize);
    if (newAddr == NULL) {
        OutOfMemoryPanicRaise("ExtendDynArray(from %u to %u) failed.\n", c->memMax, newSize);
    }
    memset(newAddr + c->memMax, 0, sizeof(void *) * c->memMax);
    if (c->memMax > 0) {
        memcpy(newAddr, c->memAddr, sizeof(void *) * c->memMax);
        free(c->memAddr);
    }
    c->memAddr = newAddr;
    c->memMax = newSize;
}

int GetBitFieldSize(TestPropertySchemaT *p, uint32_t *begin, uint32_t end)
{
    uint32_t i = *begin;
    int sum = 0;
    if (p[i].dataType == GMC_DATATYPE_BITFIELD8) {
        sum = sizeof(uint8_t);
    } else if (p[i].dataType == GMC_DATATYPE_BITFIELD16) {
        sum = sizeof(uint16_t);
    } else if (p[i].dataType == GMC_DATATYPE_BITFIELD32) {
        sum = sizeof(uint32_t);
    } else if (p[i].dataType == GMC_DATATYPE_BITFIELD64) {
        sum = sizeof(uint64_t);
    } else {
        assert(0);
    }
    uint8_t bitfieldOffset = p[i].bitfieldOffset;
    GmcDataTypeE dataType = p[i].dataType;
    for (i = i + 1; i < end; ++i) {
        if (p[i].dataType == dataType && p[i].bitfieldOffset > bitfieldOffset) {
            bitfieldOffset = p[i].bitfieldOffset;
            continue;
        }
        break;
    }
    *begin = i - 1;
    return sum;
}

void SeriVertexRecord(GmcSeriT *s, TestSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot, uint32_t *size)
{
    structTestCtx *c = (structTestCtx *)s->userData;
    uint32_t newVal, newSize;
    uint32_t propeNum = schema->propeNum;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    if (vertexLabel->vertexType != FIXED_VERTEX) {
        // 读取getSerialVertexLength中记录的recordLen
        ConvertUint32ToVarint(c->lenStack[c->idx++], &newVal, &newSize);
        *(uint32_t *)*destBuf = newVal;
        *destBuf += newSize;
    }
    // 读取getSerialVertexLength中记录的fixedLen
    int fixedLen = c->lenStack[c->idx++];
    ConvertUint32ToVarint(fixedLen, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;

    uint32_t structSize = 0;
    TestPropertySchemaT *properties = schema->properties;
    for (uint32_t i = 0; i < propeNum; i++) {
        // 填充定长区域
        if (IsBitFieldType(properties[i].dataType)) {
            int bitFieldSize = GetBitFieldSize(properties, &i, propeNum);
            memcpy(*destBuf, srcBuf + structSize, bitFieldSize);
            structSize += bitFieldSize;
            *destBuf += bitFieldSize;
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            uint32_t bitMapSize = properties[i].size / BYTE_LENGTH;
            memcpy(*destBuf, srcBuf + structSize, bitMapSize);
            structSize += bitMapSize;
            *destBuf += bitMapSize;
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            memcpy(*destBuf, srcBuf + structSize, properties[i].size);
            structSize += properties[i].size;
            *destBuf += properties[i].size;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
    }

    if (isRoot) {
        // sys_checkversion的预留字段
        *(*destBuf) = 0;
        *destBuf += TEST_RESERVED_SIZE;
    }
    // nullable
    bool *fieldNullInfo = c->fieldNullInfo;
    ConvertUint32ToVarint(schema->propeNum + RESERVED_FIELD_COUNT, &newVal, &newSize);
    *(uint32_t *)*destBuf = newVal;
    *destBuf += newSize;
    uint32_t i, propeNumSize;
    propeNumSize = CALC_ALIGN_SIZE(schema->propeNum + RESERVED_FIELD_COUNT, BYTE_LENGTH) / BYTE_LENGTH;
    uint8_t *nullableInfo = *destBuf;
    // init, all field are valid
    /* e.g. propeNum = 10
     * field nullInfo: 11111111 00000011
     * key  nullInfo:  00000011 11111111
     */
    for (i = 0; i < schema->propeNum; ++i) {
        // e.g. propeNum = 30, 若i = 10, ******** | *****a**  | ******** | ********, 则将a位置为1
        if (!properties[i].isValid) {
            continue;
        }
        // 测试时指定字段写入空值，1表示该字段有值，0表示该字段为空
        if (fieldNullInfo != NULL && fieldNullInfo[i]) {
            continue;
        }
        SetBitTrueIntoUint8Arr(*destBuf, i);
    }
    *destBuf += propeNumSize;
    if (vertexLabel->vertexType == FIXED_VERTEX) {
        return;
    }
    // var fields
    if (isRoot) {
        fixedLen = fixedLen - TEST_RESERVED_SIZE;
    }

    structSize = 0;
    uint8_t **varAddr;
    for (i = 0; i < propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            structSize += GetBitFieldSize(properties, &i, propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            structSize += properties[i].size / BYTE_LENGTH;
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            structSize += properties[i].size;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // 填充变长区域
        if (!properties[i].isFixed && properties[i].isValid) {
            uint16_t varFieldLen = *(uint16_t *)(srcBuf + structSize);
            if (varFieldLen > properties[i].size) {
                printf("[SeriVertexRecord] warn: fieldName:%s, maxSize:%u, inputSize:%u.\n", properties[i].name,
                    properties[i].size, varFieldLen);
            }
            ConvertUint32ToVarint(varFieldLen, &newVal, &newSize);
            *(uint32_t *)*destBuf = newVal;
            *destBuf += newSize;
            if (varFieldLen > 0) {
                varAddr = (uint8_t **)(srcBuf + structSize + sizeof(uint16_t));
                memcpy(*destBuf, *varAddr, varFieldLen);
                *destBuf += varFieldLen;
            }
            structSize += STRUCT_VAR_SIZE;
        }
    }

    structSize = 0;
    uint8_t **nodeAddr, *nodeLen;
    for (i = 0; i < propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            structSize += GetBitFieldSize(properties, &i, propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            structSize += properties[i].size / BYTE_LENGTH;
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            structSize += properties[i].size;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            nodeLen = srcBuf + structSize;
            nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
            if (*(uint16_t *)nodeLen == 0 || *nodeAddr == NULL) {
                // 将为空的node的nullInfo位置为0
                SetBitFalseIntoUint8Arr(nullableInfo, i);
            }
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
    }
    if (size) {
        *size = structSize;
    }
}

void SeriVertexSubNode(GmcSeriT *s, TestSchemaT *schema, uint8_t *srcBuf, uint8_t **destBuf, bool isRoot)
{
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    uint32_t i, k, newValue, newSize, elementNum, offset = 0;
    // nodeNum
    ConvertUint32ToVarint(schema->nodeNum, &newValue, &newSize);
    *(uint32_t *)*destBuf = newValue;
    *destBuf += newSize;
    if (schema->nodeNum == 0) {
        return;
    }
    uint32_t propeNum = schema->propeNum;
    TestPropertySchemaT *properties = schema->properties;
    // nodes
    uint8_t *nodeLen;
    uint8_t **nodeAddr;
    uint32_t nodeIndex = 0;
    TestNodeSchemaT *nodes = schema->nodes;
    uint32_t structSize = 0;
    for (i = 0; i < propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            structSize += GetBitFieldSize(properties, &i, propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            structSize += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            structSize += properties[i].size;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            *(uint8_t *)*destBuf = 0xff;
            *destBuf += IS_CREATE_SIZE;  // isCreated标记
            nodeLen = srcBuf + structSize;
            elementNum = *(uint16_t *)nodeLen;
            nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
            ConvertUint32ToVarint(elementNum, &newValue, &newSize);
            *(uint32_t *)*destBuf = newValue;
            *destBuf += newSize;
            if (*nodeAddr == NULL) {
                continue;
            }
            for (k = 0; k < elementNum; ++k) {
                TestSchemaT *nodeSchema = nodes[nodeIndex].schema;
                SeriVertexRecord(s, nodeSchema, *nodeAddr + offset * k, destBuf, false, &offset);
                SeriVertexSubNode(s, nodeSchema, *nodeAddr + offset * k, destBuf, false);
            }
            ++nodeIndex;
            structSize += STRUCT_VAR_SIZE;
        }
        if (nodeIndex > schema->nodeNum) {
            printf("[SeriVertexSubNode] error! nodeIndex : %u, schema->nodeNum : %u\n", nodeIndex, schema->nodeNum);
            assert(0);
        }
    }
    return;
}

// ######## Entry function 2: SeriStructVertex
int32_t SeriStructVertex(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    c->idx = 0;
    // vertex type
    *(uint8_t *)destBuf = vertexLabel->vertexType;
    if (c->haveBitmap) {
        // ***a**aa : 目前使用位为a所在的位
        *(uint8_t *)destBuf = *(uint8_t *)destBuf | (uint8_t)0x10;
    }
    uint8_t *buf = (uint8_t *)destBuf + VERTEX_TYPE_SIZE;
    // tree, vertex length
    uint32_t newVal, newSize;
    if (vertexLabel->vertexType == TREE_VERTEX) {
        ConvertUint32ToVarint(s->bufSize, &newVal, &newSize);
        *(uint32_t *)buf = newVal;
        buf += newSize;
    }
    // 序列化根节点vertexLen后面的定长和变长部分
    SeriVertexRecord(s, schema, s->obj, &buf);
    if (vertexLabel->vertexType == FIXED_VERTEX || vertexLabel->vertexType == FLAT_VERTEX) {
        return GMERR_OK;
    }
    // 序列化根节点的node部分
    SeriVertexSubNode(s, schema, s->obj, &buf, true);
    return GMERR_OK;
}

int getRecordSize(GmcSeriT *s, TestSchemaT *schema, uint8_t *addr, bool isRoot, uint32_t *size)
{
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    uint32_t newVal, newSize;
    int id = -1;
    if (vertexLabel->vertexType != FIXED_VERTEX) {
        ExtendLenStack(c);
        id = c->useSize++;
    }
    // fixed properties
    uint32_t i, fieldLen, fixedLen = 0, structSize = 0;
    int32_t totalVarFieldLen = 0;
    TestPropertySchemaT *properties = schema->properties;
    for (i = 0; i < schema->propeNum; ++i) { // 不包含sys_version预留字段
        if (IsBitFieldType(properties[i].dataType)) {
            int bitFieldSize = GetBitFieldSize(properties, &i, schema->propeNum);
            fixedLen += bitFieldSize;
            structSize += bitFieldSize;
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            c->haveBitmap = true;
            fixedLen += (properties[i].size / BYTE_LENGTH);
            structSize += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (!properties[i].isFixed && properties[i].isValid) { // 变长
            uint16_t varFieldLen = *(uint16_t *)(addr + structSize);
            totalVarFieldLen += (int32_t)varFieldLen;
            ConvertUint32ToVarint(varFieldLen, &newVal, &newSize);
            totalVarFieldLen += newSize;
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        if (!properties[i].isValid) { // node
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // 定长
        fixedLen += properties[i].size;
        structSize += properties[i].size;
    }
    // fieldLen为序列化后定长字段的长度, 包含sys_version预留字段
    // fixedLen为结构体中定长字段的长度, 不包含sys_version预留字段
    fieldLen = fixedLen;
    if (isRoot) {
        // 根节点包含预留字段
        fieldLen += TEST_RESERVED_SIZE;
    }
    // the length of fixed properties
    ConvertUint32ToVarint(fieldLen, &newVal, &newSize);
    int sum = fieldLen + newSize;
    ExtendLenStack(c);
    c->lenStack[c->useSize++] = fieldLen;  // c->lenStack[1] = fixedLen
    // propNum
    ConvertUint32ToVarint(schema->propeNum + RESERVED_FIELD_COUNT, &newVal, &newSize);
    sum += newSize;
    // nullable
    sum += CALC_ALIGN_SIZE(schema->propeNum + RESERVED_FIELD_COUNT, BYTE_LENGTH) / BYTE_LENGTH;
    if (vertexLabel->vertexType == FIXED_VERTEX) {
        return sum;
    }
    sum += totalVarFieldLen;
    if (size) {
        // 结构体偏移一整个数组元素(定长 + 变长 + node), 偏移到下一个元素开头
        *size = structSize;
    }
    ConvertUint32ToVarint(sum, &newVal, &newSize);
    sum += newSize;
    c->lenStack[id] = sum;  //  c->lenStack[0] = recordLen(定长 + 变长 + recordLen本身的占用字节数)
    return sum;
}

// ------>> inner function
int getSubNodeSize(GmcSeriT *s, TestSchemaT *schema, uint8_t *addr)
{
    uint32_t i, j, newSize, offset = 0;
    // nodeNum
    GetVarintLength(schema->nodeNum, &newSize);
    int sum = newSize;
    if (schema->nodeNum == 0) {
        return sum;
    }
    uint8_t *nodeBase = addr, *buf, **nodeAddr;
    uint32_t elementNum, nodeIndex = 0;
    TestPropertySchemaT *properties = schema->properties;
    TestNodeSchemaT *nodes = schema->nodes;
    for (i = 0; i < schema->propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            nodeBase += properties[i].size;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            nodeBase += STRUCT_VAR_SIZE;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            buf = nodeBase;
            elementNum = *(uint16_t *)buf;
            nodeAddr = (uint8_t **)(buf + sizeof(uint16_t));
            GetVarintLength(elementNum, &newSize);
            sum += newSize;
            if (*nodeAddr == NULL) {
                continue;
            }
            for (j = 0; j < elementNum; ++j) {
                TestSchemaT *nodeSchema = nodes[nodeIndex].schema;
                sum += getRecordSize(s, nodeSchema, *nodeAddr + offset * j, false, &offset);
                sum += getSubNodeSize(s, nodeSchema, *nodeAddr + offset * j);
            }
            sum += IS_CREATE_SIZE;  // isCreated标记
            nodeBase += STRUCT_VAR_SIZE;
            nodeIndex++;
        }
        if (nodeIndex > schema->nodeNum) {
            printf("[getSubNodeSize] error! nodeIndex : %u, schema->nodeNum : %u\n", nodeIndex, schema->nodeNum);
            assert(0);
        }
    }
    return sum;
}

// ######## Entry function 3: getSerialVertexLength
void getSerialVertexLength(GmcSeriT *s)
{
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    if (vertexLabel->vertexType == FIXED_VERTEX && c->vertexSize > 0) {
        s->bufSize = c->vertexSize;
        return;
    }
    c->useSize = 0;
    uint8_t *addr = s->obj;
    // vertextType
    c->vertexSize = VERTEX_TYPE_SIZE;
    // root node
    c->vertexSize += getRecordSize(s, schema, addr);
    s->bufSize = c->vertexSize;
    if (vertexLabel->vertexType == FIXED_VERTEX || vertexLabel->vertexType == FLAT_VERTEX) {
        return;
    }
    // sub nodes
    c->vertexSize += getSubNodeSize(s, schema, addr);
    uint32_t newSize;
    GetVarintLength(c->vertexSize, &newSize);
    c->vertexSize += newSize;
    s->bufSize = c->vertexSize;
}

// ######## Entry function 4: SeriPrimaryKey
int32_t SeriPrimaryKey(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    TestIndexLabelT *index;
    uint32_t i, k, j = 0;
    if (c->keyId == 0) {
        *destBuf = 0xff;  // 主键 不允许为空
        index = vertexLabel->pkIndex;
    } else {
        if (c->keyId < 0 || c->keyId > vertexLabel->secIndexNum) {
            printf("[SeriPrimaryKey] invalid keyId:%u, it must been in [0, %u], tableName:%s.\n", c->keyId,
                vertexLabel->secIndexNum, vertexLabel->topRecordName);
            assert(0);
        } else {
            index = vertexLabel->secIndexes + c->keyId - 1;
            uint8_t keyInfo = 0;
            if (c->keyNullInfo) {  // 用户有传 keyNullInfo 数组, 按用户传的来处理 (暂不支持)
                bool *keyNullInfo = c->keyNullInfo;
                for (i = 0; i < MAX_KEY_FIELD_NUM; ++i) {  // 待修改成实际size
                    keyInfo = keyInfo << 1;
                    if (keyNullInfo[i] != 0) {
                        keyInfo |= 1;
                    }
                }
            } else {  // 用户没有传 keyNullInfo 数组, 默认按 索引字段全不为空 来处理
                keyInfo = ((uint32_t)1 << (index->propeNum)) - 1;
            }
            *destBuf = keyInfo;

            // lpm 不允许为空; 所有索引字段的 nullable 都为 false 时, 置为 0xff
            if (index->indexType == TEST_LPM4_INDEX || index->indexType == TEST_LPM6_INDEX || !index->isNullable) {
                *destBuf = 0xff;
            }
        }
    }
    uint32_t offset = CALC_ALIGN_SIZE(index->propeNum, BYTE_LENGTH) / BYTE_LENGTH;
    uint8_t *srcBuf = s->obj;
    uint8_t **newAddr;
    TestPropertySchemaT *properties = schema->properties;
    TestPropertySchemaT *indexProperties = index->properties;
    for (i = 0; i < index->propeNum; ++i) {
        srcBuf = s->obj;
        // keyBuf先填定长
        for (j = 0; j < schema->propeNum; ++j) {
            char *indexName = indexProperties[i].name;
            char *name2 = properties[j].name;
            if (0 == strcmp(indexName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (IsBitFieldType(properties[j].dataType)) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == GMC_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / BYTE_LENGTH);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        if (indexProperties[i].isFixed) {
            memcpy(destBuf + offset, srcBuf, indexProperties[i].size);
            offset += indexProperties[i].size;
            srcBuf += indexProperties[i].size;
        }
    }
    if (vertexLabel->vertexType == FIXED_VERTEX) {
        return GMERR_OK;
    }
    for (i = 0; i < index->propeNum; ++i) {
        srcBuf = s->obj;
        // keyBuf后填变长
        for (j = 0; j < schema->propeNum; ++j) {
            char *indexName = indexProperties[i].name;
            char *name2 = properties[j].name;
            if (0 == strcmp(indexName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (IsBitFieldType(properties[j].dataType)) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == GMC_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / BYTE_LENGTH);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        if (!indexProperties[i].isFixed) {
            k = *(uint16_t *)srcBuf;
            *(uint32_t *)(destBuf + offset) = k;
            offset += sizeof(uint32_t);
            newAddr = (uint8_t **)(srcBuf + sizeof(uint16_t));
            memcpy(destBuf + offset, *newAddr, k);
            offset += k;
            srcBuf += STRUCT_VAR_SIZE;
        }
    }
    return GMERR_OK;
}

// ######## Entry function 5: getSerialKeyLength
void getSerialKeyLength(GmcSeriT *s)
{
    structTestCtx *c = (structTestCtx *)s->userData;
    if (c->isFixKey && c->keySize > 0) {
        s->bufSize = c->keySize;
        return;
    }
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    TestIndexLabelT *index;
    if (c->keyId == 0) {
        index = vertexLabel->pkIndex;
    } else {
        if (c->keyId < 0 || c->keyId > vertexLabel->secIndexNum) {
            printf("[getSerialKeyLength] invalid keyId:%u, it must been in [0, %u], tableName:%s.\n", c->keyId,
                vertexLabel->secIndexNum, vertexLabel->topRecordName);
            assert(0);
        } else {
            index = vertexLabel->secIndexes + c->keyId - 1;
        }
    }
    uint8_t *srcBuf = s->obj;
    uint32_t i, j = 0;
    c->keySize = CALC_ALIGN_SIZE(index->propeNum, BYTE_LENGTH) / BYTE_LENGTH;
    c->isFixKey = true;
    TestPropertySchemaT *properties = schema->properties;
    TestPropertySchemaT *indexProperties = index->properties;
    for (i = 0; i < index->propeNum; ++i) {
        srcBuf = s->obj;
        for (j = 0; j < schema->propeNum; ++j) {
            char *indexName = indexProperties[i].name;
            char *name2 = properties[j].name;
            if (0 == strcmp(indexName, name2)) {
                break;
            }
            if (properties[j].isFixed) {
                if (IsBitFieldType(properties[j].dataType)) {
                    srcBuf += GetBitFieldSize(properties, &j, schema->propeNum);
                    continue;
                }
                if (properties[j].dataType == GMC_DATATYPE_BITMAP) {
                    c->haveBitmap = true;
                    srcBuf += (properties[j].size / BYTE_LENGTH);
                    continue;
                }
                srcBuf += properties[j].size;
            } else {
                srcBuf += STRUCT_VAR_SIZE;
            }
        }
        ++j;
        if (j > schema->propeNum) {
            printf("[getSerialKeyLength] invalid field:%s of key:%s.\n", properties[j].name, index->indexName);
            assert(0);
        }
        if (indexProperties[i].isFixed) {
            c->keySize += indexProperties[i].size;
            srcBuf += indexProperties[i].size;
        } else {
            c->keySize += sizeof(uint32_t);
            c->keySize += *(uint16_t *)srcBuf;
            srcBuf += STRUCT_VAR_SIZE;
            c->isFixKey = false;
        }
    }
    s->bufSize = c->keySize;
}

void deSeriStructRecord(
    GmcDeseriT *d, TestSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data, uint32_t *fixedLen)
{
    structTestCtx *c = (structTestCtx *)d->userData;
    uint32_t totalLen, length, offset;
    TestPropertySchemaT *properties = schema->properties;
    uint32_t propeNum = schema->propeNum;
    // recordLength
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &totalLen, &offset);
    const uint8_t *addrEnd = srcBuf + *sum + totalLen;
    *sum += offset;
    // fixedLength
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), fixedLen, &offset);
    *sum += offset;
    uint32_t structSize = 0, tsum = *sum;
    if (*fixedLen > 0) {
        for (uint32_t i = 0; i < propeNum; ++i) {
            // 定长
            if (IsBitFieldType(properties[i].dataType)) {
                int bitFieldSize = GetBitFieldSize(properties, &i, propeNum);
                memcpy(data + structSize, srcBuf + *sum, bitFieldSize);
                structSize += bitFieldSize;
                *sum += bitFieldSize;
                continue;
            }
            if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
                uint32_t bitmapSize = properties[i].size / BYTE_LENGTH;
                memcpy(data + structSize, srcBuf + *sum, bitmapSize);
                structSize += bitmapSize;
                *sum += bitmapSize;
                continue;
            }
            if (properties[i].isFixed && properties[i].isValid) {
                memcpy(data + structSize, srcBuf + *sum, properties[i].size);
                structSize += properties[i].size;
                *sum += properties[i].size;
                continue;
            }
            // 变长, node
            if (!properties[i].isFixed || !properties[i].isValid) {
                structSize += STRUCT_VAR_SIZE;
            }
        }
        if ((*sum - tsum) != (*fixedLen)) {
            printf("[deSeriStructVertex] error! sum : %u, tsum : %u, fixedLen : %u\n", *sum, tsum, *fixedLen);
            assert(0);
        }
    }
    uint32_t propeNumFromBuf;
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &propeNumFromBuf, &offset);
    *sum += offset;
    const uint8_t *nullAddr = srcBuf + *sum;
    *sum += CALC_ALIGN_SIZE(propeNumFromBuf, BYTE_LENGTH) / BYTE_LENGTH;

    structSize = 0;
    // addrLen为d->obj的根节点中变长字段Len起始位置
    uint8_t *addrLen;
    uint8_t **addrVal;
    for (uint32_t i = 0; i < propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            structSize += GetBitFieldSize(properties, &i, propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            structSize += properties[i].size / BYTE_LENGTH;
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            structSize += properties[i].size;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            if (srcBuf + *sum < addrEnd) {
                ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &length, &offset);
                *sum += offset;
                addrLen = data + structSize;
                *(uint16_t *)addrLen = length;
                if (length > 0) {
                    addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
                    if (!c->useExternalMem) {
                        *addrVal = (uint8_t *)malloc(length + 1);
                        if (*addrVal == NULL) {
                            OutOfMemoryPanicRaise("[deSeriStructRecord] warn: malloc memory(%u) failed.\n", length + 1);
                        }
                        ExtendDynArray(c);
                        c->memAddr[c->memSize++] = *addrVal;
                    }
                    memcpy(*addrVal, srcBuf + *sum, length);
                    *(*addrVal + length) = 0;
                    *sum += length;
                }
                // 判断字段是否存在
                if (!GetBitValueFromUint8Arr(nullAddr, i)) {
                    *(uint16_t *)addrLen = 0;
                }
            }
        } else {
            // new schema + old data
            addrLen = data + structSize;
            *(uint16_t *)addrLen = 0;
            addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
            if (*addrVal) {
                (*addrVal)[0] = '\0';
            }
        }
        structSize += STRUCT_VAR_SIZE;
    }

    if (srcBuf + *sum < addrEnd) {
        // old schema + newdata
        *sum = addrEnd - srcBuf;
        return;
    }
}

void deSeriStructSubNode(GmcDeseriT *d, TestSchemaT *schema, const uint8_t *srcBuf, uint32_t *sum, uint8_t *data)
{
    structTestCtx *c = (structTestCtx *)d->userData;
    uint32_t i, j, size, nodeNum, elementNum, newSize;
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &nodeNum, &newSize);
    *sum += newSize;
    uint8_t *nodeBase = data;
    TestPropertySchemaT *properties = schema->properties;
    uint8_t *nodeLen;
    uint8_t **nodeAddr;
    TestSchemaT *s;
    int id = 0;
    TestNodeSchemaT *nodes = schema->nodes;
    for (i = 0; i < schema->propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            nodeBase += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            nodeBase += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            nodeBase += properties[i].size;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            nodeBase += STRUCT_VAR_SIZE;
            continue;
        }
        // node
        nodeLen = nodeBase;
        nodeAddr = (uint8_t **)(nodeLen + sizeof(uint16_t));
        if (nodeNum == 0) {
            *(uint16_t *)nodeLen = 0;
            nodeBase += STRUCT_VAR_SIZE;
            continue;
        }
        // new schema + old data
        if (nodeNum < schema->nodeNum && i >= nodeNum) {
            *(uint16_t *)nodeLen = 0;
            nodeBase += STRUCT_VAR_SIZE;
            continue;
        }
        // +1为isCreated标记
        *sum += IS_CREATE_SIZE;
        ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &elementNum, &newSize);
        *sum += newSize;
        *(uint16_t *)nodeLen = elementNum;
        if (elementNum == 0) {
            *nodeAddr = NULL;
            continue;
        }
        s = nodes[id].schema;
        id++;
        size = 0;
        TestPropertySchemaT *sproperties = s->properties;
        for (j = 0; j < s->propeNum; ++j) {
            if (!sproperties[j].isValid) {
                size += STRUCT_VAR_SIZE;
                continue;
            }
            if (IsBitFieldType(sproperties[j].dataType)) {
                size += GetBitFieldSize(sproperties, &j, s->propeNum);
                continue;
            }
            if (sproperties[j].dataType == GMC_DATATYPE_BITMAP) {
                size += (sproperties[j].size / BYTE_LENGTH);
                continue;
            }
            if (sproperties[j].isFixed) {
                size += sproperties[j].size;
            } else {
                size += STRUCT_VAR_SIZE;
            }
        }
        if (!c->useExternalMem) {
            *nodeAddr = (uint8_t *)malloc(size * elementNum);
            if (*nodeAddr == NULL) {
                OutOfMemoryPanicRaise(
                    "[deSeriStructSubNode] warn: malloc memory for nodeAddr failed, elementNum = %u, elementSize = "
                    "%u.\n",
                    elementNum, size);
            }
            ExtendDynArray(c);
            c->memAddr[c->memSize++] = *nodeAddr;
        }
        for (j = 0; j < elementNum; ++j) {
            // s为子节点的schema
            // sum偏移到srcBuf中子节点的每个元素的realElementNum + isCreate标记之后
            // nodeAddr初始化为d->obj的子节点指针的起始位置
            // size为每个元素的大小
            // 反序列化每个元素的定长变长字段
            deSeriStructRecord(d, s, srcBuf, sum, *nodeAddr + size * j, &newSize);
            // 反序列化每个元素的子节点
            deSeriStructSubNode(d, s, srcBuf, sum, *nodeAddr + size * j);
        }
        nodeBase += STRUCT_VAR_SIZE;
    }
    if (nodeNum == 0) {
        return;
    }
    if (nodeNum <= schema->nodeNum) {
        return;
    }
    // discard the subnode
    uint32_t totalLen, offset;
    for (i = schema->nodeNum; i < nodeNum; ++i) {
        ConvertVarintToUint32(*(uint32_t *)(srcBuf + *sum), &totalLen, &offset);
        *sum += offset;
        *sum += totalLen;
    }
}

// ######## Entry function 6: deSeriStructVertex
int32_t deSeriStructVertex(void *deSeri, const uint8_t *srcBuf, uint32_t srcLen, GmcStructureResvT *reservedSize)
{
    GmcDeseriT *d = (GmcDeseriT *)deSeri;
    structTestCtx *c = (structTestCtx *)d->userData;
    uint32_t length, offset, totalLen, fixedLen = 0;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    TestPropertySchemaT *properties = schema->properties;
    uint32_t propeNum = schema->propeNum;

    if (vertexLabel->vertexType == FIXED_VERTEX) {
        for (uint32_t i = 0; i < propeNum; ++i) {
            if (IsBitFieldType(properties[i].dataType)) {
                fixedLen += GetBitFieldSize(properties, &i, propeNum);
            } else if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
                fixedLen += (properties[i].size / BYTE_LENGTH);
            } else {
                fixedLen += properties[i].size;
            }
        }
        fixedLen += TEST_RESERVED_SIZE;
        ConvertVarintToUint32(*(uint32_t *)(srcBuf + VERTEX_TYPE_SIZE), &length, &offset);
        uint32_t minLen = (fixedLen <= length) ? fixedLen : length;
        memcpy(d->obj, srcBuf + offset + VERTEX_TYPE_SIZE, minLen - TEST_RESERVED_SIZE);
        if (fixedLen > length) {
            memset(d->obj + length, 0, fixedLen - length);
        }
        return GMERR_OK;
    }
    // free dynamic memory
    deSeriFreeDynMem(c);

    // sum全程跟踪对srcBuf的偏移
    uint32_t sum = VERTEX_TYPE_SIZE;
    if (vertexLabel->vertexType == TREE_VERTEX) {
        // vertexLength
        ConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &totalLen, &offset);
        sum += offset;
    }
    // recordLength
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
    // addrEnd为变长字段区结束地址
    const uint8_t *addrEnd = srcBuf + sum + length;
    sum += offset;
    // fixedPropeLength
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &fixedLen, &offset);
    sum += offset;
    uint32_t structSize = 0, tsum = sum;
    if (fixedLen > TEST_RESERVED_SIZE) {
        for (uint32_t i = 0; i < propeNum; ++i) {
            // 定长
            if (IsBitFieldType(properties[i].dataType)) {
                int bitFieldSize = GetBitFieldSize(properties, &i, propeNum);
                memcpy(d->obj + structSize, srcBuf + sum, bitFieldSize);
                structSize += bitFieldSize;
                sum += bitFieldSize;
                continue;
            }
            if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
                uint32_t bitmapSize = properties[i].size / BYTE_LENGTH;
                memcpy(d->obj + structSize, srcBuf + sum, bitmapSize);
                structSize += bitmapSize;
                sum += bitmapSize;
                continue;
            }
            if (properties[i].isFixed && properties[i].isValid) {
                memcpy(d->obj + structSize, srcBuf + sum, properties[i].size);
                structSize += properties[i].size;
                sum += properties[i].size;
                continue;
            }
            // 变长, node
            if (!properties[i].isFixed || !properties[i].isValid) {
                structSize += STRUCT_VAR_SIZE;
            }
        }
        if ((sum - tsum) != (fixedLen - TEST_RESERVED_SIZE)) {
            printf("[deSeriStructVertex] error! sum : %u, tsum : %u, fixedLen : %u\n", sum, tsum, fixedLen);
            assert(0);
        }
    }
    sum += TEST_RESERVED_SIZE;

    uint32_t propeNumFromBuf;
    ConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &propeNumFromBuf, &offset);
    sum += offset;
    const uint8_t *nullAddr = srcBuf + sum;
    sum += CALC_ALIGN_SIZE(propeNumFromBuf, BYTE_LENGTH) / BYTE_LENGTH;

    structSize = 0;
    uint8_t *addrLen;
    uint8_t **addrVal;
    for (uint32_t i = 0; i < propeNum; ++i) {
        // 定长
        if (IsBitFieldType(properties[i].dataType)) {
            structSize += GetBitFieldSize(properties, &i, propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            structSize += properties[i].size / BYTE_LENGTH;
            continue;
        }
        if (properties[i].isFixed && properties[i].isValid) {
            structSize += properties[i].size;
            continue;
        }
        // node
        if (!properties[i].isValid) {
            structSize += STRUCT_VAR_SIZE;
            continue;
        }
        // 变长
        if (!properties[i].isFixed && properties[i].isValid) {
            if (srcBuf + sum < addrEnd) {
                // 变长字段Len
                ConvertVarintToUint32(*(uint32_t *)(srcBuf + sum), &length, &offset);
                sum += offset;
                addrLen = d->obj + structSize;
                *(uint16_t *)addrLen = length;
                if (length > 0) {
                    addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
                    if (!c->useExternalMem) {
                        *addrVal = (uint8_t *)malloc(length + 1);
                        if (*addrVal == NULL) {
                            OutOfMemoryPanicRaise("[deSeriStructVertex] warn: malloc memory(%u) failed.\n", length + 1);
                        }
                        ExtendDynArray(c);
                        c->memAddr[c->memSize++] = *addrVal;
                    }
                    memcpy(*addrVal, srcBuf + sum, length);
                    *(*addrVal + length) = 0;
                    sum += length;
                }
                if (!GetBitValueFromUint8Arr(nullAddr, i)) {
                    *(uint16_t *)addrLen = 0;
                }
            } else {
                // maybe new schema + old data
                addrLen = d->obj + structSize;
                *(uint16_t *)addrLen = 0;
                addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
                if (*addrVal) {
                    (*addrVal)[0] = '\0';
                }
            }
            structSize += STRUCT_VAR_SIZE;
        }
    }

    if (srcBuf + sum < addrEnd) {
        // old schema + new data
        sum = addrEnd - srcBuf;
    }
    if (vertexLabel->vertexType == FLAT_VERTEX) {
        return GMERR_OK;
    }
    deSeriStructSubNode(d, schema, srcBuf, &sum, d->obj);
    return GMERR_OK;
}

#endif /* end of !defined FEATURE_STREAM && !defined PRODUCT_USG_TS */

#if !defined PRODUCT_USG_TS && !defined FEATURE_STREAM

/*************************** 以下为用例通用接口 ***************************/
/**
 * @par 描述：
 * 接口用于结构化写, 先填充seri结构体。
 * (1)填充seri结构体:
 * a.使用入参structObj填充seri.obj;
 * b.计算出序列化后需要的buffer大小填充seri.bufSize;
 * c.seri.seriFunc为回调函数, 实现序列化功能, 在客户端内部调用这个回调, 完成序列化后再发送报文。
 * (2)将填充好的seri结构体作为入参, 调用GmcSetVertexWithBuf。
 */
int32_t testStructSetVertexWithBuf(GmcStmtT *stmt, void *structObj, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);
    structTestCtx seriCtx = (structTestCtx){0};
    seriCtx.vertexLabel = vertexLabel;
    seriCtx.stmt = stmt;
    GmcSeriT seri = {0};
    seri.seriFunc = SeriStructVertex;
    seri.version = GMC_SERI_VERSION_DEFAULT;
    seri.obj = (uint8_t *)structObj;
    seri.userData = &seriCtx;
    // 计算出序列化后需要的buffer大小填充seri.bufSize
    getSerialVertexLength(&seri);
    ret = GmcSetVertexWithBuf(stmt, &seri);
    deSeriFreeDynMem(&seriCtx, true);
    return ret;
}

void structSetKeySeri(
    GmcStmtT *stmt, void *structObj, GmcSeriT *seri, structTestCtx *seriCtx, uint32_t keyId, bool *keyNullInfo)
{
    seriCtx->stmt = stmt;
    seriCtx->keyId = keyId;
    seriCtx->keyNullInfo = keyNullInfo;
    seri->seriFunc = SeriPrimaryKey;
    seri->version = GMC_SERI_VERSION_DEFAULT;
    seri->obj = (uint8_t *)structObj;
    seri->userData = seriCtx;
    getSerialKeyLength(seri);
}

int32_t testStructSetIndexKeyWithBuf(
    GmcStmtT *stmt, void *structObj, uint32_t keyId, bool *keyNullInfo, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);
    structTestCtx seriCtx = (structTestCtx){0};
    seriCtx.vertexLabel = vertexLabel;
    GmcSeriT seri = (GmcSeriT){0};
    structSetKeySeri(stmt, structObj, &seri, &seriCtx, keyId, keyNullInfo);
    return GmcSetIndexKeyWithBuf(stmt, keyId, &seri);
}

int32_t testStructSetKeyRangeStructure(GmcStmtT *stmt, void *structLeftKey, void *structRightKey,
    const GmcRangeItemFlagT *items, uint32_t itemNumber, uint32_t keyId, bool *keyNullInfo, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);

    GmcRangeKeySeriT rangeKeyInfo;
    rangeKeyInfo.keyId = keyId;

    structTestCtx seriCtx = (structTestCtx){0};
    seriCtx.vertexLabel = vertexLabel;
    GmcSeriT leftKey = (GmcSeriT){0};
    GmcSeriT rightKey = (GmcSeriT){0};

    if (!structLeftKey) {  // structLeftKey为NULL, 左key为空
        rangeKeyInfo.leftKeySeri = NULL;
    } else {
        structSetKeySeri(stmt, structLeftKey, &leftKey, &seriCtx, keyId, keyNullInfo);
        rangeKeyInfo.leftKeySeri = &leftKey;
    }

    if (!structRightKey) {  // structRightKey为NULL, 右key为空
        rangeKeyInfo.rightKeySeri = NULL;
    } else {
        structSetKeySeri(stmt, structRightKey, &rightKey, &seriCtx, keyId, keyNullInfo);
        rangeKeyInfo.rightKeySeri = &rightKey;
    }

    return GmcSetKeyRangeStructure(stmt, items, itemNumber, &rangeKeyInfo);
}

void testStructSetDeseri(GmcStmtT *stmt, void *structObj, GmcDeseriT *deseri, structTestCtx *deseriCtx,
    bool useExternalMem, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);

    deseriCtx->useExternalMem = useExternalMem;
    deseriCtx->stmt = stmt;
    deseriCtx->vertexLabel = vertexLabel;
    deseri->deseriFunc = deSeriStructVertex;
    deseri->version = GMC_SERI_VERSION_DEFAULT;
    deseri->obj = (uint8_t *)structObj;
    deseri->userData = deseriCtx;
}

int32_t testStructGetVertexDeseri(GmcStmtT *stmt, GmcDeseriT *deseri)
{
    return GmcGetVertexDeseri(stmt, deseri);
}

void structSetInputBufInfo(GmcStmtT *stmt, void *structObj, GmcSeriT *keySeri, GmcStructBufferT *inputBufInfo)
{
    GmcSeriT *s = (GmcSeriT *)keySeri;
    structTestCtx *c = (structTestCtx *)s->userData;
    TestVertexLabelT *vertexLabel = c->vertexLabel;
    TestSchemaT *schema = vertexLabel->schema;
    uint32_t i, fixedLen = 0, newVal, newSize;
    TestPropertySchemaT *properties = schema->properties;
    for (i = 0; i < schema->propeNum; ++i) {
        if (IsBitFieldType(properties[i].dataType)) {
            fixedLen += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            fixedLen += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (!properties[i].isFixed || !properties[i].isValid) {
            break;
        }
        fixedLen += properties[i].size;
    }
    ConvertUint32ToVarint(fixedLen, &newVal, &newSize);
    inputBufInfo->bufLen = fixedLen;
    inputBufInfo->buf = (uint8_t *)structObj;
    inputBufInfo->vertexOffset = VERTEX_TYPE_SIZE + newSize;  // vertexType + fixedLen
}

int32_t testStructGetVertexBuf(GmcStmtT *stmt, void *structObj, uint32_t keyId, GmcSeriT *keySeri,
    GmcStructBufferT *inputBufInfo, TestLabelInfoT *labelInfo)
{
    structTestCtx seriCtx = (structTestCtx){0};
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);
    seriCtx.vertexLabel = vertexLabel;
    keySeri->userData = &seriCtx;
    if (keySeri && !keyId) {
        structSetKeySeri(stmt, structObj, keySeri, &seriCtx, keyId, NULL);
    }
    structSetInputBufInfo(stmt, structObj, keySeri, inputBufInfo);
    return GmcGetVertexBuf(
        stmt, keyId, keySeri, inputBufInfo);  // 只有当 keyId=0(即主键), 且 keySeri 不为空时, 才起到减少拷贝次数的作用
}

int32_t GetNodeRecordBuf(uint8_t *buf, uint32_t bufLen, TestVertexLabelT *vertexLabel, TestSchemaT *schema,
    structTestCtx *c, void *structObj)
{
    TestPropertySchemaT *properties = schema->properties;
    uint32_t expFixedLen = 0;
    for (uint32_t i = 0; i < schema->propeNum; ++i) {
        if (IsBitFieldType(properties[i].dataType)) {
            // 位域字段
            expFixedLen += GetBitFieldSize(properties, &i, schema->propeNum);
            continue;
        }
        if (properties[i].dataType == GMC_DATATYPE_BITMAP) {
            // bitmap字段
            expFixedLen += (properties[i].size / BYTE_LENGTH);
            continue;
        }
        if (!properties[i].isFixed) {
            // 遇到变长, 不计算
            continue;
        }
        if (!properties[i].isValid) {
            // 遇到node, 不计算
            continue;
        }
        expFixedLen += properties[i].size;
    }
    uint32_t sum = 0, offset = 0, fixedLen = 0;
    if (vertexLabel->vertexType == FIXED_VERTEX) {
        ConvertVarintToUint32(*(uint32_t *)buf, &fixedLen, &offset);
        sum += offset;
        memcpy((uint8_t *)structObj, buf + sum, expFixedLen);
    } else if (vertexLabel->vertexType == FLAT_VERTEX || vertexLabel->vertexType == TREE_VERTEX) {
        uint32_t recordLen;
        ConvertVarintToUint32(*(uint32_t *)buf, &recordLen, &offset);
        if (recordLen != bufLen) {
            AW_FUN_Log(LOG_ERROR, "[GetNodeRecordBuf] recordLen : %d, bufLen: %d.", recordLen, bufLen);
            RETURN_IFERR(FAILED);
        }
        sum += offset;
        ConvertVarintToUint32(*(uint32_t *)(buf + sum), &fixedLen, &offset);
        sum += offset;
        memcpy((uint8_t *)structObj, buf + sum, expFixedLen);
        sum += fixedLen;
        uint32_t propeNum;
        ConvertVarintToUint32(*(uint32_t *)(buf + sum), &propeNum, &offset);
        sum += offset;
        const uint8_t *nullAddr = buf + sum;
        sum += CALC_ALIGN_SIZE(propeNum, BYTE_LENGTH) / BYTE_LENGTH;

        uint32_t varFieldIndex = 0;
        // addrEnd为变长字段区结束地址
        const uint8_t *addrEnd = buf + bufLen;
        // addrLen为obj的变长字段Len起始位置
        uint8_t *addrLen = (uint8_t *)structObj + expFixedLen;
        uint8_t **addrVal;
        while (buf + sum < addrEnd && varFieldIndex < schema->propeNum) {  // 存在变长字段
            // 为定长字段则跳过
            if (properties[varFieldIndex].isFixed && properties[varFieldIndex].isValid) {
                varFieldIndex++;
                continue;
            }
            // 为node则跳过, 直到碰到变长字段
            if (!properties[varFieldIndex].isValid) {
                varFieldIndex++;
                continue;
            }
            // 变长字段Len
            uint32_t length;
            ConvertVarintToUint32(*(uint32_t *)(buf + sum), &length, &offset);
            sum += offset;
            *(uint16_t *)addrLen = length;
            if (length > 0) {
                addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
                if (!c->useExternalMem) {
                    *addrVal = (uint8_t *)malloc(length + 1);
                    if (*addrVal == NULL) {
                        OutOfMemoryPanicRaise("[GetNodeRecordBuf] warn: malloc memory(%u) failed.\n", length + 1);
                    }
                    ExtendDynArray(c);
                    c->memAddr[c->memSize++] = *addrVal;
                }
                memcpy(*addrVal, buf + sum, length);
                *(*addrVal + length) = 0;
                sum += length;
            }
            if (!GetBitValueFromUint8Arr(nullAddr, varFieldIndex)) {
                *(uint16_t *)addrLen = 0;
            }
            addrLen += STRUCT_VAR_SIZE;
            varFieldIndex++;
        }

        if (buf + sum < addrEnd) {
            // old schema + new data, nodeBuf还有变长字段, 但是schema没有了
            AW_FUN_Log(LOG_INFO, "[GetNodeRecordBuf] buf + sum < addrEnd, sum: %d, varFieldIndex : %d, propeNum : %d.",
                sum, varFieldIndex, schema->propeNum);
        } else {
            // maybe new schema, nodeBuf没有变长字段了, 但是schema还有
            for (; varFieldIndex < schema->propeNum; ++varFieldIndex) {
                if (properties[varFieldIndex].isValid) {
                    if (properties[varFieldIndex].isFixed) {
                        continue;
                    }
                    *(uint16_t *)addrLen = 0;
                    addrVal = (uint8_t **)(addrLen + sizeof(uint16_t));
                    if (*addrVal) {
                        (*addrVal)[0] = '\0';
                    }
                    addrLen += STRUCT_VAR_SIZE;
                }
            }
        }
    } else {
        assert(0);
    }
    return 0;
}

/* **********************************************************************
 * 函数: TestGmcVertexGetRecordBuf(GmcStmtT *stmt, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo)
 * 功能: 获取vertex的record buf的地址和长度, 并且将buf内容解析到对应结构体中。
 * 参数:
 * stmt[in] -- 连接句柄。
 * structObj[out] -- 用户预先定义好的表对应的结构体, 要求按照 先定长字段, 再变长字段 的顺序来定义结构体(node节点直接忽略)。
 * c[out] -- 读取的数据中变长字段有值时, 自动申请内存存放变长字段内容, 并存放在此上下文变量中,
 *           用户从structObj中验证完数据后, 需手动调用deSeriFreeDynMem接口释放内存。
 * ********************************************************************** */
int32_t TestGmcVertexGetRecordBuf(GmcStmtT *stmt, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);

    uint8_t *vertexBuf = NULL;
    uint32_t vertexBufLen = 0;
    ret = GmcVertexGetRecordBuf(stmt, &vertexBuf, &vertexBufLen);
    RETURN_IFERR(ret);

    ret = GetNodeRecordBuf(vertexBuf, vertexBufLen, vertexLabel, vertexLabel->schema, c, structObj);
    RETURN_IFERR(ret);
    return 0;
}

int32_t GetNodeSchema(TestSchemaT *schema, const char *nodeName, TestSchemaT **nodeSchema)
{
    if (schema->nodeNum == 0) {
        return 0;
    }
    for (uint32_t i = 0; i < schema->nodeNum; i++) {
        if (0 == strcmp(schema->nodes[i].name, nodeName)) {
            *nodeSchema = schema->nodes[i].schema;
            return 0;
        }
        GetNodeSchema(schema->nodes[i].schema, nodeName, nodeSchema);
    }
    return 0;
}

/* **********************************************************************
 * 函数: TestGmcNodeGetNodeRecordBuf(GmcNodeT *node, const char *nodeName, void *structObj,
 * structTestCtx *c, TestLabelInfoT *labelInfo)
 * 功能: 获取当前node节点的record buf的地址和长度, 并且将buf内容解析到对应结构体中。
 * 参数:
 * node[in] -- 节点句柄。
 * structObj[out] -- 用户预先定义好的表对应的结构体, 要求按照 先定长字段, 再变长字段 的顺序来定义结构体(node节点直接忽略)。
 * c[out] -- 读取的数据中变长字段有值时, 自动申请内存存放变长字段内容, 并存放在此上下文变量中,
 *           用户从structObj中验证完数据后, 需手动调用deSeriFreeDynMem接口释放内存。
 * ********************************************************************** */
int32_t TestGmcNodeGetNodeRecordBuf(
    GmcNodeT *node, const char *nodeName, void *structObj, structTestCtx *c, TestLabelInfoT *labelInfo)
{
    TestVertexLabelT *vertexLabel;
    int ret = TestGetVertexLabelFromCtx(labelInfo, &vertexLabel);
    RETURN_IFERR(ret);
    TestSchemaT *nodeSchema;
    if (0 == strcmp(vertexLabel->topRecordName, nodeName)) {
        nodeSchema = vertexLabel->schema;
    } else {
        ret = GetNodeSchema(vertexLabel->schema, nodeName, &nodeSchema);
        RETURN_IFERR(ret);
    }

    uint8_t *nodeBuf = NULL;
    uint32_t nodeBufLen = 0;
    ret = GmcNodeGetNodeRecordBuf(node, &nodeBuf, &nodeBufLen);
    RETURN_IFERR(ret);

    ret = GetNodeRecordBuf(nodeBuf, nodeBufLen, vertexLabel, nodeSchema, c, structObj);
    RETURN_IFERR(ret);
    return 0;
}

#endif  // PRODUCT_USG_TS FEATURE_STREAM

/******************************************* 结构化 end *******************************************/

long readJanssonFile(const char *path, char **buf, bool convertArray, char *nsName, bool needStructParse)
{
    char fileName[256];
#if defined RUN_DATACOM_HPE
    (void)snprintf(fileName, sizeof(fileName), "%s/%s", g_programPath, path);
#else
    (void)snprintf(fileName, sizeof(fileName), "%s", path);
#endif
    FILE *fp;
    fp = fopen(fileName, "rb");
    if (NULL == fp) {
        printf("[readJanssonFile] open file:%s fail.\n", fileName);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[readJanssonFile] fseek file:%s to end failed.\n", fileName);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        printf("[readJanssonFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }

    char *pBuffer = (char *)malloc(size + 8);
    if (pBuffer == NULL) {
        printf("[readJanssonFile] malloc memory:%ld for file:%s failed.\n", size + 4, fileName);
        fclose(fp);
        return -1;
    }
    int i = 0;
    if (convertArray) {
        i = 1;
        pBuffer[0] = ' ';
    }
    fseek(fp, 0L, SEEK_SET);
    long readSize = fread(pBuffer + i, 1, size, fp);
    if (readSize != size) {
        printf("[readJanssonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", fileName, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    fclose(fp);
    if (convertArray) {
        for (; i < size; ++i) {
            if (pBuffer[i] == ' ') {
                continue;
            }
            if (pBuffer[i] == '{') {
                pBuffer[0] = '[';
                pBuffer[size + 1] = ']';
                pBuffer[size + 2] = '\0';
            } else {
                pBuffer[size + 1] = '\0';
            }
            break;
        }
    } else {
        pBuffer[size] = '\0';
    }
    if (needStructParse) {
        int ret = TestGetVertexLabelFromSchema(pBuffer, nsName);
        if (ret != 0) {
            // 不符合json格式, 解析失败, 不返回错误给用户
        }
    }
    *buf = pBuffer;
    return size;
}

void RestartWhenFailed()
{
    const testing::TestCase *testCase = testing::UnitTest::GetInstance()->current_test_case();
    if (testCase == NULL) {
        return;
    }
    int passedCount = 0;
    passedCount = testCase->successful_test_count();
    if (passedCount == 0) {
        (void)system("sh $TEST_HOME/tools/stop.sh -f");
    }
}

int findKeyword(const char *cmd, const char *keyword)
{
    FILE *fp;
    char line[256];
    bool found = false;
    fp = popen(cmd, "r");
    if (fp == NULL) {
        perror("popen");
        return T_FAILED;
    }
    while (fgets(line, sizeof(line), fp)) {
        if (strstr(line, keyword) != NULL) {
            found = true;
            break;
        }
    }
    pclose(fp);
    if (found) {
        AW_FUN_Log(LOG_ERROR, "[CheckWhenSucc] There is %s that has not been dropped.", keyword);
        system(cmd);
        (void)system("sh $TEST_HOME/tools/stop.sh -f");
        assert(0);
        return T_FAILED;
    } else {
        return GMERR_OK;
    }
}

int CheckWhenSucc()
{
    const testing::TestCase *testCase = testing::UnitTest::GetInstance()->current_test_case();
    if (testCase == NULL) {
        return T_FAILED;
    }
    int passedCount = 0, tryCnt = 0;
    passedCount = testCase->successful_test_count();
    if (passedCount != 0) {
        int tbmTbl = 0xff, msgNotifyTbl = 0xff, edgeLabel = 0xff, sub = 0xff, kvTbl = 0xff, resPool = 0xff, nsp = 0xff,
            udf = 0xff;
        bool clearFlag = (tbmTbl > 0 || msgNotifyTbl > 0 || edgeLabel > 0 || sub > 0 || kvTbl > 1 || resPool > 0 ||
                          nsp > 3 || udf > 0);
        while (tryCnt < 50 && clearFlag) {  // clearFlag为true: 有资源未清理
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"TBM_TABLE_NUM\" | awk '{print $2}'", &tbmTbl));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"MSG_NOTIFY_TABLE_NUM\" | awk '{print $2}'",
                &msgNotifyTbl));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"EDGE_LABEL_NUM\" | awk '{print $2}'", &edgeLabel));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"LABEL_SUBS_NUM\" | awk '{print $2}'", &sub));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"KV_TABLE_NUM\" | awk '{print $2}'", &kvTbl));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"RESOURCE_POOL_NUM\" | awk '{print $2}'", &resPool));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"NAMESPACE_NUM\" | awk '{print $2}'", &nsp));
            RETURN_IFERR(TestGetResultCommand(
                "gmsysview -q V\\$CATA_GENERAL_INFO | grep -w \"UDF_NUM\" | awk '{print $2}'", &udf));
            AW_FUN_Log(LOG_INFO,
                "tbmTbl:%d, msgNotifyTbl:%d, edgeLabel:%d, sub:%d, kvTbl:%d, resPool:%d, "
                "nsp:%d, udf:%d",
                tbmTbl, msgNotifyTbl, edgeLabel, sub, kvTbl, resPool, nsp, udf);
            clearFlag = (tbmTbl > 0 || msgNotifyTbl > 0 || edgeLabel > 0 || sub > 0 || kvTbl > 1 || resPool > 0 ||
                         nsp > 3 || udf > 0);
            if (!clearFlag) {
                break;
            } else {
                tryCnt++;
                usleep(10000);
                continue;
            }
        }
        if (clearFlag) {
            AW_FUN_Log(LOG_ERROR, "[CheckWhenSucc] Some resources were not cleared. Expect: tbmTbl=0, msgNotifyTbl=0, "
                                  "edgeLabel=0, sub=0, kvTbl=1, resPool=0, nsp=3, udf=0.");
            system("gmsysview -q V\\$CATA_GENERAL_INFO");
            (void)system("sh $TEST_HOME/tools/stop.sh -f");
            assert(0);
            return T_FAILED;
        }
        // 检测是否有表残留
        int ret = findKeyword("gmsysview -q V\\$STORAGE_VERTEX_COUNT", "table");
        if (ret != 0) {
            return ret;
        }
    }
    return GMERR_OK;
}

int TestWaitRsmRecoverFinish(int timeout)
{
    char cmd[200] = {0};
    int ret;
    int timeoutUsec = timeout * 1000 * 1000;
    (void)snprintf(cmd, sizeof(cmd), "gmsysview -q 'V$QRY_RECOVERY_TASK_STAT' -s %s|grep 'IS_RECOVERY_FINISHED: 1$'", g_connServer);
    while(true){
        ret = GtExecSystemCmd(cmd);
        if (ret == T_OK) {
            break;
        }
        // 10ms
        usleep(10000);
        timeoutUsec = timeoutUsec - 10000;
        if(timeoutUsec == 0){
            AW_FUN_Log(LOG_STEP, "WaitRsmRecoverFinish time out.");
            return T_FAILED;
        }
    }
    return ret;
}

/******************************************* client Register Signal start *******************************************/
char g_cliCrashLogPath[512] = "";
char g_procName[32] = "";
uint32_t g_serverPid = 0;

void TestWriteLog(LogLevelID level, const char *format, ...)
{
    if (level < LOG_STEP || level >= LOG_BUTT) {
        printf("[error] wrong parameter[0]. it is not in [LOG_STEP, LOG_INFO, LOG_WARN, LOG_ERROR, LOG_DEBUG, "
               "LOG_DEBUG_ALL].\n");
        return;
    }
    if (level > g_logLevelWillPrint) {
        // 打印级别高于预设级别，不打印日志
        return;
    }
    if (format == NULL) {
        printf("[error] wrong parameter[1]. format is NULL.\n");
        return;
    }
    char pName[32] = {0};
    prctl(PR_GET_NAME, pName);
    struct timespec ct;
    clock_gettime(CLOCK_REALTIME, &ct);
    tzset();
    struct tm t;
    localtime_r(&ct.tv_sec, &t);
    char logText[256];
    (void)snprintf(logText, sizeof(logText), "%04d-%02d-%02d %02d:%02d:%02d.%03ld [%s:%lu] [%s]  ", t.tm_year + 1900,
        t.tm_mon + 1, t.tm_mday, t.tm_hour, t.tm_min, t.tm_sec, ct.tv_nsec / 1000000, pName, pthread_self(),
        g_logLevelName[level]);
    int size = strlen(logText);
    va_list args;
    va_start(args, format);
    int ret = vsnprintf(logText + size, sizeof(logText) - size - 2, format, args);
    if (ret <= 0) {
        printf("[error] vsnprintf failed, ret = %d\n", ret);
        va_end(args);
        return;
    }
    va_end(args);
    size = strlen(logText);
    logText[size] = '\n';
    logText[size + 1] = '\0';

    WriteTestLog(level, pName, logText);
}

void TestUitoa(unsigned int uVal, char *str, int strSize)
{
    int i = 0;
    int val = uVal;
    do {
        str[i++] = '0' + (val % 10);
        val /= 10;
    } while (val > 0 && i < (strSize - 1));

    str[i] = '\0';
    int len = i;
    int j = 0;

    while (j < len / 2) {
        char temp = str[j];
        str[j] = str[len - j - 1];
        str[len - j - 1] = temp;
        j++;
    }
}

int TestWriteLogSignalSecure(const char *content)
{
    int fd;
    if ((fd = open(g_cliCrashLogPath, O_CREAT | O_WRONLY | O_APPEND | O_SYNC, 0666)) < 0) {
        printf("open %s failed.\n", g_cliCrashLogPath);
        return -1;
    };

    struct timespec ct;
    (void)clock_gettime(CLOCK_REALTIME, &ct);
    char timeBuf[11] = {0};
    TestUitoa(ct.tv_sec, timeBuf, 11);

    char pidStr[11] = {0};
    TestUitoa(getpid(), pidStr, 11);

    char logText[256] = {0};
    int logLen = 0;
    while (timeBuf[logLen] != '\0' && logLen < 256 - 1) {
        logText[logLen] = timeBuf[logLen];
        logLen++;
    }
    logText[logLen] = ' ';
    logLen++;

    int pidLen = 0;
    while (pidStr[pidLen] != '\0' && logLen < 256 - 1) {
        logText[logLen] = pidStr[pidLen];
        pidLen++;
        logLen++;
    }
    logText[logLen] = ' ';
    logLen++;

    int contentLen = 0;
    while ((content[contentLen] != '\0') && logLen < 256 - 1) {
        logText[logLen] = content[contentLen];
        contentLen++;
        logLen++;
    }
    logText[logLen] = '\0';

    if (write(fd, logText, logLen) < 0) {
        printf("write logText failed.\n");
        close(fd);
        return -2;
    }

    close(fd);
    return 0;
}

void TestExecve()
{
    const char *scriptPath = "./stopLongStability.sh";
    if (!access(scriptPath, F_OK)) {
        char *argv[] = {(char *)"sh", (char *)"stopLongStability.sh", g_procName, NULL};
        char *envp[] = {NULL};
        int ret = execve("/bin/sh", argv, envp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

bool TestIsHpeEnv(void)
{
#ifdef RUN_DATACOM_HPE
    TestWriteLog(LOG_INFO, "current env is hpe.");
    return true;
#else
    return false;
#endif
}

typedef void (*TestCrashHandlerFunc)(int signo, siginfo_t *info, void *context);

bool IsUserSendSignal(siginfo_t *info)
{
    if (info == NULL) {
        return false;
    }
    return (info->si_code == SI_USER) && (info->si_pid != getpid());
}

void TestCrashHandler(int signo, siginfo_t *info, void *context)
{
    GmcSignalInfoT dbSignalInfo = {
        .signalNum = signo,
        .reserved = 0,
        .sigInfo = info,
        .signalCon = context,
    };

    GmcSignalOutputDataT output = {0};
    int32_t ret = GmcCrashHandlerHook(&dbSignalInfo, &output);
    if (ret != GMERR_OK) {
        ret = TestWriteLogSignalSecure("GmcCrashHandlerHook failed.\n");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        TestExecve();
        // SIGABORT 对应6
        ret = kill(g_serverPid, SIGABRT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        abort();
        return;
    }

    switch (output.policy) {
        case 0: {
            // 如果是内部信号导致，重新raise出去，如果是用例发送信号，用SIGTERM退出客户端
            ret = TestWriteLogSignalSecure("policy zero exit.\n");
            TEST_EXPECT_INT32(GMERR_OK, ret);
            bool isUserSend = IsUserSendSignal(info);
            (void)raise(isUserSend ? SIGTERM : signo);
            break;
        }
        case 2:
            ret = TestWriteLogSignalSecure("policy two exit.\n");
            TEST_EXPECT_INT32(GMERR_OK, ret);
            TestExecve();
            // SIGABORT 对应6
            ret = kill(g_serverPid, SIGABRT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            (void)raise(signo);
            break;
        case 1:
            ret = TestWriteLogSignalSecure("policy one exit.\n");
            TEST_EXPECT_INT32(GMERR_OK, ret);
        default:
            break;
    }
    return;
}

int DbCrashHandler(GmcSignalInfoT *signalInfo, GmcSignalOutputDataT *outputData)
{
    return GmcCrashHandlerHook(signalInfo, outputData);
}

int32_t TestRegisterLinuxSignalProtect(TestCrashHandlerFunc crashHandler)
{
    struct sigaction act;
    act.sa_sigaction = crashHandler;
    act.sa_flags = (int)(SA_SIGINFO | SA_RESETHAND);
    // pssp接管信号范围，内部信号SIGILL, SIGABRT, SIGBUS, SIGFPE
    int32_t signalList[] = {
        SIGHUP, SIGINT, SIGQUIT, SIGILL, SIGABRT, SIGBUS, SIGFPE, SIGSEGV, SIGXCPU, SIGXFSZ, SIGSYS};
    uint32_t listSize = (uint32_t)(sizeof(signalList) / sizeof(int32_t));
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < listSize; i++) {
        int32_t signo = signalList[i];
        ret = sigaction(signo, &act, NULL);
        if (ret != GMERR_OK) {
            TestWriteLog(LOG_ERROR, "Register signal unsucc, signo: %" PRId32 ".", signo);
            return ret;
        }
    }
    return GMERR_OK;
}

// 根据进程名字，获取进程id
int GetProcessIdByName(const char *proName)
{
    int pid = 0;
    char cmd[1024] = {0};
#if defined(ENV_RTOSV2X) && defined(RUN_DATACOM_DAP)
    (void)snprintf(cmd, 1024, "ps -l | grep %s | grep -v defunct | grep -v grep | awk '{print $3}'", proName);
#else
    (void)snprintf(cmd, 1024, "ps -ef | grep %s | grep -v defunct | grep -v grep | awk '{print $2}'", proName);
#endif
    (void)TestGetResultCommand(cmd, &pid);
    return pid;
}

static bool g_dopraInit = false;
typedef unsigned int (*DopraVosCfgDbInitByFsldr)(const char *cfgFile);
typedef unsigned int (*DopraVosSysProcIdSet)(unsigned int uiProcId);
typedef unsigned int (*DopraVosStart)(void);
typedef unsigned long (*DopraVosMemPtCreateEx)(
    const char *pscPtName, const char *pscAlgoName, void *pPara, void *pAddr, size_t uvSize, unsigned char *pucPtNo);

int TestInitDopra(char *adapterLib)
{
    if (g_dopraInit) {
        return 0;
    }

    TestWriteLog(LOG_INFO, "adapterLib = %s .", adapterLib);
    void *adpt = dlopen(adapterLib, RTLD_GLOBAL | RTLD_NOW);
    if (adpt == NULL) {
        TestWriteLog(LOG_ERROR, "[InitDopra] dlopen(%s) failed, err:%s.", adapterLib, dlerror());
        return 1;
    }

    char adapterCfg[128];
    const char *testHome = getenv("TEST_HOME");
    if (testHome == NULL) {
        TestWriteLog(LOG_INFO, "[InitDopra] getenv TEST_HOME failed.");
        return -1;
    }

    unsigned int ret = 1;
    do {
        DopraVosCfgDbInitByFsldr funEntry1 = (DopraVosCfgDbInitByFsldr)dlsym(adpt, "VOS_CfgdbInitByFsldr");
        if (funEntry1 == NULL) {
            TestWriteLog(LOG_ERROR, "[InitDopra] dlsym(VOS_CfgdbInitByFsldr) failed.");
            break;
        }
#if defined(ENV_RTOSV2X) && defined(RUN_DATACOM_DAP)
        snprintf(adapterCfg, sizeof(adapterCfg), "%s/../conf/lbas_dopra.cfg", g_hpeLibDir);
#else
        snprintf(adapterCfg, sizeof(adapterCfg), "%s/cfg/test_signal_dopra.cfg", testHome);
#endif
        TestWriteLog(LOG_INFO, "adapterCfg = %s .", adapterCfg);
        ret = funEntry1(adapterCfg);
        if (ret != 0) {
            TestWriteLog(LOG_INFO, "[InitDopra] VOS_CfgdbInitByFsldr(%s) failed, ret = %d.", adapterCfg, ret);
            break;
        }

        DopraVosSysProcIdSet funEntry2 = (DopraVosSysProcIdSet)dlsym(adpt, "VOS_SysProcIdSet");
        if (funEntry2 == NULL) {
            TestWriteLog(LOG_ERROR, "[InitDopra] dlsym(VOS_SysProcIdSet) failed.");
            break;
        }
        int32_t offSet = 20000;
        int32_t v8_Location_Id = getpid() + offSet;
        if (v8_Location_Id < offSet) {
            TestWriteLog(LOG_ERROR, "[InitDopra] getpid() failed, v8_Location_Id = %d.", v8_Location_Id);
            break;
        }
        ret = funEntry2(v8_Location_Id);
        if (ret != 0) {
            TestWriteLog(LOG_ERROR, "[InitDopra] VOS_SysProcIdSet() failed, ret = %d.", ret);
            break;
        }

        DopraVosStart funEntry3 = (DopraVosStart)dlsym(adpt, "VOS_Start");
        if (funEntry3 == NULL) {
            TestWriteLog(LOG_ERROR, "[InitDopra] dlsym(VOS_Start) failed.");
            break;
        }
        ret = funEntry3();
        if (ret != 0) {
            TestWriteLog(LOG_ERROR, "[InitDopra] VOS_Start() failed, ret = %d.", ret);
            break;
        }

        DopraVosMemPtCreateEx funEntry4 = (DopraVosMemPtCreateEx)dlsym(adpt, "VOS_MemPtCreateEx");
        if (funEntry4 == NULL) {
            TestWriteLog(LOG_ERROR, "[InitDopra] dlsym(VOS_MemPtCreateEx) failed.");
            break;
        }

        uint8_t dopraPtNo = 0;
        const char *memAlgo = "HIM";
        ret = funEntry4("test_signal", memAlgo, 0, 0, 512 * 1024, &dopraPtNo);
        if (ret != 0) {
            TestWriteLog(LOG_ERROR, "[InitDopra] VOS_MemPtCreateEx() failed, ret = %d.", ret);
            break;
        }

        ret = 0;
    } while (0);

    g_dopraInit = true;
    return ret;
}

static bool g_patchDopraInit = false;
typedef uint32_t (*TestDopraPatchInit)(void);

int TestInitPatchDopra()
{
    if (g_patchDopraInit) {
        return 0;
    }

    char patchDopraLib[512];
    memset(patchDopraLib, 0, sizeof(patchDopraLib));
    (void)snprintf(patchDopraLib, 512, "%s/libpatch_dopra.so", g_hpeLibDir);
    TestWriteLog(LOG_INFO, "patchDopraLib = %s .", patchDopraLib);
    void *patchDopraHandle = dlopen(patchDopraLib, RTLD_LAZY | RTLD_NODELETE);
    if (patchDopraHandle == NULL) {
        TestWriteLog(LOG_ERROR, "[InitPatchDopra] dlopen(%s) failed, err:%s.", patchDopraLib, dlerror());
        return 1;
    }

    unsigned int ret = 1;

    do {
        TestDopraPatchInit funEntry1 = (TestDopraPatchInit)dlsym(patchDopraHandle, "SISP_PAT_DopraPatchInit");
        if (funEntry1 == NULL) {
            TestWriteLog(LOG_ERROR, "[TestInitPatchDopra] dlsym(SISP_PAT_DopraPatchInit) failed.");
            break;
        }

        ret = funEntry1();
        if (ret != 0) {
            TestWriteLog(LOG_INFO, "[TestInitPatchDopra] SISP_PAT_DopraPatchInit() failed, ret = %d.", ret);
            break;
        }
        ret = 0;
    } while (0);

    g_patchDopraInit = true;
    return ret;
}

static bool g_libPatchAppInit = false;
typedef struct tagSispPatchModuleInitPara {
    char acProcName[32];
} SISP_PAT_MODULE_INIT_PARA_S;
typedef uint32_t (*TestPatchModuleInit)(SISP_PAT_MODULE_INIT_PARA_S *modulePara);

int TestInitLibPatchApp()
{
    if (g_libPatchAppInit) {
        return 0;
    }

    char libPatchApp[512];
    memset(libPatchApp, 0, sizeof(libPatchApp));
    (void)snprintf(libPatchApp, 512, "%s/libpatch_app_interaction.so", g_hpeLibDir);
    TestWriteLog(LOG_INFO, "libPatchApp = %s .", libPatchApp);
    void *libPatchAppHandle = dlopen(libPatchApp, RTLD_LAZY | RTLD_NODELETE);
    if (libPatchAppHandle == NULL) {
        TestWriteLog(LOG_ERROR, "[TestInitLibPatchApp] dlopen(%s) failed, err:%s.", libPatchApp, dlerror());
        return 1;
    }

    unsigned int ret = 1;

    do {
        TestPatchModuleInit funEntry1 = (TestPatchModuleInit)dlsym(libPatchAppHandle, "SISP_PAT_ModuleInit");
        if (funEntry1 == NULL) {
            TestWriteLog(LOG_ERROR, "[TestInitLibPatchApp] dlsym(SISP_PAT_ModuleInit) failed.");
            break;
        }

        SISP_PAT_MODULE_INIT_PARA_S testModuleInitPara;
        ret = sprintf_s(testModuleInitPara.acProcName, sizeof(testModuleInitPara.acProcName), "%s", g_procName);
        if (ret <= 0) {
            TestWriteLog(LOG_ERROR, "[TestInitLibPatchApp] sprintf_s worthless, ret = %d, module %s", ret, g_procName);
            break;
        }

        ret = funEntry1(&testModuleInitPara);
        if (ret != 0) {
            TestWriteLog(LOG_INFO, "[TestInitLibPatchApp] SISP_PAT_ModuleInit() failed, ret = %d.", ret);
            break;
        }
        ret = 0;
    } while (0);

    g_libPatchAppInit = true;
    return ret;
}

static bool g_psspAgentInit = false;
typedef int32_t (*DbSignalHook)(GmcSignalInfoT *signalInfo, GmcSignalOutputDataT *output);
typedef int32_t (*PsspRegSigHandlerHook)(DbSignalHook dbSignalHookFunc);
typedef int32_t (*PsspAgentStart)(const char *cfgFile);

int TestInitPsspAgent(DbSignalHook dbSignalHookFunc)
{
    if (g_psspAgentInit) {
        return 0;
    }

    char psspAgentLib[512] = "";
    memset(psspAgentLib, 0, sizeof(psspAgentLib));
    (void)snprintf(psspAgentLib, 512, "%s/libpssp_agent.so", g_hpeLibDir);
    TestWriteLog(LOG_INFO, "psspAgentLib = %s .", psspAgentLib);
    void *psspAgentHandle = dlopen(psspAgentLib, RTLD_LAZY | RTLD_NODELETE);
    if (psspAgentHandle == NULL) {
        TestWriteLog(LOG_ERROR, "[initPsspAgent] dlopen(%s) failed, err:%s.", psspAgentLib, dlerror());
        return 1;
    }

    PsspAgentStart agentStartFunc = (PsspAgentStart)dlsym(psspAgentHandle, "PSSP_AgentStart");
    if (agentStartFunc == NULL) {
        AW_FUN_Log(LOG_ERROR, "[initPsspAgent] dlsym(PSSP_AgentStart) failed, err:%s.", psspAgentLib, dlerror());
        return 1;
    }
    unsigned int ret = agentStartFunc(NULL);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "[initPsspAgent] PSSP_AgentStart() failed, ret = %d.", ret);
        return ret;
    }

    ret = 1;
    do {
        PsspRegSigHandlerHook funEntry1 = (PsspRegSigHandlerHook)dlsym(psspAgentHandle, "PSSP_RegSigHandlerHook");
        if (funEntry1 == NULL) {
            TestWriteLog(LOG_ERROR, "[initPsspAgent] dlsym(PSSP_RegSigHandlerHook) failed.");
            break;
        }

        ret = funEntry1(dbSignalHookFunc);
        if (ret != 0) {
            TestWriteLog(LOG_ERROR, "[initPsspAgent] PSSP_RegSigHandlerHook() failed, ret = %d.", ret);
            break;
        }
        ret = 0;
    } while (0);

    g_psspAgentInit = true;
    return ret;
}

// rtosv2x init dopra
#define DOPRA_START_IS_OK "VOS_StarterIsOK"
typedef bool (*DopraStarterIsOKFunc)();
int V2xInitDopra()
{
    if (g_dopraInit) {
        return 0;
    }
    char adapterLib[512] = "";
    memset(adapterLib, 0, sizeof(adapterLib));
    (void)snprintf(adapterLib, 512, "%s/../../../lib/libdopra.so", g_hpeLibDir);
    TestWriteLog(LOG_INFO, "adapterLib = %s .", adapterLib);
    void *adpt = dlopen(adapterLib, RTLD_LAZY | RTLD_NODELETE);
    if (adpt == NULL) {
        TestWriteLog(LOG_ERROR, "[V2xInitDopra] dlopen(libdopra.so) failed, err:%s.", dlerror());
        return 1;
    }

    unsigned int ret = 1;
    do {
        DopraStarterIsOKFunc funEntry1 = (DopraStarterIsOKFunc)dlsym(adpt, DOPRA_START_IS_OK);
        if (funEntry1 == NULL) {
            TestWriteLog(LOG_ERROR, "[V2xInitDopra] dlsym(VOS_StarterIsOK) failed.");
            break;
        }

        bool ret1 = funEntry1();
        if (ret1) {
            TestWriteLog(LOG_INFO, "[V2xInitDopra] VOS_StarterIsOK failed, ret = %d.", ret1);
            break;
        }

        ret = 0;
    } while (0);

    g_dopraInit = true;
    return ret;
}
// rtosv2x reg singo
#define PSSP_EXCEPT_INIT_FUNC "PSSP_ExceptInit"
#define PSSP_SIGNAL_HANDLE_FUNC "PSSP_SignalInfoRecordHandler"
typedef uint32_t (*PsspExceptInitFunc)(void);
typedef void (*PsspSignalInfoRecordHandlerFunc)(int32_t signalNum, siginfo_t *signalInfo, void *signalCon);
int V2xRegSingo()
{
    if (g_psspAgentInit) {
        return 0;
    }

    char psspAgentLib[512] = "";
    memset(psspAgentLib, 0, sizeof(psspAgentLib));
    (void)snprintf(psspAgentLib, 512, "%s/libpssp_agent.so", g_hpeLibDir);
    TestWriteLog(LOG_INFO, "psspAgentLib = %s .", psspAgentLib);
    void *psspAgentHandle = dlopen(psspAgentLib, RTLD_LAZY | RTLD_NODELETE);
    if (psspAgentHandle == NULL) {
        TestWriteLog(LOG_ERROR, "[V2xRegSingo] dlopen(%s) failed, err:%s.", psspAgentLib, dlerror());
        return 1;
    }

    int ret = 1;
    do {
        PsspExceptInitFunc exceptInitFunc = (PsspExceptInitFunc)dlsym(psspAgentHandle, PSSP_EXCEPT_INIT_FUNC);
        if (exceptInitFunc == NULL) {
            TestWriteLog(LOG_ERROR, "[V2xRegSingo] dlsym(PSSP_ExceptInit) failed, err:%s.", psspAgentLib, dlerror());
            break;
        }
        ret = exceptInitFunc();
        if (ret != 0) {
            TestWriteLog(LOG_ERROR, "[V2xRegSingo] PSSP_ExceptInit() failed, ret = %d.", ret);
            break;
        }

        ret = 1;
        do {
            PsspRegSigHandlerHook funEntry1 = (PsspRegSigHandlerHook)dlsym(psspAgentHandle, "PSSP_RegSigHandlerHook");
            if (funEntry1 == NULL) {
                TestWriteLog(LOG_ERROR, "[V2xRegSingo] dlsym(PSSP_RegSigHandlerHook) failed.");
                break;
            }

            ret = funEntry1(DbCrashHandler);
            if (ret != 0) {
                TestWriteLog(LOG_ERROR, "[V2xRegSingo] PSSP_RegSigHandlerHook() failed, ret = %d.", ret);
                break;
            }
            ret = 0;
        } while (0);
    } while (0);

    g_psspAgentInit = true;
    return ret;
}

int TestTryRegisterSignal(TestCrashHandlerFunc crashHandler)
{
    if (TestIsHpeEnv()) {
        return 1;
    }

    int ret = GMERR_OK;

    char command[1024] = {0};
    (void)snprintf(command, 1024, "cat /proc/%u/comm | awk '{print $1}'", getpid());
    ret = TestGetResultCommand(command, NULL, g_procName, sizeof(g_procName));
    if (ret != 0) {
        TestWriteLog(
            LOG_ERROR, "get current client name failed, cliName = %s, ret = %d, pid is %u.", g_procName, ret, getpid());
        return 1;
    };
    TestWriteLog(LOG_INFO, "current client name is %s.", g_procName);

    char adapterLib[512];
    memset(adapterLib, 0, sizeof(adapterLib));
// rtosv2 设备信号注册
#if defined(ENV_RTOSV2) && defined(RUN_DATACOM_DAP)
    (void)snprintf(g_cliCrashLogPath, 512, "%s/longclientexit.log", g_serverLogDir);
    (void)snprintf(adapterLib, 512, "%s/libdopra.so", g_hpeLibDir);
    ret = TestInitDopra(adapterLib);
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] TestInitDopra failed ret = %d.", ret);
        return ret;
    }

    ret = TestInitPatchDopra();
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] TestInitPatchDopra failed ret = %d.", ret);
        return ret;
    }

    ret = TestInitLibPatchApp();
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] TestInitLibPatchApp failed ret = %d.", ret);
        return ret;
    }

    ret = TestInitPsspAgent(DbCrashHandler);
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] TestInitPsspAgent(GmcCrashHandlerHook) failed ret = %d.", ret);
        return ret;
    }
    GmcSignalRegisterNotify();
    TestWriteLog(LOG_INFO, "%d rtosv2 register successfully.", getpid());
// euler/arm32/仿真信号注册
#elif defined(RUN_INDEPENDENT) && !defined(FEATURE_PERSISTENCE)
    (void)snprintf(g_cliCrashLogPath, 512, "%s", "./longclientexit.log");
    ret = TestRegisterLinuxSignalProtect(crashHandler);
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestRegisterLinuxSignalProtect] TestRegisterLinuxSignalProtect(crashHandler) failed ret = %d.", ret);
        return ret;
    }
    TestWriteLog(LOG_INFO, "%d linux register successfully.", getpid());

    g_serverPid = GetProcessIdByName("gmserver");
    if (g_serverPid <= 0) {
        TestWriteLog(LOG_ERROR, "get server pid failed, current pid is %u.", g_serverPid);
        return 1;
    };
    GmcSignalRegisterNotify();
    TestWriteLog(LOG_INFO, "server pid is %d.", g_serverPid);
#elif defined(ENV_RTOSV2X) && defined(RUN_DATACOM_DAP)
    GmcSignalRegisterNotify();
    (void)snprintf(g_cliCrashLogPath, 512, "%s/longclientexit.log", g_serverLogDir);
    (void)snprintf(adapterLib, 512, "%s/../../../lib/libdopra.so", g_hpeLibDir);
    ret = TestInitDopra(adapterLib);
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] V2xInitDopra failed ret = %d.", ret);
        return ret;
    }

    ret = V2xRegSingo();
    if (ret != GMERR_OK) {
        TestWriteLog(LOG_ERROR, "[TestTryRegisterSignal] V2xRegSingo failed ret = %d.", ret);
        return ret;
    }

    TestWriteLog(LOG_INFO, "%d rtosv2x register successfully.", getpid());
#else
    TestWriteLog(LOG_INFO, "No need to register signals.");
#endif
    return ret;
}
/******************************************* client Register Signal end *******************************************/

int TestGmcGetAlarmData(TestAlarmDataT *testAlarmData)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    RETURN_IFERR(ret);
    GmcAlarmDataT alarmData;
    memset(&alarmData, 0, sizeof(alarmData));
    ret = GmcGetAlarmData(stmt, GMC_ALARM_SUB_CONN_RING, &alarmData);
    RETURN_IFERR(ret);
    testAlarmData->subConnRingFailTimes = alarmData.failTimes;
    ret = testGmcDisconnect(conn, stmt);
    RETURN_IFERR(ret);
    return ret;
}
