#include "NewSub.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

class NewSubSys : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void NewSubSys::SetUpTestCase()
{}

void NewSubSys::TearDownTestCase()
{}

void NewSubSys::SetUp()
{}

void NewSubSys::TearDown()
{}

// 001.聚簇容器表构造系统共享内存不足(APP_SHM_SIZE)
TEST_F(NewSubSys, reliability_NewSubSys_001)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");

    // maxTotalShmSize >=（maxSeMem + maxSysShmSize + APP_SHM_SIZE）
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\" \"enableVerticalIsolation=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxSeMem=128\" \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/start.sh");
    testEnvInit();
    create_epoll_thread();

    // 校验视图字段'TOTAL_ALLOC_SIZE',观察内存是否回收
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=SeStMgPubSubMemCtx");

    const char *subjson = R"(
        {
            "name": "SubJson001",
            "label_name": "CLUSTERED_LABEL",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "persist":false,
            "priority": 1,
            "subs_type":"status_merge"
        })";

    int ret = 0;
    GmcConnT *g_conn = NULL, *conn_sub = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_sub = NULL;
    const char *subName = "NewSubName001";
    SnUserDataT *userData = NULL;
    testSnMallocUserData(&userData, 100, 0);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ClusteredContainer(g_stmt);

    GmcSubConfigT subinfo;
    subinfo.subsName = subName;
    subinfo.configJson = subjson;
    ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int i;
    int cyclenum = 1;
    int cyclenum2 = 1;
    signs = false;

    // 写入数据至链表共享内存耗尽
    for (i = cyclenum;; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "CLUSTERED_LABEL", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
#ifndef DIRECT_WRITE
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("Unexpected null value. |SE-PUBSUB| nodePtr is novalid");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("|SE-PUBSUB| nodePtr is novalid in direct write.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
            AW_FUN_Log(LOG_INFO, "Insert %d, memory is exhausted, check view 'TOTAL_ALLOC_SIZE'", i - 1);
            system(g_command);
            ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        cyclenum++;
    }

    // 删除
    for (i = cyclenum2; i < cyclenum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "CLUSTERED_LABEL", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0Value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "CLUSTERED_LABEL_INDEX");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cyclenum2++;
    }

    // 不消费，删除后校验内存
    AW_FUN_Log(LOG_INFO, "Delete %d, check view 'TOTAL_ALLOC_SIZE'", cyclenum2 - 1);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开始消费
    signs = true;

    // 校验推送次数
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, cyclenum - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预期内存下降 TOTAL_ALLOC_SIZE
    ret = GmcDeleteAllFast(g_stmt, "CLUSTERED_LABEL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 消费，删除后校验内存
    AW_FUN_Log(LOG_INFO, "Consumption, check view 'TOTAL_ALLOC_SIZE'");
    sleep(5);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "current modify %d.", userData->insertNum);
    userData->insertNum = 0;

    // 重新写入数据并消费
    int cyclenum3 = 1;
    for (i = 1;; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "CLUSTERED_LABEL", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
#ifndef DIRECT_WRITE
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("Unexpected null value. |SE-PUBSUB| nodePtr is novalid");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("|SE-PUBSUB| nodePtr is novalid in direct write.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
            AW_FUN_Log(LOG_INFO, "Insert %d, memory is exhausted", i - 1);
            break;
        }
        cyclenum3++;
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, cyclenum3 - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验两次写满数据误差
    int num = (cyclenum - cyclenum3);
    if (num != 0) {
        EXPECT_LT(abs(cyclenum - cyclenum3), 100);
    }

    testSnFreeUserData(userData);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "CLUSTERED_LABEL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    GmcDetachAllShmSeg();
    close_epoll_thread();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "END");
}

// 002.heap表构造系统共享内存不足
TEST_F(NewSubSys, reliability_NewSubSys_002)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");

    // maxTotalShmSize >=（maxSeMem + maxSysShmSize + APP_SHM_SIZE）
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableVerticalIsolation=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxSeMem=128\" \"maxSysShmSize=12\"");
    system("sh $TEST_HOME/tools/start.sh");
    testEnvInit();
    create_epoll_thread();

    // 校验视图字段'TOTAL_ALLOC_SIZE',观察内存是否回收
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=SeStMgPubSubMemCtx");

    const char *subjson = R"(
        {
            "name": "SubJson002",
            "label_name": "Heap",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "persist":false,
            "priority": 1,
            "subs_type":"status_merge"
        })";

    int ret = 0;
    GmcConnT *g_conn = NULL, *conn_sub = NULL;
    GmcStmtT *g_stmt = NULL, *stmt_sub = NULL;
    const char *subName = "NewSubName002";
    SnUserDataT *userData = NULL;
    testSnMallocUserData(&userData, 100, 0);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    HeapCreate(g_stmt);

    GmcSubConfigT subinfo;
    subinfo.subsName = subName;
    subinfo.configJson = subjson;
    ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不消费
    signs = false;

    // 写入数据至链表共享内存耗尽
    int i = 0;
    int cyclenum = 1;
    int cyclenum2 = 1;
    const char *str = "test";
    for (i = cyclenum;; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Heap", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
#ifndef DIRECT_WRITE
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("Unexpected null value. |SE-PUBSUB| nodePtr is novalid");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("|SE-PUBSUB| nodePtr is novalid in direct write.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
            AW_FUN_Log(LOG_INFO, "Insert %d, memory is exhausted, check view 'TOTAL_ALLOC_SIZE'", i - 1);
            system(g_command);
            ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        cyclenum++;
    }

    // 删除
    for (i = cyclenum2; i < cyclenum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Heap", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t f0Value = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &f0Value, sizeof(f0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "Heap_Index");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cyclenum2++;
    }

    // 不消费，删除后校验内存
    AW_FUN_Log(LOG_INFO, "Delete %d, check view 'TOTAL_ALLOC_SIZE'", cyclenum2 - 1);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开始消费
    signs = true;

    // 校验推送次数
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_DELETE, cyclenum - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预期内存下降 TOTAL_ALLOC_SIZE
    ret = GmcDeleteAllFast(g_stmt, "Heap");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 消费，删除后校验内存
    AW_FUN_Log(LOG_INFO, "Consumption, check view 'TOTAL_ALLOC_SIZE'");
    sleep(5);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "current modify %d.", userData->insertNum);
    userData->insertNum = 0;

    // 重新写入数据并消费
    int cyclenum3 = 1;
    for (i = 1;; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, "Heap", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
#ifndef DIRECT_WRITE
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("Unexpected null value. |SE-PUBSUB| nodePtr is novalid");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNEXPECTED_NULL_VALUE, ret);
            ret = testGmcGetLastError("|SE-PUBSUB| nodePtr is novalid in direct write.");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
            AW_FUN_Log(LOG_INFO, "Insert %d, memory is exhausted", i - 1);
            break;
        }
        cyclenum3++;
    }

    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_MODIFY, cyclenum3 - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验两次写满数据误差
    int num = (cyclenum - cyclenum3);
    if (num != 0) {
        EXPECT_LT(abs(cyclenum - cyclenum3), 100);
    }

    testSnFreeUserData(userData);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "Heap");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    GmcDetachAllShmSeg();
    close_epoll_thread();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "END");
}

class NewSubProtect : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

static struct sigaction oldact;
struct sigaction act;
uint32_t g_expectPolicy = 0;
uint32_t g_count = 0;
void TestCrashHandler(int signo, siginfo_t *info, void *context)
{
    GmcSignalInfoT dbSignalInfo = {
        .signalNum = signo,
        .reserved = 0,
        .sigInfo = info,
        .signalCon = context,
    };
    GmcSignalOutputDataT output = {0};
    int32_t ret = GmcCrashHandlerHook(&dbSignalInfo, &output);
    EXPECT_EQ(GMERR_OK, ret);
    // 内部线程接管外部信号，有临界区操作，先走policy 1，然后走policy 0
    g_count++;
    if (g_count == 1) {
        EXPECT_EQ(g_expectPolicy, output.policy);
    } else {
        EXPECT_EQ(0, output.policy);
    }
}

void NewSubProtect::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/start.sh");
    testEnvInit();
    create_epoll_thread();
    int ret = 0;
    struct sigaction act;
    act.sa_sigaction = TestCrashHandler;
    act.sa_flags = SA_SIGINFO;
    ret = sigaction(SIGSEGV, &act, &oldact);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = sigaction(SIGBUS, &act, &oldact);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = sigaction(SIGFPE, &act, &oldact);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void NewSubProtect::TearDownTestCase()
{
    int ret = 0;
    GmcDetachAllShmSeg();
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void NewSubProtect::SetUp()
{}
void NewSubProtect::TearDown()
{}

void *KillPro15(void *args)
{
    int pidval = 0;

    // 获取进程pid
    GetPrintBycmd("ps -e | grep 'gmserver' | awk '{print $1}'", &pidval, NULL, 0);
    system("ps -e | grep 'gmserver'");
    AW_FUN_Log(LOG_INFO, "Pid is %d", pidval);

    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "kill -15 %d", pidval);
    system(g_command);
    return NULL;
}
void *VertexDml(void *args)
{
    int ret = 0;
    signs = true;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    testSnMallocUserData(&userData, 100, 0);
    const char *subName = "NewSubName003";
    const char *subjson = R"(
        {
            "name": "SubJson003",
            "label_name": "vertex",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "persist":false,
            "priority": 1,
            "subs_type":"status_merge"
        })";

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateVertex(g_stmt);
    VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

    GmcSubConfigT subinfo;
    subinfo.subsName = subName;
    subinfo.configJson = subjson;
    ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
    if (ret != GMERR_OK) {
        // 并发服务端异常退出
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }

    // DML
    ret = GmcBeginCheck(g_stmt, "vertex", 0xff);
    if (ret != GMERR_OK) {
        // 并发服务端异常退出
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
    VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
    VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
    VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

    ret = GmcEndCheck(g_stmt, "vertex", 0xff, false);
    if (ret != GMERR_OK) {
        // 并发服务端异常退出
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }

    // 校验推送消息
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcUnSubscribe(g_stmt, subName);
    if (ret != GMERR_OK) {
        // 并发服务端异常退出
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, "vertex");
    if (ret != GMERR_OK) {
        // 并发服务端异常退出
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
    }
    ret = testSubDisConnect(conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
// 003.客户端进程异常退出(持久化订阅)
TEST_F(NewSubProtect, reliability_NewSubSys_003)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");

    pid_t pid, pid1, pid2;
    pid1 = fork();
    if (pid1 < 0) {
        AW_FUN_Log(LOG_INFO,"fork_error");
        exit(1);
    } else if (pid1 == 0) {
        int ret = 0;
        signs = true;
        GmcConnT *conn_sub = NULL;
        GmcStmtT *stmt_sub = NULL;
        SnUserDataT *userData = NULL;
        testSnMallocUserData(&userData, 100, 0);
        const char *subName = "NewSubName003";
        const char *subjson = R"(
            {
                "name": "SubJson003",
                "label_name": "vertex",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "persist":false,
                "priority": 1,
                "subs_type":"status_merge"
            })";

        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        CreateVertex(g_stmt);
        VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

        GmcSubConfigT subinfo;
        subinfo.subsName = subName;
        subinfo.configJson = subjson;
        ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DML
        ret = GmcBeginCheck(g_stmt, "vertex", 0xff);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
        VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
        VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
        VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

        ret = GmcEndCheck(g_stmt, "vertex", 0xff, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验推送消息
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testSnFreeUserData(userData);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "vertex");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "kill -15 %d", getppid());
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "END");
}

// 非持久化 persistent：false
void *VertexDml02(void *args)
{
    int ret = 0;
    signs = true;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    testSnMallocUserData(&userData, 100, 0);
    const char *subName = "NewSubName003";
    const char *subjson = R"(
        {
            "name": "SubJson003",
            "label_name": "vertex",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "persist":false,
            "priority": 1,
            "persistent":false,
            "subs_type":"status_merge"
        })";

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateVertex(g_stmt);
    VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

    GmcSubConfigT subinfo;
    subinfo.subsName = subName;
    subinfo.configJson = subjson;
    ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    ret = GmcBeginCheck(g_stmt, "vertex", 0xff);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
    VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
    VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
    VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

    ret = GmcEndCheck(g_stmt, "vertex", 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验推送消息
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "vertex");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
// 004.客户端进程异常退出(非持久化订阅)
TEST_F(NewSubProtect, reliability_NewSubSys_004)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");

    pid_t pid, pid1, pid2;
    pid1 = fork();
    if (pid1 < 0) {
        AW_FUN_Log(LOG_INFO,"fork_error");
        exit(1);
    } else if (pid1 == 0) {
        int ret = 0;
        signs = true;
        GmcConnT *conn_sub = NULL;
        GmcStmtT *stmt_sub = NULL;
        SnUserDataT *userData = NULL;
        testSnMallocUserData(&userData, 100, 0);
        const char *subName = "NewSubName003";
        const char *subjson = R"(
            {
                "name": "SubJson003",
                "label_name": "vertex",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "persist":false,
                "priority": 1,
                "persistent":false,
                "subs_type":"status_merge"
            })";

        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        CreateVertex(g_stmt);
        VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

        GmcSubConfigT subinfo;
        subinfo.subsName = subName;
        subinfo.configJson = subjson;
        ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DML
        ret = GmcBeginCheck(g_stmt, "vertex", 0xff);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
        VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
        VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
        VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

        ret = GmcEndCheck(g_stmt, "vertex", 0xff, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验推送消息
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testSnFreeUserData(userData);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "vertex");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "kill -15 %d", getppid());
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "END");
}

void *KillPro9(void *args)
{
    int pidval = 0;

    // 获取进程pid
    GetPrintBycmd("ps -e | grep 'gmserver' | awk '{print $1}'", &pidval, NULL, 0);
    AW_FUN_Log(LOG_INFO, "Pid is %d", pidval);

    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "kill -9 %d", pidval);
    system(g_command);
    return NULL;
}
void *VertexDml03(void *args)
{
    int ret = 0;
    signs = true;
    GmcConnT *conn_sub = NULL;
    GmcStmtT *stmt_sub = NULL;
    SnUserDataT *userData = NULL;
    testSnMallocUserData(&userData, 100, 0);
    const char *subName = "NewSubName003";
    const char *subjson = R"(
        {
            "name": "SubJson003",
            "label_name": "vertex",
            "comment":"status_merge",
            "type":"before_commit",
            "events":
                [
                    {"type":"initial_load", "msgTypes":["new object"]},
                    {"type":"modify", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "is_path":false,
            "retry":true,
            "persist":false,
            "priority": 1,
            "persistent":false,
            "subs_type":"status_merge"
        })";

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateVertex(g_stmt);
    VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

    GmcSubConfigT subinfo;
    subinfo.subsName = subName;
    subinfo.configJson = subjson;
    ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML
    VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
    VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
    VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
    VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

    // 校验推送消息
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testSnFreeUserData(userData);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "vertex");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
// 007.服务端进程异常退出(kill -9)
TEST_F(NewSubProtect, reliability_NewSubSys_007)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");
    int ret = 0;

    pthread_t thr_arr1;
    pthread_t thr_arr2;

    pthread_create(&thr_arr1, NULL, VertexDml, NULL);
    pthread_create(&thr_arr2, NULL, KillPro9, NULL);

    ret = pthread_join(thr_arr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(thr_arr2, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 008.客户端进程挂起
TEST_F(NewSubProtect, reliability_NewSubSys_008)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");

    pid_t pid, pid1, pid2;
    pid1 = fork();
    if (pid1 < 0) {
        AW_FUN_Log(LOG_INFO,"fork_error");
        exit(1);
    } else if (pid1 == 0) {
        int ret = 0;
        signs = true;
        GmcConnT *conn_sub = NULL;
        GmcStmtT *stmt_sub = NULL;
        SnUserDataT *userData = NULL;
        testSnMallocUserData(&userData, 100, 0);
        const char *subName = "NewSubName003";
        const char *subjson = R"(
            {
                "name": "SubJson003",
                "label_name": "vertex",
                "comment":"status_merge",
                "type":"before_commit",
                "events":
                    [
                        {"type":"initial_load", "msgTypes":["new object"]},
                        {"type":"modify", "msgTypes":["new object", "old object"]},
                        {"type":"delete", "msgTypes":["new object", "old object"]}
                    ],
                "is_path":false,
                "retry":true,
                "persist":false,
                "priority": 1,
                "persistent":false,
                "subs_type":"status_merge"
            })";

        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubConnect(&conn_sub, NULL, 1, g_epoll_reg_info, subName, &g_chanRingLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        CreateVertex(g_stmt);
        VertexDml(g_stmt, GMC_OPERATION_INSERT, 1);

        GmcSubConfigT subinfo;
        subinfo.subsName = subName;
        subinfo.configJson = subjson;
        ret = GmcSubscribe(g_stmt, &subinfo, conn_sub, NewSubCallBack, userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DML
        ret = GmcBeginCheck(g_stmt, "vertex", 0xff);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        VertexDml(g_stmt, GMC_OPERATION_INSERT, 2);
        VertexDml(g_stmt, GMC_OPERATION_REPLACE, 3);
        VertexDml(g_stmt, GMC_OPERATION_UPDATE, 2);
        VertexDml(g_stmt, GMC_OPERATION_DELETE, 2);

        ret = GmcEndCheck(g_stmt, "vertex", 0xff, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 校验推送消息
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_MODIFY, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitStMgSnRecv(userData, GMC_SUB_EVENT_INITIAL_LOAD, -1, -1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testSnFreeUserData(userData);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt, "vertex");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testSubDisConnect(conn_sub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "kill -19 %d", getppid());
        system(g_command);

        (void)snprintf(g_command, MAX_CMD_SIZE, "kill -18 %d", getppid());
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "END");
}

void *KillProRe(void *args)
{
    int pidval = 0;

    // 获取进程pid
    GetPrintBycmd("ps -e | grep 'gmserver' | awk '{print $1}'", &pidval, NULL, 0);
    AW_FUN_Log(LOG_INFO, "Pid is %d", pidval);

    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "kill -19 %d", pidval);
    system(g_command);

    sleep(1);

    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "kill -18 %d", pidval);
    system(g_command);
    return NULL;
}
// 009.服务端进程挂起
TEST_F(NewSubProtect, reliability_NewSubSys_009)
{
    AW_FUN_Log(LOG_STEP, "TEST START.");
    int ret = 0;

    pthread_t thr_arr1;
    pthread_t thr_arr2;

    pthread_create(&thr_arr1, NULL, VertexDml, NULL);
    pthread_create(&thr_arr2, NULL, KillProRe, NULL);

    ret = pthread_join(thr_arr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(thr_arr2, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

