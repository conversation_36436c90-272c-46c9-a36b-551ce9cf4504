/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
extern "C" {}
#include <malloc.h>
#include <errno.h>
#include <sys/types.h>
// 共享内存需要的头文件
#include <sys/ipc.h>
#include <sys/shm.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "tsdb_rel_scene_ddl.h"
// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif
class tsdb_rel_scene_start : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void tsdb_rel_scene_start::SetUp()
{}
void tsdb_rel_scene_start::TearDown()
{}
void *thread_rCPU_Overloadl_value100(void *args)
{
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rCPU_Overloadl_value");
}
void *thread_rSysClockJump_60(void *args)
{
    system("date");
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rSysClockJump_60");
    system("date");
}
void *thread_rSysClockJump_plus60(void *args)
{
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rSysClockJump_plus60");
}
void *thread_tsdb_rel_scene_gmsysview(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 服务端进程绑定CPU指定核心 0
    system("taskset -pc 0 `pidof gmserver_ts`");
    // 建立链接
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *viewName = "V\\$DRT_CONN_STAT";
    char DFX_command[256] = "\n";
    snprintf(
        DFX_command, 256, "%s/gmsysview -sql \"select * from '%s'\" -s %s", g_toolPath, viewName, g_connServerTsdb);
    AW_FUN_Log(LOG_INFO, "%s\n", DFX_command);
    system(DFX_command);

    // 延时15S 等待故障注入
    sleep(15);

    // 通过视图
    AW_FUN_Log(LOG_INFO, "%s\n", DFX_command);
    system(DFX_command);
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 002  系统CPU过载   1.使用工具占用cpu（CPU1使用率为100%）
                                    2.启动服务端进程（绑定到CPU1）
                                    3.服务启动成功，耗时较长。
                                    4.查询视图数据，成功返回。
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    pthread_t FLT, DB;
    int ret = pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value100, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 起服务初始化
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = pthread_create(&DB, NULL, thread_tsdb_rel_scene_gmsysview, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(FLT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(DB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
}
/*****************************************************************************
 * Description  : 003  动态内存不足  1.使用工具占用动态内存
                                    2.启动服务端进程，启动失败
                                    3.工具释放动态内存
                                    4.再次启动服务端进程，启动成功
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_002)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    // 消耗内存以后启动server
    int ret;
    if ((ret = system(
             "${TEST_HOME}/../../output/euler/aarch64/bin/gmserver_ts -p /usr/local/file/gmserver_ts.ini -b")) != 0) {
        perror("system");
        AW_FUN_Log(LOG_INFO, "error is %d \n", errno);
    }
    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d \n", i);
        free(Myblock[i]);
    }
    // 启动server
    system("${TEST_HOME}/../../output/euler/aarch64/bin/gmserver_ts -p /usr/local/file/gmserver_ts.ini -b");
    // 建立链接
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重启服务，避免构建卡死
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb/");
    system("sh $TEST_HOME/tools/start.sh -ts");
}
/*****************************************************************************
 * Description  : 004  共享内存不足  1.使用工具占用共享内存
                                    2.启动服务端进程，启动失败
                                    3.工具释放共享内存
                                    4.再次启动服务端进程，启动成功
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_003)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    AW_FUN_Log(LOG_STEP, "test start.");
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 20; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
        if ((shmid[i] == -1)) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        count++;
    }
    // 消耗共享内存以后启动server
    int ret;
    if ((ret = system(
             "${TEST_HOME}/../../output/euler/aarch64/bin/gmserver_ts -p /usr/local/file/gmserver_ts.ini -b")) != 0) {
        perror("system");
        AW_FUN_Log(LOG_INFO, "error is %d \n", errno);
    }
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d \n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    // 启动server
    system("${TEST_HOME}/../../output/euler/aarch64/bin/gmserver_ts -p /usr/local/file/gmserver_ts.ini -b");
    // 建立链接
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
}
/*****************************************************************************
 * Description  : 014  时钟向前跳变  1.启动服务端进程
                                    2.修改系统时间往后+60秒
                                    3.系统启动成功
                                    4.查询系统视图，查询数据正常
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    pthread_t FLT, DB;
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb/");
    // 系统时间会在10s内修复至正常值
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/start.sh -ts");

    int ret = pthread_create(&FLT, NULL, thread_rSysClockJump_60, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&DB, NULL, thread_tsdb_rel_scene_gmsysview, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(FLT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(DB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 015  时钟向后跳变  1.启动服务端进程
                                    2.修改系统时间往后-60秒
                                    3.系统启动成功
                                    4.查询系统视图，查询数据正常
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    pthread_t FLT, DB;
    // 起服务初始化
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb/");
    system("ipcrm -a");
    // 系统时间会在10s内修复至正常值
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = pthread_create(&FLT, NULL, thread_rSysClockJump_plus60, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&DB, NULL, thread_tsdb_rel_scene_gmsysview, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(FLT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(DB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 016  进程重复启动  1.启动服务端进程，成功
                                    2.启动一个客户端，持续访问服务端
                                    3.再次启动服务端进程，失败（报服务已启动）
                                    4.原客户端业务正常、连续
                                    5.新起客户做数据读写操作，正常
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
bool twoclient = false;
void *thread_tsdb_rel_scene_ddl_while_creat_delete_table(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelName[] = "dns_global_cfg";
    // 建立链接
    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int number = 0;
    // 循环建表删表
    while (1) {
        int tableNum = 1;
        ret = DropCmTable(g_stmt_sync, tableNum);
        ret = BatchCreateTable(g_stmt_sync, tableNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (number == 100) {
            AW_FUN_Log(LOG_INFO, "aaaaaaaaaaaaaaaaa\n");
        }
        ret = DropCmTable(g_stmt_sync, tableNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        number++;
        if (twoclient) {
            // 断开链接
            ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 清理环境
            ret = close_epoll_thread();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testEnvClean();
            return 0;
        }
    }
}
void *thread_while_creat_delete_table(void *args)
{
    system("sh $TEST_HOME/tools/start.sh -ts");
    sleep(20);
    GmcConnT *conn_sync = NULL;  //  conn 句柄
    GmcStmtT *stmt_sync = NULL;  //  stmt 句柄
    // 建立链接
    int ret = TestTsGmcConnect(&conn_sync, &stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int number = 0;
    char labelName[] = "dnsv4_server_info";
    // 循环建表删表
    while (1) {
        if (number == 100) {
            AW_FUN_Log(LOG_INFO, "two connect creatvertlabel done\n");
            twoclient = true;
        }

        char sqlCmd[256] = {0};
        static char g_tableName[64] = "testdb";
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        uint32_t cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt_sync, sqlCmd, cmdLen);
        char gSqlCmd[MAX_CMD_SIZE] = {0};

        (void)snprintf(gSqlCmd, MAX_CMD_SIZE,
            "create table %s(id integer, time integer, name char(64), ip inet, ns text)"
            " with (time_col = 'time', interval = '1 hour', ttl = '1 hour', cache_size = '0', disk_limit = '100 MB',"
            " sensitive_col = 'name', compression = 'fast(rapidlz)', table_path = '/data/gmdb/');",
            g_tableName);

        ret = GmcExecDirect(stmt_sync, gSqlCmd, MAX_CMD_SIZE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)sprintf(sqlCmd, "drop table %s;", g_tableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt_sync, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        number++;
        if (twoclient) {
            // 断开链接
            ret = testGmcDisconnect(conn_sync, stmt_sync);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            return 0;
        }
    }
}

/*****************************************************************************
 * Description  : 006  重复建表删表  1.启动服务端进程，成功
                                    2.启动一个客户端，重复建100个表删表
                                    3.启动第二个客户端，重复建1个表删表
                                    4.执行正常
 * Input        : None
 * Output       : None
 * Author       : qwx620469
 * Modification : Create function
 * *****************************************************************************/
TEST_F(tsdb_rel_scene_start, tsdb_rel_scene_start_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    pthread_t FLT, DB;
    // 起服务初始化
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("rm /data/gmdb/");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = pthread_create(&DB, NULL, thread_tsdb_rel_scene_ddl_while_creat_delete_table, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    ret = pthread_create(&FLT, NULL, thread_while_creat_delete_table, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(FLT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(DB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
