/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 Description  : TSDB支持SQLite数据导入工具
 Notes        : 割接工具写文件可靠性场景加固
 Author       : chenbangjun
 Modification :
 create       : 2025/05/23
**************************************************************************** */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include "t_datacom_lite.h"
#include "tsdb_support_gmextimport_rel.h"
#include <sys/ipc.h>  // 共享内存需要的头文件
#include <sys/shm.h>  // 共享内存需要的头文件
#include "gmc_test.h"
#include "t_rd_inject.h"

#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif

#define MAX_CMD_SIZE 1024

Status ret = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_command[MAX_CMD_SIZE];
char g_grepDbName[20] = {".db"};
char g_grepTxtName[20] = {".txt"};
static char g_tempFilePath[250] = {0};
bool g_isGmextimportDone = false;
char g_gmextimport[20] = {0};
char g_tempTableName[20] = {"t_dbtest_tsdb"};
char *dir = getenv("PWD");
#ifdef RUN_INDEPENDENT
#define TABLE_PATH "/data/gmdb/"
#else
#define TABLE_PATH "/mnt/hdd/data/gmdb/"
#endif


class GmextImportTool : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        InitTsCiCfg();
#ifdef FEATURE_MULTI_TS
    (void)sprintf(g_gmextimport, "gmextimport");
#else
    (void)sprintf(g_gmextimport, "gmextimport_ts");
#endif
        (void)sprintf(g_dataFilePath1, "%s/data/gmdb1", dir);
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        system("rm -rf ./data/gmdb1");
        system("mkdir -p ./data/gmdb1/");
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        RecoverTsCiCfg();
        TsDefulatDbFileClean();
        system("rm -rf ./data/");
        system("rm -rf ./dbfile");
        system("sh $TEST_HOME/tools/gmserver_replace.sh recover_ts");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmextImportTool::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *cmd = "pwd";
    FILE *fptr = popen(cmd, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    fgets(g_tempFilePath, sizeof(g_tempFilePath), fptr);
    fclose(fptr);
    g_tempFilePath[strlen(g_tempFilePath) - 1] = '\0';  //替换结尾换行符
    system("rm -rf ./dbfile");
    system("mkdir -p ./dbfile");
    system("rm -rf ./progress.json");
    // 增加截断日志，本地复现时发现有大量其他用例出现截断问题，在此统一添加
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
}

void GmextImportTool::TearDown()
{
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

typedef struct {
    GmcStmtT *stmt;
    char *sqlCmd;
} ConstructDataType;

// 执行导数命令
void *gmextImportInsert(void *arg)
{
    ConstructDataType constructData = *(ConstructDataType *)arg;
    int ret = system(constructData.sqlCmd);
    if (ret == 0) {
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        AW_FUN_Log(LOG_STEP, "gmextimport进程被kill或者时序服务被kill");
    }
    g_isGmextimportDone = true;
    return nullptr;
}

int getTableDataNumber(char *sqlTemp)
{
    int ret = 0;
    ret = GmcExecDirect(g_stmt, sqlTemp, strlen(sqlTemp));
    EXPECT_EQ(GMERR_OK, ret);
    bool eof = false;
    ret = GmcFetch(g_stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t res = 0;
    uint32_t propSize = strlen(sqlTemp);
    bool isNull = false;
    ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &res, &propSize, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    return res;
}

int getDbfileNum(char *grepFileNum)
{
    char command[1024];
    if (grepFileNum == NULL) {
        (void)snprintf(command, 1024, "ls -1 %s/dbfile | grep .db | wc -l", g_tempFilePath);
    } else {
        (void)snprintf(command, 1024, "ls -1 %s/dbfile | grep %s | wc -l", g_tempFilePath, grepFileNum);
    }
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

// 001.导入过程中内存不足 预期:内存不足后退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free");
    void *Myblock[20];
    // 消耗掉系统的内存
    for (int i = 0; i < 20; i++) {
        Myblock[i] = NULL;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            free(Myblock[i - 1]);
            free(Myblock[i - 2]);
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        AW_FUN_Log(LOG_INFO, "Currently allocating %d GB\n", i + 1);
        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d \n", count);
    system("cat /proc/meminfo | grep Free");
    sleep(30);
    // 释放消耗掉的内存
    for (int i = 0; i < count - 2; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        free(Myblock[i]);
    }
    system("cat /proc/meminfo | grep Free");
    pthread_join(tid[0], NULL);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.导入过程中删除默认dataFilePath文件夹 预期:删除文件夹后，退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(5);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "rm -rf %s;", TABLE_PATH);
    system(sqlCmd);
    
    sleep(3);
    pthread_join(tid[0], NULL);

    
    system("cat ./progress.json");

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    // 错误码不为文件读写/断连/内存不够,实际报错为1004004
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_UNIQUE_VIOLATION);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.导入过程中构造默认dataFilePath磁盘满 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 10000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    // 注入磁盘满
    system("cd /root/CFE_Tool/cfe;./cfe \"inject rfile_full (diskname) values (/)\"");
    system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
    sleep(30);
    system("ps -ef|grep gmextimport");
    // 取消磁盘满
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rfile_full where diskname=/\"");

    pthread_join(tid[0], NULL);
    system("cat ./progress.json");

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(10000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DTS2025052806336 返回错误码变化导致用例适配
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_DUPLICATE_COLUMN, GMERR_FILE_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.导入过程中构造默认dataFilePath磁盘损坏,目录不可写 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_readonly (filename) values (%s)\"", TABLE_PATH);
    system(sqlCmd);
    system("ps -ef|grep gmextimport");
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_readonly where filename = %s\"", TABLE_PATH);
    // 取消目录不可写故障
    system(sqlCmd);
    pthread_join(tid[0], NULL);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(10000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.导入过程中删除正在导入的db文件 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    system("rm -rf ./dbfile/Dbtest_Org.db");
    system("ps -ef|grep gmextimport");
    pthread_join(tid[0], NULL);

    (void)sprintf(sqlCmd, "cat ./progress.json ");
    // DTS2025052806336 返回错误码变化导致用例适配
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(5000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.导入过程中构造db文件路径磁盘损坏，目录不可读 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};

    (void)memset(tempDbPath, 0, 512);
    (void)sprintf(tempDbPath, "%s/dbfile", dir);
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", tempDbPath);
    system(sqlCmd);
    system("ps -ef|grep gmextimport");
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", tempDbPath);
    // 取消目录不可读故障
    system(sqlCmd);
    pthread_join(tid[0], NULL);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(1000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.导入开始前，损坏db文件 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};

    (void)memset(tempDbPath, 0, 512);
    (void)sprintf(tempDbPath, "%s/dbfile/Dbtest_Org.db", dir);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%s)\"",
        tempDbPath);
    system(sqlCmd);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    
    system("ps -ef|grep gmextimport");
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %s\"", tempDbPath);
    // 取消目录不可读故障
    system(sqlCmd);
    pthread_join(tid[0], NULL);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.导入过程中构造正在导入的db文件损坏 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};

    (void)memset(tempDbPath, 0, 512);
    (void)sprintf(tempDbPath, "%s/dbfile/Dbtest_Org.db", dir);
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%s)\"",
        tempDbPath);
    system(sqlCmd);
    system("ps -ef|grep gmextimport");
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %s\"", tempDbPath);
    // 取消故障
    system(sqlCmd);
    pthread_join(tid[0], NULL);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    // DTS2025052806336 返回错误码变化导致用例适配
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(1000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.导入过程中构造未被导入的db文件损坏 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "Dbtest_Org.txt");
    ret = getDbfileNum(sqlCmd);
    (void)memset(tempDbPath, 0, 512);
    // 通过生成的txt文件判断未被导入的db
    if (ret == 0) {
        (void)sprintf(tempDbPath, "%s/dbfile/Dbtest_Org.db", dir);
    } else {
        (void)sprintf(tempDbPath, "%s/dbfile/Dbtest_Org1.db", dir);
    }
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%s)\"",
        tempDbPath);
    system(sqlCmd);
    system("ps -ef|grep gmextimport");
    // 等待故障注入
    sleep(20);
    pthread_join(tid[0], NULL);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %s\"", tempDbPath);
    // 取消故障
    system(sqlCmd);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 2", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    // 损坏后数据无法确定,应该大于等于1000000
    EXPECT_GE(ret, 1000000);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.导入过程中重启，等待10秒后，置空txt文件 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    pthread_join(tid[0], NULL);
    // 等待重试失败
    sleep(6);

    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);

    system("rm -rf ./dbfile/Dbtest_Org.txt");
    system("echo "" > ./dbfile/Dbtest_Org.txt");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_LT(3000000, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = DropTable(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 10000");
    system("rm -rf ./dbfile/Dbtest_Org.txt");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(10000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.导入过程中重启，等待10秒后，txt文件中写入字符串 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    pthread_join(tid[0], NULL);
    // 等待重试失败
    sleep(6);

    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);

    system("rm -rf ./dbfile/Dbtest_Org.txt");
    system("echo 'avsgdf' > ./dbfile/Dbtest_Org.txt");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_LT(3000000, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_COLUMN);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.导入过程中重启，等待10秒后，txt文件写入负数 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    pthread_join(tid[0], NULL);
    // 等待重试失败
    sleep(6);

    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);

    system("rm -rf ./dbfile/Dbtest_Org.txt");
    system("echo -120000 > ./dbfile/Dbtest_Org.txt");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    // 写入负数后，实际导入数据不可预期
    AW_MACRO_EXPECT_NE_INT(3000000, ret);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_DUPLICATE_COLUMN);
    AW_ADD_TRUNCATION_WHITE_LIST(2, "TsServiceEntry try prepare sql", "TsServiceEntry try execute ddl sql");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.导入过程中建连失败,重试超时场景 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    pthread_join(tid[0], NULL);
    // 等待重试失败
    sleep(6);
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(3000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.导入过程中构造锁库 预期:锁库后退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    

    // 触发锁库
    ret = GmcSetDbEmergency(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid[0], NULL);
   
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 恢复锁库
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(3000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.导入过程中构造锁超时 预期:内存不足后退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // diskLessBoot设置为0会切换失败导致锁库，用例需要修改默认配置项
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
// euler和仿真环境清共享内存，清理原有DB文件
#if defined RUN_INDEPENDENT
    system("sh $TEST_HOME/tools/stop.sh -f");
#endif
    system("sh $TEST_HOME/tools/stop.sh -ts");
    TsDefulatDbFileClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    ret = TestTsGmcConnectTime(&conn_t, &stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ConstructDataType constructDataType = {stmt_t, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(5);
    ret = GmcSwapDataDir(g_stmt, g_dataFilePath1, NULL);
    pthread_join(tid[0], NULL);
    
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    // DTS2025052806336 返回错误码变化导致用例适配
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(3000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_LOCK_NOT_AVAILABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int getDbfileNumWithPath(char *filePath, char *grepFileNum)
{
    char command[128];
    if (grepFileNum == NULL) {
        (void)snprintf(command, 128, "ls -1 %s | grep .db | wc -l", filePath);
    } else {
        (void)snprintf(command, 128, "ls -1 %s | grep %s | wc -l", filePath, grepFileNum);
    }
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

// 016.割接工具初始化失败 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 30000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempProgress[20] = {"progress"};
    int conn_num = 1;
    pthread_t tid[conn_num];
    
    // 1.gmdb-uri参数不对
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --table-process-progress ./dbfile/progress.json",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_gmextimport);
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./dbfile/progress.json");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 2.gmdb-uri参数不对且不指定progress位置
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_gmextimport);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNumWithPath(g_tempFilePath, tempProgress);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 3.directory参数不对且指定progress位置
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --director %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --table-process-progress ./dbfile/progress.json",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 4.directory参数不对且不指定progress位置
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --director %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNumWithPath(g_tempFilePath, tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 5.file指定文件夹且不指定progress位置
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --file %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNumWithPath(g_tempFilePath, tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 6.file指定文件夹且指定progress位置
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --file %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --table-process-progress ./dbfile/progress.json",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 7.table-schema-dir参数不对
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-di %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 8.table-schema-dir 为具体文件
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1/dbtest_basic068.json  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 9.加载第三方库时没有对应so文件
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s --sqlite3-lib-path /data/gmdb/libsqlite3.so",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 10.json文件中缺少src_table,dst_table
    system("rm -rf ./dbfile1/");
    system("cp ./table_schema/dbtest_basic_error1.json ./dbfile1");
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    // 11.json文件中出现不支持的字段
    system("rm -rf ./dbfile1/");
    system("cp ./table_schema/dbtest_basic_error2.json ./dbfile1");
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");
    
    // 12.json文件中内容无效
    system("rm -rf ./dbfile1/");
    system("cp ./table_schema/dbtest_basic_error3.json ./dbfile1");
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    ret = getDbfileNum(tempProgress);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    system("rm -rf ./progress.json");
    system("rm -rf ./dbfile/progress.json");

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    EXPECT_GE(3000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int getCpuNum()
{
    char command[128];
    (void)snprintf(command, 128, "cat /proc/cpuinfo | grep processor | wc -l");
    FILE *fp = popen(command, "r");
    if (fp == NULL) {
        AW_FUN_Log(LOG_STEP, "db文件地址错误");
        return -1;
    }
    char result[20] = {0};
    char *flag1 = fgets(result, sizeof(result), fp);
    if (flag1 == NULL) {
        AW_FUN_Log(LOG_STEP, "获取db文件数量错误");
        return -1;
    }
    int ret = atoi(result);
    pclose(fp);
    return ret;
}

int32_t GetViewFieldResultValue(const char *viewName)
{
    int maxCmdSize = 256;
    char cmdOutput[maxCmdSize];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));

    char command[maxCmdSize];
    (void)snprintf(command, maxCmdSize, "%s", viewName);

    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error./n", command);
    }
    EXPECT_NE((void *)NULL, pf);
    while (NULL != fgets(cmdOutput, maxCmdSize, pf)) {
    };
    pclose(pf);

    return atoi(cmdOutput);
}

// 017.单表多db文件导入，导入过程中构造CPU使用率大于80% 预期:CPU超限后停止导数，回落后继续导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 1000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 1000000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 90)\"", cpuNum - 1);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system(sqlCmd);
    // 等待故障注入,CPU稳定大于80
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s"
        " | grep ROW_CNT | awk -F ':' '{print $2}'", g_connServerTsdb);
    int64_t dataCountBefore = 0;
    int64_t dataCountAfter = 0;
    int64_t fetchTimes = 0;
    int64_t failTime = 0;
    while (fetchTimes <  50) {
        sleep(2);
        fetchTimes++;
        if (fetchTimes == 1) {
            dataCountAfter = GetViewFieldResultValue(sqlCmd);
        }
        dataCountBefore = GetViewFieldResultValue(sqlCmd);
        if (dataCountBefore != dataCountAfter) {
            failTime++;
            EXPECT_GE(6, failTime);
        }
        dataCountAfter = dataCountBefore;
    }

    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");

    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(7000000, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.多表多db文件导入，导入过程中构造CPU使用率大于80% 预期:CPU超限后停止导数，回落后继续导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 1000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 1000000");
    system("./t_alarmrel -p ./dbfile/Alarm_Org.db -n 1500000");
    system("./t_alarmrel -p ./dbfile/Alarm_Org1.db -n 1500000");
    system("./t_idmlogrel -p ./dbfile/Idm_Org.db -n 1500000");
    system("./t_idmlogrel -p ./dbfile/Idm_Org1.db -n 1500000");

    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 90)\"", cpuNum - 1);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);
    int count = 0;
    ret = 1;
    while (ret == 1) {
        sleep(2);
        count++;
        if (count > 50) {
            ret = 0;
        }
    }

    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");

    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 7", "\"imported_file_num\": 7", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(7000000, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.单表多db文件导入，导入完成后进行数据校验 预期:导数完成后，数据校验准确
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 1000000");
    system("./t_dbtest_check -p ./dbfile/Dbtest_Org1.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel019.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s"
        " | grep ROW_CNT | awk -F ':' '{print $2}'", g_connServerTsdb);
    ret = GetViewFieldResultValue(sqlCmd);
    AW_MACRO_EXPECT_EQ_INT(1010000, ret);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 2", "\"imported_file_num\": 2", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int64_t log_time = 100;
    int64_t temp_int1 = 2147483647;
    int64_t temp_int2 = 9223372036854775807;
    int64_t temp_int3 = 0;
    char temp_char1[64] = {"daviddaviddaviddaviddaviddaviddaviddaviddaviddaviddaviddaviddav"};
    char temp_blob1[160] = {"0071 35200071 35200071 35200071 35200071 35200071 35200071 35200071"
        " 35200071 35200071 35200071 35200071 35200071 35200071 35200071 35200071 35200071 35200071 3"};
    char temp_blob2[160];
    char temp_charint1[64] = {"2147483647"};
    char temp_charint2[64] = {"9223372036854775807"};
    char temp_charint3[64] = {"0"};
    char temp_charipv41[46] = {"00000000"};
    char temp_charipv42[46] = {"05f5e0ff"};
    char temp_charipv43[46] = {"ffffffff"};
    int64_t temp_intip1 = 00000000;
    int64_t temp_intip2 = 99999999;
    int64_t temp_intip3 = 99999999;
    char temp_charip1[46] = {"00000000"};
    char temp_charip2[46] = {"99999999999999999999999999999999"};
    char temp_charip3[46] = {"ffffffffffffffffffffffffffffffff"};
    char temp_text1[160] = {"daviddaviddaviddaviddaviddaviddaviddaviddaviddaviddaviddaviddav"};


    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "select * from t_dbtest_tsdb where log_time1 >= 100"
        " and log_time1 < 12886308 order by log_time1 limit 10000;");
    uint32_t sqlStateLen = strlen(sqlCmd);
    Status ret = GmcExecDirect(g_stmt, sqlCmd, sqlStateLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 查询过滤后的数据是否正确
    bool eof = false;
    bool isNull = false;
    int64_t cId = 0;
    int64_t cTime = 0;
    char cIp[33] = {0};
    char cName[64] = {0};
    char cText[160] = {0};
    uint32_t sizeCharText = sizeof(cText);
    uint8_t cBlob[160] = {0};
    uint32_t sizeInt = sizeof(int64_t);
    uint32_t sizeCharIp = sizeof(cIp);
    uint32_t sizeCharNmae = sizeof(cName);
    uint32_t sizeCharBolb = sizeof(cBlob);
    int64_t fetchTimes = 0;
    int64_t count = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 0, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(fetchTimes + 1, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 1, &cTime, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(log_time + 1, cTime);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 2, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(temp_int1, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 3, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(temp_int2, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 4, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(temp_int3, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 5, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_char1, cName);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 6, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_char1, cName);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 7, &cName, &sizeCharNmae, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_char1, cName);
        sizeCharBolb = 1000;
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 8, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        EXPECT_EQ(memcmp(cBlob, temp_blob1, 160), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 9, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        EXPECT_EQ(memcmp(cBlob, temp_blob1, 160), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 10, &cBlob, &sizeCharBolb, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        EXPECT_EQ(memcmp(cBlob, temp_blob1, 160), 0);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 11, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(2147483647, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 12, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(9223372036854775807, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 13, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(0, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 14, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(0, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 15, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(99999999, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 16, &cId, &sizeInt, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_INT(99999999, cId);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 17, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charip1, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 18, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charipv42, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 19, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charipv42, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 20, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charip1, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 21, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charip2, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 22, &cIp, &sizeCharIp, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_EXPECT_EQ_STR((const char *)temp_charip3, cIp);
        ret = GmcGetPropertyById((const GmcStmtT *)g_stmt, 23, &cText, &sizeCharText, &isNull);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_MACRO_ASSERT_EQ_STR((const char *)temp_text1, cText);
        fetchTimes++;
        log_time++;
    }
    AW_MACRO_EXPECT_EQ_INT(fetchTimes, 10000);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.工具切换过程中，模拟设备复位
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    pthread_create(&tid[1], NULL, ConcurrentDeviceReset, NULL);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tempTableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    AW_MACRO_ASSERT_NE_INT(dataNum, 0);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.工具切换过程后，模拟设备复位，单表多db
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 1000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 1000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_create(&tid[1], NULL, ConcurrentDeviceReset, NULL);
    pthread_join(tid[1], NULL);
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(7000000, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tempTableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.单表多db情况下，cpu80%过载以下，工具切换 预期:导数继续
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 65)\"", cpuNum - 1);
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    // 注入故障
    system(sqlCmd);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.文件句柄不足，工具切换 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);

    // 注入异常
    int time_out = 20;
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish inject %d", time_out);
    sleep(30);
    system("ps -ef|grep gmextimport");
    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_deplenish clean");

    pthread_join(tid[0], NULL);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(10000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.权限错误，工具切换,对应目录不可读写 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 10000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);

    // 等待几秒触发导数开始
    sleep(3);

    // 取消目录操作权限
    (void)GtExecSystemCmd(
        "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", TABLE_PATH);
    AW_FUN_Log(LOG_STEP, "sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw inject %s", TABLE_PATH);

    sleep(30);
    system("ps -ef|grep gmextimport");
    // 恢复目录操作权限
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/tsdb_reliability/cfe_inject.sh rfile_unrw clean %s", TABLE_PATH);

    pthread_join(tid[0], NULL);
    system("cat ./progress.json");

    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(10000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.时钟向前跳变后，进行导数 预期:导数完成
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (-,7200)\"");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    // 注入故障
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    // 恢复环境
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rSysClockJump where DIRECTION = - and OFFSET=7200\"");
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.时钟向前跳变后，进行导数 预期:导数完成
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (+,7200)\"");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    // 注入故障
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    // 恢复环境
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rSysClockJump where DIRECTION = + and OFFSET=7200\"");
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.修改时区后，进行导数 预期:导数完成
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 500000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    AW_FUN_Log(LOG_STEP, "local time: %s", asctime(local_tm));
    // 修改时区为UTC-1
    setenv("TZ", "UCT-1", 1);
    tzset();
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    pthread_join(tid[0], NULL);
    // 修改时区为Asia/Shanghai
    setenv("TZ", "Asia/Shanghai", 1);
    tzset();
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(520000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.满规格（256列）工具切换 预期:导数完成
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel028.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (+,7200)\"");
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    // 注入故障
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    // 恢复环境
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rSysClockJump where DIRECTION = + and OFFSET=7200\"");
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
//  0 3 done需要确认为什么？？？？
// 029.工具指定文件有部分损坏,指定的json文件损坏 预期:导数退出
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    system("cp ./table_schema/alarm.json ./dbfile1");
    system("cp ./table_schema/idm.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename)"
        " values (%s/dbfile1/dbtest_basic_rel001.json)\"",
        g_tempFilePath);
    system(sqlCmd);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 0", "\"imported_file_num\": 0", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./progress.json");
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;"
        "./cfe \"clean rfile_fs_broken where filename = %s/dbfile1/dbtest_basic_rel001.json\"",
        g_tempFilePath);
    // 取消故障
    system(sqlCmd);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.工具切换，cpu长时间处于80%过载以上，回调cpu压力，继续工具切换 预期:CPU超限后停止导数，回落后继续导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    int cpuNum = getCpuNum();
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 90)\"", cpuNum - 1);

    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system(sqlCmd);
    // 等待故障注入,CPU稳定大于80
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s"
        " | grep ROW_CNT | awk -F ':' '{print $2}'", g_connServerTsdb);
    int64_t dataCountBefore = 0;
    int64_t dataCountAfter = 0;
    int64_t fetchTimes = 0;
    int64_t failTime = 0;
    // cpu长时间过载3分钟
    while (fetchTimes < 90) {
        sleep(2);
        fetchTimes++;
        if (fetchTimes == 1) {
            dataCountAfter = GetViewFieldResultValue(sqlCmd);
        }
        dataCountBefore = GetViewFieldResultValue(sqlCmd);
        if (dataCountBefore != dataCountAfter) {
            failTime++;
            EXPECT_GE(6, failTime);
        }
        dataCountAfter = dataCountBefore;
    }

    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");

    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(5020000, ret);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = 't_dbtest_tsdb'\" -s %s", g_connServerTsdb);
    system(sqlCmd);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.保持工具切换，挂起服务端进程 预期:
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};

    int pid = 0;
    ret = TestGetResultCommand("ps -ef |grep gmserver_ts |grep -v grep |grep -v sh |awk '{print $2}'", &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rProc_h (pid) values (%d)\"", pid);
    system(sqlCmd);
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rProc_h where pid = %d\"", pid);
    system(sqlCmd);
    pthread_join(tid[0], NULL);


    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.保持工具切换，挂起客户端进程 预期:
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};

    int pid = 0;
    ret = TestGetResultCommand("ps -ef |grep gmserver_ts |grep -v grep |grep -v sh |awk '{print $2}'", &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 

        "cd /root/CFE_Tool/cfe;./cfe \"inject rProc_h (pid) values (%d)\"", pid);
    system(sqlCmd);
    sleep(20);
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rProc_h where pid = %d\"", pid);
    system(sqlCmd);
    pthread_join(tid[0], NULL);


    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.反复重启服务端进程后，工具切换 预期:
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org1.db -n 10000");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org2.db -n 10000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic_rel001.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tempDbPath[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数继续
    sleep(3);
    for (int i = 0; i < 10; i++) {
        system("sh $TEST_HOME/tools/stop.sh -ts");
        system("sh $TEST_HOME/tools/start.sh -ts");
    }
    ret = TestTsGmcConnectTime(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid[0], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 3", "\"imported_file_num\": 3", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    // 重启时会根据txt文件中留存的数据导入，偶现多导数据，数据应大于或等于db所对应数据
    EXPECT_GE(ret, 5020000);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thread_rProc_kill19_18_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rProc_kill19_18_server");
}

// 034.保持工具切换，向服务端发送coredump信号 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待导数开始
    sleep(3);
    // 等待几秒触发导数开始
    sleep(3);
    system("kill -11 `pidof gmserver_ts`");
    pthread_join(tid[0], NULL);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(5000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_CONNECTION_RESET_BY_PEER);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thread_rProc_SIGILL(void *args)
{
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rProc_SIGILL");
}

// 035.保持工具切换，向服务端发送terminate信号 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("kill -15 `pidof gmserver_ts`");
    pthread_join(tid[0], NULL);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(5000000, ret);

    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_INTERNAL_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *thread_rProc_SIGINT(void *args)
{
    system("sh ${TEST_HOME}/reliability/tsdb_reliability/tsdb_rel_scene_ddl.sh rProc_SIGINT");
}

// 036.保持工具切换，向服务端发送ignore信号 预期:退出导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    system("kill -2 `pidof gmserver_ts`");
    pthread_join(tid[0], NULL);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(5000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_INTERNAL_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037.工具导数过程中构造锁资源耗尽 预期:继续导数
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    char tableName[256] = {0};
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 3000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = TestTsGmcConnectTime(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int dataCount = 1000;
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    AW_FUN_Log(LOG_STEP, "local time: %s", asctime(local_tm));
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 100; i++) {
        // 创建表
        (void)memset(sqlCmd, 0, 1024);
        (void)sprintf(sqlCmd,
            "create table testdb%d(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h')",
            i);
        ret = GmcExecDirect(stmt, sqlCmd, strlen(sqlCmd));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 写入数据
    for (int i = 0; i < 1000; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb0");
        ret = rowInsertDataWithTime(stmt, tableName, dataCount, startTime + 3600 * i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb1");
        ret = rowInsertDataWithTime(stmt, tableName, dataCount, startTime + 3600 * i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    InsertQueryDataType constructDataType_1 = {stmt, g_tableName, 0, 0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1, "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s", g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 2;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    // 等待几秒触发导数开始
    sleep(3);
    pthread_create(&tid[1], NULL, ConcurrentDeleteTable, &constructDataType_1);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    AW_FUN_Log(LOG_STEP, "DateCount is %d", ret);
    AW_MACRO_EXPECT_EQ_INT(3000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_REQUEST_TIME_OUT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038、系统动态内存不足,进行导数
// 预期：导数成功
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject_ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 500000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    // 注入动态内存不足故障
    ret = TestCollectlibPath((char *)"memory_traverse_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;
    injectInfo->totalCnt = 1;
    injectInfo->injectCnt = 1;
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    for (int i = 0; i < injectInfo->totalCnt; i++) {
        // 等待几秒触发导数开始
        sleep(3);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
    }
    pthread_join(tid[0], NULL);
    // 恢复动态内存不足故障
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 0", "\"status\": \"processing\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(500000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_INTERNAL_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039、系统共享内存不足，进行导数
// 预期：导数成功
TEST_F(GmextImportTool, tsdb_rel_sqlite_tools_rel_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject_ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("./t_dbtest_nomalrel -p ./dbfile/Dbtest_Org.db -n 5000000");
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = system("mkdir -p ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("cp ./table_schema/dbtest_basic068.json ./dbfile1");
    char sqlCmd[1024] = {0};
    char sqlCmd1[1024] = {0};
    (void)memset(sqlCmd1, 0, 1024);
    (void)sprintf(sqlCmd1,
        "%s --directory %s/dbfile/ --table-schema-dir %s/dbfile1  "
        " --gmdb-uri %s",
        g_gmextimport, g_tempFilePath, g_tempFilePath, g_connServerTsdb);
    int conn_num = 1;
    pthread_t tid[conn_num];
    ConstructDataType constructDataType = {g_stmt, sqlCmd1};
    // 注入动态内存不足故障
    ret = TestCollectlibPath((char *)"shm_traverse_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;
    injectInfo->totalCnt = 1;
    injectInfo->injectCnt = 1;
    pthread_create(&tid[0], NULL, gmextImportInsert, &constructDataType);
    for (int i = 0; i < injectInfo->totalCnt; i++) {
        // 等待几秒触发导数开始
        sleep(2);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
    }
    pthread_join(tid[0], NULL);
    // 恢复动态内存不足故障
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat ./progress.json");
    (void)memset(sqlCmd, 0, 1024);
    (void)sprintf(sqlCmd, "cat ./progress.json ");
    ret = executeCommand(sqlCmd, "\"total_file_num\": 1", "\"imported_file_num\": 1", "\"status\": \"done\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(g_stmt, g_tempTableName);
    EXPECT_GE(5000000, ret);
    ret = system("rm -rf ./dbfile1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(2, GMERR_CONNECTION_RESET_BY_PEER, GMERR_INTERNAL_ERROR);
    AW_FUN_Log(LOG_STEP, "test end.");
}
