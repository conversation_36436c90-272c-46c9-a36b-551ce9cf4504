/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tsdb_rel_scene_sn
 * Author: qwx620469
 * Create: 2024-12-12
 * Description:
 */
#include "tsdb_rel_scene_sn.h"

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#endif

class tsdb_rel_scene_sn : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        RecoverTsCiCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
    SnUserDataT *user_data;
};

void tsdb_rel_scene_sn::SetUp()
{}

void tsdb_rel_scene_sn::TearDown()
{}

/*****************************************************************************
 Description  : 4.系统CPU过载
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动故障：系统CPU过载
    system("sh ./tsdb_rel_scene_sn.sh CpuOverload");
    sleep(1);

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************test end*********************/
    // 取消故障
    system("sh ./tsdb_rel_scene_sn.sh CleanCpuOverload");

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 5.系统动态内存不足
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动故障：系统动态内存不足，由于内存占满机器会重启，脚本只占用60%的内存
    system("sh ./tsdb_rel_scene_sn.sh CpuOverload");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放内存
    system("sh ./tsdb_rel_scene_sn.sh CleanCpuOverload");

    // 释放连接
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 6.系统共享内存不足
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;

    int shmid[10000];
    key_t key[10000];
    for (int i = 0; i < 10000; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    int j = 0;
    for (int i = 0; i < 10000; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d, i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            j = i;
            break;
        } else {
            char *p;
            p = (char *)shmat(shmid[i], NULL, 0);
            // 3. 对共享内存实现读写
            memset(p, 1, MEGABYTE);
            // 4. 取消映射
            shmdt(p);
            count++;
        }
    }
    for (int i = j; i < 10000; i++) {
        shmid[i] = shmget(key[i], 20000, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d, i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            sleep(50);
            j = i;
            break;
        } else {
            char *p;
            p = (char *)shmat(shmid[i], NULL, 0);
            // 3. 对共享内存实现读写
            memset(p, 1, 20000);
            // 4. 取消映射
            shmdt(p);
            count++;
        }
    }
    // 消耗共享内存以后启动server
#if defined ENV_RTOSV2X
#else
    ret = system("${TEST_HOME}/../../output/euler/aarch64/bin/gmserver_ts -p /usr/local/file/gmserver_ts.ini -b");
    if (ret != 0) {
        perror("system");
        AW_FUN_Log(LOG_INFO, "error is %d \n", errno);
    }
#endif

    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d \n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 16.时钟向前跳变
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);

    // insert
    AW_FUN_Log(LOG_INFO, "Insert Start.\n");

    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入故障：时钟跳变
    system("sh ./tsdb_rel_scene_sn.sh TimeDumpForward");

    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);

    // insert
    AW_FUN_Log(LOG_INFO, "Insert Start.\n");

    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验收到的消息
    CheckData(idCheck2);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消故障
    system("sh ./tsdb_rel_scene_sn.sh CleanTimeDumpForward");
    /*****************test end*********************/
}

/*****************************************************************************
 Description  : 17.时钟向后跳变
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 启动服务
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"workerHungThreshold=6,200,300\"");
    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);

    // insert
    AW_FUN_Log(LOG_INFO, "Insert Start.\n");
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入故障：时钟跳变
    system("sh ./tsdb_rel_scene_sn.sh TimeDumpBackward");

    // 校验收到的消息
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消故障
    system("sh ./tsdb_rel_scene_sn.sh CleanTimeDumpBackward");

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 21.进程死循环
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入故障：进程死循环
    system("sh ./tsdb_rel_scene_sn.sh ProcessInfiniteLoop");
    sleep(3);
    // // 重启服务 or 故障恢复
    system("sh ./tsdb_rel_scene_sn.sh CleanProcessInfiniteLoop");

    // 校验收到的消息
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 22.进程异常退出
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    uint32_t times = 50;

    /*****************test start*********************/
    for (uint32_t i = 0; i < times; i++) {
        if (i == times / 2) {
            usleep(10000);

            // 注入故障：进程异常退出
            ret = system("sh ./tsdb_rel_scene_sn.sh ProcessExitAbnormally");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            break;
        }

        // 执行DML和订阅用例
        ret = system("./tsdb_rel_sn_022 ");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 再次执行DML和订阅用例
    ret = system("./tsdb_rel_sn_022 ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************test end*********************/
}

/*****************************************************************************
 Description  : 23.进程挂起
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入故障：进程挂起
    system("sh ./tsdb_rel_scene_sn.sh ProcessSuspended");

    // 再次操作失败
    ret = GmcPrepareStmtByLabelName(stmt, g_tableName, GMC_OPERATION_SQL_INSERT);
    AW_MACRO_EXPECT_EQ_INT(1016004, ret);

    // 重启服务 or 故障恢复
    system("sh ./tsdb_rel_scene_sn.sh CleanProcessSuspended");

    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 26.进程重复启动
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次启动，无影响
    char g_command[MAX_CMD_SIZE];
    snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/start.sh -ts");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 29.coredump信号
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入故障：coredump信号
    system("sh ./tsdb_rel_scene_sn.sh ProcessCoredump");

    // 建立连接，返回失败
    GmcConnT *g_conn1;
    GmcStmtT *g_stmt1;
    ret = TestTsGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 重启服务
    TestStartServer();

    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
}

/*****************************************************************************
 Description  : 30.terminate信号
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 注入故障：terminate信号
    system("sh ./tsdb_rel_scene_sn.sh ProcessTerminate");

    // 建立连接，返回失败
    GmcConnT *g_conn1;
    GmcStmtT *g_stmt1;
    ret = TestTsGmcConnect(&g_conn1, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    // 重启服务
    TestStartServer();

    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
}

/*****************************************************************************
 Description  : 31.ignore信号
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入故障：ignore信号
    system("sh ./tsdb_rel_scene_sn.sh ProcessIgnore");

    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 34.表的个数超系统限制
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    GmcConnT *g_conn_sync;
    GmcStmtT *g_stmt_sync;

    InitTsCiCfgPush();

    ret = TestTsGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int tableNum = 1022;
    ret = BatchCreateTable(g_stmt_sync, tableNum);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_RESOURCES, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = DropCmTable(g_stmt_sync, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);

    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&sqlCmd, 0, sizeof(sqlCmd));
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = BatchCreateTable(g_stmt_sync, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*     // 删除所有表
        ret = DropCmTable(g_stmt_sync, tableNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); */
}

/*****************************************************************************
 Description  : 36.连接资源耗尽
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"redoPubBufSize=256\"");
    InitTsCiCfgPush();
    int ret = 0;

    GmcConnT *conn[MAX_CONN_SIZE];
    GmcStmtT *stmt[MAX_CONN_SIZE];
    int i = 2;

    uint32_t exitConn = 0;
    ret = TestGetTsdbConnNum(&exitConn);
#ifdef ENV_SUSE
    exitConn = exitConn + 1;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>> create %u conn exitConn: %u.", MAX_CONN_SIZE - exitConn, exitConn);

    for (i = 2; i < MAX_CONN_SIZE - exitConn; i++) {
        ret = TestTsGmcConnect(&conn[i], &stmt[0]);
        if (ret != 0) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = TestTsGmcConnect(&conn[0], &stmt[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt[0], g_tableName);
    (void)DropStreamView(stmt[0], g_viewName);
    //  初始化数据
    int64_t count = 10;
    int64_t id[count] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    int64_t time[count] = {1827563231, 1827563232, 1827563233, 1827563234, 1827563235, 1827563236, 1827563237,
        1827563238, 1827563239, 1827563240};
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 10, max_size = 5000, delay_time = '1 s', disk_limit = '4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt[0], sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '5 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt[0], sqlCmd, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt[0], &config, subConn, SubCallback1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = 2;
    // 插入数据
    ret = BlukInsert_char(stmt[0], g_tableName, count, 3, id, time, name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t idCheck2[count] = {5, 5};
    CheckData(idCheck2);
    // 取消订阅
    ret = GmcUnSubscribe(stmt[0], g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropStreamView(stmt[0], g_viewName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt[0], g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn[0], stmt[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 2; i < MAX_CONN_SIZE - exitConn; i++) {
        ret = testGmcDisconnect(conn[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 38.订阅消息队列写满
 Author       : qwx620469
*****************************************************************************/
TEST_F(tsdb_rel_scene_sn, tsdb_rel_scene_sn_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    InitTsCiCfgPush();
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)DropStreamTable(stmt, g_tableName);
    (void)DropStreamView(stmt, g_viewName);
    //  初始化数据
    int64_t count = 1024;
    int64_t id[count] = {0};
    int64_t time[count] = {0};
    for (int i = 0; i < count; i++) {
        id[i] = i;
        time[i] = 1827563231;
    }
    char name[count][64] = {"david", "nut", "bob", "olivia", "tim", "lucy", "Tom", "deft", "night", "leyu"};
    char sqlCmd[256] = {0};
    char sqlQueryCmd[256] = {0};
    // 建表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64)) with (time_col = 'time',"
        "is_stream = 'true', step_time = '1 s', step_count = 1024, max_size = 5000, delay_time = '1 s', disk_limit = "
        "'4 "
        "MB');",
        g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 建视图
    (void)sprintf(sqlCmd, "create view %s with (window_size= '8 s', close_wait_time = '10 s') as select * from %s;",
        g_viewName, g_tableName);
    ret = CreateStreamTable(stmt, sqlCmd, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建订阅连接
    int chanRingLen = 64;
    ret = TestTsGmcConnect(&subConn, &subStmt, 2, (char *)g_subConnName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcSubConfigT config;
    config.subsName = "subQueryC1";
    config.configJson = R"({
        "name":"subQueryC1",
        "label_name":"testdb_view",
        "comment":"testdb_view subscription",
        "events":[{ "type":"insert", "msgTypes":["new object"] }]
    })";
    ret = GmcSubscribe(stmt, &config, subConn, SubCallback1, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    sleep(1);
    g_testDataInserted = true;
    g_winCnt = 0;
    g_fetchTimes = count;
    // 插入数据
    ret = BlukInsert_char(stmt, g_tableName, count, 3, id, time, name);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 取消订阅
    ret = GmcUnSubscribe(stmt, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    // 资源释放 去掉GmcFreeIndexKey(subStmt);，因为stmt prepare前后会清理内存
    ret = testSubDisConnect(subConn, subStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = DropStreamView(stmt, g_viewName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = DropStreamTable(stmt, g_tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
