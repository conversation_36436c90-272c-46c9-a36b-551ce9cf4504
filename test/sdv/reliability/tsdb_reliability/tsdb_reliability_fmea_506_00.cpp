/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 时序交付,GMDB 506.0.0时序FMEA
 * Author: chenbangjun
 * Create: 2025-05-28
 */
#include "gtest/gtest.h"
#include "t_rd_ts.h"
#include <sys/ipc.h>  // 共享内存需要的头文件
#include <sys/shm.h>  // 共享内存需要的头文件
#include "tsdb_reliability_fmea.h"
#include "gmc_test.h"

static GmcConnOptionsT *connOptions = NULL;
static GmcConnT *conn = NULL;
static GmcStmtT *stmt = NULL;
char tempDisk[20] = {0};
char g_cStoreDir[64] = {0};
char *dir = getenv("GMDB_HOME");
Status ret = 0;

#define CMD_LENGTH 2014
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#define MEGABYTESMALL (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#define MEGABYTESMALL (256 * 1024 * 1024)
#endif

class TsdbFmea50600 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbFmea50600::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    InitTsCiCfg();
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"crcCheckEnable=1\"");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    (void)sprintf(g_dataFilePath1, "%s/data/gmdb1/", g_filePath);
    (void)sprintf(g_dataFilePath2, "%s/data/gmdb2/", g_filePath);
    (void)sprintf(g_tableFilePath, "%s/gmdb/", g_filePath);
    (void)sprintf(g_dataFilePath, "%s", TABLE_PATH);
    TsDefulatDbFileClean();
    system("rm -rf ./data/");
    system("rm -rf ./gmdb/");
    system("mkdir -p ./data/gmdb1");
    system("mkdir -p ./data/gmdb2");
    system("mkdir -p ./gmdb");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void TsdbFmea50600::TearDown()
{
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    GmcConnOptionsDestroy(connOptions);
    GmcFreeStmt(stmt);
    GmcDisconnect(conn);
    close_epoll_thread();
    testEnvClean();
    RecoverTsCiCfg();
    system("sh $TEST_HOME/tools/gmserver_replace.sh recover_ts");
}

class TsdbFmea50600_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TsdbFmea50600_test1::SetUp()
{
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(g_dataFilePath2, "%s/data/gmdb2/", g_filePath);
}

void TsdbFmea50600_test1::TearDown()
{
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

// 001.tsAllowDiskClean默认值为1,写到阈值后,注入默认dataFilePath路径
// 不可读写,再次注入数据
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int64_t cycleCount = 20;
    for (int i = 0; i < cycleCount; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", g_dataFilePath);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.tsAllowDiskClean设置为0,循环注入数据写到超限后,注入默认dataFilePath路径
// 不可读写,再次注入数据
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "15 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int64_t cycleCount = 20;
    for (int i = 0; i < cycleCount; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", g_dataFilePath);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath);
    system(sqlCmd);
    system("mkdir /data/gmdb/tempFile");
    system("cp /data/gmdb/tsrow* /data/gmdb/tempFile/");
    sleep(3);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.tsAllowDiskClean设置为0,循环注入数据写到超限后,在线修改为1。
// 循环注入数据,此时注入默认dataFilePath路径不可读写
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=0\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int64_t cycleCount = 20;
    for (int i = 0; i < cycleCount; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
            AW_FUN_Log(LOG_STEP, "注入数据超限");
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 修改AllowDiskClean为0
    uint32_t setConfigValue = 1;
    ret = GmcSetCfg(stmt, "tsAllowDiskClean", GMC_DATATYPE_INT32, &setConfigValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", g_dataFilePath);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);

    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.建一张逻辑表,tsAllowDiskClean默认值为1,注入数据超限后,仍继续写入1000批次数据
// 预期：数据量符合预期,且不为0,没有hang死,没有启线程失败的错误日志
TEST_F(TsdbFmea50600, Timing_Fmea_50600_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int64_t cycleCount = 20;
    for (int i = 0; i < cycleCount; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
            AW_FUN_Log(LOG_STEP, "注入数据超限");
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    dataCount = 10000;
    cycleCount = 100;
    for (int i = 0; i < cycleCount; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
            AW_FUN_Log(LOG_STEP, "注入数据超限");
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = returnDataCount(stmt, g_tableName);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;

    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.建80张逻辑表,tsAllowDiskClean默认值为1,注入数据超限后,仍继续写入1000批次数据
// 预期：数据量符合预期,且不为0,没有hang死,没有启线程失败的错误日志
TEST_F(TsdbFmea50600, Timing_Fmea_50600_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"tsAllowDiskClean=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    char tempTableName[20] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "5 MB");
    for (int i = 0; i < 5; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    int64_t dataCount = 50000;
    int64_t startTime = 1262275200;
    int64_t cycleCount = 10;
    for (int i = 0; i < cycleCount; i++) {
        for (int j = 0; j < 5; j++) {
            (void)memset(tempTableName, 0, 20);
            (void)sprintf(tempTableName, "testdb%d", j);
            ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
            if (ret != GMERR_OK) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_TABLE_EXCEED_DISK_LIMIT, ret);
                AW_FUN_Log(LOG_STEP, "注入数据超限");
                break;
            }
        }
    }
    ret = returnDataCount(stmt, tempTableName);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, 512);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", tempTableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    for (int i = 0; i < 5; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.时序服务启动并建连后,构造默认/data/gmdb/目录不可读写,创建带table_path（非默认目录）的逻辑表
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", g_dataFilePath);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 此处注入故障后,注入数据首先操作cu,发现没有权限就退出了而没有操作行存,所以没有导致锁库
// 007.时序服务启动并建连后,创建带table_path的逻辑表,构造table_path目录不可读写,此时写入数据
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"", g_dataFilePath1);
    system(sqlCmd);
    // 等待故障注入
    sleep(20);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath1);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.时序服务启动并建连后,创建带table_path的逻辑表,循环写入数据此时构造table_path目录不可读写
// 预期：锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 10000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    for (int i = 0; i < 10; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
        }
        if (i == errorFlag) {
            // 注入磁盘不可读写故障
            (void)memset(sqlCmd, 0, CMD_LENGTH);
            (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"",
                g_dataFilePath1);
            system(sqlCmd);
        }
    }
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath1);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.时序服务启动并建连后,创建带table_path的逻辑表,构造table_path目录下cuFile文件损坏但不超过阈值（单分区）,查询注入数据
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DamageDataFile(false, 1, false, g_dataFilePath1);
    
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    ret = returnDataCount(stmt, g_tableName);
    EXPECT_LT(ret, dataNum);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.时序服务启动并建连后,创建带table_path的逻辑表,构造table_path目录下cuFile文件损坏超过阈值（单分区）,查询注入数据
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DamageDataFile(false, 1, true, g_dataFilePath1);
    
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    ret = returnDataCount(stmt, g_tableName);
    EXPECT_LT(ret, dataNum);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.时序服务启动并建连后,创建带table_path的逻辑表,构造table_path目录下cuMetaFile文件损坏（单分区）,查询注入数据
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DamageDataFile(true, 1, true, g_dataFilePath1);
    
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_CORRUPTION, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.时序服务启动并建连后,创建带table_path的逻辑表,构造table_path目录下行存文件损坏,查询注入数据
// 预期：行存文件损坏后，损坏前的数据被删除，后续操作不影响
TEST_F(TsdbFmea50600, Timing_Fmea_50600_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %stsrow*", g_dataFilePath1);
    system(sqlCmd);

    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(stmt, g_tableName);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, dataCount);

    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
  
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.时序服务启动并建连后,创建带table_path的逻辑表,循环写入数据,此时构造磁盘空间不足
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    for (int i = 0; i < 15; i++) {
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
        }
        if (i == errorFlag) {
            // 注入磁盘满故障
            (void)memset(sqlCmd, 0, CMD_LENGTH);
            (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_full (diskname) values (%s)\"",
                g_dataFilePath1);
            system(sqlCmd);
            system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
        }
    }
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DISK_NO_SPACE_ERROR, ret);
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_full where diskname = %s\"", g_dataFilePath1);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED, GMERR_DATABASE_NOT_AVAILABLE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.时序服务启动并建连后,创建带table_path的逻辑表和不带table_path（所在磁盘分区不一致）
// 80张表,循环写入数据,此时构造磁盘空间不足,指定table_path路径对应的磁盘空间不足
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    char tempTableName[20] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    for (int i = 0; i < 80; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        if (i < 40) {
            ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        
    }
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    int tempTable1 = 0;
    int tempTable2 = 0;
    for (int i = 0; i < 15; i++) {
        tempTable1 = rand()%39;
        tempTable2 = rand()%39 + 40;
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", tempTable1);
        ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DISK_NO_SPACE_ERROR, ret);
        }
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", tempTable2);
        ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
        }
        if (i == errorFlag) {
            // 注入磁盘满故障
            (void)memset(sqlCmd, 0, CMD_LENGTH);
            (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_full (diskname) values (%s)\"",
                g_dataFilePath1);
            system(sqlCmd);
            system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
        }
    }
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_full where diskname = %s\"", g_dataFilePath1);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.时序服务启动并建连后,创建带table_path的逻辑表和不带table_path（所在磁盘分区不一致）
// 80张表,循环写入数据,此时构造磁盘空间不足,指定默认路径对应的磁盘空间不足
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    char tempTableName[20] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    for (int i = 0; i < 80; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        if (i < 40) {
            ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        
    }
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    int tempTable1 = 0;
    int tempTable2 = 0;
    for (int i = 0; i < 15; i++) {
        tempTable1 = rand()%39;
        tempTable2 = rand()%39 + 40;
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", tempTable1);
        ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
        if (ret != GMERR_OK && ret != GMERR_FILE_OPERATE_FAILED && ret != GMERR_DISK_NO_SPACE_ERROR) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
        }
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", tempTable2);
        ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
        if (ret != GMERR_OK && ret != GMERR_FILE_OPERATE_FAILED && ret != GMERR_DISK_NO_SPACE_ERROR) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
        }
        if (i == errorFlag) {
            // 注入磁盘满故障
            (void)memset(sqlCmd, 0, CMD_LENGTH);
            (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_full (diskname) values (%s)\"",
                g_dataFilePath);
            system(sqlCmd);
            system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
        }
    }
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 等待故障注入
    sleep(20);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName2);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "select * from %s;", g_tableName);
    cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FILE_OPERATE_FAILED, ret);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 恢复磁盘故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_full where diskname = %s\"", g_dataFilePath);
    system(sqlCmd);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.TSDB支持OFFSET关键字查询时构造内存不足
// 预期：查询成功，结果正确
TEST_F(TsdbFmea50600, Timing_Fmea_50600_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    int tempTable1 = 0;
    int tempTable2 = 0;
    
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 20; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        count++;
    }
    system("cat /proc/meminfo | grep Free");

    (void)sprintf(sqlCmd, "select * from %s order by id, time limit 100000 offset 1000",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    system("cat /proc/meminfo | grep Free");

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.TSDB支持avg聚合函数查询时构造内存不足
// 预期：查询成功，结果正确
TEST_F(TsdbFmea50600, Timing_Fmea_50600_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 3;
    int tempTable1 = 0;
    int tempTable2 = 0;
    
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 20; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        count++;
    }
    system("cat /proc/meminfo | grep Free");

    (void)sprintf(sqlCmd, "select id, avg(id), avg(id+time), sum(id), * from %s"
        " order by id, time limit 100000 offset 1000",
        g_tableName);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    system("cat /proc/meminfo | grep Free");

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.创建逻辑表和内存表，写入数据，切换目录再切换回去，并检查视图
// 预期：切换前后，视图查询的数据符合预期
TEST_F(TsdbFmea50600, Timing_Fmea_50600_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建内存表
    (void)sprintf(sqlCmd,
        "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
        " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
        " enGine = 'mEmOry', max_size = 500000)",
        g_tableName3);
    uint32_t cmdLen = strlen(sqlCmd);
    ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName3, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 切换至目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd2[256] = {"DQL_SUCCESS_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 0);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = '%s'\"", g_tableName2);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 0);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where table_name = '%s'\"", g_tableName2);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 0);

    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
    ret = returnDataCount(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
    ret = returnDataCount(stmt, g_tableName3);
    AW_MACRO_EXPECT_EQ_INT(ret, 0);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = '%s'\"", g_tableName);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 380);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = '%s'\"", g_tableName2);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 380);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = '%s'\"", g_tableName3);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 0);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$TS_TBL_OPER_STATIS' where table_name = '%s'\"", g_tableName2);
    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd2, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(dataNum, 1);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.切换前构造CPU过载
// 预期：可能切换失败，也可能切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cpuNum = GetCpuNum();
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 90)\"", cpuNum - 1);
    system(sqlCmd);
    // 等待故障注入,CPU稳定大于80%
    sleep(20);
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    if (ret == GMERR_OK) {
        ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 1000000;
        int64_t startTime = 1262275200;
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.切换前构造内存不足
// 预期：可能切换失败，也可能切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 共享内存不足故障
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 20; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    for (int i = 0; i < 20; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        count++;
    }
    // 共享内存占满后设备会重启
    system("cat /proc/meminfo | grep Free");


    // 等待故障注入稳定
    sleep(20);
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    if (ret == GMERR_OK) {
        ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 1000000;
        int64_t startTime = 1262275200;
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

     // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    system("cat /proc/meminfo | grep Free");

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.切换前不断写系统IO
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cpuNum = GetCpuNum();
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rIO_iowait (type,dir,bs,count,time) values (w_iowait,%s,10000,10000,1000)\"", g_dataFilePath2);
    system(sqlCmd);
    // 等待故障注入稳定
    sleep(20);

    // 等待故障注入稳定
    sleep(20);
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    if (ret == GMERR_OK) {
        ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 1000000;
        int64_t startTime = 1262275200;
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 恢复磁盘故障
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rIO_iowait\"");
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 手工用例需要两个磁盘
// 022.从A目录启动，构造B目录磁盘满故障，切换至B目录，再切换至A目录，两个磁盘
// 预期：切换至B目录可能失败，写入数据锁库，切换至A目录成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入磁盘满故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_full (diskname) values (%s)\"",
        g_dataFilePath2);
    system(sqlCmd);
    system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
    // 等待故障注入稳定
    sleep(20);
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    if (ret == GMERR_OK) {
        ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 1000000;
        int64_t startTime = 1262275200;
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    
    
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_full where diskname = %s\"", g_dataFilePath1);
    system(sqlCmd);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 手工用例需要两个磁盘
// 023.从A目录启动，构造B目录不可读写，切换至B目录，再切换至A目录
// 预期：
TEST_F(TsdbFmea50600, Timing_Fmea_50600_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 注入磁盘满故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"",
        g_dataFilePath2);
    system(sqlCmd);
    system("cd /root/CFE_Tool/cfe;./cfe \"query rfile_full\"");
    // 等待故障注入稳定
    sleep(20);
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    if (ret == GMERR_OK) {
        ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t dataCount = 1000000;
        int64_t startTime = 1262275200;
        ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath2);
    system(sqlCmd);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024.建表设置table_path，is_volatile_label为true，写入数据。kill时序服务
// 删除table_path指定目录，构造table_path目录不可读写，启动时序服务
// 预期：启动失败
TEST_F(TsdbFmea50600, Timing_Fmea_50600_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %s*", g_dataFilePath1);
    system(sqlCmd);
    // 注入磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unrw (filename) values (%s)\"",
        g_dataFilePath1);
    system(sqlCmd);
    // 等待故障注入稳定
    sleep(20);
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    // 恢复磁盘不可读写故障
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unrw where filename = %s\"", g_dataFilePath1);
    system(sqlCmd);
    sleep(20);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cp tsdb_reliability_fmea_506_00.cpp %s", g_dataFilePath1);
    system(sqlCmd);
    system("df -h");
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    system("ps -ef|grep gmserver");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 025.建表设置table_path，is_volatile_label为true，写入数据。kili时序服务
// 损坏列存文件，启动时序服务
// 预期：第一次查询失败，后续查询注入等操作成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 1000000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    // 损坏列存文件
    DamageDataFile(false, 1, false, g_dataFilePath1);
    system("sh $TEST_HOME/tools/start.sh -ts");

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    EXPECT_LT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026.建表设置table_path，is_volatile_label为true，写入数据。kili时序服务
// 损坏行存文件，启动时序服务
// 预期：行存文件损坏后，损坏前的数据被删除，后续操作不影响
TEST_F(TsdbFmea50600, Timing_Fmea_50600_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %stsrow*", g_dataFilePath1);
    system(sqlCmd);

    // 等待故障注入稳定
    sleep(20);
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 027.建表设置table_path，is_volatile_label为true，写入数据。损坏行存文件，重启服务
// 预期：行存文件损坏后，损坏前的数据被删除，后续操作不影响
TEST_F(TsdbFmea50600, Timing_Fmea_50600_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %stsrow*", g_dataFilePath1);
    system(sqlCmd);

    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 028.开启磁盘整理，注入多批次，大量数据，每次注入一条数据。破坏列存文件
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 3000; i++) {
        ret = rowInsertData(stmt, g_tableName, 1, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, 1, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        startTime++;
    }
    // 损坏列存文件
    DamageDataFile(false, 1, false, g_dataFilePath1);
    DamageDataFile(false, 1, false, g_dataFilePath);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    ret = returnDataCount(stmt, g_tableName);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_NE_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    ret = returnDataCount(stmt, g_tableName2);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_NE_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 029.开启磁盘整理，注入多批次，大量数据，每次注入一条数据。破坏行存文件
// 预期：损坏后第一次查询失败,第二次成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 3000; i++) {
        ret = rowInsertData(stmt, g_tableName, 1, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertData(stmt, g_tableName2, 1, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        startTime++;
    }
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %stsrow*", g_dataFilePath1);
    system(sqlCmd);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "rm -rf %stsrow*", g_dataFilePath);
    system(sqlCmd);

    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(ret, 3000);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(ret, 3000);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = returnDataCount(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(ret, 3000);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName2);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);

    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.开启磁盘整理，注入大量批次数据，每次注入一条数据。并发时序服务端重启
// 预期：重启成功，数据准确
TEST_F(TsdbFmea50600, Timing_Fmea_50600_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%20 + 30;
    for (int i = 0; i < 3000; i++) {
        ret = rowInsertData(stmt, g_tableName, 1, startTime);
        if (ret != GMERR_OK) {
            break;
        }
        if (i == errorFlag) {
            pthread_create(&tid[0], NULL, ConcurrentRestart, NULL);
            break;
        }
        startTime++;
    }
    pthread_join(tid[0], NULL);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(errorFlag, ret -1);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 031.开启磁盘整理,建带table_path的逻辑表,注入大量批次数据，每次注入一条数据。并发时序服务端重启
// 预期：重启成功，数据准确
TEST_F(TsdbFmea50600, Timing_Fmea_50600_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadCount = 1;
    pthread_t tid[threadCount];
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 100000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%20 + 30;
    for (int i = 0; i < 3000; i++) {
        ret = rowInsertData(stmt, g_tableName, 1, startTime);
        if (ret != GMERR_OK) {
            break;
        }
        if (i == errorFlag) {
            pthread_create(&tid[0], NULL, ConcurrentRestart, NULL);
            break;
        }
        startTime++;
    }
    pthread_join(tid[0], NULL);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempSqlCmd1[256] = {"ROW_CNT"};
    char tempSqlCmd3[256] = {"awk -F ':' '{print $2}'"};
    int dataNum = 0;
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(errorFlag, ret - 1);
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select * from 'V\\$STORAGE_DISK_USAGE' where table_name = '%s'\"", g_tableName);

    dataNum = GetKeyWordValue(sqlCmd, tempSqlCmd1, tempSqlCmd3);
    AW_MACRO_EXPECT_EQ_INT(ret, dataNum);
    AW_MACRO_EXPECT_NE_INT(ret, 0);
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 032.多张内存表，插入大量数据，插入时构造内存不足
// 预期：插入数据失败
TEST_F(TsdbFmea50600, Timing_Fmea_50600_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTableName[20] = {0};
    char sqlCmdMemory[CMD_LENGTH] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 10; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)memset(sqlCmdMemory, 0, CMD_LENGTH);
        (void)sprintf(tempTableName, "testdb%d", i);
        // 创建内存表
        (void)sprintf(sqlCmdMemory,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
            " enGine = 'mEmOry', max_size = 500000)",
            tempTableName);
        cmdLen = strlen(sqlCmdMemory);
        ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t dataCount = 10000;
    int64_t startTime = 1262275200;
    int errorFlag = rand()%5 + 5;
    int count = 0;
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 20; i++) {
        for (int j = 0; j < 10; j++) {
            (void)memset(tempTableName, 0, 20);
            (void)sprintf(tempTableName, "testdb%d", j);
            ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
            if (ret != GMERR_OK) {
                break;
            }
        }
        if (i == errorFlag) {
            
            for (int i = 0; i < 20; i++) {
                shmid[i] = 0;
                key[i] = i + 12345;
            }
            for (int i = 0; i < 20; i++) {
                shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
                AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
                if (shmid[i] == -1) {
                    break;
                }
                char *p;
                p = (char *)shmat(shmid[i], NULL, 0);
                // 对共享内存实现读写
                memset(p, 1, MEGABYTE);
                // 取消映射
                shmdt(p);
                count++;
            }
            system("cat /proc/meminfo | grep Free");
        }
    }
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    system("cat /proc/meminfo | grep Free");
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 033.多张内存表，插入大量数据，查询时构造内存不足
// 预期：查询失败
TEST_F(TsdbFmea50600, Timing_Fmea_50600_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTableName[20] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char sqlCmdMemory[CMD_LENGTH] = {0};
    uint32_t cmdLen = 0;
    int64_t dataCount = 10000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 10; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)memset(sqlCmdMemory, 0, CMD_LENGTH);
        (void)sprintf(tempTableName, "testdb%d", i);
        // 创建内存表
        (void)sprintf(sqlCmdMemory,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
            " enGine = 'mEmOry', max_size = 500000)",
            tempTableName);
        cmdLen = strlen(sqlCmdMemory);
        ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < 10; j++) {
            ret = rowInsertData(stmt, tempTableName, dataCount, startTime);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    int errorFlag = rand()%5 + 5;
    int count = 0;
    int shmid[20];
    key_t key[20];
    for (int i = 0; i < 10; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        (void)sprintf(sqlCmd, "select * from %s", tempTableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (ret != GMERR_OK) {
            break;
        }
        if (i == errorFlag) {
            for (int i = 0; i < 20; i++) {
                shmid[i] = 0;
                key[i] = i + 12345;
            }
            for (int i = 0; i < 20; i++) {
                shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
                AW_FUN_Log(LOG_INFO, "shmid is %d  i is %d\n", shmid[i], i);
                if (shmid[i] == -1) {
                    break;
                }
                char *p;
                p = (char *)shmat(shmid[i], NULL, 0);
                // 对共享内存实现读写
                memset(p, 1, MEGABYTE);
                // 取消映射
                shmdt(p);
                count++;
            }
            system("cat /proc/meminfo | grep Free");
        }
    }
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    system("cat /proc/meminfo | grep Free");
    
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 034、一张逻辑表多个（100）分区，进行drop操作，建多张内存表，插入查询数据
// 预期：插入数据失败
TEST_F(TsdbFmea50600, Timing_Fmea_50600_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"lockTableBucketNum=11\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"cuCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "1024 MB");
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, g_dataFilePath1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t dataCount = 500000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 150; i++) {
        ret = rowInsertDataTimeLow(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = rowInsertDataTimeLow(stmt, g_tableName2, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        startTime += 3600;
    }
    ret = DropTable(stmt, g_tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tempTableName[20] = {0};
    char sqlCmdMemory[CMD_LENGTH] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 20; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)memset(sqlCmdMemory, 0, CMD_LENGTH);
        (void)sprintf(tempTableName, "test_db%d", i);
        // 创建内存表
        (void)sprintf(sqlCmdMemory,
            "create table %s(id integer, time integer, name char(64), ip inet, message blob(160),"
            " description text, id1 integer, time1 integer)with (time_col = 'time', interval = '1 h', "
            " enGine = 'mEmOry', max_size = 500000)",
            tempTableName);
        cmdLen = strlen(sqlCmdMemory);
        ret = GmcExecDirect(stmt, sqlCmdMemory, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035、80张表注入多分区数据，循环执行Truncate表，此时构造服务端异常重启
// 预期：重启前Truncate的表数据为空，剩余表数据不为空，再次Truncate后数据全为空
TEST_F(TsdbFmea50600, Timing_Fmea_50600_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    char tempTableName[20] = {0};
    uint32_t cmdLen = 0;
    for (int i = 0; i < 80; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, NULL, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int64_t dataCount = 500000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 80; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = rowInsertDataTimeLow(stmt, tempTableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int threadCount = 1;
    pthread_t tid[threadCount];
    pthread_create(&tid[0], NULL, ConcurrentRestart, NULL);
    int errorFlag = rand()%10 + 30;
    for (int i = 0; i < 80; i++) {
        (void)memset(sqlCmd, 0, CMD_LENGTH);
        (void)sprintf(sqlCmd, "TRUNCATE TABLE testdb%d", i);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
            break;
        }
        if (i == errorFlag) {
            pthread_create(&tid[0], NULL, ConcurrentRestart, NULL);
            break;
        }
    }
    pthread_join(tid[0], NULL);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = errorFlag; i < 80; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 036、多张逻辑表多个(100)分区，进行drop操作，修改ttl使其触发
// 预期：drop和ttl操作均成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTtl[20] = {"1 hour"};
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    char tempTableName[20] = {0};
    uint32_t cmdLen = 0;
    int64_t dataCount = 50000;
    int64_t startTime = 1262275200;
    for (int i = 0; i < 20; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = CreateTable(stmt, tempTableName, g_intervalHour, tempDisk, tempTtl, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int j = 0; j < 100; j++) {
            ret = rowInsertDataTimeLow(stmt, tempTableName, dataCount, startTime + j * 3600);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "注入数据结束");
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid;
    InsertQueryDataType constructDataType_1 = {stmt1, "testdb", 20, 0};
    int errorFlag = rand()%5 + 3;
    for (int i = 0; i < 20; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i == errorFlag) {
            pthread_create(&tid, NULL, ConcurrentAlter, &constructDataType_1);
        }
    }
    pthread_join(tid, NULL);
    AW_FUN_Log(LOG_STEP, "删表结束");
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037、构造大量数据,select a + b，查询时构造内存不足
// 预期：聚合、排序时，查询成功，结果正确
TEST_F(TsdbFmea50600, Timing_Fmea_50600_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject_ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnectTime(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTtl[20] = {"1 hour"};
    char sqlCmd[CMD_LENGTH] = {0};
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "200 MB");
    char tempTableName[20] = {0};
    uint32_t cmdLen = 0;
    int64_t dataCount = 5000000;
    int64_t startTime = 1262275200;
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertDataTimeLow(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 注入共享内存不足故障
    ret = TestCollectlibPath((char *)"shm_traverse_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    AW_FUN_Log(LOG_INFO, "111111 isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n", 
        injectInfo->isFirstCal, injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    for (int i = 0; i < 10; i++) {
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        (void)sprintf(sqlCmd,
            "select iD, time, ip, name, description, id + id1, id + time1 from %s"
            " where id + time >= 1262275200 group by name, id order by id ",
            g_tableName);
        cmdLen = strlen(sqlCmd);
        ret = GmcExecDirect(stmt, sqlCmd, cmdLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "222222 isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n ", injectInfo->isFirstCal,
            injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    }
    // 恢复动态内存不足故障
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("cat /proc/meminfo | grep Free");
    ret = DropTable(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 038、目录切换过程中，模拟设备复位
// 预期：各项操作预期正常，表存在数据存在
TEST_F(TsdbFmea50600, Timing_Fmea_50600_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadCount = 4;
    char tableName[256] = {0};
    int dataCount = 1000;
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int errorFlag = rand()%3 + 3;
    for (int i = 0; i < 20; i++) {
        // 切换至目录B
        ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        }
        sleep(1);
        ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
        }
        if (i == errorFlag) {
            pthread_create(&tid[3], NULL, ConcurrentDeviceReset, NULL);
        }
    }
    pthread_join(tid[3], NULL);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, tableName);
        if (i < 1) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        }
        ret = DropTable(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 039、目录切换后，模拟设备复位
// 预期
TEST_F(TsdbFmea50600, Timing_Fmea_50600_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int threadCount = 4;
    char tableName[256] = {0};
    int dataCount = 1000;
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_create(&tid[3], NULL, ConcurrentDeviceReset, NULL);
    pthread_join(tid[3], NULL);
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, tableName);
        if (i < 1) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        }
        ret = DropTable(stmt, tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 040、cpu100%过载下，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int cpuNum = GetCpuNum();
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rCPU_Overloadal (cpuid1, cpuid2, usage) values (0, %d, 100)\"", cpuNum - 1);
    system(sqlCmd);
    // 等待故障注入,CPU稳定大于80%
    sleep(20);
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 恢复环境
    system("cd /root/CFE_Tool/cfe;./cfe \"clean rCPU_Overloadal\"");
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, 0);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 041、权限错误时进行目录切换
// 预期：切换失败，切换会原目录成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 构造迁入目录不可写
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_unwrite (filename) values (%s)\"",
        g_dataFilePath2);
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);
    // 切换至目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FILE_OPERATE_FAILED);
    // 恢复环境
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_unwrite where filename = %s\"",
        g_dataFilePath2);
    system(sqlCmd);
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);
    }
    // 切换至目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    // 切换会原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = returnDataCount(stmt, tableName);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 042、文件系统只读(具体到某个文件)，目录切换
// 预期：
TEST_F(TsdbFmea50600, Timing_Fmea_50600_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_STEP, "i is %d", i);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 构造dbCtrlFile文件不可读
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_readonly (filename) values (%sdbCtrlFile)\"",
        g_dataFilePath);
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);

    // 切换回原目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_FILE_OPERATE_FAILED);
    // 恢复环境
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_readonly where filename = %sdbCtrlFile\"",
        g_dataFilePath);
    system(sqlCmd);

    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 043、时钟向前跳变后，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = 1262275200; // 2010-01-01:00
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb1'\"");
    partitioningBefore = GetKeyWordValue(sqlCmd, "UPPER_BOUND_TIME_STR", cutSymbol);
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 构造dbCtrlFile文件不可读
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (-,7200)\"");
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);

    // 切换回原目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb1'\"");
    partitioningAfter = GetKeyWordValue(sqlCmd, "UPPER_BOUND_TIME_STR", cutSymbol);
    // 恢复环境
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rSysClockJump where DIRECTION = - and OFFSET=7200\"");
    system(sqlCmd);

    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 3);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        }
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 044、时钟向后跳变后，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = 1262275200; // 2010-01-01:00
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb1'\"");
    partitioningBefore = GetKeyWordValue(sqlCmd, "UPPER_BOUND_TIME_STR", cutSymbol);
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 构造dbCtrlFile文件不可读
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (+,7200)\"");
    system(sqlCmd);
    // 等待故障稳定
    sleep(5);

    // 切换回原目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$PHY_TBL_DISK_USAGE' where LOGIC_LABEL_NAME = 'testdb1'\"");
    partitioningAfter = GetKeyWordValue(sqlCmd, "UPPER_BOUND_TIME_STR", cutSymbol);
    // 恢复环境
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, 
        "cd /root/CFE_Tool/cfe;./cfe \"clean rSysClockJump where DIRECTION = + and OFFSET=7200\"");
    system(sqlCmd);

    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 3);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        }
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 045、易失性路径行存文件丢失，目录切换至该目录
// 预期：
TEST_F(TsdbFmea50600, Timing_Fmea_50600_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 删除原目录下列存文件
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd,
        "rm -rf %scstore", g_dataFilePath);
    system(sqlCmd);
    // 切换回原目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
        }
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i == 0) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
        } else if( i == 1) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount*2);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        }
        ret = DropTable(stmt, g_tableName);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 046、行存文件损坏，目录切换至该目录
// 预期：切换失败后锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "ls %s| grep tsrow",
        g_dataFilePath);
    FILE *fptr = popen(sqlCmd, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    char tempFilePath[250] = {0};
    fgets(tempFilePath, sizeof(tempFilePath), fptr);
    fclose(fptr);
    tempFilePath[strlen(tempFilePath) - 1] = '\0';  //替换结尾换行符

    // 损坏行存文件
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%s%s)\"",
        g_dataFilePath, tempFilePath);
    system(sqlCmd);
    sleep(20);
    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    }
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %s%s\"",
        g_dataFilePath, tempFilePath);
    // 取消文件损坏
    system(sqlCmd);
    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);
        ret = DropTable(stmt, g_tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);
        ret = DropTable(stmt, g_tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 047、易失性路径目录损坏，目录切换至该目录
// 预期：切换失败后锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "ls %s| grep tsrow",
        g_tableFilePath);
    FILE *fptr = popen(sqlCmd, "r");
    if (fptr == NULL) {
        ASSERT_TRUE(false);
    }
    char tempFilePath[250] = {0};
    fgets(tempFilePath, sizeof(tempFilePath), fptr);
    fclose(fptr);
    tempFilePath[strlen(tempFilePath) - 1] = '\0';  //替换结尾换行符

    // 损坏行存文件
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%s%s)\"",
        g_tableFilePath, tempFilePath);
    system(sqlCmd);
    sleep(20);
    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    }
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %s%s\"",
        g_tableFilePath, tempFilePath);
    // 取消文件损坏
    system(sqlCmd);
    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_UNDEFINED_TABLE);
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATABASE_NOT_AVAILABLE);
        ret = DropTable(stmt, g_tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 048、系统文件损坏，目录切换至该目录
// 预期：dbCtrlFile文件损坏后，切换失败但不锁库
TEST_F(TsdbFmea50600, Timing_Fmea_50600_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 损坏行存文件
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"inject rfile_fs_broken (filename) values (%sdbCtrlFile)\"",
        g_dataFilePath);
    system(sqlCmd);
    sleep(20);
    // 切换回原目录A，切换前会校验一下新目录,发现持久化文件损坏，切换失败但不会卸载旧目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_DATA_CORRUPTION);
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset(sqlCmd, 0, CMD_LENGTH);
    (void)sprintf(sqlCmd, "cd /root/CFE_Tool/cfe;./cfe \"clean rfile_fs_broken where filename = %sdbCtrlFile\"",
        g_dataFilePath);
    // 取消文件损坏
    system(sqlCmd);
    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("sh $TEST_HOME/tools/stop.sh -ts");
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i == 0) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        } else if( i == 1) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, 0);
        }
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 049、构造服务端锁库，目录切换
// 预期：锁库后，切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    int partitioningBefore = 0;
    int partitioningAfter = 0;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 构造锁库
    ret = GmcSetDbEmergency(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换回原目录A
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i < 2) {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount * 2);
        } else {
            AW_MACRO_EXPECT_EQ_INT(ret, dataCount);
        }
        ret = DropTable(stmt, tempTableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 050、修改时区后，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    AW_FUN_Log(LOG_STEP, "local time: %s", asctime(local_tm));
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 修改时区为UTC-1
    setenv("TZ", "UCT-1", 1);
    tzset();
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换至空目录B
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 修改时区为Asia/Shanghai
    setenv("TZ", "Asia/Shanghai", 1);
    tzset();

    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 051、进行目录切换，服务端异常退出
// 预期：切换失败（根据共享内存，存的地址启动db）
TEST_F(TsdbFmea50600, Timing_Fmea_50600_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    AW_FUN_Log(LOG_STEP, "local time: %s", asctime(local_tm));
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 并发切换目录和异常退出
    InsertQueryDataType swapData = {stmt, g_dataFilePath2};
    pthread_create(&tid[0], NULL, ConcurrentSwapDataDir, &swapData);
    pthread_create(&tid[1], NULL, ConcurrentRestart, NULL);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select DEFAULT_TABLE_PATH from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\" -s %s",
        g_connServerTsdb);
    char result[512] = {0};
    GetViewFieldResultValueString(result, sqlCmd, "DEFAULT_TABLE_PATH");
    AW_FUN_Log(LOG_INFO, "current DB started path is %s", result);

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tempCompare[100] = {0};
    (void)memset(tempCompare, 0, 100);
    (void)sprintf(tempCompare, " %s\n", TABLE_PATH);
    // 根据启动地址进行目录切换动作
    if (strcmp(result, tempCompare) == 0) {
        // 切换回至当前目录
        ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(0, ret);

        // 切换回原目录
        ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
        AW_MACRO_EXPECT_EQ_INT(0, ret);
    } else {
        // 切换回原目录
        ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
        AW_MACRO_EXPECT_EQ_INT(0, ret);
    }

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *SwipCurrrntStopClint(void *arg)
{
    system("./tsdb_reliability_fmea_506_00 --gtest_also_run_disabled_tests "
           "--gtest_filter=TsdbFmea50600_test1.swipCurrrntStopClint_001");
    return nullptr;
}

// 构造客户端异常退出场景使用
// 预期：可能切换成功
TEST_F(TsdbFmea50600_test1, swipCurrrntStopClint_001)
{   
    AW_FUN_Log(LOG_STEP, "test start.");
    // 置为空
    GmcConnT *conn_1 = NULL;
    GmcStmtT *stmt_1 = NULL;
    // 建连
    int ret = TestTsGmcConnect(&conn_1, &stmt_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换至当前目录
    ret = GmcSwapDataDir(stmt_1, g_dataFilePath2, NULL);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 052、进行目录切换，客户端异常退出
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char g_errorCode01[256] = {0};
    char g_errorCode02[256] = {0};
    char g_errorCode03[256] = {0};
    int pid;
    // 客户端异常退出，仿真环境有15004错误码
    (void)snprintf(g_errorCode01, 256, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode02, 256, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, 256, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
#ifdef ENV_RTOSV2
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
#else
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
#endif
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启一个线程，拉起子进程场景，执行目录切换动作
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    ret = pthread_create(&thr_arr[0], NULL, SwipCurrrntStopClint, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "ls -l %s |wc -l", g_dataFilePath2);

    ret = executeCommand(sqlCmd, "1");
    while (ret == 0) {
        sleep(0.2);
        ret = executeCommand(sqlCmd, "1");
    }
    // 等待子进程退出
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = TestGetResultCommand("ps -ef |grep gtest_also_run_disabled_tests "
                               "|grep -v grep |grep -v sh |awk '{print $2}'",
        &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
    AW_FUN_Log(LOG_STEP, "pid is %d\n", pid);
#else
    ret = GtExecSystemCmd("ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'", &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    ret = kill(pid, SIGKILL);

    sleep(5);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select DEFAULT_TABLE_PATH from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\" -s %s",
        g_connServerTsdb);
    char result[512] = {0};
    GetViewFieldResultValueString(result, sqlCmd, "DEFAULT_TABLE_PATH");
    AW_FUN_Log(LOG_INFO, "current DB started path is %s", result);

    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053、进行目录切换，挂起服务端进程
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    // 目录切换时挂起服务端
    InsertQueryDataType swapData = {stmt, g_dataFilePath2};
    pthread_create(&tid[0], NULL, ConcurrentSwapDataDir, &swapData);

    system("kill -19 $(pgrep 'gmserver_ts')");
    sleep(20);
    system("kill -18 $(pgrep 'gmserver_ts')");
    pthread_join(tid[0], NULL);

    sleep(5);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select DEFAULT_TABLE_PATH from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\" -s %s",
        g_connServerTsdb);
    char result[512] = {0};
    GetViewFieldResultValueString(result, sqlCmd, "DEFAULT_TABLE_PATH");
    AW_FUN_Log(LOG_INFO, "current DB started path is %s", result);

    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054、循环进行目录切换，挂起客户端进程
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char g_errorCode01[256] = {0};
    char g_errorCode02[256] = {0};
    char g_errorCode03[256] = {0};
    int pid;
    // 客户端异常退出，仿真环境有15004错误码
    (void)snprintf(g_errorCode01, 256, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(g_errorCode02, 256, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, 256, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
#ifdef ENV_RTOSV2
    AW_ADD_ERR_WHITE_LIST(3, g_errorCode01, g_errorCode02, g_errorCode03);
#else
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
#endif
    AW_FUN_Log(LOG_STEP, "test start.");
    // 开启一个线程，拉起子进程场景，执行目录切换动作
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    ret = pthread_create(&thr_arr[0], NULL, SwipCurrrntStopClint, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)sprintf(sqlCmd, "ls -l %s |wc -l", g_dataFilePath2);

    ret = executeCommand(sqlCmd, "1");
    while (ret == 0) {
        sleep(0.2);
        ret = executeCommand(sqlCmd, "1");
    }
    // 等待子进程退出
#if defined(RUN_INDEPENDENT) || defined(ENV_RTOSV2)
    ret = TestGetResultCommand("ps -ef |grep gtest_also_run_disabled_tests "
                               "|grep -v grep |grep -v sh |awk '{print $2}'",
        &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps -ef |grep gtest_also_run_disabled_tests |grep -v grep");
    AW_FUN_Log(LOG_STEP, "pid is %d\n", pid);
#else
    ret = GtExecSystemCmd("ps |grep gtest_also* |grep -v grep |grep -v sh |awk '{print $1}'", &pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("ps |grep gtest_also* |grep -v grep");
#endif
    ret = kill(pid, SIGSTOP);

    sleep(5);
    ret = kill(pid, SIGCONT);
    (void)sprintf(sqlCmd,
        "gmsysview -sql \"select DEFAULT_TABLE_PATH from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\" -s %s",
        g_connServerTsdb);
    char result[512] = {0};
    GetViewFieldResultValueString(result, sqlCmd, "DEFAULT_TABLE_PATH");
    AW_FUN_Log(LOG_INFO, "current DB started path is %s", result);

    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055、反复重启服务端进程后，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i  = 0; i < 10; i++) {
        system("sh $TEST_HOME/tools/stop.sh -ts");
        system("sh $TEST_HOME/tools/start.sh -ts");
    }

    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到新目录目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056、循环进行目录切换，向服务端发送coredump信号
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 发送给服务端coredump信号
    system("kill -11 $(pgrep 'gmserver_ts')");
    // 重新拉起服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    // 发送coredump信号后,构建机器上偶现重新拉起失败，需要重启
    ret = TestTsGmcConnect(&conn, &stmt);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
        system("sh $TEST_HOME/tools/stop.sh -ts");
        system("sh $TEST_HOME/tools/start.sh -ts");
        ret = TestTsGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换到新目录目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057、循环进行目录切换，向服务端发送terminate信号
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 发送给服务端terminate信号
    system("kill -15 $(pgrep 'gmserver_ts')");
    // 重新拉起服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到新目录目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058、循环进行目录切换，向服务端发送ignore信号
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {1, 2, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 发送给服务端ignore信号
    system("kill -2 $(pgrep 'gmserver_ts')");
    // 重新拉起服务
    system("sh $TEST_HOME/tools/start.sh -ts");
    ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 切换到新目录目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    for (int i = 0; i < 3; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char tempTableName[20] = {0};
    for (int i = 0; i < 3; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 2) {
            AW_MACRO_EXPECT_EQ_INT(dataCount * 2, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059、满规格表数目（1021），目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1000, 0, 0};
    CreateInfo createInfo1 = {21, 1, 1000};
    CreateInfo createInfo2 = {1, 2, 1021};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 1021; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb%d", i);
        ret = rowInsertData(stmt, tableName, dataCount, startTime);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 切换到新目录目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 写入数据
    (void)memset(sqlCmd, 0, 256);
    (void)sprintf(sqlCmd, "gmsysview -sql \"select * from 'V\\$TS_TBL_PROPS' where table_name = 'testdb1'\"");
    ret = GetKeyWordValue(sqlCmd, g_dataFilePath, cutSymbol);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    char tempTableName[20] = {0};
    for (int i = 0; i < 1021; i++) {
        (void)memset(tempTableName, 0, 20);
        (void)sprintf(tempTableName, "testdb%d", i);
        ret = returnDataCount(stmt, tempTableName);
        if (i != 1021) {
            AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060、锁资源耗尽，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 1000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    CreateInfo createInfo0 = {1, 0, 0};
    CreateInfo createInfo1 = {1, 1, 1};
    CreateInfo createInfo2 = {98, 0, 2};
    pthread_create(&tid[0], NULL, ConcurrentCreateTableByParam1, &createInfo0);
    pthread_create(&tid[1], NULL, ConcurrentCreateTableByParam1, &createInfo1);
    pthread_create(&tid[2], NULL, ConcurrentCreateTableByParam1, &createInfo2);
    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
    pthread_join(tid[2], NULL);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    // 写入数据
    for (int i = 0; i < 1000; i++) {
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb0");
        ret = rowInsertData(stmt, tableName, dataCount, startTime + 3600 * i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        (void)memset(tableName, 0, 256);
        (void)sprintf(tableName, "testdb1");
        ret = rowInsertData(stmt, tableName, dataCount, startTime + 3600 * i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = TestTsGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    InsertQueryDataType constructDataType_1 = {stmt1, g_tableName, 0, 0};
    pthread_create(&tid[3], NULL, ConcurrentDeleteTable, &constructDataType_1);
    DbSleep(500);
    // 切换到新目录目录
    int ret1 = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
    pthread_join(tid[3], NULL);
    // 大量分区删表时构建机器上偶现超时，此处调整切换位置
    if (ret1 != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret1);
        // 等待后台线程走完后切换到新目录
        sleep(3);
        ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    if (ret != GMERR_UNDEFINED_TABLE) {
        AW_MACRO_EXPECT_EQ_INT(dataCount * 1000, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061、系统动态内存不足，目录切换
// 预期：切换成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject_ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char tempDisk[20] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 100000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入动态内存不足故障
    ret = TestCollectlibPath((char *)"memory_traverse_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;
    injectInfo->totalCnt = 1;
    injectInfo->injectCnt = 1;
    for (int i = 0; i < injectInfo->totalCnt; i++) {
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        // 切换到新目录目录
        ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 恢复动态内存不足故障
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(dataCount, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062、系统共享内存不足，目录切换
// 预期：切换失败，后续操作报锁库，切换回原目录成功
TEST_F(TsdbFmea50600, Timing_Fmea_50600_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/gmserver_replace.sh inject_ts");
    system("sh $TEST_HOME/tools/modifyCfg.sh -ts \"diskLessBoot=1\"");
    system("sh $TEST_HOME/tools/start.sh -ts");
    int ret = TestTsGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char tableName[256] = {0};
    char sqlCmd[CMD_LENGTH] = {0};
    char tempDisk[20] = {0};
    char cutSymbol[20] = {"wc -l"};
    int dataCount = 100000;
    int threadCount = 4;
    pthread_t tid[threadCount];
    (void)memset(tempDisk, 0, 20);
    (void)sprintf(tempDisk, "10 MB");
    ret = CreateTable(stmt, g_tableName, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateTable(stmt, g_tableName2, g_intervalHour, tempDisk, NULL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 获取当前时间
    time_t time_T;
    time_T = time(NULL);
    struct tm *local_tm = localtime(&time_T);
    // 注入数据到检查时间全部在1h以外
    int64_t startTime = time_T + 3600;
    ret = rowInsertData(stmt, g_tableName, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = rowInsertData(stmt, g_tableName2, dataCount, startTime);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入动态内存不足故障
    ret = TestCollectlibPath((char *)"shm_traverse_inject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;
    injectInfo->totalCnt = 1;
    injectInfo->injectCnt = 1;
    for (int i = 0; i < injectInfo->totalCnt; i++) {
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        // 切换到新目录目录
        ret = GmcSwapDataDir(stmt, g_dataFilePath2, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    }
    // 恢复动态内存不足故障
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATABASE_NOT_AVAILABLE, ret);
    // 切换回原目录
    ret = GmcSwapDataDir(stmt, g_dataFilePath, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = returnDataCount(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(dataCount, ret); 
    AW_FUN_Log(LOG_STEP, "test end.");
}



