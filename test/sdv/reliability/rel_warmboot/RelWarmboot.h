/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: rel_warmboot
 * Author: guankeke
 * Create: 2025-06-05
 */
#ifndef REL_WARMBOOT_H
#define REL_WARMBOOT_H

#include <unistd.h>
#include <sys/mman.h>
#include "t_datacom_lite.h"
#include "t_rd_inject.h"

#define MAX_NAME_LENGTH 128
#define MAX_CMD_SIZE 1024
char *g_cfeExePath = (char *)"/root/CFE_Tool/cfe/cfe";
char *g_schemaDir = (char *)"./reliability/rel_warmboot/schemaFile";

// Run the cmd command to obtain the command output
int GetResultOfCmd(const char *cmd, char *value)
{
    FILE *pipe = popen(cmd, "r");
    if (!pipe) {
        printf("[LLAGetValueOfCmd] popen() failed");
        return -1;
    }
    array<char, 1024> buffer{};
    string result;
    while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {
        result.append(buffer.data());
    }
    int ret = pclose(pipe);
    if (ret == -1) {
        printf("pclose failed, errno = %d.", errno);
    }
    (void)snprintf(value, result.size(), "%s", result.c_str());
    return 0;
}

void TestDropVertexLabelAll(GmcStmtT *stmt, uint32_t tableNum = 2)
{
    int ret = 0;
    char labelName[MAX_NAME_LENGTH] = {0};

    // 删除导入的表
    GmcDropVertexLabel(stmt, "sysDB0");
    GmcDropVertexLabel(stmt, "sysTable0");

    for (uint32_t i = 1; i <= tableNum; i++) {
        (void)snprintf(labelName, MAX_NAME_LENGTH, "table%d", i);
        GmcDropVertexLabel(stmt, labelName);
    }
    AddWhiteList(GMERR_UNDEFINED_TABLE);
}

void TestStartDbWarmReboot()
{
    (void)system("sh $TEST_HOME/tools/stop.sh -f");
    (void)system("sh $TEST_HOME/tools/start.sh -f -rb");
}

/* // Insert
void TestInsertVertex1(GmcStmtT *stmt, const char *labelname, uint32_t startNum, uint32_t endNum)
{
    int ret;
    uint32_t i = 0;

    // insert vertex
    for (i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[22];
        (void)memset_s(keyV1, 22, 0, 22);
        (void)snprintf(keyV1, 22, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Insert
void TestInsertVertex1Big(GmcStmtT *stmt, const char *labelname)
{
    int ret;
    uint32_t i = 0;
    uint32_t j = 0;
    uint32_t value = 0;

    // insert vertex
    for (j = 1; j <= 100; j++) {
        for (i = 0; i < 1000; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            value = j * 1000 + i;

            // 写数据
            char keyV1[22];
            (void)memset_s(keyV1, 22, 0, 22);
            (void)snprintf(keyV1, 22, "key%d", value);

            char valueV1[52];
            (void)memset_s(valueV1, 52, 0, 52);
            (void)snprintf(valueV1, 52, "value%d", value);

            ret = GmcSetVertexProperty(stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

// Insert
void TestInsertVertex2(GmcStmtT *stmt, const char *labelname, uint32_t startNum, uint32_t endNum)
{
    int ret;
    uint32_t i = 0;

    // insert vertex
    for (i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        char keyV1[32];
        (void)memset_s(keyV1, 32, 0, 32);
        (void)snprintf(keyV1, 32, "key%d", i);

        char valueV1[52];
        (void)memset_s(valueV1, 52, 0, 52);
        (void)snprintf(valueV1, 52, "value%d", i);

        ret = GmcSetVertexProperty(stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

// Insert
void TestInsertVertex2Big(GmcStmtT *stmt, const char *labelname)
{
    int ret;
    uint32_t i = 0;
    uint32_t j = 0;
    uint32_t value = 0;

    // insert vertex
    for (j = 1; j <= 100; j++) {
        for (i = 0; i < 1000; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            value = j * 1000 + i;

            // 写数据
            char keyV1[32];
            (void)memset_s(keyV1, 32, 0, 32);
            (void)snprintf(keyV1, 32, "key%d", value);

            char valueV1[52];
            (void)memset_s(valueV1, 52, 0, 52);
            (void)snprintf(valueV1, 52, "value%d", value);

            ret = GmcSetVertexProperty(stmt, "Key", GMC_DATATYPE_BYTES, keyV1, strlen(keyV1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "Value", GMC_DATATYPE_BYTES, valueV1, strlen(valueV1));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}
 */
void TestQueryBlockStat(bool isRsmExtendBlock)
{
    int ret;

    // STORAGE_RSM_BLOCK_STAT 字段校验
    char command[1024];
    (void)sprintf_s(command, sizeof(command), "gmsysview -q V\\$STORAGE_RSM_BLOCK_STAT");
    if (isRsmExtendBlock) {
        ret = executeCommand(
            command, "RSM_BLOCK_STATUS: BLOCK_USED", "IS_RSM_EXTEND_BLOCK: 1", "RSM_BLOCK_STATUS: BLOCK_NOT_USED");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = executeCommand(
            command, "RSM_BLOCK_STATUS: BLOCK_USED", "IS_RSM_EXTEND_BLOCK: 0", "RSM_BLOCK_STATUS: BLOCK_NOT_USED");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

int CfeInjectFaults(char *cfeCmd, ...)
{
    int ret = GtExecSystemCmd("%s \"%s\"", g_cfeExePath, cfeCmd);
    return ret;
}

typedef struct Fld27Stru {
    // DBT_BCD 4
    uint16_t v0;
    // DBT_FLOAT 4
    float v1;
    // DBT_DOUBLE 8
    double v2;
    // DBT_BIT 4
    char v3[4];
    // DBT_NUMERIC 8
    double v4;
    // DBT_BYTES 8 + 2
    char v5[10];
    // DBT_TIME
    char v6[3];
    // DBT_STRING 10 + 1
    char v7[11];
    // DBT_BLOCK 1
    char v8[1];
    // DBT_UINT8
    uint8_t v9;
    // DBT_UINT16
    uint16_t v10;
    // DBT_UINT32
    uint32_t v11;
    // DBT_SINT8
    int8_t v12;
    // DBT_SINT16
    int16_t v13;
    // DBT_SINT32
    int32_t v14;
    // DBT_DATE
    char v15[4];
    // DBT_IP_ADDRESS
    uint32_t v16;
    // DBT_MAC_ADDRESS
    char v17[6];
    // DBT_INT64
    int64_t v18;
    // DBT_UINT64
    uint64_t v19;
    // DBT_IPV4PREFIX
    char v20[5];
    // DBT_IPV6
    char v21[16];
    // DBT_IPV6PREFIX
    char v22[17];
    // DBT_DATETIME
    char v23[7];
    // DBT_TIMEZONE
    char v24[3];
    // DBT_MIBSTR 10 + 1
    char v25[11];
    // DBT_VBYTES 10 + 2
    char v26[12];
} Fld27StruT;

void TestConstructAllTypeDataBuff(Fld27StruT *fldStru, uint32_t ulLoop, uint32_t addVal)
{
    char bytesVal1[10];
    memset_s(bytesVal1, 10, 0x00, 10);
    *(uint16_t *)bytesVal1 = 8;
    (void)sprintf_s(bytesVal1 + 2, 8, "08re%u", ulLoop);

    char bytesVal2[12];
    memset_s(bytesVal2, 12, 0x00, 12);
    *(uint16_t *)bytesVal2 = 10;
    (void)sprintf_s(bytesVal2 + 2, 10, "08re%u", ulLoop);

    fldStru->v0 = (uint16_t)((ulLoop + addVal) % 65535);
    fldStru->v1 = (float)(ulLoop + addVal + 1);
    fldStru->v2 = (double)(ulLoop + addVal + 2);
    (void)sprintf(fldStru->v3, "r%03d", ulLoop + addVal);
    fldStru->v4 = (double)(ulLoop + addVal + 4);
    memcpy(fldStru->v5, bytesVal1, 10);
    (void)sprintf(fldStru->v6, "r%02d", ulLoop + addVal);
    (void)sprintf(fldStru->v7, "r%09d", ulLoop + addVal);
    (void)sprintf(fldStru->v8, "%d", ulLoop + addVal);
    fldStru->v9 = (uint8_t)(ulLoop + addVal + 9);
    fldStru->v10 = ulLoop + addVal + 10;
    fldStru->v11 = ulLoop + addVal + 11;
    fldStru->v12 = (int8_t)(ulLoop + addVal + 12);
    fldStru->v13 = ulLoop + addVal + 13;
    fldStru->v14 = ulLoop + addVal + 14;
    (void)sprintf(fldStru->v15, "r%03d", ulLoop + addVal);
    fldStru->v16 = ulLoop + addVal + 16;
    (void)sprintf(fldStru->v17, "r%05d", ulLoop + addVal);
    fldStru->v18 = ulLoop + addVal + 18;
    fldStru->v19 = ulLoop + addVal + 19;
    (void)sprintf(fldStru->v20, "r%04d", ulLoop + addVal);
    (void)sprintf(fldStru->v21, "r%015d", ulLoop + addVal);
    (void)sprintf(fldStru->v22, "r%016d", ulLoop + addVal);
    (void)sprintf(fldStru->v23, "r%06d", ulLoop + addVal);
    (void)sprintf(fldStru->v24, "r%02d", ulLoop + addVal);
    (void)sprintf(fldStru->v25, "r%09d", ulLoop + addVal);
    memcpy(fldStru->v26, bytesVal2, 12);
}

void TestGetTable2PropertyAndCheck(GmcStmtT *stmt, uint32_t ulLoop, uint32_t addVal)
{
    Fld27StruT fldStruExp;
    TestConstructAllTypeDataBuff(&fldStruExp, ulLoop, addVal);

    Fld27StruT fldStru;
    int ret = 0;
    bool isNull = true;

    ret = GmcGetVertexPropertyByName(stmt, "F1", &fldStru.v1, sizeof(float), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v1, fldStru.v1);

    ret = GmcGetVertexPropertyByName(stmt, "F2", &fldStru.v2, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v2, fldStru.v2);

    ret = GmcGetVertexPropertyByName(stmt, "F3", fldStru.v3, 4, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v3, fldStru.v3, 4), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F4", &fldStru.v4, sizeof(double), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v4, fldStru.v4);

    ret = GmcGetVertexPropertyByName(stmt, "F5", fldStru.v5, 10, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v5, fldStru.v5, 10), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F6", fldStru.v6, 3, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v6, fldStru.v6, 3), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F7", fldStru.v7, 11, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v7, fldStru.v7, 11), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F8", fldStru.v8, 1, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v8, fldStru.v8, 1), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F9", &fldStru.v9, sizeof(uint8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v9, fldStru.v9);

    ret = GmcGetVertexPropertyByName(stmt, "F10", &fldStru.v10, sizeof(uint16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v10, fldStru.v10);

    ret = GmcGetVertexPropertyByName(stmt, "F11", &fldStru.v11, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v11, fldStru.v11);

    ret = GmcGetVertexPropertyByName(stmt, "F12", &fldStru.v12, sizeof(int8_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v12, fldStru.v12);

    ret = GmcGetVertexPropertyByName(stmt, "F13", &fldStru.v13, sizeof(int16_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v13, fldStru.v13);

    ret = GmcGetVertexPropertyByName(stmt, "F14", &fldStru.v14, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v14, fldStru.v14);

    ret = GmcGetVertexPropertyByName(stmt, "F15", fldStru.v15, 4, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v15, fldStru.v15, 4), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F16", &fldStru.v16, sizeof(uint32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v16, fldStru.v16);

    ret = GmcGetVertexPropertyByName(stmt, "F17", fldStru.v17, 6, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v17, fldStru.v17, 6), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F18", &fldStru.v18, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v18, fldStru.v18);

    ret = GmcGetVertexPropertyByName(stmt, "F19", &fldStru.v19, sizeof(uint64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(fldStruExp.v19, fldStru.v19);

    ret = GmcGetVertexPropertyByName(stmt, "F20", fldStru.v20, 5, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v20, fldStru.v20, 5), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F21", fldStru.v21, 16, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v21, fldStru.v21, 16), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F22", fldStru.v22, 17, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v22, fldStru.v22, 17), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F23", fldStru.v23, 7, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v23, fldStru.v23, 7), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F24", fldStru.v24, 3, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v24, fldStru.v24, 3), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F25", fldStru.v25, 11, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v25, fldStru.v25, 11), 0);

    ret = GmcGetVertexPropertyByName(stmt, "F26", fldStru.v26, 12, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    AW_MACRO_EXPECT_EQ_INT(strncmp(fldStruExp.v26, fldStru.v26, 12), 0);
}

void TestScanTable2ByPk(
    GmcStmtT *stmt, const char *keyName, uint32_t begin, uint32_t end, uint32_t addVal, const char *labelName)
{
    int ret = 0;
    uint32_t keyvalue;

    int cnt = begin;
    for (int i = begin; i < end; i++) {
        // scan vertex
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        keyvalue = (uint16_t)(i % 65535);
        // size 是根据v1 DBT_BCD的size计算出来，设置的是4字节，实际存储字节为4 + 1 / 2
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, &keyvalue, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;

        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            if (GMERR_OK != ret) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = testGmcGetLastError();
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            if (isFinish) {
                break;
            }

            TestGetTable2PropertyAndCheck(stmt, cnt, addVal);
            cnt++;
        }
    }

    AW_MACRO_EXPECT_EQ_INT(end - begin, cnt - begin);
    AW_FUN_Log(LOG_INFO, "Index scan[%s] is complete, count is %d.", keyName, cnt - begin);
}

void TestGmcSetVertexPropertyPk(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexProperty(GmcStmtT *stmt, uint32_t i, uint32_t addVal, bool isSetNodeV1 = false)
{
    int ret = 0;
    uint32_t value = i + addVal;

    uint32_t f1Value = value;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f2Value = value;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f3Value = value;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f5Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT32, &f5Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f6Value = 1;
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT32, &f6Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f7Value = ((i + 2) << 8);
    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f8Value = ((24) & 0xff);
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_UINT8, &f8Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexPropertyP0(GmcStmtT *stmt)
{
    int ret = 0;

    // 写string数据
    uint32_t superSize = 400;
    char *superValue = (char *)malloc(superSize);
    if (superValue == NULL) {
        AW_FUN_Log(LOG_INFO, "Malloc superValue unsucc.");
        return;
    }
    memset(superValue, 'B', superSize);

    ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_FIXED, superValue, superSize);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(superValue);
}

// Insert
int TestInsertVertexLabel(GmcStmtT *stmt, uint32_t index, uint32_t addVal, const char *labelName, int32_t version = 0)
{
    int ret = 0;
    uint32_t i = index;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, version, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "ret : %d, index : %d.", ret, i);
        return ret;
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set pk
    TestGmcSetVertexPropertyPk(stmt, i);
    // set Property
    TestGmcSetVertexProperty(stmt, i, addVal);
    TestGmcSetVertexPropertyP0(stmt);

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "ret : %d, index : %d.", ret, i);
        return ret;
    };
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int TestGetViewCount(const char *viewName, const char *grepStr)
{
    int ret = 0;
    int count = 0;
    char cmd[1024] = {0};

    (void)sprintf_s(cmd, sizeof(cmd), "gmsysview -q %s | grep %s | wc -l", viewName, grepStr);
    AW_FUN_Log(LOG_INFO, "cmd is %s.", cmd);
    ret = TestGetResultCommand(cmd, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return count;
}

// 根据进程名字，获取进程id
int GetProcessIdByName(const char *proName)
{
    int pid = 0;
    char cmd[1024] = {0};
#if defined(ENV_RTOSV2X) && defined(RUN_DATACOM_DAP)
    (void)snprintf(cmd, 1024, "ps -l | grep %s | grep -v defunct | grep -v grep | awk '{print $3}'", proName);
#else
    (void)snprintf(cmd, 1024, "ps -ef | grep %s | grep -v defunct | grep -v grep | awk '{print $2}'", proName);
#endif
    (void)TestGetResultCommand(cmd, &pid);
    return pid;
}

#endif
