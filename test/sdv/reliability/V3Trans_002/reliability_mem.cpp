extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
//#include "DefaultSize.h"
#include <time.h>
#include <sys/time.h>
#include "t_datacom_lite.h"
#define MAX_NAME_LENGTH 128
#define MAX_NUM 1
#define LOOP 1
#define CONN_MAX_NUM 1024

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcTxConfigT MSTrxConfig;
int ret = 0;
class reliability002_mem : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"lpm6VrIdMax=30\"");  // lpm6VrfIdMax
        // system("sh $TEST_HOME/tools/modifyCfg.sh \"lpm6VrfIdMax=1024\"");
        // system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=1024\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,5\"");
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void reliability002_mem::SetUp()
{
    usleep(1000);
    MSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    MSTrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    MSTrxConfig.readOnly = false;
    int ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

void reliability002_mem::TearDown()
{
    usleep(1000);
    testGmcDisconnect(g_conn, g_stmt);
}

char const *g_schema = "[{\"type\":\"record\", \"name\":\"Normal\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
                       "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", "
                       "\"type\":\"uint32\"},{\"name\":\"F3\", \"type\":\"uint32\"}],"
                       "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
                       "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
char const *g_Tree_schema =
    "[{\"type\":\"record\", \"name\":\"NormalTree\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
    "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
    "\"type\":\"record\",\"vector\":true,\"size\":3,\"fields\":[{\"name\":\"A1\", "
    "\"type\":\"uint32\"},{\"name\":\"A2\", \"type\":\"uint32\"}]}],"
    "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
char Normal_schema_names[1023];
char Normal_schemas[1023];
const char *V_config = "{\"max_record_num\":1000000000,\"defragmentation\":true}";
const char *MAX_config = "{\"max_record_num\":18446744073709551615,\"defragmentation\":true}";
uint32_t insert_num = 520;
uint32_t del_num = insert_num * 0.5;
uint32_t up_num = insert_num * 0.7;
uint32_t up_value = 0;
bool isNull;
char *Normal_schema = NULL;
char *Normal_schema_names1 = NULL;
pthread_mutex_t mut_tcnt;
GmcConnT *g_conn1[CONN_MAX_NUM];
GmcStmtT *g_stmt1[CONN_MAX_NUM];
GmcConnT *g_conn2[1024];
GmcStmtT *g_stmt2[1024];
int cnt = 0;
void *create_max_table_thread_func(void *args)
{
    GmcConnT *g_conn2[1024];
    GmcStmtT *g_stmt2[1024];
    char Normal_schemas[1023];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    int ret = 0;
    snprintf(Normal_schemas, 1023,
        "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint64\"},"
        "{\"name\":\"F1\", \"type\":\"uint64\"},{\"name\":\"F2\", \"type\":\"uint64\"},{\"name\":\"F3\", "
        "\"type\":\"uint64\"},{\"name\":\"F4\", \"type\":\"string\"}],"
        "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        num);
    cnt++;
    ret = GmcCreateVertexLabel(g_stmt2[num], Normal_schemas, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        printf("%d\n", num);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *insert_max_table_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    // printf("write func:%s\n",Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" write func failed:%s\n", Normal_thread_names);
    } else {
        printf(" write func success:%s\n", Normal_thread_names);
    }
    bool open = true;
    char bigstr[6144];
    memset(bigstr, 'e', sizeof(bigstr) - 1);
    while (open) {
        for (uint64_t j = 0; j < 8446744073709551615; j++)  //未到uint64上限
        {
            ret = GmcSetVertexProperty(g_stmt2[num], "F0", GMC_DATATYPE_UINT64, &j, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "F1", GMC_DATATYPE_UINT64, &j, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "F2", GMC_DATATYPE_UINT64, &j, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "F3", GMC_DATATYPE_UINT64, &j, sizeof(uint64_t));
            EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(g_stmt2[num],"F4",GMC_DATATYPE_STRING,&bigstr,strlen(bigstr));
            // EXPECT_EQ(GMERR_OK,ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcExecute(g_stmt2[num]);
            // EXPECT_EQ(GMERR_OK,ret);
            if (ret != 0 && ret != GMERR_OUT_OF_MEMORY) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

            } else if (ret == GMERR_OUT_OF_MEMORY) {
                open = false;
                printf("Out of memory");
                break;
            } else {
                EXPECT_EQ(GMERR_OK, ret);
            }
            printf("%d,", j);
        }
    }
    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\nthread %d insert num is :%d\n", num, result);
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *update_max_table_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_UPDATE);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" update func failed:%s\n", Normal_thread_names);
    } else {
        printf(" update func success:%s\n", Normal_thread_names);
    }
    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("result is:%d\n", result);
    for (uint64_t j = 0; j < result; j++) {
        uint64_t up_value = 3000000000 + j;
        ret = GmcSetIndexKeyName(g_stmt2[num], "Normal_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt2[num], 0, GMC_DATATYPE_UINT64, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt2[num], "F0", GMC_DATATYPE_UINT64, &up_value, sizeof(up_value));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt2[num]);
        if (ret == 0) {
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret != 0) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
    }

    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *read_max_table_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);

    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("result is:%d\n", result);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_SCAN);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" read func failed:%s\n", Normal_thread_names);
    } else {
        printf(" read func success:%s\n", Normal_thread_names);
    }
    ret = GmcSetIndexKeyName(g_stmt2[num], NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prop_size = 0;
    uint64_t prop_value = 0;
    uint64_t cnt = 0;
    bool isEof = false;
    while (!isEof) {
        if (isEof == true) {
            break;
        }
        cnt++;
        ret = GmcFetch(g_stmt2[num], &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt2[num], "F0", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt2[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        if (cnt <= result) {
            EXPECT_EQ(prop_value, cnt - 1);
        }
    }
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *drop_max_table_thread_func(void *args)
{
    GmcConnT *g_conn2[1024];
    GmcStmtT *g_stmt2[1024];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    char Normal_schema_names[1023];
    bool isEof;
    bool isNull;
    int ret = 0;
    snprintf(Normal_schema_names, 15, "Normal%d", num);
    ret = GmcDropVertexLabel(g_stmt2[num], Normal_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *connect_thread_func(void *args)
{
    int index = *((int *)args);
    ret = testGmcConnect(&g_conn1[index], &g_stmt1[index]);
    EXPECT_EQ(GMERR_OK, ret);
    testGmcDisconnect(g_conn1[index], g_stmt1[index]);
    return NULL;
}
void *DML_multi_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 100; j++) {
            ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < 50; j++) {
            uint32_t up_value = j + 201;
            ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &up_value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 50; j < 100; j++) {
            ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        bool isEof = false;
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t prop_size = 0;
        uint32_t prop_value = 0;
        while (!isEof) {
            if (isEof == true) {
                break;
            }
            ret = GmcFetch(g_stmt1[num], &isEof);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F0", &prop_size);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt1[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
            EXPECT_EQ(GMERR_OK, ret);
            printf("table %d:the F0 value is:%d  ", i, prop_value);
        }
        printf("\n");
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *create_table_thread_func(void *args)
{
    // GmcConnT *conn1[MAX_NUM];
    // GmcStmtT *stmt1[MAX_NUM];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    int ret = 0;
    // int table_name=i//0~9
    snprintf(Normal_schemas, 1023,
        "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
        "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
        "\"type\":\"uint32\"}],"
        "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        num);
    ret = GmcCreateVertexLabel(g_stmt1[num], Normal_schemas, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *write_table_thread_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("write func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" write func success:%s\n", Normal_thread_names);
    } else {
        printf(" write func success:%s\n", Normal_thread_names);
    }
    // assert(GMERR_OK==ret);
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *delete_table_thread_func(void *args)
{
    int num = *((int *)args);
    // int num=0;
    int value = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("delete func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_DELETE);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" delete func success:%s\n", Normal_thread_names);
    }
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        // j=j+5;
        ret = GmcSetIndexKeyName(g_stmt1[num], "K1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *read_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("read func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prop_size = 0;
    uint32_t prop_value = 0;
    while (!isEof) {
        if (isEof == true) {
            break;
        }
        ret = GmcFetch(g_stmt1[num], &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F0", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *insert_muti_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < num; i++) {
        snprintf(Normal_schema_names, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < insert_num; j++) {
            ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *read_muti_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < num; i++) {
        bool isEof = false;
        snprintf(Normal_schema_names, 15, "Normal%d", i);
        ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t prop_size = 0;
        uint32_t prop_value = 0;
        while (!isEof) {
            if (isEof == true) {
                break;
            }
            ret = GmcFetch(g_stmt1[num], &isEof);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F0", &prop_size);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(g_stmt1[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *update_muti_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int j = 0; j < num; j++) {
        snprintf(Normal_schema_names, 15, "Normal%d", j);
        for (int i = 0; i < up_num; i++) {
            up_value = i + 600;
            ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_UPDATE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *delete_muti_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int j = 0; j < num; j++) {
        snprintf(Normal_schema_names, 15, "Normal%d", j);
        for (int i = 0; i < del_num; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_schema_names, GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt1[num]);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *create_muti_table_thread_func(void *args)
{
    GmcConnT *conn1[MAX_NUM];
    GmcStmtT *stmt1[MAX_NUM];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    int ret = 0;
    for (int i = 0; i < 1024; i++) {
        // int table_name=i//0~9
        snprintf(Normal_schemas, 1023,
            "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
            "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
            "\"type\":\"uint32\"}],"
            "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i);
        ret = GmcCreateVertexLabel(g_stmt1[num], Normal_schemas, V_config);
        EXPECT_EQ(GMERR_OK, ret);
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *drop_muti_table_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 1024; i++) {
        snprintf(Normal_schema_names, 15, "Normal%d", i);
        ret = GmcDropVertexLabel(g_stmt1[num], Normal_schema_names);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *create_single_table_thread_func(void *args)
{
    const char *single_schema;
    int ret = 0;
    pthread_mutex_lock(&mut_tcnt);
    single_schema =
        "[{\"type\":\"record\", \"name\":\"Normal_single\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
        "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
        "\"type\":\"uint32\"}],"
        "\"keys\":[{\"node\":\"Normal\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]";
    ret = GmcCreateVertexLabel(g_stmt1[0], single_schema, V_config);
    if (ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_OK) {
        EXPECT_EQ(GMERR_OK, 0);
    }
    // EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    pthread_mutex_unlock(&mut_tcnt);
    return NULL;
}
void *drop_single_table_thread_func(void *args)
{
    ret = GmcDropVertexLabel(g_stmt1[0], "Normal_single");
    if (ret == GMERR_INVALID_PARAMETER_VALUE || ret == GMERR_OK) {
        EXPECT_EQ(GMERR_OK, 0);
    }
    return NULL;
}
void *truncate_table_thread_func(void *args)
{
    int num = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    // printf("write func:%s\n",Normal_thread_names);
    ret = GmcTruncateVertexLabel(g_stmt1[num], Normal_thread_names);
    EXPECT_EQ(GMERR_OK, ret);
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *truncate_same_thread_func(void *args)
{
    int num = *((int *)args);
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt1[num], "Normal");
    EXPECT_EQ(GMERR_OK, ret);
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *GetVertexCount_thread_func(void *args)
{
    int num = *((int *)args);
    int value = *((int *)args);
    uint64_t result;
    GmcConnT *g_conn1[CONN_MAX_NUM];  //注意写在函数中，不要写全局变量
    GmcStmtT *g_stmt1[CONN_MAX_NUM];
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("GetCount func:%s\n", Normal_thread_names);
    GmcGetVertexCount(g_stmt1[num], Normal_thread_names, NULL, &result);
    printf("the count is:%d", result);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(result, 100);

    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *write_same_table_func(void *args)
{
    int num = 0;
    int value = *((int *)args);
    GmcConnT *g_conn1[CONN_MAX_NUM];  //注意写在函数中，不要写全局变量
    GmcStmtT *g_stmt1[CONN_MAX_NUM];
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], "Normal", GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint32_t f1_value = 9;
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &f1_value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}

void *delete_same_table_thread_func(void *args)
{
    int num = 0;
    int value = *((int *)args);
    GmcConnT *g_conn1[CONN_MAX_NUM];  //注意写在函数中，不要写全局变量
    GmcStmtT *g_stmt1[CONN_MAX_NUM];
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], "Normal", GMC_OPERATION_DELETE);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *update_same_table_thread_func(void *args)
{
    int num = 0, pk_index = 1;
    int value = *((int *)args);
    GmcConnT *g_conn1[CONN_MAX_NUM];  //注意写在函数中，不要写全局变量
    GmcStmtT *g_stmt1[CONN_MAX_NUM];
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t pk_value[1000], pk_upvalue[1000];
    pk_value[0] = 100;
    for (int i = 1; i < 1000; i++) {
        pk_value[i] = 100 + i * 100;
        pk_upvalue[i] = 10000 + i * 100;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], "Normal", GMC_OPERATION_UPDATE);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (uint32_t j = pk_value[value]; j < pk_value[value + 1]; j++) {
        ret = GmcSetIndexKeyName(g_stmt1[num], "Normal_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt1[num], 0, GMC_DATATYPE_UINT32, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        up_value = pk_upvalue[pk_index];
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
        EXPECT_EQ(GMERR_OK, ret);
        pk_index++;
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *read_same_table_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    // for(int i=0;i<num;i++)
    // {
    bool isEof = false;
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], "Normal", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prop_size = 0;
    uint32_t prop_value = 0;
    while (!isEof) {
        if (isEof == true) {
            break;
        }
        ret = GmcFetch(g_stmt1[num], &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F1", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F1", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(prop_value, 9);
    }
    // }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}
void *write_test_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn1[CONN_MAX_NUM];
    GmcStmtT *g_stmt1[CONN_MAX_NUM];
    int ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("write func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" write func success:%s\n", Normal_thread_names);
    } else {
        printf(" write func success:%s\n", Normal_thread_names);
    }
    // assert(GMERR_OK==ret);
    for (uint32_t j = 0; j < insert_num; j++) {
        // j=j+5;
        ret = GmcSetVertexProperty(g_stmt1[num], "F0", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F2", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt1[num], "F3", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt1[num]);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}

void *read_table_all_thread_func(void *args)
{
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn1[num], &g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "Normal%d", num);
    printf("write func:%s\n", Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt1[num], Normal_thread_names, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt1[num], NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt1[num]);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t prop_size = 0;
    uint32_t prop_value = 0;
    while (!isEof) {
        if (isEof == true) {
            break;
        }
        ret = GmcFetch(g_stmt1[num], &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F0", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F0", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F1", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F1", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F2", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F2", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertySizeByName(g_stmt1[num], "F3", &prop_size);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt1[num], "F3", &prop_value, prop_size, &isNull);  // isNull传出
        EXPECT_EQ(GMERR_OK, ret);
    }
    testGmcDisconnect(g_conn1[num], g_stmt1[num]);
    return NULL;
}

void *create_max_table_lpm_thread_func(void *args)
{
    GmcConnT *g_conn2[1024];
    GmcStmtT *g_stmt2[1024];
    char Normal_schemas[1023];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    bool isEof;
    bool isNull;
    int ret = 0;
    snprintf(Normal_schemas, 1023,
        "[{\"type\":\"record\", \"name\":\"lpm6_%d\", \"fields\":[{\"name\":\"vr_id\", \"type\":\"uint32\"},"
        "{\"name\":\"vrf_index\", \"type\":\"uint32\"},{\"name\":\"dest_ip_addr\", "
        "\"type\":\"fixed\",\"size\":16},{\"name\":\"mask_len\", \"type\":\"uint8\"},"
        "{\"name\":\"primary_label\", \"type\":\"uint64\"},{\"name\":\"heavy\", \"type\":\"string\"}],"
        "\"keys\":[{\"node\":\"lpm6\", \"name\":\"prikey\", \"fields\":[\"primary_label\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"lpm6\", \"name\":\"lpm6key\", \"fields\":[\"vr_id\",\"vrf_index\",\"dest_ip_addr\",\"mask_len\"], "
        "\"index\":{\"type\":\"lpm6_tree_bitmap\"},\"constraints\":{ \"unique\":true}}]}]",
        num);
    cnt++;
    ret = GmcCreateVertexLabel(g_stmt2[num], Normal_schemas, V_config);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        printf("%d\n", num);
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *insert_max_table_lpm_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "lpm6_%d", num);
    // printf("write func:%s\n",Normal_thread_names);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_INSERT);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" write func failed:%s\n", Normal_thread_names);
    } else {
        printf(" write func success:%s\n", Normal_thread_names);
    }
    bool open = true;
    uint64_t cnt = 0;
    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    char bigstr[6144];
    memset(bigstr, 'e', sizeof(bigstr) - 1);
    bigstr[6144 - 1] = '\0';
    // while(open)
    // {
    for (uint32_t j = 0; j < 30; j++)  // 4096*1024挂了
    {
        uint32_t value_vr = j % 30;
        for (uint32_t k = 0; k < 1024; k++)  // 128byte*4096+8byte*16384
        {
            uint32_t value_vrf = k % 1024;
            uint8_t value_mask = k % 128;
            cnt++;
            ret = GmcSetVertexProperty(g_stmt2[num], "primary_label", GMC_DATATYPE_UINT64, &cnt, sizeof(cnt));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "vr_id", GMC_DATATYPE_UINT32, &value_vr, sizeof(value_vr));
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcSetVertexProperty(g_stmt2[num], "vrf_index", GMC_DATATYPE_UINT32, &value_vrf, sizeof(value_vrf));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "dest_ip_addr", GMC_DATATYPE_FIXED, wr_fixed, 16);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(g_stmt2[num], "mask_len", GMC_DATATYPE_UINT8, &value_mask, sizeof(value_mask));
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcSetVertexProperty(g_stmt2[num], "heavy", GMC_DATATYPE_STRING, &bigstr, strlen(bigstr));
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            ret = GmcExecute(g_stmt2[num]);
            // EXPECT_EQ(GMERR_OK,ret);
            if (ret != 0 && ret != GMERR_OUT_OF_MEMORY) {
                printf("the vr_id is %lld,the vrf_id is %lld,the cnt is :%lld\n", j, k, cnt);
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);

            } else if (ret == GMERR_OUT_OF_MEMORY) {
                open = false;
                printf("out of memory\n");
                break;
            } else {
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // printf("%d,",j);
    }
    // }
    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("\nthread %d insert num is :%d\n", num, result);
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *update_max_table_lpm_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "lpm6_%d", num);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_UPDATE);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" update func failed:%s\n", Normal_thread_names);
    } else {
        printf(" update func success:%s\n", Normal_thread_names);
    }
    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("result is:%d\n", result);
    for (uint64_t j = 0; j < 1024 * 30; j++) {
        uint64_t up_value = 300000 + j;
        ret = GmcSetIndexKeyName(g_stmt2[num], "prikey");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt2[num], 0, GMC_DATATYPE_UINT64, &j, sizeof(j));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt2[num], "primary_label", GMC_DATATYPE_UINT64, &up_value, sizeof(up_value));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != 0) {
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt2[num]);
        if (ret == 0) {
            EXPECT_EQ(GMERR_OK, ret);
        } else if (ret == GMERR_OUT_OF_MEMORY) {
            EXPECT_EQ(GMERR_OK, 0);
            printf("j:%ld,", j);
        }
    }

    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *read_max_table_lpm_thread_func(void *args)
{
    int num = *((int *)args);
    GmcConnT *g_conn2[CONN_MAX_NUM];
    GmcStmtT *g_stmt2[CONN_MAX_NUM];
    uint64_t result;
    int ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char Normal_thread_names[1023];
    snprintf(Normal_thread_names, 15, "lpm6_%d", num);

    ret = GmcGetVertexCount(g_stmt2[num], Normal_thread_names, NULL, &result);
    EXPECT_EQ(GMERR_OK, ret);
    printf("result is:%d\n", result);
    ret = testGmcPrepareStmtByLabelName(g_stmt2[num], Normal_thread_names, GMC_OPERATION_SCAN);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
        printf(" read func failed:%s\n", Normal_thread_names);
    } else {
        printf(" read func success:%s\n", Normal_thread_names);
    }
    uint64_t cnt_out = 0;
    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    for (uint32_t j = 0; j < 30; j++)  // 4096*1024挂了
    {
        uint32_t value_vr = j % 30;
        for (uint32_t k = 0; k < 1024; k++)  // 128byte*4096+8byte*16384
        {
            uint32_t value_vrf = k % 1024;
            uint8_t value_mask = k % 128;
            cnt_out++;
            ret = GmcSetIndexKeyValue(g_stmt2[num], 0, GMC_DATATYPE_UINT32, &value_vr, sizeof(value_vr));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt2[num], 1, GMC_DATATYPE_UINT32, &value_vrf, sizeof(value_vrf));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt2[num], 2, GMC_DATATYPE_FIXED, wr_fixed, 16);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt2[num], 3, GMC_DATATYPE_UINT8, &value_mask, sizeof(value_mask));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt2[num], "lpm6key");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(g_stmt2[num]);
            EXPECT_EQ(GMERR_OK, ret);
            if (ret != 0) {
                ret = testGmcGetLastError(NULL);
                EXPECT_EQ(GMERR_OK, ret);
            }
            uint32_t prop_size = 0;
            uint64_t prop_value = 0;
            uint64_t cnt = 0;
            bool isEof = false;
            while (!isEof) {
                if (isEof == true) {
                    break;
                }
                cnt++;
                ret = GmcFetch(g_stmt2[num], &isEof);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertySizeByName(g_stmt2[num], "mask_len", &prop_size);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(
                    g_stmt2[num], "mask_len", &prop_value, prop_size, &isNull);  // isNull传出
                EXPECT_EQ(GMERR_OK, ret);
                // printf("%d\n",prop_value);
            }
        }
    }
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
void *drop_max_table_lpm_thread_func(void *args)
{
    GmcConnT *g_conn2[1024];
    GmcStmtT *g_stmt2[1024];
    int num = *((int *)args);
    ret = testGmcConnect(&g_conn2[num], &g_stmt2[num]);
    EXPECT_EQ(GMERR_OK, ret);
    char *Normal_schema = NULL;
    char Normal_schema_names[1023];
    bool isEof;
    bool isNull;
    int ret = 0;
    snprintf(Normal_schema_names, 15, "lpm6_%d", num);
    ret = GmcDropVertexLabel(g_stmt2[num], Normal_schema_names);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        ret = testGmcGetLastError(NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    testGmcDisconnect(g_conn2[num], g_stmt2[num]);
    return NULL;
}
/*******************************************************************************
  函 数 名		:  max_record_wrup_thread_001
  功能描述		:  DB内存1G，多线程并发插入满数据，更新数据
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(reliability002_mem, max_record_wrup_thread_001)
{
    pthread_t create_max_table[1024];
    pthread_t drop_max_table[1024];
    pthread_t insert_max_table[1024];
    pthread_t delete_muti_table[MAX_NUM];
    pthread_t update_max_table[1024];
    pthread_t read_muti_table[MAX_NUM];
    int i, index[1099] = {0};

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&create_max_table[i], NULL, create_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
        usleep(1000000);
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(create_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&insert_max_table[i], NULL, insert_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(insert_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&update_max_table[i], NULL, update_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(update_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    EXPECT_EQ(cnt, 1);
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&drop_max_table[i], NULL, drop_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(drop_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
}

/*******************************************************************************
  函 数 名		:  max_record_wrre_thread_002
  功能描述		:  DB内存1G，多线程并发插入满数据，读数据
  输入参数		:  (写满读数据主要是动态内存？)
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(reliability002_mem, max_record_wrre_thread_002)
{
    pthread_t create_max_table[1024];
    pthread_t drop_max_table[1024];
    pthread_t insert_max_table[1024];
    pthread_t delete_muti_table[MAX_NUM];
    pthread_t update_max_table[1024];
    pthread_t read_max_table[1024];
    int i, index[1099] = {0};

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&create_max_table[i], NULL, create_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
        usleep(4000000);
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(create_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&insert_max_table[i], NULL, insert_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(insert_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&read_max_table[i], NULL, read_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(read_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    EXPECT_EQ(cnt, 1024);
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&drop_max_table[i], NULL, drop_max_table_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(drop_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
}
/*******************************************************************************
  函 数 名		:  max_record_wrlpmread_thread_003
  功能描述		:  DB内存1G，lpm表多线程并发插入满数据，使用Lpm读，更新数据
  输入参数		:
  输出参数		:  None
  返 回 值		:  None
*******************************************************************************/
TEST_F(reliability002_mem, max_record_wrlpmread_thread_003)
{

    pthread_t create_max_table[1024];
    pthread_t drop_max_table[1024];
    pthread_t insert_max_table[1024];
    pthread_t delete_muti_table[MAX_NUM];
    pthread_t update_max_table[1024];
    pthread_t read_max_table[1024];
    int i, index[1099] = {0};
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&create_max_table[i], NULL, create_max_table_lpm_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
        usleep(1000000);
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(create_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&insert_max_table[i], NULL, insert_max_table_lpm_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(insert_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&read_max_table[i], NULL, read_max_table_lpm_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(read_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&update_max_table[i], NULL, update_max_table_lpm_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(update_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }

    EXPECT_EQ(cnt, 1);
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            index[i] = i;
            ret = pthread_create(&drop_max_table[i], NULL, drop_max_table_lpm_thread_func, (void *)&index[i]);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
    for (int j = 0; j < LOOP; j++) {
        for (i = j * MAX_NUM; i < (j + 1) * MAX_NUM; i++) {
            ret = pthread_join(drop_max_table[i], NULL);
            EXPECT_EQ(ret, GMERR_OK);
        }
    }
}
