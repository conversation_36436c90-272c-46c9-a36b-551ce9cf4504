/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDBV503 datalog规则触发动态内存不足
 Notes        :001.join规则触发动态内存不足
               002.笛卡尔积规则触发动态内存不足
               003.not join规则触发动态内存不足
               004.function规则触发动态内存不足
               005.聚合规则触发动态内存不足
               006.union_delete触发动态内存不足
               007.pubsub触发动态内存不足
               008.null(0)规则触发动态内存不足
               009.表记录过期触发动态内存不足
               010.precedence规则触发动态内存不足
 History      :
 Author       : youwanyong/ywx1157510
 Create       : [2023.08.07]
*****************************************************************************/
#include <sys/ipc.h>
#include <sys/shm.h>
#include "yreliability.h"
#include "PessimisticSerializable.h"
// 订阅对于后续count位一正一负的数据需要将count合并为0两条消息会合并为一条进行推送
// function 一条tuple一次
// agg 一组数据一次
// tbm 每个tuple一次
// msgtbm， 每个msgsize一次
// timeout 一条tuple一次

using namespace std;

class reliability01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void reliability01::SetUp()
{
    AW_CHECK_LOG_BEGIN(0);
    system("rm -rf *client.txt");
}
void reliability01::TearDown()
{
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  : 001.join规则触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_001";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[10000][7] = {{1, 1, 1, 2, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 100; j++) {
        for (int i = 1; i < 100; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            k++;
        }
    }

    // 单次写入数据的数量
    int32_t sizeNum = 615;
    // 写入数据的总条数
    int32_t endNum = sizeNum * 2;
    char viewFilterValue[40] = "CUR_ITEM_NUM: sizeNum";
    (void)snprintf(viewFilterValue, 40, "CUR_ITEM_NUM: %d", sizeNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    for (int32_t startVal = 0; startVal < endNum; startVal += sizeNum) {
        AW_FUN_Log(LOG_STEP, "startVal %d", startVal);
        ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, startVal + sizeNum, 0, false, startVal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    sleep(10);
    int32_t cycle = 10;
    while (cycle > 0) {
        for (int32_t startVal = 0; startVal < endNum; startVal += sizeNum) {
            AW_FUN_Log(LOG_STEP, "startVal %d", startVal);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, startVal + sizeNum, 0, false, startVal);
        }
        cycle--;
    }
    sleep(10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_DYN_MEM: [2] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 当前第一组数据能够正常写入,第二组sizeNum条数据数据写入失败,内存满
    ret = ReadTableABC(g_stmt, tableA1, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(sizeNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(sizeNum, ret);

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);
    // b.多个两表join直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    char tableE2[20] = "Ejoin02";
    ret = BatchInsertByte256(g_conn, g_stmt, tableB2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableC2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(5, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableE2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // c.多个表同时join直到内存满
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";
    char tableE3[20] = "Ejoin03";

    ret = BatchInsertByte256(g_conn, g_stmt, tableB3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableC3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(8, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = ReadTableABC(g_stmt, tableA3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableE3, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               002.笛卡尔积规则触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_002)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_002";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表笛卡尔积，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 2, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            k++;
        }
    }
    int32_t sizeNum = 600;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 600");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    int32_t cycle = 10;
    while (cycle > 0) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, sizeNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        cycle--;
    }
    sleep(10);
    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = executeCommand(g_command, "GLOBAL_DYN_MEM: [2] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableA1, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 600");
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);
    // b.多个两表笛卡尔积
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    char tableE2[20] = "Ejoin02";
    ret = BatchInsertByte256(g_conn, g_stmt, tableB2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableC2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 600");
    AW_MACRO_EXPECT_EQ_INT(4, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableE2, count1, sizeNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               003.not join规则触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_003)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_003";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[2000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 201; i++) {
            count1[k][0] = j;
            count1[k][1] = i;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    // 2023.11.28.notjoin plan占用内存变大
    int32_t dataNum = 615;
    char viewFilterValue[40] = "CUR_ITEM_NUM: 500";
    (void)snprintf(viewFilterValue, 40, "CUR_ITEM_NUM: %d", dataNum * 2);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1 + dataNum, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1 + dataNum, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2023.12.6运行内存优化单批数据暂无法触发

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum * 2);
    AW_MACRO_EXPECT_EQ_INT(dataNum * 2, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum * 2);
    AW_MACRO_EXPECT_EQ_INT(dataNum * 2, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);

    // 删除表B数据触发not join规则B表删除失败
    ret = BatchDeleteByte256(g_conn, g_stmt, tableB1, count1, 1, GMERR_OK, false, SECDELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum * 2);
    AW_MACRO_EXPECT_EQ_INT(dataNum * 2, ret);
    // A表可以删除
    ret = BatchDeleteByte256(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_OK, false, PKTWODELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 删除A表之后B表也可以删除
    ret = BatchDeleteByte256(g_conn, g_stmt, tableB1, count1, dataNum-1, GMERR_OK, false, PKTWODELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchDeleteByte256(g_conn, g_stmt, tableA1, count1,dataNum, GMERR_OK, false, SECDELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    ret = BatchDeleteByte256(g_conn, g_stmt, tableB1, count1, 1, GMERR_OK, false, SECDELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               004.function规则触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_004)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_004";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 610;
    char viewFilterValue[40] = "CUR_ITEM_NUM: 500";
    (void)snprintf(viewFilterValue, 40, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    // function中写D表数据
    count1[dataNum][0] = 1;
    count1[dataNum][1] = 1001;
    ret = ReadTableABC(g_stmt, tableD2, count1, dataNum + 1);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, dataNum + 1);
    AW_MACRO_EXPECT_EQ_INT(dataNum + 1, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    // c.输入表join function，udf内读多个输入表的org直到内存满
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 600");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA3, count1, dataNum, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    if(ret != GMERR_OUT_OF_MEMORY){
        AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    }
    ret = ReadTableABC(g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableD3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 回滚过程中可查到该记录
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 600");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2023.11.27 不再触发使用到逃生内存
    ret = CheckLog();
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               005.聚合规则触发动态内存不足

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_005)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_005";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // a.聚合规则含many_to_one,写输入直到内存满
    AW_FUN_Log(LOG_STEP, "a.聚合规则含many_to_one,写输入直到内存满");
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    char tableE2[20] = "Ejoin02";
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";
    int32_t count1[2000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 21; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 850;
    char viewFilterValue[40] = "CUR_ITEM_NUM: 500";
    (void)snprintf(viewFilterValue, 40, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "b.聚合规则含many_to_many,写输入直到内存满");
    // b.聚合规则含many_to_many,写输入直到内存满
    // 2023.12.5 当前内存不足时已触发udf写D表
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count1, dataNum);
    if (ret != GMERR_OUT_OF_MEMORY) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    }
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableE2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableD2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c.聚合规则，udf读其它org表，直到内存满
    AW_FUN_Log(LOG_STEP, "c.聚合规则，udf读其它org表，直到内存满");
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA3, count1, dataNum, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    if (ret != GMERR_OUT_OF_MEMORY) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    }
    ret = ReadTableABC(g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableD3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", viewFilterValue);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 该用例未触发逃生通道
    const char *logFilter = "Unable to alloc from ctx planExec, ctx ID: 89.";
    ret = CheckLog(logFilter);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               006.union_delete触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_006)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_006";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    // a.输入表写入数据，再主表删除全部数据触发动态内存不足
    char tableA022[] = "A022";
    char tableA023[] = "A023";
    char tableA024[] = "A024";
    char tableA025[] = "A025";
    char tableA026[] = "A026";
    char tableA027[] = "A027";
    char tableA028[] = "A028";
    char tableA029[] = "A029";
    char tableA030[] = "A030";
    int32_t dataNum = 100;
    ret = BatchInsertByte256(g_conn, g_stmt, tableA022, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA023, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA024, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA025, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA026, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA027, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA028, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA029, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA030, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 100");
    AW_MACRO_EXPECT_EQ_INT(9, oldItemNum);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA022, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableA030, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);

    // 删除主表数据
    ret = BatchDeleteByte256(g_conn, g_stmt, tableA022, count1, 1, GMERR_OK, false, SECDELETE);
    if(ret != GMERR_OUT_OF_MEMORY){
        AW_MACRO_EXPECT_EQ_INT(GMERR_MEMORY_OPERATE_FAILED, ret);
    }
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 回滚过程中可查到该记录
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 100");
    AW_MACRO_EXPECT_EQ_INT(9, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 未触发订阅逃生通道
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               007.pubsub触发动态内存不足
a.验证普通pubsub表订阅消息能够正常回滚
b.验证pubsub中间表订阅消息能够正常回滚
c.验证外部表订阅消息能够正常回滚
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_007)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_007";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N007");
    readJanssonFile("./datalog_file/N007.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表创建订阅
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalog_file/outTable_pubsub007.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    // 普通pubsub表创建订阅
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoOutput);

    // pubsub资源表创建订阅
    GmcConnT *subConnResource;
    GmcStmtT *subStmtResource;
    SnUserDataWithFuncT *userDataResource;
    const char *subConnNameResource = "testSubResource";
    ret = testSubConnect(&subConnResource, &subStmtResource, 1, g_epoll_reg_info, subConnNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoResource = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub_resource.gmjson", &sub_infoResource);
    EXPECT_NE((void *)NULL, sub_infoResource);
    const char *subNameResource = "testSub_Resource";
    GmcSubConfigT tmp_sub_infoResource;
    tmp_sub_infoResource.subsName = subNameResource;
    tmp_sub_infoResource.configJson = sub_infoResource;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSubPubSubResource(subConnResource, &userDataResource, &tmp_sub_infoResource, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoResource);

    // a.两表 join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "G000";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 615;
    char expectFileterContent[40] = "CUR_ITEM_NUM: dataNum";
    (void)sprintf(expectFileterContent, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_SUB_PUSH_QUEUE_FULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA21[20] = "L000";
    char tableA22[20] = "L001";
    char tableB2[20] = "K000";
    char tableC2[20] = "M000";
    ret = BatchInsertBigClient(g_conn, g_stmt, tableA22, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertBigClient(g_conn, g_stmt, tableA21, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    // 11.3 输入表L0001写入失败，预期数据回滚，输出表无数据
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA22, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableA21, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataResource->data, GMC_SUB_EVENT_INSERT, 1024, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // c.验证外部表订阅消息能够正常回滚
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "N007";

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA3, count1, dataNum);
    if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    }
    ret = ReadTableABC(g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [3] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 未触发订阅逃生通道
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = GmcUnSubscribe(g_stmt, subNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataResource->data);
    free(userDataResource);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(subConnResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               008.null(0)规则触发动态内存不足
a.两表join投影到null（0）
b.两表做笛卡尔积，投影到null(0)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_008)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_008";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 2, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            k++;
        }
    }
    int32_t dataNum = 615;
    char expectFileterContent[40] = "CUR_ITEM_NUM: dataNum";
    (void)sprintf(expectFileterContent, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    int32_t cycle = 10;
    while (cycle > 0) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        cycle--;
    }
    sleep(10);
    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = executeCommand(g_command, "GLOBAL_DYN_MEM: [2] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    // b.两个表进行笛卡尔积写数据直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    ret = BatchInsertByte256(g_conn, g_stmt, tableB2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(4, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 未触发订阅逃生通道
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               009.表记录过期触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_009)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char nsName1[128] = "DataLog_reliability_03_009";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "A000";
    char tableB1[20] = "B000";
    char tableC1[20] = "C000";
    int32_t count1[2000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 201; i++) {
            count1[k][0] = j;
            count1[k][1] = i;
            count1[k][2] = j;
            count1[k][3] = 2000;
            k++;
        }
    }
    int32_t dataNum = 615;
    char expectFileterContent[40] = "CUR_ITEM_NUM: dataNum";
    (void)sprintf(expectFileterContent, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1 + dataNum, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum * 2);
    AW_MACRO_EXPECT_EQ_INT(dataNum * 2, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);

    sleep(4);
    // 过期成功
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 未触发订阅逃生通道
    ret = CheckLog((char *)"only rollback by undo because of oom, it may cause inconsistent data");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    // 2023.12.6当前场景内存优化后不会触发
    ret = CheckLog((char *)"Handle rollback");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);

    // 主键删除B000表数据
    ret = BatchDeleteByte256(g_conn, g_stmt, tableB1, count1, 580, GMERR_OK, false, PKTWODELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableB1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, 0);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);

    // 当前有足够的内存供表数据过期
    sleep(5);
    ret = ReadTableABC(g_stmt, tableB1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  :010.precedence规则触发动态内存不足
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_010)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char nsName1[128] = "DataLog_reliability_03_010";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提前查视图预留视图元数据内存
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");

    // a.多表投影
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Ajoin02";
    char tableC1[20] = "Ajoin03";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 615;
    char expectFileterContent[40] = "CUR_ITEM_NUM: dataNum";
    (void)sprintf(expectFileterContent, "CUR_ITEM_NUM: %d", dataNum);
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    // 写delta表会使用到系统内存
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_OUT_OF_MEMORY);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", expectFileterContent);
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    system(g_command);
    ret = executeCommand(g_command, "GLOBAL_PHY_SIZE: [0] MB");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckLog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               011.function规则触发动态内存不足含udf超时日志
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_011)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_011";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 10;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);
    const char *logFilter1 = "UDF func:dtl_ext_func_funcA001 exceed.";
    ret = CheckLog(logFilter1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(11, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    const char *logFilter2 = "UDF func:dtl_ext_func_funcA002 exceed.";
    ret = CheckLog(logFilter2);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // c.输入表join function，udf内读多个输入表的org直到内存满
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableA3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 回滚过程中可查到该记录
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(5, oldItemNum);
    const char *logFilter3 = "UDF func:dtl_ext_func_funcA003 exceed.";
    ret = CheckLog(logFilter3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               012.聚合规则触发动态内存不足含udf超时

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_012)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_012";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 10;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    const char *logFilter1 = "UDF function dtl_agg_func_aggA001 exceed timeout alarm threshold";
    ret = CheckLog(logFilter1);
    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(6, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    const char *logFilter2 = "UDF function dtl_agg_func_aggA002 exceed timeout alarm threshold";
    ret = CheckLog(logFilter2);
    // c.输入表join function，udf内读多个输入表的org直到内存满
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(6, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableA3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 回滚过程中可查到该记录
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(9, oldItemNum);
    const char *logFilter3 = "UDF func:dtl_agg_func_aggA003 exceed.";
    ret = CheckLog(logFilter3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               013.表记录过期时, 注入时钟向后跳变的故障, 原本不会过期的数据提前过期

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_013)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char nsName1[128] = "DataLog_reliability_03_009";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "A000";
    char tableB1[20] = "B000";
    char tableC1[20] = "C000";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][3] = 20000;
            k++;
        }
    }
    // 开启时钟跳变
    g_testClockChange.isNeedChangeTime = true;
    g_testClockChange.clockChangeMode = g_clockLeft;
    g_testClockChange.sleepTime = 0;
    g_testClockChange.isNeedSleep = false;
    int32_t dataNum = 1;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);

    sleep(12);
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    system("gmsysview record B000");
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               014.3,4,5的挂死阈值, pubsub等待回应超时期间, 时钟向前跳变60s, 服务端挂死

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_014)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sed -i \"s/sleep 10/sleep 4/g\" ../reliability_test/reliability_scene_ddl.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_007";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N007");
    readJanssonFile("./datalog_file/N007.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表创建订阅
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalog_file/outTable_pubsub007.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    // 普通pubsub表创建订阅
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoOutput);

    // pubsub资源表创建订阅
    GmcConnT *subConnResource;
    GmcStmtT *subStmtResource;
    SnUserDataWithFuncT *userDataResource;
    const char *subConnNameResource = "testSubResource";
    ret = testSubConnect(&subConnResource, &subStmtResource, 1, g_epoll_reg_info, subConnNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoResource = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub_resource.gmjson", &sub_infoResource);
    EXPECT_NE((void *)NULL, sub_infoResource);
    const char *subNameResource = "testSub_Resource";
    GmcSubConfigT tmp_sub_infoResource;
    tmp_sub_infoResource.subsName = subNameResource;
    tmp_sub_infoResource.configJson = sub_infoResource;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSubPubSubResource(subConnResource, &userDataResource, &tmp_sub_infoResource, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoResource);
    // 开启时钟跳变
    g_testClockChange.isNeedChangeTime = true;
    g_testClockChange.clockChangeMode = g_clockLeft;
    g_testClockChange.sleepTime = 0;
    g_testClockChange.isNeedSleep = false;
    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "G000";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 100;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 100");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    g_testClockChange.isNeedChangeTime = false;
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_testClockChange.isNeedChangeTime = true;
    ret = BatchInsertByte256(g_conn, g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ReadTableABC(g_stmt, tableA1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 100");
    AW_MACRO_EXPECT_EQ_INT(2, oldItemNum);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = GmcUnSubscribe(g_stmt, subNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataResource->data);
    free(userDataResource);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, "N007");
    ret = testGmcDisconnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(subConnResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sed -i \"s/sleep 4/sleep 10/g\" ../reliability_test/reliability_scene_ddl.sh");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
               015.udf交互时钟跳变预期超时机制不受影响

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_015)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_012";
    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启时钟跳变
    g_testClockChange.isNeedChangeTime = true;
    g_testClockChange.clockChangeMode = g_clockLeft;
    g_testClockChange.sleepTime = 0;
    g_testClockChange.isNeedSleep = false;
    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "Ajoin01";
    char tableB1[20] = "Bjoin01";
    char tableC1[20] = "Cjoin01";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 10;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count1, dataNum, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 动态内存不随数据cycle增加而发生变化
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='Top Dynamic Memory Context'",
        g_toolPath);
    ret = ReadTableABC(g_stmt, tableA1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableC1, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(3, oldItemNum);
    const char *logFilter1 = "UDF func:dtl_agg_func_aggA001 exceed.";
    ret = CheckLog(logFilter1);
    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(6, oldItemNum);
    ret = ReadTableABC(g_stmt, tableA2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(10, ret);
    ret = ReadTableABC(g_stmt, tableC2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(20, ret);
    ret = ReadTableABC(g_stmt, tableB2, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    const char *logFilter2 = "UDF func:dtl_agg_func_aggA002 exceed.";
    ret = CheckLog(logFilter2);
    // c.输入表join function，udf内读多个输入表的org直到内存满
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";

    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(6, oldItemNum);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA3, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTableABC(g_stmt, tableA3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableB3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    ret = ReadTableABC(g_stmt, tableD3, count1, 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    // 回滚过程中可查到该记录
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(9, oldItemNum);
    const char *logFilter3 = "UDF func:dtl_agg_func_aggA003 exceed.";
    ret = CheckLog(logFilter3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

void *UNINSTALLSO(void *args)
{
    char nsName[128] = "DataLog_reliability_03_016";
    int ret;
    // 卸载datalog.so
    sleep(4);
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
}

void *CHANGETIME(void *args)
{
    g_testClockChange.isNeedChangeTime = true;
    g_testClockChange.clockChangeMode = g_clockLeft;
    g_testClockChange.sleepTime = 0;
    g_testClockChange.isNeedSleep = false;

    // 增加时钟跳变故障
    sleep(4);
    if (g_testClockChange.isNeedChangeTime) {
        AW_FUN_Log(LOG_STEP, "clock change");
        memset(g_command, 0, sizeof g_command);
        (void)sprintf(g_command, "%s %s", g_cfeTool, g_testClockChange.clockChangeMode);
        system(g_command);
        if (g_testClockChange.isNeedSleep) {
            // 是否需要触发回调时sleep
            AW_FUN_Log(LOG_STEP, "sleep after clock change");
            sleep(g_testClockChange.sleepTime);
        }
    }
}

/* ****************************************************************************
 Description  : 016.表记录过期&&so卸载交互时钟跳变

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_016)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char nsName1[128] = "DataLog_reliability_03_016";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // a.两表not join，批写数据直到内存满
    char tableA1[20] = "A000";
    char tableB1[20] = "B000";
    char tableC1[20] = "C000";
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][3] = 2000;
            k++;
        }
    }
    // 开启时钟跳变

    int32_t dataNum = 1;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    // 写入数据后开启两个线程，一个线程进行卸载，一个进行时钟跳变
    ret = BatchInsertByte256(g_conn, g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(dataNum, ret);
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(1, oldItemNum);
    ret = pthread_create(&client_thr[0], NULL, UNINSTALLSO, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, CHANGETIME, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = ReadTableABC(g_stmt, tableB1, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    system("gmsysview record B000");
    oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    const char *logFilter = "UDF function dtl_timeout_callback_B000 exceed timeout alarm threshold";
    ret = CheckLog(logFilter);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

char g_startClientName[20] = "";
char g_stopClientName[20] = "";
void *STARTCLIENT(void *args)
{
    system(g_startClientName);
}

void *STOPCLIENT(void *args)
{
    system(g_stopClientName);
}
/* ****************************************************************************
 Description  : 017.客户端进程异常退出，agg(manyt _to_many)，写入数据后异常退出客户端

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_03_017)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "agg_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "agg_ClientExit";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, " test end");
}

/* ****************************************************************************
 Description  : 018.客户端进程异常退出，TBM，udf内sleep，写入数据后异常退出客户端

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_018)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "tbm_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "tbm_ClientExit";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    system("rm -rf  /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, " test end");
}

/* ****************************************************************************
 Description  : 019.客户端进程异常退出，join多表，写入数据后异常退出客户端

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_019)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "join_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "join_ClientExit";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, " test end");
}

/* ****************************************************************************
 Description  : 020.客户端进程异常退出，pubsub大报文订阅，写入数据后异常退出客户端

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_020)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_020";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // pubsub资源表创建订阅
    GmcConnT *subConnResource;
    GmcStmtT *subStmtResource;
    SnUserDataWithFuncT *userDataResource;
    const char *subConnNameResource = "testSubResource";
    ret = testSubConnect(&subConnResource, &subStmtResource, 1, g_epoll_reg_info, subConnNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoResource = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub_resource.gmjson", &sub_infoResource);
    EXPECT_NE((void *)NULL, sub_infoResource);
    const char *subNameResource = "testSub_Resource";
    GmcSubConfigT tmp_sub_infoResource;
    tmp_sub_infoResource.subsName = subNameResource;
    tmp_sub_infoResource.configJson = sub_infoResource;
    AW_FUN_Log(LOG_STEP, "创建订阅");

    ret = TestCreateSubPubSubResource(subConnResource, &userDataResource, &tmp_sub_infoResource, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoResource);
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "big_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "big_ClientExit";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "断连");
    ret = testWaitSnRecv(userDataResource->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConnResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    while (ret) {
        ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    }

    memset(g_command, 0, sizeof g_command);
    AW_FUN_Log(LOG_STEP, "取消订阅");
    ret = GmcUnSubscribe(g_stmt, subNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "释放订阅内存");
    testSnFreeUserData(userDataResource->data);
    free(userDataResource);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "reliability03.cpp test end");
}

/* ****************************************************************************
 Description  : 021.客户端进程异常退出，pubsub订阅消息队列满，写入数据后异常退出客户端

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_021)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_020";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // pubsub资源表创建订阅
    GmcConnT *subConnResource;
    GmcStmtT *subStmtResource;
    SnUserDataWithFuncT *userDataResource;
    const char *subConnNameResource = "testSubResource";
    ret = testSubConnect(&subConnResource, &subStmtResource, 1, g_epoll_reg_info, subConnNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoResource = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub_resource.gmjson", &sub_infoResource);
    EXPECT_NE((void *)NULL, sub_infoResource);
    const char *subNameResource = "testSub_Resource";
    GmcSubConfigT tmp_sub_infoResource;
    tmp_sub_infoResource.subsName = subNameResource;
    tmp_sub_infoResource.configJson = sub_infoResource;
    AW_FUN_Log(LOG_STEP, "创建订阅");

    ret = TestCreateSubPubSubResource(subConnResource, &userDataResource, &tmp_sub_infoResource, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoResource);
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "pubsub_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "pubsub_ClientExit";
    g_isNeedASleep = true;
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "断连");
    ret = testWaitSnRecv(userDataResource->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConnResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "取消订阅");
    
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    while (ret) {
        ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    }

    memset(g_command, 0, sizeof g_command);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "释放订阅内存");
    testSnFreeUserData(userDataResource->data);
    free(userDataResource);
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "reliability03.cpp test end");
}

void *thread_rProc_kill19_18_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_kill19_18_server");
}

void *thread_Agg_Client(void *args)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_017";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 1;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 10");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    // 2.输入表join function，udf内写多个输入表的delta直到内存满
    char tableA2[20] = "Ajoin02";
    char tableB2[20] = "Bjoin02";
    char tableC2[20] = "Cjoin02";
    char tableD2[20] = "Djoin02";
    // 异常退出标识
    AW_FUN_Log(LOG_STEP, "BatchInsertByteOne begin");
    system("touch agg_client.txt");
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count1, dataNum, GMERR_REQUEST_TIME_OUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
}

/* ****************************************************************************
 Description  : 022.agg用例, 运行过程中服务端挂起一段时间后恢复, 验证数据计算正常.

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_022)
{
    // 客户端超时，但服务恢复后数据正常处理成功
    int32_t ret = 0;
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, thread_Agg_Client, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat agg_client.txt");
        ret = executeCommand(g_command, "No such file or directory");
    } while (!ret);
    AW_FUN_Log(LOG_STEP, "服务端挂起");
    ret = pthread_create(&client_thr[1], NULL, thread_rProc_kill19_18_server, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    // 验证数据写入成功
    sleep(10);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName1[128] = "DataLog_reliability_03_017";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count[500][7] = {0};
    for (int32_t i = 0; i < 500; i++) {
        count[i][0] = 1;
        count[i][1] = i + 1;
    }
    char tableE[10] = "Ejoin02";
    ret = ReadTableABC(g_stmt, tableE, count, 500);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof g_command);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, " test end");
}
void *thread_Tbm_Client(void *args)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_018";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 10;
    int32_t oldItemNum = GetViewFieldResultCount("V\\$STORAGE_HEAP_STAT", "CUR_ITEM_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(0, oldItemNum);
    char tableQ[] = "Q000";
    AW_FUN_Log(LOG_STEP, "BatchInsertByteOne begin");
    system("touch tbm_client.txt");
    ret = BatchInsertByte256(g_conn, g_stmt, tableQ, count1, 10, GMERR_REQUEST_TIME_OUT, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
}
/* ****************************************************************************
 Description  : 023.tbm用例, 运行过程中服务端挂起一段时间后恢复, 验证数据计算正常.

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_023)
{
    // 客户端超时，但服务恢复后数据正常处理成功
    int32_t ret = 0;
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    AW_FUN_Log(LOG_STEP, "test start");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, thread_Tbm_Client, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat tbm_client.txt");
        ret = executeCommand(g_command, "No such file or directory");
    } while (!ret);
    AW_FUN_Log(LOG_STEP, "服务端挂起");
    sleep(2);
    ret = pthread_create(&client_thr[1], NULL, thread_rProc_kill19_18_server, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    // 验证数据写入成功
    sleep(10 * 10);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName1[128] = "DataLog_reliability_03_018";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    do {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
        ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
        sleep(5);
    } while (ret);

    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    char tableQ[] = "Q000";
    ret = ReadTableABC(g_stmt, tableQ, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    memset(g_command, 0, sizeof g_command);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("rm -rf  /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, " test end");
}
void *thread_Join_Client(void *args)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_019";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count1[1000][7] = {{1, 1, 1, 2, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            k++;
        }
    }
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";

    ret = BatchInsertByte256(g_conn, g_stmt, tableB3, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD3, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "BatchInsertByte256 begin");
    system("touch join_client.txt");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA3, count1, 10, GMERR_REQUEST_TIME_OUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
}
/* ****************************************************************************
 Description  : 024.join用例, 运行过程中服务端挂起一段时间后恢复, 验证数据计算正常.

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_024)
{
    // 客户端超时，但服务恢复后数据正常处理成功
    int32_t ret = 0;
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, thread_Join_Client, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat join_client.txt");
        ret = executeCommand(g_command, "No such file or directory");
    } while (!ret);
    AW_FUN_Log(LOG_STEP, "服务端挂起");
    sleep(2);
    ret = pthread_create(&client_thr[1], NULL, thread_rProc_kill19_18_server, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    // 验证数据写入成功
    sleep(10 * 10);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char nsName1[128] = "DataLog_reliability_03_019";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    do {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
        ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
        sleep(5);
    } while (ret);

    int32_t count1[1000][7] = {{1, 1, 1, 2, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            k++;
        }
    }
    char tableA3[20] = "Ajoin03";
    char tableB3[20] = "Bjoin03";
    char tableC3[20] = "Cjoin03";
    char tableD3[20] = "Djoin03";
    ret = ReadTableABC(g_stmt, tableA3, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = ReadTableABC(g_stmt, tableC3, count1, 10);
    AW_MACRO_EXPECT_EQ_INT(10, ret);

    memset(g_command, 0, sizeof g_command);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, " test end");
}

void *thread_Pubsub_Client(void *args)
{
    AW_FUN_Log(LOG_STEP, "test begin");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    int32_t count1[1000][7] = {{1, 1, 1, 0, 2, 1, 2}};
    int32_t k = 0;
    for (int j = 1; j < 11; j++) {
        for (int i = 1; i < 101; i++) {
            count1[k][0] = i;
            count1[k][1] = j;
            count1[k][2] = 1;
            count1[k][4] = 2;
            k++;
        }
    }
    int32_t dataNum = 10;
    char tableA2[20] = "L000";
    char tableB2[20] = "K000";
    char tableC2[20] = "M000";
    AW_FUN_Log(LOG_STEP, " client wtite data");
    system("touch pubsub_client.txt");
    ret = BatchInsertBigClient(g_conn, g_stmt, tableA2, count1, dataNum, GMERR_REQUEST_TIME_OUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    AW_FUN_Log(LOG_STEP, " pubsub_client wtite data end");
}
/* ****************************************************************************
 Description  : 025.pubsub大报文推送用例, 运行过程中服务端挂起一段时间后恢复, 验证数据计算正常.

*******************************************************/
TEST_F(reliability01, DataLog_reliability_03_025)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
#define MAX_CMD_SIZE 1024
    char command[MAX_CMD_SIZE] = {0};
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=3,4,15\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=74\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=62\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=10\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    // 创建epoll监听线程
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_03_020";
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(command, 0, sizeof command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // pubsub资源表创建订阅
    GmcConnT *subConnResource;
    GmcStmtT *subStmtResource;
    SnUserDataWithFuncT *userDataResource;
    const char *subConnNameResource = "testSubResource";
    ret = testSubConnect(&subConnResource, &subStmtResource, 1, g_epoll_reg_info, subConnNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoResource = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub_resource.gmjson", &sub_infoResource);
    EXPECT_NE((void *)NULL, sub_infoResource);
    const char *subNameResource = "testSub_Resource";
    GmcSubConfigT tmp_sub_infoResource;
    tmp_sub_infoResource.subsName = subNameResource;
    tmp_sub_infoResource.configJson = sub_infoResource;
    AW_FUN_Log(LOG_STEP, "创建订阅");

    ret = TestCreateSubPubSubResource(subConnResource, &userDataResource, &tmp_sub_infoResource, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoResource);
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, thread_Pubsub_Client, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat pubsub_client.txt");
        ret = executeCommand(g_command, "No such file or directory");
    } while (!ret);
    AW_FUN_Log(LOG_STEP, "服务端挂起");
    sleep(2);
    ret = pthread_create(&client_thr[1], NULL, thread_rProc_kill19_18_server, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "断连");
    sleep(40);
    ret = testWaitSnRecv(userDataResource->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConnResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "取消订阅");
    ret = GmcUnSubscribe(g_stmt, subNameResource);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "释放订阅内存");
    testSnFreeUserData(userDataResource->data);
    free(userDataResource);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$STORAGE_LOCK_OVERVIEW", g_toolPath);
    ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
    while (ret) {
        ret = executeCommand(g_command, "USED_LOCK_CNT: 0");
        sleep(10);
    }

    memset(g_command, 0, sizeof g_command);
    snprintf(g_command, MAX_CMD_SIZE,
        "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME='datalog' | grep TOTAL_ALLOC_SIZE_ON_THIS_TREE |wc -l", g_toolPath);
    ret = executeCommand(g_command, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}
