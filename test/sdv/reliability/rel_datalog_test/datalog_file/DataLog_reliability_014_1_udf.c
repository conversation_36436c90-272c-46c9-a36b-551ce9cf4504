/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

#include <stdio.h>
#include <string.h>
#include "gm_udf.h"
#include "assert.h"

#pragma pack(1)
typedef struct {
    int32_t a;
    int32_t b;
} DEL;
#pragma pack(0)

int32_t dtl_ext_func_funcA001(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;

    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }

    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA002(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA003(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA004(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA005(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA006(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA007(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA008(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA009(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0010(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0011(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0012(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0013(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0014(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0015(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0016(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0017(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0018(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0019(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0020(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0021(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0025(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0026(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0027(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0028(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0029(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0030(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0031(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0032(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0033(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0034(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0035(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0036(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0037(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0038(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0039(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0040(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0041(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0042(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0043(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0044(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0045(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0046(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0047(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0048(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0049(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0050(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0051(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0052(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0053(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0054(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0055(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0056(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0057(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0058(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0059(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0060(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0061(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0062(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0063(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0064(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0065(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0066(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0067(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0068(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0069(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0070(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0071(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0072(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0073(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0074(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0075(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0076(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0077(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0078(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0079(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0080(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0081(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0082(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0083(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0084(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0085(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0086(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0087(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0088(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0089(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0090(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0091(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0092(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0093(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0094(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0095(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0096(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0097(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0098(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA0099(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00100(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00101(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00102(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00103(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00104(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00105(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00106(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00107(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00108(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00109(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00110(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00111(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00112(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00113(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00114(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00115(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00116(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00117(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00118(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00119(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00120(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00121(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00122(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00123(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00124(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00125(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00126(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00127(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00128(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00129(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00130(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00131(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00132(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00133(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00134(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00135(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00136(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00137(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00138(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00139(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00140(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00141(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00142(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00143(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00144(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00145(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00146(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00147(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00148(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00149(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00150(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00151(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00152(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00153(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00154(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00155(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00156(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00157(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00158(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00159(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00160(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00161(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00162(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00163(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00164(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00165(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00166(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00167(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00168(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00169(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00170(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00171(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00172(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00173(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00174(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00175(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00176(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00177(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00178(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00179(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00180(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00181(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00182(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00183(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00184(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00185(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00186(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00187(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00188(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00189(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00190(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00191(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00192(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00193(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00194(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00195(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00196(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00197(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00198(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00199(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00200(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00201(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00202(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00203(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00204(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00205(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00206(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00207(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00208(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00209(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00210(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00211(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00212(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00213(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00214(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00215(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00216(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00217(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00218(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00219(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00220(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00221(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00222(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00223(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00224(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00225(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00226(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00227(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00228(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00229(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00230(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00231(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00232(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00233(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00234(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00235(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00236(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00237(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00238(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00239(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00240(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00241(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00242(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00243(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00244(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00245(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00246(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00247(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00248(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00249(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00250(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00251(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00252(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00253(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00254(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00255(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00256(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00257(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00258(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00259(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00260(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00261(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00262(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00263(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00264(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00265(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00266(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00267(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00268(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00269(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00270(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00271(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00272(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00273(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00274(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00275(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00276(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00277(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00278(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00279(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00280(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00281(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00282(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00283(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00284(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00285(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00286(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00287(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00288(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00289(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00290(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00291(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00292(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00293(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00294(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00295(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00296(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00297(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00298(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00299(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00300(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00301(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00302(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00303(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00304(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00305(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00306(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00307(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00308(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00309(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00310(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00311(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00312(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00313(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00314(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00315(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00316(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00317(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00318(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00319(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00320(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00321(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00322(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00323(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00324(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00325(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00326(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00327(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00328(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00329(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00330(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00331(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00332(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00333(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00334(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00335(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00336(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00337(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00338(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00339(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00340(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00341(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00342(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00343(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00344(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00345(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00346(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00347(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00348(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00349(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00350(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00351(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00352(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00353(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00354(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00355(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00356(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00357(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00358(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00359(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00360(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00361(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00362(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00363(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00364(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00365(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00366(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00367(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00368(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00369(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00370(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00371(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00372(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00373(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00374(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00375(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00376(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00377(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00378(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00379(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00380(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00381(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00382(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00383(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00384(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00385(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00386(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00387(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00388(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00389(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00390(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00391(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00392(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00393(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00394(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00395(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00396(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00397(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00398(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00399(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00400(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00401(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00402(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00403(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00404(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00405(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00406(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00407(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00408(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00409(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00410(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00411(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00412(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00413(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00414(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00415(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00416(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00417(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00418(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00419(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00420(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00421(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00422(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00423(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00424(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00425(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00426(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00427(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00428(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00429(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00430(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00431(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00432(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00433(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00434(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00435(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00436(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00437(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00438(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00439(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00440(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00441(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00442(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00443(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00444(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00445(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00446(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00447(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00448(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00449(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00450(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00451(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00452(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00453(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00454(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00455(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00456(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00457(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00458(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00459(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00460(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00461(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00462(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00463(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00464(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00465(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00466(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00467(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00468(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00469(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00470(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00471(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00472(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00473(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00474(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00475(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00476(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00477(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00478(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00479(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00480(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00481(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00482(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00483(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00484(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00485(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00486(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00487(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00488(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00489(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00490(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00491(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00492(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00493(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00494(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00495(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00496(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00497(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00498(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00499(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00500(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00501(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00502(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00503(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00504(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00505(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00506(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00507(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00508(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00509(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00510(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00511(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00512(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00513(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00514(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00515(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00516(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00517(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00518(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00519(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00520(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00521(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00522(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00523(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00524(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00525(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00526(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00527(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00528(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00529(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00530(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00531(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00532(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00533(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00534(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00535(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00536(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00537(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00538(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00539(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00540(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00541(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00542(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00543(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00544(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00545(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00546(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00547(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00548(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00549(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00550(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00551(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00552(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00553(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00554(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00555(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00556(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00557(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00558(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00559(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00560(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00561(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00562(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00563(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00564(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00565(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00566(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00567(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00568(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00569(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00570(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00571(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00572(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00573(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00574(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00575(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00576(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00577(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00578(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00579(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00580(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00581(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00582(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00583(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00584(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00585(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00586(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00587(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00588(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00589(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00590(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00591(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00592(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00593(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00594(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00595(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00596(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00597(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00598(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00599(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00600(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00601(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00602(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00603(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00604(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00605(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00606(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00607(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00608(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00609(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00610(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00611(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00612(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00613(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00614(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00615(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00616(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00617(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00618(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00619(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00620(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00621(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00622(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00623(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00624(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00625(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00626(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00627(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00628(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00629(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00630(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00631(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00632(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00633(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00634(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00635(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00636(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00637(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00638(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00639(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00640(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00641(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00642(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00643(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00644(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00645(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00646(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00647(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00648(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00649(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00650(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00651(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00652(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00653(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00654(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00655(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00656(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00657(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00658(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00659(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00660(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00661(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00662(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00663(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00664(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00665(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00666(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00667(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00668(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00669(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00670(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00671(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00672(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00673(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00674(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00675(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00676(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00677(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00678(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00679(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00680(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00681(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00682(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00683(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00684(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00685(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00686(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00687(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00688(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00689(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00690(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00691(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00692(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00693(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00694(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00695(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00696(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00697(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00698(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00699(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00700(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00701(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00702(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00703(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00704(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00705(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00706(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00707(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00708(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00709(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00710(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00711(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00712(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00713(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00714(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00715(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00716(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00717(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00718(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00719(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00720(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00721(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00722(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00723(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00724(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00725(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00726(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00727(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00728(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00729(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00730(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00731(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00732(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00733(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00734(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00735(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00736(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00737(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00738(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00739(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00740(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00741(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00742(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00743(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00744(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00745(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00746(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00747(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00748(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00749(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00750(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00751(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00752(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00753(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00754(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00755(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00756(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00757(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00758(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00759(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00760(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00761(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00762(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00763(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00764(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00765(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00766(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00767(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00768(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00769(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00770(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00771(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00772(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00773(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00774(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00775(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00776(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00777(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00778(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00779(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00780(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00781(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00782(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00783(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00784(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00785(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00786(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00787(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00788(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00789(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00790(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00791(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00792(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00793(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00794(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00795(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00796(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00797(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00798(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00799(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00800(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00801(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00802(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00803(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00804(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00805(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00806(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00807(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00808(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00809(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00810(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00811(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00812(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00813(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00814(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00815(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00816(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00817(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00818(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00819(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00820(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00821(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00822(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00823(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00824(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00825(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00826(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00827(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00828(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00829(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00830(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00831(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00832(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00833(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00834(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00835(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00836(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00837(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00838(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00839(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00840(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00841(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00842(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00843(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00844(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00845(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00846(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00847(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00848(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00849(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00850(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00851(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00852(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00853(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00854(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00855(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00856(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00857(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00858(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00859(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00860(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00861(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00862(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00863(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00864(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00865(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00866(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00867(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00868(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00869(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00870(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00871(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00872(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00873(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00874(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00875(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00876(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00877(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00878(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00879(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00880(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00881(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00882(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00883(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00884(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00885(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00886(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00887(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00888(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00889(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00890(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00891(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00892(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00893(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00894(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00895(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00896(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00897(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00898(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00899(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00900(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00901(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00902(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00903(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00904(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00905(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00906(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00907(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00908(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00909(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00910(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00911(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00912(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00913(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00914(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00915(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00916(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00917(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00918(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00919(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00920(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00921(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00922(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00923(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00924(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00925(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00926(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00927(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00928(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00929(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00930(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00931(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00932(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00933(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00934(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00935(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00936(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00937(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00938(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00939(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00940(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00941(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00942(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00943(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00944(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00945(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00946(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00947(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00948(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00949(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00950(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00951(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00952(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00953(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00954(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00955(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00956(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00957(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00958(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00959(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00960(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00961(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00962(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00963(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00964(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00965(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00966(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00967(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00968(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00969(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00970(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00971(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00972(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00973(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00974(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00975(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00976(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00977(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00978(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00979(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00980(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00981(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00982(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00983(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00984(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00985(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00986(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00987(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00988(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00989(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00990(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00991(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00992(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00993(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00994(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00995(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00996(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00997(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00998(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA00999(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001000(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001001(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001002(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001003(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001004(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001005(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001006(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001007(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001008(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001009(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001010(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001011(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001012(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001013(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001014(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001015(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001016(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001017(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001018(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001019(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001020(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001021(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001022(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001023(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}

int32_t dtl_ext_func_funcA001024(void *tuple, GmUdfCtxT *ctx)
{
    DEL *a = (DEL *)tuple;
    a->b = a->a + 1;
    DEL *outStruct = NULL;
    if (a->a % 2 == 0) {
        outStruct = GmUdfMemAlloc(ctx, 2 * 1024);
    }

    if (a->a % 2 != 0) {
        outStruct = GmUdfMemAlloc(ctx, 1.5 * 1024);
    }
    if (outStruct == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    return GMERR_OK;
}
