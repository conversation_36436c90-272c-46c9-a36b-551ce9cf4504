%version v0.0.0->v1.0.0
namespace ns9
{
%alter function funcA0989(a: int4 -> b: int4){ }
%alter rule r1 A0990(a,b,c1,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9) :- A0989(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9), funcA0989(c,c1).
}

%block 1
