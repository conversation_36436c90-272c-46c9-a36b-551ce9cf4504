namespace ns4{
%table C(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8, e4:byte1, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, g8:byte256, a9:str){
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)),index(31(d3))
}
%table B(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8, e4:int4, f4:byte128, g4:byte256 ,a5:int1, b5:int2, c5:int4, d5:int8, e5:byte1, f5:byte128, g5:byte256 ,a6:int1, b6:int2, c6:int4, d6:int8, e6:byte1, f6:byte128, g6:byte256 ,a7:int1, b7:int2, c7:int4, d7:int8, e7:byte1, f7:byte128, g7:byte256 ,a8:int1, b8:int2, c8:int4, d8:int8, e8:byte1, f8:byte128, g8:byte256, a9:str){
index(0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4)),index(1(b)),index(2(c)),index(3(d)),index(4(e)),index(5(f)),index(6(g)),index(7(a1)),index(8(b1)),index(9(c1)),
index(10(d1)),index(11(e1)),index(12(f1)),index(13(g1)),index(14(a2)),index(15(b2)),index(16(c2)),index(17(d2)),index(18(e2)),index(19(f2)),
index(20(g2)),index(21(a3)),index(22(b3)),index(23(c3)),index(24(d3)),index(25(e3)),index(26(f3)),index(27(g3)),index(28(a3)),index(29(b3)),
index(30(c3)),index(31(d3))
}
%resource reB0(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB1(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB2(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB3(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB4(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB5(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB6(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB7(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB8(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB9(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB10(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB11(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB12(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB13(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB14(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB15(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB16(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB17(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB18(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB19(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB20(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB21(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB22(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB23(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB24(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB25(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB26(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB27(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB28(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB29(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB30(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB31(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB32(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB33(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB34(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB35(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB36(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB37(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB38(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB39(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB40(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB41(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB42(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB43(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB44(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB45(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB46(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB47(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB48(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB49(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB50(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB51(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB52(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB53(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB54(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB55(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB56(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB57(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB58(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB59(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB60(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB61(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB62(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB63(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB64(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB65(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB66(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB67(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB68(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB69(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB70(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB71(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB72(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB73(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB74(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB75(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB76(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB77(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB78(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB79(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB80(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB81(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB82(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB83(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB84(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB85(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB86(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB87(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB88(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB89(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB90(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB91(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB92(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB93(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB94(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB95(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB96(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB97(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB98(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB99(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB100(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB101(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB102(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB103(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB104(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB105(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB106(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB107(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB108(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB109(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB110(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB111(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB112(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB113(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB114(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB115(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB116(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB117(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB118(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB119(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB120(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB121(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB122(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB123(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB124(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB125(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB126(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB127(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB128(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB129(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB130(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB131(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB132(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB133(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB134(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB135(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB136(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB137(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB138(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB139(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB140(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB141(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB142(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB143(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB144(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB145(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB146(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB147(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB148(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB149(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB150(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB151(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB152(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB153(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB154(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB155(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB156(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB157(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB158(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB159(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB160(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB161(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB162(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB163(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB164(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB165(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB166(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB167(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB168(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB169(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB170(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB171(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB172(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB173(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB174(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB175(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB176(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB177(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB178(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB179(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB180(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB181(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB182(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB183(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB184(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB185(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB186(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB187(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB188(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB189(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB190(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB191(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB192(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB193(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB194(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB195(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB196(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }
%resource reB197(a:int1, b:int2, c:str, d:int8, e:byte1, f:byte2, g:byte2, a1:int1, b1:int2, c1:int4, d1:int8, e1:byte1, f1:byte2, g1:byte2,a2:int1, b2:int2, c2:int4, d2:int8, e2:byte1, f2:byte2, g2:byte2,a3:int1, b3:int2, c3:int4, d3:int8, e3:byte1, f3:byte2, g3:byte2,a4:int1, b4:int2, c4:int4, d4:int8 -> e4:int4) { sequential(max_size(1000000)) }

reB0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB0(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB21(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB21(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB22(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB22(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB23(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB23(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB24(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB24(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB25(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB25(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB26(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB26(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB27(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB27(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB28(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB28(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB29(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB29(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB30(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB30(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB31(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB31(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB32(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB32(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB33(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB33(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB34(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB34(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB35(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB35(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB36(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB36(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB37(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB37(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB38(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB38(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB39(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB39(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB40(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB40(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB41(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB41(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB42(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB42(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB43(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB43(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB44(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB44(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB45(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB45(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB46(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB46(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB47(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB47(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB48(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB48(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB49(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB49(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB50(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB50(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB51(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB51(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB52(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB52(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB53(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB53(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB54(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB54(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB55(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB55(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB56(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB56(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB57(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB57(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB58(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB58(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB59(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB59(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB60(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB60(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB61(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB61(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB62(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB62(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB63(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB63(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB64(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB64(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB65(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB65(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB66(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB66(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB67(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB67(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB68(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB68(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB69(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB69(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB70(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB70(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB71(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB71(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB72(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB72(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB73(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB73(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB74(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB74(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB75(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB75(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB76(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB76(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB77(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB77(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB78(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB78(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB79(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB79(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB80(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB80(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB81(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB81(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB82(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB82(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB83(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB83(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB84(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB84(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB85(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB85(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB86(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB86(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB87(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB87(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB88(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB88(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB89(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB89(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB90(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB90(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB91(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB91(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB92(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB92(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB93(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB93(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB94(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB94(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB95(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB95(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB96(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB96(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB97(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB97(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB98(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB98(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB99(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB99(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).

reB197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-) :- C(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-, -).
B(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1) :- reB197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,d4,e4).
}
