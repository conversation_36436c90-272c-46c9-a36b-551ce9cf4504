/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: reliability07.cpp
 * Description: datalog new sub scribe
 * Author: youwanyong ywx1157510
 * Create: 2024-10-15
 */
// DataLog_reliability_007_016 降级需完善
// GMC_ALARM_SUB_CONN_RING 触发条件不满足？
#include "UniversalTools.h"
#include "t_datacom_lite.h"
SnUserDataT *userData = {0};
// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024 * 1024)
#define MINBYTE (1024 * 1024)
#endif

using namespace std;

class reliability07 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
};

void reliability07::SetUp()
{}
void reliability07::TearDown()
{}

/* ****************************************************************************
 Description  :001.datalog 创建新订阅，订阅过程中时钟往前跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#ifndef ENV_RTOSV2X
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,30,40\"");
#endif
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往前跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithJumpLeft);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :002.datalog 创建新订阅，订阅过程中时钟往后跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithJumpRight);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :003.datalog 创建新订阅，订阅过程中进程死循环（挂死），服务恢复，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_INTERNAL_ERROR);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithHung);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    // 服务挂死后环境清理再次拉起服务
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    FreeAllAllocMem(&dlrAndSubData);
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);

    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithHung);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dlrAndSubData.userData->insertNum = 0;

    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 客户端订阅进程异常退出
void *Client004Exce(void *args)
{
    system("./rel07_004");
}

// 客户端订阅进程
void *Client004(void *args)
{
    int32_t ret = system("./rel07_004");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

// 推送过程中 kill 客户端
void *PkillClient(void *args)
{
    //
    int32_t ret = 0;
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat rel07_004.txt");
        ret = executeCommand(g_command, "No such file or directory");
        if (!ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    } while (!ret);
    ret = system("pkill rel07_004");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(10);
}
/* ****************************************************************************
 Description  :004.datalog 创建新订阅，订阅过程中kill客户端进程，再次拉起，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_004)
{
    AW_FUN_Log(LOG_STEP, "test begin.");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("rm -rf ./rel07_004.txt");
    // 多线程反复拉起客户端
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    // 先开启pkill 进程
    int32_t ret = pthread_create(&client_thr[0], NULL, PkillClient, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 拉起客户端进程
    ret = pthread_create(&client_thr[1], NULL, Client004Exce, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次拉起客户端进程
    ret = pthread_create(&client_thr[2], NULL, Client004, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :005.datalog 创建新订阅，订阅过程中挂起服务端，再次拉起，订阅正常
 当前二次写数据失败，core
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "创建新订阅，订阅过程中挂起服务端，再次拉起，订阅正常.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr19);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(60);
    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(500, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :006.datalog 创建新订阅，订阅过程中服务端反复异常退出100次，再次恢复，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t cycle = 100;
    while (cycle > 0) {
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
        AW_CHECK_LOG_BEGIN();
        AddWhiteList(GMERR_UNDEFINED_OBJECT);
        AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
        AddWhiteList(GMERR_INTERNAL_ERROR);
        AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
        GmcSignalRegisterNotify();
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        char soName1[FILE_PATH] = "reliability07_001";
        GmcStmtT *stmt;
        GmcConnT *conn;
        AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
        system("mkdir -p /root/_datalog_/");
        system("chmod 777 /root/_datalog_/");
        AW_FUN_Log(LOG_STEP, "1.加载so.");
        // // 创建外部表
        char tableName[10] = "N000";
        char dlrTable[10] = "dlrTable";

        char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
        const char *configJson =
            "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
        CreateTestTable(lableJsonpath, configJson, tableName);
        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 加载hung，so
        ret = LoadSoFile((char *)"serverHuang");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_connSync, &g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /*创建状态合并订阅*/
        AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
        ret = GetSubJson(tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DIR&&Sub
        DlrAndSubT dlrAndSubData;
        ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_callBackTime = 0;
        // N000
        ret = createSubscriptionAndDlr(
            g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithHung);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启多个线程写数据
        AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
        int32_t thread_num = 2;
        pthread_t client_thr[thread_num];
        PthreadControlT value1 = {0, 500};
        ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(client_thr[0], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(5);
        // 服务挂死后环境清理再次拉起服务
        ret = testGmcDisconnect(g_connSub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_CHECK_LOG_END();
        FreeAllAllocMem(&dlrAndSubData);
        ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        system("sh ${TEST_HOME}/tools/start.sh");
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSignalRegisterNotify();
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
        AW_CHECK_LOG_BEGIN();
        AddWhiteList(GMERR_UNDEFINED_OBJECT);

        CreateTestTable(lableJsonpath, configJson, tableName);
        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 加载hung，so
        ret = LoadSoFile((char *)"serverHuang");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_connSync, &g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // N000
        ret = createSubscriptionAndDlr(
            g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithHung);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        dlrAndSubData.userData->insertNum = 0;

        ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(client_thr[0], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(5);

        PthreadControlT value2 = {500, 500};
        ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(client_thr[1], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = cancelSubscription(g_stmtSync, g_subName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 数据重演
        // 建重演表
        AW_FUN_Log(LOG_STEP, "4.数据重演.");
        char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
        const char *configJson1 =
            "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
        CreateTestTable(lableJsonpath1, configJson1, dlrTable);
        dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int recordNum = 1000;
        char strT[10] = {0};
        NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        ret = SetArrayValue(objIn1, strT, recordNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 读重演表数据
        ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
        ret = TestUninstallDatalog(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTestTable(tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTestTable(dlrTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        FreeAllAllocMem(&dlrAndSubData);
        ToFreeAlloc(objIn1, recordNum);
        system("rm -rf /root/_datalog_/");

        sleep(2);
        AW_CHECK_LOG_END();
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        if (cycle % 10 == 0) {
            AW_FUN_Log(LOG_STEP, "cycle is %d Now.", cycle);
        }
        cycle--;
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :007.datalog 创建新订阅，订阅过程中服务端收到coredump信号异常退出，再次恢复，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_INTERNAL_ERROR);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithCoredump);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    // 服务挂死后环境清理再次拉起服务
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    FreeAllAllocMem(&dlrAndSubData);
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);

    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithCoredump);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dlrAndSubData.userData->insertNum = 0;

    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :008.datalog 创建新订阅，订阅过程中服务端收到terminate信号异常退出，再次恢复，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_INTERNAL_ERROR);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithTerminate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    // 服务挂死后环境清理再次拉起服务
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    FreeAllAllocMem(&dlrAndSubData);
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();

    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);

    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithTerminate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    dlrAndSubData.userData->insertNum = 0;

    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :009.datalog 创建新订阅，订阅过程中服务端收到ignore信号，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_CONNECTION_RESET_BY_PEER);
    AddWhiteList(GMERR_INTERNAL_ERROR);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅过程中时钟往后跳变.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载hung，so
    ret = LoadSoFile((char *)"serverHuang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlrWithIgnore);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :010.datalog 加载满字段，输入表）满索引so， 创建新订阅，订阅正常
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 加载满字段，输入表）满索引so， 创建新订阅，订阅正常");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertSixtyFouthValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertSixtyFouthValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable2.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    SixtyFouthTableStruct *objIn1 = (SixtyFouthTableStruct *)malloc(sizeof(SixtyFouthTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetSixtyFouthArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, SixtyFouthTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeSixtyFouthAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :011.表的个数超系统限制
 1.向两个namespace中加载满规格so，so内为最大表数量，再加载so3失败，卸载so1，加载so3成功
2.对so3内表创建新订阅、新增、修改、删除数据、取消订阅，都成功
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    char soName2[FILE_PATH] = "reliability07_1000table_001";
    char soName3[FILE_PATH] = "reliability07_1000table_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 表的个数超系统限制.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    ret = LoadSoFile(soName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {200, 500};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value3 = {0, 200};
    ret = pthread_create(&client_thr[2], NULL, DeleteValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 800);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(300, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(30);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :012.超大规格数据写
 1.循环向两个namespace中加载满规格so，so内为最大表数量，再加载so3失败，卸载so1，加载so3成功
2.对so3内表创建新订阅、新增、修改、删除数据、取消订阅，都成功
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    char soName2[FILE_PATH] = "reliability07_1000table_001";
    char soName3[FILE_PATH] = "reliability07_1000table_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 表的个数超系统限制.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    int32_t cycle = 10;
    while (cycle > 0) {
        CreateTestTable(lableJsonpath, configJson, tableName);

        ret = LoadSoFile(soName3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建namespace2 加载so
        ret = testGmcConnect(&g_connSync, &g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char nameSpaceName[] = "DataLog_reliability_007_012_namespace";
        ret = GmcCreateNamespace(g_stmtSync, nameSpaceName, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = LoadSoFile(soName2, false, nameSpaceName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        ret = TestUninstallDatalog(soName3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /*创建状态合并订阅*/
        AW_FUN_Log(LOG_STEP, "2.cycle %d 创建外部表状态合并订阅.", cycle);
        ret = GetSubJson(tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // DIR&&Sub
        DlrAndSubT dlrAndSubData;
        ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        g_callBackTime = 0;
        // N000
        ret = createSubscriptionAndDlr(
            g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启多个线程写数据
        AW_FUN_Log(LOG_STEP, "3.cycle %d 多线程不同线程写不同主键数据.", cycle);
        int32_t thread_num = 3;
        pthread_t client_thr[thread_num];
        PthreadControlT value1 = {0, 500};
        ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(client_thr[0], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(10);

        PthreadControlT value2 = {200, 500};
        ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        PthreadControlT value3 = {0, 200};
        ret = pthread_create(&client_thr[2], NULL, DeleteValueData, &value3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = pthread_join(client_thr[2], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = pthread_join(client_thr[1], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 800);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = cancelSubscription(g_stmtSync, g_subName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 数据重演
        // 建重演表
        AW_FUN_Log(LOG_STEP, "4.cycle %d 数据重演.", cycle);
        char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
        const char *configJson1 =
            "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
        CreateTestTable(lableJsonpath1, configJson1, dlrTable);
        dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int recordNum = 1000;
        char strT[10] = {0};
        NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        ret = SetArrayValue(objIn1, strT, recordNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 读重演表数据
        ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
        AW_MACRO_EXPECT_EQ_INT(300, ret);
        ret = TestUninstallDatalog(soName2, nameSpaceName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropNamespace(g_stmtSync, nameSpaceName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_STEP, "5.cycle d% 卸载datalog so.", cycle);
        (void)TestUninstallDatalog(soName1);
        ret = DropTestTable(tableName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = DropTestTable(dlrTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        FreeAllAllocMem(&dlrAndSubData);
        ToFreeAlloc(objIn1, recordNum);
        cycle--;
    }
    system("rm -rf /root/_datalog_/");
    sleep(5);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :013.异步消息队列写满
1.启动客户端加载so，，创建新订阅，异步DML操作，正常的生产、不消费（发送异步请求消息，不接受响应消息）
2.客户端开始处理正常，后面报队列满错误码，告警激活（GMC_ALARM_ASYNC_CONN_RING）
3.恢复消费后继续发送请求，不会报错，可以正常收到推送消息
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_002";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅,异步消息队列满.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 3000;
    char strT[10] = {0};
    SixtyFouthTableStruct *objIn1 = (SixtyFouthTableStruct *)malloc(sizeof(SixtyFouthTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetSixtyFouthArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.异步写数据.");
    YangConnOptionT connOptions = {0};
    connOptions.msgQueueSize = 64;
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
    AsyncUserDataT dataRev = {0};
    ret = TestYangGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespaceAsync(asyncStmt, g_testNameSpace, use_namespace_callback, &dataRev);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&dataRev);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, dataRev.status);

    g_isNeedSleep = true;
    ret = writeRecordAsyncWithOutWait(
        asyncConn, asyncStmt, "inp", objIn1, recordNum, SixtyFouthTableSet, &dataRev, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
    g_isNeedSleep = false;
    sleep(20);
    system("gmsysview count inp");

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcAlarmIdE alarmId = GMC_ALARM_ASYNC_CONN_RING;
    GmcAlarmDataT alarmData = {};
    ret = GmcGetAlarmData(g_stmtSync, alarmId, &alarmData);
#ifdef ENV_RTOSV2X
    EXPECT_EQ(GMERR_OK, ret);
#else
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
#endif

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演%d.", g_dircount);
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, g_dircount, SixtyFouthTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isNeedSleep = false;
    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeSixtyFouthAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");
    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :014.订阅消息队列写满
1.启动客户端，加载so，创建新订阅，正常的生产、不消费（持续进行DML操作触发订阅，不消费订阅消息）
2.客户端处理正常，队列满后会报错，告警激活（GMC_ALARM_SUB_MSG_POOL，GMC_ALARM_SUB_CONN_RING）
3.恢复消费后继续进行DML操作，校验收到的订阅消息不合并时发送的消息等接收到的相等
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
           "\"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_SUB_PUSH_QUEUE_FULL);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    // 外部表订阅流控能够生效
    AddWhiteList(GMERR_COMMON_STREAM_OVERLOAD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_003";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅,订阅消息队列满.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // out
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(stmt, g_connSub1, (char *)"datalog_file/schema_file/pubsub.gmjson", &userData, 1000,
        g_subName1, snCallback, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 970;
    char strT[10] = {0};
    SixtyFouthTableStruct *objIn1 = (SixtyFouthTableStruct *)malloc(sizeof(SixtyFouthTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetSixtyFouthArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.写数据.");
    g_isFailed = true;
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, SixtyFouthTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);

    // 预期触发告警  GMC_ALARM_SUB_CONN_RING  GMC_ALARM_SUB_MSG_POOL
    // 实时生效
    GmcAlarmIdE alarmId1 = GMC_ALARM_SUB_CONN_RING;
    GmcAlarmDataT alarmData = {};
    ret = GmcGetAlarmData(g_stmtSync, alarmId1, &alarmData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, alarmData.alarmStatus);

    // hpe生效
    GmcAlarmIdE alarmId2 = GMC_ALARM_SUB_MSG_POOL;
    ret = GmcGetAlarmData(g_stmtSync, alarmId2, &alarmData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    g_isFailed = false;
    sleep(20);
    system("gmsysview count inp");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演%d.", 0);
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, SixtyFouthTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isNeedSleep = false;
    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeSixtyFouthAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :015.订阅消息队列写满
升级重做过程中，订阅消息队列满，升级失败（block1）
1.加载so
2.升级so，含多个订阅端构造重做时推送大量数据，回调中减缓接收速度，重做失败，日志16006，
3.取消订阅，卸载so后再重新加载升级so，写入数据，降级so，触发推送，预期推送失败16006，升级失败
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
           "\"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_SUB_PUSH_QUEUE_FULL);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    char soName1[FILE_PATH] = "reliability07_003";
    char nsName2[128] = "reliability07_003_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅,订阅消息队列满.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // out
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(stmt, g_connSub1, (char *)"datalog_file/schema_file/pubsub.gmjson", &userData, 1000,
        g_subName1, snCallback, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 970;
    char strT[10] = {0};
    SixtyFouthTableStruct *objIn1 = (SixtyFouthTableStruct *)malloc(sizeof(SixtyFouthTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetSixtyFouthArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "3.写数据.");
    g_isFailed = false;
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, SixtyFouthTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isFailed = true;
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据失败");

    // 预期触发告警  GMC_ALARM_SUB_CONN_RING  GMC_ALARM_SUB_MSG_POOL
    // 实时生效
    GmcAlarmIdE alarmId1 = GMC_ALARM_SUB_CONN_RING;
    GmcAlarmDataT alarmData = {};
    ret = GmcGetAlarmData(g_stmtSync, alarmId1, &alarmData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, alarmData.alarmStatus);

    // hpe生效
    GmcAlarmIdE alarmId2 = GMC_ALARM_SUB_MSG_POOL;
    ret = GmcGetAlarmData(g_stmtSync, alarmId2, &alarmData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    g_isFailed = false;
    sleep(20);
    system("gmsysview count inp");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演%d.", 0);
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, SixtyFouthTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isNeedSleep = false;
    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeSixtyFouthAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :016.订阅消息队列写满
升级，订阅消息队列满，写数据失败（block1，redo off）含tbm升级
1.加载so1
2.升级so，含多个订阅端构造重做时推送大量数据，回调中减缓接收速度，重做失败，日志16006，
3.取消订阅，卸载so后再重新加载升级so，写入数据，降级so，触发推送，预期推送失败16006，升级失败
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
           "\"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_SUB_PUSH_QUEUE_FULL);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    // 外部表订阅流控能够生效
    AddWhiteList(GMERR_COMMON_STREAM_OVERLOAD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    char soName1[FILE_PATH] = "reliability07_004";
    char nsName2[128] = "reliability07_004_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅,订阅消息队列满.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table002.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // out
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(stmt, g_connSub1, (char *)"datalog_file/schema_file/pubsub.gmjson", &userData, 1000,
        g_subName1, snCallback, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 970;
    char strT[10] = {0};
    SixtyFouthTableStruct *objIn1 = (SixtyFouthTableStruct *)malloc(sizeof(SixtyFouthTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetSixtyFouthArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "升级成功");
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    g_isFailed = true;
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, SixtyFouthTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
    g_isFailed = false;

    // 预期触发告警  GMC_ALARM_SUB_CONN_RING  GMC_ALARM_SUB_MSG_POOL
    // 实时生效
    GmcAlarmIdE alarmId1 = GMC_ALARM_SUB_CONN_RING;
    GmcAlarmDataT alarmData = {};
    ret = GmcGetAlarmData(g_stmtSync, alarmId1, &alarmData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, alarmData.alarmStatus);

    // hpe生效
    GmcAlarmIdE alarmId2 = GMC_ALARM_SUB_MSG_POOL;
    ret = GmcGetAlarmData(g_stmtSync, alarmId2, &alarmData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    g_isFailed = false;
    sleep(20);
    system("gmsysview count inp");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演%d.", 0);
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, SixtyFouthTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_isNeedSleep = false;
    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeSixtyFouthAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :017.事务锁资源耗尽
1.加载so，创建新订阅
2.显示开启事务，循环进行100W+DML操作（当前事务锁总个数5000+，并且会升级，很难耗尽）
3.提交事务，校验收到的订阅消息数量正确
DLR不支持直连写
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 增加日志白名单（DTS2024110819976）
    AW_ADD_ERR_WHITE_LIST(1, "DynCtx reaches mem peak. CtxName: sessionMemCtx");
    g_subNeedMalloc = 100;

    char soName1[FILE_PATH] = "reliability07_005";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，事务锁资源耗尽.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table017.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 连接设置长事务监控60,360
    YangConnOptionT connOptionT;
    connOptionT.logThreshold = 60;
    connOptionT.rollBackThreshold = 360;
    // 建连
    ret = TestYangGmcConnect(&g_connSync, &g_stmtSync, GMC_CONN_TYPE_SYNC, &connOptionT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithNoDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "订阅后查数据为空.");

    int recordNum = 1000000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启fastpath写数据和对datalog输入表写数据
    // 悲观读已提交事务配置
    AW_FUN_Log(LOG_STEP, "开始事务写数据.");
    GmcTxConfigT prtrxConfig;
    prtrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    prtrxConfig.type = GMC_TX_ISOLATION_COMMITTED;
    prtrxConfig.readOnly = false;
    prtrxConfig.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(g_connSync, &prtrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "N000", objIn1 + 1000, recordNum - 1000, NewAlLTypeTableSet, false, false,
        GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 800};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTransCommit(g_connSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "事务提交.");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, recordNum - 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(30);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 读
    AW_FUN_Log(LOG_STEP, "读数据.");
    ret = readRecord("N000", objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(recordNum - 1000, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :018.资源池耗尽
1.加载so，创建新订阅
2.循环进行DML操作，直到资源池满，返回失败
3.DML操作执行成功的数据可以正常收到订阅消息，资源池满后，不再收到订阅消息。
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RESOURCE_POOL_NOT_ENOUGH);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_018";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，资源池耗尽.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table018.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 100};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再写一条数据失败
    PthreadControlT value2 = {100, 1};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable3.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(recordNum, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    sleep(5);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :019.表空间满
1.加载so,创建新订阅，客户端创建10张表并写入数据到表空间满，收到的订阅消息数量正确。
2.删除部分数据，收到的订阅消息数量正确。
3.继续写入数据，写入成功，收到的订阅消息数量正确。
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=100\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_019";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，表空间满.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table018.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_subNeedMalloc = 10;

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 50000};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 10000, 50000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    // 删除数据
    PthreadControlT value2 = {0, 1000};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 1000, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    value1.num = 100;
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 10000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :020.客户端epoll线程挂死
1.客户端循环加载so创建新订阅、新增、修改、删除数据
2.客户端epoll线程挂死(回调sleep)
3.无法继续收到订阅消息
4.客户端epoll线程恢复
5.恢复后，DML操作正常进行，正常接收订阅消息
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 客户端epoll线程挂死.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_blockEpoll = true;

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 3;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {200, 500};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value3 = {0, 200};
    ret = pthread_create(&client_thr[2], NULL, DeleteValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    sleep(60);
    system("gmsysview count");

    g_blockEpoll = false;
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_create(&client_thr[2], NULL, DeleteValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    // 确保过期表正常过期
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 800, 1600);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 500, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(300, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :021.在线修改系统配置为异常值
1.客户端加载so，循环创建新订阅、新增、修改、删除数据
2.在线修改系统配置为异常值，修改返回失败
3.DML操作正常进行，正常接收订阅消息
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_DATA_EXCEPTION);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_001";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 在线修改系统配置为异常值.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table001.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);

    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 4;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    ret = pthread_create(&client_thr[3], NULL, ChangeCfg, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value2 = {200, 500};
    ret = pthread_create(&client_thr[1], NULL, UpdateValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value3 = {0, 200};
    ret = pthread_create(&client_thr[2], NULL, DeleteValueData, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[2], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[3], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 800);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(300, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :022.消费过载流控
1.加载so，对多张表都创建新订阅
2.多线程进行DML操作，触发流控
3.订阅消息不能及时收到，订阅通道满
过载流控请求处理1s一条
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    if (g_envType != 0) {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\" "
               "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
               "\"overloadThreshold=cpu:20,30,30,40,40,60;dynamicMemory:2,3,3,4,4,6;shareMemory:20,30,40,50,80,90;"
               "subscribeQueue:2,"
               "3,3,4,4,6\" "
               "\"maxSeMem=192\" \"maxSysShmSize=32\" \"maxSysDynSize=170\"");
    } else {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=256\" \"maxTotalDynSize=512\" "
               "\"clientServerFlowControl=1;1;1;1\" \"flowControlSleepTime=0,0,0\" "
               "\"overloadThreshold=cpu:20,30,30,40,40,60;dynamicMemory:2,3,3,4,4,6;shareMemory:20,30,40,50,80,90;"
               "subscribeQueue:2,"
               "3,3,4,4,6\" "
               "\"maxSeMem=192\" \"maxSysShmSize=32\" \"maxSysDynSize=170\"");
    }
    system("sh $TEST_HOME/tools/modifyCfg.sh \"flowControlSleepTime=100,200,1000\" ");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_COMMON_STREAM_OVERLOAD);
    AddWhiteList(GMERR_NO_DATA);

    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_022";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，消费过载流控.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table018.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_subNeedMalloc = 10;
    g_blockEpoll = true;

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 20000};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDbFlowCtrlLevelE level;
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");

    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }
    // 不再阻塞订阅
    g_blockEpoll = false;
    // 触发流控条数
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待状态合并推送完毕
    WaitDatalogNewSubEnd(tableName);

    // 再次写入数据刷新流控等级
    value1.index = 20000;
    value1.num = 20001;
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");

    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");

    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");
    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }

    system("gmsysview count");
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");
    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }

    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetConnFlowCtrlLevel(g_connSync, &level);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "3.Failed to get current flow control level.");
    } else {
        AW_FUN_Log(LOG_STEP, "3.get current flow control level is %d.", level);
    }
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");
    value1.num = 1;
    value1.index = 0;
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    PthreadControlT value2 = {0, 1};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :023.订阅关系满
1.加载so，写入数据
2.创建多个订阅连接订阅多张表，升级订阅表相关topo订阅接收
3.写入数据，订阅接收，降级so，订阅接收，写入数据
4.读数据，卸载so
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_023";
    char nsName2[128] = "reliability07_023_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_023_rollbackV2.so";

    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅关系满，订阅多张表并进行升降级后DML.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";
    char tableName2[10] = "N002";
    char tableName3[10] = "N003";
    char tableName4[10] = "N004";
    char tableName5[10] = "N005";

    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table00S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);
    CreateTestTable(lableJsonpath, configJson, tableName2, true);
    CreateTestTable(lableJsonpath, configJson, tableName3, true);
    CreateTestTable(lableJsonpath, configJson, tableName4, true);
    CreateTestTable(lableJsonpath, configJson, tableName5, true);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub2, NULL, 1, g_epoll_reg_info, g_subConnName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub3, NULL, 1, g_epoll_reg_info, g_subConnName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub4, NULL, 1, g_epoll_reg_info, g_subConnName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub5, NULL, 1, g_epoll_reg_info, g_subConnName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    // N000
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N001
    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub1, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N002
    ret = GetSubJson(tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData2;
    ret = GetMemWithDlrAndSub(&dlrAndSubData2, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub2, (char *)g_subJson, &dlrAndSubData2, 1000, g_subName2, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N003
    ret = GetSubJson(tableName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData3;
    ret = GetMemWithDlrAndSub(&dlrAndSubData3, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub3, (char *)g_subJson, &dlrAndSubData3, 1000, g_subName3, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N004
    ret = GetSubJson(tableName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData4;
    ret = GetMemWithDlrAndSub(&dlrAndSubData4, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub4, (char *)g_subJson, &dlrAndSubData4, 1000, g_subName4, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N004
    ret = GetSubJson(tableName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DlrAndSubT dlrAndSubData5;
    ret = GetMemWithDlrAndSub(&dlrAndSubData5, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub5, (char *)g_subJson, &dlrAndSubData5, 1000, g_subName5, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    int32_t thread_num = 1;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 100};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData3.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData4.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData5.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级
    AW_FUN_Log(LOG_STEP, "升级.");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData3.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData4.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData5.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除部分数据
    AW_FUN_Log(LOG_STEP, "3.删除部分数据.");
    value1.index = 50;
    value1.num = 50;
    ret = pthread_create(&client_thr[0], NULL, DeleteValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData3.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData4.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData5.userData, GMC_SUB_EVENT_DELETE, 50);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 降级
    AW_FUN_Log(LOG_STEP, "降级.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "写数据.");
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData2.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData3.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData4.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData5.userData, GMC_SUB_EVENT_MODIFY, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int recordNum = 100;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读数据
    AW_FUN_Log(LOG_STEP, "读数据.");
    ret = readRecord(tableName, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(tableName1, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableName2, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableName3, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableName4, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(tableName5, objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    FreeAllAllocMem(&dlrAndSubData2);
    FreeAllAllocMem(&dlrAndSubData3);
    FreeAllAllocMem(&dlrAndSubData4);
    FreeAllAllocMem(&dlrAndSubData5);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :024.订阅关系满
1.加载so，创建1024个新订阅, 订阅1024个表
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_024_001";
    char soName2[FILE_PATH] = "reliability07_024_002";

    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，订阅关系满，创建1024个新订阅, 订阅1024个表");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // // 创建外部表
    char tableName[20] = "N000";
    char subName[40] = "subVertexLabelYWY";
    int32_t num[3] = {0, 0, 0};
    int32_t cycle = 2;
    while (cycle > 0) {
        for (int32_t i = 0; i < 999; i++) {
            if (i >= 100) {
                num[0] = i / 100;
                num[1] = i / 10 % 10;
                num[2] = i % 10;
            } else if (i < 100 && i >= 10) {
                num[0] = 0;
                num[1] = i / 10;
                num[2] = i % 10;
            } else {
                num[0] = 0;
                num[1] = 0;
                num[2] = i;
            }

            sprintf(tableName, "ns%dN%d%d%d", cycle, num[0], num[1], num[2]);
            char lableJsonpath[100] = "./datalog_file/schema_file/table00S.gmjson";
            const char *configJson =
                "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
            CreateTestTable(lableJsonpath, configJson, tableName, true);
            if (i < 512) {
                ret = GetSubJson(tableName);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char subName[40] = "subVertexLabelYWY";
                sprintf(subName, "subVertexLabelYWY%dN%d%d%d", cycle, num[0], num[1], num[2]);
                ret = createSubscriptionAndDlr(
                    g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, subName, snCallbackWhithDlr);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
        cycle--;
    }
    AW_FUN_Log(LOG_STEP, "加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadSoFile(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    cycle = 2;
    AW_FUN_Log(LOG_STEP, "取消订阅.");
    while (cycle > 0) {
        for (int32_t i = 0; i < 512; i++) {
            if (i >= 100) {
                num[0] = i / 100;
                num[1] = i / 10 % 10;
                num[2] = i % 10;
            } else if (i < 100 && i >= 10) {
                num[0] = 0;
                num[1] = i / 10;
                num[2] = i % 10;
            } else {
                num[0] = 0;
                num[1] = 0;
                num[2] = i;
            }

            sprintf(subName, "subVertexLabelYWY%dN%d%d%d", cycle, num[0], num[1], num[2]);
            ret = cancelSubscription(g_stmtSync, subName);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        cycle--;
    }

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "删表.");
    while (cycle > 0) {
        for (int32_t i = 0; i < 999; i++) {
            if (i >= 100) {
                num[0] = i / 100;
                num[1] = i / 10 % 10;
                num[2] = i % 10;
            } else if (i < 100 && i >= 10) {
                num[0] = 0;
                num[1] = i / 10;
                num[2] = i % 10;
            } else {
                num[0] = 0;
                num[1] = 0;
                num[2] = i;
            }
            sprintf(tableName, "ns%dN%d%d%d", cycle, num[0], num[1], num[2]);
            ret = DropTestTable(tableName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    FreeAllAllocMem(&dlrAndSubData);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :025.订阅关系满
1.20个dml线程, 20个新订阅, 订阅同一张外部表
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_025";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，20个dml线程, 20个新订阅, 订阅同一张外部表");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char lableJsonpath[100] = "./datalog_file/schema_file/table00S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // N000
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t thread_num = 22;
#ifdef ENV_RTOSV2X
    thread_num = 5;
#endif
    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 100};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value2 = {50, 50};
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 0; i < 2; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    int32_t value[20];
    for (int32_t i = 2; i < thread_num; i++) {
        value[i] = i;
        ret = pthread_create(&client_thr[i], NULL, SubWait, &value[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int32_t i = 2; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    int recordNum = 50;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 读数据
    AW_FUN_Log(LOG_STEP, "读数据.");
    ret = readRecord("N000", objIn1, recordNum, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable("N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :026.系统动态内存不足
加载so，创建新订阅，系统动态内内存不足订阅推送反压验证订阅是否推送回滚信息
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_026";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，系统动态内内存不足订阅推送反压.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table017.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ../../reliability_test/reliability_scene_sn.sh CpuOverload");

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放内存
    system("sh ../../reliability_test/reliability_scene_sn.sh CleanCpuOverload");

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  :027.系统共享内存不足
加载so，创建新订阅，系统动态内内存不足订阅推送反压验证订阅是否推送回滚信息
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char soName1[FILE_PATH] = "reliability07_026";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，系统动态内内存不足订阅推送反压.");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table017.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 占用系统内存
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 2;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0;  // 实际申请内存次数
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }
    AW_FUN_Log(LOG_INFO, "actual alloc time is %d", count);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 500};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {500, 500};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放消耗掉的内存
    AW_FUN_Log(LOG_STEP, "free -h");
    system("free -h");
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :028.加载so,构造新订阅，写数据失败，验证订阅推送是否回滚
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_028";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，写数据失败，验证订阅推送是否回滚.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table028.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 258};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    PthreadControlT value2 = {0, 257};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 257);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 257, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :029.加载so,构造新订阅，升级成功写数据失败，验证订阅推送是否回滚
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_028";
    char nsName2[128] = "reliability07_028_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，写数据失败，验证订阅推送是否回滚.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table028.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "升级.");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 258};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(10);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PthreadControlT value2 = {0, 257};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 257);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 257, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :030.加载so,构造新订阅，升级失败验证订阅推送是否回滚
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_030";
    char nsName2[128] = "reliability07_030_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，写数据失败，验证订阅推送是否回滚.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table028.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 258};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "升级失败.");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 257);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 257);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 数据重演
    // // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, 258, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :031.加载so,新订阅 未取消订阅，卸载so失败，再次写入数据正常，取消订阅，卸载so正常
 DTS2024102817688
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_030";
    char nsName2[128] = "reliability07_030_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，未取消订阅，卸载so失败.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N000tbm";

    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table028.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 258};
    ret = pthread_create(&client_thr[0], NULL, InsertValueData, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[0], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 未取消订阅卸载失败
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 数据重演
    // // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 0, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :032.加载so,创建新订阅，fastpath 写数据 触发订阅推送，
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AddWhiteList(GMERR_RESTRICT_VIOLATION);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_032";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "datalog 创建新订阅，fastpath 写数据 触发订阅推送，.");
    system("mkdir -p /root/_datalog_/");
    system("chmod 777 /root/_datalog_/");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N000tbm";

    char dlrTable[10] = "dlrTable";

    char lableJsonpath[100] = "./datalog_file/schema_file/table028.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName);
    ret = LoadSoFile(soName1, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 1000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 1000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 1000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启多个线程写数据
    AW_FUN_Log(LOG_STEP, "3.多线程不同线程写不同主键数据.");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    PthreadControlT value1 = {0, 128};
    // datalog
    PthreadControlT value2 = {128, 127};
    ret = pthread_create(&client_thr[1], NULL, InsertValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // fastPath
    ret = writeRecord(g_connSync, g_stmtSync, "N000", objIn1, 128, NewAlLTypeTableSet, false, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // datalog
    ret = pthread_create(&client_thr[1], NULL, DeleteValueData, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_join(client_thr[1], NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 255);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 127);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 127);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 127);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 数据重演
    // // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 128, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1 + 128, 127, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("rm -rf /root/_datalog_/");

    AW_FUN_Log(LOG_STEP, "test end.");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  :033.允许升级时(含block1）跨topo的join，升级重做过程中
 ，内存不足升级失败，服务正常，数据回滚
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "允许升级时(含block1）跨topo的join，升级重做过程中");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(
        g_connSync, g_stmtSync, "N000", objIn1 + 1000, 1000, NewAlLTypeTableSet, false, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num + 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    // 升级过程中写入数据内存不足，写入失败
    AW_FUN_Log(LOG_STEP, "升级过程中写入数据内存不足，写入失败.");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 数据重演
    // // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, num + 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1 + num, num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :034.允许升级时(含block1，输入表为过期表）跨topo的join，
 升级后，写入数据内存满，删除数据，写入数据成功
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "允许升级时(含block1）跨topo的join，升级重做过程中");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(
        g_connSync, g_stmtSync, "N000", objIn1 + 1000, 1000, NewAlLTypeTableSet, false, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num + 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    // 删除数据
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + num / 2, num / 2 + 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1 + num, num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :035.允许升级时(含block1，输入表为过期表）跨topo的join，
 升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    sleep(30);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :036.允许升级时(含block1，输入表为过期表）跨topo的join，
 升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    sleep(10);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :037.允许升级时(含block1，输入表为过期表）跨topo的join，
 降级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_033_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_033_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    // 降级
    AW_FUN_Log(LOG_STEP, "降级.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(30);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :038.允许升级时(含block1，输入表为过期表）跨topo的join，
 降级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_033_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_033_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    // 降级
    AW_FUN_Log(LOG_STEP, "降级.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 40000 - num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(30);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :039.允许升级时(含block1）跨topo的join，降级重做过程中
 ，内存不足升级失败，服务正常，数据回滚
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=100\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_033";
    char nsName2[128] = "reliability07_033_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_033_rollbackV2.so";

    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "允许升级时(含block1）跨topo的join，升级重做过程中");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_033_rule.d datalog_file/reliability07_033_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_033_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_033_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 200000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmsysview count");

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num = 190000;
    ret = writeRecord(
        g_connSync, g_stmtSync, "N001", objIn1 + 600, num, NewAlLTypeTableSet, false, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "降级过程中写入数据内存不足，写入失败.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 150000, 160000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // // 数据重演
    // // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 600 + 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, 600, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 600, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :040.允许升级时(含block1，redo off）跨topo的join，写满内存
 升级，内存不足升级失败，服务正常，数据回滚（redo off不会进行数据重做)
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=100\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，内存不足升级失败，服务正常，数据回滚");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 200000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, recordNum, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 78061);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    // 写入数据内存不足进行升级升级过程中
    AW_FUN_Log(LOG_STEP, "写入数据内存不足进行升级升级过程中.");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次删除数据
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 78061, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 78061);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 600);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, 1, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据
    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, num, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(1, ret);
    ret = readRecord("inp", objIn1, 1, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1 + num, num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :041.允许升级时(含block1，redo off，输入表为过期表）
 跨topo的join，升级后，写入数据内存满，删除数据，写入数据成功
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级后，写入数据内存满，删除数据，写入数据成功");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(
        g_connSync, g_stmtSync, "N000", objIn1 + 1000, 1000, NewAlLTypeTableSet, false, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num + 1000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    // 删除数据
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1 + num / 2, num / 2 + 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1 + num, num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :042.允许升级时(含block1，redo off， 输入表为过期表）跨topo的join，
 升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh -f");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, num * num / 2 + num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    sleep(30);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :043.允许升级时(含block1，redo off， 输入表为过期表）跨topo的join，
 升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh -f");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, num * num / 2 + num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num * num * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    sleep(30);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :044.允许升级时(含block1，redo off，输入表为过期表）跨topo的join
 ，降级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh -f");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_040_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_040_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    // 降级
    AW_FUN_Log(LOG_STEP, "降级.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    sleep(30);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :045.允许升级时(含block1，redo off，输入表为过期表）跨topo的join，
 降级重做过程中，时钟往前跳变,读数据，写入数据过程中进行时钟往后跳变
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_040_rollbackV2.so";
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "升级重做过程中，时钟往后跳变,读数据，写入数据过程中进行时钟往前跳变");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_040_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 2000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 200;
    // inp
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");

    // 升级
    AW_FUN_Log(LOG_STEP, "升级成功");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 进行时钟跳变
    char cmd[128] = "";
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(cmd);
    // 降级
    AW_FUN_Log(LOG_STEP, "降级.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(2);

    // 时钟跳变
    AW_FUN_Log(LOG_STEP, "时钟跳变");
    (void)sprintf(cmd, "%s %s &", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(cmd);
    AW_FUN_Log(LOG_STEP, "写inp1");
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "删除inp");
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1, num / 2, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, num / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入数据成功
    AW_FUN_Log(LOG_STEP, "再次写入inp.");
    ret = SetArrayValue(objIn1, strT, recordNum, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + num, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 300, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, num * 2, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp", objIn1, num / 2 + num, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);

    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :046.允许升级时(含block1，redo off）跨topo的join，
 降级后，内存不足升级失败，服务正常，数据回滚（redo off不会进行数据重做）
**************************************************************************** */
TEST_F(reliability07, DataLog_reliability_007_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=100\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    GmcSignalRegisterNotify();
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char soName1[FILE_PATH] = "reliability07_040";
    char nsName2[128] = "reliability07_040_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/reliability07_040_rollbackV2.so";

    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "允许升级时(含block1）跨topo的join，升级重做过程中");
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    // // 创建外部表
    char tableName[10] = "N000";
    char tableName1[10] = "N001";

    char dlrTable[10] = "dlrTable";
    g_subNeedMalloc = 20;

    char lableJsonpath[100] = "./datalog_file/schema_file/table033S.gmjson";
    const char *configJson = "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath, configJson, tableName, true);
    CreateTestTable(lableJsonpath, configJson, tableName1, true);

    // 编译生成so
    system("gmprecompiler -u datalog_file/reliability07_040_rule.d datalog_file/reliability07_040_patch.d -supupgerr");
    system("gcc ./plan_str.c -fPIC -shared -o datalog_file/reliability07_040_patchV2.so");
    system("gcc ./plan_str_rollback.c -fPIC -shared -o datalog_file/reliability07_040_rollbackV2.so");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmtSync, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*创建状态合并订阅*/
    AW_FUN_Log(LOG_STEP, "2.创建外部表状态合并订阅.");
    ret = GetSubJson(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData;
    ret = GetMemWithDlrAndSub(&dlrAndSubData, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N000
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData, 2000, g_subName, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GetSubJson(tableName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DIR&&Sub
    DlrAndSubT dlrAndSubData1;
    ret = GetMemWithDlrAndSub(&dlrAndSubData1, true, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_callBackTime = 0;
    // N001
    ret = createSubscriptionAndDlr(
        g_stmtSync, g_connSub, (char *)g_subJson, &dlrAndSubData1, 2000, g_subName1, snCallbackWhithDlr);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 200000;
    char strT[10] = {0};
    NewALLTypeTableStruct *objIn1 = (NewALLTypeTableStruct *)malloc(sizeof(NewALLTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    AW_FUN_Log(LOG_STEP, "写数据.");
    // inp1
    int32_t num = 600;
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, num);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmsysview count");

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num = 190000;
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + 600, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    system("gmsysview count");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "降级过程中写入数据内存不足，写入失败.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除数据，再次写入成功
    ret = SetArrayValue(objIn1, strT, recordNum, -1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_connSync, g_stmtSync, "inp1", objIn1 + 600, num, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(60);
    system("gmsysview count inp1");
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = SetArrayValue(objIn1, strT, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(g_connSync, g_stmtSync, "inp", objIn1 + 600, 600, NewAlLTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData.userData, GMC_SUB_EVENT_MODIFY, 600);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitStMgSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_MODIFY, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(dlrAndSubData1.userData, GMC_SUB_EVENT_DELETE, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = cancelSubscription(g_stmtSync, g_subName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 数据重演
    // 建重演表
    AW_FUN_Log(LOG_STEP, "4.数据重演.");
    char lableJsonpath1[100] = "./datalog_file/schema_file/dlrTable28.gmjson";
    const char *configJson1 =
        "{\"isFastReadUncommitted\":false, \"status_merge_sub\": true, \"data_sync_label\": true}";
    CreateTestTable(lableJsonpath1, configJson1, dlrTable);
    dataReplayIncre(g_connSync, g_stmtSync, dlrTable, dlrAndSubData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    // // 读重演表数据
    ret = readRecord(dlrTable, objIn1, 600 + 1000, NewTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(600, ret);
    ret = readRecord("inp", objIn1 + 600, 600, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("inp1", objIn1, 600, NewTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "5.卸载datalog so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropTestTable(dlrTable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FreeAllAllocMem(&dlrAndSubData);
    FreeAllAllocMem(&dlrAndSubData1);
    ToFreeAlloc(objIn1, recordNum);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    AW_CHECK_LOG_END();
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    AW_FUN_Log(LOG_STEP, "test end.");
}
