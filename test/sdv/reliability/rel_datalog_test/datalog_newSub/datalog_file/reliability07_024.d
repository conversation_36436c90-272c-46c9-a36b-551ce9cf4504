%version v0.0.0
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{
index(0(a,b,c)),update
}

// 外部表
%table N000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N001(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N002(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N003(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N004(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N005(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N006(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N007(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N008(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N009(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N010(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N001(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N002(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N003(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N004(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N005(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N006(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N007(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N008(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N009(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N011(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N012(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N013(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N014(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N015(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N016(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N017(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N018(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N019(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N020(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N011(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N012(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N013(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N014(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N015(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N016(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N017(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N018(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N019(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N021(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N022(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N023(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N024(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N025(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N026(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N027(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N028(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N029(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N021(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N022(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N023(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N024(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N025(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N026(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N027(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N028(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N029(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N031(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N032(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N033(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N034(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N035(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N036(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N037(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N038(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N039(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N031(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N032(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N033(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N034(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N035(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N036(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N037(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N038(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N039(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N041(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N042(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N043(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N044(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N045(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N046(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N047(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N048(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N049(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N041(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N042(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N043(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N044(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N045(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N046(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N047(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N048(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N049(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N051(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N052(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N053(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N054(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N055(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N056(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N057(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N058(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N059(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N051(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N052(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N053(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N054(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N055(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N056(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N057(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N058(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N059(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N061(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N062(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N063(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N064(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N065(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N066(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N067(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N068(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N069(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N061(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N062(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N063(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N064(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N065(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N066(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N067(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N068(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N069(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N071(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N072(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N073(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N074(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N075(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N076(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N077(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N078(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N079(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N071(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N072(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N073(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N074(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N075(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N076(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N077(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N078(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N079(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N081(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N082(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N083(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N084(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N085(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N086(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N087(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N088(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N089(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N081(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N082(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N083(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N084(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N085(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N086(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N087(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N088(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N089(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N091(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N092(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N093(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N094(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N095(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N096(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N097(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N098(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N099(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N091(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N092(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N093(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N094(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N095(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N096(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N097(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N098(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N099(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N030(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N040(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N050(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N060(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N070(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N080(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N090(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N100(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N101(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N102(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N103(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N104(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N105(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N106(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N107(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N108(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N109(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N110(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N111(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N112(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N113(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N114(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N115(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N116(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N117(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N118(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N119(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N120(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N121(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N122(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N123(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N124(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N125(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N126(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N127(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N128(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N129(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N131(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N132(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N133(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N134(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N135(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N136(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N137(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N138(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N139(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N141(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N142(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N143(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N144(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N145(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N146(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N147(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N148(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N149(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N151(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N152(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N153(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N154(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N155(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N156(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N157(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N158(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N159(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N161(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N162(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N163(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N164(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N165(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N166(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N167(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N168(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N169(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N171(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N172(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N173(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N174(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N175(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N176(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N177(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N178(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N179(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N181(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N182(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N183(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N184(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N185(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N186(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N187(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N188(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N189(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N191(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N192(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N193(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N194(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N195(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N196(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N197(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N198(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N199(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N198(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N199(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N130(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N140(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N150(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N160(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N170(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N180(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N190(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N201(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N202(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N203(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N204(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N205(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N206(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N207(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N208(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N209(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N210(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N201(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N202(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N203(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N204(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N205(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N206(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N207(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N208(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N209(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N211(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N212(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N213(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N214(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N215(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N216(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N217(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N218(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N219(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N220(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N211(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N212(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N213(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N214(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N215(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N216(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N217(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N218(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N219(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N221(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N222(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N223(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N224(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N225(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N226(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N227(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N228(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N229(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N221(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N222(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N223(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N224(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N225(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N226(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N227(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N228(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N229(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N231(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N232(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N233(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N234(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N235(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N236(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N237(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N238(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N239(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N231(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N232(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N233(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N234(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N235(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N236(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N237(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N238(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N239(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N241(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N242(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N243(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N244(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N245(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N246(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N247(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N248(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N249(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N241(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N242(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N243(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N244(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N245(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N246(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N247(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N248(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N249(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N251(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N252(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N253(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N254(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N255(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N256(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N257(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N258(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N259(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N251(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N252(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N253(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N254(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N255(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N256(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N257(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N258(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N259(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N261(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N262(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N263(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N264(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N265(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N266(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N267(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N268(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N269(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N261(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N262(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N263(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N264(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N265(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N266(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N267(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N268(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N269(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N271(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N272(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N273(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N274(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N275(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N276(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N277(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N278(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N279(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N271(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N272(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N273(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N274(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N275(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N276(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N277(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N278(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N279(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N281(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N282(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N283(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N284(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N285(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N286(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N287(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N288(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N289(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N281(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N282(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N283(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N284(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N285(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N286(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N287(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N288(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N289(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N291(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N292(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N293(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N294(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N295(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N296(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N297(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N298(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N299(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N291(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N292(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N293(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N294(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N295(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N296(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N297(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N298(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N299(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N230(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N240(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N250(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N260(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N270(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N280(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N290(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N301(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N302(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N303(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N304(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N305(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N306(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N307(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N308(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N309(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N310(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N301(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N302(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N303(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N304(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N305(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N306(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N307(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N308(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N309(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N311(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N312(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N313(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N314(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N315(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N316(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N317(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N318(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N319(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N320(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N311(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N312(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N313(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N314(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N315(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N316(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N317(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N318(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N319(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N321(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N322(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N323(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N324(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N325(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N326(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N327(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N328(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N329(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N321(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N322(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N323(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N324(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N325(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N326(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N327(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N328(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N329(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N331(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N332(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N333(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N334(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N335(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N336(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N337(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N338(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N339(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N331(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N332(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N333(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N334(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N335(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N336(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N337(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N338(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N339(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N341(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N342(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N343(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N344(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N345(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N346(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N347(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N348(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N349(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N341(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N342(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N343(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N344(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N345(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N346(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N347(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N348(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N349(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N351(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N352(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N353(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N354(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N355(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N356(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N357(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N358(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N359(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N351(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N352(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N353(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N354(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N355(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N356(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N357(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N358(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N359(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N361(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N362(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N363(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N364(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N365(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N366(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N367(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N368(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N369(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N361(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N362(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N363(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N364(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N365(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N366(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N367(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N368(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N369(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N371(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N372(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N373(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N374(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N375(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N376(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N377(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N378(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N379(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N371(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N372(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N373(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N374(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N375(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N376(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N377(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N378(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N379(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N381(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N382(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N383(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N384(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N385(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N386(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N387(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N388(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N389(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N381(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N382(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N383(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N384(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N385(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N386(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N387(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N388(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N389(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N391(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N392(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N393(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N394(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N395(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N396(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N397(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N398(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N399(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N391(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N392(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N393(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N394(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N395(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N396(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N397(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N398(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N399(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N330(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N340(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N350(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N360(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N370(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N380(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N390(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N401(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N402(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N403(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N404(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N405(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N406(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N407(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N408(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N409(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N410(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N401(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N402(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N403(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N404(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N405(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N406(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N407(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N408(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N409(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N411(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N412(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N413(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N414(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N415(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N416(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N417(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N418(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N419(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N420(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N411(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N412(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N413(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N414(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N415(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N416(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N417(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N418(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N419(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N421(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N422(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N423(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N424(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N425(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N426(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N427(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N428(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N429(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N421(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N422(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N423(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N424(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N425(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N426(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N427(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N428(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N429(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N431(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N432(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N433(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N434(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N435(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N436(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N437(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N438(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N439(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N431(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N432(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N433(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N434(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N435(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N436(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N437(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N438(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N439(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N441(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N442(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N443(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N444(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N445(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N446(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N447(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N448(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N449(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N441(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N442(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N443(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N444(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N445(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N446(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N447(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N448(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N449(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N451(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N452(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N453(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N454(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N455(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N456(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N457(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N458(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N459(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N451(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N452(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N453(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N454(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N455(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N456(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N457(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N458(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N459(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N461(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N462(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N463(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N464(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N465(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N466(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N467(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N468(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N469(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N461(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N462(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N463(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N464(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N465(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N466(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N467(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N468(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N469(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N471(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N472(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N473(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N474(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N475(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N476(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N477(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N478(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N479(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N471(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N472(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N473(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N474(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N475(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N476(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N477(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N478(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N479(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N481(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N482(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N483(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N484(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N485(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N486(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N487(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N488(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N489(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N481(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N482(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N483(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N484(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N485(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N486(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N487(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N488(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N489(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N491(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N492(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N493(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N494(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N495(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N496(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N497(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N498(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N499(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N491(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N492(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N493(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N494(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N495(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N496(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N497(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N498(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N499(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N430(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N440(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N450(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N460(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N470(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N480(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N490(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N501(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N502(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N503(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N504(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N505(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N506(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N507(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N508(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N509(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N510(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N501(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N502(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N503(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N504(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N505(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N506(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N507(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N508(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N509(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N511(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N512(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N513(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N514(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N515(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N516(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N517(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N518(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N519(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N520(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N511(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N512(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N513(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N514(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N515(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N516(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N517(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N518(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N519(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N521(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N522(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N523(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N524(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N525(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N526(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N527(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N528(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N529(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N521(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N522(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N523(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N524(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N525(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N526(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N527(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N528(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N529(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N531(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N532(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N533(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N534(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N535(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N536(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N537(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N538(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N539(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N531(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N532(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N533(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N534(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N535(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N536(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N537(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N538(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N539(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N541(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N542(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N543(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N544(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N545(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N546(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N547(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N548(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N549(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N541(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N542(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N543(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N544(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N545(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N546(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N547(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N548(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N549(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N551(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N552(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N553(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N554(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N555(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N556(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N557(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N558(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N559(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N551(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N552(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N553(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N554(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N555(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N556(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N557(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N558(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N559(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N561(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N562(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N563(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N564(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N565(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N566(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N567(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N568(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N569(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N561(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N562(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N563(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N564(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N565(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N566(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N567(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N568(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N569(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N571(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N572(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N573(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N574(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N575(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N576(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N577(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N578(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N579(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N571(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N572(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N573(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N574(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N575(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N576(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N577(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N578(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N579(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N581(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N582(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N583(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N584(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N585(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N586(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N587(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N588(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N589(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N581(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N582(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N583(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N584(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N585(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N586(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N587(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N588(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N589(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N591(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N592(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N593(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N594(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N595(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N596(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N597(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N598(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N599(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N591(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N592(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N593(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N594(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N595(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N596(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N597(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N598(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N599(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N530(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N540(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N550(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N560(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N570(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N580(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N590(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N601(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N602(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N603(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N604(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N605(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N606(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N607(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N608(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N609(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N610(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N601(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N602(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N603(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N604(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N605(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N606(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N607(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N608(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N609(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N611(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N612(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N613(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N614(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N615(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N616(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N617(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N618(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N619(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N620(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N611(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N612(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N613(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N614(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N615(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N616(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N617(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N618(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N619(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N621(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N622(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N623(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N624(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N625(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N626(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N627(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N628(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N629(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N621(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N622(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N623(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N624(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N625(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N626(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N627(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N628(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N629(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N631(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N632(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N633(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N634(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N635(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N636(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N637(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N638(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N639(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N631(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N632(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N633(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N634(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N635(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N636(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N637(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N638(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N639(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N641(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N642(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N643(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N644(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N645(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N646(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N647(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N648(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N649(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N641(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N642(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N643(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N644(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N645(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N646(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N647(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N648(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N649(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N651(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N652(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N653(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N654(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N655(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N656(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N657(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N658(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N659(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N651(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N652(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N653(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N654(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N655(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N656(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N657(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N658(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N659(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N661(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N662(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N663(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N664(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N665(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N666(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N667(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N668(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N669(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N661(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N662(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N663(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N664(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N665(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N666(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N667(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N668(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N669(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N671(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N672(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N673(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N674(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N675(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N676(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N677(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N678(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N679(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N671(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N672(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N673(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N674(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N675(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N676(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N677(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N678(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N679(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N681(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N682(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N683(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N684(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N685(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N686(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N687(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N688(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N689(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N681(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N682(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N683(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N684(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N685(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N686(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N687(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N688(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N689(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N691(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N692(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N693(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N694(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N695(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N696(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N697(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N698(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N699(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N691(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N692(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N693(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N694(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N695(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N696(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N697(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N698(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N699(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N630(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N640(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N650(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N660(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N670(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N680(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N690(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
// 外部表
%table N701(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N702(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N703(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N704(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N705(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N706(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N707(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N708(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N709(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N710(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N701(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N702(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N703(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N704(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N705(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N706(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N707(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N708(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N709(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N711(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N712(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N713(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N714(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N715(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N716(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N717(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N718(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N719(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N720(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N711(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N712(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N713(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N714(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N715(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N716(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N717(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N718(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N719(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N721(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N722(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N723(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N724(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N725(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N726(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N727(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N728(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N729(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N721(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N722(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N723(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N724(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N725(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N726(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N727(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N728(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N729(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N731(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N732(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N733(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N734(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N735(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N736(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N737(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N738(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N739(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N731(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N732(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N733(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N734(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N735(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N736(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N737(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N738(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N739(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N741(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N742(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N743(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N744(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N745(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N746(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N747(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N748(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N749(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N741(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N742(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N743(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N744(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N745(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N746(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N747(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N748(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N749(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N751(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N752(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N753(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N754(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N755(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N756(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N757(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N758(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N759(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N751(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N752(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N753(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N754(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N755(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N756(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N757(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N758(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N759(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N761(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N762(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N763(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N764(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N765(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N766(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N767(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N768(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N769(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N761(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N762(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N763(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N764(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N765(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N766(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N767(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N768(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N769(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N771(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N772(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N773(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N774(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N775(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N776(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N777(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N778(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N779(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N771(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N772(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N773(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N774(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N775(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N776(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N777(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N778(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N779(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N781(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N782(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N783(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N784(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N785(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N786(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N787(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N788(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N789(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N781(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N782(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N783(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N784(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N785(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N786(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N787(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N788(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N789(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N791(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N792(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N793(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N794(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N795(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N796(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N797(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N798(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N799(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N791(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N792(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N793(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N794(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N795(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N796(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N797(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N798(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N799(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N730(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N740(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N750(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N760(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N770(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N780(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N790(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N801(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N802(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N803(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N804(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N805(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N806(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N807(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N808(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N809(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N810(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N801(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N802(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N803(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N804(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N805(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N806(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N807(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N808(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N809(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N811(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N812(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N813(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N814(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N815(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N816(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N817(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N818(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N819(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N820(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N811(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N812(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N813(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N814(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N815(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N816(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N817(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N818(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N819(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N821(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N822(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N823(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N824(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N825(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N826(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N827(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N828(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N829(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N821(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N822(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N823(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N824(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N825(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N826(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N827(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N828(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N829(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N831(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N832(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N833(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N834(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N835(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N836(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N837(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N838(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N839(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N831(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N832(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N833(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N834(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N835(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N836(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N837(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N838(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N839(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N841(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N842(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N843(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N844(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N845(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N846(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N847(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N848(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N849(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N841(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N842(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N843(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N844(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N845(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N846(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N847(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N848(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N849(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N851(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N852(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N853(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N854(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N855(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N856(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N857(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N858(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N859(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N851(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N852(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N853(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N854(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N855(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N856(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N857(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N858(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N859(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N861(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N862(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N863(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N864(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N865(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N866(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N867(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N868(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N869(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N861(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N862(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N863(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N864(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N865(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N866(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N867(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N868(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N869(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N871(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N872(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N873(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N874(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N875(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N876(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N877(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N878(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N879(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N871(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N872(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N873(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N874(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N875(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N876(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N877(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N878(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N879(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N881(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N882(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N883(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N884(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N885(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N886(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N887(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N888(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N889(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N881(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N882(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N883(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N884(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N885(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N886(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N887(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N888(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N889(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N891(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N892(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N893(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N894(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N895(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N896(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N897(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N898(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N899(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N891(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N892(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N893(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N894(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N895(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N896(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N897(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N898(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N899(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N830(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N840(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N850(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N860(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N870(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N880(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N890(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N901(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N902(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N903(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N904(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N905(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N906(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N907(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N908(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N909(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N910(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N901(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N902(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N903(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N904(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N905(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N906(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N907(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N908(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N909(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N911(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N912(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N913(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N914(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N915(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N916(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N917(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N918(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N919(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table N920(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N911(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N912(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N913(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N914(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N915(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N916(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N917(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N918(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N919(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N921(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N922(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N923(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N924(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N925(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N926(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N927(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N928(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N929(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N921(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N922(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N923(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N924(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N925(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N926(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N927(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N928(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N929(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N931(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N932(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N933(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N934(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N935(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N936(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N937(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N938(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N939(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N931(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N932(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N933(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N934(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N935(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N936(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N937(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N938(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N939(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N941(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N942(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N943(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N944(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N945(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N946(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N947(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N948(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N949(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N941(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N942(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N943(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N944(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N945(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N946(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N947(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N948(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N949(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N951(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N952(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N953(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N954(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N955(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N956(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N957(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N958(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N959(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N951(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N952(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N953(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N954(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N955(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N956(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N957(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N958(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N959(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N961(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N962(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N963(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N964(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N965(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N966(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N967(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N968(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N969(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N961(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N962(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N963(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N964(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N965(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N966(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N967(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N968(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N969(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N971(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N972(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N973(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N974(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N975(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N976(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N977(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N978(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N979(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N971(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N972(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N973(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N974(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N975(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N976(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N977(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N978(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N979(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N981(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N982(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N983(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N984(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N985(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N986(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N987(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N988(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N989(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N981(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N982(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N983(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N984(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N985(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N986(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N987(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N988(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N989(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table N991(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N992(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N993(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N994(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N995(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N996(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N997(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table N998(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N991(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N992(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N993(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N994(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N995(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N996(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N997(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
N998(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table N930(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N940(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N950(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N960(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N970(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N980(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N990(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N200(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N300(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table N400(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table N500(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N600(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N700(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N800(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table N900(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
