%version v0.0.0
namespace ns2{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{
index(0(a,b,c)),update
}

// 外部表
%table ns2N000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N001(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N002(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N003(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N004(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N005(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N006(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N007(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N008(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N009(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N010(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N001(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N002(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N003(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N004(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N005(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N006(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N007(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N008(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N009(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N011(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N012(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N013(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N014(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N015(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N016(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N017(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N018(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N019(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N020(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N011(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N012(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N013(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N014(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N015(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N016(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N017(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N018(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N019(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N021(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N022(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N023(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N024(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N025(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N026(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N027(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N028(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N029(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N021(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N022(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N023(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N024(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N025(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N026(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N027(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N028(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N029(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N031(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N032(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N033(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N034(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N035(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N036(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N037(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N038(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N039(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N031(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N032(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N033(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N034(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N035(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N036(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N037(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N038(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N039(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N041(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N042(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N043(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N044(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N045(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N046(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N047(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N048(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N049(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N041(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N042(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N043(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N044(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N045(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N046(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N047(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N048(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N049(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N051(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N052(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N053(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N054(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N055(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N056(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N057(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N058(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N059(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N051(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N052(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N053(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N054(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N055(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N056(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N057(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N058(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N059(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N061(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N062(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N063(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N064(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N065(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N066(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N067(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N068(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N069(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N061(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N062(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N063(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N064(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N065(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N066(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N067(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N068(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N069(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N071(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N072(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N073(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N074(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N075(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N076(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N077(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N078(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N079(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N071(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N072(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N073(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N074(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N075(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N076(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N077(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N078(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N079(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N081(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N082(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N083(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N084(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N085(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N086(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N087(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N088(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N089(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N081(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N082(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N083(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N084(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N085(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N086(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N087(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N088(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N089(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N091(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N092(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N093(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N094(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N095(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N096(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N097(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N098(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N099(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N091(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N092(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N093(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N094(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N095(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N096(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N097(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N098(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N099(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N030(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N040(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N050(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N060(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N070(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N080(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N090(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N100(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N101(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N102(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N103(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N104(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N105(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N106(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N107(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N108(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N109(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N110(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N111(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N112(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N113(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N114(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N115(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N116(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N117(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N118(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N119(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N120(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N121(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N122(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N123(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N124(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N125(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N126(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N127(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N128(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N129(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N131(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N132(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N133(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N134(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N135(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N136(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N137(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N138(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N139(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N141(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N142(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N143(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N144(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N145(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N146(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N147(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N148(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N149(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N151(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N152(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N153(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N154(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N155(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N156(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N157(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N158(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N159(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N161(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N162(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N163(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N164(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N165(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N166(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N167(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N168(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N169(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N171(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N172(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N173(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N174(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N175(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N176(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N177(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N178(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N179(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N181(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N182(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N183(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N184(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N185(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N186(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N187(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N188(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N189(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N191(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N192(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N193(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N194(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N195(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N196(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N197(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N198(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N199(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N198(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N199(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N130(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N140(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N150(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N160(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N170(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N180(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N190(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N201(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N202(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N203(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N204(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N205(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N206(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N207(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N208(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N209(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N210(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N201(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N202(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N203(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N204(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N205(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N206(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N207(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N208(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N209(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N211(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N212(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N213(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N214(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N215(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N216(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N217(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N218(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N219(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N220(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N211(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N212(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N213(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N214(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N215(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N216(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N217(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N218(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N219(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N221(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N222(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N223(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N224(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N225(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N226(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N227(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N228(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N229(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N221(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N222(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N223(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N224(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N225(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N226(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N227(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N228(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N229(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N231(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N232(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N233(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N234(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N235(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N236(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N237(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N238(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N239(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N231(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N232(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N233(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N234(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N235(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N236(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N237(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N238(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N239(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N241(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N242(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N243(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N244(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N245(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N246(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N247(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N248(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N249(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N241(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N242(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N243(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N244(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N245(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N246(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N247(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N248(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N249(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N251(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N252(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N253(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N254(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N255(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N256(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N257(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N258(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N259(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N251(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N252(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N253(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N254(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N255(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N256(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N257(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N258(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N259(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N261(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N262(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N263(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N264(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N265(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N266(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N267(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N268(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N269(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N261(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N262(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N263(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N264(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N265(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N266(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N267(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N268(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N269(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N271(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N272(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N273(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N274(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N275(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N276(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N277(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N278(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N279(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N271(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N272(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N273(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N274(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N275(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N276(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N277(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N278(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N279(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N281(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N282(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N283(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N284(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N285(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N286(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N287(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N288(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N289(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N281(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N282(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N283(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N284(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N285(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N286(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N287(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N288(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N289(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N291(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N292(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N293(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N294(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N295(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N296(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N297(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N298(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N299(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N291(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N292(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N293(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N294(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N295(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N296(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N297(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N298(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N299(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N230(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N240(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N250(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N260(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N270(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N280(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N290(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N301(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N302(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N303(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N304(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N305(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N306(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N307(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N308(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N309(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N310(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N301(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N302(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N303(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N304(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N305(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N306(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N307(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N308(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N309(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N311(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N312(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N313(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N314(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N315(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N316(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N317(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N318(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N319(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N320(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N311(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N312(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N313(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N314(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N315(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N316(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N317(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N318(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N319(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N321(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N322(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N323(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N324(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N325(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N326(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N327(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N328(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N329(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N321(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N322(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N323(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N324(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N325(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N326(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N327(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N328(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N329(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N331(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N332(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N333(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N334(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N335(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N336(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N337(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N338(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N339(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N331(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N332(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N333(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N334(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N335(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N336(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N337(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N338(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N339(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N341(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N342(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N343(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N344(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N345(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N346(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N347(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N348(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N349(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N341(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N342(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N343(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N344(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N345(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N346(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N347(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N348(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N349(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N351(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N352(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N353(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N354(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N355(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N356(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N357(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N358(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N359(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N351(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N352(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N353(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N354(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N355(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N356(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N357(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N358(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N359(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N361(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N362(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N363(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N364(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N365(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N366(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N367(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N368(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N369(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N361(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N362(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N363(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N364(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N365(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N366(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N367(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N368(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N369(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N371(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N372(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N373(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N374(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N375(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N376(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N377(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N378(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N379(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N371(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N372(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N373(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N374(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N375(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N376(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N377(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N378(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N379(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N381(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N382(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N383(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N384(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N385(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N386(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N387(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N388(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N389(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N381(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N382(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N383(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N384(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N385(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N386(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N387(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N388(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N389(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N391(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N392(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N393(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N394(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N395(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N396(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N397(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N398(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N399(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N391(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N392(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N393(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N394(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N395(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N396(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N397(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N398(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N399(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N330(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N340(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N350(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N360(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N370(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N380(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N390(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N401(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N402(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N403(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N404(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N405(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N406(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N407(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N408(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N409(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N410(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N401(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N402(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N403(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N404(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N405(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N406(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N407(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N408(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N409(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N411(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N412(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N413(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N414(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N415(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N416(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N417(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N418(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N419(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N420(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N411(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N412(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N413(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N414(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N415(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N416(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N417(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N418(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N419(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N421(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N422(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N423(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N424(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N425(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N426(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N427(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N428(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N429(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N421(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N422(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N423(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N424(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N425(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N426(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N427(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N428(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N429(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N431(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N432(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N433(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N434(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N435(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N436(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N437(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N438(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N439(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N431(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N432(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N433(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N434(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N435(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N436(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N437(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N438(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N439(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N441(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N442(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N443(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N444(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N445(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N446(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N447(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N448(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N449(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N441(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N442(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N443(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N444(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N445(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N446(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N447(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N448(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N449(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N451(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N452(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N453(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N454(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N455(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N456(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N457(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N458(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N459(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N451(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N452(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N453(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N454(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N455(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N456(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N457(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N458(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N459(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N461(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N462(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N463(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N464(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N465(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N466(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N467(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N468(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N469(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N461(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N462(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N463(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N464(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N465(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N466(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N467(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N468(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N469(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N471(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N472(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N473(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N474(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N475(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N476(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N477(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N478(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N479(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N471(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N472(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N473(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N474(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N475(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N476(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N477(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N478(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N479(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N481(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N482(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N483(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N484(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N485(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N486(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N487(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N488(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N489(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N481(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N482(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N483(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N484(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N485(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N486(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N487(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N488(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N489(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N491(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N492(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N493(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N494(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N495(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N496(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N497(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N498(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N499(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N491(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N492(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N493(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N494(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N495(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N496(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N497(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N498(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N499(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N430(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N440(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N450(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N460(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N470(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N480(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N490(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N501(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N502(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N503(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N504(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N505(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N506(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N507(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N508(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N509(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N510(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N501(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N502(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N503(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N504(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N505(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N506(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N507(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N508(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N509(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N511(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N512(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N513(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N514(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N515(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N516(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N517(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N518(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N519(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N520(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N511(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N512(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N513(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N514(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N515(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N516(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N517(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N518(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N519(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N521(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N522(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N523(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N524(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N525(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N526(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N527(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N528(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N529(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N521(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N522(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N523(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N524(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N525(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N526(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N527(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N528(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N529(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N531(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N532(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N533(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N534(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N535(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N536(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N537(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N538(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N539(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N531(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N532(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N533(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N534(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N535(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N536(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N537(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N538(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N539(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N541(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N542(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N543(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N544(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N545(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N546(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N547(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N548(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N549(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N541(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N542(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N543(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N544(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N545(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N546(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N547(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N548(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N549(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N551(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N552(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N553(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N554(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N555(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N556(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N557(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N558(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N559(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N551(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N552(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N553(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N554(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N555(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N556(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N557(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N558(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N559(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N561(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N562(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N563(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N564(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N565(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N566(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N567(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N568(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N569(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N561(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N562(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N563(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N564(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N565(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N566(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N567(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N568(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N569(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N571(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N572(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N573(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N574(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N575(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N576(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N577(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N578(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N579(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N571(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N572(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N573(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N574(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N575(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N576(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N577(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N578(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N579(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N581(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N582(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N583(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N584(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N585(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N586(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N587(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N588(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N589(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N581(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N582(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N583(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N584(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N585(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N586(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N587(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N588(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N589(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N591(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N592(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N593(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N594(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N595(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N596(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N597(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N598(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N599(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N591(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N592(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N593(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N594(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N595(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N596(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N597(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N598(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N599(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N530(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N540(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N550(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N560(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N570(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N580(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N590(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N601(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N602(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N603(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N604(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N605(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N606(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N607(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N608(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N609(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N610(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N601(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N602(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N603(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N604(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N605(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N606(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N607(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N608(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N609(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N611(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N612(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N613(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N614(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N615(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N616(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N617(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N618(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N619(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N620(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N611(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N612(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N613(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N614(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N615(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N616(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N617(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N618(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N619(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N621(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N622(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N623(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N624(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N625(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N626(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N627(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N628(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N629(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N621(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N622(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N623(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N624(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N625(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N626(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N627(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N628(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N629(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N631(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N632(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N633(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N634(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N635(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N636(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N637(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N638(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N639(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N631(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N632(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N633(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N634(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N635(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N636(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N637(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N638(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N639(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N641(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N642(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N643(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N644(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N645(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N646(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N647(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N648(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N649(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N641(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N642(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N643(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N644(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N645(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N646(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N647(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N648(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N649(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N651(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N652(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N653(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N654(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N655(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N656(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N657(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N658(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N659(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N651(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N652(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N653(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N654(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N655(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N656(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N657(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N658(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N659(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N661(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N662(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N663(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N664(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N665(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N666(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N667(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N668(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N669(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N661(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N662(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N663(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N664(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N665(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N666(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N667(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N668(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N669(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N671(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N672(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N673(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N674(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N675(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N676(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N677(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N678(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N679(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N671(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N672(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N673(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N674(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N675(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N676(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N677(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N678(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N679(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N681(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N682(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N683(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N684(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N685(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N686(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N687(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N688(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N689(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N681(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N682(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N683(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N684(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N685(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N686(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N687(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N688(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N689(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N691(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N692(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N693(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N694(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N695(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N696(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N697(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N698(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N699(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N691(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N692(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N693(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N694(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N695(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N696(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N697(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N698(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N699(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N630(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N640(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N650(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N660(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N670(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N680(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N690(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
// 外部表
%table ns2N701(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N702(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N703(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N704(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N705(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N706(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N707(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N708(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N709(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N710(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N701(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N702(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N703(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N704(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N705(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N706(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N707(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N708(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N709(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N711(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N712(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N713(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N714(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N715(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N716(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N717(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N718(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N719(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N720(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N711(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N712(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N713(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N714(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N715(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N716(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N717(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N718(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N719(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N721(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N722(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N723(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N724(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N725(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N726(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N727(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N728(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N729(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N721(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N722(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N723(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N724(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N725(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N726(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N727(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N728(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N729(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N731(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N732(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N733(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N734(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N735(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N736(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N737(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N738(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N739(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N731(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N732(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N733(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N734(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N735(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N736(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N737(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N738(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N739(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N741(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N742(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N743(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N744(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N745(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N746(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N747(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N748(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N749(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N741(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N742(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N743(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N744(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N745(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N746(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N747(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N748(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N749(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N751(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N752(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N753(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N754(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N755(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N756(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N757(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N758(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N759(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N751(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N752(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N753(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N754(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N755(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N756(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N757(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N758(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N759(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N761(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N762(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N763(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N764(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N765(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N766(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N767(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N768(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N769(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N761(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N762(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N763(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N764(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N765(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N766(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N767(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N768(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N769(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N771(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N772(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N773(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N774(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N775(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N776(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N777(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N778(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N779(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N771(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N772(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N773(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N774(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N775(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N776(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N777(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N778(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N779(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N781(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N782(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N783(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N784(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N785(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N786(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N787(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N788(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N789(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N781(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N782(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N783(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N784(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N785(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N786(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N787(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N788(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N789(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N791(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N792(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N793(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N794(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N795(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N796(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N797(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N798(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N799(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N791(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N792(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N793(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N794(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N795(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N796(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N797(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N798(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N799(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N730(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N740(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N750(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N760(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N770(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N780(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N790(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N801(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N802(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N803(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N804(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N805(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N806(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N807(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N808(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N809(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N810(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N801(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N802(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N803(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N804(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N805(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N806(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N807(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N808(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N809(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N811(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N812(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N813(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N814(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N815(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N816(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N817(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N818(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N819(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N820(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N811(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N812(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N813(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N814(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N815(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N816(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N817(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N818(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N819(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N821(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N822(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N823(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N824(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N825(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N826(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N827(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N828(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N829(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N821(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N822(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N823(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N824(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N825(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N826(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N827(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N828(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N829(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N831(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N832(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N833(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N834(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N835(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N836(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N837(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N838(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N839(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N831(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N832(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N833(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N834(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N835(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N836(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N837(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N838(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N839(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N841(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N842(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N843(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N844(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N845(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N846(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N847(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N848(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N849(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N841(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N842(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N843(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N844(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N845(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N846(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N847(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N848(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N849(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N851(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N852(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N853(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N854(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N855(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N856(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N857(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N858(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N859(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N851(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N852(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N853(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N854(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N855(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N856(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N857(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N858(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N859(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N861(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N862(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N863(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N864(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N865(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N866(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N867(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N868(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N869(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N861(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N862(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N863(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N864(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N865(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N866(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N867(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N868(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N869(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N871(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N872(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N873(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N874(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N875(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N876(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N877(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N878(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N879(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N871(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N872(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N873(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N874(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N875(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N876(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N877(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N878(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N879(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N881(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N882(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N883(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N884(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N885(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N886(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N887(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N888(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N889(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N881(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N882(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N883(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N884(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N885(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N886(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N887(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N888(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N889(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N891(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N892(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N893(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N894(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N895(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N896(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N897(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N898(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N899(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N891(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N892(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N893(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N894(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N895(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N896(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N897(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N898(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N899(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N830(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N840(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N850(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N860(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N870(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N880(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N890(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N901(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N902(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N903(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N904(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N905(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N906(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N907(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N908(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N909(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N910(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N901(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N902(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N903(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N904(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N905(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N906(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N907(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N908(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N909(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N911(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N912(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N913(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N914(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N915(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N916(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N917(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N918(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N919(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns2N920(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N911(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N912(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N913(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N914(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N915(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N916(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N917(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N918(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N919(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N921(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N922(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N923(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N924(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N925(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N926(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N927(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N928(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N929(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N921(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N922(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N923(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N924(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N925(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N926(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N927(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N928(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N929(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N931(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N932(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N933(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N934(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N935(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N936(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N937(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N938(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N939(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N931(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N932(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N933(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N934(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N935(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N936(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N937(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N938(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N939(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N941(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N942(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N943(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N944(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N945(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N946(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N947(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N948(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N949(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N941(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N942(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N943(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N944(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N945(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N946(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N947(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N948(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N949(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N951(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N952(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N953(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N954(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N955(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N956(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N957(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N958(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N959(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N951(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N952(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N953(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N954(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N955(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N956(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N957(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N958(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N959(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N961(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N962(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N963(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N964(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N965(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N966(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N967(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N968(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N969(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N961(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N962(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N963(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N964(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N965(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N966(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N967(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N968(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N969(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N971(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N972(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N973(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N974(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N975(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N976(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N977(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N978(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N979(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N971(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N972(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N973(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N974(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N975(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N976(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N977(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N978(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N979(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N981(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N982(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N983(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N984(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N985(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N986(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N987(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N988(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N989(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N981(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N982(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N983(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N984(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N985(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N986(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N987(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N988(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N989(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns2N991(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N992(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N993(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N994(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N995(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N996(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N997(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns2N998(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns2N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N991(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N992(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N993(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N994(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N995(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N996(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N997(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns2N998(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns2N930(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N940(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N950(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N960(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N970(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N980(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N990(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N200(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N300(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns2N400(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns2N500(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N600(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N700(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N800(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns2N900(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns2N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}
