%version v0.0.0
namespace ns1{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{
index(0(a,b,c)),update
}

// 外部表
%table ns1N000(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N001(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N002(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N003(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N004(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N005(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N006(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N007(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N008(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N009(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N010(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N001(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N002(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N003(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N004(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N005(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N006(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N007(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N008(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N009(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N011(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N012(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N013(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N014(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N015(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N016(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N017(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N018(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N019(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N020(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N010(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N011(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N012(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N013(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N014(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N015(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N016(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N017(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N018(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N019(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N021(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N022(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N023(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N024(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N025(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N026(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N027(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N028(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N029(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N020(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N021(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N022(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N023(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N024(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N025(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N026(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N027(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N028(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N029(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N031(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N032(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N033(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N034(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N035(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N036(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N037(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N038(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N039(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N031(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N032(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N033(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N034(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N035(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N036(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N037(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N038(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N039(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N041(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N042(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N043(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N044(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N045(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N046(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N047(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N048(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N049(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N041(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N042(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N043(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N044(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N045(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N046(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N047(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N048(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N049(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N051(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N052(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N053(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N054(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N055(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N056(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N057(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N058(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N059(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N051(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N052(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N053(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N054(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N055(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N056(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N057(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N058(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N059(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N061(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N062(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N063(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N064(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N065(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N066(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N067(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N068(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N069(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N061(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N062(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N063(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N064(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N065(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N066(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N067(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N068(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N069(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N071(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N072(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N073(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N074(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N075(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N076(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N077(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N078(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N079(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N071(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N072(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N073(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N074(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N075(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N076(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N077(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N078(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N079(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N081(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N082(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N083(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N084(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N085(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N086(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N087(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N088(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N089(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N081(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N082(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N083(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N084(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N085(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N086(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N087(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N088(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N089(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N091(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N092(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N093(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N094(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N095(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N096(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N097(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N098(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N099(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N091(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N092(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N093(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N094(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N095(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N096(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N097(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N098(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N099(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N030(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N030(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N040(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N040(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N050(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N050(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N060(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N060(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N070(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N070(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N080(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N080(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N090(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N090(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N100(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N101(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N102(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N103(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N104(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N105(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N106(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N107(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N108(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N109(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N110(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N100(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N101(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N102(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N103(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N104(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N105(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N106(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N107(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N108(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N109(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N111(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N112(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N113(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N114(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N115(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N116(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N117(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N118(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N119(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N120(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N110(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N111(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N112(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N113(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N114(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N115(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N116(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N117(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N118(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N119(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N121(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N122(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N123(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N124(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N125(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N126(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N127(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N128(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N129(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N120(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N121(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N122(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N123(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N124(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N125(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N126(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N127(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N128(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N129(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N131(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N132(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N133(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N134(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N135(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N136(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N137(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N138(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N139(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N131(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N132(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N133(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N134(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N135(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N136(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N137(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N138(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N139(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N141(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N142(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N143(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N144(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N145(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N146(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N147(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N148(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N149(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N141(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N142(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N143(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N144(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N145(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N146(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N147(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N148(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N149(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N151(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N152(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N153(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N154(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N155(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N156(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N157(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N158(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N159(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N151(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N152(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N153(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N154(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N155(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N156(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N157(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N158(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N159(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N161(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N162(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N163(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N164(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N165(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N166(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N167(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N168(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N169(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N161(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N162(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N163(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N164(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N165(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N166(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N167(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N168(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N169(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N171(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N172(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N173(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N174(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N175(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N176(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N177(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N178(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N179(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N171(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N172(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N173(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N174(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N175(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N176(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N177(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N178(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N179(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N181(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N182(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N183(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N184(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N185(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N186(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N187(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N188(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N189(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N181(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N182(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N183(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N184(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N185(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N186(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N187(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N188(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N189(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N191(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N192(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N193(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N194(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N195(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N196(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N197(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N198(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N199(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N191(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N192(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N193(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N194(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N195(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N196(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N197(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N198(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N199(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N130(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N130(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N140(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N140(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N150(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N150(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N160(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N160(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N170(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N170(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N180(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N180(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N190(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N190(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N201(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N202(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N203(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N204(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N205(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N206(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N207(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N208(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N209(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N210(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N201(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N202(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N203(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N204(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N205(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N206(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N207(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N208(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N209(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N211(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N212(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N213(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N214(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N215(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N216(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N217(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N218(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N219(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N220(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N210(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N211(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N212(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N213(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N214(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N215(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N216(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N217(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N218(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N219(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N221(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N222(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N223(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N224(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N225(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N226(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N227(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N228(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N229(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N220(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N221(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N222(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N223(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N224(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N225(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N226(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N227(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N228(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N229(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N231(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N232(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N233(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N234(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N235(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N236(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N237(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N238(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N239(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N231(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N232(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N233(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N234(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N235(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N236(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N237(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N238(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N239(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N241(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N242(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N243(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N244(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N245(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N246(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N247(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N248(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N249(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N241(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N242(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N243(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N244(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N245(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N246(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N247(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N248(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N249(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N251(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N252(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N253(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N254(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N255(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N256(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N257(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N258(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N259(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N251(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N252(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N253(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N254(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N255(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N256(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N257(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N258(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N259(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N261(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N262(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N263(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N264(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N265(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N266(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N267(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N268(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N269(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N261(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N262(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N263(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N264(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N265(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N266(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N267(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N268(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N269(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N271(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N272(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N273(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N274(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N275(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N276(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N277(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N278(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N279(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N271(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N272(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N273(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N274(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N275(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N276(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N277(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N278(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N279(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N281(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N282(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N283(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N284(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N285(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N286(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N287(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N288(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N289(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N281(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N282(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N283(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N284(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N285(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N286(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N287(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N288(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N289(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N291(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N292(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N293(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N294(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N295(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N296(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N297(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N298(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N299(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N291(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N292(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N293(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N294(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N295(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N296(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N297(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N298(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N299(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N230(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N230(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N240(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N240(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N250(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N250(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N260(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N260(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N270(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N270(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N280(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N280(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N290(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N290(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N301(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N302(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N303(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N304(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N305(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N306(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N307(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N308(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N309(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N310(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N301(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N302(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N303(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N304(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N305(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N306(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N307(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N308(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N309(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N311(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N312(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N313(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N314(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N315(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N316(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N317(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N318(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N319(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N320(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N310(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N311(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N312(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N313(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N314(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N315(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N316(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N317(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N318(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N319(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N321(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N322(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N323(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N324(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N325(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N326(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N327(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N328(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N329(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N320(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N321(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N322(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N323(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N324(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N325(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N326(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N327(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N328(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N329(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N331(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N332(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N333(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N334(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N335(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N336(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N337(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N338(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N339(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N331(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N332(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N333(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N334(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N335(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N336(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N337(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N338(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N339(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N341(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N342(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N343(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N344(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N345(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N346(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N347(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N348(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N349(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N341(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N342(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N343(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N344(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N345(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N346(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N347(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N348(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N349(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N351(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N352(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N353(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N354(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N355(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N356(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N357(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N358(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N359(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N351(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N352(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N353(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N354(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N355(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N356(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N357(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N358(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N359(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N361(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N362(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N363(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N364(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N365(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N366(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N367(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N368(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N369(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N361(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N362(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N363(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N364(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N365(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N366(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N367(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N368(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N369(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N371(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N372(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N373(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N374(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N375(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N376(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N377(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N378(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N379(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N371(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N372(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N373(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N374(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N375(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N376(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N377(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N378(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N379(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N381(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N382(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N383(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N384(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N385(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N386(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N387(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N388(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N389(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N381(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N382(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N383(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N384(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N385(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N386(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N387(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N388(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N389(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N391(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N392(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N393(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N394(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N395(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N396(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N397(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N398(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N399(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N391(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N392(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N393(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N394(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N395(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N396(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N397(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N398(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N399(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N330(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N330(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N340(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N340(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N350(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N350(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N360(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N360(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N370(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N370(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N380(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N380(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N390(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N390(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N401(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N402(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N403(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N404(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N405(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N406(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N407(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N408(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N409(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N410(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N401(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N402(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N403(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N404(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N405(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N406(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N407(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N408(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N409(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N411(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N412(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N413(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N414(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N415(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N416(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N417(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N418(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N419(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N420(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N410(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N411(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N412(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N413(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N414(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N415(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N416(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N417(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N418(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N419(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N421(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N422(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N423(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N424(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N425(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N426(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N427(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N428(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N429(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N420(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N421(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N422(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N423(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N424(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N425(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N426(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N427(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N428(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N429(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N431(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N432(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N433(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N434(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N435(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N436(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N437(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N438(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N439(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N431(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N432(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N433(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N434(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N435(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N436(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N437(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N438(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N439(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N441(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N442(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N443(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N444(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N445(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N446(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N447(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N448(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N449(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N441(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N442(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N443(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N444(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N445(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N446(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N447(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N448(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N449(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N451(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N452(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N453(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N454(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N455(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N456(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N457(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N458(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N459(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N451(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N452(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N453(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N454(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N455(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N456(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N457(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N458(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N459(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N461(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N462(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N463(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N464(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N465(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N466(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N467(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N468(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N469(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N461(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N462(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N463(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N464(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N465(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N466(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N467(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N468(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N469(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N471(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N472(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N473(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N474(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N475(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N476(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N477(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N478(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N479(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N471(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N472(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N473(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N474(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N475(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N476(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N477(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N478(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N479(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N481(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N482(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N483(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N484(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N485(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N486(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N487(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N488(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N489(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N481(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N482(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N483(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N484(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N485(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N486(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N487(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N488(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N489(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N491(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N492(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N493(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N494(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N495(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N496(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N497(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N498(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N499(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N491(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N492(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N493(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N494(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N495(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N496(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N497(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N498(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N499(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N430(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N430(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N440(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N440(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N450(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N450(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N460(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N460(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N470(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N470(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N480(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N480(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N490(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N490(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N501(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N502(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N503(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N504(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N505(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N506(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N507(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N508(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N509(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N510(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N501(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N502(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N503(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N504(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N505(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N506(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N507(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N508(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N509(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N511(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N512(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N513(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N514(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N515(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N516(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N517(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N518(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N519(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N520(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N510(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N511(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N512(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N513(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N514(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N515(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N516(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N517(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N518(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N519(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N521(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N522(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N523(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N524(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N525(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N526(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N527(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N528(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N529(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N520(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N521(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N522(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N523(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N524(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N525(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N526(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N527(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N528(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N529(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N531(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N532(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N533(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N534(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N535(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N536(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N537(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N538(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N539(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N531(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N532(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N533(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N534(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N535(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N536(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N537(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N538(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N539(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N541(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N542(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N543(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N544(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N545(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N546(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N547(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N548(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N549(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N541(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N542(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N543(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N544(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N545(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N546(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N547(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N548(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N549(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N551(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N552(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N553(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N554(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N555(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N556(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N557(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N558(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N559(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N551(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N552(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N553(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N554(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N555(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N556(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N557(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N558(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N559(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N561(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N562(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N563(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N564(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N565(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N566(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N567(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N568(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N569(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N561(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N562(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N563(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N564(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N565(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N566(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N567(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N568(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N569(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N571(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N572(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N573(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N574(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N575(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N576(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N577(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N578(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N579(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N571(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N572(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N573(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N574(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N575(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N576(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N577(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N578(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N579(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N581(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N582(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N583(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N584(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N585(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N586(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N587(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N588(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N589(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N581(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N582(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N583(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N584(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N585(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N586(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N587(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N588(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N589(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N591(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N592(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N593(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N594(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N595(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N596(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N597(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N598(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N599(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N591(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N592(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N593(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N594(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N595(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N596(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N597(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N598(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N599(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N530(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N530(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N540(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N540(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N550(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N550(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N560(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N560(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N570(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N570(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N580(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N580(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N590(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N590(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N601(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N602(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N603(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N604(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N605(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N606(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N607(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N608(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N609(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N610(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N601(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N602(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N603(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N604(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N605(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N606(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N607(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N608(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N609(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N611(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N612(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N613(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N614(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N615(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N616(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N617(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N618(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N619(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N620(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N610(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N611(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N612(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N613(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N614(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N615(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N616(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N617(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N618(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N619(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N621(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N622(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N623(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N624(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N625(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N626(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N627(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N628(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N629(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N620(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N621(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N622(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N623(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N624(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N625(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N626(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N627(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N628(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N629(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N631(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N632(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N633(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N634(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N635(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N636(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N637(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N638(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N639(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N631(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N632(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N633(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N634(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N635(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N636(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N637(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N638(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N639(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N641(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N642(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N643(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N644(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N645(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N646(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N647(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N648(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N649(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N641(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N642(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N643(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N644(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N645(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N646(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N647(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N648(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N649(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N651(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N652(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N653(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N654(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N655(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N656(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N657(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N658(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N659(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N651(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N652(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N653(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N654(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N655(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N656(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N657(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N658(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N659(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N661(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N662(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N663(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N664(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N665(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N666(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N667(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N668(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N669(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N661(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N662(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N663(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N664(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N665(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N666(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N667(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N668(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N669(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N671(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N672(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N673(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N674(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N675(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N676(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N677(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N678(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N679(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N671(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N672(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N673(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N674(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N675(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N676(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N677(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N678(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N679(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N681(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N682(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N683(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N684(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N685(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N686(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N687(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N688(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N689(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N681(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N682(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N683(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N684(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N685(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N686(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N687(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N688(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N689(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N691(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N692(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N693(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N694(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N695(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N696(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N697(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N698(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N699(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N691(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N692(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N693(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N694(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N695(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N696(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N697(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N698(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N699(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N630(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N630(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N640(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N640(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N650(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N650(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N660(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N660(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N670(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N670(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N680(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N680(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N690(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N690(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
// 外部表
%table ns1N701(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N702(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N703(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N704(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N705(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N706(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N707(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N708(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N709(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N710(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N701(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N702(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N703(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N704(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N705(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N706(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N707(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N708(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N709(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N711(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N712(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N713(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N714(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N715(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N716(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N717(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N718(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N719(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N720(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N710(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N711(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N712(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N713(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N714(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N715(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N716(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N717(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N718(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N719(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N721(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N722(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N723(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N724(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N725(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N726(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N727(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N728(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N729(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N720(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N721(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N722(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N723(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N724(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N725(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N726(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N727(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N728(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N729(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N731(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N732(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N733(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N734(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N735(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N736(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N737(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N738(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N739(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N731(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N732(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N733(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N734(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N735(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N736(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N737(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N738(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N739(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N741(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N742(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N743(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N744(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N745(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N746(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N747(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N748(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N749(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N741(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N742(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N743(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N744(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N745(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N746(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N747(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N748(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N749(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N751(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N752(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N753(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N754(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N755(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N756(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N757(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N758(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N759(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N751(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N752(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N753(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N754(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N755(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N756(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N757(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N758(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N759(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N761(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N762(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N763(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N764(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N765(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N766(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N767(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N768(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N769(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N761(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N762(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N763(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N764(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N765(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N766(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N767(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N768(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N769(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N771(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N772(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N773(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N774(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N775(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N776(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N777(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N778(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N779(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N771(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N772(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N773(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N774(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N775(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N776(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N777(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N778(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N779(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N781(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N782(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N783(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N784(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N785(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N786(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N787(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N788(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N789(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N781(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N782(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N783(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N784(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N785(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N786(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N787(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N788(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N789(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N791(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N792(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N793(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N794(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N795(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N796(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N797(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N798(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N799(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N791(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N792(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N793(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N794(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N795(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N796(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N797(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N798(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N799(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N730(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N730(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N740(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N740(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N750(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N750(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N760(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N760(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N770(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N770(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N780(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N780(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N790(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N790(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N801(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N802(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N803(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N804(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N805(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N806(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N807(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N808(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N809(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N810(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N801(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N802(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N803(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N804(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N805(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N806(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N807(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N808(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N809(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N811(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N812(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N813(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N814(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N815(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N816(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N817(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N818(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N819(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N820(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N810(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N811(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N812(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N813(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N814(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N815(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N816(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N817(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N818(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N819(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N821(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N822(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N823(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N824(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N825(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N826(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N827(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N828(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N829(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N820(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N821(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N822(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N823(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N824(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N825(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N826(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N827(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N828(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N829(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N831(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N832(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N833(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N834(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N835(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N836(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N837(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N838(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N839(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N831(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N832(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N833(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N834(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N835(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N836(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N837(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N838(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N839(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N841(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N842(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N843(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N844(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N845(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N846(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N847(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N848(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N849(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N841(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N842(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N843(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N844(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N845(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N846(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N847(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N848(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N849(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N851(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N852(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N853(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N854(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N855(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N856(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N857(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N858(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N859(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N851(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N852(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N853(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N854(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N855(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N856(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N857(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N858(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N859(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N861(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N862(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N863(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N864(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N865(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N866(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N867(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N868(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N869(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N861(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N862(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N863(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N864(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N865(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N866(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N867(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N868(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N869(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N871(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N872(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N873(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N874(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N875(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N876(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N877(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N878(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N879(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N871(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N872(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N873(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N874(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N875(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N876(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N877(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N878(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N879(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N881(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N882(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N883(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N884(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N885(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N886(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N887(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N888(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N889(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N881(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N882(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N883(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N884(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N885(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N886(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N887(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N888(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N889(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N891(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N892(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N893(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N894(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N895(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N896(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N897(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N898(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N899(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N891(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N892(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N893(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N894(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N895(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N896(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N897(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N898(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N899(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N830(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N830(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N840(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N840(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N850(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N850(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N860(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N860(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N870(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N870(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N880(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N880(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N890(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N890(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N901(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N902(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N903(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N904(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N905(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N906(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N907(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N908(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N909(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N910(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N901(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N902(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N903(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N904(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N905(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N906(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N907(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N908(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N909(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N911(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N912(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N913(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N914(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N915(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N916(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N917(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N918(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N919(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

%table ns1N920(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N910(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N911(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N912(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N913(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N914(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N915(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N916(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N917(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N918(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N919(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N921(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N922(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N923(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N924(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N925(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N926(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N927(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N928(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N929(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N920(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N921(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N922(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N923(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N924(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N925(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N926(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N927(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N928(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N929(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N931(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N932(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N933(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N934(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N935(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N936(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N937(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N938(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N939(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N931(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N932(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N933(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N934(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N935(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N936(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N937(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N938(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N939(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N941(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N942(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N943(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N944(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N945(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N946(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N947(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N948(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N949(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N941(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N942(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N943(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N944(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N945(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N946(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N947(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N948(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N949(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N951(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N952(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N953(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N954(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N955(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N956(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N957(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N958(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N959(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N951(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N952(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N953(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N954(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N955(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N956(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N957(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N958(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N959(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N961(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N962(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N963(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N964(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N965(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N966(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N967(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N968(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N969(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N961(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N962(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N963(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N964(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N965(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N966(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N967(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N968(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N969(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N971(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N972(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N973(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N974(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N975(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N976(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N977(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N978(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N979(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N971(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N972(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N973(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N974(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N975(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N976(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N977(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N978(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N979(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N981(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N982(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N983(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N984(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N985(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N986(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N987(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N988(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N989(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N981(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N982(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N983(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N984(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N985(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N986(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N987(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N988(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N989(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

// 外部表
%table ns1N991(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N992(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N993(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N994(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N995(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N996(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N997(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

// 外部表
%table ns1N998(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}

ns1N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N991(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N992(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N993(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N994(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N995(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N996(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N997(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
ns1N998(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns1N930(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N930(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N940(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N940(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N950(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N950(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N960(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N960(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N970(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N970(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N980(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N980(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N990(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N990(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N200(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N200(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N300(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N300(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns1N400(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N400(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
%table ns1N500(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N500(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N600(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N600(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N700(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N700(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N800(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N800(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).

%table ns1N900(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte)
{ index(0(a,b)), external}
ns1N900(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}
