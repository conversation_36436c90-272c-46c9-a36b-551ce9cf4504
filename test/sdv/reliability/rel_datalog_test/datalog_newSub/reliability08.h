/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: reliability08.h
 * Description: tool
 * Author: youwanyong ywx1157510
 * Create: 2024-09-02
 */

#ifndef __RELIABILITY08_H__
#define __RELIABILITY08_H__

#include "t_datacom_lite.h"
#include "StructDatalogTable08.h"

#define FILE_PATH 512
#define MAX_CMD_SIZE 1024
#define MAX_NAME_LENGTH 512
#define STRING_LEN 10
#define STR_LEN 128
#define BYTE_1 1
#define BYTE_4 4
#define BYTE_128 128
GmcStmtT *g_stmtSync = NULL;
int32_t g_ret = GMERR_OK;

char g_outputDir[FILE_PATH] = "datalog_file";
typedef int (*FuncWrite)(GmcStmtT *stmt, void *t, bool isStateTableInput);
typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);
typedef int (*FuncRead)(GmcStmtT *stmt, void *t, int len, bool external, bool isResource);
typedef int (*FuncRead1)(GmcStmtT *stmt, uint8_t *dataCheckIndexes, bool isPubsubRsc);
typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count);
typedef int (*FuncReadSub)(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc);

char g_command[MAX_CMD_SIZE] = {0};
uint32_t g_writeCount = 20, g_sizeMalloc = 100;
enum DmlType { ALL = 1, NOBITFILED = 2, NOBITFILEDANDBYTE = 3, NOBITFILEDANDBYTEANDSR = 4 };
// 多线程控制数据写入结构
typedef struct {
    char *labelName;                             // 表名
    DmlType type;                                // 写哪种表
    int (*funcWrite)(GmcStmtT *, void *, bool);  // set字段函数
    int32_t index;                               // 从多少开始写
    int32_t num;                                 // 写入数量
} PthreadControlT;                               // 多线程控制数据写入结构

void SystemSnprintf(const char *format, ...)
{
    char command[MAX_CMD_SIZE] = {0};
    (void)memset(command, 0, sizeof(command));
    va_list p;
    va_start(p, format);
    (void)vsnprintf(command, MAX_CMD_SIZE, format, p);
    va_end(p);
    int32_t ret = system(command);
    if (ret) {
        AW_FUN_Log(LOG_INFO, "system cmd is excuted no ok !!!");
    }
}
// 启动服务
void StartServer()
{
#ifndef ENV_RTOSV2X
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");

    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxLockTimeOut=150000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogCallBackTimeoutThreshold=30\"");
#endif
    system("sh ${TEST_HOME}/tools/start.sh");
}

// 释放已申请的内存
void ToFreeAlloc(AllTypeTableStruct *objIn1, int recordNum = 100)
{
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4Buf);
    }
    free(objIn1);
    objIn1 = NULL;
}

void ToFreeAllocNoBitField(AllTypeTableExceptBitFieldStruct *objIn1, int recordNum = 100)
{
    for (int i = 0; i < recordNum; i++) {
        free(objIn1[i].a4Buf);
    }
    free(objIn1);
    objIn1 = NULL;
}
void ToFreeAllocNoBitFieldAndByte(AllTypeTableExceptBitfildAndByteStruct *objIn1, int recordNum = 100)
{
    free(objIn1);
    objIn1 = NULL;
}

void ToFreeAllocNoBitFieldAndByteAndStr(AllTypeTableExceptBitfildAndByteAndStrStruct *objIn1, int recordNum = 100)
{
    free(objIn1);
    objIn1 = NULL;
}

// 加载so文件
int LoadSoFile(const char *soName, bool isDistributed = false, const char *nsName = NULL)
{
    memset(g_command, 0, sizeof(g_command));
    if (isDistributed) {
        (void)snprintf(g_command, MAX_CMD_SIZE, "./%s/%s.so --distribute", g_outputDir, soName);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "./%s/%s.so", g_outputDir, soName);
    }
    if (nsName) {
        return TestLoadDatalog(g_command, nsName);
    } else {
        return TestLoadDatalog(g_command);
    }
}

// 建表
void CreateTestTable(
    const char *labelJsonPath, const char *configJson, const char *labelName, bool isNeedPartName = false)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "建%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)GmcDropVertexLabel(stmt, labelName);
    char *schema = NULL;
    readJanssonFile(labelJsonPath, &schema);
    EXPECT_NE((void *)NULL, schema);
    if (isNeedPartName) {
        char realSchema[2048] = "";
        (void)sprintf(realSchema, schema, labelName, labelName);
        ret = GmcCreateVertexLabel(stmt, realSchema, configJson);
    } else {
        ret = GmcCreateVertexLabel(stmt, schema, configJson);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 删表
int32_t DropTestTable(const char *labelName)
{
    int32_t ret = -1;
    GmcStmtT *stmt;
    GmcConnT *conn;
    AW_FUN_Log(LOG_STEP, "删%s表", labelName);
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_COMMON_STREAM_OVERLOAD);
    if (ret) {
        testGmcGetLastError();
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
// 含所有数据类型
int SetAllDataTypes(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    AllTypeTableStruct *obj = (AllTypeTableStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }
    uint8_t value1 = obj->a2;
    ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_BITFIELD8, &value1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a2: %d, ret = %d.", value1, ret);
        return ret;
    }

    uint16_t value2 = obj->b2;
    ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_BITFIELD16, &value2, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b2: %d, ret = %d.", value2, ret);
        return ret;
    }

    uint32_t value3 = obj->c2;
    ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_BITFIELD32, &value3, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] c2: %d, ret = %d.", value3, ret);
        return ret;
    }

    uint64_t value4 = obj->d2;
    ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_BITFIELD64, &value4, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] d2: %d, ret = %d.", value4, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3Buf, (strlen((char *)obj->a3Buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a3: %s, ret = %d.", obj->a3Buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_BYTES, obj->a4Buf, obj->a4Len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a4: %s, ret = %d.", obj->a4Buf, ret);
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 不含位域
int SetAllDataTypesExceptBitFields(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    AllTypeTableExceptBitFieldStruct *obj = (AllTypeTableExceptBitFieldStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableExceptBitFieldStruct] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableExceptBitFieldStruct] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3Buf, (strlen((char *)obj->a3Buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a3: %s, ret = %d.", obj->a3Buf, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_BYTES, obj->a4Buf, obj->a4Len);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] a4: %s, ret = %d.", obj->a4Buf, ret);
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] upgradeVersion: %d, ret = %d.", obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypes] dtlReservedCount: %d, ret = %d.", obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 不含位域和变长byte  // 级联输入表
int SetAllDataTypesExceptBitFieldsAndByte(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    AllTypeTableExceptBitfildAndByteStruct *obj = (AllTypeTableExceptBitfildAndByteStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_STRING, obj->a3Buf, (strlen((char *)obj->a3Buf)));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] a3: %s, ret = %d.", obj->a3Buf, ret);
        return ret;
    }

    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] upgradeVersion: %d, ret = %d.",
                obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] dtlReservedCount: %d, ret = %d.",
                obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}

// 不含位域和变长byte和str  // 资源表
int SetAllDataTypesExceptBitFieldsAndByteAndStr(GmcStmtT *stmt, void *t, bool isStateTableInput = false)
{
    AllTypeTableExceptBitfildAndByteAndStrStruct *obj = (AllTypeTableExceptBitfildAndByteAndStrStruct *)t;
    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(int8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableExceptBitfildAndByteAndStrStruct] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(int16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableExceptBitfildAndByteAndStrStruct] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(int32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[AllTypeTableExceptBitfildAndByteAndStrStruct] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &obj->d, sizeof(int64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] e: %u, ret = %d.", obj->e, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] f: %u, ret = %d.", obj->f, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] g: %u, ret = %d.", obj->g, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_UINT8, &obj->a1, sizeof(uint8_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] a1: %d, ret = %d.", obj->a1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_UINT16, &obj->b1, sizeof(uint16_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] b1: %d, ret = %d.", obj->b1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_UINT32, &obj->c1, sizeof(uint32_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] c1: %d, ret = %d.", obj->c1, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_UINT64, &obj->d1, sizeof(uint64_t));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] d1: %d, ret = %d.", obj->d1, ret);
        return ret;
    }

    if (!isStateTableInput) {
        ret = GmcSetVertexProperty(
            stmt, "upgradeVersion", GMC_DATATYPE_INT32, &obj->upgradeVersion, sizeof(obj->upgradeVersion));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] upgradeVersion: %d, ret = %d.",
                obj->upgradeVersion, ret);
            return ret;
        }

        ret = GmcSetVertexProperty(
            stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[SetAllDataTypesExceptBitFieldsAndByte] dtlReservedCount: %d, ret = %d.",
                obj->dtlReservedCount, ret);
            return ret;
        }
    }
    return ret;
}
int32_t g_dircount = 0;
template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK, bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    if (ret) {
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    TestLabelInfoT labelInfo = {(char *)labelName, 0, g_testNameSpace};
    for (int i = 0; i < objLen; i++) {
        if (isStruct) {
            ret = testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo);
        } else {
            ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        }
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecute(stmt);
            g_dircount = i;
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

// 生成byte变长字段
Status Generate_Bytes(uint8_t **bytes, uint32_t byteLen)
{
    uint8_t *byteArray = (uint8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}

// 设置写入值
uint32_t SetArrayValue(
    AllTypeTableStruct *objIn1, char strT[10], int recordNum = 100, uint32_t count = 1, int32_t startValue = 0)
{
    if ((recordNum - startValue) != 1) {
        memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    } else {
        memset(objIn1, 0, sizeof(AllTypeTableStruct));
    }
    int32_t index = 0;
    for (int i = startValue; i < startValue + recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[index].a = (i + 1) % 127;
            objIn1[index].b = (i + 1) % 32767;
            objIn1[index].c = i;
            objIn1[index].d = i + 40000;  // 预期数据40s内不会过期
            for (int j = 0; j < 1; j++) {
                objIn1[index].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[index].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[index].g[j] = (i + 1) % 50;
            }
            objIn1[index].a1 = (i + 1) % 255;
            objIn1[index].b1 = (i + 1) % 255;
            objIn1[index].c1 = (i + 1) % 255;
            objIn1[index].d1 = (i + 1) % 255;

            objIn1[index].a2 = (i + 1) % 255;
            objIn1[index].b2 = (i + 1) % 255;
            objIn1[index].c2 = (i + 1) % 255;
            objIn1[index].d2 = (i + 1) % 255;
        }
        objIn1[index].a3Len = (i + 1) % 255;
        objIn1[index].a4Len = (i + 1) % 255 + 3;
        objIn1[index].a3Len = 10;
        objIn1[index].a3Buf = strT;
        (void)snprintf((char *)objIn1[index].a3Buf, 10, "s%08d", (i + 1) % 10);
        int32_t ret = Generate_Bytes(&objIn1[index].a4Buf, objIn1[index].a4Len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[index].dtlReservedCount = count;
        objIn1[index].upgradeVersion = 0;
        index++;
    }
    return GMERR_OK;
}

// 设置写入值少位域
uint32_t SetArrayValueExpectBitField(AllTypeTableExceptBitFieldStruct *objIn1, char strT[10], int recordNum = 100,
    uint32_t count = 1, int32_t startValue = 0)
{
    if ((recordNum - startValue) != 1) {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitFieldStruct) * recordNum);
    } else {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitFieldStruct));
    }
    int32_t index = 0;
    for (int i = startValue; i < recordNum + startValue; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[index].a = (i + 1) % 127;
            objIn1[index].b = (i + 1) % 32767;
            objIn1[index].c = i;
            objIn1[index].d = i + 40000;  // 预期数据40s内不会过期
            for (int j = 0; j < 1; j++) {
                objIn1[index].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[index].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[index].g[j] = (i + 1) % 50;
            }
            objIn1[index].a1 = (i + 1) % 255;
            objIn1[index].b1 = (i + 1) % 255;
            objIn1[index].c1 = (i + 1) % 255;
            objIn1[index].d1 = (i + 1) % 255;
        }
        objIn1[index].a3Len = (i + 1) % 255;
        objIn1[index].a4Len = (i + 1) % 255 + 3;
        objIn1[index].a3Len = 10;
        objIn1[index].a3Buf = strT;
        (void)snprintf((char *)objIn1[index].a3Buf, 10, "s%08d", (i + 1) % 10);
        int32_t ret = Generate_Bytes(&objIn1[index].a4Buf, objIn1[index].a4Len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[index].dtlReservedCount = count;
        objIn1[index].upgradeVersion = 0;
        index++;
    }
    return GMERR_OK;
}

// 设置写入值少位域和byte
uint32_t SetArrayValueExpectBitFieldAndByte(AllTypeTableExceptBitfildAndByteStruct *objIn1, char strT[10],
    int recordNum = 100, uint32_t count = 1, int32_t startValue = 0)
{
    if ((recordNum - startValue) != 1) {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitfildAndByteStruct) * recordNum);
    } else {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitfildAndByteStruct));
    }
    int32_t index = 0;
    for (int i = startValue; i < recordNum + startValue; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[index].a = (i + 1) % 127;
            objIn1[index].b = (i + 1) % 32767;
            objIn1[index].c = i;
            objIn1[index].d = i + 40000;  // 预期数据40s内不会过期
            for (int j = 0; j < 1; j++) {
                objIn1[index].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[index].f[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[index].g[j] = (i + 1) % 50;
            }
            objIn1[index].a1 = (i + 1) % 255;
            objIn1[index].b1 = (i + 1) % 255;
            objIn1[index].c1 = (i + 1) % 255;
            objIn1[index].d1 = (i + 1) % 255;
        }
        objIn1[index].a3Len = (i + 1) % 255;
        objIn1[index].a3Len = 10;
        objIn1[index].a3Buf = strT;
        (void)snprintf((char *)objIn1[index].a3Buf, 10, "s%08d", (i + 1) % 10);
        objIn1[index].dtlReservedCount = count;
        objIn1[index].upgradeVersion = 0;
        index++;
    }
    return GMERR_OK;
}

// 设置写入值少位域和byte和str
uint32_t SetArrayValueExpectBitFieldAndByteAndStr(AllTypeTableExceptBitfildAndByteAndStrStruct *objIn1, char strT[10],
    int recordNum = 100, uint32_t count = 1, int32_t startValue = 0)
{
    if ((recordNum - startValue) != 1) {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitfildAndByteAndStrStruct) * recordNum);
    } else {
        memset(objIn1, 0, sizeof(AllTypeTableExceptBitfildAndByteAndStrStruct));
    }
    int32_t index = 0;
    for (int i = startValue; i < recordNum + startValue; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[index].a = (i + 1) % 127;
            objIn1[index].b = (i + 1) % 32767;
            objIn1[index].c = 1;
            objIn1[index].d = i + 40000;  // 预期数据40s内不会过期
            for (int j = 0; j < 1; j++) {
                objIn1[index].e[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[index].f[j] = 0;
            }
            objIn1[index].f[0] = 1;
            for (int j = 0; j < 256; j++) {
                objIn1[index].g[j] = 0;
            }
            objIn1[index].g[0] = 1;
            objIn1[index].a1 = (i + 1) % 255;
            objIn1[index].b1 = (i + 1) % 255;
            objIn1[index].c1 = 1;
            objIn1[index].d1 = 1;
        }
        objIn1[index].dtlReservedCount = count;
        objIn1[index].upgradeVersion = 0;
        index++;
    }
    return GMERR_OK;
}
bool g_isUseNameSpace = false;
int32_t g_countValue = 1;
void *InsertValueData(void *args)
{
    PthreadControlT begin = *(PthreadControlT *)args;
    int recordNum = begin.num;
    int32_t ret = -1;
    char strT[10] = {0};
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_isUseNameSpace) {
        // 切换namespace
        ret = GmcUseNamespace(stmt, "namespace1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (begin.type == ALL) {
        AllTypeTableStruct *objIn1;
        objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * (recordNum));
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        begin.funcWrite = SetAllDataTypes;
        ret = SetArrayValue((AllTypeTableStruct *)objIn1, strT, recordNum, g_countValue, begin.index);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (strcmp(begin.labelName, "N000") == 0) {
            ret = GmcUseNamespace(stmt, g_testNameSpace);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = writeRecord(conn, stmt, begin.labelName, objIn1, begin.num, begin.funcWrite, true, false, 0, true);
        } else {
            ret = writeRecord(conn, stmt, begin.labelName, objIn1, begin.num, begin.funcWrite, true);
        }
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, g_ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ToFreeAlloc(objIn1, recordNum);

        // 少位域
    } else if (begin.type == NOBITFILED) {
        AllTypeTableExceptBitFieldStruct *objIn1;
        objIn1 = (AllTypeTableExceptBitFieldStruct *)malloc(sizeof(AllTypeTableExceptBitFieldStruct) * (recordNum));
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        begin.funcWrite = SetAllDataTypesExceptBitFields;
        ret = SetArrayValueExpectBitField(
            (AllTypeTableExceptBitFieldStruct *)objIn1, strT, recordNum, g_countValue, begin.index);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecord(conn, stmt, begin.labelName, objIn1, begin.num, begin.funcWrite, true);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, g_ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ToFreeAllocNoBitField(objIn1, recordNum);

        // 少位域和byte
    } else if (begin.type == NOBITFILEDANDBYTE) {
        AllTypeTableExceptBitfildAndByteStruct *objIn1;
        objIn1 = (AllTypeTableExceptBitfildAndByteStruct *)malloc(
            sizeof(AllTypeTableExceptBitfildAndByteStruct) * (recordNum));
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        begin.funcWrite = SetAllDataTypesExceptBitFieldsAndByte;
        ret = SetArrayValueExpectBitFieldAndByte(
            (AllTypeTableExceptBitfildAndByteStruct *)objIn1, strT, recordNum, g_countValue, begin.index);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecord(conn, stmt, begin.labelName, objIn1, begin.num, begin.funcWrite, true);
        // 含服务端挂起超时
        ToFreeAllocNoBitFieldAndByte(objIn1, recordNum);
        // 少位域和byte和str
    } else {
        AllTypeTableExceptBitfildAndByteAndStrStruct *objIn1;
        objIn1 = (AllTypeTableExceptBitfildAndByteAndStrStruct *)malloc(
            sizeof(AllTypeTableExceptBitfildAndByteAndStrStruct) * (recordNum));
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        begin.funcWrite = SetAllDataTypesExceptBitFieldsAndByteAndStr;
        ret = SetArrayValueExpectBitFieldAndByteAndStr(
            (AllTypeTableExceptBitfildAndByteAndStrStruct *)objIn1, strT, recordNum, g_countValue, begin.index);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = writeRecord(conn, stmt, begin.labelName, objIn1, begin.num, begin.funcWrite, true);
        if (ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, g_ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ToFreeAllocNoBitFieldAndByteAndStr(objIn1, recordNum);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
template <typename StructObjT>
int arrycmp(const StructObjT *arry1, const StructObjT *arry2, int len)
{
    for (int i = 0; i < len; i++) {
        if (arry1[i] > arry2[i]) {
            return 1;
        } else if (arry1[i] < arry2[i]) {
            return -1;
        }
    }
    return 0;
}
int NewAllTypeTableCmp(
    const AllTypeTableStruct *st1, const AllTypeTableStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            return -1;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (st1->a2 != st2->a2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a2, st1: %d, st2: %d.", st1->a2, st2->a2);
            }
        }

        if (st1->b2 != st2->b2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] b2, st1: %d, st2: %d.", st1->b2, st2->b2);
            }
        }

        if (st1->c2 != st2->c2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] c2, st1: %d, st2: %d.", st1->c2, st2->c2);
            }
        }

        if (st1->d2 != st2->d2) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] d2, st1: %d, st2: %d.", st1->d2, st2->d2);
            }
        }

        if (arrycmp(st1->a3Buf, st2->a3Buf, st1->a3Len) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] a3, st1: %s, st2: %s.", st1->a3Buf, st2->a3Buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableCmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                        st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int NewAllTypeTableExceptBitFieldCmp(const AllTypeTableExceptBitFieldStruct *st1,
    const AllTypeTableExceptBitFieldStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            return -1;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (arrycmp(st1->a3Buf, st2->a3Buf, st1->a3Len) != 0) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] a3, st1: %s, st2: %s.", st1->a3Buf, st2->a3Buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int NewAllTypeTableExceptBitFieldAndByteCmp(const AllTypeTableExceptBitfildAndByteStruct *st1,
    const AllTypeTableExceptBitfildAndByteStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            return -1;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] e, st1: %d, st2: %d.", st1->e[0], st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] f, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] g, st1: %d, st2: %d.", st1->f[0], st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] a1, st1: %d, st2: %d.", st1->a1, st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] b1, st1: %d, st2: %d.", st1->b1, st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] c1, st1: %d, st2: %d.", st1->c1, st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] d1, st1: %d, st2: %d.", st1->d1, st2->d1);
            }
        }

        if (arrycmp(st1->a3Buf, st2->a3Buf, st1->a3Len) != 0) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] a3, st1: %s, st2: %s.", st1->a3Buf,
                    st2->a3Buf);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO,
                        "[NewAllTypeTableExceptBitFieldAndByteCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}

int NewAllTypeTableExceptBitFieldAndByteAndStrCmp(const AllTypeTableExceptBitfildAndByteAndStrStruct *st1,
    const AllTypeTableExceptBitfildAndByteAndStrStruct *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] a, st1: %d, st2: %d.", st1->a, st2->a);
            }
            return -1;
        }

        if (st1->b != st2->b) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] b, st1: %d, st2: %d.", st1->b, st2->b);
            }
        }

        if (st1->c != st2->c) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] c, st1: %d, st2: %d.", st1->c, st2->c);
            }
        }

        if (st1->d != st2->d) {
            if (isPrint) {
                AW_FUN_Log(
                    LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] d, st1: %d, st2: %d.", st1->d, st2->d);
            }
        }

        if (st1->e[0] != st2->e[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] e, st1: %d, st2: %d.", st1->e[0],
                    st2->e[0]);
            }
        }

        if (st1->f[0] != st2->f[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] f, st1: %d, st2: %d.", st1->f[0],
                    st2->f[0]);
            }
        }

        if (st1->g[0] != st2->g[0]) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] g, st1: %d, st2: %d.", st1->f[0],
                    st2->f[0]);
            }
        }

        if (st1->a1 != st2->a1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] a1, st1: %d, st2: %d.", st1->a1,
                    st2->a1);
            }
        }

        if (st1->b1 != st2->b1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] b1, st1: %d, st2: %d.", st1->b1,
                    st2->b1);
            }
        }

        if (st1->c1 != st2->c1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] c1, st1: %d, st2: %d.", st1->c1,
                    st2->c1);
            }
        }

        if (st1->d1 != st2->d1) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] d1, st1: %d, st2: %d.", st1->d1,
                    st2->d1);
            }
        }

        if (!isExternal) {
            if (st1->dtlReservedCount != st2->dtlReservedCount) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO,
                        "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] dtlReservedCount, st1: %d, st2: %d.",
                        st1->dtlReservedCount, st2->dtlReservedCount);
                }
            }
            if (st1->upgradeVersion != st2->upgradeVersion) {
                if (isPrint) {
                    AW_FUN_Log(LOG_INFO,
                        "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] upgradeVersion, st1: %d, st2: %d.",
                        st1->upgradeVersion, st2->upgradeVersion);
                }
            }
        }
        ret = 0;
    } while (0);

    return ret;
}
// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,uint1-uint8,uint1_8-uint8_64,str,byte
int NewTypeTableGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableStruct *checkObj = (AllTypeTableStruct *)t;
    AllTypeTableStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }
    uint8_t value1 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "a2", &value1, sizeof(uint8_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'a2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.a2 = value1;

    uint16_t value2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "b2", &value2, sizeof(uint16_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'b2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.b2 = value2;

    uint32_t value3 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "c2", &value3, sizeof(uint32_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'c2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.c2 = value3;

    uint64_t value4 = 0;
    ret = GmcGetVertexPropertyByName(stmt, "d2", &value4, sizeof(uint64_t), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'd2' fail, ret = %d.", ret);
        return ret;
    }
    getObj.d2 = value4;

    getObj.a3Len = checkObj[0].a3Len;
    getObj.a3Buf = (char *)malloc(getObj.a3Len * sizeof(char));
    if (getObj.a3Buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3Buf, getObj.a3Len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4Len = length;
    uint8_t *expectBValue = NULL;
    ret = Generate_Bytes(&expectBValue, length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4Buf = (uint8_t *)malloc(getObj.a4Len + 1);
    if (getObj.a4Buf == NULL) {
        free(expectBValue);
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a4", getObj.a4Buf, getObj.a4Len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (expectBValue) {
        ret = memcmp((char *)expectBValue, (char *)getObj.a4Buf, length);
        if (ret) {
            free(expectBValue);
            return -1;
        }
    } else {
        free(expectBValue);
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3Buf);
    free(getObj.a4Buf);
    return ret;
}

// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,str,byte
int NewTypeTableNoBitFieldGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableExceptBitFieldStruct *checkObj = (AllTypeTableExceptBitFieldStruct *)t;
    AllTypeTableExceptBitFieldStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    getObj.a3Len = checkObj[0].a3Len;
    getObj.a3Buf = (char *)malloc(getObj.a3Len * sizeof(char));
    if (getObj.a3Buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3Buf, getObj.a3Len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    uint32_t length = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "a4", &length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4Len = length;
    uint8_t *expectBValue = NULL;
    ret = Generate_Bytes(&expectBValue, length);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    getObj.a4Buf = (uint8_t *)malloc(getObj.a4Len + 1);
    if (getObj.a4Buf == NULL) {
        free(expectBValue);
        return -1;
    }
    bool eof = true;
    ret = GmcGetVertexPropertyByName(stmt, "a4", getObj.a4Buf, getObj.a4Len, &eof);
    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
    if (expectBValue) {
        ret = memcmp((char *)expectBValue, (char *)getObj.a4Buf, length);
        if (ret) {
            free(expectBValue);
            return -1;
        }
    } else {
        free(expectBValue);
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableExceptBitFieldCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableExceptBitFieldCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableExceptBitFieldCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3Buf);
    free(getObj.a4Buf);
    return ret;
}

// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256,str
int NewTypeTableNoBitFieldAndByteGet(GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableExceptBitfildAndByteStruct *checkObj = (AllTypeTableExceptBitfildAndByteStruct *)t;
    AllTypeTableExceptBitfildAndByteStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    getObj.a3Len = checkObj[0].a3Len;
    getObj.a3Buf = (char *)malloc(getObj.a3Len * sizeof(char));
    if (getObj.a3Buf == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    // 获取字符串
    ret = GmcGetVertexPropertyByName(stmt, "a3", getObj.a3Buf, getObj.a3Len, &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableExceptBitFieldAndByteCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableExceptBitFieldAndByteCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableExceptBitFieldAndByteCmp] count: %d.", getObj.dtlReservedCount);
    }
    // 释放内存
    free(getObj.a3Buf);
    return ret;
}

// a:int1, b:int2, c:int4, d:int64, e:byte1, f:byte128, g:byte256
int NewTypeTableNoBitFieldAndByteAndStrGet(
    GmcStmtT *stmt, void *t, int len, bool isExternal = false, bool isResource = false)
{
    AllTypeTableExceptBitfildAndByteAndStrStruct *checkObj = (AllTypeTableExceptBitfildAndByteAndStrStruct *)t;
    AllTypeTableExceptBitfildAndByteAndStrStruct getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d", &getObj.d, sizeof(getObj.d), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "e", getObj.e, sizeof(getObj.e), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'e' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "f", getObj.f, sizeof(getObj.f), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'f' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "g", getObj.g, sizeof(getObj.g), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'g' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'a1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b1", &getObj.b1, sizeof(getObj.b1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'b1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c1", &getObj.c1, sizeof(getObj.c1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'c1' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "d1", &getObj.d1, sizeof(getObj.d1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'd' fail, ret = %d.", ret);
        return ret;
    }

    if (!isExternal) {
        ret = GmcGetVertexPropertyByName(
            stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'dtlReservedCount' fail, ret = %d.", ret);
            return ret;
        }
        ret = GmcGetVertexPropertyByName(
            stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_ERROR, "[NewTypeTableNoBitFieldGet] get 'upgradeVersion' fail, ret = %d.", ret);
            return ret;
        }
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (NewAllTypeTableExceptBitFieldAndByteAndStrCmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    if (ret == -1) {
        (void)NewAllTypeTableExceptBitFieldAndByteAndStrCmp(&getObj, &checkObj[0], NULL, isExternal);
        AW_FUN_Log(LOG_ERROR, "[NewAllTypeTableExceptBitFieldAndByteAndStrCmp] count: %d.", getObj.dtlReservedCount);
    }
    return ret;
}

template <typename StructObjT>
int readRecord(const char *labelName, StructObjT *obj, int objLen, FuncRead func, bool isExternal = false,
    bool checkRecord = true, int *outCnt = NULL, bool isResource = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (g_isUseNameSpace) {
        ret = GmcUseNamespace(stmt, "namespace1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish || ret) {
            break;
        }
        ret = func(stmt, (void *)obj, objLen, isExternal, isResource);
        if ((checkRecord) && (ret)) {
            AW_FUN_Log(LOG_ERROR, "[readRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        cnt++;
    }
    int32_t ret1 = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    if ((cnt != objLen) || (tRet == -1)) {
        return cnt;
    }
    return ret;
}

// keyId删除数据
template <typename StructObjT>
int32_t DeletedataByPk(GmcStmtT *stmt, const char *labelName, StructObjT *t, int objLen)
{
    int32_t ret = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AllTypeTableExceptBitfildAndByteStruct *obj = (AllTypeTableExceptBitfildAndByteStruct *)t;
    int32_t upgradeVersion = 0;
    for (int i = 0; i < objLen; i++) {
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &(obj[i].a), sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &(obj[i].b), sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &(obj[i].c), sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret) {
            break;
        }
    }
    return ret;
}

// 设置64个字段值写入值
uint32_t SetSixtyFouthArrayValue(SixtyFouthTableStruct *objIn1, char strT[10], int recordNum = 100, uint32_t count = 1)
{
    memset(objIn1, 0, sizeof(SixtyFouthTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        for (int j = 0; j < 512; j++) {
            objIn1[i].a = (i + 1) % 127;
            objIn1[i].b = (i + 1) % 32767;
            objIn1[i].c = i;
            objIn1[i].d = (i + 1) % 255 + 40000;  // 预期数据40s内不会过期

            objIn1[i].a0 = (i + 1) % 127;
            objIn1[i].b0 = (i + 1) % 255;
            objIn1[i].c0 = i;
            objIn1[i].d0 = i;

            objIn1[i].a00 = (i + 1) % 127;
            objIn1[i].b00 = (i + 1) % 255;
            objIn1[i].c00 = i;
            objIn1[i].d00 = i;

            objIn1[i].a000 = (i + 1) % 127;
            objIn1[i].b000 = (i + 1) % 255;
            objIn1[i].c000 = i;
            objIn1[i].d000 = i;

            for (int j = 0; j < 1; j++) {
                objIn1[i].e[j] = (i + 1) % 50;
                objIn1[i].e0[j] = (i + 1) % 50;
                objIn1[i].e00[j] = (i + 1) % 50;
                objIn1[i].e000[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 128; j++) {
                objIn1[i].f[j] = (i + 1) % 50;
                objIn1[i].f0[j] = (i + 1) % 50;
                objIn1[i].f00[j] = (i + 1) % 50;
            }
            for (int j = 0; j < 256; j++) {
                objIn1[i].g[j] = (i + 1) % 50;
                objIn1[i].g0[j] = (i + 1) % 50;
                objIn1[i].g00[j] = (i + 1) % 50;
            }
            objIn1[i].a1 = (i + 1) % 255;
            objIn1[i].b1 = (i + 1) % 255;
            objIn1[i].c1 = (i + 1) % 255;
            objIn1[i].d1 = (i + 1) % 255;

            objIn1[i].e1 = (i + 1) % 255;
            objIn1[i].f1 = (i + 1) % 255;
            objIn1[i].g1 = (i + 1) % 255;
            objIn1[i].a2 = (i + 1) % 255;

            objIn1[i].a10 = (i + 1) % 255;
            objIn1[i].b10 = (i + 1) % 255;
            objIn1[i].c10 = (i + 1) % 255;
            objIn1[i].d10 = (i + 1) % 255;

            objIn1[i].e10 = (i + 1) % 255;
            objIn1[i].f10 = (i + 1) % 255;
            objIn1[i].g10 = (i + 1) % 255;
            objIn1[i].a20 = (i + 1) % 255;

            objIn1[i].a100 = (i + 1) % 255;
            objIn1[i].b100 = (i + 1) % 255;
            objIn1[i].c100 = (i + 1) % 255;
            objIn1[i].d100 = (i + 1) % 255;

            objIn1[i].e100 = (i + 1) % 255;
            objIn1[i].f100 = (i + 1) % 255;
            objIn1[i].g100 = (i + 1) % 255;
            objIn1[i].a200 = (i + 1) % 255;
        }
        objIn1[i].a3len = (i + 1) % 255;
        objIn1[i].a4len = (i + 1) % 255 + 3;
        objIn1[i].a3len = 10;
        objIn1[i].a3buf = strT;
        (void)snprintf((char *)objIn1[i].a3buf, 10, "s%08d", (i + 1) % 10);
        int32_t ret = Generate_Bytes(&objIn1[i].a4buf, objIn1[i].a4len);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        objIn1[i].a3len0 = (i + 1) % 255;
        objIn1[i].a4len0 = (i + 1) % 255 + 3;
        objIn1[i].a3len0 = 10;
        objIn1[i].a3buf0 = strT;
        (void)snprintf((char *)objIn1[i].a3buf0, 10, "s%08d", (i + 1) % 10);
        ret = Generate_Bytes(&objIn1[i].a4buf0, objIn1[i].a4len0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        objIn1[i].a3len00 = (i + 1) % 255;
        objIn1[i].a4len00 = (i + 1) % 255 + 3;
        objIn1[i].a3len00 = 10;
        objIn1[i].a3buf00 = strT;
        (void)snprintf((char *)objIn1[i].a3buf00, 10, "s%08d", (i + 1) % 10);
        ret = Generate_Bytes(&objIn1[i].a4buf00, objIn1[i].a4len00);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        objIn1[i].dtlReservedCount = count;
        objIn1[i].upgradeVersion = 0;
    }
    return GMERR_OK;
}
void RelCheckAlarmStatus()
{
    GmcConnT *connSync;
    GmcStmtT *stmtSync;
    GmcAlarmIdE AlarmId = GMC_ALARM_ASYNC_CONN_RING;
    GmcAlarmDataT testAlarmData;
    memset(&testAlarmData, 0, sizeof(testAlarmData));
    int32_t ret = 1;
    AW_FUN_Log(LOG_INFO, "获取告警日志");
    ret = TestYangGmcConnect(&connSync, &stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetAlarmData(stmtSync, AlarmId, &testAlarmData);
#ifdef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(GMC_ALARM_STATUS, testAlarmData.alarmStatus);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
#endif
    ret = testGmcDisconnect(connSync, stmtSync);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
bool g_isNeedSleep = false;
bool g_blockEpoll = false;

// 异步批量写需要sleep
void BatchExecuteCallBackWithSleep(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    if (g_isNeedSleep) {
        sleep(3);
    }
    batch_execute_callback(userData, batchRet, status, errMsg);
}
void InsertVertexCallBackWithSleep(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    if (g_isNeedSleep) {
        sleep(3);
    }
    insert_vertex_callback(userData, affectedRows, status, errMsg);
}
template <typename StructObjT>
int writeRecordAsyncWithOutWait(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen,
    FuncWrite func, AsyncUserDataT *dataRev, bool isBatch = true, bool isStruct = false, int res1 = GMERR_OK,
    bool isStateTabelInput = false)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AllTypeTableStruct *objIn = (AllTypeTableStruct *)obj;
    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = dataRev;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (int i = 0; i < objLen; i++) {
        ret = func(stmt, (void *)(obj + i), isStateTabelInput);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = GmcExecuteAsync(stmt, &insertRequestCtx);
            if (g_isNeedSleep && !ret) {
            } else {
                if (ret) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
                    AW_FUN_Log(LOG_INFO, "************************i = %d****************", i);
                    system("gmsysview alarm ");
                    RelCheckAlarmStatus();
                    g_dircount = i;
                    i = objLen;
                }
            }
            if (ret) {
                ret = (ret == res1) ? GMERR_OK : ret;
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, res1 = %d, ret = %d.", res1, ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = GmcBatchExecuteAsync(batch, BatchExecuteCallBackWithSleep, dataRev);
        EXPECT_EQ(GMERR_OK, ret);
        if (g_blockEpoll) {
        } else {
            ret = testWaitAsyncRecv(dataRev);
            if (dataRev->status != GMERR_OK) {
                ret = dataRev->status;
            } else {
                EXPECT_EQ(GMERR_OK, dataRev->status);
                AW_MACRO_EXPECT_EQ_INT(objLen, dataRev->totalNum);
                AW_MACRO_EXPECT_EQ_INT(objLen, dataRev->succNum);
            }
        }
        if (ret) {
            ret = (ret == res1) ? GMERR_OK : ret;
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, res1 = %d, ret = %d.", res1, ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

#endif /* __RELIABILITY08_H__ */
