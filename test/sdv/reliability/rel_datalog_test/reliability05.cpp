/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2线程. All rights reserved.
 Description  : 基础可靠性测试 datalog异步接口
 Notes        :
 History      :
 Author       : youwanyong/ywx1157510
 Create       :
*****************************************************************************/
// upgradeVersion的增加仅和关联topo的最高版本一致,仅用于新老数据区分和版本控制
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/resource.h>
#include "UniversalTools.h"
#include "t_datacom_lite.h"

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024)
#define MINBYTE (1024 * 1024)
#endif

using namespace std;

class reliability05 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        SystemSnprintf("mkdir -p /root/_datalog_/");
        SystemSnprintf("chmod -R 777 /root/_datalog_/");
    }
    static void TearDownTestCase()
    {}
};

void reliability05::SetUp()
{
    AW_CHECK_LOG_BEGIN(0);
}
void reliability05::TearDown()
{
    AW_CHECK_LOG_END();
    SystemSnprintf("rm -rf 777 /root/_datalog_/");
}

// 1。异步消息堆积告警，在euler能否触发，以及如何触发
// 2.异步连接数据，如何能够写入
/* ****************************************************************************
 Description  : 001.异步消息队列满，阻塞回调，写数据直到异步消息队列满，触发告警异步消息堆积告警
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    int32_t ret = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.msgQueueSize = 64;
    ret = TestYangGmcConnect(&asyncConn, &asyncStmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建异步连接
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAlarmIdE alarmId = GMC_ALARM_ASYNC_CONN_RING;
    GmcAlarmDataT alarmData = {};
    ret = GmcGetAlarmData(syncStmt, alarmId, &alarmData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(asyncStmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    // 写数据
    int recordNum = 3000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }

        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = i + 1;
        objIn1[i].upgradeVersion = 0;
    }
    g_isNeedSleep = true;
    // 写表inpNormal recordNum条数据
    AsyncUserDataT *dataRev = {0};
    ret = writeRecordAsyncWithOutWait(
        asyncConn, asyncStmt, "nsRel.inpNormal", objIn1, recordNum, AllTypeTableSet, dataRev, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
    sleep(10);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabelAsync(asyncStmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testGmcDisconnect(asyncConn, asyncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

// datalog异步的处理方式
/* ****************************************************************************
 Description  : 002.异步写数据失败，导致订阅推送回滚，写数据不接收，接收订阅消息预期订阅消息回滚
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_002)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal005.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 70;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 写输入表2,外部表超限
    int num2 = 70;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    AW_FUN_Log(LOG_INFO, "write end");
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, num1 * num2 * 2, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :
003.异步写数据，阻塞订阅回调，写数据不接收，订阅通道满预期写数据失败，(订阅通道满还是无法构造该场景下和同步场景一致)
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_003)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写输入表2
    AW_FUN_Log(LOG_STEP, "写输入表2");
    int num2 = 100;

    // 阻塞订阅回调不接收预期写入数据订阅超时
    g_isNeedSubSleep = true;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    AW_FUN_Log(LOG_STEP, "订阅接收");
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 200, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 4 32k大对象可靠订阅, 回调sleep(60), 写数据直到返回16006, 当前场景无法构造，datalog异步场景为
创建新的订阅连接, 预期建连失败, 待内存资源释放创建1000个订阅连接成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_004)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 100000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev1 = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev1;
    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写输入表2
    AW_FUN_Log(LOG_STEP, "写输入表2");
    int num2 = 100000;

    // 阻塞订阅回调不接收预期写入数据订阅超时
    g_isNeedSubSleep = true;
    AsyncUserDataT *dataRev = {0};
    ret = writeRecordAsyncWithOutWait(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, dataRev, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
    GmcConnT *subConnUpdate1[MAX_SUB_CONN_SIZE];
    GmcStmtT *subStmtUpdate1[MAX_SUB_CONN_SIZE];
    char subConnName1[20] = "testSubUpdate1";
    YangConnOptionT subConnOptions1 = {0};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    // 预留一个读连接和一个卸载so工具连接
    for (int32_t i = 0; i < MAX_SUB_CONN_SIZE - existConnNum - 2; i++) {
        (void)sprintf(subConnName1, "testSubUpdate1%d", i);
        subConnOptions1.connName = subConnName1;
        ret = TestYangGmcConnect(&subConnUpdate1[i], &subStmtUpdate1[i], GMC_CONN_TYPE_SUB, &subConnOptions1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "connect %dth is failed ret = %d", i, ret);
        }
    }

    AW_FUN_Log(LOG_STEP, "订阅接收");
    sleep(4);
    g_isNeedSubSleep = false;
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 200, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(20);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    AW_FUN_Log(LOG_INFO, "卸载so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_INFO, "删除外部表");
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "断开所有订阅连接");
    for (int32_t i = 0; i < MAX_SUB_CONN_SIZE - existConnNum - 2; i++) {
        ret = testGmcDisconnect(subConnUpdate1[i], subStmtUpdate1[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, "断开异步连接");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "清理环境");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

void *ThreadAsyncWrite(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWrite027(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

// block1 
void *ThreadAsyncWrite12002(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

// block0
void *ThreadAsyncWriteBlock0(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

// block0
void *ThreadAsyncWriteBlock012002(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_LOCK_NOT_AVAILABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWriteBatch(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWriteSameDataFail(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1 + index) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
    if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GMERR_PRIMARY_KEY_VIOLATION);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
    }
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWriteSameDataSucc(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1 + index) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
    }
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWriteSameDataBatchFail(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1 + index) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, 20, AllTypeTableSet, &insertRequestCtx, true);
    if (ret == GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GMERR_PRIMARY_KEY_VIOLATION);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
    }
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadAsyncWriteSameDataBatchSucc(void *args)
{
    GmcStmtT *stmt;
    GmcConnT *conn;
    AsyncUserDataT data = {0};
    int32_t ret = 1;
    int index = *(int *)args;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1 + index) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(
        conn, stmt, "nsRel.inpNormal", objIn1, 20, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
    }
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn1);
}

void *ThreadSyncWrite(void *args)
{
    // 建连
    GmcStmtT *stmt;
    GmcConnT *conn;
    int32_t ret = 1;
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int index = *(int *)args;
    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 写表inpNormal
    int num1 = index * 20;

    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    ret = writeRecord(conn, stmt, "nsRel.inpNormal", objIn1 + index * 20, 20, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, "nsRel.inpState", objIn1, 20, AllTypeTableSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断连
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  : 005.开启100个线程50个线程建异步连接，50个线程建同步连接，并发写相同数据，
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_005)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_001";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = 10;
        if (i % 2 == 0) {
            // 同步线程写相同数据
            pthread_create(&thr_arr[i], NULL, ThreadSyncWrite, (void *)&index[i]);
        } else {
            // 异步线程写相同数据
            pthread_create(&thr_arr[i], NULL, ThreadAsyncWrite, (void *)&index[i]);
        }
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 006.开启100个线程50个线程建异步连接，50个线程建同步连接，并发写不同数据
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_006)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_001";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        if (i % 2 == 0) {
            // 同步线程写相同数据
            pthread_create(&thr_arr[i], NULL, ThreadSyncWrite, (void *)&index[i]);
        } else {
            // 异步线程写相同数据
            pthread_create(&thr_arr[i], NULL, ThreadAsyncWrite, (void *)&index[i]);
        }
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 2000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 40000, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, 2000, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 2000, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 007.满规格批写数据直到内存满，更新，删除，
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_007)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 写输入表2
    int num2 = 60;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

char g_dirName[]={'/','\n'};
// 时钟跳变
void *TimeDumpForwardThread(void *arg)
{
    char cmd[100] = "";
    (void)snprintf(cmd,100,"%croot/CFE_Tool/cfe/cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (+,60)\"", g_dirName[0]);
    for (int i = 0; i < 5; i++) {
        system(cmd);
        sleep(1);
    }
    sleep(3);
    (void)snprintf(cmd,100,"%croot/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=+ and OFFSET=300\"", g_dirName[0]);
    system(cmd);
}
void *TimeDumpBackwardThread(void *arg)
{
    char cmd[100] = "";
    (void)snprintf(cmd,100,"%croot/CFE_Tool/cfe/cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (-,60)\"", g_dirName[0]);
    for (int i = 0; i < 5; i++) {
        system(cmd);
        sleep(1);
    }
    sleep(3);
    (void)snprintf(cmd,100,"%croot/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=- and OFFSET=300\"", g_dirName[0]);
    system(cmd);
}
/* ****************************************************************************
 Description  : 008.时钟跳变，时钟往前跳变，预期不超时、
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_008)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};

     // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = 10;
        if (i % 2 == 1) {
            // 异步线程写数据
            pthread_create(&thr_arr[i], NULL, ThreadAsyncWrite, (void *)&index[i]);
        } else {
            // 时钟往前跳变
            pthread_create(&thr_arr[i], NULL, TimeDumpForwardThread, NULL);
        }
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 20;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 009.时钟跳变，时钟往后跳变，预期正常超时
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_009)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写输入表2
    AW_FUN_Log(LOG_STEP, "写输入表2");
    int num2 = 100;

    // 时钟往后跳变
    int32_t threadNum = 1;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    for (int i = 0; i < threadNum; i++) {
        pthread_create(&thr_arr[i], NULL, TimeDumpBackwardThread, NULL);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 阻塞订阅回调不接收预期写入数据订阅超时
    g_isNeedSubSleep = true;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    AW_FUN_Log(LOG_STEP, "订阅接收");
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 200, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 010.异步删除已绑定so外部表删除成功，
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_010)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 异步删除外部表成功
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    
    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_STEP, "写输入表1失败");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num1, AllTypeTableSet, &insertRequestCtx, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("outExternal", objIn1, 0, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 外部表被删除，卸载so成功
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 011.异步删除so中已存在表，建表失败
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_011)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

       // 异步删除外部表成功
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    DatalogCreateExternalTable(labelInfoPath);

    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 卸载so成功
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 异步删除外部表成功
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
void *ThreadLoadSo(void *args){
    int32_t ret = 1;
    char nsName1[128] = "DataLog_reliability_005_012";
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
}

void *ThreadDropTable(void *args){
    int32_t ret = 1;
    AsyncUserDataT data{0};
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
/* ****************************************************************************
 Description  : 012.so中init函数中sleep3s，加载so   1s后异步创建so中同名表，预期so加载失败，删除同名表加载成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_012)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_012";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = 10;
        if (i % 2 == 0) {
            // 加载so
            pthread_create(&thr_arr[i], NULL, ThreadLoadSo, (void *)&index[i]);
        } else {
            // 延迟1s异步删除外部表
            pthread_create(&thr_arr[i], NULL, ThreadDropTable, (void *)&index[i]);
        }
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    DatalogCreateExternalTable(labelInfoPath);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 卸载so成功
    ret = GmcUnSubscribe(syncStmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 异步删除外部表成功
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 013_1.批写产生大量数据
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_013_1)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_001";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_INFO,"写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2");
    int num2 = 60;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024)
#define MINBYTE (1024 * 1024)
#endif
/* ****************************************************************************
 Description  : 013.系统动态内存不足,预置数据后，占用系统动态内存，内存不足写入失败，更新失败，删除失败，恢复内存后，所有DML操作正常
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_013)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_013";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = i%5;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_INFO,"写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2");
    int num2 = 10;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, num2*num1, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num2 = 25;
    // 占用系统内存
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 18000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0; // 实际申请内存次数
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }
    AW_FUN_Log(LOG_INFO,"actual alloc time is %d", count);
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1+1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    num2 = 10;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, num2, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record nsRel.inpNormal");

    // 二级索引更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, 1, AllTypeTableUpdateSecondSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 二级索引删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSecondDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     // 释放消耗掉的内存
     AW_FUN_Log(LOG_STEP, "free -h");
      system("free -h");
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }
    num1 = 1024;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写输入表2
    AW_FUN_Log(LOG_INFO, "写输入表2成功");
    num2 = 25;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1 + 10, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, num2, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 014.db动态内存不足,预置数据后，占用系统动态内存，内存不足写入失败，更新失败，删除失败，恢复内存后，所有DML操作正常
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_014)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_013";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_INFO,"写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 占用系统内存
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 18000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0; // 实际申请内存次数
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }
    AW_FUN_Log(LOG_INFO,"actual alloc time is %d", count);

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2失败");
    int num2 = 20;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, 0, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     // 释放消耗掉的内存
     AW_FUN_Log(LOG_STEP, "free -h");
      system("free -h");
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2成功");
    num2 = 1;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, num2, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 015.系统共享内存不足 预置数据后，占用系统共享内存，内存不足写入失败，更新失败，删除失败，恢复内存后，所有DML操作正常
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_015)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_013";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_INFO,"写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 占用系统内存
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 18000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0; // 实际申请内存次数
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }
    AW_FUN_Log(LOG_INFO,"actual alloc time is %d", count);

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2失败");
    int num2 = 20;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, 0, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     // 释放消耗掉的内存
     AW_FUN_Log(LOG_STEP, "free -h");
      system("free -h");
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2成功");
    num2 = 1;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, num2, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 016.db共享内存不足,预置数据后，占用db共享内存，内存不足写入失败，更新失败，删除失败，恢复内存后，所有DML操作正常
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_016)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_013";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 1024;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    AW_FUN_Log(LOG_INFO,"写输入表1");
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待服务端将消息全部处理完毕
    sleep(5);

    // 占用系统内存
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 18000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0; // 实际申请内存次数
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }
    AW_FUN_Log(LOG_INFO,"actual alloc time is %d", count);

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2失败");
    int num2 = 20;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, 0, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 主键删除表1
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableDelelteSet, &deleteRequestCtx, true,false,0,false,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    num1 = 0;
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

     // 释放消耗掉的内存
     AW_FUN_Log(LOG_STEP, "free -h");
      system("free -h");
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }

    // 写输入表2
    AW_FUN_Log(LOG_INFO,"写输入表2成功");
    num2 = 1;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpState", objIn1, num2, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpState", objIn1, num2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outExternal", objIn1, num2, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 017.非可更新表,开启一百个线程，线程内异步单写相同数据，预期其中一个线程能够写成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_017)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_017";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        // 异步线程写相同数据
        pthread_create(&thr_arr[i], NULL, ThreadAsyncWriteSameDataFail, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 018.非可更新表,开启一百个线程，线程内异步批写相同数据，预期其中一个线程能够写成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_018)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_017";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        // 异步线程写相同数据
        pthread_create(&thr_arr[i], NULL, ThreadAsyncWriteSameDataBatchFail, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 019.可更新表,开启一百个线程，线程内异步单写相同数据，预期所有线程都能够写成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_019)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_001";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        // 异步线程写相同数据
        pthread_create(&thr_arr[i], NULL, ThreadAsyncWriteSameDataSucc, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 020.可更新表,开启一百个线程，线程内异步批写相同数据，预期所有线程都能够写成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_020)
{
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "DataLog_reliability_005_001";
    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 多线程并发
    int32_t threadNum = 100;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        // 异步线程写相同数据
        pthread_create(&thr_arr[i], NULL, ThreadAsyncWriteSameDataBatchSucc, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 400, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.outNormal", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("nsRel.inpState", objIn1, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(syncStmt, "outExternal");
    EXPECT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);
    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

// 只支持单个请求的更新，不支持批量的更新
/* ****************************************************************************
 Description  : 021.批量DML并发 同一个请求，批写100条，批删100条，批更新100条，批写相同数据，预期操作均成功，数据写入成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_021)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_017";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, 200, AllTypeTableSet, &insertRequestCtx, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     ret = executeCommand((char*)"gmsysview record nsRel.inpNormal  |grep '\"dtlReservedCount\": 2'|wc -l", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system("gmsysview record nsRel.inpNormal  |grep '\"dtlReservedCount\": 2'|wc -l");
    }

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 022.批量DML并发 同一个请求，批写100条，批更新100条， 批删100条，批写相同数据，预期操作均成功，数据写入成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_022)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_017";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }
    // 写表inpNormal
    int num1 = 100;
    AsyncUserDataT dataRev = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = InsertVertexCallBackWithSleep;
    insertRequestCtx.userData = &dataRev;
    // 写输入表1
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, 200, AllTypeTableSet, &insertRequestCtx, true,GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
     ret = executeCommand((char*)"gmsysview record nsRel.inpNormal  |grep '\"dtlReservedCount\": 2'|wc -l", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 主键更新表1
     for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 3) % 127;
    }
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.insertCb = update_vertex_callback;
    updateRequestCtx.userData = &dataRev;
    ret = writeRecordAsync(conn, stmt, "nsRel.inpNormal", objIn1, num1, AllTypeTableUpdateSet, &updateRequestCtx, false,false,0,false,GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.inpNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1, num1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

void *ThreadLoadUpgradeSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load upgrade so test start.");
    const char *patchSoName = (char *)args;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadUpgradeDatalog(patchSoName));
    AW_FUN_Log(LOG_STEP, "load upgrade so test end.");
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
}

void *ThreadLoadRollBackSo(void *args)
{
    AW_FUN_Log(LOG_STEP, "load rollback so test start.");
    const char *patchSoName = (char *)args;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(patchSoName));
    AW_FUN_Log(LOG_STEP, "load rollback so test end.");
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
}

/* ****************************************************************************
 Description  : 023.升降级并发:配置项为不支持并发，异步写数据并发升级so,预期写入数据失败block，（block为1新增表和已有的表不能做join)
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_023)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_023";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(patchSoName, "./%s_patchV2", nsName1);

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    // 预置数据
    index[0] = 10;
    pthread_create(&thr_arr[0], NULL, ThreadAsyncWrite, (void *)&index[0]);

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    system("rm -rf /root/_datalog_/funRunLog.txt");
    // 升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    // 写表失败，锁不可用
    pthread_create(&thr_arr[1], NULL, ThreadAsyncWrite12002, (void *)&index[0]);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期升级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023", "BLOCK_MODE: 1","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023");
    }
    
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 024.升降级并发：配置项为支持并发，异步写数据并发升级so,预期写入数据成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_024)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    // out table
    char outNormal[] = "outNormal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_024";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(patchSoName, "./%s_patchV2", nsName1);

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    // 预置数据
    index[0] = 10;
    pthread_create(&thr_arr[0], NULL, ThreadAsyncWriteBlock0, (void *)&index[0]);

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    system("rm -rf /root/_datalog_/funRunLog.txt");
    // 升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    // 写表
    pthread_create(&thr_arr[1], NULL, ThreadAsyncWriteBlock012002, (void *)&index[0]);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期升级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024", "BLOCK_MODE: 0","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024");
    }
    
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 025.升降级并发：配置项为不支持并发，异步写数据并发降级so,预期写入数据失败
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_025)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_023";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(patchSoName, "./%s_patchV2", nsName1);

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    // 预置数据
    index[0] = 10;
    pthread_create(&thr_arr[0], NULL, ThreadAsyncWrite, (void *)&index[0]);

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    system("rm -rf /root/_datalog_/funRunLog.txt");
    // 升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期升级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023", "VERSION: [v0.0.0]->[v1.0.0]","BLOCK_MODE: 1","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023");
    }
   
    // 降级so
    (void)sprintf(patchSoName, "./datalog_file/%s_rollbackV2.so", nsName1);
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollBackSo, (void *)patchSoName);
     sleep(1);
    // 写表失败，锁不可用
    pthread_create(&thr_arr[1], NULL, ThreadAsyncWrite12002, (void *)&index[0]);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期降级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023", "VERSION: [v1.0.0]->[v0.0.0]","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_023");
    }
    
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 026.升降级并发：配置项为支持并发，异步写数据并发降级so,预期写入数据成功
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_026)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_024";
    if (g_runMode == 0) {
        AW_FUN_Log(LOG_INFO, "runmode= 0");
    } else if (g_runMode == 1) {
        AW_FUN_Log(LOG_INFO, "runmode= 1");
    }

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 1) % 127;
        objIn1[i].b = (i + 1) % 32766;
        objIn1[i].c = i + 1;
        objIn1[i].d = i + 1;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(patchSoName, "./%s_patchV2", nsName1);

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    // 预置数据
    index[0] = 10;
    pthread_create(&thr_arr[0], NULL, ThreadAsyncWriteBlock0, (void *)&index[0]);

    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    system("rm -rf /root/_datalog_/funRunLog.txt");
    // 升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    for (int i = 0; i < 1; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期升级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024", "VERSION: [v0.0.0]->[v1.0.0]","BLOCK_MODE: 1","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024");
    }
    sleep(1);
    // 降级so
    (void)sprintf(patchSoName, "./datalog_file/%s_rollbackV2.so", nsName1);
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollBackSo, (void *)patchSoName);
    // 写表失败，锁不可用
    pthread_create(&thr_arr[1], NULL, ThreadAsyncWriteBlock012002, (void *)&index[0]);

    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
    sleep(120);
    // 预期降级成功
    ret = executeCommand(
        (char *)"gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024", "VERSION: [v1.0.0]->[v0.0.0]","PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if(ret){
        system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_005_024");
    }
    
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 027.异步并发写不同so，直到内存满
**************************************************************************** */
TEST_F(reliability05, DataLog_reliability_005_027)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = 1;
    GmcConnT *conn;
    GmcStmtT *stmt;

    AsyncUserDataT data{0};
    memset(&data, 0, sizeof(AsyncUserDataT));
    // inp table
    char inpNormal[] = "inpNormal";
    char inpState[] = "inpState";
    // mid table
    char midField[] = "midField";
    char res1[] = "res1";
    char midState[] = "midState";
    // out table
    char outNormal[] = "outNormal";
    char outExternal[] = "outExternal";

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName1[128] = "DataLog_reliability_005_024";
    char nsName2[128] = "DataLog_reliability_005_027";

    // 创建外部表
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(stmt, g_testNameSpace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);

    char labelInfoPath[40] = "./datalog_file/outExternal.gmjson";
    DatalogCreateExternalTable(labelInfoPath);

    // 创建订阅
    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    YangConnOptionT subConnOptions = {0};
    subConnOptions.connName = subConnName;
    ret = TestYangGmcConnect(&subConnUpdate, &subStmtUpdate, GMC_CONN_TYPE_SUB, &subConnOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/outPusub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    userDataUpdate = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userDataUpdate->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((userDataUpdate)->data, 0, sizeof(SnUserDataT));
    userDataUpdate->funcType = 0;
    userDataUpdate->tableType = 0;
    userDataUpdate->readIdFunc = DoubleInt4_getId;
    userDataUpdate->startid = 0;
    userDataUpdate->endid = 1;
    userDataUpdate->count = 1;
    GmcConnT *syncConn;
    GmcStmtT *syncStmt;
    ret = TestYangGmcConnect(&syncConn, &syncStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(syncStmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "1.load so");
    TestUninstallDatalog(nsName1, NULL, false);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    TestUninstallDatalog(nsName2, NULL, false);
    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    ret = GmcSubscribe(syncStmt, &tmp_sub_infoUpdate, subConnUpdate, snCallback, userDataUpdate);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_infoUpdate);

    // 写数据
    int recordNum = 2846;
    char strT[10] = {0};
    AllTypeTableStruct *objIn1 = (AllTypeTableStruct *)malloc(sizeof(AllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(AllTypeTableStruct) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = (i + 11) % 127;
        objIn1[i].b = (i + 11) % 32766;
        objIn1[i].c = i + 11;
        objIn1[i].d = i + 11;
        objIn1[i].e[0] = (i + 1) % 10;
        for (int j = 0; j < 128; j++) {
            objIn1[i].f[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 256; j++) {
            objIn1[i].g[j] = (i + 1) % 10;
        }
        for (int j = 0; j < 512; j++) {
            objIn1[i].h[j] = (i + 1) % 10;
        }
        objIn1[i].iLen = 10;
        objIn1[i].i = strT;
        (void)snprintf((char *)objIn1[i].i, 10, "s%08d", (i + 1) % 10);
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    // 多线程并发
    int32_t threadNum = 2;
    pthread_t thr_arr[threadNum];
    void *thr_ret[threadNum];
    int index[threadNum];

    index[0] = 10;
    // 写so1
    pthread_create(&thr_arr[0], NULL, ThreadAsyncWriteBlock0, (void *)&index[0]);
    // 写so2
    pthread_create(&thr_arr[1], NULL, ThreadAsyncWrite027, (void *)&index[0]);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

     ret = readRecord("nsRel.inpNormal",  objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("nsRel.outNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord("inpNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord("outNormal", objIn1 + 10 * 20, 20, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    
    ret = TestUninstallDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(stmt, "outExternal", drop_vertex_label_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataUpdate->data);
    free(userDataUpdate);

    ret = testGmcDisconnect(syncConn, syncStmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
