/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2线程. All rights reserved.
 Description  : 基础可靠性测试 热补丁
 Notes        :
 History      :
 Author       : youwanyong/ywx1157510
 Create       :
*****************************************************************************/
// upgradeVersion的增加仅和关联topo的最高版本一致,仅用于新老数据区分和版本控制
#include <sys/ipc.h>
#include <sys/shm.h>
#include <sys/resource.h>
#include "yreliability.h"
#include "PessimisticSerializable.h"
#include "t_datacom_lite.h"

// 内存申请大小限制
#if defined ENV_RTOSV2X
#define MEGABYTE (10 * 1024)
#else
#define MEGABYTE (1024 * 1024)
#define MINBYTE (1024 * 1024)
#endif

using namespace std;

class reliability01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("mkdir -p /root/_datalog_/");
        system("chmod -R 777 /root/_datalog_/");
    }
    static void TearDownTestCase()
    {
        system("rm -rf /root/_datalog_/");
    }
};

void reliability01::SetUp()
{
    AW_CHECK_LOG_BEGIN(0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void reliability01::TearDown()
{
    AW_CHECK_LOG_END();
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
}

#define MAX_CMD_SIZE 1024
// 当前仅满足1000表规格(字段63，索引31）
/* ****************************************************************************
 Description  : 001.so升降级时共享不足，升降级失败,释放内存后升级成功，降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    // 当前内核共享内存能够申请到的共享内存上限存在波动，怀疑存在共享内存未释放
    AW_FUN_Log(LOG_STEP, "**********before 10s***************");
    system("ipcs");
    
    sleep(10);
    AW_FUN_Log(LOG_STEP, "************after 10s*****************");
    system("ipcs");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 0");
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 80;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_001";
    char nsName2[128] = "DataLog_reliability_004_001_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_001_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 写数据后占用全部共享内存直到共享内存满
    // 加载so后申请共享内存
    int32_t allocTimes = 5001;

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    int shmid[allocTimes];
    key_t key[allocTimes];
    for (int i = 0; i < allocTimes; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    for (int i = 0; i < allocTimes; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        if (shmid[i] == -1) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "count is %d\n", count);
        }

        count++;
    }
    AW_FUN_Log(LOG_INFO, "count is %d\n", count);
    // 升级时写入数据失败
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    sleep(10);
    bool isRollBackTrue  = true;
    // 查询热补丁视图
    ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    if (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_FAIL");
        isRollBackTrue = false;
    }

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);
    int32_t getUpgradeVersion = -1;
    if (isRollBackTrue) {
        ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1009010, getUpgradeVersion);

        // 读数据
        ret = executeCommand((char *)"gmsysview count D019", "80");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D020", "80");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D021", "80");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count D021");
        sleep(1);
    }

    // 释放共享内存后升级成功
    AW_FUN_Log(LOG_INFO, "count is %d\n", count);
    for (int i = 0; i < count - 1; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "count is %d\n", count);
        }
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(2);
    ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "40");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "40");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "1600");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 1");
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 002.系统占用动态内存，加载升级so内存不足升级失败，待空间释放后，加载升级降级so成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_002)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 提前申请视图元数据内存
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 200;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_002";
    char nsName2[128] = "DataLog_reliability_004_002_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_002_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 加载so后申请动态内存直到申请失败
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 9000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0;
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 升级so
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, -1);

    // 释放消耗掉的内存
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }
     // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 再次加载so
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "20000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 50;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 003.申请动态内存不足升级失败，释放内存，升级降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_003)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    // 降级so
    char nsName1[128] = "DataLog_reliability_004_002";
    char nsName2[128] = "DataLog_reliability_004_002_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_002_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 加载so后申请动态内存直到申请失败
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 9000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0;
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // popen失败
    ret = TestLoadUpgradeDatalog(nsName2);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, -1);

    // 释放消耗掉的内存
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "20000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 1);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 1);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 1);
    AW_MACRO_EXPECT_EQ_INT(20000, upgradeVersion);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 50;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 0);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

// 线程不断写数据直到数据重做完成
void *WriteInpTableUntilUpgradeDone(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t dataNum = 100;
    char tableD[] = "D000";
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 11;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 写输入表
    ret = 1;
    int32_t cycleTimes = 90;
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    while (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + cycleTimes, 1);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            system(viewCommand);
        }
        cycleTimes--;
        if (cycleTimes == 0) {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char filterContent[20] = "null";
    (void)sprintf(filterContent, "%d", (90 - cycleTimes) + 10);
    ret = executeCommand((char *)"gmsysview count D000", filterContent);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "D000 重做失败");
        system("gmsysview count D000");
        system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=D000");
    }
    ret = executeCommand((char *)"gmsysview count N000", filterContent);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_STEP, "N000 重做失败");
        system("gmsysview count N000");
        system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=N000");
    }

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

// 线程内不断修改重做条数和并发配置
void *ChangeCfgVal(void *args)
{
    int32_t cycle = 100;
    while (cycle > 0) {
        SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 0 > test.txt");
        SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 10 > test.txt");
        SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1> test.txt");
        SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100 > test.txt");
        cycle--;
    }
}

bool g_isEnd = false;  // 控制升降级线程结束条件
// 线程内不断升降级so
void *UpgeadeAndRollBack(void *args)
{
    char *patchSoName = (char *)args;
    char nsName1[128] = {0};
    (void)sprintf(nsName1, "%s", patchSoName);
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    (void)sprintf(nsName2, "%s_patchV2", nsName1);
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    (void)sprintf(soName2, "./datalog_file/%s_rollbackV2.so", nsName1);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t sleepTime = 120;
    int32_t cycle = sleepTime;  // 重做时间
    int32_t ret = 0;
    while (true) {
        // 升级so
        ret = TestLoadUpgradeDatalog(nsName2);
        if (ret == -1) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(1);
        cycle = sleepTime;
        while ((ret == 0) && (cycle > 0)) {
            ret = executeCommand(viewCommand, "PATCH_STATE: REDOING");
            if (ret != 0) {
                sleep(1);
                system(viewCommand);
            }
            cycle--;
        }

        // 降级so
        cycle = sleepTime;
        ret = 0;
        ret = TestLoadRollbackSo(soName2);
        if(ret){
            system(viewCommand);
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        sleep(1);
        while ((ret == 0) && (cycle > 0)) {
            ret = executeCommand(viewCommand, "PATCH_STATE: REDOING");
            if (ret != 0) {
                sleep(1);
                system(viewCommand);
            }
            cycle--;
        }
        if (g_isEnd) {
            break;
        }
    }
}

// 线程不断写数据直到内存满
void *WriteInpTable(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int32_t dataNum = 100;
    char tableD[] = "D000";
    int32_t count1[][7] = {{1, 2, 1, 2, 2, 1, 2}};
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 写输入表
    ret = 1;
    int32_t cycleTimes = 5;
    int32_t i = 0;
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    while (true) {
        count1[0][0] = (i + 11) % 256;
        count1[0][1] = (i + 1) % 32767;
        count1[0][2] = i + 1;
        ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, 1);
        if (ret != 0) {
            break;
        }
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_STEP, "GMERR_OUT_OF_MEMORY num is %d", i);
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}

/* ****************************************************************************
Description  : 004.线程一进行不断的升降级，线程二进行不断的写数据

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_004)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_023";
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    pthread_t sub_thr1;
    // 开启线程不断写数据
    ret = pthread_create(&sub_thr1, NULL, WriteInpTable, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 开启线程不断升降级so
    pthread_t sub_thr2;
    ret = pthread_create(&sub_thr2, NULL, UpgeadeAndRollBack, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    g_isEnd = true;
    ret = pthread_join(sub_thr2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 005.文件句柄超限
1.设置文件句柄为1024;
2.句柄耗尽预期升级失败，释放一个句柄升级成功
3.句柄耗尽预期降级级失败，释放一个句柄降级成功,
构建和手动执行占用的文件句柄数量不同
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_005)
{
    AW_FUN_Log(LOG_STEP, "test begin 1211");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    // 降级so
    char nsName1[128] = "DataLog_reliability_004_002";
    char nsName2[128] = "DataLog_reliability_004_002_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_002_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
#if !defined RUN_DATACOM_HPE
    struct rlimit rlim;
    rlim.rlim_cur = 1024;
    rlim.rlim_max = 1024;
    ret = setrlimit(RLIMIT_NOFILE, &rlim);
    EXPECT_EQ(ret, GMERR_OK);
#endif
    system("ulimit -n");
    system("gmsysview count");
    // 获取当前进程已占用文件句柄数
    int32_t fileStmts = GetFileStmtNums();
    AW_FUN_Log(LOG_INFO, "当前进程已占用%d文件句柄", fileStmts);
    // 升级成功
    int32_t openTimes = 1024 - fileStmts;  // 已存在其它文件句柄
    int32_t actualTimes = 0;
    FILE *fp[openTimes] = {0};
    for (int i = 0; i < openTimes; i++) {
        fp[i] = fopen("./datalog_file/DataLog_reliability_004_002_patchV2.so", "a+");
        if (fp[i] == NULL) {
            AW_FUN_Log(LOG_DEBUG, "fopen %dth error", i);
            break;
        }
        actualTimes++;
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    sleep(1);
    int32_t releaseStmt = 2;
    for (int i = 0; i < releaseStmt; i++) {
        fclose(fp[i]);
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int i = releaseStmt; i < actualTimes; i++) {
        fclose(fp[i]);
    }

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "20000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 1);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 1);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 1);
    AW_MACRO_EXPECT_EQ_INT(20000, upgradeVersion);

    // 降级
    actualTimes = 0;
    fileStmts = GetFileStmtNums();
    openTimes = 1024 - fileStmts;
    FILE *fp1[openTimes] = {0};
    for (int i = 0; i < 1024; i++) {
        fp1[i] = fopen("./datalog_file/DataLog_reliability_004_002_rollbackV2.so", "a+");
        if (fp1[i] == NULL) {
            AW_FUN_Log(LOG_DEBUG, "fopen error");
            break;
        }
        actualTimes++;
    }
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    for (int i = 0; i < releaseStmt; i++) {
        fclose(fp1[i]);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = -1;
    for (int i = releaseStmt; i < actualTimes; i++) {
        fclose(fp1[i]);
    }
    cycleTimes = 10;
    while (ret == -1 && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret == -1) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 0);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 006.时钟跳变
1.升级过程中时钟向前跳变;
2.降级过程中时钟往前跳变
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_006)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 65");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
        count1[i][3] = 35000;  // 计划35s后过期
    }

    char nsName1[128] = "DataLog_reliability_004_006";
    char nsName2[128] = "DataLog_reliability_004_006_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_006_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t readStartTime = 0;
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so前 【时间戳】%lu", readStartTime);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so后【时间戳】 %lu", readStartTime);
    sleep(1);
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(g_command);

    AW_FUN_Log(LOG_INFO, "时钟往后跳变20s");
    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(15);
    AW_FUN_Log(LOG_INFO, "等待过期表数据过期");
    // // 读数据
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 输出表订阅接收
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 3, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 降级时向前跳变
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(g_command);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(35);

    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA2", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 4, RECV_TIMEOUT / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 007.时钟跳变
1.升级过程中时钟向后跳变;
2.降级过程中时钟往后跳变
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_007)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
        count1[i][3] = 10000;  // 计划10s后过期
    }

    char nsName1[128] = "DataLog_reliability_004_006";
    char nsName2[128] = "DataLog_reliability_004_006_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_006_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int64_t readStartTime = 0;
    GetTimeMs(&readStartTime);
    system("gmsysview record inpA 1");
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so前%lu", readStartTime);
    system("gmsysview record inpA 1");
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so后%lu", readStartTime);
    sleep(1);
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(g_command);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据完成");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);

    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(5);
    AW_FUN_Log(LOG_INFO, "等待过期表数据过期");
    // // 读数据
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 输出表订阅接收
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 降级时向前跳变
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(g_command);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(3);
    // 时钟跳变不会提前过期
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA2", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    sleep(9);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 3, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

char g_startClientName[20] = "";
char g_stopClientName[20] = "";
void *STARTCLIENT(void *args)
{
    (void)system(g_startClientName);
}

void *STOPCLIENT(void *args)
{
    uint32_t sleepTime = 0;
    time_t timer = time(0);
    if (!timer) {
        printf("STOPCLIENT get time fail\n");
    }
    srand((uint32_t)timer);

    sleepTime = rand() % 10 + 10;
    sleep(sleepTime);
    system("pkill -f patch_client");
    int32_t ret = system(g_stopClientName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}
/* ****************************************************************************
Description  : 008.客户端异常退出
1.客户端反复加载升级降级卸载so 10000遍的过程中随机异常退出
2.重启客户端，查询so及upgradeversion，再次加载so升级so降级so卸载so
3.操作成功时检查结果是否正常
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_008)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    // 被kill客户端进程
    char clientName[20] = "patch_client";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "patch_clientExit";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, " test end");
}

/* ****************************************************************************
Description  : 009.挂起服务端进程
1.客户端反复升级降级
2.挂起服务端进程, 客户端请求失败
3.65秒后解挂服务端进程
4.客户端业务恢复
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_009)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_001";
    char nsName2[128] = "DataLog_reliability_004_001_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_001_rollbackV2.so";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    free(schema);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycle = 2;
    // .d文件加载：创建表和连接规则
    while (cycle > 0) {
        AW_FUN_Log(LOG_STEP, "1.load so");

        // 再次加载写入数据成功
        ret = LoadSoFile(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count");

        // 升级成功
        ret = TestLoadUpgradeDatalog(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        // 升级时挂起服务端
        system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_server");
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 查询热补丁视图以及表upgradeVersion字段值是否正常
        int32_t getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        // 尝试写入数据
        ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        // 读数据
        ret = executeCommand((char *)"gmsysview count D019", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D020", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D021", "484");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count D021");
        sleep(1);

        // 降级
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        // 降级时挂起服务端
        system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_server");
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);

        ret = executeCommand((char *)"gmsysview count D019", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D020", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D021", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        cycle--;
    }

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
/* ****************************************************************************
Description  : 010.挂起客户端进程
1.客户端反复升级降级
2.挂起客户端进程
3.65秒后解挂客户端进程
4.客户端业务恢复
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_010)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    int32_t ret = 0;
    system("patch_clientStop");
    // 升级时挂起服务端
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat isUpgrade.txt");
        ret = executeCommand(g_command, "No such file or directory");
        if (!ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    } while (!ret);
    system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_clent");
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat rollBack.txt");
        ret = executeCommand(g_command, "No such file or directory");
        if (!ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    } while (!ret);
    // 降级时挂起服务端
    system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_clent");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 011.超大规格表
1.加载2000个表(包括所有表类型, 每个表包含64个字段和32个索引(含变长), 包含所有运算规则)和1024个UDF,
写数据直到内存满，删除数据，升级so，写数据，内存满，删除数据，再降级 2.步骤一反复执行10000遍, 预期服务端内存保持稳定,
不上涨
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_011)
{
    AW_FUN_Log(LOG_STEP, "test begin");

    char nsName1[128] = "DataLog_reliability_004_011";
    char nsName2[128] = "DataLog_reliability_002";
    char upgradePatch[128] = "DataLog_reliability_004_011_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_011_rollbackV2.so";
    int32_t ret = 0;
    // 循环次数
    int32_t cycle = 10;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$COM_MEM_SUMMARY");

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#define MAX_CMD_SIZE 1024
    char g_command[MAX_CMD_SIZE] = {0};
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA061[] = "A061";
    int32_t count1[][7] = {{1, 2, 1, 2, 2, 1, 2}};
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int i;
    while (cycle > 0) {
        if (cycle % 100 == 0) {
            AW_FUN_Log(LOG_STEP, "--------------当前还有 %d 次----------------------------", cycle);
        }
        AW_FUN_Log(LOG_STEP, "1.写入数据直到内存满");
        i = 0;
        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap1.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "2.删除数据");
        int deleteI;
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 0;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap2.txt");
        // 升级so
        ret = TestLoadUpgradeDatalog(upgradePatch);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = 1;
        int32_t cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                system(viewCommand);
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_FUN_Log(LOG_INFO, "3.升级so成功");
        i = 0;

        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_FUN_Log(LOG_INFO, "4.升级so写入数据直到内存满");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap3.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "5.升级so删除数据");
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 1;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 降级
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_FUN_Log(LOG_INFO, "6.降级so成功");
        i = 0;
        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        AW_FUN_Log(LOG_INFO, "7.降级写入数据直到内粗满");
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap4.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "8.降级删除数据");
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 0;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        cycle--;
    }
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = TestUninstallDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDropVertexLabel(g_stmt, "N000");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmsysview -q V\\$COM_MEM_SUMMARY");
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 12.连接占满升降级失败，断开一个已有连接升降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_012)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_001";
    char nsName2[128] = "DataLog_reliability_004_001_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_001_rollbackV2.so";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    free(schema);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    uint32_t existConnNum;
    testGetConnNum(&existConnNum);
    GmcConnT *conn1[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < MAX_CONN_SIZE - existConnNum; i++) {
        testGmcConnect(&conn1[i]);
    }

    // 升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    for (int i = 0; i < 1; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = -1;
    while (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "484");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);

    // 降级
    for (int i = 0; i < 1; i++) {
        testGmcConnect(&conn1[i]);
    }
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    for (int i = 0; i < 1; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    for (int i = 1; i < MAX_CONN_SIZE - existConnNum; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = -1;
    while (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);

    ret = executeCommand((char *)"gmsysview count D019", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 013.升级重做过程中，订阅消息队列满，升级失败
1.加载so
2.升级so，含多个订阅端构造重做时推送大量数据，回调中减缓接收速度，重做失败，日志16006，
3.取消订阅，卸载so后再重新加载升级so，写入数据，降级so，触发推送，预期推送失败16006，升级失败
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_013)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 500;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_013";
    char nsName2[128] = "DataLog_reliability_004_013_patchV2";
    char nsName3[128] = "DataLog_reliability_004_013_patchV3";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_013_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/DataLog_reliability_004_013_rollbackV3.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅G000输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);

    // 订阅G001输出表
    GmcConnT *subConnOutput1;
    GmcStmtT *subStmtOutput1;
    SnUserDataWithFuncT *userDataOutput1;
    const char *subConnNameOutput1 = "testSubOutput1";
    ret = testSubConnect(&subConnOutput1, &subStmtOutput1, 1, g_epoll_reg_info, subConnNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput1 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub1.gmjson", &sub_infoOutput1);
    EXPECT_NE((void *)NULL, sub_infoOutput1);
    const char *subNameOutput1 = "testSub_Output1";
    GmcSubConfigT tmp_sub_infoOutput1;
    tmp_sub_infoOutput1.subsName = subNameOutput1;
    tmp_sub_infoOutput1.configJson = sub_infoOutput1;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput1, &userDataOutput1, &tmp_sub_infoOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput1);

    // 订阅G002输出表
    GmcConnT *subConnOutput2;
    GmcStmtT *subStmtOutput2;
    SnUserDataWithFuncT *userDataOutput2;
    const char *subConnNameOutput2 = "testSubOutput2";
    ret = testSubConnect(&subConnOutput2, &subStmtOutput2, 1, g_epoll_reg_info, subConnNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput2 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub2.gmjson", &sub_infoOutput2);
    EXPECT_NE((void *)NULL, sub_infoOutput2);
    const char *subNameOutput2 = "testSub_Output2";
    GmcSubConfigT tmp_sub_infoOutput2;
    tmp_sub_infoOutput2.subsName = subNameOutput2;
    tmp_sub_infoOutput2.configJson = sub_infoOutput2;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput2, &userDataOutput2, &tmp_sub_infoOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput2);

    // 订阅G003输出表
    GmcConnT *subConnOutput3;
    GmcStmtT *subStmtOutput3;
    SnUserDataWithFuncT *userDataOutput3;
    const char *subConnNameOutput3 = "testSubOutput3";
    ret = testSubConnect(&subConnOutput3, &subStmtOutput3, 1, g_epoll_reg_info, subConnNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput3 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub3.gmjson", &sub_infoOutput3);
    EXPECT_NE((void *)NULL, sub_infoOutput3);
    const char *subNameOutput3 = "testSub_Output3";
    GmcSubConfigT tmp_sub_infoOutput3;
    tmp_sub_infoOutput3.subsName = subNameOutput3;
    tmp_sub_infoOutput3.configJson = sub_infoOutput3;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput3, &userDataOutput3, &tmp_sub_infoOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput3);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据失败");

    ret = CheckLog((char *)"GMERR-1016006");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);
    ret = GmcUnSubscribe(g_stmt, subNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput1->data);
    free(userDataOutput1);
    ret = GmcUnSubscribe(g_stmt, subNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput2->data);
    free(userDataOutput2);
    ret = GmcUnSubscribe(g_stmt, subNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput3->data);
    free(userDataOutput3);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput3, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅G000输出表
    subConnOutput = NULL;
    subStmtOutput = NULL;
    userDataOutput = NULL;
    subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    subNameOutput = "testSub_Output";
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);

    // 订阅G001输出表
    subConnOutput1 = NULL;
    subStmtOutput1 = NULL;
    userDataOutput1 = NULL;
    subConnNameOutput1 = "testSubOutput1";
    ret = testSubConnect(&subConnOutput1, &subStmtOutput1, 1, g_epoll_reg_info, subConnNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput1 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub1.gmjson", &sub_infoOutput1);
    EXPECT_NE((void *)NULL, sub_infoOutput1);
    subNameOutput1 = "testSub_Output1";
    tmp_sub_infoOutput1.subsName = subNameOutput1;
    tmp_sub_infoOutput1.configJson = sub_infoOutput1;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput1, &userDataOutput1, &tmp_sub_infoOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput1);

    // 订阅G002输出表
    subConnOutput2 = NULL;
    subStmtOutput2 = NULL;
    userDataOutput2 = NULL;
    subConnNameOutput2 = "testSubOutput2";
    ret = testSubConnect(&subConnOutput2, &subStmtOutput2, 1, g_epoll_reg_info, subConnNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput2 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub2.gmjson", &sub_infoOutput2);
    EXPECT_NE((void *)NULL, sub_infoOutput2);
    subNameOutput2 = "testSub_Output2";
    tmp_sub_infoOutput2.subsName = subNameOutput2;
    tmp_sub_infoOutput2.configJson = sub_infoOutput2;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput2, &userDataOutput2, &tmp_sub_infoOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput2);

    // 订阅G003输出表
    subConnOutput3 = NULL;
    subStmtOutput3 = NULL;
    userDataOutput3 = NULL;
    subConnNameOutput3 = "testSubOutput3";
    ret = testSubConnect(&subConnOutput3, &subStmtOutput3, 1, g_epoll_reg_info, subConnNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput3 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub3.gmjson", &sub_infoOutput3);
    EXPECT_NE((void *)NULL, sub_infoOutput3);
    subNameOutput3 = "testSub_Output3";
    tmp_sub_infoOutput3.subsName = subNameOutput3;
    tmp_sub_infoOutput3.configJson = sub_infoOutput3;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput3, &userDataOutput3, &tmp_sub_infoOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput3);
    dataNum = 10;
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);

    ret = GetTableUpgradeVersionByApi((char *)"G001", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G002", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G003", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(3);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum * dataNum + dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级1
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int32_t dataNum1 = 1;
    // 写数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + 1, dataNum1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + 1, dataNum1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级2
    ret = TestLoadUpgradeDatalog(nsName3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 110, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    dataNum = 480;
    // 写数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + 20, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + 20, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次降级1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName3));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT");

    ret = CheckLog((char *)"GMERR-1016006");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = GmcUnSubscribe(g_stmt, subNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput1->data);
    free(userDataOutput1);

    ret = GmcUnSubscribe(g_stmt, subNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput2->data);
    free(userDataOutput2);

    ret = GmcUnSubscribe(g_stmt, subNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput3->data);
    free(userDataOutput3);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}
bool g_isLockTransation = true;
void *ReadAndCommit(void *args)
{
#define MAX_VERTEX_NUM 10000
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    void *g_label = NULL, *g_label_2 = NULL;

    AsyncUserDataT data = {0};
    GmcConnT *conn;
    GmcConnT *conn1;
    GmcConnT *conn2;
    GmcStmtT *stmt;
    GmcStmtT *stmt1;
    GmcStmtT *stmt2;
    char label_name1[] = "OP_T0";
    char lalable_name_PK1[] = "OP_PK";
    char label_name2[] = "DST_T0";
    char lalable_name_PK2[] = "DST_PK";
    char label_name3[] = "edgelabel_testEdgeBatchOp";
    char g_label_config_test[] = "{\"max_record_num\":10000, \"isFastReadUncommitted\":0}";
    char *test_schema1 = NULL;
    char *test_schema2 = NULL;
    char *test_schema3 = NULL;
    const char *edgeLabelName = "edgelabel_testEdgeBatchOp";
    int ret = 0;
    int start_num = 0;
    int end_num = 100;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char labelName[20] = "T20_all_type";

    int32_t tableNums = 2000;
    int32_t tableNum = CreateMuiltTables(stmt, tableNums);

    // // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    // /* ******************查询视图  预期视图无变化************* */

    AW_FUN_Log(LOG_INFO, "事务开启前查询锁占用情况");
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    // 开启一个事务(cs模式)
    for (int i = 0; i < tableNum; i++) {
        (void)sprintf(labelName, "T20_all_type%d", i);
        // 插入顶点
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "tableNum is %d,i = %d, lableName = %s", tableNum, i, labelName);
            break;
        }

        /* ******************查询视图  预期视图无变化************* */

        for (int i = start_num; i < end_num; i++) {
            set_VertexProperty_PK(stmt, i);
            set_VertexProperty(stmt, i, 0, (char *)"string");
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    }
    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    bool isBreak = false;
    for (int i = 1; i < tableNum; i += 2) {
        (void)sprintf(labelName, "T20_all_type%d", i);

        // 更新顶点
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(ret, GMERR_OK);
        for (int ii = start_num; ii < 50; ii++) {
            int64_t f0_value = ii;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
            set_VertexProperty(stmt, 2 * ii, 1, (char *)"string2");
            ret = GmcSetIndexKeyName(stmt, lalable_name_PK1);
            EXPECT_EQ(ret, GMERR_OK);
            ret = GmcExecute(stmt);

            if (ret) {
                break;
            }
        }
        if (i % 100 == 0) {
            system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
        }

        if (ret) {
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_LOCK_NOT_AVAILABLE);
    g_isLockTransation = false;
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    sleep(70);  // 等待升级重试失败
    // // 查询事务锁预期全部占满
    AW_FUN_Log(LOG_STEP, "查询事务锁预期无锁空闲");
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    DropMuiltTables(stmt, tableNum);
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}
// 2024.5.14锁桶上的锁是随机hash散列分配的
/* ****************************************************************************
Description  : 014.升级重做时需要使用到事务锁
1.加载so提前将事务锁占满不释放5114
2.升级so(含数据重做)，升级重做失败12002
3.释放事务锁，再次升级升级成功
11.29：当前(不同表)表锁和行锁可以共存共同占用5114锁资源
2024.5.14 当前锁无法占满
2024.5.14锁桶上的锁是随机hash散列分配的
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_014)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=6000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "A001";
    char tableB[] = "A001000";
    int32_t dataNum = 1;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_014";
    char nsName2[128] = "DataLog_reliability_004_014_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_014_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview record A001 1");

    // 占满事务锁，预期升级失败
    pthread_t sub_thr;
    ret = pthread_create(&sub_thr, NULL, ReadAndCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    while (g_isLockTransation) {
        sleep(1);
    }
    // 升级加载需要锁
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做失败锁不可用");
    ret = CheckLog((char *)"GMERR-1012002");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 释放锁重做成功
    //  卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "load so again");

    // 加载写入数据成功
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview record A001 1");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 占用锁降级重做失败
    g_isLockTransation = true;
    ret = pthread_create(&sub_thr, NULL, ReadAndCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    AW_FUN_Log(LOG_INFO, "占用锁降级重做失败");
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    while (g_isLockTransation) {
        sleep(1);
    }
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"A001", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"A001000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 015.资源池创建过多耗尽内存后升降级
1. 加载多个资源池, 每个资源池的容量达到上限,
 直至内存耗尽加载失败对加载成功so进行升级写数据，降级写数据卸载so，
 加载之前加载失败so，写数据，升级so写数据，降级so写数据;
2. 步骤1反复执行10000遍;(10000次待验证)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_015)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxNormalTableNum=4000\"");
    // 当前资源表占用内存为共享内存
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\" \"maxResPoolShmSize=300\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "maxcountresourcetable001";
    char nsName2[128] = "maxcountresourcetable002";
    char nsName3[128] = "maxcountresourcetable003";
    char patchName2[128] = "maxcountresourcetable002_patchV2";
    char patchName3[128] = "maxcountresourcetable003_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/maxcountresourcetable002_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/maxcountresourcetable003_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 确保数据量能够看护到内存上涨
    int cycle = 400;
    char tableB[] = "ns1.B";
    char tableC[] = "ns1.C";
    char tableC2[] = "ns2.C";
    char tableC3[] = "ns3.C";
    int32_t count[][7] = {{1, 2, 1, 2, 2, 1, 2}};

    // // 写入5条数据

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (cycle > 0) {
        // 卸载同名datalog.so
        if (cycle % 20 == 0) {
            AW_FUN_Log(LOG_STEP, "************************cycle = %d********************************", cycle);
        }
        TestUninstallDatalog(nsName1, NULL, false);
        TestUninstallDatalog(nsName2, NULL, false);
        TestUninstallDatalog(nsName3, NULL, false);

        // .d文件加载：创建表和连接规则
        AW_FUN_Log(LOG_STEP, "1.------------load so-----------------");
        ret = LoadSoFile(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = LoadSoFile(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 对加载成功so进行写数据
        ret = BatchInsert(g_conn, g_stmt, tableC, count, 1);
        if (ret) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 内存不足加载失败
        ret = LoadSoFile(nsName3);
        AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
        char cmd[200] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable003|grep "
                        "maxcountresourcetable003|wc -l";
        ret = executeCommand(cmd, "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 加载成功so进行升级
        count[0][1] = 10;
        ret = TestLoadUpgradeDatalog(patchName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = 1;
        int32_t cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "SUCCESS");
            if (ret) {
                sleep(1);
                if (cycleTimes == 0) {
                    system(viewCommand);
                }
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        int32_t getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC2, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);

        // 升级写ns2表数据
        ret = BatchInsert(g_conn, g_stmt, tableC2, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns2升级写数据失败");
            break;
        }

        // 降级写数据
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        ret = 1;
        count[0][1] = 11;
        cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "SUCCESS");
            if (ret) {
                sleep(1);
                if (cycleTimes == 0) {
                    system(viewCommand);
                }
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC2, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = BatchInsert(g_conn, g_stmt, tableC2, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns2降级写数据失败");
            break;
        }

        // 卸载已加载so,加载之前 失败so成功
        ret = TestUninstallDatalog(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = LoadSoFile(nsName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        char cmd1[200] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable003";
        ret = executeCommand(cmd1, "maxcountresourcetable003");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        count[0][1] = 1;
        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 升级写ns3表数据
        count[0][1] = 10;
        ret = TestLoadUpgradeDatalog(patchName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = 1;
        cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "SUCCESS");
            if (ret) {
                sleep(1);
                if (cycleTimes == 0) {
                    system(viewCommand);
                }
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC3, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);

        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns3升级写数据失败");
            break;
        }

        // 降级写数据
        count[0][1] = 11;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName3));
        ret = 1;
        cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "SUCCESS");
            if (ret) {
                sleep(1);
                if (cycleTimes == 0) {
                    system(viewCommand);
                }
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC3, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns3降级写数据失败");
            break;
        }
        char cmd2[200] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable002|grep "
                         "maxcountresourcetable002|wc -l";
        ret = executeCommand(cmd2, "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 卸载so
        AW_FUN_Log(LOG_STEP, "2.---------------------uninstall so------------------");
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = TestUninstallDatalog(nsName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        cycle--;
        if (cycle % 10 == 0) {
            system("gmsysview -q V\\$COM_MEM_SUMMARY > mem1.txt");
        }
    }
    system("gmsysview -q V\\$COM_MEM_SUMMARY > mem2.txt");
    int32_t filesValue1 = 0;
    int32_t filesValue2 = 1;
    filesValue1 = GetFileValue((char *)"mem1.txt");
    filesValue2 = GetFileValue((char *)"mem2.txt");
    AW_MACRO_EXPECT_EQ_INT(filesValue1, filesValue2);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 016.订阅关系满
1.加载so，写入数据
2.创建多个订阅连接订阅多张表，升级订阅表相关topo订阅接收
3.写入数据，订阅接收，降级so，订阅接收，写入数据
4.读数据，卸载so
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_016)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    int32_t ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName[128] = "DataLog_reliability_022_1";
    char nsName1[128] = "DataLog_reliability_022_2";
    char patchName2[128] = "DataLog_reliability_022_1_patchV2";
    char patchName3[128] = "DataLog_reliability_022_2_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_022_1_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/DataLog_reliability_022_2_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 加载多个so
    TestUninstallDatalog(nsName, NULL, false);
    TestUninstallDatalog(nsName1, NULL, false);

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    GmcConnT *subConn[1024];
    char subInfo[1024] = {0};
    char subName[30] = {0};
    char subInfoPart1[30] = {0};
    char subConnNameC[20] = "testSubC";
    char tableA1[20] = "rel0221.A001";
    char tableA2[20] = "rel0222.A001";
    int32_t count[][7] = {{1, 2, 1, 2, 2, 1, 2}};

    SnUserDataWithFuncT *userData[1024] = {0};
    uint32_t CurentconnNum = 0;
    GmcSubConfigT tmp_subInfo;
    ret = testGetConnNum(&CurentconnNum);
    EXPECT_EQ(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "可建订阅连接%d", 1022 - CurentconnNum);
    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        sprintf(subConnNameC, "subConnNameC%d", i);
        ret = testSubConnect(&subConn[i], NULL, 1, g_epoll_reg_info, subConnNameC);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "subconnect %dth failed ret =%d", i, ret);
            break;
        }
        const char *subInfoPart2 = R"(
    "comment":"VertexLabel subscription",
    "events":
        [
            {"type":"insert", "msgTypes":["new object"]}
        ],
    "is_reliable": true}
        )";
        userData[i] = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
        userData[i]->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
        memset(userData[i]->data, 0, sizeof(SnUserDataT));
        userData[i]->funcType = 0;
        userData[i]->tableType = 0;
        userData[i]->readIdFunc = SixtyFourthProperty_getId;
        userData[i]->startid = 0;
        userData[i]->endid = 1;
        userData[i]->count = 1;

        if (i >= 999) {
            sprintf(subInfoPart1, "\"rel0222.A00%d\",", i - 999 + 2);
        } else {
            sprintf(subInfoPart1, "\"rel0221.A00%d\",", i + 2);
        }
        sprintf(subInfo, "{\"label_name\":%s%s", subInfoPart1, subInfoPart2);
        sprintf(subName, "subVertexLabel%d", i);
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(g_stmt, &tmp_subInfo, subConn[i], snCallback, userData[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    char cmd1[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_022_1";
    char cmd2[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_022_2";
    // 升级so写数据
    ret = TestLoadUpgradeDatalog(patchName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd1, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(cmd1);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestLoadUpgradeDatalog(patchName3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    cycleTimes = 5;

    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd2, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        sleep(2);
        system(cmd2);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "升级so写数据");
    count[0][1] = 11;
    // 降级so写数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd1, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {

        system(cmd1);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName3));
    AW_FUN_Log(LOG_STEP, "降级so写数据");
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd2, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(viewCommand);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        ret = testWaitSnRecv(userData[i]->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "接收订阅消息%dth失败", i);
            break;
        }
    }

    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        sprintf(subName, "subVertexLabel%d", i);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testSubDisConnect(subConn[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        testSnFreeUserData(userData[i]->data);
        free(userData[i]);
    }
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 017.告警验证
1.subsChannelGlobalShareMemSizeMax修改为20M
2.查询告警，加载so，写入数据直到写入失败16006，300s后查询告警
3.300s后查询告警，升级so，删除数据，再次写入数据直到16006，300s后查询告警
4.300s后查询告警，降级so，删除数据，再次写入数据直到16006，300s后查询告警
5.卸载so
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_017)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char nsName[128] = "DataLog_reliability_017";
    char nsName2[128] = "DataLog_reliability_017_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_017_rollbackV2.so";
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
           "\"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    // 预期触发告警  GMC_ALARM_SUB_CONN_RING  GMC_ALARM_SUB_MSG_POOL
    GmcAlarmIdE AlarmId = GMC_ALARM_SUB_CONN_RING;
    GmcAlarmDataT test_AlarmData;
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 加载多个so
    TestUninstallDatalog(nsName, NULL, false);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 16");
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
#ifdef ENV_RTOSV2X
    sleep(300);
    AlarmId = GMC_ALARM_SUB_MSG_POOL;
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = true;

    // 进行订阅推送
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalog_file/outTable_pubsub.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    free(sub_infoF);

    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    ret = testSubConnect(&subConnUpdate, &subStmtUpdate, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnUpdate, &userDataUpdate, &tmp_sub_infoUpdate);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoUpdate);

    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    /******************写输入表触发订阅推送*********************/
    int32_t batchData = 129;
    int32_t count1[batchData][7] = {{1, 2, 1, 2, 2, 1, 2}};
    char tableA[] = "D000";
    int k = 0;
    g_isNeedASleep = true;
    for (int i = 0; i < batchData; i++) {
        for (int j = 0; j < 7; j++) {
            count1[i][j] = 1 + i + k * batchData;
        }
    }
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOL
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(305);
    // so 升级
    // upgrade
    // 并发查询输入表数据
    sleep(10);
    system("gmsysview count");
    (void)TestLoadUpgradeDatalog(nsName2);
    sleep(1);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    k = 0;
    g_isNeedASleep = true;
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOLp
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 256, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "upgrade GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(305);
    // 降级so
    // 并发查询输入表数据
    (void)TestLoadRollbackSo(soName2);
    sleep(1);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    k = 0;
    g_isNeedASleep = true;
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOL
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 256, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "RollBack GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    /******************释放订阅资源*********************/
    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3821");
    ret = GmcUnSubscribe(g_stmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3825");
    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3828");
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3831");
    ret = testSubDisConnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3834");
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "3837");
    testSnFreeUserData(userDataF->data);
    testSnFreeUserData(userDataUpdate->data);
    testSnFreeUserData(userDataOutput->data);
    free(userDataF);
    free(userDataUpdate);
    free(userDataOutput);
#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);

    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 018.重做含多条二级索引相同数据
1.旧so中输入表二级索引写入为join规则，单表写入1000条数据，规则升级为笛卡尔积
2.升级后进行二级索引删除(字段总数过多，笛卡尔数据量到不了100万)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_018)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    // 表空间内存不足，暂定数据量为400
    int32_t dataNum = 400;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_018";
    char nsName2[128] = "DataLog_reliability_004_018_patchV2";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(4);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");

    // 二级索引删除数据内存不足删除失败
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(g_stmt, (char *)"4");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int64_t value1 = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &value1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = executeCommand((char *)"gmsysview count inpA", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 使用别的二级索引分批删除删除成功
    int32_t value6 = 1;
    for (int32_t k = 0; k < dataNum; k++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetIndexKeyName(g_stmt, (char *)"2");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        value6 = k + 1;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = executeCommand((char *)"gmsysview count inpA", "400");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "160000");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

void *ScanInpTabel(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char tableD[] = "D000";
    int32_t ret = 0;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    D000 *d000 = {0};
    // 全表读
    ret = readRecord(conn, stmt, tableD, d000, 10, D000Get, false);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
}

void ScanHundredTimes()
{
    int32_t threadNum = 100;
    pthread_t threadArray[threadNum];
    for (int32_t i = 0; i < threadNum; i++) {
        (void)pthread_create(&threadArray[i], NULL, ScanInpTabel, NULL);
    }

    for (int32_t i = 0; i < threadNum; i++) {
        (void)pthread_join(threadArray[i], NULL);
    }
}
/* ****************************************************************************
Description  : 019.100个线程并发访问同一张表，预期服务正常
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_019)
{
    // 加载so
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_023";
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");

    // 预置数据
    // 并发查询输入表数据
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 读
    ScanHundredTimes();

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // upgrade
    // 并发查询输入表数据
    (void)TestLoadUpgradeDatalog(nsName2);
    ScanHundredTimes();
    sleep(1);

    // 降级so
    // 并发查询输入表数据
    (void)TestLoadRollbackSo(soName2);
    ScanHundredTimes();

    // 查询so是否还存在再进行卸载
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_023", nsName1);
    if (ret == 0) {
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // view check so is unloaded
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_023", nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 删除so
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 020.升级so文件丢失
1.加载so，并升级
2.再次升级时需要加载的so不存在, 预期报错, 降级时，需要加载的so不存在预期报错;
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_020)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    // 表空间内存不足，暂定数据量为400
    int32_t dataNum = 400;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_018";
    char nsName2[128] = "DataLog_reliability_004_018_patchV2";
    char nsName3[128] = "DataLog_reliability_004_018_patchV4";
    char nsName4[128] = "DataLog_reliability_004_018_rollbackV4";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(4);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");

    // 再次升级指定不存在文件
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "ret = 1013000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);

    // 降级文件不存在
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./datalog_file/%s.so", g_toolPath, nsName4);
    ret = executeCommand(command, "ret = 1013000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);

    ret = executeCommand((char *)"gmsysview count inpA", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "160000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 021.文件内容异常
1.加载so，并升级
2.再次升级so， gmprecompiler和gmserver版本不匹配, 加载失败，降级so加载失败
3.使用gmprecompiler和gmserver版本一致so，预期加载升级成功，降级so成功(当前版本不一致仅打印日志)*
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_021)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 100;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_018";
    char nsName2[128] = "DataLog_reliability_004_018_patchV2";
    char nsName3[128] = "DataLog_reliability_004_018_rollbackV2";
    // 修改升级.c中版本号
    char cmdExe[128] = "sed -i 's/506/502/g' ./datalog_file/DataLog_reliability_004_018_patch.c";
    // 修改降级.c中版本号
    char cmdExe1[128] = "sed -i 's/506/502/g' ./datalog_file/DataLog_reliability_004_018_patch_rollback.c";

    // 重新编译正确so
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 得到版本不一致so
    system(cmdExe);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared "
        "./datalog_file/DataLog_reliability_004_018_patch.c -o "
        "./datalog_file/%s.so \n",
        nsName2);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CheckLog("Gmprecompiler dismatched");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared "
        "./datalog_file/DataLog_reliability_004_018_patch_rollback.c -o "
        "./datalog_file/%s.so \n",
        nsName3);
    system(command);
    // 降级版本不一致so
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CheckLog("Gmprecompiler dismatched");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    ret = executeCommand((char *)"gmsysview count inpA", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 022.文件路径过长
加载卸载路径超长，upgrade rollback 参数最长路径，超出256报错合理
1.加载so
2.加载升级so绝对路径超过256字节，加载失败
3.绝对路径等于256字节加载成功
4.降级so，绝对路径超过256字节降级失败
5.绝对路径等于256字节降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_022)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char nsName1[128] = "DataLog_reliability_004_018";
    char nsName2[128] = "DataLog_reliability_004_018_patchV2";
    char nsName3[128] = "DataLog_reliability_004_018_rollbackV2";
    char newlibName[260] = {0};
    char libName[] = "datalog_file/DataLog_reliability_004_018.so";
    char libName1[] = "datalog_file/DataLog_reliability_004_018_patchV2.so";
    char libName2[] = "datalog_file/DataLog_reliability_004_018_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 100;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    int offset = GetPathLengthNums();
    int32_t soNameLength = 115;
    char libDir[128] = "loadlib";
    memset_s(newlibName, soNameLength, 'b', soNameLength);
    memset_s(nsName1, soNameLength, 'b', soNameLength);
    char filePath[256] = "cds";
    // 当前路径长度+soName长度+.so后缀+结束符
    int32_t needPathLength = 256 - offset - soNameLength - 4;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    // 重新编译正确so
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "load so");
    (void)snprintf(newlibName + soNameLength, 260, "%s", ".so");
    (void)SystemSnprintf("cp %s %s/%s", libName, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("rm -rf %s", filePath);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("mkdir -p %s", filePath);

    // 路径长度比实际规格大一.patch
    memset_s(filePath, 256, '\0', 256);
    needPathLength -= 5;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);

    (void)snprintf(newlibName + soNameLength, 260, "%s", "_patch.so");
    (void)SystemSnprintf("cp %s %s/%s", libName1, filePath, newlibName);

    AW_FUN_Log(LOG_STEP, "1.升级导入绝对路径长度大于256 so加载失败");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "the absolute path len is 256. ret = 1004003");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset_s(filePath, 256, '\0', 256);
    needPathLength -= 1;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    (void)SystemSnprintf("cp %s %s/%s", libName1, filePath, newlibName);

    // 升级
    AW_FUN_Log(LOG_STEP, "2.升级导入绝对路径长度等于256 so加载成功");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 等待重做完成
    sleep(5);
    SystemSnprintf("rm -rf %s", filePath);
    memset_s(filePath, 256, '\0', 256);

    // 绝对路径大于256
    needPathLength -= 4;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);

    // 降级绝对路径过长加载失败
    AW_FUN_Log(LOG_STEP, "3.降级导入绝对路径长度大于256 so加载失败");
    memset_s(newlibName, 260, '\0', 260);
    memset_s(newlibName, soNameLength, 'b', soNameLength);
    (void)snprintf(newlibName + soNameLength, 260, "%s", "_rollbackV2.so");
    (void)SystemSnprintf("cp %s %s/%s", libName2, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./%s/%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "the absolute path len is 256. ret = 1004003");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("rm -rf %s", filePath);

    // 绝对路径等于256
    AW_FUN_Log(LOG_STEP, "4.降级导入绝对路径长度等于256 so加载成功");
    needPathLength -= 1;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    (void)SystemSnprintf("cp %s %s/%s", libName2, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./%s/%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    SystemSnprintf("rm -rf %s", filePath);

    ret = executeCommand((char *)"gmsysview count inpA", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

// 写外部表线程
void *WriteOutTableButNoCommit(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char label_name[] = "N000";
    int32_t ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int32_t count1[][7] = {{40, 2, 1, 2, 2, 1, 2}};
    // 直接写外部表
    ret = BatchInsertByte256(conn, stmt, label_name, count1, 1, GMERR_OK, false, GMERR_OK, true);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(100);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(ret, GMERR_OK);
}
/* ****************************************************************************
Description  : 023.锁获取失败，重做失败
1.线程1开启事务写外部表不提交
2.线程二升级so，重做失败，卸载so
3.再次加载so，线程1事务提交，进行升级成功，
4.线程1再次开启事务写数据，不提交，进行降级，降级失败
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_023)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_023";
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    AW_FUN_Log(LOG_STEP, "1.线程事务内写外部表时升级失败");
    // 线程中写外部表 100s后提交
    pthread_t sub_thr;
    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(5);
    // 线程中升级so,抢锁失败,
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    system(viewCommand);
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v0.0.0]", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级成功
    AW_FUN_Log(LOG_STEP, "2.再次升级成功");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);
    // 查询热补丁视图
    system(viewCommand);
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "3.事务内写外部表不提交时降级失败");
    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 024.在线修改系统配置为异常值
1.线程1进行升级重做并不断修改重做条数和DML并发配置
2.线程二不断写数据直到重做结束
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_024)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_023";
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    pthread_t sub_thr1;
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    ret = pthread_create(&sub_thr1, NULL, WriteInpTableUntilUpgradeDone, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启线程升级so
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启线程不断修改重做条数和DML并发配置
    pthread_t sub_thr2;
    ret = pthread_create(&sub_thr2, NULL, ChangeCfgVal, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);

    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(1, getUpgradeVersion);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 025.升级时修改udf实现，申请最大支持动态内存2M，预期升级失败，内存被释放
1.重做条数为1000加载so后进行udf实现修改升级，udf中增加申请内存操作
2.预期升级重做时，触发udf申请内存，内存不足，重做失败，内存被释放(重做失败仅影响数据一致性)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_025)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_024";
    char nsName2[128] = "DataLog_reliability_004_024_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_024_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableOut[] = "A0990";
    char tableMid[] = "A0989";
    char tableInp[] = "A0988";
    int32_t dataNum = 1001;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableInp, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 修改重做条数为1000条
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    // 升级so失败
    char command[256] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(100);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    system(viewCommand);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 026.异常导致加载失败后, 重新加载正常的可成功
1.不涉及重做，udf升级时udf未声明实现，升级失败
2.再次加载声明实现udf升级so，升级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_026)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_024";
    char nsName2[128] = "DataLog_reliability_004_024_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_024_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableOut[] = "A0990";
    char tableMid[] = "A0989";
    char tableInp[] = "A0988";
    int32_t dataNum = 1001;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableInp, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 修改重做条数为1000条
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    char command[256] = {0};
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_024_patchV2.so "
                   "./datalog_file/DataLog_reliability_004_024_patchV2_1.so");
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_025_patchV2.so "
                   "./datalog_file/DataLog_reliability_004_024_patchV2.so");
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    // 升级so
    sleep(2);
    SystemSnprintf("rm -rf ./datalog_file/DataLog_reliability_004_024_patchV2.so");
    sleep(1);
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_024_patchV2_1.so "
                   "./datalog_file/DataLog_reliability_004_024_patchV2.so");
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(30);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

// 线程加载升级so
void *LoadSameNameUpgradeSo(void *args)
{
    char nsName2[128] = "DataLog_reliability_004_027_patchV2";
    char *patchSoName = (char *)args;
    (void)sprintf(nsName2, "%s_patchV2", patchSoName);
    (void)TestLoadUpgradeDatalog(nsName2);
}

// 线程升级不同so
void *LoadDiffNameUpgradeSo(void *args)
{
    int32_t index = *(int32_t *)args;
    char nsName2[128] = "DataLog_reliability_004_027_patchV2";
    (void)sprintf(nsName2, "DataLog_reliability_004_027_%d_patchV2", index);
    (void)TestLoadUpgradeDatalog(nsName2);
}
/* ****************************************************************************
Description  : 027.并发升级同1个so和并发升级多个so
1.并发对同一个so进行升级，预期只会有一个so升级成功，其余so报错
2。并发10个线程对不同so升级，预期互不影响，升级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_027)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    // 并发升级相同so
    char nsName1[128] = "DataLog_reliability_004_027";
    char nsName2[128] = "DataLog_reliability_004_027_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_027_rollbackV2.so";

    //  并发升级不同so
    char *soName[10] = {(char *)"DataLog_reliability_004_027_1", (char *)"DataLog_reliability_004_027_2",
        (char *)"DataLog_reliability_004_027_3", (char *)"DataLog_reliability_004_027_4",
        (char *)"DataLog_reliability_004_027_5", (char *)"DataLog_reliability_004_027_6",
        (char *)"DataLog_reliability_004_027_7", (char *)"DataLog_reliability_004_027_8",
        (char *)"DataLog_reliability_004_027_9", (char *)"DataLog_reliability_004_027_10"};

    char *upgradeSoName[10] = {(char *)"DataLog_reliability_004_027_1_patchV2",
        (char *)"DataLog_reliability_004_027_2_patchV2", (char *)"DataLog_reliability_004_027_3_patchV2",
        (char *)"DataLog_reliability_004_027_4_patchV2", (char *)"DataLog_reliability_004_027_5_patchV2",
        (char *)"DataLog_reliability_004_027_6_patchV2", (char *)"DataLog_reliability_004_027_7_patchV2",
        (char *)"DataLog_reliability_004_027_8_patchV2", (char *)"DataLog_reliability_004_027_9_patchV2",
        (char *)"DataLog_reliability_004_027_10_patchV2"};
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    for (int32_t i = 0; i < 10; i++) {
        TestUninstallDatalog(soName[i], NULL, false);
    }
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = LoadSoFile(soName[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "1.加载多个so");
    // 多个线程并发加载相同升级so
    int32_t threadNum = 10;
    pthread_t sub_thr1[threadNum];
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&sub_thr1[i], NULL, LoadSameNameUpgradeSo, (char *)nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(sub_thr1[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so,间隔一段时间查询视图");

    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "LOAD_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    // 开启多个线程升级不同so
    pthread_t sub_thr2[threadNum];
    int32_t dataNum[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        dataNum[i] = i + 1;
        ret = pthread_create(&sub_thr2[i], NULL, LoadDiffNameUpgradeSo, &dataNum[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = pthread_join(sub_thr2[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so");
    char viewCommand2[128] = "empty";
    for (int32_t i = 0; i < threadNum; i++) {
        (void)sprintf(
            viewCommand2, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f DataLog_reliability_004_027_%d_patchV2", i + 1);
        ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = TestUninstallDatalog(soName[i], NULL, false);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

// 线程加载降级so
void *LoadSameNameRollBackSo(void *args)
{
    char nsName2[128] = "./datalog_file/DataLog_reliability_004_027_rollbackV2.so";
    char *patchSoName = (char *)args;
    (void)sprintf(nsName2, "./datalog_file/%s_rollbackV2.so", patchSoName);
    (void)TestLoadRollbackSo(nsName2);
}

// 线程降级不同so
void *LoadDiffNameRollBackSo(void *args)
{
    int32_t index = *(int32_t *)args;
    char nsName2[128] = "./datalog_file/DataLog_reliability_004_027_1_rollbackV2.so";
    (void)sprintf(nsName2, "./datalog_file/DataLog_reliability_004_027_%d_rollbackV2.so", index);
    (void)TestLoadRollbackSo(nsName2);
}

// 线程降级不同so
void *LoadDiffNameRollBackSo1(void *args)
{
    int32_t index = *(int32_t *)args;
    char nsName2[128] = "./datalog_file/DataLog_reliability_004_056_1_rollbackV2.so";
    (void)sprintf(nsName2, "./datalog_file/DataLog_reliability_004_056_%d_rollbackV2.so", index);
    (void)TestLoadRollbackSo(nsName2);
}
/* ****************************************************************************
Description  : 028.并发降级同一个so和并发降级多个so
1.并发降级同一个so预期其中一个so成功，其余so失败
2.并发降级不同so，预期so降级都能成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_028)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    // 并发升级相同so
    char nsName1[128] = "DataLog_reliability_004_027";
    char nsName2[128] = "DataLog_reliability_004_027_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_027_rollbackV2.so";

    //  并发升级不同so
    char *soName[10] = {(char *)"DataLog_reliability_004_027_1", (char *)"DataLog_reliability_004_027_2",
        (char *)"DataLog_reliability_004_027_3", (char *)"DataLog_reliability_004_027_4",
        (char *)"DataLog_reliability_004_027_5", (char *)"DataLog_reliability_004_027_6",
        (char *)"DataLog_reliability_004_027_7", (char *)"DataLog_reliability_004_027_8",
        (char *)"DataLog_reliability_004_027_9", (char *)"DataLog_reliability_004_027_10"};

    char *upgradeSoName[10] = {(char *)"DataLog_reliability_004_027_1_patchV2",
        (char *)"DataLog_reliability_004_027_2_patchV2", (char *)"DataLog_reliability_004_027_3_patchV2",
        (char *)"DataLog_reliability_004_027_4_patchV2", (char *)"DataLog_reliability_004_027_5_patchV2",
        (char *)"DataLog_reliability_004_027_6_patchV2", (char *)"DataLog_reliability_004_027_7_patchV2",
        (char *)"DataLog_reliability_004_027_8_patchV2", (char *)"DataLog_reliability_004_027_9_patchV2",
        (char *)"DataLog_reliability_004_027_10_patchV2"};
    char viewCommand[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_004_027";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    for (int32_t i = 0; i < 10; i++) {
        TestUninstallDatalog(soName[i], NULL, false);
    }
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = LoadSoFile(soName[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "1.加载多个so");

    // 加载升级so
    for (int32_t i = 0; i < 10; i++) {
        (void)TestLoadUpgradeDatalog(upgradeSoName[i]);
    }
    (void)TestLoadUpgradeDatalog(nsName2);

    // 多个线程并发加载相同降级so
    int32_t threadNum = 10;
    pthread_t sub_thr1[threadNum];
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&sub_thr1[i], NULL, LoadSameNameRollBackSo, (char *)nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(sub_thr1[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so,间隔一段时间查询视图");

    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);
    system(viewCommand);

    // 开启多个线程降级不同so
    pthread_t sub_thr2[threadNum];
    int32_t dataNum[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        dataNum[i] = i + 1;
        ret = pthread_create(&sub_thr2[i], NULL, LoadDiffNameRollBackSo, &dataNum[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = pthread_join(sub_thr2[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so");
    char viewCommand2[128] = "empty";
    for (int32_t i = 0; i < threadNum; i++) {
        (void)sprintf(
            viewCommand2, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_004_027_%d", i + 1);
        ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            system(viewCommand2);
            AW_FUN_Log(LOG_DEBUG, "cmd is %s", viewCommand2);
            system(viewCommand2);
        }
    }

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = TestUninstallDatalog(soName[i], NULL, false);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

// thread: when upgrade successful to write and rollBack so
void *WriteAndRollBack(void *args)
{
    int32_t ret = -1;
    int32_t cycle = 20;
    char tableD[] = "D000";
    AW_FUN_Log(LOG_INFO, "thread: WriteAndRollBack");
    char *patchSoName = (char *)args;
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    (void)sprintf(soName2, "./datalog_file/%s_rollbackV2.so", patchSoName);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t count = 10;
    // view check patch state is RollBack success
    while (count > 0) {
        do {
            ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
            if (ret == -1) {
                sleep(4);
                cycle--;
            }
            if (cycle == 0) {
                system(viewCommand);
                AW_FUN_Log(LOG_INFO, "【check view fail VERSION: [v0.0.0]->[v1.0.0]】");
                return (void *)0;
            }
        } while (ret == -1);
        if (ret != 0) {
            count--;
            continue;
        }

        int32_t dataNum = 100;
        int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
        for (int32_t i = 0; i < dataNum; i++) {
            count1[i][0] = i + 1;
            count1[i][1] = i + 1;
            count1[i][2] = 1;
        }

        // write data count +
        cycle = 10;
        do {
            ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
            if (ret) {
                sleep(2);
                cycle--;
                if(ret == GMERR_UNDEFINED_TABLE){
                    continue;
                }
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【upgrade write count + data fail ret is %d】", ret);
                return (void *)0;
            }

        } while ((ret != 0) && (cycle != 0));

        for (int32_t i = 0; i < dataNum; i++) {
            count1[i][2] = -1;
        }

        // write data count -
        cycle = 10;
        do {
            ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
            if (ret) {
                sleep(2);
                cycle--;
                if(ret == GMERR_UNDEFINED_TABLE){
                    continue;
                }
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【upgrade write count - data fail ret is %d】", ret);
                return (void *)0;
            }

        } while ((ret != 0) && (cycle != 0));
        sleep(2);
        // rollback
        cycle = 10;
        do {
            (void)TestLoadRollbackSo(soName2);
            sleep(1);
            ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
            if (ret == -1) {
                cycle--;
                sleep(4);
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【rollback so fail】");
                return (void *)0;
            }
        } while (ret == -1);
        if (ret != 0) {
            count--;
            continue;
        }
        count--;
    }
}

// thread: when rollBack successful to write and upgrade so
void *WriteAndUpgrade(void *args)
{
    int32_t ret = -1;
    int32_t cycle = 20;
    char tableD[] = "D000";
    AW_FUN_Log(LOG_INFO, "thread: WriteAndUpgrade");
    char *patchSoName = (char *)args;
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    (void)sprintf(nsName2, "%s_patchV2", patchSoName);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // view check patch state is upgrade success
    int32_t count = 10;
    while (count > 0) {
        do {
            ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
            if (ret == -1) {
                sleep(4);
                cycle--;
            }
            if (cycle == 0) {
                system(viewCommand);
                AW_FUN_Log(LOG_INFO, "【check view fail VERSION: [v1.0.0]->[v0.0.0]】");
                return (void *)0;
            }
        } while (ret == -1);
        if (ret != 0) {
            count--;
            continue;
        }

        int32_t dataNum = 100;
        int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
        for (int32_t i = 0; i < dataNum; i++) {
            count1[i][0] = i + 1;
            count1[i][1] = i + 1;
            count1[i][2] = 1;
        }

        // write data count +
        cycle = 10;
        do {
            ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
            if (ret) {
                sleep(2);
                cycle--;
                if(ret == GMERR_UNDEFINED_TABLE){
                    continue;
                }
            }
            if (cycle == 0) {
                system(viewCommand);
                AW_FUN_Log(LOG_INFO, "【rollBack write count + data fail ret is %d】", ret);
                return (void *)0;
            }

        } while ((ret != 0) && (cycle != 0));
        for (int32_t i = 0; i < dataNum; i++) {
            count1[i][2] = -1;
        }

        // write data count -
        cycle = 10;
        do {
            ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
            if (ret) {
                sleep(2);
                cycle--;
                if(ret == GMERR_UNDEFINED_TABLE){
                    continue;
                }
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【rollBack write count - data fail ret is %d】", ret);
                return (void *)0;
            }

        } while ((ret != 0) && (cycle != 0));
        cycle = 10;
        sleep(2);
        // upgrade
        do {
            (void)TestLoadUpgradeDatalog(nsName2);
            sleep(1);
            ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
            if (ret == -1) {
                sleep(4);
                cycle--;
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【upgrade so fail】");
                return (void *)0;
            }
        } while (ret == -1);
        if (ret != 0) {
            count--;
            continue;
        }
    }
}

// thread: read data
void *ReadData(void *args)
{
    char tableD[] = "D000";
    AW_FUN_Log(LOG_INFO, "thread: ReadData");
    sleep(5);
    int32_t count = 10;
    while (true && count > 0) {
        system("gmsysview count D000");
        sleep(15);
        count--;
    }
}

// thread: unload and load so
void *UnloadAndLoad(void *args)
{
    char *patchSoName = (char *)args;
    char nsName1[128] = "DataLog_reliability_004_023";
    (void)sprintf(nsName1, "%s", patchSoName);
    int32_t ret = -1;
    int32_t cycle = 100;  // 失败重试次数
    int32_t count = 10;   // load and unload 循环次数
    while (true) {
        // unload so
        do {
            ret = TestUninstallDatalog(nsName1, NULL, false);
            if (ret == -1) {
                sleep(3);
                cycle--;
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【unload so fail ret = %d】", ret);
                break;
            }
        } while (ret == -1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret != 0) {
            return (void *)0;
        }
        sleep(15);
        // load so
        cycle = 100;
        ret = -1;
        do {
            ret = LoadSoFile(nsName1);
            if (ret == -1) {
                sleep(3);
                cycle--;
            }
            if (cycle == 0) {
                AW_FUN_Log(LOG_INFO, "【load so fail】");
                break;
            }
        } while (ret == -1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret != 0) {
            return (void *)0;
        }
        count--;
        if (count == 0) {
            break;
        }
    }
}
/* ****************************************************************************
Description  : 029.DDL, DML, DQL并发, 非交互式事务(非显示事务）
1.线程1写升级后数据，预期so升级成功后写入成功，否则失败，so降级，降级成功验证upgradeversion字段值
2.启动线程2反复读表数据;
3.启动线程3反复加载卸载ns1.A(遇到12002则间隔5s再重试);
4.线程4写降级后数据，预期so降级成功后写入成功，否则失败，so升级升级成功，校验upgradeVersion字段值
以上操作循环100次

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_029)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_023";
    char nsName2[128] = "DataLog_reliability_004_023_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_023_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // upgrade
    (void)TestLoadUpgradeDatalog(nsName2);
    sleep(1);

    // 开启多个线程
    int32_t threadNum = 4;
    pthread_t threadArray[threadNum];
    ret = pthread_create(&threadArray[0], NULL, WriteAndRollBack, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[1], NULL, WriteAndUpgrade, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[2], NULL, ReadData, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[3], NULL, UnloadAndLoad, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadArray[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // 查询so是否还存在再进行卸载
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_023", nsName1);
    if (ret == 0) {
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // view check so is unloaded
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_023", nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

// 当前仅满足1000表规格(字段63，索引31）  block 1
/* ****************************************************************************
 Description  : 030.so升降级时共享不足，升降级失败,释放内存后升级成功，降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_030)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 0");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 500;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_030";
    char nsName2[128] = "DataLog_reliability_004_030_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_030_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, 120);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 写数据后占用全部共享内存直到共享内存满

    // 加载so后申请共享内存
    int32_t allocTimes = 4500;
    int shmid[allocTimes];
    key_t key[allocTimes];
    for (int i = 0; i < allocTimes; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;  // 未初始化赋值,这里会给它一个随机值
    for (int i = 0; i < allocTimes; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        if (shmid[i] == -1) {
            break;
        }
        char *p;
        p = (char *)shmat(shmid[i], NULL, 0);
        // 对共享内存实现读写
        memset(p, 1, MEGABYTE);
        // 取消映射
        shmdt(p);
        if (i % 500 == 0) {
            AW_FUN_Log(LOG_INFO, "count is %d\n", count);
        }

        count++;
    }

    // 升级时写入数据成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    sleep(30);
    cycle = 10;
    ret = -1;
    while (ret == -1 && cycle > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_FAIL");
        sleep(10);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret == -1) {
        system(viewCommand);
    }

    // 释放共享内存
    AW_FUN_Log(LOG_INFO, "count is %d\n", count);
    for (int i = 0; i < count - 1; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "count is %d\n", count);
        }
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
     ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载so
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDropVertexLabel(g_stmt, "N000");
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, 120);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(60);
    ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
    cycle = 10;
    ret = -1;
    while (ret == -1 && cycle > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        sleep(10);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret == -1) {
        system(viewCommand);
    }

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // block1 1时进行升降级后，表数据的upgradeVersion不会发生变化
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + 11, 120);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "261");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "261");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "68121");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    sleep(1);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    cycle = 10;
    ret = -1;
    while (ret == -1 && cycle > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        sleep(10);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret == -1) {
        system(viewCommand);
    }

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);

    ret = executeCommand((char *)"gmsysview count D019", "261");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "261");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "261");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 031.系统占用动态内存，加载升级so内存不足升级失败，待空间释放后，加载升级降级so成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_031)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\" \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 提前申请视图元数据内存
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 500;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_030";
    char nsName2[128] = "DataLog_reliability_004_030_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_030_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 加载so后申请动态内存直到申请失败
    (void)mallopt(M_MMAP_THRESHOLD, 0);
    system("free -h");
    int32_t allocTimes = 9000;
    void *Myblock[allocTimes];
    // 消耗掉系统的内存
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = NULL;
    }
    // 系统内存在申请MB级别时容易申请失败，设备挂掉
    int count = 0;
    for (int i = 0; i < allocTimes; i++) {
        Myblock[i] = (void *)malloc(MEGABYTE);
        if (!Myblock[i]) {
            AW_FUN_Log(LOG_INFO, "malloc err\n");
            break;
        }
        memset(Myblock[i], 1, MEGABYTE);
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "Currently allocating %d MB\n", i + 1);
        }
        count++;
    }

    // 升级so内存太低popen执行失败  
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    cycle = 2;
    ret = -1;
    // 内存太低popen执行失败
    while (ret == -1 && cycle > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        sleep(10);
        cycle--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, -1);
    if (ret == -1) {
        system(viewCommand);
    }
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    // 释放消耗掉的内存
    for (int i = 0; i < count; i++) {
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d\n", i);
        }

        free(Myblock[i]);
    }
    system(viewCommand);

    //  释放内存后查询热补丁视图
    ret = 1;
    int32_t cycleTimes = 100;
    while (cycleTimes > 0) {
        ret = executeCommand(viewCommand, "fetched all records, finish!");
        if (ret != 0) {
            sleep(1);
            system(viewCommand);
            cycleTimes--;
            continue;
        }
        break;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 再次加载so
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 10;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常 block1
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 100);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "250");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "63750");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    sleep(1);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 20;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "250");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "250");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 032.申请动态内存不足升级失败，释放内存，升级降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_032)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=192\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=96\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\" \"datalogResetWhenUpgradeRollbackFail=0\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 500;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    // 降级so
    char nsName1[128] = "DataLog_reliability_004_030";
    char nsName2[128] = "DataLog_reliability_004_030_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_030_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 写入数据失败
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    sleep(10);

    // 查询热补丁视图
    ret = 1;
    int32_t cycleTimes = 20;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: REDO_FAIL_ROLL_BACK_FAIL");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 再次升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 20;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 100);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "65025");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    sleep(1);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 20;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "255");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 0);
    AW_MACRO_EXPECT_EQ_INT(255, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 0);
    AW_MACRO_EXPECT_EQ_INT(255, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 0);
    AW_MACRO_EXPECT_EQ_INT(255, upgradeVersion);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 033.线程一进行不断的升降级，线程二进行不断的写数据

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_033)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_033";
    char nsName2[128] = "DataLog_reliability_004_033_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_033_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    pthread_t sub_thr1;
    // 开启线程不断写数据
    ret = pthread_create(&sub_thr1, NULL, WriteInpTable, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 开启线程不断升降级so
    pthread_t sub_thr2;
    ret = pthread_create(&sub_thr2, NULL, UpgeadeAndRollBack, (void *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    g_isEnd = true;
    ret = pthread_join(sub_thr2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 034.文件句柄超限
1.设置文件句柄为1024;
2.句柄耗尽预期升级失败，释放一个句柄升级成功
3.句柄耗尽预期降级级失败，释放一个句柄降级成功,
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_034)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=16\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    // 降级so
    char nsName1[128] = "DataLog_reliability_004_031";
    char nsName2[128] = "DataLog_reliability_004_031_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_031_rollbackV2.so";
    int cycle = 1;

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
#if !defined RUN_DATACOM_HPE
    struct rlimit rlim;
    rlim.rlim_cur = 1024;
    rlim.rlim_max = 1024;
    ret = setrlimit(RLIMIT_NOFILE, &rlim);
    EXPECT_EQ(ret, GMERR_OK);
#endif
    system("ulimit -n");
    system("gmsysview count");
    // 获取当前进程已占用文件句柄数
    int32_t fileStmts = GetFileStmtNums();
    AW_FUN_Log(LOG_INFO, "当前进程已占用%d文件句柄", fileStmts);
    // 升级成功
    int32_t openTimes = 1024 - fileStmts;  // 已存在其它文件句柄
    int32_t actualTimes = 0;
    FILE *fp[openTimes] = {0};
    for (int i = 0; i < openTimes; i++) {
        fp[i] = fopen("./datalog_file/DataLog_reliability_004_031_patchV2.so", "a+");
        if (fp[i] == NULL) {
            AW_FUN_Log(LOG_DEBUG, "fopen %dth error", i);
            break;
        }
        actualTimes++;
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    for (int i = 0; i < 2; i++) {
        fclose(fp[i]);
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int i = 2; i < actualTimes; i++) {
        fclose(fp[i]);
    }

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + (dataNum / 2), dataNum / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 规则需要的内存大一点
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + (dataNum / 2), dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "20000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 1);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    // 降级
    actualTimes = 0;
    fileStmts = GetFileStmtNums();
    openTimes = 1024 - fileStmts;
    FILE *fp1[openTimes] = {0};
    for (int i = 0; i < openTimes; i++) {
        fp1[i] = fopen("./datalog_file/DataLog_reliability_004_002_rollbackV2.so", "a+");
        if (fp1[i] == NULL) {
            AW_FUN_Log(LOG_DEBUG, "fopen error");
            break;
        }
        actualTimes++;
    }
    AW_FUN_Log(LOG_INFO, "当前进程实际已占用%d文件句柄", actualTimes+fileStmts);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));

    for (int i = 0; i < 2; i++) {
        fclose(fp1[i]);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = -1;
    for (int i = 2; i < actualTimes; i++) {
        fclose(fp1[i]);
    }
    cycleTimes = 10;
    while (ret == -1 && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret == -1) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    system("gmsysview count ADD1");

    ret = executeCommand((char *)"gmsysview count D019", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D019", 0);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D020", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"D021", 0);
    AW_MACRO_EXPECT_EQ_INT(100, upgradeVersion);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 035.时钟跳变
1.升级过程中时钟向前跳变;
2.降级过程中时钟往前跳变
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_035)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 65");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
        count1[i][3] = 35000;  // 计划35s后过期
    }

    char nsName1[128] = "DataLog_reliability_004_035";
    char nsName2[128] = "DataLog_reliability_004_035_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_035_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int64_t readStartTime = 0;
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so前 【时间戳】%lu", readStartTime);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so后【时间戳】 %lu", readStartTime);
    sleep(1);
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(g_command);

    AW_FUN_Log(LOG_INFO, "时钟往后跳变20s");
    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(15);
    AW_FUN_Log(LOG_INFO, "等待过期表数据过期");
    // // 读数据
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 输出表订阅接收
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 3, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 降级时向前跳变
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockLeft);
    system(g_command);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(35);

    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA2", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 4, RECV_TIMEOUT / 5);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 036.时钟跳变
1.升级过程中时钟向后跳变;
2.降级过程中时钟往后跳变
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_036)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 200;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = 1;
        count1[i][3] = 10000;  // 计划10s后过期
    }

    char nsName1[128] = "DataLog_reliability_004_035";
    char nsName2[128] = "DataLog_reliability_004_035_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_035_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 64");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int64_t readStartTime = 0;
    GetTimeMs(&readStartTime);
    system("gmsysview record inpA 1");
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so前%lu", readStartTime);
    system("gmsysview record inpA 1");
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GetTimeMs(&readStartTime);
    AW_FUN_Log(LOG_INFO, "升级so后%lu", readStartTime);
    sleep(1);
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(g_command);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据完成");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);

    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(5);
    AW_FUN_Log(LOG_INFO, "等待过期表数据过期");
    // // 读数据
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 输出表订阅接收
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 降级时向前跳变
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    memset(g_command, 0, sizeof g_command);
    (void)sprintf(g_command, "%s %s", "sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh", g_clockRight);
    system(g_command);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(3);
    // 时钟跳变不会提前过期
    ret = executeCommand((char *)"gmsysview count inpA", "200");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    upgradeVersion = 0;
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA2", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(200, upgradeVersion);
    sleep(9);
    upgradeVersion = GetUpgradeVersionByViewResult((char *)"inpA", 0);
    AW_MACRO_EXPECT_EQ_INT(0, upgradeVersion);

    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum * 3, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}
/* ****************************************************************************
Description  : 037.客户端异常退出
1.客户端反复加载升级降级卸载so 10000遍的过程中随机异常退出
2.重启客户端，查询so及upgradeversion，再次加载so升级so降级so卸载so
3.操作成功时检查结果是否正常
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_037)
{
    int32_t ret = 0;
    AW_FUN_Log(LOG_STEP, "test start");
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    // 被kill客户端进程
    char clientName[20] = "patch_client_1";
    (void)sprintf(g_startClientName, "./%s", clientName);
    // kill客户端进程
    char stopClientName[20] = "patch_clientExit_1";
    (void)sprintf(g_stopClientName, "./%s", stopClientName);
    ret = pthread_create(&client_thr[0], NULL, STARTCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, STOPCLIENT, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (uint32_t i = 0; i < thread_num; i++) {
        pthread_join(client_thr[i], NULL);
    }
    // 客戶端异常退出环境干扰排除
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    AW_FUN_Log(LOG_STEP, " test end");
}

/* ****************************************************************************
Description  : 038.挂起服务端进程
1.客户端反复升级降级
2.挂起服务端进程, 客户端请求失败
3.65秒后解挂服务端进程
4.客户端业务恢复
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_038)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_030";
    char nsName2[128] = "DataLog_reliability_004_030_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_030_rollbackV2.so";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    free(schema);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t cycle = 2;
    // .d文件加载：创建表和连接规则
    while (cycle > 0) {
        AW_FUN_Log(LOG_STEP, "1.load so");

        // 再次加载写入数据成功
        ret = LoadSoFile(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count");

        // 升级成功
        ret = TestLoadUpgradeDatalog(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        // 升级时挂起服务端
        system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_server");
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 查询热补丁视图以及表upgradeVersion字段值是否正常
        int32_t getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        // 尝试写入数据
        ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + 11, dataNum / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        // 读数据
        ret = executeCommand((char *)"gmsysview count D019", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D020", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D021", "484");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count");
        sleep(1);

        // 降级
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        // 降级时挂起服务端
        system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_server");
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);

        ret = executeCommand((char *)"gmsysview count D019", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D020", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count D021", "22");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview count");

        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        cycle--;
    }

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
/* ****************************************************************************
Description  : 039.挂起客户端进程
1.客户端反复升级降级
2.挂起客户端进程
3.65秒后解挂客户端进程
4.客户端业务恢复
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_039)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    int32_t ret = 0;
    system("patch_clientStop_1");
    // 升级时挂起服务端
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat isUpgrade.txt");
        ret = executeCommand(g_command, "No such file or directory");
        if (!ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    } while (!ret);
    system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_clent");
    do {
        snprintf(g_command, MAX_CMD_SIZE, "cat rollBack.txt");
        ret = executeCommand(g_command, "No such file or directory");
        if (!ret) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
    } while (!ret);
    // 降级时挂起服务端
    system("sh ${TEST_HOME}/reliability/rel_datalog_test/datalogRel.sh rProc_kill19_18_clent");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 040.超大规格表
1.加载2000个表(包括所有表类型, 每个表包含64个字段和32个索引(含变长), 包含所有运算规则)和1024个UDF,
写数据直到内存满，删除数据，升级so，写数据，内存满，删除数据，再降级 2.步骤一反复执行10000遍, 预期服务端内存保持稳定,
不上涨
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_040)
{
    AW_FUN_Log(LOG_STEP, "test begin");

    char nsName1[128] = "DataLog_reliability_004_040";
    char nsName2[128] = "DataLog_reliability_002";
    char upgradePatch[128] = "DataLog_reliability_004_040_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_040_rollbackV2.so";
    int32_t ret = 0;
    // 循环次数
    int32_t cycle = 10;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$COM_MEM_SUMMARY");

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#define MAX_CMD_SIZE 1024
    char g_command[MAX_CMD_SIZE] = {0};
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA061[] = "A061";
    int32_t count1[][7] = {{1, 2, 1, 2, 2, 1, 2}};
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    TestUninstallDatalog(nsName2, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = LoadSoFile(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int i;
    while (cycle > 0) {
        if (cycle % 100 == 0) {
            AW_FUN_Log(LOG_STEP, "--------------当前还有 %d 次----------------------------", cycle);
        }
        AW_FUN_Log(LOG_STEP, "1.写入数据直到内存满");
        i = 0;
        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap1.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "2.删除数据");
        int deleteI;
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 0;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap2.txt");
        // 升级so
        ret = TestLoadUpgradeDatalog(upgradePatch);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = 1;
        int32_t cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                system(viewCommand);
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_FUN_Log(LOG_INFO, "3.升级so成功");
        i = 0;

        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_FUN_Log(LOG_INFO, "4.升级so写入数据直到内存满");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap3.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "5.升级so删除数据");
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 1;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 降级
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        ret = -1;
        while (ret) {
            ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
            if (ret) {
                sleep(1);
                AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
            }
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        AW_FUN_Log(LOG_INFO, "6.降级so成功");
        i = 0;
        while (true) {
            count1[0][1] = ++i;
            count1[0][2] = ++i;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchInsertByte256(g_conn, g_stmt, tableA020, count1, 1);
            ret = BatchInsertByte256(g_conn, g_stmt, tableA019, count1, 1);
            if (ret) {
                AW_FUN_Log(LOG_STEP, "ret = %d, i=%d\n", ret, i);
                break;
            }
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
        AW_FUN_Log(LOG_INFO, "7.降级写入数据直到内粗满");
        system("gmsysview -q V\\$STORAGE_HEAP_STAT >heap4.txt");
        // 删除数据
        AW_FUN_Log(LOG_STEP, "8.降级删除数据");
        deleteI = 0;
        while (true) {
            count1[0][1] = ++deleteI;
            count1[0][2] = ++deleteI;
            count1[0][6] = 0;
            char tableA020[] = "A020";
            char tableA019[] = "A019";
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA020, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            ret = BatchDeleteByte256(g_conn, g_stmt, tableA019, count1, 1, GMERR_OK, false, PKTHREEDELETE);
            if (deleteI > i && deleteI % 2 != 1) {
                deleteI = 1;
            }
            if (deleteI > i && deleteI % 2 == 1) {
                AW_FUN_Log(LOG_STEP, "deleteI = %d\n", i);
                break;
            }
        }
        ret = executeCommand((char *)"gmsysview count A021", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A020", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = executeCommand((char *)"gmsysview count A019", "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        cycle--;
    }
    // 卸载so
    AW_FUN_Log(LOG_STEP, "2.uninstall so");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = TestUninstallDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    GmcDropVertexLabel(g_stmt, "N000");
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmsysview -q V\\$COM_MEM_SUMMARY");
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 041.连接占满升降级失败，断开一个已有连接升降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_041)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_030";
    char nsName2[128] = "DataLog_reliability_004_030_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_030_rollbackV2.so";

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    free(schema);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");

    // 再次加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    uint32_t existConnNum;
    testGetConnNum(&existConnNum);
    GmcConnT *conn1[MAX_CONN_SIZE] = {0};
    for (int i = 0; i < MAX_CONN_SIZE - existConnNum; i++) {
        testGmcConnect(&conn1[i]);
    }

    // 升级成功
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    for (int i = 0; i < 1; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = -1;
    while (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    // 尝试写入数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1 + 11, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 读数据
    ret = executeCommand((char *)"gmsysview count D019", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "484");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count D021");
    sleep(1);

    // 降级
    for (int i = 0; i < 1; i++) {
        testGmcConnect(&conn1[i]);
    }
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    for (int i = 0; i < 1; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    for (int i = 1; i < MAX_CONN_SIZE - existConnNum; i++) {
        ret = testGmcDisconnect(conn1[i]);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = -1;
    while (ret) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            AW_FUN_Log(LOG_INFO, "load patch error %d", ret);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"ADD1", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, getUpgradeVersion);

    ret = executeCommand((char *)"gmsysview count D019", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D020", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count D021", "22");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(g_command);
    }
#endif
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

// 当前block 1未触发16006
/* ****************************************************************************
Description  : 042.升级重做过程中，订阅消息队列满，升级失败
1.加载so
2.升级so，含多个订阅端构造重做时推送大量数据，回调中减缓接收速度，重做失败，日志16006，
3.取消订阅，卸载so后再重新加载升级so，写入数据，降级so，触发推送，预期推送失败16006，升级失败
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_042)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 0");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 500;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_042";
    char nsName2[128] = "DataLog_reliability_004_042_patchV2";
    char nsName3[128] = "DataLog_reliability_004_042_patchV3";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_042_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/DataLog_reliability_004_042_rollbackV3.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅G000输出表
    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);

    // 订阅G001输出表
    GmcConnT *subConnOutput1;
    GmcStmtT *subStmtOutput1;
    SnUserDataWithFuncT *userDataOutput1;
    const char *subConnNameOutput1 = "testSubOutput1";
    ret = testSubConnect(&subConnOutput1, &subStmtOutput1, 1, g_epoll_reg_info, subConnNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput1 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub1.gmjson", &sub_infoOutput1);
    EXPECT_NE((void *)NULL, sub_infoOutput1);
    const char *subNameOutput1 = "testSub_Output1";
    GmcSubConfigT tmp_sub_infoOutput1;
    tmp_sub_infoOutput1.subsName = subNameOutput1;
    tmp_sub_infoOutput1.configJson = sub_infoOutput1;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput1, &userDataOutput1, &tmp_sub_infoOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput1);

    // 订阅G002输出表
    GmcConnT *subConnOutput2;
    GmcStmtT *subStmtOutput2;
    SnUserDataWithFuncT *userDataOutput2;
    const char *subConnNameOutput2 = "testSubOutput2";
    ret = testSubConnect(&subConnOutput2, &subStmtOutput2, 1, g_epoll_reg_info, subConnNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput2 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub2.gmjson", &sub_infoOutput2);
    EXPECT_NE((void *)NULL, sub_infoOutput2);
    const char *subNameOutput2 = "testSub_Output2";
    GmcSubConfigT tmp_sub_infoOutput2;
    tmp_sub_infoOutput2.subsName = subNameOutput2;
    tmp_sub_infoOutput2.configJson = sub_infoOutput2;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput2, &userDataOutput2, &tmp_sub_infoOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput2);

    // 订阅G003输出表
    GmcConnT *subConnOutput3;
    GmcStmtT *subStmtOutput3;
    SnUserDataWithFuncT *userDataOutput3;
    const char *subConnNameOutput3 = "testSubOutput3";
    ret = testSubConnect(&subConnOutput3, &subStmtOutput3, 1, g_epoll_reg_info, subConnNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput3 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub3.gmjson", &sub_infoOutput3);
    EXPECT_NE((void *)NULL, sub_infoOutput3);
    const char *subNameOutput3 = "testSub_Output3";
    GmcSubConfigT tmp_sub_infoOutput3;
    tmp_sub_infoOutput3.subsName = subNameOutput3;
    tmp_sub_infoOutput3.configJson = sub_infoOutput3;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput3, &userDataOutput3, &tmp_sub_infoOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput3);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);
    ret = GmcUnSubscribe(g_stmt, subNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput1->data);
    free(userDataOutput1);
    ret = GmcUnSubscribe(g_stmt, subNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput2->data);
    free(userDataOutput2);
    ret = GmcUnSubscribe(g_stmt, subNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput3->data);
    free(userDataOutput3);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput3, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 订阅G000输出表
    subConnOutput = NULL;
    subStmtOutput = NULL;
    userDataOutput = NULL;
    subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    subNameOutput = "testSub_Output";
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);

    // 订阅G001输出表
    subConnOutput1 = NULL;
    subStmtOutput1 = NULL;
    userDataOutput1 = NULL;
    subConnNameOutput1 = "testSubOutput1";
    ret = testSubConnect(&subConnOutput1, &subStmtOutput1, 1, g_epoll_reg_info, subConnNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput1 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub1.gmjson", &sub_infoOutput1);
    EXPECT_NE((void *)NULL, sub_infoOutput1);
    subNameOutput1 = "testSub_Output1";
    tmp_sub_infoOutput1.subsName = subNameOutput1;
    tmp_sub_infoOutput1.configJson = sub_infoOutput1;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput1, &userDataOutput1, &tmp_sub_infoOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput1);

    // 订阅G002输出表
    subConnOutput2 = NULL;
    subStmtOutput2 = NULL;
    userDataOutput2 = NULL;
    subConnNameOutput2 = "testSubOutput2";
    ret = testSubConnect(&subConnOutput2, &subStmtOutput2, 1, g_epoll_reg_info, subConnNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput2 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub2.gmjson", &sub_infoOutput2);
    EXPECT_NE((void *)NULL, sub_infoOutput2);
    subNameOutput2 = "testSub_Output2";
    tmp_sub_infoOutput2.subsName = subNameOutput2;
    tmp_sub_infoOutput2.configJson = sub_infoOutput2;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput2, &userDataOutput2, &tmp_sub_infoOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput2);

    // 订阅G003输出表
    subConnOutput3 = NULL;
    subStmtOutput3 = NULL;
    userDataOutput3 = NULL;
    subConnNameOutput3 = "testSubOutput3";
    ret = testSubConnect(&subConnOutput3, &subStmtOutput3, 1, g_epoll_reg_info, subConnNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sub_infoOutput3 = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub3.gmjson", &sub_infoOutput3);
    EXPECT_NE((void *)NULL, sub_infoOutput3);
    subNameOutput3 = "testSub_Output3";
    tmp_sub_infoOutput3.subsName = subNameOutput3;
    tmp_sub_infoOutput3.configJson = sub_infoOutput3;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput3, &userDataOutput3, &tmp_sub_infoOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput3);
    dataNum = 10;
    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    // 新增数据表未触发重做
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);

    ret = GetTableUpgradeVersionByApi((char *)"G001", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G002", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G003", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 降级
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GetTableUpgradeVersionByApi((char *)"inpA2", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"inpA", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"G000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, getUpgradeVersion);
    sleep(3);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级1
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int32_t dataNum1 = 1;
    // 写数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + 1, dataNum1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + 1, dataNum1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级2
    ret = TestLoadUpgradeDatalog(nsName3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, 90, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    dataNum = 480;
    // 写数据
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + 20, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + 20, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput1->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput2->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput3->data, GMC_SUB_EVENT_INSERT, dataNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次降级1
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName3));
    ret = 1;
    cycleTimes = 100;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }

    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT");

    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput->data);
    free(userDataOutput);
    ret = GmcUnSubscribe(g_stmt, subNameOutput1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput1->data);
    free(userDataOutput1);
    ret = GmcUnSubscribe(g_stmt, subNameOutput2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput2->data);
    free(userDataOutput2);
    ret = GmcUnSubscribe(g_stmt, subNameOutput3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataOutput3->data);
    free(userDataOutput3);

    ret = testGmcDisconnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(subConnOutput3, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("gmadmin -cfgName datalogResetWhenUpgradeRollbackFail -cfgVal 1");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}
// 2024.5.14锁桶上的锁是随机hash散列分配的
/* ****************************************************************************
Description  : 043.升级重做时需要使用到事务锁
1.加载so提前将事务锁占满不释放5114
2.升级so(含数据重做)，升级重做失败12002
3.释放事务锁，再次升级升级成功
11.29：当前(不同表)表锁和行锁可以共存共同占用5114锁资源
2024.5.14 当前锁无法占满
2024.5.14锁桶上的锁是随机hash散列分配的
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_043)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=6000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "A001";
    char tableB[] = "A001000";
    int32_t dataNum = 1;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = i + 1;
    }

    char nsName1[128] = "DataLog_reliability_004_043";
    char nsName2[128] = "DataLog_reliability_004_043_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_004_043_rollbackV2.so";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview record A001 1");

    // 占满事务锁，预期升级失败
    pthread_t sub_thr;
    ret = pthread_create(&sub_thr, NULL, ReadAndCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    while (g_isLockTransation) {
        sleep(1);
    }
    // 升级加载需要锁
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做失败锁不可用");
    ret = CheckLog((char *)"GMERR-1012002");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 释放锁重做成功
    //  卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "load so again");

    // 加载写入数据成功
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA, count1, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview record A001 1");
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "SUCCESS");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 占用锁降级重做失败
    g_isLockTransation = true;
    ret = pthread_create(&sub_thr, NULL, ReadAndCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(1);
    AW_FUN_Log(LOG_INFO, "占用锁降级重做失败");
    system("gmsysview -q V\\$STORAGE_LOCK_OVERVIEW");
    while (g_isLockTransation) {
        sleep(1);
    }
    ret = TestLoadRollbackSo(soName2);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
        if (ret) {
            sleep(1);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"A001", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"A001000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

/* ****************************************************************************
Description  : 044.资源池创建过多耗尽内存后升降级
1. 加载多个资源池, 每个资源池的容量达到上限,
 直至内存耗尽加载失败对加载成功so进行升级写数据，降级写数据卸载so，
 加载之前加载失败so，写数据，升级so写数据，降级so写数据;
2. 步骤1反复执行10000遍;(10000次待验证)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_044)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxNormalTableNum=4000\"");
    // 当前资源表占用内存为共享内存
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\" \"maxResPoolShmSize=300\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName1[128] = "maxcountresourcetable001_1";
    char nsName2[128] = "maxcountresourcetable002_1";
    char nsName3[128] = "maxcountresourcetable003_1";
    char patchName2[128] = "maxcountresourcetable002_1_patchV2";
    char patchName3[128] = "maxcountresourcetable003_1_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/maxcountresourcetable002_1_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/maxcountresourcetable003_1_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 确保数据量能够看护到内存上涨
    int cycle = 400;
    char tableB[] = "ns1.B";
    char tableC[] = "ns1.C";
    char tableC2[] = "ns2.C";
    char tableC3[] = "ns3.C";
    int32_t count[][7] = {{1, 2, 1, 2, 2, 1, 2}};

    // // 写入5条数据

    // 创建外部表
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    while (cycle > 0) {
        // 卸载同名datalog.so
        TestUninstallDatalog(nsName1, NULL, false);
        TestUninstallDatalog(nsName2, NULL, false);
        TestUninstallDatalog(nsName3, NULL, false);

        // .d文件加载：创建表和连接规则
        AW_FUN_Log(LOG_STEP, "1.load so");
        ret = LoadSoFile(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = LoadSoFile(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 对加载成功so进行写数据
        ret = BatchInsert(g_conn, g_stmt, tableC, count, 1);
        if (ret) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 内存不足加载失败
        ret = LoadSoFile(nsName3);
        AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
        char cmd[200] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable003_1|grep "
                        "maxcountresourcetable003_1|wc -l";
        ret = executeCommand(cmd, "0");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 加载成功so进行升级
        count[0][1] = 10;
        ret = TestLoadUpgradeDatalog(patchName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = 1;
        int32_t cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "SUCCESS");
            if (ret) {
                sleep(1);
                system(viewCommand);
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        int32_t getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC2, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);

        // 升级写ns2表数据
        ret = BatchInsert(g_conn, g_stmt, tableC2, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns2升级写数据失败");
            break;
        }

        // 降级写数据
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
        ret = 1;
        count[0][1] = 11;
        cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "REDO_FAIL_ROLL_BACK_SUC");
            if (ret) {
                sleep(1);
                system(viewCommand);
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC2, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = BatchInsert(g_conn, g_stmt, tableC2, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns2降级写数据失败");
            break;
        }

        // 卸载已加载so,加载之前 失败so成功
        ret = TestUninstallDatalog(nsName2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = LoadSoFile(nsName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        char cmd1[100] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable003_1";
        ret = executeCommand(cmd1, "maxcountresourcetable002_1");
        AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);
        count[0][1] = 1;
        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        // 升级写ns3表数据
        count[0][1] = 10;
        ret = TestLoadUpgradeDatalog(patchName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = 1;
        cycleTimes = 5;
        while (ret && cycleTimes > 0) {
            ret = executeCommand(viewCommand, "REDO_FAIL_ROLL_BACK_SUC");
            if (ret) {
                sleep(1);
                system(viewCommand);
            }
            cycleTimes--;
        }
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC3, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);

        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns3升级失败写数据失败");
            break;
        }

        // 降级写数据
        count[0][1] = 11;
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, TestLoadRollbackSo(soName3));
        getUpgradeVersion = -1;
        ret = GetTableUpgradeVersionByApi(tableC3, &getUpgradeVersion);
        AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
        ret = BatchInsert(g_conn, g_stmt, tableC3, count, 1);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "ns3降级写数据失败");
            break;
        }
        char cmd2[100] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=maxcountresourcetable002_1";
        ret = executeCommand(cmd2, "maxcountresourcetable002_1");
        AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

        // 卸载so
        AW_FUN_Log(LOG_STEP, "2.uninstall so");
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = TestUninstallDatalog(nsName3);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        cycle--;
        if (cycle % 10 == 0) {
            system("gmsysview -q V\\$COM_MEM_SUMMARY > mem1.txt");
        }
    }
    system("gmsysview -q V\\$COM_MEM_SUMMARY > mem2.txt");
    int32_t filesValue1 = 0;
    int32_t filesValue2 = 1;
    filesValue1 = GetFileValue((char *)"mem1.txt");
    filesValue2 = GetFileValue((char *)"mem2.txt");
    AW_MACRO_EXPECT_EQ_INT(filesValue1, filesValue2);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 045.订阅关系满
1.加载so，写入数据
2.创建多个订阅连接订阅多张表，升级订阅表相关topo订阅接收
3.写入数据，订阅接收，降级so，订阅接收，写入数据
4.读数据，卸载so
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_045)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    int32_t ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char nsName[128] = "DataLog_reliability_045_1";
    char nsName1[128] = "DataLog_reliability_045_2";
    char patchName2[128] = "DataLog_reliability_045_1_patchV2";
    char patchName3[128] = "DataLog_reliability_045_2_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_045_1_rollbackV2.so";
    char soName3[FILE_PATH] = "./datalog_file/DataLog_reliability_045_2_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 加载多个so
    TestUninstallDatalog(nsName, NULL, false);
    TestUninstallDatalog(nsName1, NULL, false);

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    GmcConnT *subConn[1024];
    char subInfo[1024] = {0};
    char subName[30] = {0};
    char subInfoPart1[30] = {0};
    char subConnNameC[20] = "testSubC";
    char tableA1[20] = "rel0221.A001";
    char tableA2[20] = "rel0222.A001";
    int32_t count[][7] = {{1, 2, 1, 2, 2, 1, 2}};

    SnUserDataWithFuncT *userData[1024] = {0};
    uint32_t CurentconnNum = 0;
    GmcSubConfigT tmp_subInfo;
    ret = testGetConnNum(&CurentconnNum);
    EXPECT_EQ(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "可建订阅连接%d", 1022 - CurentconnNum);
    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        sprintf(subConnNameC, "subConnNameC%d", i);
        ret = testSubConnect(&subConn[i], NULL, 1, g_epoll_reg_info, subConnNameC);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "subconnect %dth failed ret =%d", i, ret);
            break;
        }
        const char *subInfoPart2 = R"(
    "comment":"VertexLabel subscription",
    "events":
        [
            {"type":"insert", "msgTypes":["new object"]}
        ],
    "is_reliable": true}
        )";
        userData[i] = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
        userData[i]->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
        memset(userData[i]->data, 0, sizeof(SnUserDataT));
        userData[i]->funcType = 0;
        userData[i]->tableType = 0;
        userData[i]->readIdFunc = SixtyFourthProperty_getId;
        userData[i]->startid = 0;
        userData[i]->endid = 1;
        userData[i]->count = 1;

        if (i >= 999) {
            sprintf(subInfoPart1, "\"rel0222.A00%d\",", i - 999 + 2);
        } else {
            sprintf(subInfoPart1, "\"rel0221.A00%d\",", i + 2);
        }
        sprintf(subInfo, "{\"label_name\":%s%s", subInfoPart1, subInfoPart2);
        sprintf(subName, "subVertexLabel%d", i);
        tmp_subInfo.subsName = subName;
        tmp_subInfo.configJson = subInfo;
        ret = GmcSubscribe(g_stmt, &tmp_subInfo, subConn[i], snCallback, userData[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    char cmd1[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_045_1";
    char cmd2[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_045_2";
    // 升级so写数据
    ret = TestLoadUpgradeDatalog(patchName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    int32_t cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd1, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(cmd1);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestLoadUpgradeDatalog(patchName3);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = 1;
    cycleTimes = 5;

    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd2, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        sleep(2);
        system(cmd2);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "升级so写数据");
    count[0][1] = 11;
    // 降级so写数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd1, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {

        system(cmd1);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA1, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName3));
    AW_FUN_Log(LOG_STEP, "降级so写数据");
    ret = 1;
    cycleTimes = 5;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(cmd2, "SUCCESS");
        if (ret) {
            sleep(2);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    if (ret) {
        system(viewCommand);
    }
    ret = BatchInsertByteOne(g_conn, g_stmt, tableA2, count, 1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        ret = testWaitSnRecv(userData[i]->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 2);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            AW_FUN_Log(LOG_DEBUG, "接收订阅消息%dth失败", i);
            break;
        }
    }

    for (int i = 0; i < 1022 - CurentconnNum; i++) {
        sprintf(subName, "subVertexLabel%d", i);
        ret = GmcUnSubscribe(g_stmt, subName);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        ret = testSubDisConnect(subConn[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        testSnFreeUserData(userData[i]->data);
        free(userData[i]);
    }
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 046.告警验证
1.subsChannelGlobalShareMemSizeMax修改为20M
2.查询告警，加载so，写入数据直到写入失败16006，300s后查询告警
3.300s后查询告警，升级so，删除数据，再次写入数据直到16006，300s后查询告警
4.300s后查询告警，降级so，删除数据，再次写入数据直到16006，300s后查询告警
5.卸载so
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_046)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char nsName[128] = "DataLog_reliability_046";
    char nsName2[128] = "DataLog_reliability_046_patchV2";
    char soName2[FILE_PATH] = "./datalog_file/DataLog_reliability_046_rollbackV2.so";
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=496\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
           "\"subsChannelGlobalDynamicMemSizeMax=1\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    // 预期触发告警  GMC_ALARM_SUB_CONN_RING  GMC_ALARM_SUB_MSG_POOL
    GmcAlarmIdE AlarmId = GMC_ALARM_SUB_CONN_RING;
    GmcAlarmDataT test_AlarmData;
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 加载多个so
    TestUninstallDatalog(nsName, NULL, false);
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 16");
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);

    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
#ifdef ENV_RTOSV2X
    sleep(300);
    AlarmId = GMC_ALARM_SUB_MSG_POOL;
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = true;

    // 进行订阅推送
    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalog_file/outTable_pubsub.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    free(sub_infoF);

    GmcConnT *subConnUpdate;
    GmcStmtT *subStmtUpdate;
    SnUserDataWithFuncT *userDataUpdate;
    const char *subConnName = "testSubUpdate";
    ret = testSubConnect(&subConnUpdate, &subStmtUpdate, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoUpdate = NULL;
    readJanssonFile((char *)"./datalog_file/update_pubsub.gmjson", &sub_infoUpdate);
    EXPECT_NE((void *)NULL, sub_infoUpdate);
    const char *subNameUpdate = "testSub_Update";
    GmcSubConfigT tmp_sub_infoUpdate;
    tmp_sub_infoUpdate.subsName = subNameUpdate;
    tmp_sub_infoUpdate.configJson = sub_infoUpdate;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnUpdate, &userDataUpdate, &tmp_sub_infoUpdate);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoUpdate);

    GmcConnT *subConnOutput;
    GmcStmtT *subStmtOutput;
    SnUserDataWithFuncT *userDataOutput;
    const char *subConnNameOutput = "testSubOutput";
    ret = testSubConnect(&subConnOutput, &subStmtOutput, 1, g_epoll_reg_info, subConnNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char *sub_infoOutput = NULL;
    readJanssonFile((char *)"./datalog_file/notify_pubsub.gmjson", &sub_infoOutput);
    EXPECT_NE((void *)NULL, sub_infoOutput);
    const char *subNameOutput = "testSub_Output";
    GmcSubConfigT tmp_sub_infoOutput;
    tmp_sub_infoOutput.subsName = subNameOutput;
    tmp_sub_infoOutput.configJson = sub_infoOutput;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConnOutput, &userDataOutput, &tmp_sub_infoOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testGmcGetLastError();
    free(sub_infoOutput);
    /******************写输入表触发订阅推送*********************/
    int32_t batchData = 129;
    int32_t count1[batchData][7] = {{1, 2, 1, 2, 2, 1, 2}};
    char tableA[] = "D000";
    int k = 0;
    g_isNeedASleep = true;
    for (int i = 0; i < batchData; i++) {
        for (int j = 0; j < 7; j++) {
            count1[i][j] = 1 + i + k * batchData;
        }
    }
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOL
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(305);
    // so 升级
    // upgrade
    // 并发查询输入表数据
    sleep(10);
    system("gmsysview count");
    (void)TestLoadUpgradeDatalog(nsName2);
    sleep(1);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    k = 0;
    g_isNeedASleep = true;
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOLp
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 256, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "upgrade GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 外部表
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(305);
    // 降级so
    // 并发查询输入表数据
    (void)TestLoadRollbackSo(soName2);
    sleep(1);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    k = 0;
    g_isNeedASleep = true;
    while (true) {
        ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, batchData);
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            break;
        }
        k++;
    }
    // GMC_ALARM_SUB_MSG_POOL
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 256, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "RollBack GMERR_SUB_PUSH_QUEUE_FULL number is %d", k * batchData);

#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_ALARM_STATUS, test_AlarmData.alarmStatus);
    // hpe环境告警共用,当前值会受影响
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    g_isNeedASleep = false;
    // notify
    ret = testWaitSnRecv(userDataOutput->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // update
    ret = testWaitSnRecv(userDataUpdate->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 6);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    /******************释放订阅资源*********************/
    ret = GmcUnSubscribe(g_stmt, subNameOutput);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, subNameUpdate);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    g_isNeedASleep = false;
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testSubDisConnect(subConnOutput, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testSubDisConnect(subConnUpdate, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    testSnFreeUserData(userDataF->data);
    testSnFreeUserData(userDataUpdate->data);
    testSnFreeUserData(userDataOutput->data);
    free(userDataF);
    free(userDataUpdate);
    free(userDataOutput);
#ifdef ENV_RTOSV2X
    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);

    sleep(300);
    ret = GmcGetAlarmData(g_stmt, AlarmId, &test_AlarmData);
    EXPECT_EQ(GMC_NORMAL_STATUS, test_AlarmData.alarmStatus);
    EXPECT_EQ(GMC_ALARM_EMPTY, test_AlarmData.status);
#endif
    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 047.重做含多条二级索引相同数据
1.旧so中输入表二级索引写入为join规则，单表写入1000条数据，规则升级为笛卡尔积
2.升级后进行二级索引删除(字段总数过多，笛卡尔数据量到不了100万)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_047)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    // 表空间内存不足，暂定数据量为400
    int32_t dataNum = 400;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_047";
    char nsName2[128] = "DataLog_reliability_004_047_patchV2";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(4);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");

    // 二级索引删除数据内存不足删除失败
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(g_stmt, (char *)"4");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    int64_t value1 = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &value1, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = executeCommand((char *)"gmsysview count inpA", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 使用别的二级索引分批删除删除成功
    int32_t value6 = 1;
    for (int32_t k = 0; k < dataNum; k++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, tableA, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcSetIndexKeyName(g_stmt, (char *)"2");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        value6 = k + 1;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = executeCommand((char *)"gmsysview count inpA", "0");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "400");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "0");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 048.100个线程并发访问同一张表，预期服务正常
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_048)
{
    // 加载so
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_033";
    char nsName2[128] = "DataLog_reliability_004_033_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_033_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");

    // 预置数据
    // 并发查询输入表数据
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 读
    ScanHundredTimes();

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // upgrade
    // 并发查询输入表数据
    (void)TestLoadUpgradeDatalog(nsName2);
    ScanHundredTimes();
    sleep(1);

    // 降级so
    // 并发查询输入表数据
    (void)TestLoadRollbackSo(soName2);
    ScanHundredTimes();

    // 查询so是否还存在再进行卸载
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_033", nsName1);
    if (ret == 0) {
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // view check so is unloaded
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_033", nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 删除so
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 049.升级so文件丢失
1.加载so，并升级
2.再次升级时需要加载的so不存在, 预期报错, 降级时，需要加载的so不存在预期报错;
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_049)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 100");

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");
    system("gmsysview -q V\\$COM_DYN_CTX >txt.txt");
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    // 表空间内存不足，暂定数据量为400
    int32_t dataNum = 100;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_047";
    char nsName2[128] = "DataLog_reliability_004_047_patchV2";
    char nsName3[128] = "DataLog_reliability_004_047_patchV4";
    char nsName4[128] = "DataLog_reliability_004_047_rollbackV4";
    int cycle = 1;

    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 升级
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    int32_t cycleTimes = 100;
    ret = 1;
    while (ret && cycleTimes > 0) {
        ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
        if (ret) {
            sleep(4);
            system(viewCommand);
        }
        cycleTimes--;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_INFO, "重做数据成功");

    // 再次升级指定不存在文件
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "ret = 1013000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);

    // 降级文件不存在
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./datalog_file/%s.so", g_toolPath, nsName4);
    ret = executeCommand(command, "ret = 1013000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);

    ret = executeCommand((char *)"gmsysview count inpA", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "10000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 050.文件内容异常
1.加载so，并升级
2.再次升级so， gmprecompiler和gmserver版本不匹配, 加载失败，降级so加载失败
3.使用gmprecompiler和gmserver版本一致so，预期加载升级成功，降级so成功(当前版本不一致仅打印日志)*
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_050)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 100;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    char nsName1[128] = "DataLog_reliability_004_047";
    char nsName2[128] = "DataLog_reliability_004_047_patchV2";
    char nsName3[128] = "DataLog_reliability_004_047_rollbackV2";
    // 修改升级.c中版本号
    char cmdExe[128] = "sed -i 's/506/502/g' ./datalog_file/DataLog_reliability_004_047_patch.c";
    // 修改降级.c中版本号
    char cmdExe1[128] = "sed -i 's/506/502/g' ./datalog_file/DataLog_reliability_004_047_patch_rollback.c";

    // 重新编译正确so
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 加载写入数据成功
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 得到版本不一致so
    system(cmdExe);
    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared "
        "./datalog_file/DataLog_reliability_004_047_patch.c -o "
        "./datalog_file/%s.so \n",
        nsName2);
    system(command);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CheckLog("Gmprecompiler dismatched");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    (void)snprintf(command, MAX_CMD_SIZE,
        "gcc -Wl,-Bsymbolic -fPIC -I ../../../../../pub/include/ --shared "
        "./datalog_file/DataLog_reliability_004_047_patch_rollback.c -o "
        "./datalog_file/%s.so \n",
        nsName3);
    system(command);
    // 降级版本不一致so
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./datalog_file/%s.so", g_toolPath, nsName3);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = CheckLog("Gmprecompiler dismatched");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    ret = executeCommand((char *)"gmsysview count inpA", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    AW_FUN_Log(LOG_STEP, "test end");

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 051.文件路径过长
加载卸载路径超长，upgrade rollback 参数最长路径，超出256报错合理
1.加载so
2.加载升级so绝对路径超过256字节，加载失败
3.绝对路径等于256字节加载成功
4.降级so，绝对路径超过256字节降级失败
5.绝对路径等于256字节降级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_051)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char nsName1[128] = "DataLog_reliability_004_047";
    char nsName2[128] = "DataLog_reliability_004_047_patchV2";
    char nsName3[128] = "DataLog_reliability_004_047_rollbackV2";
    char newlibName[260] = {0};
    char libName[] = "datalog_file/DataLog_reliability_004_047.so";
    char libName1[] = "datalog_file/DataLog_reliability_004_047_patchV2.so";
    char libName2[] = "datalog_file/DataLog_reliability_004_047_rollbackV2.so";
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    system("sh ${TEST_HOME}/tools/start.sh");
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableA[] = "inpA";
    char tableB[] = "G000";
    char tableA2[] = "inpA2";
    int32_t dataNum = 100;
    int32_t upgradeVersion = 0;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
        count1[i][3] = 1;
    }

    int offset = GetPathLengthNums();
    int32_t soNameLength = 115;
    char libDir[128] = "loadlib";
    memset_s(newlibName, soNameLength, 'b', soNameLength);
    memset_s(nsName1, soNameLength, 'b', soNameLength);
    char filePath[256] = "cds";
    // 当前路径长度+soName长度+.so后缀+结束符
    int32_t needPathLength = 256 - offset - soNameLength - 4;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    // 重新编译正确so
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char command[MAX_CMD_SIZE] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(command);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "load so");
    (void)snprintf(newlibName + soNameLength, 260, "%s", ".so");
    (void)SystemSnprintf("cp %s %s/%s", libName, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("rm -rf %s", filePath);

    AW_FUN_Log(LOG_STEP, "写入数据");
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableA2, count1 + dataNum / 2, dataNum / 2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("mkdir -p %s", filePath);

    // 路径长度比实际规格大一.patch
    memset_s(filePath, 256, '\0', 256);
    needPathLength -= 5;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);

    (void)snprintf(newlibName + soNameLength, 260, "%s", "_patch.so");
    (void)SystemSnprintf("cp %s %s/%s", libName1, filePath, newlibName);

    AW_FUN_Log(LOG_STEP, "1.升级导入绝对路径长度大于256 so加载失败");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "the absolute path len is 256. ret = 1004003");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    memset_s(filePath, 256, '\0', 256);
    needPathLength -= 1;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    (void)SystemSnprintf("cp %s %s/%s", libName1, filePath, newlibName);

    // 升级
    AW_FUN_Log(LOG_STEP, "2.升级导入绝对路径长度等于256 so加载成功");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./%s%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 等待重做完成
    sleep(5);
    SystemSnprintf("rm -rf %s", filePath);
    memset_s(filePath, 256, '\0', 256);

    // 绝对路径大于256
    needPathLength -= 4;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);

    // 降级绝对路径过长加载失败
    AW_FUN_Log(LOG_STEP, "3.降级导入绝对路径长度大于256 so加载失败");
    memset_s(newlibName, 260, '\0', 260);
    memset_s(newlibName, soNameLength, 'b', soNameLength);
    (void)snprintf(newlibName + soNameLength, 260, "%s", "_rollbackV2.so");
    (void)SystemSnprintf("cp %s %s/%s", libName2, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./%s/%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "the absolute path len is 256. ret = 1004003");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    SystemSnprintf("rm -rf %s", filePath);

    // 绝对路径等于256
    AW_FUN_Log(LOG_STEP, "4.降级导入绝对路径长度等于256 so加载成功");
    needPathLength -= 1;
    for (int32_t i = 0; i < needPathLength; i++) {
        filePath[i] = 'b';
        if ((i > 0) && (i % 2 != 0)) {
            filePath[i] = '/';
        }
        if (needPathLength % 2 != 0) {
            filePath[needPathLength - 1] = '/';
            filePath[needPathLength - 2] = 'b';
        }
    }
    SystemSnprintf("mkdir -p %s", filePath);
    (void)SystemSnprintf("cp %s %s/%s", libName2, filePath, newlibName);
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -rollback ./%s/%s", g_toolPath, filePath, newlibName);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    SystemSnprintf("rm -rf %s", filePath);

    ret = executeCommand((char *)"gmsysview count inpA", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count inpA2", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = executeCommand((char *)"gmsysview count G000", "100");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 052.锁获取失败，重做失败
1.线程1开启事务写外部表不提交
2.线程二升级so，重做失败，卸载so
3.再次加载so，线程1事务提交，进行升级成功，
4.线程1再次开启事务写数据，不提交，进行降级，降级失败
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_052)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_033";
    char nsName2[128] = "DataLog_reliability_004_033_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_033_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 22;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    AW_FUN_Log(LOG_STEP, "1.线程事务内写外部表时升级失败");
    // 线程中写外部表 100s后提交
    pthread_t sub_thr;
    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    sleep(5);
    // 线程中升级so,抢锁失败,
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 查询热补丁视图以及表upgradeVersion字段值是否正常
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D021", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D020", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D019", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    system(viewCommand);
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v0.0.0]", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 再次升级成功
    AW_FUN_Log(LOG_STEP, "2.再次升级成功");
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);
    // 查询热补丁视图
    system(viewCommand);
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    AW_FUN_Log(LOG_STEP, "3.事务内写外部表不提交时降级失败");
    ret = pthread_create(&sub_thr, NULL, WriteOutTableButNoCommit, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadRollbackSo(soName2));
    ret = pthread_join(sub_thr, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 053.在线修改系统配置为异常值
1.线程1进行升级重做并不断修改重做条数和DML并发配置
2.线程二不断写数据直到重做结束
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_053)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_033";
    char nsName2[128] = "DataLog_reliability_004_033_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_033_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD020, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByte256(g_conn, g_stmt, tableD019, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");
    pthread_t sub_thr1;
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    ret = pthread_create(&sub_thr1, NULL, WriteInpTableUntilUpgradeDone, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启线程升级so
    ret = TestLoadUpgradeDatalog(nsName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启线程不断修改重做条数和DML并发配置
    pthread_t sub_thr2;
    ret = pthread_create(&sub_thr2, NULL, ChangeCfgVal, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_join(sub_thr2, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = pthread_join(sub_thr1, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";

    // 查询热补丁视图
    ret = executeCommand(viewCommand, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);
    int32_t getUpgradeVersion = -1;
    ret = GetTableUpgradeVersionByApi((char *)"D000", &getUpgradeVersion);
    AW_MACRO_EXPECT_EQ_INT(0, getUpgradeVersion);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

// block 1升级成功
/* ****************************************************************************
Description  : 054.升级时修改udf实现，申请最大支持动态内存2M，预期升级失败，内存被释放
1.重做条数为1000加载so后进行udf实现修改升级，udf中增加申请内存操作
2.预期升级重做时，触发udf申请内存，内存不足，重做失败，内存被释放(重做失败仅影响数据一致性)
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_054)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_054";
    char nsName2[128] = "DataLog_reliability_004_054_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_054_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableOut[] = "A0990";
    char tableMid[] = "A0989";
    char tableInp[] = "A0988";
    int32_t dataNum = 1001;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableInp, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 修改重做条数为1000条
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1000");

    // 升级so失败
    char command[256] = {0};
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(100);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
Description  : 055.异常导致加载失败后, 重新加载正常的可成功
1.不涉及重做，udf升级时udf未声明实现，升级失败
2.再次加载声明实现udf升级so，升级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_055)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=48\" \"workerHungThreshold=20,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=16\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"pageSize=4\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_054";
    char nsName2[128] = "DataLog_reliability_004_054_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_054_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableOut[] = "A0990";
    char tableMid[] = "A0989";
    char tableInp[] = "A0988";
    int32_t dataNum = 1001;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = BatchInsertByteOne(g_conn, g_stmt, tableInp, count1, dataNum);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system("gmsysview count");
    // 修改重做条数为1000条
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    char command[256] = {0};
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_054_patchV2.so "
                   "./datalog_file/DataLog_reliability_004_054_patchV2_1.so");
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_055_patchV2.so "
                   "./datalog_file/DataLog_reliability_004_054_patchV2.so");
    SystemSnprintf("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    snprintf(command, MAX_CMD_SIZE, "%s/gmimport -c datalog -upgrade ./datalog_file/%s.so", g_toolPath, nsName2);
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    // 升级so
    sleep(2);
    SystemSnprintf("rm -rf ./datalog_file/DataLog_reliability_004_054_patchV2.so");
    sleep(1);
    SystemSnprintf("mv ./datalog_file/DataLog_reliability_004_054_patchV2_1.so "
                   "./datalog_file/DataLog_reliability_004_054_patchV2.so");
    ret = executeCommand(command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(30);

    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    // 查询热补丁视图
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    system(viewCommand);

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end");
}
// 线程升级不同so
void *LoadDiffNameUpgradeSo1(void *args)
{
    int32_t index = *(int32_t *)args;
    char nsName2[128] = "DataLog_reliability_004_056_patchV2";
    (void)sprintf(nsName2, "DataLog_reliability_004_056_%d_patchV2", index);
    (void)TestLoadUpgradeDatalog(nsName2);
}
/* ****************************************************************************
Description  : 056.并发升级同1个so和并发升级多个so
1.并发对同一个so进行升级，预期只会有一个so升级成功，其余so报错
2。并发10个线程对不同so升级，预期互不影响，升级成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_056)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    // 并发升级相同so
    char nsName1[128] = "DataLog_reliability_004_056";
    char nsName2[128] = "DataLog_reliability_004_056_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_056_rollbackV2.so";

    //  并发升级不同so
    char *soName[10] = {(char *)"DataLog_reliability_004_056_1", (char *)"DataLog_reliability_004_056_2",
        (char *)"DataLog_reliability_004_056_3", (char *)"DataLog_reliability_004_056_4",
        (char *)"DataLog_reliability_004_056_5", (char *)"DataLog_reliability_004_056_6",
        (char *)"DataLog_reliability_004_056_7", (char *)"DataLog_reliability_004_056_8",
        (char *)"DataLog_reliability_004_056_9", (char *)"DataLog_reliability_004_056_10"};

    char *upgradeSoName[10] = {(char *)"DataLog_reliability_004_056_1_patchV2",
        (char *)"DataLog_reliability_004_056_2_patchV2", (char *)"DataLog_reliability_004_056_3_patchV2",
        (char *)"DataLog_reliability_004_056_4_patchV2", (char *)"DataLog_reliability_004_056_5_patchV2",
        (char *)"DataLog_reliability_004_056_6_patchV2", (char *)"DataLog_reliability_004_056_7_patchV2",
        (char *)"DataLog_reliability_004_056_8_patchV2", (char *)"DataLog_reliability_004_056_9_patchV2",
        (char *)"DataLog_reliability_004_056_10_patchV2"};
    char viewCommand[50] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    for (int32_t i = 0; i < 10; i++) {
        TestUninstallDatalog(soName[i], NULL, false);
    }
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = LoadSoFile(soName[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "1.加载多个so");
    // 多个线程并发加载相同升级so
    int32_t threadNum = 10;
    pthread_t sub_thr1[threadNum];
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&sub_thr1[i], NULL, LoadSameNameUpgradeSo, (char *)nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(sub_thr1[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so,间隔一段时间查询视图");

    // 查询热补丁视图 ，升级补丁状态会受并发影响
    ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(5);
    system("gmsysview -q V\\$PTL_DATALOG_PATCH_INFO");

    // 通过降级校验并发升级时能够成功
    ret = TestLoadRollbackSo(soName2);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 开启多个线程升级不同so
    pthread_t sub_thr2[threadNum];
    int32_t dataNum[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        dataNum[i] = i + 1;
        ret = pthread_create(&sub_thr2[i], NULL, LoadDiffNameUpgradeSo1, &dataNum[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = pthread_join(sub_thr2[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so");
    char viewCommand2[128] = "empty";
    for (int32_t i = 0; i < threadNum; i++) {
        (void)sprintf(
            viewCommand2, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f DataLog_reliability_004_056_%d_patchV2", i + 1);
        ret = executeCommand(viewCommand, "VERSION: [v0.0.0]->[v1.0.0]", "PATCH_STATE: SUCCESS");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = TestUninstallDatalog(soName[i], NULL, false);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 057.并发降级同一个so和并发降级多个so
1.并发降级同一个so预期其中一个so成功，其余so失败
2.并发降级不同so，预期so降级都能成功
**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_057)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    // 并发升级相同so
    char nsName1[128] = "DataLog_reliability_004_056";
    char nsName2[128] = "DataLog_reliability_004_056_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_056_rollbackV2.so";

    //  并发升级不同so
    char *soName[10] = {(char *)"DataLog_reliability_004_056_1", (char *)"DataLog_reliability_004_056_2",
        (char *)"DataLog_reliability_004_056_3", (char *)"DataLog_reliability_004_056_4",
        (char *)"DataLog_reliability_004_056_5", (char *)"DataLog_reliability_004_056_6",
        (char *)"DataLog_reliability_004_056_7", (char *)"DataLog_reliability_004_056_8",
        (char *)"DataLog_reliability_004_056_9", (char *)"DataLog_reliability_004_056_10"};

    char *upgradeSoName[10] = {(char *)"DataLog_reliability_004_056_1_patchV2",
        (char *)"DataLog_reliability_004_056_2_patchV2", (char *)"DataLog_reliability_004_056_3_patchV2",
        (char *)"DataLog_reliability_004_056_4_patchV2", (char *)"DataLog_reliability_004_056_5_patchV2",
        (char *)"DataLog_reliability_004_056_6_patchV2", (char *)"DataLog_reliability_004_056_7_patchV2",
        (char *)"DataLog_reliability_004_056_8_patchV2", (char *)"DataLog_reliability_004_056_9_patchV2",
        (char *)"DataLog_reliability_004_056_10_patchV2"};
    char viewCommand[256] = "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_004_056";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    for (int32_t i = 0; i < 10; i++) {
        TestUninstallDatalog(soName[i], NULL, false);
    }
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = LoadSoFile(soName[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "1.加载多个so");

    // 加载升级so
    for (int32_t i = 0; i < 10; i++) {
        (void)TestLoadUpgradeDatalog(upgradeSoName[i]);
    }
    (void)TestLoadUpgradeDatalog(nsName2);

    // 多个线程并发加载相同降级so
    int32_t threadNum = 10;
    pthread_t sub_thr1[threadNum];
    // 开启线程不断写数据直到线程重做完成 验证数据重做完成时数据状态
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_create(&sub_thr1[i], NULL, LoadSameNameRollBackSo, (char *)nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(sub_thr1[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so,间隔一段时间查询视图");

    // 查询热补丁视图
    sleep(5);
    ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    sleep(2);
    system(viewCommand);

    // 开启多个线程降级不同so
    pthread_t sub_thr2[threadNum];
    int32_t dataNum[threadNum] = {0};
    for (int32_t i = 0; i < threadNum; i++) {
        dataNum[i] = i + 1;
        ret = pthread_create(&sub_thr2[i], NULL, LoadDiffNameRollBackSo1, &dataNum[i]);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        ret = pthread_join(sub_thr2[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    AW_FUN_Log(LOG_STEP, "2.多个线程并发加载同名升级so");
    char viewCommand2[128] = "empty";
    sleep(3);
    for (int32_t i = 0; i < threadNum; i++) {
        (void)sprintf(
            viewCommand2, "gmsysview -q V\\$PTL_DATALOG_PATCH_INFO -f SO_NAME=DataLog_reliability_004_056_%d", i + 1);
        ret = executeCommand(viewCommand, "VERSION: [v1.0.0]->[v0.0.0]", "PATCH_STATE: SUCCESS");
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret) {
            system(viewCommand2);
            AW_FUN_Log(LOG_DEBUG, "cmd is %s", viewCommand2);
            system(viewCommand2);
        }
    }

    // 卸载so
    ret = TestUninstallDatalog(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (int32_t i = 0; i < 10; i++) {
        ret = TestUninstallDatalog(soName[i], NULL, false);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
Description  : 058.DDL, DML, DQL并发, 非交互式事务(非显示事务）
1.线程1写升级后数据，预期so升级成功后写入成功，否则失败，so降级，降级成功验证upgradeversion字段值
2.启动线程2反复读表数据;
3.启动线程3反复加载卸载ns1.A(遇到12002则间隔5s再重试);
4.线程4写降级后数据，预期so降级成功后写入成功，否则失败，so升级升级成功，校验upgradeVersion字段值
以上操作循环100次

**************************************************************************** */
TEST_F(reliability01, DataLog_reliability_004_058)
{
    // 加载so并预置数据
    AW_FUN_Log(LOG_STEP, "test begin");
    system("sh ${TEST_HOME}/tools/start.sh");
    char nsName1[128] = "DataLog_reliability_004_033";
    char nsName2[128] = "DataLog_reliability_004_033_patchV2";
    char soName2[128] = "./datalog_file/DataLog_reliability_004_033_rollbackV2.so";
    int32_t ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    char tableD[] = "D000";
    char tableD020[] = "D020";
    char tableD019[] = "D019";
    int32_t dataNum = 10;
    int32_t count1[dataNum][7] = {{1, 2, 1, 2, 2, 1, 2}};
    for (int32_t i = 0; i < dataNum; i++) {
        count1[i][0] = i + 1;
        count1[i][1] = i + 1;
        count1[i][2] = i + 1;
    }
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./datalog_file/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(ret, GMERR_OK);
    free(schema);
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName1, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    // 修改为支持DML与升级重做并发
    SystemSnprintf("gmadmin -cfgName enableDatalogDmlWhenUpgrading -cfgVal 1");

    // upgrade
    (void)TestLoadUpgradeDatalog(nsName2);
    sleep(1);

    // 开启多个线程
    int32_t threadNum = 4;
    pthread_t threadArray[threadNum];
    ret = pthread_create(&threadArray[0], NULL, WriteAndRollBack, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[1], NULL, WriteAndUpgrade, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[2], NULL, ReadData, NULL);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = pthread_create(&threadArray[3], NULL, UnloadAndLoad, (char *)nsName1);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    for (int32_t i = 0; i < threadNum; i++) {
        ret = pthread_join(threadArray[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // 查询so是否还存在再进行卸载
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_033", nsName1);
    if (ret == 0) {
        ret = TestUninstallDatalog(nsName1);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    }

    // view check so is unloaded
    ret =
        executeCommand((char *)"gmsysview -q V\\$PTL_DATALOG_SO_INFO -f SO_NAME=DataLog_reliability_004_033", nsName1);
    AW_MACRO_EXPECT_NE_INT(ret, GMERR_OK);

    ret = GmcDropVertexLabel(g_stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
