/*****************************************************************************
 Description  : 增量持久化可靠性测试
 Notes        : 
            reliability_incre_pst_002 删除0区持久化文件
            reliability_incre_pst_003 删除1区持久化文件
            reliability_incre_pst_004 删除0和1区持久化文件
            reliability_incre_pst_009 0区目录不可写
            reliability_incre_pst_010 1区目录不可写
            reliability_incre_pst_011 0、1区目录都不可写
            reliability_incre_pst_012 0区目录下文件不可读写
            reliability_incre_pst_013 1区目录下文件不可读写
            reliability_incre_pst_014 0、1区目录下的文件都不可读写

 History      :
 Author       : 潘鹏 pwx860460
 Modification :
 Date         : 2023-12-01
*****************************************************************************/

#include "incre_pst_common.h"

int g_beginIndex = 0;
int g_endIndex = 200;
char g_dbFilePath1[1024] = {0};
char g_dbFilePath2[1024] = {0};

class RelIncrePst : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase() {}
    static void TearDownTestCase() {}
};

void RelIncrePst::SetUp()
{
    printf("[INFO] Incremental Persistence cfg test Start.\n");
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    (void)sprintf(g_dbFilePath, "%s/gmdb", pwdDir);
    (void)sprintf(g_newDbFilePath, "%s/new_gmdb", pwdDir);

    ret = ChangeGmserverCfg((char *)"persistentMode", (char *)"1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"multizonePersistNum", (char *)"2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 确保不锁库
    ret = ChangeGmserverCfg((char *)"redoFlushByTime", (char *)"6000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"redoFlushByTrx", (char *)"0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"recoveryZoneId", (char *)"2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /usr/local/file/gmserver.ini|grep  recoveryZoneId");
    system("cat /usr/local/file/gmserver.ini|grep  dataFileDirPath");
    (void)sprintf(g_dbFilePath1, "%s1", g_dbFilePath);
    (void)sprintf(g_dbFilePath2, "%s2", g_dbFilePath);
    (void)Rmdir(g_dbFilePath1);
    (void)Rmdir(g_dbFilePath2);
    ret = mkdir(g_dbFilePath1, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = mkdir(g_dbFilePath2, S_IRUSR | S_IWUSR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char temp[2048] = {0};
    (void)sprintf(temp, "%s,%s", g_dbFilePath1, g_dbFilePath2);
    ret = ChangeGmserverCfg((char *)"dataFileDirPath", temp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void RelIncrePst::TearDown()
{
    int ret = ChangeGmserverCfg((char *)"recover", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERRNUM_WHITE_LIST(1, GMERR_FILE_OPERATE_FAILED);
    AW_CHECK_LOG_END();
    system("chattr -R -i gmdb/");
    printf("[INFO] Incremental Persistence cfg test End.\n");
}

// 删除0区持久化文件
TEST_F(RelIncrePst, reliability_incre_pst_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除持久化文件
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "rm %s/gmdb1/* -rf", pwdDir);
    system(g_command1);

    system("echo xxxxxx;ls -l gmdb1");

    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除1区持久化文件
TEST_F(RelIncrePst, reliability_incre_pst_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除持久化文件
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "rm %s/gmdb2/* -rf", pwdDir);
    system(g_command1);

    system("echo xxxxxx;ls -l gmdb1");

    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);

    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 删除0和1区持久化文件
TEST_F(RelIncrePst, reliability_incre_pst_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除持久化文件
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "rm %s/gmdb1/* -rf", pwdDir);
    system(g_command1);
    char g_command2[1024];
    (void)sprintf(g_command2, "rm %s/gmdb2/* -rf", pwdDir);
    system(g_command2);

    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 修改0或1区任一持久化数据文件内容 暂不涉及 reliability_incre_pst_005

// 修改0、1区持久化数据文件内容，且修改方式不一致  暂不涉及 reliability_incre_pst_006


// 0区目录不可写
TEST_F(RelIncrePst, reliability_incre_pst_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb1", pwdDir);
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 建表
    ret = CommonDDL();
    // 增量有锁库的操作，锁库返回19004,调整配置 不锁库
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 增量有锁库的操作，锁库返回19004
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");
    ret = RestartAndConn();
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 1区目录不可写
TEST_F(RelIncrePst, reliability_incre_pst_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"ckptPeriod", (char *)"60");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2", pwdDir);
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite inject %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 增量有锁库的操作，锁库返回19004，调整配置避免锁库
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 增量有锁库的操作，锁库返回19004
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite clean %s", g_command1);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");
    // 锁库重启恢复
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 0、1区目录都不可写
TEST_F(RelIncrePst, reliability_incre_pst_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"ckptPeriod", (char *)"60");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb1", pwdDir);
    char g_command2[1024];
    (void)sprintf(g_command2, "%s/gmdb2", pwdDir);
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite inject %s", g_command1, g_command2);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 增量有锁库的操作，锁库返回19004，调整配置不锁库
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 增量有锁库的操作，锁库返回19004,调整配置不锁库
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unwrite clean %s", g_command1,g_command2);
    AW_FUN_Log(LOG_STEP, "after clean exception\n");
    // 锁库后重启恢复
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 0区目录下文件不可读写
TEST_F(RelIncrePst, reliability_incre_pst_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"ckptPeriod", (char *)"60");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb1/dbCtrlFile", pwdDir);
    char g_command2[1024];
    (void)sprintf(g_command2, "%s/gmdb1/dbUndoSpace", pwdDir);
    char g_command3[1024];
    (void)sprintf(g_command3, "%s/gmdb1/dbUserSpace", pwdDir);
    char g_command4[1024];
    (void)sprintf(g_command4, "%s/gmdb1/dbSystemSpace", pwdDir);
    char g_command9[1024];
    (void)sprintf(g_command9, "%s/gmdb1", pwdDir);// 确保不锁库
    // 注入异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);


    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 1区目录下文件不可读写
TEST_F(RelIncrePst, reliability_incre_pst_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"ckptPeriod", (char *)"60");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb2/dbCtrlFile", pwdDir);
    char g_command2[1024];
    (void)sprintf(g_command2, "%s/gmdb2/dbUndoSpace", pwdDir);
    char g_command3[1024];
    (void)sprintf(g_command3, "%s/gmdb2/dbUserSpace", pwdDir);
    char g_command4[1024];
    (void)sprintf(g_command4, "%s/gmdb2/dbSystemSpace", pwdDir);
    char g_command9[1024];
    (void)sprintf(g_command9, "%s/gmdb1", pwdDir);// 确保不锁库
    // 注入异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

// 0、1区目录下的文件都不可读写
TEST_F(RelIncrePst, reliability_incre_pst_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    int ret = ChangeGmserverCfg((char *)"redoFileSize", (char *)"16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ChangeGmserverCfg((char *)"ckptPeriod", (char *)"60");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/start.sh");
    
    ret = InitAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 注入异常
    char *pwdDir = getenv("PWD");
    if (pwdDir == NULL) {
        AW_FUN_Log(LOG_STEP, "get env PWD fail.\n");
    }
    char g_command1[1024];
    (void)sprintf(g_command1, "%s/gmdb1/dbCtrlFile", pwdDir);
    char g_command2[1024];
    (void)sprintf(g_command2, "%s/gmdb1/dbUndoSpace", pwdDir);
    char g_command3[1024];
    (void)sprintf(g_command3, "%s/gmdb1/dbUserSpace", pwdDir);
    char g_command4[1024];
    (void)sprintf(g_command4, "%s/gmdb1/dbSystemSpace", pwdDir);
    char g_command9[1024];
    (void)sprintf(g_command9, "%s/gmdb1", pwdDir);
    char g_command5[1024];
    (void)sprintf(g_command5, "%s/gmdb2/dbCtrlFile", pwdDir);
    char g_command6[1024];
    (void)sprintf(g_command6, "%s/gmdb2/dbUndoSpace", pwdDir);
    char g_command7[1024];
    (void)sprintf(g_command7, "%s/gmdb2/dbUserSpace", pwdDir);
    char g_command8[1024];
    (void)sprintf(g_command8, "%s/gmdb2/dbSystemSpace", pwdDir);
    char g_command10[1024];
    (void)sprintf(g_command10, "%s/gmdb2", pwdDir);
    // 注入异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw inject %s",\
    g_command5, g_command6, g_command7, g_command8, g_command10);
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");
    
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw query");
    AW_FUN_Log(LOG_STEP, "after inject exception.\n");

    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消异常
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s",\
    g_command1, g_command2, g_command3, g_command4, g_command9);
    (void)GtExecSystemCmd("sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s;\
    sh ${TEST_HOME}/reliability/rel_incre_pst/cfe_inject.sh rfile_unrw clean %s",\
    g_command5, g_command6, g_command7, g_command8, g_command10);
    // 建表
    ret = CommonDDL();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 写入数据
    ret = CommonInsert(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 落盘
    ret = GmcFlushData(g_stmt, NULL, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = RestartAndConn();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckInCount(g_endIndex - g_beginIndex, g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CommonCheckRecord(g_beginIndex, g_endIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DisConnAndClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}

