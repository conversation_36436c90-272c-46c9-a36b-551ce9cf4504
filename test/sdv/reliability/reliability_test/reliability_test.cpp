/*****************************************************************************
 Description  : 可靠性测试
 Notes        :
 History      :
 Author       : 吴雪琦 00495442
 Modification :
 Date         : 2021/08/30
*****************************************************************************/

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "reliability.h"

class reliability_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void reliability_test::SetUp()
{
    int ret;
    //创建连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/arp.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name_arp);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, label_config_json);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    readJanssonFile("schema_file/ip4forward_for_ls.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt_sync, g_label_name_for_ls);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, label_config_json);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    memset(&g_flowControl, 0, sizeof(g_flowControl));

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
}
void reliability_test::TearDown()
{}

//异步回调处理缓慢，不影响写入, 其他业务线程（异步、订阅写阻塞的表）不受影响
TEST_F(reliability_test, reliability_012)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 1, dml_thread_num = 2;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_async_replace_ip4forward_block, (void *)&index[0]);
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_async_replace_ip4forward, (void *)&index[1]);
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_ip4forward_async_block, (void *)&index[2]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//异步回调处理缓慢，不影响写入, 其他业务线程（异步、订阅不阻塞的表）不受影响
TEST_F(reliability_test, reliability_013)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 2000;
#else
    g_data_num = 20000;
#endif
    const uint32_t thread_num = 4;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    g_thread_num = thread_num;

    for (i = 0; i < g_thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, g_thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_async_replace_ip4forward_block, (void *)&index[0]);
    index[1] = 1;
    // pthread_create(&thr_arr[1], NULL, thread_replace_arp, (void *)&index[1]); //同步
    pthread_create(&thr_arr[1], NULL, thread_async_replace_arp, (void *)&index[1]);  //异步
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_arp, (void *)&index[2]);
    index[3] = 3;
    pthread_create(&thr_arr[3], NULL, thread_scan_arp, (void *)&index[3]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < g_thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//异步回调不处理，不影响写入, 其他业务线程（异步、订阅阻塞的表）不受影响
TEST_F(reliability_test, reliability_014)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 1, dml_thread_num = 2;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_async_replace_ip4forward_stop_epoll, (void *)&index[0]);
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_async_replace_ip4forward, (void *)&index[1]);
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_ip4forward_async_block, (void *)&index[2]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//可靠订阅同一个表回调处理缓慢，影响写入（订阅队列满报15002），其他业务线程（异步、订阅）不受影响
TEST_F(reliability_test, reliability_015)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 2, dml_thread_num = 1;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;
    g_cnt_suc = 0;
    g_cnt_fail = 0;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    for (i = 0; i < dml_thread_num; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_async_replace_ip4forward_sub_full, (void *)&index[i]);
    }

    index[dml_thread_num] = dml_thread_num;
    //订阅线程1正常接收
    pthread_create(&thr_arr[dml_thread_num], NULL, thread_sub_ip4forward_async_block, (void *)&index[dml_thread_num]);
    index[dml_thread_num + 1] = dml_thread_num + 1;
    //订阅线程2接收延时
    pthread_create(&thr_arr[dml_thread_num + 1], NULL, thread_sub_ip4forward_block, (void *)&index[dml_thread_num + 1]);

    for (i = 0; i < thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    printf("g_suc_fail : %d, g_cnt_fail : %d, total : %d\n", g_cnt_suc, g_cnt_fail, g_cnt_suc + g_cnt_fail);
    if (g_flowControl.replace_flowControl_num == 0) {
        EXPECT_EQ(g_data_num, g_cnt_suc + g_cnt_fail);
    }

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//可靠订阅同一个表回调不处理，影响写入（订阅队列满报15002），其他业务线程（异步、订阅）不受影响
TEST_F(reliability_test, reliability_016)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 2, dml_thread_num = 1;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;
    g_cnt_suc = 0;
    g_cnt_fail = 0;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    for (i = 0; i < dml_thread_num; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_async_replace_ip4forward_sub_full, (void *)&index[i]);
    }

    index[dml_thread_num] = dml_thread_num;
    //订阅线程1正常接收
    pthread_create(&thr_arr[dml_thread_num], NULL, thread_sub_ip4forward_async_block, (void *)&index[dml_thread_num]);
    index[dml_thread_num + 1] = dml_thread_num + 1;
    //订阅线程2关掉epoll
    pthread_create(
        &thr_arr[dml_thread_num + 1], NULL, thread_sub_ip4forward_block_stop_epoll, (void *)&index[dml_thread_num + 1]);

    for (i = 0; i < thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    printf("g_suc_fail : %d, g_cnt_fail : %d, total : %d\n", g_cnt_suc, g_cnt_fail, g_cnt_suc + g_cnt_fail);
    if (g_flowControl.replace_flowControl_num == 0) {
        EXPECT_EQ(g_data_num, g_cnt_suc + g_cnt_fail);
    }

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//不可靠订阅回调处理缓慢，不影响写入，其他业务线程（异步、订阅）不受影响
TEST_F(reliability_test, reliability_017)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 2, dml_thread_num = 2;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;
    g_cnt_suc = 0;
    g_cnt_fail = 0;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_async_replace_ip4forward_sub_full, (void *)&index[0]);
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_async_replace_arp, (void *)&index[1]);
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_arp_unrelia, (void *)&index[2]);
    index[3] = 3;
    pthread_create(&thr_arr[3], NULL, thread_sub_ip4forward_block_unrelia, (void *)&index[3]);

    for (i = 0; i < thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    printf("g_suc_fail : %d, g_cnt_fail : %d, total : %d\n", g_cnt_suc, g_cnt_fail, g_cnt_suc + g_cnt_fail);
    if (g_flowControl.replace_flowControl_num == 0) {
        EXPECT_EQ(g_data_num, g_cnt_suc + g_cnt_fail);
    }
    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//不可靠订阅回调不处理，不影响写入，其他业务线程（异步、订阅）不受影响
TEST_F(reliability_test, reliability_018)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 100000;
#endif
    const uint32_t sub_thread_num = 2, dml_thread_num = 2;
    const uint32_t thread_num = sub_thread_num + dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = dml_thread_num;
    g_cnt_suc = 0;
    g_cnt_fail = 0;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_barrier_init(&barrier, NULL, thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_async_replace_ip4forward_sub_full, (void *)&index[0]);
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_async_replace_arp, (void *)&index[1]);
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_arp_unrelia, (void *)&index[2]);
    index[3] = 3;
    pthread_create(&thr_arr[3], NULL, thread_sub_ip4forward_stop_epoll_unrelia, (void *)&index[3]);

    for (i = 0; i < thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    printf("g_suc_fail : %d, g_cnt_fail : %d, total : %d\n", g_cnt_suc, g_cnt_fail, g_cnt_suc + g_cnt_fail);
    if (g_flowControl.replace_flowControl_num == 0) {
        EXPECT_EQ(g_data_num, g_cnt_suc + g_cnt_fail);
    }
    pthread_barrier_destroy(&barrier);
    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// maxSeMem：配置为2G，线程1写入大量数据直到内存满回滚，删除部分数据后，可以继续写入；线程2订阅推送正常；线程3扫描正常
//写满内存后尝试再多写40%,删除20%,继续写入到满
// euler上用例执行时间大概18分钟
TEST_F(reliability_test, reliability_019)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 100000;
#else
    g_data_num = 10000000;  // 4百万大概1G
#endif
    const uint32_t thread_num = 3;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_thread_num = thread_num;
    g_delete_num = 0;
    g_try_write_num = 0;
    g_thread_wait = 0;

    for (i = 0; i < g_thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_init(&barrier, NULL, g_thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_replace_ip4forward, (void *)&index[0]);
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_delete_maxSemem_ip4forward, (void *)&index[1]);
    index[2] = 2;
    pthread_create(&thr_arr[2], NULL, thread_sub_ip4forward_async_block, (void *)&index[2]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    pthread_join(thr_arr[2], NULL);

    char command[5120] = {0};
    char const *view_name = "V\\$COM_SHMEM_CTX";
    snprintf(command, 5120, "%s/gmsysview -u %s -s %s -p %s -e %s -q %s", g_toolPath, g_userName, g_connServer,
        g_passwd, g_runEnv[g_runMode], view_name);
    printf("%s\n", command);
    system(command);

    pthread_barrier_destroy(&barrier);
    for (i = 0; i < g_thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//表中预制大量数据，多个事务同时删除数据直到写满undo报错，回滚事务，可以继续删除
// euler事务回滚5s挂死，DTS2021102516126
TEST_F(reliability_test, reliability_020)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 10000;
#else
    g_data_num = 670000;
#endif
    const uint32_t thread_num = 2;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_thread_num = thread_num;
    g_thread_wait = 0;

    for (i = 0; i < g_thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //创建连接，开启心跳
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //插入数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("INSERT", i, 100000, -1);
        test_insert_ip4forward_vertex(g_stmt_sync, g_label_name_for_ls, i, i, fixed);
        test_replace_arp_vertex(g_stmt_sync, g_label_name_arp, i, i, fixed);
    }
    pthread_barrier_init(&barrier, NULL, g_thread_num);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_delete_ip4forward_trx, (void *)&index[0]);
    index[1] = 0;
    pthread_create(&thr_arr[1], NULL, thread_delete_arp_trx, (void *)&index[1]);
    // index[2] = 2;
    // pthread_create(&thr_arr[2], NULL, thread_sub_ip4forward_delete, (void *)&index[2]);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    // pthread_join(thr_arr[2], NULL);

    pthread_barrier_destroy(&barrier);

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    for (i = 0; i < g_thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//表中预制数据，并发进行查询结果集排序
TEST_F(reliability_test, reliability_021)
{
    int ret;
    uint32_t i, j, circle = 5;
#if defined ENV_RTOSV2X
    const uint32_t thread_num = 10;
#else
    const uint32_t thread_num = 50;  // 60会报15004
#endif
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 5000;  // 670000
    g_thread_num = thread_num;
    g_thread_wait = 0;

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    //创建连接，开启心跳
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    //插入数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("INSERT", i, 1000, -1);
        test_insert_ip4forward_vertex(g_stmt_sync, g_label_name_for_ls, i, 0, fixed);
        // test_replace_arp_vertex(g_stmt_sync, g_label_name_arp, i, i, fixed);
    }

    for (j = 0; j < circle; j++) {
        printf("[CIRCLE] circle : %d\n", j);
        pthread_barrier_init(&barrier, NULL, g_thread_num);

        for (i = 0; i < thread_num; i++) {
            index[i] = i;
            pthread_create(&thr_arr[i], NULL, thread_sort_ip4forward, (void *)&index[i]);
        }

        for (i = 0; i < thread_num; i++) {
            pthread_join(thr_arr[i], NULL);
        }

        pthread_barrier_destroy(&barrier);
    }

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 500个消费线程，每个线程创建4个订阅关系，使用不同的订阅通道
TEST_F(reliability_test, reliability_022)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    const uint32_t thread_num = 11;
#else
    const uint32_t thread_num = 501;
#endif
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 2000;
    g_thread_num = thread_num;
    g_thread_wait = 0;

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_init(&barrier, NULL, g_thread_num);

    for (i = 0; i < g_thread_num - 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sub_ip4forward_4, (void *)&index[i]);
    }
    //写线程
    index[g_thread_num - 1] = g_thread_num - 1;
    pthread_create(&thr_arr[g_thread_num - 1], NULL, thread_replace_ip4forward_2000, (void *)&index[g_thread_num - 1]);

    for (i = 0; i < g_thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    pthread_barrier_destroy(&barrier);

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 100个消费线程，每个线程创建20个订阅关系，使用不同的订阅通道
TEST_F(reliability_test, reliability_023)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    const uint32_t thread_num = 11;
#else
    const uint32_t thread_num = 101;
#endif
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 2000;
    g_thread_num = thread_num;
    g_thread_wait = 0;

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_init(&barrier, NULL, g_thread_num);

    for (i = 0; i < g_thread_num - 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sub_ip4forward_20, (void *)&index[i]);
    }
    //写线程
    index[g_thread_num - 1] = g_thread_num - 1;
    pthread_create(&thr_arr[g_thread_num - 1], NULL, thread_replace_ip4forward_2000, (void *)&index[g_thread_num - 1]);

    for (i = 0; i < g_thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    pthread_barrier_destroy(&barrier);

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 1000个消费线程，每个线程创建1个订阅关系
TEST_F(reliability_test, reliability_024)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    const uint32_t thread_num = 11;
#else
    const uint32_t thread_num = 1001;
#endif
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 100;
    g_thread_num = thread_num;
    g_thread_wait = 0;

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_init(&barrier, NULL, g_thread_num);

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[0].userEpollFd;
    connOptions.connName = g_subConnName;
    ret = TestYangGmcConnect(&g_conn_sub, &g_stmt_sub, GMC_CONN_TYPE_SUB, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_thread_num - 1; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_sub_ip4forward_2000, (void *)&index[i]);
    }
    //写线程
    index[g_thread_num - 1] = g_thread_num - 1;
    pthread_create(&thr_arr[g_thread_num - 1], NULL, thread_replace_ip4forward_2000, (void *)&index[g_thread_num - 1]);

    for (i = 0; i < g_thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    pthread_barrier_destroy(&barrier);

    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 1024个线程，每个线程开启1个事务
TEST_F(reliability_test, reliability_025)
{
    // 全局变量设置
    // 用例变量设置
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    const uint32_t thread_num = 24;
#else
    const uint32_t thread_num = 1024;
#endif
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 1024;
    g_thread_wait = 0;
    g_thread_num = thread_num;

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_init(&barrier, NULL, thread_num);

    for (i = 0; i < thread_num; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, thread_replace_ip4forward_trx, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (i = 0; i < thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    pthread_barrier_destroy(&barrier);

    for (i = 0; i < MAX_EP_ASYNC_NUM; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

//事务 dml 扫描并发: 预制1条数据, 创建2个线程, 线程1 开启事务,删除该数据,插入该数据,提交事务, 重复10000次; 线程2
//主键读该数据, 重复4000次 已有CCB结论，不需要执行
TEST_F(reliability_test, reliability_026)
{
    int ret;
    uint32_t i;
    const uint32_t thread_num = 2;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    char fixed2[7] = "0xffff";
    g_data_num = 1;
    g_thread_num = thread_num;
    g_thread_wait = 0;

    pthread_barrier_init(&barrier, NULL, g_thread_num);

    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    test_replace_ip4forward_vertex(g_stmt_sync, g_label_name_for_ls, 0, 0, fixed);
    testGmcDisconnect(g_conn_sync, g_stmt_sync);

    char command[5120] = {0};
    snprintf(command, 5120, "%s/gmsysview record %s 10 -u %s -s %s -p %s -e %s", g_toolPath, g_label_name_for_ls,
        g_userName, g_connServer, g_passwd, g_runEnv[g_runMode]);
    printf("%s\n", command);
    system(command);

    index[0] = 0;
    pthread_create(&thr_arr[0], NULL, thread_delete_insert_ip4forward, (void *)&index[0]);  //反复操作同一条数据
    index[1] = 1;
    pthread_create(&thr_arr[1], NULL, thread_query_ip4forward, (void *)&index[1]);  //反复扫描同一条数据

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    pthread_barrier_destroy(&barrier);
}

//不合适的索引应用：多个字段构成超长key，使用local key，进行范围删除和查询
// C10不使用local
// euler上local key 40W范围删除5s挂死 DTS2021110913681
TEST_F(reliability_test, reliability_027)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 4000;
#else
    g_data_num = 400000;
#endif
    const uint32_t fixed_long_length = 513;
    char fixed[fixed_long_length] = "0xffffffffffffff";
    char *fixed_long;

    fixed_long = (char *)malloc(sizeof(char) * fixed_long_length);
    memset(fixed_long, 0, sizeof(char) * (fixed_long_length));
    memset(fixed_long, 'A', sizeof(char) * (fixed_long_length - 1));

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t msgReadTimeout = CS_NEVER_TIMEOUT;
    uint32_t msgWriteTimeout = CS_NEVER_TIMEOUT;
    ConnOptionT *connOption;

    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, NULL, &msgReadTimeout, &msgWriteTimeout);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL, NULL, NULL, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);

    // 创建表
    GmcDropVertexLabel(g_stmt_sync, g_label_name_local);
    readJanssonFile("schema_file/long_localkey.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_local, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value_PK = i, value_vertex = i, value_SK_unique = i, value_SK_non_unique = i;
        test_set_ip4forward_PK(g_stmt_sync, value_PK);
        test_set_ip4forward_SK_non_unique(g_stmt_sync, value_SK_non_unique);
        test_set_ip4forward_SK_unique(g_stmt_sync, value_SK_unique);
        test_set_ip4forward_vertex(g_stmt_sync, value_vertex, fixed);
        ret = GmcSetVertexProperty(g_stmt_sync, "F26", GMC_DATATYPE_FIXED, fixed_long, fixed_long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //范围区间扫描
    unsigned int arrLen = 1;
    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_FIXED;
    leftKeyProps[0].value = fixed_long;
    leftKeyProps[0].size = strlen(fixed_long);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_FIXED;
    rightKeyProps[0].value = fixed_long;
    rightKeyProps[0].size = strlen(fixed_long);

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = &leftKeyProps[0];
    items_sc[0].rValue = &rightKeyProps[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;

    for (i = 0; i < 10; i++) {
        TEST_INFO("SCAN", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_local, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(g_stmt_sync, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(g_data_num, cnt);
    }

    //范围区间删除
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_local, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_local);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(g_stmt_sync, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    system("date");
    ret = GmcExecute(g_stmt_sync);
    system("date");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(g_data_num, affectRows);

    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    free(fixed_long);
}

//不合适的索引应用：多个字段构成超长key，使用hushcluster，进行增删改查
TEST_F(reliability_test, reliability_028)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 4000;
#else
    g_data_num = 400000;
#endif
    const uint32_t fixed_long_length = 513;
    char fixed[fixed_long_length] = "0xffffffffffffff";
    char *fixed_long;

    fixed_long = (char *)malloc(sizeof(char) * fixed_long_length);
    memset(fixed_long, 0, sizeof(char) * (fixed_long_length));
    memset(fixed_long, 'A', sizeof(char) * (fixed_long_length - 1));

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t msgReadTimeout = CS_NEVER_TIMEOUT;
    uint32_t msgWriteTimeout = CS_NEVER_TIMEOUT;
    ConnOptionT *connOption;

    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, NULL, &msgReadTimeout, &msgWriteTimeout);
    EXPECT_EQ(GMERR_OK, ret);

    // ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL, NULL, NULL, connOption);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);

    // 创建表
    GmcDropVertexLabel(g_stmt_sync, g_label_name_hashcluster);
    readJanssonFile("schema_file/long_hashcluster.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_hashcluster, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value_PK = i, value_vertex = i, value_SK_unique = i, value_SK_non_unique = i;
        test_set_ip4forward_PK(g_stmt_sync, value_PK);
        test_set_ip4forward_SK_non_unique(g_stmt_sync, value_SK_non_unique);
        test_set_ip4forward_SK_unique(g_stmt_sync, value_SK_unique);
        test_set_ip4forward_vertex(g_stmt_sync, value_vertex, fixed);
        ret = GmcSetVertexProperty(g_stmt_sync, "F26", GMC_DATATYPE_FIXED, fixed_long, fixed_long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //读
    for (i = 0; i < 10; i++) {
        TEST_INFO("SCAN", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_hashcluster, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_hashcluster);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_FIXED, fixed_long, fixed_long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(g_data_num, cnt);
    }

    //删除
    for (i = 0; i < 1; i++) {
        TEST_INFO("DELETE", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_hashcluster, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_hashcluster);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_FIXED, fixed_long, fixed_long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        system("date");
        ret = GmcExecute(g_stmt_sync);
        system("date");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_data_num, affectRows);
    }
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    free(fixed_long);
}

//变长字段string/bytes长度,不合适的索引应用：使用localhash，写入非唯一索引冲突链大的数据
TEST_F(reliability_test, reliability_029)
{
    int ret;
    uint32_t i;
#if defined ENV_RTOSV2X
    g_data_num = 1000;
#else
    g_data_num = 10000;
#endif
    const uint32_t long_length = 513, long_length2 = 3 * 1024 + 1;
    char fixed[long_length] = "0xffffffffffffff";

    char *value_string, *value_string2;
    value_string = (char *)malloc(sizeof(char) * long_length);
    memset(value_string, 0, sizeof(char) * (long_length));
    memset(value_string, 'A', sizeof(char) * (long_length - 1));

    value_string2 = (char *)malloc(sizeof(char) * long_length2);
    memset(value_string2, 0, sizeof(char) * (long_length2));
    memset(value_string2, 'A', sizeof(char) * (long_length2 - 1));

    char *value_bytes, *value_bytes2;
    value_bytes = (char *)malloc(sizeof(char) * long_length);
    memset(value_bytes, 0, sizeof(char) * (long_length));
    memset(value_bytes, 'A', sizeof(char) * (long_length - 1));

    value_bytes2 = (char *)malloc(sizeof(char) * long_length2);
    memset(value_bytes2, 0, sizeof(char) * (long_length2));
    memset(value_bytes2, 'A', sizeof(char) * (long_length2 - 1));

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t msgReadTimeout = CS_NEVER_TIMEOUT;
    uint32_t msgWriteTimeout = CS_NEVER_TIMEOUT;
    ConnOptionT *connOption;
    ret = testMallocConnOptions(&connOption, NULL, NULL, NULL, NULL, &msgReadTimeout, &msgWriteTimeout);
    EXPECT_EQ(GMERR_OK, ret);
    do {
        //创建连接，开启心跳
        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, false, NULL, NULL, NULL, connOption);
    } while (ret != GMERR_OK);
    EXPECT_EQ(GMERR_OK, ret);
    testFreeConnOptions(connOption);

    // 创建表
    GmcDropVertexLabel(g_stmt_sync, g_label_name_string_bytes);
    readJanssonFile("schema_file/long_string_bytes.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema);

    //写数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_string_bytes, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value_uint32 = i;
        ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_UINT32, &value_uint32, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_STRING, value_string, long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_STRING, value_string2, long_length2 - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_BYTES, value_bytes, long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F4", GMC_DATATYPE_BYTES, value_bytes2, long_length2 - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    //读
    for (i = 0; i < 10; i++) {
        TEST_INFO("SCAN by string", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_string_bytes, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_string);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_STRING, value_string, long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(g_data_num, cnt);
    }

    for (i = 0; i < 10; i++) {
        TEST_INFO("SCAN by bytes", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_string_bytes, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_bytes);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_BYTES, value_bytes, long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt_sync, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(g_data_num, cnt);
    }

    //删除
    for (i = 0; i < 1; i++) {
        TEST_INFO("DELETE", i, 1, -1);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_string_bytes, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_SK_bytes);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_BYTES, value_bytes, long_length - 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(g_data_num, affectRows);
    }

    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    free(value_string);
    free(value_bytes);
}

// ops,10倍压力
//类似长稳，不需要执行，while(1)循环
TEST_F(reliability_test, reliability_099)
{
    int ret;
    uint32_t i;
    const uint32_t dml_thread_num = 9;
    const uint32_t thread_num = dml_thread_num;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_data_num = 1400000;
    g_thread_num = thread_num;
    g_ops1 = 0, g_ops2 = 0;

    for (i = 0; i < thread_num; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

#if 0
    //创建连接，开启心跳
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, true, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, true, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    gettimeofday(&start,NULL);
    //插入数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, -1);
        test_replace_ip4forward_vertex(g_stmt_sync, g_label_name_for_ls, i, i, fixed);
    }
    gettimeofday(&end,NULL);
    duration = 1000000 * (end.tv_sec-start.tv_sec)+ end.tv_usec-start.tv_usec;
    printf("[INFO] Time %lf s, all OpNum : %d, ops : %lf\n",
           (double)duration/1000000, g_data_num, (double)g_data_num/(duration/1000000));

    gettimeofday(&start,NULL);
    //插入数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE ASYNC", i, 10000, -1);
        test_replace_ip4forward_vertex_async(g_stmt_async, g_label_name_for_ls, i, i, fixed, replace_vertex_callback_verify);
    }
    gettimeofday(&end,NULL);
    duration = 1000000 * (end.tv_sec-start.tv_sec)+ end.tv_usec-start.tv_usec;
    printf("[INFO] Time %lf s, all OpNum : %d, ops : %lf\n",
           (double)duration/1000000, g_data_num, (double)g_data_num/(duration/1000000));

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    testGmcDisconnect(g_conn_async, g_stmt_async);
#endif

    pthread_barrier_init(&barrier, NULL, 1);
    pthread_create(&thr_arr[0], NULL, thread_replace_ip4forward_ops, (void *)&index[0]);
    pthread_join(thr_arr[0], NULL);
    pthread_barrier_destroy(&barrier);

    pthread_barrier_init(&barrier, NULL, dml_thread_num - 1);

    for (i = 1; i < dml_thread_num; i++) {
        index[i] = i;
        // pthread_create(&thr_arr[i], NULL, thread_async_replace_ip4forward_ops, (void *)&index[i]);
        pthread_create(&thr_arr[i], NULL, thread_replace_ip4forward_ops, (void *)&index[i]);
    }
    for (i = 1; i < dml_thread_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    // printf("ops : %lf\n", g_ops1 + g_ops2);
    for (i = 1; i < dml_thread_num; i++) {
        printf("g_flowControl_arr[%d].replace_flowControl_num : %d\n", i, g_flowControl_arr[i].replace_flowControl_num);
    }

    int fail_cnt = 0, succ_cnt;
    for (i = 1; i < dml_thread_num; i++) {
        fail_cnt = fail_cnt + g_flowControl_arr[i].replace_flowControl_num;
    }
    succ_cnt = g_data_num * (dml_thread_num - 1) - fail_cnt;
    printf("total cnt : %d, succ cnt : %d, fail cnt : %d, succ rate : %lf\n", g_data_num * (dml_thread_num - 1),
        succ_cnt, fail_cnt, (double)succ_cnt / (g_data_num * (dml_thread_num - 1)));

    for (i = 0; i < thread_num; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_barrier_destroy(&barrier);
}

//表空间占用达到80%
//类似长稳，不需要执行，while(1)循环
TEST_F(reliability_test, reliability_098)
{
    int ret;
    uint32_t i;
    const uint32_t thread_num = 1;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_data_num = 3000000;
    g_thread_num = thread_num;

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //创建连接，开启心跳
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, true, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    //插入数据
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, -1);
        // test_replace_ip4forward_vertex(g_stmt_sync, g_label_name_for_ls, i, i, fixed);

        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_for_ls, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value_PK = i, value_vertex = i;
        uint32_t value_SK_unique = i, value_SK_non_unique = i;
        test_set_ip4forward_PK(g_stmt_sync, value_PK);
        test_set_ip4forward_SK_non_unique(g_stmt_sync, value_SK_non_unique);
        test_set_ip4forward_SK_unique(g_stmt_sync, value_SK_unique);
        test_set_ip4forward_vertex(g_stmt_sync, value_vertex, fixed);

        do {
            ret = GmcExecute(g_stmt_sync);
            if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                g_flowControl.replace_flowControl_num++;
                TEST_INFO("GMERR_COMMON_STREAM_OVERLOAD", value_PK, 1, -1);
            }
        } while (ret == GMERR_COMMON_STREAM_OVERLOAD);
        if (ret == GMERR_OK) {
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            printf("i : %d, ret : %d\n", i, ret);
        }
    }

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

//类似长稳，不需要执行，while(1)循环
TEST_F(reliability_test, reliability_097)
{
    int ret;
    uint32_t i;
    const uint32_t thread_num = 1;
    pthread_t thr_arr[thread_num];
    void *thr_ret[thread_num];
    int index[thread_num] = {0};
    char fixed[17] = "0xffffffffffffff";
    g_data_num = 3000000;
    g_thread_num = thread_num;

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    //创建连接，开启心跳
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, true, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    while (1) {
        for (i = 3000000; i < 4000000; i++) {
            TEST_INFO("REPLACE", i, 10000, -1);
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_for_ls, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value_PK = i, value_vertex = i;
            uint32_t value_SK_unique = i, value_SK_non_unique = i;
            test_set_ip4forward_PK(g_stmt_sync, value_PK);
            test_set_ip4forward_SK_non_unique(g_stmt_sync, value_SK_non_unique);
            test_set_ip4forward_SK_unique(g_stmt_sync, value_SK_unique);
            test_set_ip4forward_vertex(g_stmt_sync, value_vertex, fixed);

            do {
                ret = GmcExecute(g_stmt_sync);
                if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                    g_flowControl.replace_flowControl_num++;
                    TEST_INFO("GMERR_COMMON_STREAM_OVERLOAD", value_PK, 1, -1);
                }
            } while (ret == GMERR_COMMON_STREAM_OVERLOAD);
            if (ret == GMERR_OK) {
                ASSERT_EQ(GMERR_OK, ret);
            } else {
                if (i % 10000 == 0) {
                    // printf("REPLACE 1 i : %d, ret : %d\n", i, ret);
                }
            }
        }

        //删除数据
        for (i = 1000000; i < 3000000; i++) {
            TEST_INFO("DELETE", i, 10000, -1);
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_for_ls, GMC_OPERATION_DELETE);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value_uint32 = i;
            uint8_t value_uint8 = i & 0xFF;

            ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value_uint32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 1, GMC_DATATYPE_UINT32, &value_uint32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 2, GMC_DATATYPE_UINT32, &value_uint32, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(g_stmt_sync, 3, GMC_DATATYPE_UINT8, &value_uint8, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt_sync, g_lable_PK);
            EXPECT_EQ(GMERR_OK, ret);

            do {
                ret = GmcExecute(g_stmt_sync);
                if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                    g_flowControl.replace_flowControl_num++;
                }
            } while (ret == GMERR_COMMON_STREAM_OVERLOAD);
            if (ret == GMERR_OK) {
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                EXPECT_EQ(GMERR_OK, ret);
                // ASSERT_EQ(1, affectRows);
            } else {
                if (i % 10000 == 0) {
                    // printf("DELETE i : %d, ret : %d\n", i, ret);
                }
            }
        }

        for (i = 1000000; i < 3000000; i++) {
            TEST_INFO("REPLACE", i, 10000, -1);
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name_for_ls, GMC_OPERATION_REPLACE);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value_PK = i, value_vertex = i;
            uint32_t value_SK_unique = i, value_SK_non_unique = i;
            test_set_ip4forward_PK(g_stmt_sync, value_PK);
            test_set_ip4forward_SK_non_unique(g_stmt_sync, value_SK_non_unique);
            test_set_ip4forward_SK_unique(g_stmt_sync, value_SK_unique);
            test_set_ip4forward_vertex(g_stmt_sync, value_vertex, fixed);

            do {
                ret = GmcExecute(g_stmt_sync);
                if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
                    g_flowControl.replace_flowControl_num++;
                    TEST_INFO("GMERR_COMMON_STREAM_OVERLOAD", value_PK, 1, -1);
                }
            } while (ret == GMERR_COMMON_STREAM_OVERLOAD);
            if (ret == GMERR_OK) {
                ASSERT_EQ(GMERR_OK, ret);
            } else {
                if (i % 10000 == 0) {
                    // printf("REPLACE 2 i : %d, ret : %d\n", i, ret);
                }
            }
        }
    }

    //断连
    testGmcDisconnect(g_conn_sync, g_stmt_sync);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}
