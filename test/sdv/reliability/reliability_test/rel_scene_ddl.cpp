/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
extern "C" {}
// C++11原子操作库
#include <atomic>
#include <iostream>
#include <malloc.h>
#include <errno.h>
#include <sys/types.h>
// 共享内存需要的头文件
#include <sys/ipc.h>
#include <sys/shm.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "reliability_scene_ddl_common.h"

std::atomic_int g_total(0);
std::atomic_int connOnline(0);
bool SIG_PIPE_SEND = false;
class reliability_scene_ddl : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
    }
    static void TearDownTestCase()
    {}
};

void reliability_scene_ddl::SetUp()
{}
void reliability_scene_ddl::TearDown()
{}
void *thread_rCPU_Overloadl_value90(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rCPU_Overloadl_value90");
    return NULL;
}
void *thread_reliability_scene_ddl_batch_export_vertexlabel(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    void *g_label = NULL;
    char *g_schema = NULL;
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 服务端进程绑定CPU指定核心
    system("taskset -pc 0 `pidof gmserver`");
    // 延时15S 等待故障注入
    sleep(15);
    // 建立链接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 完成批量导入表
    batchexportVextexlabel();
    // 叠加长稳用例后，使用视图查看的表个数不一样，后续需要更新脚本验证导入表的个数
    // 删表
    ret = deleteLabel(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    return NULL;
}
/*****************************************************************************
 * Description  : 002  90%过载下导入表
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value90, NULL);
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_batch_export_vertexlabel, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  : 005  共享内存不足时，导入表
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_005)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    AW_FUN_Log(LOG_STEP, "test start\n");
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

// 单次申请共享内存大小
#if defined ENV_RTOSV2X
#define SHAREMEMSIZE 1048576  // 1M
#define MEMCOUNT 5            // 内存申请次数
#else
#define MEMCOUNT 10000           //
#define SHAREMEMSIZE 1073741824  // 1G
#endif
    // 建立链接
    AW_FUN_Log(LOG_STEP, "建立链接%d\n", SHAREMEMSIZE);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmid[10000];
    key_t key[10000];
    for (int i = 0; i < 10000; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
        if (i % 1000 == 0) {
            AW_FUN_Log(LOG_STEP, "i=%d\n");
        }
    }
    AW_FUN_Log(LOG_STEP, "key[i] = i + 12345\n");
    int count = 0;
    int j = 0;
    for (int i = 0; i < MEMCOUNT; i++) {
        shmid[i] = shmget(key[i], SHAREMEMSIZE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_STEP, "shmid is %d  i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            j = i;
            AW_FUN_Log(LOG_STEP, "shmid[i] == -1\n");
            break;
        } else {
            char *p;
            AW_FUN_Log(LOG_STEP, "内存初始化");
            p = (char *)shmat(shmid[i], NULL, 0);
            // 对共享内存实现读写
            memset(p, 1, SHAREMEMSIZE);
            AW_FUN_Log(LOG_STEP, "取消映射");
            // 取消映射
            shmdt(p);
            count++;
        }
    }
    for (int i = j; i < MEMCOUNT; i++) {
        shmid[i] = shmget(key[i], SHAREMEMSIZE, IPC_CREAT | 0630);
        AW_FUN_Log(LOG_STEP, "shmid is %d  i is %d\n", shmid[i], i);
        if (shmid[i] == -1) {
            AW_FUN_Log(LOG_STEP, "(shmid[i] == -1)\n");
            sleep(50);
            j = i;
            break;
        } else {
            char *p;
            p = (char *)shmat(shmid[i], NULL, 0);
            // 对共享内存实现读写
            memset(p, 1, 20000);
            // 取消映射
            AW_FUN_Log(LOG_STEP, "取消映射");
            shmdt(p);
            count++;
        }
    }
    batchexportVextexlabel();
    AW_FUN_Log(LOG_STEP, "count is %d ", count);
    // 释放消耗掉的共享内存
    for (int i = 0; i < count; i++) {
        AW_FUN_Log(LOG_STEP, "i is %d \n", i);
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
    sleep(20);
    batchexportVextexlabel();
    AW_FUN_Log(LOG_STEP, "删表");
    // 删表
    ret = deleteLabel(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "断开链接");
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(20);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
void *thread_reliability_scene_ddl_batch_export_vertexlabel_test(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    void *g_label = NULL;
    char *g_schema = NULL;
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建立链接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 完成批量导入表
    batchexportVextexlabel();
    // 叠加长稳用例后，使用视图查看的表个数不一样，后续需要更新脚本验证导入表的个数
    // 删表
    ret = deleteLabel(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开链接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    return NULL;
}
/*****************************************************************************
 * Description  : 007  时钟向前跳变，导入表过程中进行时间跳变
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_rSysClockJump_60(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rSysClockJump_60");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_batch_export_vertexlabel_test, NULL);
    pthread_create(&FLT, NULL, thread_rSysClockJump_60, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  : 008  时钟向后跳变，导入表过程中进行时间跳变
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_rSysClockJump_plus60(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rSysClockJump_plus60");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_batch_export_vertexlabel_test, NULL);
    pthread_create(&FLT, NULL, thread_rSysClockJump_plus60, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :011 服务端异常退出，1.客户端循环建表和删表
                                    2.服务端异常退出
                                    3.客户端收到异常通知
                                    4.客户端关闭连接资源，正常
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * Note         ：此场景不叠加在长稳用例中
 * *****************************************************************************/
void *thread_reliability_scene_ddl_while_creat_delete_vertexlabel(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    void *g_label = NULL;
    char *g_schema = NULL;

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelName[] = "dns_global_cfg";
    // 建立链接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("../../schema_file/r21_ndb/gmjson/dns/dns_global_cfg.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    int number = 0;
    // 循环建表删表
    while (1) {
        ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
        if ((ret == GMERR_CONNECTION_RESET_BY_PEER) || (ret == GMERR_INTERNAL_ERROR)) {
            AW_FUN_Log(LOG_STEP, "creat vertex label number is %d\n", number);
            // 断开链接
            ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            number++;
            ret = GmcDropVertexLabel(g_stmt_sync, labelName);
            if ((ret == GMERR_CONNECTION_RESET_BY_PEER) || (ret == GMERR_INTERNAL_ERROR)) {
                AW_FUN_Log(LOG_STEP, "creat vertex label number is %d\n", number);
                // 断开链接
                ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 如果服务端收到SIGPIPE信号 客户端完成一次建表操作 然后退出
        if (SIG_PIPE_SEND) {
            ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcDropVertexLabel(g_stmt_sync, labelName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            free(g_schema);
            // 清理环境
            ret = close_epoll_thread();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testEnvClean();
            return 0;
        }
    }
    free(g_schema);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    return NULL;
}
void *thread_rProc_k(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_k");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel, NULL);
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :012 客户端异常退出，1.客户端循环建表和删表
                                    2.客户端异常退出
                                    3.客户端收到异常通知
                                    4.客户端关闭连接资源，正常
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_reliability_scene_ddl_while_creat_delete_vertexlabel_clent(void *args)
{
    system("./rel_ddl_client");
    return NULL;
}
void *thread_rProc_k_clent(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_k_clent");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel_clent, NULL);
    pthread_create(&FLT, NULL, thread_rProc_k_clent, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :013 服务端挂起，1.客户端循环建表和删表
                                    2.服务端挂起
                                    3.客户端收到异常通知
                                    4.客户端关闭连接资源，正常
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_reliability_scene_ddl_while_creat_delete_vertexlabel_kill19_18(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    void *g_label = NULL;
    char *g_schema = NULL;

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char labelName[] = "dns_global_cfg";
    // 建立链接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int number = 0;
    // 循环建表删表
    bool isGmserver = true;
    int count = 0;
    readJanssonFile("../../schema_file/r21_ndb/gmjson/dns/dns_global_cfg.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    while (1) {
        if (isGmserver && (count == 1)) {
            break;
        }
        ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
        if (ret == GMERR_REQUEST_TIME_OUT) {
            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {
            isGmserver = true;  // 正常
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        if (ret == GMERR_REQUEST_TIME_OUT) {
            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {
            isGmserver = true;  // 正常
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(g_schema);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    return NULL;
}
void *thread_rProc_kill19_18_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_kill19_18_server");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel_kill19_18, NULL);
    pthread_create(&FLT, NULL, thread_rProc_kill19_18_server, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :014 客户端端挂起，1.客户端循环建表和删表
                                2.挂起客户端端进程
                                3.5秒后解挂服务端进程
                                4.客户端业务恢复
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_reliability_scene_ddl_while_creat_delete_vertexlabel_kill19_18_clent(void *args)
{
    system("./rel_ddl_kill");
    return NULL;
}
void *thread_rProc_kill19_18_client(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_kill19_18_clent");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel_kill19_18_clent, NULL);
    pthread_create(&FLT, NULL, thread_rProc_kill19_18_client, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :016 发送SIGILL信号，1.客户端循环建表和删表
                                    2.向服务端进程发送SIGILL信号
                                    3.服务端进程coredump
                                    4.客户端进程获得服务端故障通知
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * Note         ：此场景不叠加在长稳用例中
 * *****************************************************************************/
void *thread_rProc_SIGILL(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_SIGILL");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel, NULL);
    pthread_create(&FLT, NULL, thread_rProc_SIGILL, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :017 发送SIGINT信号，1.客户端循环建表和删表
                                    2.向服务端进程发送SIGILL信号
                                    3.服务端进程coredump
                                    4.客户端进程获得服务端故障通知
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * Note         ：此场景不叠加在长稳用例中
 * *****************************************************************************/
void *thread_rProc_SIGINT(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_SIGINT");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel, NULL);
    pthread_create(&FLT, NULL, thread_rProc_SIGINT, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :018发送SIGPIPE信号，1.客户端循环建表和删表
                                    2.向服务端进程发送SIGPIPE信号
                                    3.无影响，客户端和服务端业务处理正常。
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_rProc_SIGPIPE(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rProc_SIGPIPE");
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret;
    GmcConnT *conn = NULL;
    ret = testGmcConnect(&conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_creat_delete_vertexlabel, NULL);
    pthread_create(&FLT, NULL, thread_rProc_SIGPIPE, NULL);
    pthread_join(FLT, NULL);
    sleep(3);
    // 确认向服务端进程发送SIGPIPE信号 将标志位写为真
    SIG_PIPE_SEND = true;
    pthread_join(DB, NULL);
}
/*****************************************************************************
 * Description  :019超大规格表， 1.建立超规格字段表，失败
                                2.建立规格内最大表，成功
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_019)
{
    AW_FUN_Log(LOG_STEP, "test start");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    void *g_label = NULL;
    char *g_schema_1024 = NULL;
    char *g_schema_1023 = NULL;
    char labelName[] = "dns_global_cfg";

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file_ddl/fields_1024_schema.gmjson", &g_schema_1024);
    EXPECT_NE((void *)NULL, g_schema_1024);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_1024, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema_1024);
    readJanssonFile("schema_file_ddl/fields_1023_schema.gmjson", &g_schema_1023);
    EXPECT_NE((void *)NULL, g_schema_1024);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_1023, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, "fields_1023_schema");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema_1023);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  :020表的个数超系统限制，  1.循环创建不同的表，直到创建失败
                                        2.删除1个存在的表，新创建1个表，成功
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_020)
{
    AW_FUN_Log(LOG_STEP, "test start");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    void *g_label = NULL;
    char *g_schema_1024 = NULL;
    char *g_schema_1023 = NULL;
    char labelName[] = "dns_global_cfg";

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表，创建2000个表，第2001个失败
    system("sh create_multi_label_ddl.sh 2000");
    char LabelName[256];
    char schema_path[256];
    uint32_t label_num = 0;
    for (uint32_t i = 0; i < 2000; i++) {
        char *schema_json = NULL;
        sprintf(LabelName, "Vertex_tree_%d", i);
        sprintf(schema_path, "./multi_vertexlabel_ddl/Vertex_tree_%d.gmjson", i);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);
        memset(schema_path, 0, 256);
        ret = GmcCreateVertexLabel(g_stmt_sync, schema_json, NULL);
        // 第1025个表建立失败, 当叠加长稳用例时，这里不一定是1024
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
            break;
        } else {
            label_num += 1;
        }
        free(schema_json);
    }
    // 删除一张表
    ret = GmcDropVertexLabel(g_stmt_sync, "Vertex_tree_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再建一张表
    char *schema_json_1 = NULL;
    readJanssonFile("./multi_vertexlabel_ddl/Vertex_tree_1.gmjson", &schema_json_1);
    ASSERT_NE((void *)NULL, schema_json_1);
    ret = GmcCreateVertexLabel(g_stmt_sync, schema_json_1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema_json_1);

    // 删除所有表
    char LabelNameDrop[256];
    for (uint32_t i = 0; i < label_num; i++) {
        ret = sprintf(LabelNameDrop, "Vertex_tree_%d", i);
        ret = GmcDropVertexLabel(g_stmt_sync, LabelNameDrop);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 021连接池耗尽，1.客户端线程功能：连接服务端，循环1万次建表和删表，建表，退出
                                2.启动maxConnSize+1个线程
                                3.maxConnSize个业务线程处理正常，无报错
                                4.最后一个线程连接服务端失败
                                5.检查maxConnSiz个表，创建成功
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
int reliability_testGmcConnect(GmcConnT **connOut, GmcStmtT **stmt = NULL, int syncMode = 0, bool needEpoll = true,
    EpollRegFunctionT epollReg = g_epoll_reg_info, const char *connName = NULL, const void *chanRingLen = NULL,
    ConnOptionT *connOptions = NULL, const int32_t *packShrinkThresholdSize = NULL, int runMode = -1,
    int *epollFd = &g_epollData.userEpollFd)
{
    int ret = testEnvInit(runMode);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testEnvInit failed, ret = %d.\n", ret);
        return -1;
    }
    *connOut = NULL;
    GmcConnT *conn;

    GmcConnOptionsT *connOptionsInner;
    ret = GmcConnOptionsCreate(&connOptionsInner);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsCreate failed, ret = %d.\n", ret);
        return ret;
    }

    const char *p1 = g_connServer;
    if (connOptions && connOptions->serverLocator) {
        p1 = connOptions->serverLocator;
    }
    const char *p3 = g_passwd;
    if (connOptions && connOptions->passwd) {
        p3 = connOptions->passwd;
    }

    ret = GmcConnOptionsSetServerLocator(connOptionsInner, p1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetServerLocator failed, serverLocator = %s, ret = %d.\n", p1, ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connName) {
        ret = GmcConnOptionsSetConnName(connOptionsInner, connName);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetConnName failed, connName = %s, ret = %d.\n", connName, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (needEpoll) {
        ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptionsInner, epollReg, epollFd);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetEpollRegFuncWithUserData failed, ret = %d.\n", ret);
            return ret;
        }
    }

    if (packShrinkThresholdSize) {
        ret = GmcConnOptionsSetPackShrinkThreshold(connOptionsInner, *packShrinkThresholdSize);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetPackShrinkThreshold failed, packShrinkThresholdSize = %d, ret = "
                   "%d.\n",
                *packShrinkThresholdSize, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->requestTimeout) {
        ret = GmcConnOptionsSetRequestTimeout(connOptionsInner, connOptions->requestTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetRequestTimeout failed, requestTimeout = %d, ret = %d.\n",
                connOptions->requestTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgReadTimeout) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptionsInner, connOptions->msgReadTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetMsgReadTimeout failed, msgReadTimeout = %d, ret = %d.\n",
                connOptions->msgReadTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->msgWriteTimeout) {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, connOptions->msgWriteTimeout);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetMsgWriteTimeout failed, msgWriteTimeout = %d, ret = %d.\n",
                connOptions->msgWriteTimeout, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    } else {
        ret = GmcConnOptionsSetMsgWriteTimeout(connOptionsInner, 1);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetMsgWriteTimeout failed, msgWriteTimeout = %d, ret = %d.\n",
                1, ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    if (connOptions && connOptions->subFailedCb) {
        ret =
            GmcConnOptionsSetSubFailedCallback(connOptionsInner, connOptions->subFailedCb, connOptions->subFailedData);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetSubFailedCallback failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    ret = GmcConnOptionsSetFlowCtrlCallback(connOptionsInner, ConnCtionFlowCtrlNotice, NULL);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetFlowCtrlCallback failed, ret = %d.\n", ret);
        return ret;
    }

    ret = testSetAsyncQueueSize(connOptionsInner, (uint32_t *)chanRingLen);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[testGmcConnect] testSetAsyncQueueSize failed, msgQueueSize = %d, ret = %d.\n",
            *(uint32_t *)chanRingLen, ret);
        GmcConnOptionsDestroy(connOptionsInner);
        return ret;
    }

    if (connOptions && connOptions->useReservedConn) {
        ret = GmcConnOptionsSetReservedFlag(connOptionsInner, connOptions->useReservedConn);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnOptionsSetReservedFlag failed, ret = %d.\n", ret);
            GmcConnOptionsDestroy(connOptionsInner);
            return ret;
        }
    }

    int n = 500;
    if (g_envType == 0) {
        ++g_total;
        while (g_total > connOnline && g_total - connOnline > 128 && n > 0) {
            usleep(10000);
            --n;
        }
    }

    GmcStmtT *st;
    do {
        ret = GmcConnect((GmcConnTypeE)syncMode, connOptionsInner, &conn);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcConnect failed, serverLocator = %s, passwd = %s, ret = %d.\n", p1,
                p3, ret);
            break;
        }
        *connOut = conn;
        if (stmt == NULL) {
            break;
        }
        ret = GmcAllocStmt(conn, &st);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "[testGmcConnect] GmcAllocStmt failed, ret = %d.\n", ret);
            break;
        }
        *stmt = st;
    } while (0);
    GmcConnOptionsDestroy(connOptionsInner);
    if (ret == 0) {
        ++connOnline;
        ret = testSetNameSpace(conn, syncMode);
    } else {
        --g_total;
    }
    return ret;
}
int reliability_testGmcDisconnect(GmcConnT *conn, GmcStmtT *stmt = NULL)
{
    if (stmt) {
        GmcFreeStmt(stmt);
    }
    int ret = GmcDisconnect(conn);
    if (ret != 0) {
        AW_FUN_Log(LOG_INFO, "[testGmcDisconnect] GmcDisconnect() failed, ret = %d.\n", ret);
    }
    --connOnline;
    --g_total;
    return ret;
}
void *thread_reliability_scene_ddl_while_1W_creat_delete_vertexlabel(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    char labelName[] = "dns_global_cfg";
    // 建立链接
    int ret = reliability_testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int number = 0;
    void *g_label = NULL;
    char *g_schema = NULL;
    readJanssonFile("schema_file_ddl/dns_global_cfg.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    // 循环建表删表
    while (1) {
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
#if defined ENV_RTOSV2X
        usleep(5000);
#endif
        ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        number++;
        if (number == 99) {
            AW_FUN_Log(LOG_STEP, "Creat and Delete Vertexlabel while 1W end\n");
            break;
        }
        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(g_schema);
    ret = reliability_testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return NULL;
}
bool limit = false;
void *thread_insert_VertexData(void *args)
{
    GmcConnT *g_conn_sync = NULL;  //  conn 句柄
    GmcStmtT *g_stmt_sync = NULL;  //  stmt 句柄
    char labelName[] = "dns_global_cfg";
    char pkName[] = "dns_global_cfg_pk";
    // 一共1024个链接 每个线程入参就是1024*1 1024*2 1024*3
    int index = (*(int *)args);
    // 建立链接
    int ret = reliability_testGmcConnect(&g_conn_sync, &g_stmt_sync);
    if (ret == GMERR_OK) {
        // insert 数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = index; i < index + 1; i++) {
            uint32_t vsys_id = i;
            ret = GmcSetVertexProperty(g_stmt_sync, "vsys_id", GMC_DATATYPE_UINT32, &vsys_id, sizeof(vsys_id));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(g_stmt_sync);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            int affectRows = 0;
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(1, affectRows);
        }
        while (1) {
            if (limit) {
                break;
            }
            int a = 0;
        }
        ret = reliability_testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
#if defined ENV_RTOSV2X
        AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_TOO_MANY_CONNECTIONS, ret);
#endif
        limit = true;
        reliability_testGmcDisconnect(g_conn_sync, g_stmt_sync);
    }
    return NULL;
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_021)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=6,200,300\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 客户端线程功能：连接服务端，循环1万次建表和删表，建表，退出
    pthread_t DB;
    pthread_create(&DB, NULL, thread_reliability_scene_ddl_while_1W_creat_delete_vertexlabel, NULL);
    pthread_join(DB, NULL);
    GmcConnT *relconn = NULL;  //  conn 句柄
    GmcStmtT *relstmt = NULL;  //  stmt 句柄
    char labelname[1024] = "dns_global_cfg";
    // 建立链接
    ret = reliability_testGmcConnect(&relconn, &relstmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *scheamjsoninfile = NULL;
    readJanssonFile("schema_file_ddl/dns_global_cfg.gmjson", &scheamjsoninfile);
    EXPECT_NE((void *)NULL, scheamjsoninfile);
    GmcDropVertexLabel(relstmt, labelname);
    ret = GmcCreateVertexLabel(relstmt, scheamjsoninfile, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = reliability_testGmcDisconnect(relconn, relstmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 启动maxConnSize+1个线程
    const int maxClient = 1537;
    pthread_t DB_Client[maxClient];
    // maxConnSize个业务线程处理正常，无报错
    int index[maxClient];
    for (int j = 0; j < maxClient; j++) {
        index[j] = j;
        pthread_create(&DB_Client[j], NULL, thread_insert_VertexData, (void *)&index[j]);
    }
    for (int i = 0; i < maxClient; i++) {
        pthread_join(DB_Client[i], NULL);
    }
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
/*****************************************************************************
 * Description  : 022异步消息队列写满    1.启动客户端，正常的生产、不消费（发送异步请求消息，不接受响应消息）
                                        2.客户端开始处理正常，后面报队列满错误码
                                        3.恢复消费后继续发送请求，不会报错
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
bool BUFFER_FULL = false;
bool callbackwaitsleep = true;
void insert_vertex_callback_wait(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    if (!BUFFER_FULL) {  // 如果没有触发队列满就一直睡眠
        usleep(1000);
    } else {
        callbackwaitsleep = false;
    }
    if (userData) {
        int ret = 0;
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->affectRows = affectedRows;
        user_data->historyRecvNum++;
        if (user_data->recvNum == 0) {
            if ((user_data->status == GMERR_OK) || (user_data->status == GMERR_REQUEST_TIME_OUT)) {
                ret = GMERR_OK;
            } else {
                ret = user_data->status;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        user_data->recvNum++;
    }
}
TEST_F(reliability_scene_ddl, reliability_scene_ddl_022)
{
    GmcConnT *g_conn_sync = NULL, *conn_async = NULL;
    GmcStmtT *g_stmt_sync = NULL, *stmt_async = NULL;
    void *g_label = NULL;
    char *g_schema = NULL;
    char labelName[] = "dns_global_cfg";

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建异步连接
    ret = reliability_testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file_ddl/dns_global_cfg.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int affectRows;
    ret = testGmcPrepareStmtByLabelName(stmt_async, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t i = 0;
    uint64_t x = 0;
    uint8_t j = 0;
    AsyncUserDataT data = {0};
    while (1) {
        uint32_t vsys_id = i;
        ret = GmcSetVertexProperty(stmt_async, "vsys_id", GMC_DATATYPE_UINT32, &vsys_id, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t vsys_enable = j % 8;
        ret = GmcSetVertexProperty(stmt_async, "resolve_enable", GMC_DATATYPE_UINT8, &vsys_enable, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t vsys_add = x;
        ret = GmcSetVertexProperty(stmt_async, "newAdd1", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt_async, "newAdd2", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt_async, "newAdd3", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt_async, "newAdd4", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt_async, "newAdd5", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd6", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd7", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd8", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd9", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd10", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "newAdd11", GMC_DATATYPE_UINT64, &vsys_add, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 写string数据
        uint32_t SuperSize = 1024;
        char *SuperValue = (char *)malloc(SuperSize);
        memset(SuperValue, 'B', (SuperSize - 1));
        SuperValue[SuperSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt_async, "P0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P16", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P17", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P18", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P19", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "P20", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q16", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q17", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q18", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q19", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "q20", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r16", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r17", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r18", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r19", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "r20", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s16", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s17", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s18", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s19", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "s20", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t0", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t1", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t2", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t3", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t4", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t5", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t6", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t7", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t8", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t9", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t10", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t11", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t12", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t13", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t14", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t15", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t16", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t17", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t18", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t19", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt_async, "t20", GMC_DATATYPE_STRING, SuperValue, (SuperSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(SuperValue);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback_wait;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt_async, &insertRequestCtx);
        if (i % 10000 == 0) {

            AW_FUN_Log(LOG_STEP, "GmcExecuteAsync %d", i);
        }
        if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                AW_FUN_Log(LOG_STEP, "GMERR_CONNECTION_SEND_BUFFER_FULL\n");
                ret = GmcExecuteAsync(stmt_async, &insertRequestCtx);  // 消息通道溢出，重发
                BUFFER_FULL = true;
            }
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            j++;
            x++;
            i++;
            if (i % 10000 == 0) {
                AW_FUN_Log(LOG_STEP, "%d\n", i);
            }
            if (!callbackwaitsleep)  // 服务端队列已放开
            {
                break;  // 退出大循环
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "%d", i);
    AW_FUN_Log(LOG_STEP, "GmcDropVertexLabel");
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = reliability_testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = reliability_testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 023表空间满       1.客户端创建10张表并写入数据到表空间满
                                    2.删除部分数据
                                    3.继续写入数据，写入成功
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_023)
{
    AW_FUN_Log(LOG_STEP, "test start");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    void *g_label = NULL;
    char *g_schema = NULL;
    char labelName[] = "dns_global_cfg";
    char labelName_pk[] = "dns_global_cfg_pk";

    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file_ddl/dns_global_cfg.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt_sync, labelName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 0;
    int affectRows = 0;
    while (1) {
        uint32_t vsys_id = i;
        ret = GmcSetVertexProperty(g_stmt_sync, "vsys_id", GMC_DATATYPE_UINT32, &vsys_id, sizeof(vsys_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);

        if (ret == GMERR_RECORD_COUNT_LIMIT_EXCEEDED) {
            AW_FUN_Log(LOG_STEP, "insert = %d\n", i);
            break;
        }
#if defined ENV_RTOSV2X
        else if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            usleep(10000);
            AW_FUN_Log(LOG_STEP, "insert\n");
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(1, affectRows);
            i++;
        }
    }
#else
        else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(1, affectRows);
            i++;
        }
    }
#endif
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < i / 2; j++) {

        uint32_t vsys_id = j;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &vsys_id, sizeof(vsys_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync, labelName_pk);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
#if defined ENV_RTOSV2X
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            usleep(50000);
            AW_FUN_Log(LOG_STEP, "delete = %d\n", j);
            j--;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(1, affectRows);
        }
#else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
#endif
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vsys_id = i / 2 - 1;
    ret = GmcSetVertexProperty(g_stmt_sync, "vsys_id", GMC_DATATYPE_UINT32, &vsys_id, sizeof(vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end");
}
/*****************************************************************************
 * Description  : 057消费过载流控    1.创建多张表，都创建订阅关系
                                    2.多线程进行DML操作，触发流控
                                    3.订阅消息不能及时收到，订阅通道满
 * Input        : None
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(reliability_scene_ddl, reliability_scene_ddl_057)
{
    // 修改流控相关配置项
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"clientServerFlowControl=1\" \"subscribeFlowControl=1\" "
           "\"overloadThreshold=cpu:70,80,80,85,85,90;dynamicMemory:70,80,80,85,85,90;shareMemory:70,80,80,85,85,"
           "90;subscribeQueue:8,10,12,15,17,20\"");
    GmcConnT *g_conn_sync = NULL, *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sync = NULL, *g_stmt_sub = NULL;
    void *g_label = NULL;
    char *g_schema = NULL;
    char labelName[] = "FlowControlLabel";
    char labelName_pk[] = "FlowControlLabel_PK";
    const char *g_subConnName = "subConnName";
    system("sh $TEST_HOME/tools/start.sh");
    int ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    readJanssonFile("schema_file_ddl/FlowControlLabel.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schema_file_ddl/FlowControlLabel_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    FlowCtrCallDataT para = {0};
    para.wait = true;
    para.waitTime = 2;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback, &para);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "FlowControlLabel", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t i = 0;
    uint32_t g_data_num = 500000;
    do {
        uint32_t value = i;
        TestGmcSetVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        i++;
    } while (ret == GMERR_OK && (i < g_data_num));
    AW_MACRO_EXPECT_EQ_INT(GMERR_COMMON_STREAM_OVERLOAD, ret);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_COMMON_STREAM_OVERLOAD, ret);
    free(g_sub_info);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清理环境
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    // 还原配置
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}
