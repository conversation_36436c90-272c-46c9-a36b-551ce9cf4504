/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include<malloc.h>

#include "gtest/gtest.h"
#include "reliability_scene_dml.h"
#include "../try.h"

const char *resPoolTestName = "resource_pool_test";
const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
#define  MEGABYTE (1024 * 1024 * 1024)

class reliability_scene_dml : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void reliability_scene_dml::SetUpTestCase()
{
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    memset(&asyncData, 0, sizeof(AsyncUserDataT));
}

void reliability_scene_dml::TearDownTestCase()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void reliability_scene_dml::SetUp()
{
    int ret = 0;
    snprintf(view_command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    AW_FUN_Log(LOG_INFO, "%s\n", view_command);
}

void reliability_scene_dml::TearDown()
{
}

// 系统CPU过载
TEST_F(reliability_scene_dml, reliability_scene_dml_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("/root/CFE_Tool/cfe/cfe \"inject rCPU_Overloadap (cpuid1, cpuid2) values(0, 7)\"");
    int ret = executeCommand(view_command, "CONN_STATUS", "CONN_STATUS_NORMAL");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("/root/CFE_Tool/cfe/cfe \"clean rCPU_Overloadap\"");
}

// 时钟向前跳变
TEST_F(reliability_scene_dml, reliability_scene_dml_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, TimeDumpForwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=+ and OFFSET=120\"");
    }
}

// 时钟向后跳变
TEST_F(reliability_scene_dml, reliability_scene_dml_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, TimeDumpBackwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=- and OFFSET=120\"");
    }
}

// 客户端进程退出
TEST_F(reliability_scene_dml, reliability_scene_dml_01_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 2);

    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, KILLThread_01_009, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 1);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// main
TEST_F(reliability_scene_dml, reliability_scene_dml_01_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 2);

    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, KILLThread_01_009, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 1);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
}
// coredump信号
TEST_F(reliability_scene_dml, reliability_scene_dml_01_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 2);

    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, KILLThread_01_009, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 1);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(g_schema);
}
// terminate信号
TEST_F(reliability_scene_dml, reliability_scene_dml_01_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 2);

    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, KILLThread_01_009, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    Test_DML_acl(g_stmt_sync, "acl_hpp_stat", 1);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(g_schema);
}
// ignore信号
TEST_F(reliability_scene_dml, reliability_scene_dml_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, KILLThread_013, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    Test_dml_acl(g_stmt_sync, labelName, 1);
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(g_schema);
}
// 超大规格表
TEST_F(reliability_scene_dml, reliability_scene_dml_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表, 1024个field
    ret = TryCreateVertexLabel(g_stmt_sync, "schema_file_dml/fields_1024_schema.gmjson",
                                NULL, "fields_1024_schema");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表, 1023个field
    const char labelName[128] = "fields_1023_schema";
    ret = TryCreateVertexLabel(g_stmt_sync, "schema_file_dml/fields_1023_schema.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    AW_FUN_Log(LOG_INFO, "Insert Start.\n");
    uint32_t times = 100;
    uint32_t i = 0;
    uint32_t value = 0;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据i
        value = i;

        for (int j = 0; j < 1023; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, 6, "F%04d", j);
            ret = GmcSetVertexProperty(g_stmt_sync, (const char *)fieldName, GMC_DATATYPE_UINT32, &value,
                sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt_sync, 1, i);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t update_value = 100;
    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = i;
        ret = GmcSetIndexKeyName(g_stmt_sync, "key_1023");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F0001", GMC_DATATYPE_UINT32, &update_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1, g_affectRows);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert vertex
    for (i = 0; i < times/2; i++) {
        // 写数据
        value = i;
        ret = GmcSetIndexKeyName(g_stmt_sync, "key_1023");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 表的个数超系统限制
TEST_F(reliability_scene_dml, reliability_scene_dml_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 建表, 1024个field
    ret = TryCreateVertexLabel(g_stmt_sync, "schema_file_dml/fields_1024_schema.gmjson",
                                NULL, "fields_1024_schema");
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表, 1023个field
    const char labelName[128] = "fields_1023_schema";
    ret = TryCreateVertexLabel(g_stmt_sync, "schema_file_dml/fields_1023_schema.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    AW_FUN_Log(LOG_INFO, "Insert Start.\n");
    uint32_t times = 100;
    uint32_t i = 0;
    uint32_t value = 0;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = i;

        for (int j = 0; j < 1023; j++) {
            char fieldName[10] = {0};
            snprintf(fieldName, 6, "F%04d", j);
            ret = GmcSetVertexProperty(g_stmt_sync, (const char *)fieldName, GMC_DATATYPE_UINT32, &value,
                sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestGmcGetStmtAttr(g_stmt_sync, 1, i);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t update_value = 100;
    // insert vertex
    for (i = 0; i < times; i++) {
        // 写数据
        value = i;
        ret = GmcSetIndexKeyName(g_stmt_sync, "key_1023");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_sync, "F0001", GMC_DATATYPE_UINT32, &update_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt_sync, 1, i);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert vertex
    for (i = 0; i < times/2; i++) {
        // 写数据
        value = i;
        ret = GmcSetIndexKeyName(g_stmt_sync, "key_1023");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcGetStmtAttr(g_stmt_sync, 1, i);
    }
    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 超大规格数据写
TEST_F(reliability_scene_dml, reliability_scene_dml_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i =0;
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%u", i);
        // 先尝试删表
        char testTlabelName[32] = "";
        sprintf(testTlabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt_sync, testTlabelName);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            cout << endl << __FILE__ << __LINE__ << "ret:" << ret << endl;
        }
        // 建表
        ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJson, NULL);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, "testT0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertex_acl(g_stmt_sync, "acl_hpp_stat");
    ret = GmcDropVertexLabel(g_stmt_sync, "acl_hpp_stat");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    i = 0;
    while (!ret) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelName);
        i++;
    }
}

// 异步消息队列写满
TEST_F(reliability_scene_dml, reliability_scene_dml_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步和异步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    TestGmcInsertVertex_acl_async(g_stmt_async, labelName, 2, false);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 推送消息队列写满
TEST_F(reliability_scene_dml, reliability_scene_dml_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "FlowControlLabel";
    // 创建连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // create label
    ret = TryCreateVertexLabel(g_stmt_sync, "./schema_file_dml/FlowControlLabel.gmjson", NULL, "FlowControlLabel");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schema_file_dml/FlowControlLabel_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);
    // 不消费订阅消息
    gIsSnCallbackWait = true;
    int i = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!ret) {
        TestGmcSetVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        i++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
    AW_FUN_Log(LOG_INFO, "insert success :%d\n", i);
    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新消费订阅消息
    gIsSnCallbackWait = false;
    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, (i - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 40000; i < 50000; i++) {
        TestGmcSetVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(g_conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 事务锁资源耗尽
TEST_F(reliability_scene_dml, reliability_scene_dml_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i = 0;
    int tableNum = 0;
    const char *labelName = "acl_hpp_stat";
    char vertexLabelJson[1024];
    char vertexLabelName[32] = "testT1";
    char g_label_config[] = "{\"max_record_count\":10000000, \"isFastReadUncommitted\":0}";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            tableNum, tableNum);
        // 先尝试删表
        char testTlabelName[32] = "";
        sprintf(testTlabelName, "testT%u", tableNum);
        ret = GmcDropVertexLabel(g_stmt_sync, testTlabelName);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            cout << endl << __FILE__ << __LINE__ << "ret:" << ret << endl;
        }
        // create vertex
        ret = GmcCreateVertexLabel(g_stmt_sync, vertexLabelJson, g_label_config);
        tableNum++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    AW_FUN_Log(LOG_INFO, "tableNum : %d", tableNum);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn_sync, &config);
    ASSERT_EQ(GMERR_OK, ret);
    int data_num = 10;
    for (i = 0; i < (tableNum - 1); i++) {
        sprintf(vertexLabelName, "testT%u", i);
        for (int j = 0; j < data_num; j++) {
            ret = TestGmcSetVertexProperty_1024(g_stmt_sync, j, vertexLabelName);
            if (ret == GMERR_OK) {
                int affectRows = 0;
                ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, affectRows);
        
            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                ret = GmcTransRollBack(g_conn_sync);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "label_index : %d, j: %d, ret : %d\n", i, j, ret);
            }
        }
    }
    // MS事务commit
    ret = GmcTransCommit(g_conn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcSetVertexProperty_1024(g_stmt_sync, 11, "testT0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < (tableNum - 1); i++) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt_sync, vertexLabelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 资源池耗尽
TEST_F(reliability_scene_dml, reliability_scene_dml_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "bfd_sess_index";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/enp_l3app/bfd_sess_index.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 先尝试解绑、删除资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, labelName);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
        cout << endl << __FILE__ << __LINE__ << "ret:" << ret << endl;
    }
    if (ret == GMERR_OK) {
        ret = GmcDestroyResPool(g_stmt_sync, resPoolTestName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 建资源池
    ret = GmcCreateResPool(g_stmt_sync, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolTestName, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    ret = TestGmcInsertVertex_resource(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmcmd -c delete from %s where %s", ToolPath, labelName, "my_discr\\>\\=0");
    AW_FUN_Log(LOG_INFO, "%s\n", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 表空间满
TEST_F(reliability_scene_dml, reliability_scene_dml_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建10张表
    ret = import_ten_label();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    Ten_label_Subscribe(g_stmt_sync, g_conn_sub);
    Ten_label_UnSubscribe(g_stmt_sync);
    // 将十张表插满
    insert_ten_label(g_stmt_sync);
    AW_FUN_Log(LOG_INFO, "second\n");
    // 删除部分数据
    ret = deleteVertex();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 再次插满数据
    insert_ten_label(g_stmt_sync);
   
    // 释放订阅连接通道
    ret = testGmcDisconnect(g_conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    drop_ten_label(g_stmt_sync);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 客户端epoll线程挂死
TEST_F(reliability_scene_dml, reliability_scene_dml_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";
    // 创建同步和异步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    gIsAsyncCallbackWait = true;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        uint32_t groupid = i;
        uint32_t ruleid = i;
        uint32_t statid = i;
        uint64_t counter = i;
        ret = GmcSetVertexProperty(g_stmt_async, "groupid", GMC_DATATYPE_UINT32, &groupid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "ruleid", GMC_DATATYPE_UINT32, &ruleid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "statid", GMC_DATATYPE_UINT32, &statid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "counter", GMC_DATATYPE_UINT64, &counter, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = InsertVertexCallbackLay;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&asyncData, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    gIsAsyncCallbackWait = false;
    ret = testWaitAsyncRecv(&asyncData, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 100; i < 200; i++) {
        uint32_t groupid = i;
        uint32_t ruleid = i;
        uint32_t statid = i;
        uint64_t counter = i;
        ret = GmcSetVertexProperty(g_stmt_async, "groupid", GMC_DATATYPE_UINT32, &groupid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "ruleid", GMC_DATATYPE_UINT32, &ruleid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "statid", GMC_DATATYPE_UINT32, &statid, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "counter", GMC_DATATYPE_UINT64, &counter, sizeof(uint64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = InsertVertexCallbackLay;
        insertRequestCtx.userData = &asyncData;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&asyncData, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 在线修改系统配置为异常值
TEST_F(reliability_scene_dml, reliability_scene_dml_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";

    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TryCreateVertexLabel(g_stmt_sync, "../../schema_file/r21_ndb/gmjson/acl/acl_hpp_stat.gmjson",
                                NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);
    pthread_t tid[2];
    ret = pthread_create(&tid[0], NULL, InsertVertexThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, OnlineModifyThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(tid[i], NULL);
    }
    ret = GmcDropVertexLabel(g_stmt_sync, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
