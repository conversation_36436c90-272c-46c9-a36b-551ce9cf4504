/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构降级头文件
 Author       : szy
 Modification :
 Date         : 2023/12/1
**************************************************************************** */
#ifndef REL_CHECK_H
#define REL_CHECK_H

#include "VertexLabelDowngrade.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 2048
char g_command[MAX_CMD_SIZE];
const char *g_subConnName = "subConnName";
bool gIsSnCallbackWait = false;
char g_testlabelConfig[] = "{\"max_record_count\":10000000, \"isFastReadUncommitted\":0}";
#define MAX_LABELNAME_LEN 128
GmcConnT *g_conn_sub = NULL;
char *g_schema = NULL;

const char *g_resPoolTestName = "resource_pool_test";
const char *g_resPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";


int TestPartiCheckBegin(GmcStmtT *stmt, const char *labelName, int exptRet)
{
    for (int partition = 0; partition < 16; partition++) {
        int ret = GmcBeginCheck(stmt, labelName, partition);
        AW_MACRO_EXPECT_EQ_INT(exptRet, ret);
        if (ret != exptRet) {
            return ret;
        }
    }

    return 0;
}

// false: 正常老化  true: 刷行版本号
int TestPartiCheckEnd(GmcStmtT *stmt, const char *labelName, bool isAbnormal, int exptRet)
{
    for (int partition = 0; partition < 16; partition++) {
        int ret = GmcEndCheck(stmt, labelName, partition, isAbnormal);
        AW_MACRO_EXPECT_EQ_INT(exptRet, ret);
        if (ret != exptRet) {
            return ret;
        }
    }

    return 0;
}


int TestCheckBegin(GmcStmtT *stmt, const char *labelName, int exptRet)
{
    int ret = GmcBeginCheck(stmt, labelName, 0xff);
    AW_MACRO_EXPECT_EQ_INT(exptRet, ret);
    if (ret != exptRet) {
        return ret;
    }

    return 0;
}


int TestCheckEnd(GmcStmtT *stmt, const char *labelName, int exptRet)
{
    int ret = GmcEndCheck(stmt, labelName, 0xff, false);
    AW_MACRO_EXPECT_EQ_INT(exptRet, ret);
    if (ret != exptRet) {
        return ret;
    }

    return 0;
}


int TestWriteCluPartiTbl(GmcStmtT *stmt, GmcOperationTypeE enType, int64_t start, int64_t end, int *nSuccSum)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, enType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isDefaultValue = false;
    int64_t i = start;
    while (!ret && i < end) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        TestSimpleT1SetPartition(stmt, i);

        ret = GmcExecute(stmt);
        i++;
        if (i % 100000 == 0) {
            printf(">>> insert %s for %d records..\n", g_labelName, i);
        }
    }

    *nSuccSum = i - 1;
    if (ret == 0) {
        *nSuccSum = i;
    }
    return ret;
}


int TestWriteCluTbl(GmcStmtT *stmt, GmcOperationTypeE enType, int64_t start, int64_t end, int *nSuccSum)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, enType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isDefaultValue = false;
    int64_t i = start;
    while (!ret && i < end) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);

        ret = GmcExecute(stmt);
        i++;
        if (i % 200000 == 0) {
            printf(">>> insert %s for %d records..\n", g_labelName, i);
        }
    }

    *nSuccSum = i - 1;
    if (ret == 0) {
        *nSuccSum = i;
    }
    return ret;
}

int TestDeleteCluTbl(GmcStmtT *stmt, int start, int end)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = start; i < end; i++) {
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return 0;
}


int TestWritePartiGeneralT2(GmcStmtT *stmt, GmcOperationTypeE enType, int64_t start, int64_t end, int *nSuccSum)
{
    uint8_t ipValue = 0;
    bool isDefault = true;
    int32_t T1Num = 3;
    int32_t T2Num = 3;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    wrFixed[15] = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    int ret = 0;
    int64_t i = start;
    while (!ret && i < end) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName3, enType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        RETURN_IFERR(ret);

        GmcNodeT *root, *T1, *T2, *R1;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点公共属性
        TestGeneralT1SetPk(root, i);
        TestGeneralSetCommonPropertyRoot(root, i, wrFixed, stringValue, isDefault);
        TestGeneralT1SetPartition(root, i);

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int32_t k = 0; k < T1Num; k++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, k);

            // 插入vectoryT2
            ret = GmcNodeGetChild(T1, "T2", &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int32_t m = 0; m < T2Num; m++) {
                ret = GmcNodeAppendElement(T2, &T2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, m);
            }
        }

        ret = GmcExecute(stmt);
        i++;
        if (i % 100000 == 0) {
            printf(">>> insert %s for %d records..\n", g_labelName3, i);
        }
    }

    *nSuccSum = i - 1;
    if (ret == 0) {
        *nSuccSum = i;
    }
    return ret;
}


int TestWriteGeneralT2(GmcStmtT *stmt, GmcOperationTypeE enType, int64_t start, int64_t end, int *nSuccSum)
{
    uint8_t ipValue = 0;
    bool isDefault = true;
    int32_t T1Num = 3;
    int32_t T2Num = 3;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    wrFixed[15] = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    int ret = 0;
    int64_t i = start;
    while (!ret && i < end) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName3, enType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        RETURN_IFERR(ret);

        GmcNodeT *root, *T1, *T2, *R1;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点公共属性
        TestGeneralT1SetPk(root, i);
        TestGeneralSetCommonPropertyRoot(root, i, wrFixed, stringValue, isDefault);

        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int32_t k = 0; k < T1Num; k++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, k);

            // 插入vectoryT2
            ret = GmcNodeGetChild(T1, "T2", &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int32_t m = 0; m < T2Num; m++) {
                ret = GmcNodeAppendElement(T2, &T2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, m);
            }
        }

        ret = GmcExecute(stmt);
        i++;
        if (i % 100000 == 0) {
            printf(">>> insert %s for %d records..\n", g_labelName3, i);
        }
    }

    *nSuccSum = i - 1;
    if (ret == 0) {
        *nSuccSum = i;
    }
    return ret;
}

int TestDeleteGeneralT2(GmcStmtT *stmt, int start, int end)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, 0, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = start; i < end; i++) {
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return 0;
}


typedef struct TagThreCfg {
    int nThreId;
    int64_t startVal;
    int64_t count;
    const char* labelName;
} GtThreCfg;

bool g_ThrFlag = true;

void *ThreadProcessRelAged(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">>> thread: %d start process: relAgedSub.\n", tArg->nThreId);
    while (g_ThrFlag) {
        system("kill -9 `pidof relAgedSub`");
        // 取消进程里的订阅
        const char *tSubName = "subVertexLabel";
        (void)GmcUnSubscribe(stmt, tSubName);
        
        system("./relAgedSub &");
        sleep(5);
        system("kill -9 `pidof relAgedSub`");
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadProcessRelGenAged(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">>> thread: %d start process: relAgedSubGen.\n", tArg->nThreId);
    while (g_ThrFlag) {
        system("kill -9 `pidof relAgedSubGen`");
        // 取消进程里的订阅
        const char *tSubName = "subVertexLabel";
        (void)GmcUnSubscribe(stmt, tSubName);
        
        system("./relAgedSubGen &");
        sleep(3);
        system("kill -9 `pidof relAgedSubGen`");
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadCluTblReplace(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">>> thread: %d start to replace %s.\n", tArg->nThreId, g_labelName);
    while (g_ThrFlag) {
        int nSuccNum = 0;
        ret = TestWriteCluTbl(stmt, GMC_OPERATION_REPLACE, 0, endValue, &nSuccNum);
        if (ret != GMERR_OUT_OF_MEMORY) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        printf(">>> thread: %d replace %d records ret: %d.\n", tArg->nThreId, nSuccNum, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadCluTblReplaceSubFull(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">>> thread: %d start to replace %s.\n", tArg->nThreId, g_labelName);
    while (g_ThrFlag) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isDefaultValue = false;
        int64_t i = startValue;
        while (i < endValue) {
            TestSimpleT1SetPk(stmt, i);
            TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            i++;
            if (i % 200000 == 0) {
                printf(">>> insert %s for %d records..\n", g_labelName, i);
            }
        }

        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, 0, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int i = startValue; i < endValue; i++) {
            int64_t f0Value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(stmt, "primary_key");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        
        printf(">>> thread: %d replace  ret: %d.\n", tArg->nThreId,  ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadCluPartiTblReplace(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        int nSuccNum = 0;
        ret = TestWriteCluPartiTbl(stmt, GMC_OPERATION_REPLACE, 0, endValue, &nSuccNum);
        if (ret != GMERR_OUT_OF_MEMORY) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        printf(">>> thread replace %d records ret: %d.\n", nSuccNum, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadGeneralT2Replace(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        int nSuccNum = 0;
        ret = TestWriteGeneralT2(stmt, GMC_OPERATION_REPLACE, 0, endValue, &nSuccNum);
        if (ret != GMERR_OUT_OF_MEMORY) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        printf(">>> thread replace %d records ret: %d.\n", nSuccNum, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadPartiGeneralT2Replace(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf(">>> thread %d start to replace %s.\n", tArg->nThreId, g_labelName3);

    while (g_ThrFlag) {
        int nSuccNum = 0;
        ret = TestWritePartiGeneralT2(stmt, GMC_OPERATION_REPLACE, 0, 0xffffffff, &nSuccNum);
        if (ret != GMERR_OUT_OF_MEMORY) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        printf(">>> thread %d replace %d records ret: %d.\n", tArg->nThreId, nSuccNum, ret);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadCheck(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">>> thread: %d start to check table: %s .\n", tArg->nThreId, tArg->labelName);
    int nCount = 0;
    int retStart = 0;
    int retEnd = 0;
    while (g_ThrFlag) {
        retStart = GmcBeginCheck(stmt, tArg->labelName, 0xff);
        if (retStart != GMERR_OK) {
            usleep(100);
            continue;
        }
        usleep(100);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retStart);

        if (nCount % 2 == 0) {
            retEnd = GmcEndCheck(stmt, tArg->labelName, 0xff, false);
        } else {
            retEnd = GmcEndCheck(stmt, tArg->labelName, 0xff, true);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retEnd);

        if (retStart != GMERR_OK || retEnd != GMERR_OK) {
            printf(">>> thread check %d time, check_start ret: %d check_end ret: %d .\n",
                nCount, retStart, retEnd);
            usleep(100);
        } else {
            nCount++;
            if (nCount % 1000 == 0) {
                printf(">>> thread check %d time, check_start ret: %d check_end ret: %d .\n",
                    nCount, retStart, retEnd);
            }
        }
    }

    printf(">>> thread: %d check success: %d times.\n", tArg->nThreId, nCount);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadPartiCheck(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t partition = tArg->startVal;
    int64_t endValue = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    printf(">>> thread: %d start to check table: %s .\n", tArg->nThreId, tArg->labelName);
    int nCount = 0;
    int retStart = 0;
    int retEnd = 0;
    while (g_ThrFlag) {
        retStart = GmcBeginCheck(stmt, tArg->labelName, partition);
        if (retStart != GMERR_OK) {
            usleep(100);
            continue;
        }

        usleep(100);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retStart);
        if (nCount % 2 == 0) {
            retEnd = GmcEndCheck(stmt, tArg->labelName, partition, false);
        } else {
            retEnd = GmcEndCheck(stmt, tArg->labelName, partition, true);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, retEnd);

        if (retStart != GMERR_OK || retEnd != GMERR_OK) {
            printf(">>> thread: %d check %d time, check_start ret: %d check_end ret: %d .\n",
                tArg->nThreId, nCount, retStart, retEnd);
            break;
        } else {
            nCount++;
        }
    }

    printf(">>> thread: %d check success: %d times.\n", tArg->nThreId, nCount);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void sn_callback_NULL(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{}

int g_snReplaceSleep = 0;
void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    while (gIsSnCallbackWait) {
        sleep(2);
    }
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_LABELNAME_LEN] = {0};
    unsigned int labelNameLen = MAX_LABELNAME_LEN;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                sleep(g_snReplaceSleep);
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                sleep(g_snReplaceSleep);
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

int g_snCallbackSleep = 0;
void sn_callback_not_cmp_sleep(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_LABELNAME_LEN] = {0};
    unsigned int labelNameLen = MAX_LABELNAME_LEN;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        usleep(g_snCallbackSleep);
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

int g_thrNumSn = 10;
int g_threSnCount = 0;
void *ThreadProcessSn(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int32_t nSnCount = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcConnT *subConn;

    int id = (tArg->nThreId) % g_thrNumSn;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    char subName[32];
    (void)sprintf(subName, "subVertexLabel_%d", tArg->nThreId);
    printf(">>> thread: %d start to sub: %s, subName: %s expect: %d.\n", tArg->nThreId,
        tArg->labelName, (char *)subName, nSnCount);
    char subjson[1024];
    (void)sprintf(subjson, "{\
            \"name\":\"subVertexLabel_%d\",\
            \"label_name\":\"%s\",\
            \"comment\":\"VertexLabel subscription\",\
            \"type\":\"before_commit\",\
            \"events\":\
                [\
                    {\"type\":\"insert\", \"msgTypes\":[\"new object\", \"old object\"]},\
                    {\"type\":\"replace\", \"msgTypes\":[\"new object\", \"old object\"]},\
                    {\"type\":\"update\", \"msgTypes\":[\"new object\", \"old object\"]},\
                    {\"type\":\"age\", \"msgTypes\":[\"new object\", \"old object\"]}\
                ],\
            \"is_path\":false,\
            \"retry\":true,\
            \"is_reliable\":false\
        }", tArg->nThreId, tArg->labelName);

    // 开启订阅 ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*15);
    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    connOptions.connName = subName;
    ret = TestYangGmcConnect(&subConn, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = subjson;
    if (tArg->nThreId == startValue) {
        ret = GmcSubscribe(stmt, &tmp_g_sub_info, subConn, sn_callback_not_cmp, user_data);
    } else {
        ret = GmcSubscribe(stmt, &tmp_g_sub_info, subConn, sn_callback_not_cmp_sleep, user_data);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_threSnCount++;

    sleep(10);
    if (tArg->nThreId != startValue) {
        // 一个订阅线程，不会丢消息，多个可能会丢消息；
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>thread: %d check sn msg, may be lost\n", tArg->nThreId);
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*2);
    }

    // 取消订阅
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    return ((void *)0);
}


void *ThreadProcessSnAged(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int32_t nSnCount = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcConnT *subConn;

    int id = (tArg->nThreId) % g_thrNumSn;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    char subName[32];
    (void)sprintf(subName, "subVertexLabel_%d", tArg->nThreId);
    printf(">>> thread: %d start to sub: %s, subName: %s expect: %d.\n", tArg->nThreId,
        tArg->labelName, (char *)subName, nSnCount);
    char subjson[1024];
    (void)sprintf(subjson, "{\
            \"name\":\"subVertexLabel_%d\",\
            \"label_name\":\"%s\",\
            \"comment\":\"VertexLabel subscription\",\
            \"type\":\"before_commit\",\
            \"events\":\
                [\
                    {\"type\":\"age\", \"msgTypes\":[\"new object\", \"old object\"]}\
                ],\
            \"is_path\":false,\
            \"retry\":true,\
            \"is_reliable\":false\
        }", tArg->nThreId, tArg->labelName);

    // 开启订阅 ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*15);
    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    connOptions.connName = subName;
    ret = TestYangGmcConnect(&subConn, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = subjson;
    ret = GmcSubscribe(stmt, &tmp_g_sub_info, subConn, sn_callback_not_cmp, user_data);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_threSnCount++;

    sleep(10);
    if (tArg->nThreId != startValue) {
        // 一个订阅线程，不会丢消息，多个会丢消息；
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*15);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>thread: %d check sn msg, aged: %d\n", tArg->nThreId, nSnCount);
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>thread: %d get sn msg, aged: %d\n", tArg->nThreId, user_data->agedNum);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    return ((void *)0);
}

void *ThreadProcessSnReplace(void *args)
{
    // replace覆盖写入数据
    GtThreCfg *tArg = (GtThreCfg *)args;
    int64_t startValue = tArg->startVal;
    int32_t nSnCount = tArg->count;

    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcConnT *subConn;

    int id = (tArg->nThreId) % g_thrNumSn;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    int ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    char subName[32];
    (void)sprintf(subName, "subVertexLabel_%d", tArg->nThreId);
    printf(">>> thread: %d start to sub: %s, subName: %s expect: %d.\n", tArg->nThreId,
        tArg->labelName, (char *)subName, nSnCount);
    char subjson[1024];
    (void)sprintf(subjson, "{\
            \"name\":\"subVertexLabel_%d\",\
            \"label_name\":\"%s\",\
            \"comment\":\"VertexLabel subscription\",\
            \"type\":\"before_commit\",\
            \"events\":\
                [\
                    {\"type\":\"replace\", \"msgTypes\":[\"new object\", \"old object\"]}\
                ],\
            \"is_path\":false,\
            \"retry\":true,\
            \"is_reliable\":false\
        }", tArg->nThreId, tArg->labelName);

    // 开启订阅 ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*15);
    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    connOptions.connName = subName;
    ret = TestYangGmcConnect(&subConn, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = subName;
    tmp_g_sub_info.configJson = subjson;
    if (tArg->nThreId == startValue) {
        ret = GmcSubscribe(stmt, &tmp_g_sub_info, subConn, sn_callback_not_cmp, user_data);
    } else {
        ret = GmcSubscribe(stmt, &tmp_g_sub_info, subConn, sn_callback_not_cmp_sleep, user_data);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_threSnCount++;

    sleep(10);
    if (tArg->nThreId != startValue) {
        // 一个订阅线程，不会丢消息，多个会丢消息；
        ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_AGED, nSnCount, RECV_TIMEOUT*15);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>thread: %d check sn msg, aged: %d\n", tArg->nThreId, nSnCount);
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>thread: %d get sn msg, aged: %d\n", tArg->nThreId, user_data->agedNum);
    // 取消订阅
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubDisConnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    return ((void *)0);
}

void *ThreadRcpuOverloadlValue90(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rCPU_Overloadl_value90");
    return NULL;
}

void *ThreadRcpuOverloadlValue100(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rCPU_Overloadl_value");
    return NULL;
}

void *ThreadRcpuOverloadlValue100With30s(void *args)
{
    system("sh ${TEST_HOME}/reliability/reliability_test/reliability_scene_ddl.sh rCPU_Overloadl_value30s");
    return NULL;
}

void *TimeDumpForwardThread(void *arg)
{
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (+,120)\"");
        sleep(1);
    }
    return NULL;
}
void *TimeDumpBackwardThread(void *arg)
{
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"inject rSysClockJump (DIRECTION,OFFSET) values (-,120)\"");
        sleep(1);
    }
    return NULL;
}

void KILLThread009()
{
    sleep(5);
    system("kill -9 `pidof gmserver`");
}

void *KILLThread019(void *arg)
{
    system("kill -19 `pidof gmserver`");
    sleep(5);
    system("kill -18 `pidof gmserver`");
    return NULL;
}

void *MvFileThread(void *arg)
{
    system("mv ./schemaFileUp100/testT99.gmjson ./schemaFileUp100/testT999.gmjson ");
    return NULL;
}

void MvFileBack()
{
    system("mv ./schemaFileUp100/testT999.gmjson ./schemaFileUp100/testT99.gmjson ");
}


void *MvDirThread(void *arg)
{
    system("mv ./schemaFileUp100 ./schemaFileUp10099 ");
    return NULL;
}

void MvDirBack()
{
    system("mv ./schemaFileUp10099 ./schemaFileUp100 ");
}


int ExecuteCommandValue(char *cmd, const char *name, uint32_t *value)
{
    char cmdOutput[128] = {0};
    uint32_t getvalue = 0;
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(cmdOutput, 128, pf)) {
        getvalue = atoi(cmdOutput);
        AW_FUN_Log(LOG_INFO, "name:%s, getvalue: %d\n", name, getvalue);
        memcpy(value, &getvalue, sizeof(getvalue));
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

void GetClusteredStat(const char *name = NULL, uint32_t *value = NULL)
{
    int ret = 0;
    char command[MAX_CMD_SIZE];

    if (name == NULL) {
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s", g_toolPath, viewName);
        printf("%s\n", command);
        system(command);
    } else {
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        snprintf(
            command, MAX_CMD_SIZE, "%s/gmsysview -q %s | grep %s |awk '{print $2}'", g_toolPath, viewName, name);
        printf("%s\n", command);

        if ((name != NULL) && (value != NULL)) {
            ret = ExecuteCommandValue(command, name, value);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    memset(command, 0, sizeof(command));
}

void *OnlineModifyThread(void *arg)
{
    // 修改配置项compatibleV3为-1
    char cfgName[50] = "compatibleV3";
    char cfgCommand[1024];
    int modifyDvalue = -1;
    int res = snprintf(cfgCommand, 1024, "%s/gmadmin -s %s -cfgName %s -cfgVal %d\n", g_toolPath, g_connServer,
        cfgName, modifyDvalue);
    if (res < 0) {
        printf("res = snprintf fail\n");
    }
    printf("%s\n", cfgCommand);
    int ret = executeCommand(cfgCommand, "Unable to set config",
        "Unable to execute gmadmin corresponding function");
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

int TestGmcSetVertexProperty_1024(GmcStmtT *stmt, uint8_t i, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &f0Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    float f1Value = 1 * i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_FLOAT, &f1Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f2Value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_DOUBLE, &f2Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    char f3Value[8] = "string";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, f3Value, (strlen(f3Value)));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

int TestGmcDelVertexProperty_1024(GmcStmtT *stmt, uint8_t i, const char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f0Value = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &f0Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "T10_PK");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    return ret;
}

// 查询现有非预留连接数
int GetConnNum(uint32_t *connNum)
{
#if defined RUN_DATACOM_HPE
    printf("[executeCommand] popen can not run in hpe env\n");
    return 0;
#else
    if (connNum == NULL) {
        return FAILED;
    }
    int ret = 0;
    uint32_t total_num = 0;
    char command[1024];
    *connNum = total_num;

    char const *viewName1 = "V\\$DRT_CONN_STAT";
    ret = snprintf(command, 1024, "%s/gmsysview -q %s -f  CONN_TYPE=CONN_TYPE_NORMAL", g_toolPath, viewName1);
    EXPECT_LT(0, ret);
    if (ret <= 0) {
        printf("snprintf, ret:%d\n", ret);
        return FAILED;
    }

    char buffer[1024] = {0};
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        printf("popen(%s) error.\n", command);
        return FAILED;
    }

    while (fgets(buffer, 500, pf) != NULL) {
        if (strstr(buffer, "CONN_ID")) {
            total_num++;
        }
    }
    ret = pclose(pf);
    if (ret == -1) {
        printf("pclose failed, errno = %d.", errno);
        return FAILED;
    }
    pf = NULL;

    // 减去IOT环境视图查询的1个连接
    if (total_num <= 0) {
        printf("[DRT_CONN_STAT]something wrong with total conn num = %d.", total_num);
        return FAILED;
    }
    *connNum = (uint32_t)(total_num - 1);

    printf("[DRT_CONN_STAT]connect num: %u\n", *connNum);
    return 0;
#endif
}

// 导入白名单
void ImportAllowList(const char *path, const char *expect)
{
    int ret;
    char gmrule[500] = {0};
    ret = snprintf(gmrule, 500, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, path, g_connServer);
    printf("%s\n", gmrule);
    ret = executeCommand(gmrule, expect);
    EXPECT_EQ(GMERR_OK, ret);
}

// 导入权限
void ImportPolicy(const char *path, const char *expect)
{
    int ret;
    char gmrule[500] = {0};
    ret = snprintf(gmrule, 500, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, path, g_connServer);
    printf("%s\n", gmrule);
    ret = executeCommand(gmrule, expect);
    EXPECT_EQ(GMERR_OK, ret);
}

int TestGmimportVertex(const char *filePath, char *expectValue)
{
    int ret = 0;
    char cmd[512] = {0};
    (void)snprintf(cmd, 512, "%s/gmimport -c imp_schema -f %s -ns %s", g_toolPath, filePath, g_testNameSpace);

    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int TestGimportGmjson()
{
    int32_t ret = 0;
#if defined ENV_RTOSV2
    return ret;
#endif
    char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/fib/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/fib6/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/hsec/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/ifm/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/ipbase/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("../../../sdv/schema_file/r21_ndb/gmjson/vlan/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

void ReadGmjson(const char *schemaPath)
{
    char *testSchema = NULL;
    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    if (testSchema) {
        free(testSchema);
    }
}


int TestDownGradeAll()
{
    char *expectValue2 = (char *)"degrade successfully";

    // 降级
    char *tmpTable1 = (char *)"specialLabel";
    int ret = TestDownGradeVertexLabel(tmpTable1, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"generalLabel";
    ret = TestDownGradeVertexLabel(tmpTable2, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = TestDownGradeVertexLabel(tmpTable3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable4 = (char *)"simpleLabelA";
    ret = TestDownGradeVertexLabel(tmpTable4, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable5 = (char *)"specialLabelA";
    ret = TestDownGradeVertexLabel(tmpTable5, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable6 = (char *)"generalLabelA";
    ret = TestDownGradeVertexLabel(tmpTable6, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


int TestDownGradeAllAbnormal()
{
    // 降级
    char *tmpTable1 = (char *)"specialLabel";
    int ret = TestDownGradeVertexLabel(tmpTable1, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"generalLabel";
    ret = TestDownGradeVertexLabel(tmpTable2, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = TestDownGradeVertexLabel(tmpTable3, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable4 = (char *)"simpleLabelA";
    ret = TestDownGradeVertexLabel(tmpTable4, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable5 = (char *)"specialLabelA";
    ret = TestDownGradeVertexLabel(tmpTable5, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable6 = (char *)"generalLabelA";
    ret = TestDownGradeVertexLabel(tmpTable6, 0, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


int TestDownGradeAllAsync()
{
    char *expectValue2 = (char *)"degrade successfully";

    // 降级
    char *tmpTable1 = (char *)"specialLabel";
    int ret = TestDownGradeVertexLabel(tmpTable1, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"generalLabel";
    ret = TestDownGradeVertexLabel(tmpTable2, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = TestDownGradeVertexLabel(tmpTable3, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable4 = (char *)"simpleLabelA";
    ret = TestDownGradeVertexLabel(tmpTable4, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable5 = (char *)"specialLabelA";
    ret = TestDownGradeVertexLabel(tmpTable5, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable6 = (char *)"generalLabelA";
    ret = TestDownGradeVertexLabel(tmpTable6, 0, expectValue2, (char *)"async");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


int TestDownGradeAllFailed()
{
    char *expectValue2 = (char *)"degrade unsuccessfully";

    // 降级
    char *tmpTable1 = (char *)"specialLabel";
    int ret = TestDownGradeVertexLabel(tmpTable1, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"generalLabel";
    ret = TestDownGradeVertexLabel(tmpTable2, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = TestDownGradeVertexLabel(tmpTable3, 0, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}


int TestUpdateVertexLabelAbnormal(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = g_uWay,
                                  char *nsName = g_testNameSpace)
{
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        char *schema = NULL;
        readJanssonFile(schemaPath, &schema);
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay,
            nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}


int TestGmcInsertVertex_resource(GmcStmtT *stmt)
{
    int ret;
    AsyncUserDataT data = {0};
    int returnVal = 0;
    uint32_t i = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, "bfd_sess_index", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    while (!returnVal) {
        uint32_t my_discr = i;
        uint32_t reserved = i;
        ret = GmcSetVertexProperty(stmt, "my_discr", GMC_DATATYPE_UINT32, &my_discr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT32, &reserved, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t respoolId = 0xFFFF;
        uint64_t count = 10;
        uint64_t startIndex = 0xFFFFFFFF;
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "sess_index", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        returnVal = data.status;
        i++;
    }
    printf("full insert= %d\n", i);
    return returnVal;
}


int TestGmcDeleteVertex_resource(GmcStmtT *stmt)
{
    int ret;
    AsyncUserDataT data = {0};
    int returnVal;
    uint32_t i = 20;
    ret = testGmcPrepareStmtByLabelName(stmt, "bfd_sess_index", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    while (i >= 0) {
        uint32_t my_discr = i;
        uint32_t reserved = i;
        ret = GmcSetVertexProperty(stmt, "my_discr", GMC_DATATYPE_UINT32, &my_discr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT32, &reserved, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t respoolId = 0xFFFF;
        uint64_t count = 10;
        uint64_t startIndex = 0xFFFFFFFF;
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "sess_index", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        if (ret == GMERR_COMMON_STREAM_OVERLOAD) {
            sleep(1);
            i++;
        } else {
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
            returnVal = data.status;
            EXPECT_EQ(GMERR_OK, returnVal);
        }
        i--;
        if (i == -1) {
            break;
        }
    }
    return returnVal;
}
typedef struct TagSimpleThreCfg {
    uint32_t schemaVersion;
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    bool isDefaultValue;
    const char* labelName;
} GtSimpleThreCfg;


void *ThreadSimpleWrite(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = tArg->isDefaultValue;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (g_ThrFlag) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1SetPk(stmt, i);
            TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
            if (schemaVersion == 0) {
                TestSimpleT2NewFieldSetFailed(stmt, i);
                TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewFieldSetOk(stmt, i);
                TestSimpleT3FullFieldUpdateSetOk(stmt, i);
            }
            ret = GmcExecute(stmt);
            // 共享内存满场景，可能内存满错误；
            if (ret != GMERR_OK && ret != GMERR_SUB_PUSH_QUEUE_FULL && ret != GMERR_OUT_OF_MEMORY) {
                AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER)
                return ((void *)0);
            } else if (ret != GMERR_SUB_PUSH_QUEUE_FULL && ret != GMERR_OUT_OF_MEMORY) {
                AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK)
            }
        }
        usleep(800);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


char* g_labelNameA = (char *)"simpleLabelA";

void TestSimpleWrite(GmcStmtT *stmt, const char* labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                     bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
 
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_REPLACE);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
        return;
    }

    for (int i = startPkVal; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK  && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
            return;
        }
    }
}


void TestSimpleWriteAsync(GmcStmtT *stmt, const char* labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                          bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
 
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_REPLACE);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
        return;
    }

    AsyncUserDataT data = {0};
    for (int i = startPkVal; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i);
        }
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            if (i % 10 == 0) {
                printf("GMERR_CONNECTION_SEND_BUFFER_FULL\n");
            }
            while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                ret = GmcExecuteAsync(stmt, &insertRequestCtx);  // 消息通道溢出，重发
            }
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
}


void TestSimpleDelete(GmcStmtT *stmt, const char* labelName, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                      bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
 
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
        return;
    }

    for (int i = startPkVal; i < endValue; i++) {
        int64_t f0Value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
            return;
        }

        ret = GmcSetIndexKeyName(stmt, "primary_key");
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
            return;
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK  && ret != GMERR_SUB_PUSH_QUEUE_FULL) {
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
            return;
        }
    }
}

int TestSimpleScan(GmcStmtT *stmt, const char* labelName, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion,
                   bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    bool fieldIsNull[8] = {0};
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d", labelName, schemaVersion);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = startPkVal; i < endValue; i++) {
        TestSimpleT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, i);
        if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
            TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i + updateValue, fieldIsNull[2]);
        }

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        return ret;
    }
    return 0;
}

void *ThreadSimpleRead(void *args)
{
    // read
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = tArg->isDefaultValue;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int nNum = 0;
    bool isFinish = false;
    while (g_ThrFlag) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tArg->labelName, schemaVersion, GMC_OPERATION_SCAN);
        if (nNum % 20 == 0) {
            AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d", tArg->labelName, schemaVersion);
        }
        nNum++;
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        if (ret != GMERR_OK) {
            return ((void *)0);
        }

        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1PkIndexSet(stmt, i);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

            TestSimpleT1UpdateGetOldPropertyByName(stmt, i, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 2) {
                TestSimpleT2NewVersionGetNewValue(stmt, i);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewVersionGetNewValue(stmt, i);
                TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i);
            }

            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        }
        usleep(800);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

// 基础表操作线程
void *ThreadGeneralWrite(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};

    while (g_ThrFlag) {
        ret = TestGeneralT2NewOldVersionWrite(stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
        EXPECT_EQ(0, ret);
        if (ret != 0) {
            return ((void *)0);
        }
        usleep(800);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadGeneralRead(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    int cNum = 1;
    while (g_ThrFlag) {
        TestGeneralT2NewOldVersionRead(stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);
        usleep(800);
        cNum++;
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

void *ThreadUpgradeDown(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级 kill -9 gmserver\n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabelAbnormal(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写版本1数据 - 简单表
        uint32_t schemaVersion = 3;
        int32_t startValue = 0;
        uint32_t endValue = 20000;
        GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
        TestSimpleWrite(stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
        TestSimpleWrite(stmt, g_labelNameA, vertexCfg1, schemaVersion, isDefaultValue);

        // 读版本1数据 - 简单表
        GtSimplelabelCfgRead readCfg;
        readCfg.startVal = startValue;
        readCfg.count = endValue;
        TestSimpleScan(stmt, g_labelName, readCfg, 0);
        TestSimpleScan(stmt, g_labelNameA, readCfg, 0);

        printf(">>>>>>>>>>降级 \n");
        ret = TestDownGradeAllAbnormal();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sleep(1);
    }

    return ((void *)0);
}


void *ThreadshcemaFullFieldWrite(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = tArg->isDefaultValue;
    char *tmplabelName = (char *)"fields_1022_schema";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t i = 0;
    uint32_t value = 0;
    uint32_t times = endValue;

    int nNum = 0;
    while (g_ThrFlag) {
        if (nNum % 2 == 0) {
            AW_FUN_Log(LOG_INFO, "thread write labelName = %s schemaVersion = %d", tmplabelName, schemaVersion);
        }
        nNum++;
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmplabelName, schemaVersion, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (i = 0; i < times; i++) {
            // 写数据
            value = i;

            for (int j = 0; j < 1022; j++) {
                char fieldName[10] = {0};
                (void)snprintf(fieldName, 6, "F%04d", j);
                ret = GmcSetVertexProperty(
                    stmt, (const char *)fieldName, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_OK || ret == GMERR_CONNECTION_RESET_BY_PEER);
            if (ret != GMERR_OK) {
                return ((void *)0);
            }
        }

        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmplabelName, schemaVersion, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t update_value = 100;
        // insert vertex
        for (i = 0; i < times; i++) {
            // 写数据
            value = i;
            ret = GmcSetIndexKeyName(stmt, "key_1022");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F0001", GMC_DATATYPE_UINT32, &update_value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_OK || ret == GMERR_CONNECTION_RESET_BY_PEER);
            if (ret != GMERR_OK) {
                return ((void *)0);
            }
        }

        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmplabelName, schemaVersion, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // insert vertex
        for (i = 0; i < times / 2; i++) {
            // 写数据
            value = i;
            ret = GmcSetIndexKeyName(stmt, "key_1022");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_OK || ret == GMERR_CONNECTION_RESET_BY_PEER);
            if (ret != GMERR_OK) {
                return ((void *)0);
            }
        }
        usleep(800);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadshcemaFullFieldRead(void *args)
{
    // read
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = tArg->isDefaultValue;
    char *tmplabelName = (char *)"fields_1022_schema";

    GmcConnT *conn;
    GmcStmtT *stmt;
    uint32_t value = 0;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int nNum = 0;
    bool isFinish = false;
    while (g_ThrFlag) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmplabelName, schemaVersion, GMC_OPERATION_SCAN);
        if (nNum % 5 == 0) {
            AW_FUN_Log(LOG_INFO, "thread write labelName = %s schemaVersion = %d", tmplabelName, schemaVersion);
        }
        nNum++;
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

        for (int i = startValue; i < endValue; i++) {
            value = i;
            ret = GmcSetIndexKeyName(stmt, "key_1022");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        }
        usleep(800);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadFullFieldUpgradeDown(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级 \n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./shcemaFileFullFieldUp/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
        printf(">>>>>>>>>>降级\n");
        char *expectValue2 = (char *)"degrade successfully";

        // 降级
        char *tmpTable1 = (char *)"fields_1022_schema";
        ret = TestDownGradeVertexLabel(tmpTable1, 0, expectValue2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 检查降级
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmpTable1, 2, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_UNDEFINED_TABLE || ret == GMERR_CONNECTION_RESET_BY_PEER);

        sleep(1);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadUpgradeDownAbnormal(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级 -- 预期失败 ThreadUpgradeDownAbnormal\n");
        char *expectValue1 = (char *)"Unsuccess upgrade vertexLabel";
        char *schemaUpFile = (char *)"./shcemaFileFullFieldUp/batchError.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf(">>>>>>>>>>升级完成 ThreadUpgradeDownAbnormal\n");

        // 检查升级
        char *tmpTable1 = (char *)"fields_1023_schema";
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, tmpTable1, 2, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        sleep(1);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadUpgradeDownNormal(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级 ThreadUpgradeDownNormal\n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写版本1数据 - 简单表
        uint32_t schemaVersion = 3;
        int32_t startValue = 0;
        uint32_t endValue = 20000;
        GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
        TestSimpleWrite(stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
        TestSimpleWrite(stmt, g_labelNameA, vertexCfg1, schemaVersion, isDefaultValue);

        // 读版本1数据 - 简单表
        GtSimplelabelCfgRead readCfg;
        readCfg.startVal = startValue;
        readCfg.count = endValue;
        TestSimpleScan(stmt, g_labelName, readCfg, 0);
        TestSimpleScan(stmt, g_labelNameA, readCfg, 0);

        printf(">>>>>>>>>>降级 ThreadUpgradeDownNormal\n");
        ret = TestDownGradeAll();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sleep(1);
    }

    return ((void *)0);
}

void *ThreadUpgradeDownTxAbnormal(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级 写内存满，可能成功可能失败\n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./schemaFileUpTx/batch1.txt";
        ret = TestUpdateVertexLabelAbnormal(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
        printf(">>>>>>>>>>降级\n");
        char *expectValue2 = (char *)"degrade successfully";

        // 降级
        char *tmpTable1 = (char *)"testT0";
        ret = TestDownGradeVertexLabel(tmpTable1, 0, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sleep(2);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}


void *ThreadUpgradeDownRes(void *args)
{
    // replace覆盖写入数据
    GtSimpleThreCfg *tArg = (GtSimpleThreCfg *)args;

    uint32_t schemaVersion = tArg->schemaVersion;
    int32_t startValue = tArg->startVal;
    uint32_t endValue = tArg->count;
    bool isDefaultValue = false;
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";

    GmcConnT *conn;
    GmcStmtT *stmt;

    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (g_ThrFlag) {
        // 升级版本 1
        printf(">>>>>>>>>>升级\n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./schemaFileUpRes/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
        printf(">>>>>>>>>>降级\n");
        char *expectValue2 = (char *)"degrade successfully";

        // 降级
        char *tmpTable1 = (char *)"bfd_sess_index";
        ret = TestDownGradeVertexLabel(tmpTable1, 0, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sleep(1);
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}

#endif
