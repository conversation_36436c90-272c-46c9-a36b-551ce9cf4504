#ifndef _IF_STABILITY_TEST_H_
#define _IF_STABILITY_TEST_H_

#include "longStability.h"

char g_isStabilityLabelName[128] = "if";
char g_isStabilityPkName[128] = "if_pk";
char g_isStabilityLocalKey[128] = "local_pk";
char g_isStabilityLocalhashKey[128] = "ifname_pk";
#define TYPE_FIXED_LEN 6

int if_stability_create_label()
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *test_schema = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("./schemaFile/if_general_complex.gmjson", &test_schema);
    EXPECT_NE((void *)NULL, test_schema);

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_isStabilityLabelName);
    ret = GmcCreateVertexLabel(stmt, test_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        printf("[INFO]Test create label success \n");
    } else {
        testGmcGetLastError(NULL);
    }
    free(test_schema);
    testGmcDisconnect(conn, stmt);
    return ret;
}

void if_stability_set_pk(GmcNodeT *node, uint32_t pkValue)
{
    int ret = 0;
    uint32_t ifindex = pkValue;
    ret = GmcNodeSetPropertyByName(node, (char *)"ifindex", GMC_DATATYPE_UINT32, &ifindex, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}
void if_stability_set_node_root(GmcNodeT *node, uint32_t index, void *fixed_value)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"name", GMC_DATATYPE_FIXED, fixed_value, TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t vrid = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"vrid", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t if_type = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"if_type", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"shutdown", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"linkup", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tbtp", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tb", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tp", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"port_switch", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"forwardType", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"macAddress", GMC_DATATYPE_FIXED, fixed_value, TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t ipv4_mtu = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv4_mtu", GMC_DATATYPE_UINT16, &ipv4_mtu, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"ipv6_mtu", GMC_DATATYPE_UINT16, &ipv4_mtu, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"lagid", GMC_DATATYPE_UINT32, &if_type, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_set_node_ifm(GmcNodeT *node, uint32_t index, void *fixed_value)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    uint32_t value6 = 7 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"simple_name", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"description", GMC_DATATYPE_FIXED, fixed_value, TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"is_configure", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"main_ifindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_max_num", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"sub_curr_num", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"error_down", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"statistic", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsys_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"zone_id", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_up_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"last_down_time", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_set_node_dev(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"dev_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"chassis_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"slot_id", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"card_id", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"unit_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"port_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_set_node_l2(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"trunk_id", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vlan_id", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"l2_portindex", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"vsi", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"tunnel_id", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_set_node_port(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    uint32_t value2 = 3 * index;
    uint32_t value3 = 4 * index;
    uint32_t value4 = 5 * index;
    uint32_t value5 = 6 * index;
    ret = GmcNodeSetPropertyByName(node, (char *)"speed", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"duplex", GMC_DATATYPE_UINT32, &value2, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"flow_control", GMC_DATATYPE_UINT32, &value3, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"jumbo", GMC_DATATYPE_UINT32, &value4, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"baud", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"rmon", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"phy_link", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"if_mib", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"on_board", GMC_DATATYPE_UINT32, &value5, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_set_node_T1_V(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t value = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"V1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"V2", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_get_node(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **ifmN, GmcNodeT **devN, GmcNodeT **l2N,
    GmcNodeT **portN, GmcNodeT **T1_VN)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL, *node3 = NULL, *node4 = NULL, *node5 = NULL;
    *root = NULL;
    *ifmN = NULL;
    *devN = NULL;
    *l2N = NULL;
    *portN = NULL;
    *T1_VN = NULL;
    //获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "ifm", &node1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "dev", &node2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "l2", &node3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "port", &node4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1_V", &node5);
    EXPECT_EQ(GMERR_OK, ret);
    *root = Root;
    *ifmN = node1;
    *devN = node2;
    *l2N = node3;
    *portN = node4;
    *T1_VN = node5;
}

void if_stability_insert(GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int32_t ret = 0;
    uint32_t vector_num = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = start_pk; i < end_pk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_set_pk(root, i);
        if_stability_set_node_root(root, i, (uint8_t *)"fixed");
        if_stability_set_node_ifm(ifmN, i, (uint8_t *)"fixed");
        if_stability_set_node_dev(devN, i);
        if_stability_set_node_l2(l2N, i);
        if_stability_set_node_port(portN, i);
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1_VN, &T1_VN);
            EXPECT_EQ(GMERR_OK, ret);
            if_stability_set_node_T1_V(T1_VN, j);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK && ret != GMERR_PRIMARY_KEY_VIOLATION && ret != GMERR_UNIQUE_VIOLATION &&
            ret != GMERR_OUT_OF_MEMORY) {
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
}

void if_stability_set_pk_index(GmcStmtT *stmt, uint32_t pkValue)
{
    int ret = 0;
    uint32_t pk_Value = pkValue;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk_Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_isStabilityPkName);
    EXPECT_EQ(GMERR_OK, ret);
}

void if_stability_update_set_property(GmcNodeT *node, uint32_t index, void *fixed_value)
{
    int ret = 0;
    ret = GmcNodeSetPropertyByName(node, (char *)"name", GMC_DATATYPE_FIXED, fixed_value, TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_pk_update(GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int32_t ret = 0;
    uint32_t vector_num = 3;
    uint8_t *fixedUpdateValue = (uint8_t *)"field";
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = start_pk; i < end_pk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, i);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_update_set_property(root, i, fixedUpdateValue);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void if_stability_pk_delete(GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int32_t ret = 0;
    uint32_t vector_num = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    for (int32_t i = start_pk; i < end_pk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_DELETE);
        ASSERT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, i);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void *recycle_write_remove_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    while (1) {
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        srand((uint32_t)time(0));
        int index = rand() % (GRAPH_WRITE_NUM * 2);  //随机产生一个2000的区间进行写，更新，删除
        if_stability_insert(stmt, index, index + OP_NUM);
        sleep(3);

        // update
        if_stability_pk_update(stmt, index, index + OP_NUM);
        sleep(3);

        // remove
        if_stability_pk_delete(stmt, index, index + OP_NUM);
        testGmcDisconnect(conn, stmt);
        sleep(ST);
        // printf("[graph info]:recycle_write_remove_if_general_complex \n");
    }
}

void read_if_stability_by_get_ifm(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = false;
    char fixed_value[TYPE_FIXED_LEN + 1] = {0};
    uint32_t f2_value = 0;
    uint32_t f3_value = 0;
    uint32_t f4_value = 0;
    uint32_t f8_value = 0;
    uint32_t f9_value = 0;
    ret = GmcNodeGetPropertyByName(node, "simple_name", &f2_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "description", fixed_value, TYPE_FIXED_LEN, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "is_configure", &f3_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "main_ifindex", &f4_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "sub_max_num", &f9_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "sub_curr_num", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "error_down", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "statistic", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "vsys_id", &f9_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "zone_id", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "last_up_time", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "last_down_time", &f8_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
}

void read_if_stability_by_get_root(GmcNodeT *node)
{
    int ret = 0;
    bool isNull = false;
    char fixed_value[TYPE_FIXED_LEN + 1] = {0};
    uint32_t f2_value = 0;
    ret = GmcNodeGetPropertyByName(node, "vrid", &f2_value, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(node, "name", fixed_value, TYPE_FIXED_LEN, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
}

void *localkey_scan_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    unsigned int arrLen = 2;
    uint32_t l_val_scan = 0;
    uint32_t l_val_scan1 = 0;
    uint32_t r_val_scan = 0;
    uint32_t r_val_scan1 = 0;
    GmcPropValueT *leftKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    GmcPropValueT *rightKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    GmcRangeItemT items_sc[arrLen];
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));  //随机产生一个值进行区间扫描
        int loop = rand() % (GRAPH_WRITE_NUM * 2);
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        l_val_scan = loop + 666;
        l_val_scan1 = loop;
        r_val_scan = loop + 666 + OP_NUM;
        r_val_scan1 = loop + OP_NUM;
        // printf("[INFO] l_val_scan:%u  r_val_scan:%u\n",l_val_scan,r_val_scan);
        //设置左值
        leftKeyProps_scan[0].type = GMC_DATATYPE_UINT32;
        leftKeyProps_scan[0].value = &l_val_scan;
        leftKeyProps_scan[0].size = sizeof(uint32_t);
        leftKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
        leftKeyProps_scan[1].value = &l_val_scan1;
        leftKeyProps_scan[1].size = sizeof(uint32_t);
        //设置右值
        rightKeyProps_scan[0].type = GMC_DATATYPE_UINT32;
        rightKeyProps_scan[0].value = &r_val_scan;
        rightKeyProps_scan[0].size = sizeof(uint32_t);
        rightKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
        rightKeyProps_scan[1].value = &r_val_scan1;
        rightKeyProps_scan[1].size = sizeof(uint32_t);

        items_sc[0].lValue = &leftKeyProps_scan[0];
        items_sc[0].rValue = &rightKeyProps_scan[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_OPEN;
        items_sc[0].order = GMC_ORDER_DESC;
        items_sc[1].lValue = &leftKeyProps_scan[1];
        items_sc[1].rValue = &rightKeyProps_scan[1];
        items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].rFlag = GMC_COMPARE_RANGE_OPEN;
        items_sc[1].order = GMC_ORDER_DESC;

        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, g_isStabilityLocalKey);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        while (!isFinish) {
            if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
            read_if_stability_by_get_ifm(ifmN);
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
        }
        GmcFreeIndexKey(stmt);
        sleep(ST);
        // printf("[graph info]:localkey_scan_if_general_complex \n");
    }
    free(leftKeyProps_scan);
    free(rightKeyProps_scan);
    testGmcDisconnect(conn, stmt);
}

void *read_thread_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));  //随机产生一个数进行读操作
        int loop = rand() % GRAPH_WRITE_NUM * 2;
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, loop);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (!isFinish) {
            if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
            read_if_stability_by_get_root(root);
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(true, isFinish);
        }
        GmcFreeIndexKey(stmt);
        sleep(ST);
        // printf("[graph info]:read_thread_if_general_complex \n");
    }
    testGmcDisconnect(conn, stmt);
}

void *update_thread_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));  //随机产生一个数进行更新操作
        int loop = rand() % GRAPH_WRITE_NUM * 2;
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, loop);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_update_set_property(root, loop, (int8_t *)"hello");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(ST);
    }
    testGmcDisconnect(conn, stmt);
    // printf("[graph info]:update_thread_if_general_complex \n");
}

void if_stability_set_localhash_index(GmcStmtT *stmt, void *fixed_value)
{
    int ret = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, fixed_value, TYPE_FIXED_LEN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_isStabilityLocalhashKey);
    EXPECT_EQ(GMERR_OK, ret);
}

void *localhash_scan_thread_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));  //随机产生一个数进行非唯一localhash 扫描
        int loop = rand() % (GRAPH_WRITE_NUM * 2);
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_set_localhash_index(stmt, (int8_t *)"field");
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        while (!isFinish) {
            if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
            read_if_stability_by_get_root(root);
            read_if_stability_by_get_ifm(ifmN);
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
        }
        GmcFreeIndexKey(stmt);
        // printf("[lite info]:localhash_scan_thread_if_general_complex \n");
        sleep(ST);
    }
    testGmcDisconnect(conn, stmt);
}

void *localkey_scan_remove_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    unsigned int arrLen = 2;
    uint32_t l_val_scan = 0;
    uint32_t l_val_scan1 = 0;
    uint32_t r_val_scan = 0;
    uint32_t r_val_scan1 = 0;
    GmcPropValueT *leftKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    GmcPropValueT *rightKeyProps_scan = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    GmcRangeItemT items_sc[arrLen];
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));  //随机产生一个值进行区间扫描
        int loop = rand() % (GRAPH_WRITE_NUM * 2);
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        l_val_scan = loop + 666;
        l_val_scan1 = loop;
        r_val_scan = loop + 666 + 66;
        r_val_scan1 = loop + 66;

        //设置左值
        leftKeyProps_scan[0].type = GMC_DATATYPE_UINT32;
        leftKeyProps_scan[0].value = &l_val_scan;
        leftKeyProps_scan[0].size = sizeof(uint32_t);
        leftKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
        leftKeyProps_scan[1].value = &l_val_scan1;
        leftKeyProps_scan[1].size = sizeof(uint32_t);
        //设置右值
        rightKeyProps_scan[0].type = GMC_DATATYPE_UINT32;
        rightKeyProps_scan[0].value = &r_val_scan;
        rightKeyProps_scan[0].size = sizeof(uint32_t);
        rightKeyProps_scan[1].type = GMC_DATATYPE_UINT32;
        rightKeyProps_scan[1].value = &r_val_scan1;
        rightKeyProps_scan[1].size = sizeof(uint32_t);

        items_sc[0].lValue = &leftKeyProps_scan[0];
        items_sc[0].rValue = &rightKeyProps_scan[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_OPEN;
        items_sc[0].order = GMC_ORDER_DESC;
        items_sc[1].lValue = &leftKeyProps_scan[1];
        items_sc[1].rValue = &rightKeyProps_scan[1];
        items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].rFlag = GMC_COMPARE_RANGE_OPEN;
        items_sc[1].order = GMC_ORDER_DESC;

        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, g_isStabilityLocalKey);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeIndexKey(stmt);
        sleep(ST);
        // printf("[graph info]:localkey_scan_if_general_complex \n");
    }
    free(leftKeyProps_scan);
    free(rightKeyProps_scan);
    testGmcDisconnect(conn, stmt);
}

void *scan_by_primary_key_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isFinish = false;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        // scan by primary key
        int index = rand() % (GRAPH_WRITE_NUM * 2);
        for (int loop = index; loop < (index + OP_NUM / 10); loop++) {
            ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            if_stability_set_pk_index(stmt, loop);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (!isFinish) {
                if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
                read_if_stability_by_get_root(root);
                read_if_stability_by_get_ifm(ifmN);
                ret = GmcFetch(stmt, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_EQ(true, isFinish);
            }
            GmcFreeIndexKey(stmt);
        }
        sleep(ST);
    }
    testGmcDisconnect(conn, stmt);
}

void *aging_thread_if_general_complex(void *arg)
{
    int ret = 0;
    bool isFinish = false;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    bool isAbnormal = false;

    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    srand((uint32_t)time(0));
    int loop = rand() % 200;

    while (1)
        ;
    {
        sleep(loop);
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBeginCheck(stmt, g_isStabilityLabelName, 0xff);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, g_isStabilityLabelName, 0xff, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
        ret = checkAccountStatus(stmt, g_isStabilityLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);
    }
    testGmcDisconnect(conn);
}

void if_stability_batch_insert(GmcStmtT *stmt, uint32_t start_pk, uint32_t end_pk)
{
    int32_t ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    int32_t vector_num = 3;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    ret = GmcBatchPrepare(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = start_pk; loop < end_pk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_set_pk(root, loop);
        if_stability_set_node_root(root, loop, (uint8_t *)"fixed");
        if_stability_set_node_ifm(ifmN, loop, (uint8_t *)"fixed");
        if_stability_set_node_dev(devN, loop);
        if_stability_set_node_l2(l2N, loop);
        if_stability_set_node_port(portN, loop);
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1_VN, &T1_VN);
            EXPECT_EQ(GMERR_OK, ret);
            if_stability_set_node_T1_V(T1_VN, j);
        }
        ret = GmcBatchAddVertexDML(stmt, GMC_CMD_INSERT_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    if (ret == GMERR_OK || ret == GMERR_PRIMARY_KEY_VIOLATION || ret == GMERR_UNIQUE_VIOLATION ||
        ret == GMERR_OUT_OF_MEMORY) {
        ASSERT_EQ(GMERR_OK, 0);
    } else {
        ASSERT_EQ(GMERR_OK, ret);
    }
}

void if_stability_batch_update(GmcStmtT *stmt, uint32_t start_pk, uint32_t end_pk)
{
    int32_t ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;

    ret = GmcBatchPrepare(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = start_pk; loop < end_pk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, loop);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_update_set_property(root, loop, (uint8_t *)"field");
        ret = GmcBatchAddVertexDML(stmt, GMC_CMD_UPDATE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
}

void if_stability_batch_delete(GmcStmtT *stmt, uint32_t start_pk, uint32_t end_pk)
{
    int32_t ret = 0;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;

    ret = GmcBatchPrepare(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    for (int loop = start_pk; loop < end_pk; loop++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, loop);
        ret = GmcBatchAddVertexDML(stmt, GMC_CMD_DELETE_VERTEX);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(stmt, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
}

void *batch_operation_if_general_complex(void *args)
{
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        srand((uint32_t)time(0));
        int start = rand() % (GRAPH_WRITE_NUM * 2);
        int end = (rand() % BATCH_NUM) + 1;
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // write
        if_stability_batch_insert(stmt, start, (start + end));
        sleep(5);

        // update
        if_stability_batch_update(stmt, start, (start + end));
        sleep(5);

        // remove
        if_stability_batch_delete(stmt, start, (start + end));
        GmcFreeStmt(stmt);
        sleep(ST);
        // printf("[grapth info]:recycle_write_remove_if_general_complex \n");
    }
    testGmcDisconnect(conn);
}

void sub_callback_with_if(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;

    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

//订阅增删改+老化事件
void *sub_if_general_complex(void *args)
{
    int ret = 0;
    char *sub_info = NULL;
    int chanRingLen = 256;
    const char *subConnName = (const char *)"subConnName";
    GmcStmtT *stmt_sub = NULL;
    GmcConnT *testSubConn = NULL;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char *subName = "subVertexLabel";
    SnUserDataT *user_data = NULL;

    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //创建订阅连接
    ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/if_complex_subinfo.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));
    //订阅事件
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(stmt, &tmp_sub_info, testSubConn, sub_callback_with_if, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    free(sub_info);

    srand(time(NULL));
    while (1) {
        int times = rand() % 10;
        sleep(times);
        user_data->subIndex = 0;
        user_data->insertNum = 0;
        user_data->deleteNum = 0;
        user_data->updateNum = 0;
        user_data->replaceNum = 0;
        user_data->replaceNum = 0;
    }
    ret = GmcUnSubscribe(stmt, subName);
    EXPECT_EQ(GMERR_OK, ret);
    free(user_data);
    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    testGmcDisconnect(conn, stmt);
}

//异步覆盖写
void if_stability_replace_async(GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int32_t ret = 0;
    uint32_t vector_num = 3;
    AsyncUserDataT *tdata = NULL;
    tdata = (AsyncUserDataT *)malloc(sizeof(AsyncUserDataT));
    memset(tdata, 0, sizeof(AsyncUserDataT));
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.insertCb = replace_vertex_callback;
    requestCtx.userData = tdata;

    for (int32_t i = start_pk; i < end_pk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_REPLACE);
        ASSERT_EQ(GMERR_OK, ret);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_set_pk(root, i);
        if_stability_set_node_root(root, i, (uint8_t *)"fixed");
        if_stability_set_node_ifm(ifmN, i, (uint8_t *)"fixed");
        if_stability_set_node_dev(devN, i);
        if_stability_set_node_l2(l2N, i);
        if_stability_set_node_port(portN, i);
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1_VN, &T1_VN);
            EXPECT_EQ(GMERR_OK, ret);
            if_stability_set_node_T1_V(T1_VN, j);
        }
        ret = GmcExecuteAsync(stmt, &requestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        if (tdata->status != GMERR_OK && tdata->status != GMERR_PRIMARY_KEY_VIOLATION &&
            tdata->status != GMERR_UNIQUE_VIOLATION && tdata->status != GMERR_OUT_OF_MEMORY) {
            ASSERT_EQ(GMERR_OK, tdata->status);
        }
    }
    free(tdata);
}

void if_stability_update_set_property_async(GmcNodeT *node, uint32_t index)
{
    int ret = 0;
    uint32_t tpValue = index;
    ret = GmcNodeSetPropertyByName(node, (char *)"tp", GMC_DATATYPE_UINT32, &tpValue, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
}

//异步主键更新
void if_stability_update_async(GmcStmtT *stmt, int32_t start_pk, int32_t end_pk)
{
    int32_t ret = 0;
    uint32_t vector_num = 3;
    AsyncUserDataT *tdata = NULL;
    tdata = (AsyncUserDataT *)malloc(sizeof(AsyncUserDataT));
    memset(tdata, 0, sizeof(AsyncUserDataT));
    GmcNodeT *root = NULL, *ifmN = NULL, *devN = NULL, *l2N = NULL, *portN = NULL, *T1_VN = NULL;
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.insertCb = update_vertex_callback;
    requestCtx.userData = tdata;

    for (int32_t i = start_pk; i < end_pk; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_isStabilityLabelName, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        if_stability_set_pk_index(stmt, i);
        if_stability_get_node(stmt, &root, &ifmN, &devN, &l2N, &portN, &T1_VN);
        if_stability_update_set_property_async(root, (i * 2) % GRAPH_WRITE_NUM);
        ret = GmcExecuteAsync(stmt, &requestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(tdata);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, tdata->status);
    }
    free(tdata);
}
void if_stability_whold_label_scan(GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    bool isFinish = true;
    uint32_t fetchNum = 0;
    //全表扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
    }
    printf("if_stability_whold_label_scan:fetchNum: %u\n", fetchNum);
}

void *ddl_and_conn_disconn(void *args)
{
    int ret = 0;
    int32_t default_conn = *(int32_t *)args;
    GmcConnT *asyncConn = NULL;
    GmcStmtT *asyncStmt = NULL;
    GmcConnT *syncConn = NULL;
    GmcStmtT *syncStmt = NULL;
    //封装的创建异步连接
    ret = testGmcConnect(&asyncConn, NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    //同步连接
    ret = testGmcConnect(&syncConn, &syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (1) {
        ret = GmcAllocStmt(asyncConn, &asyncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        //覆盖写  --异步
        srand((uint32_t)time(0));
        int index = rand() % GRAPH_WRITE_NUM;
        if_stability_replace_async(asyncStmt, index, (index + OP_NUM));
        GmcFreeStmt(asyncStmt);

        //全表扫描 --同步
        if_stability_whold_label_scan(syncStmt, g_isStabilityLabelName);

        // 一次创建obj_num个异步object
        GmcConnT *asyncConnT[MAX_CONN_SIZE] = {NULL};
        GmcStmtT *asyncStmtT[MAX_CONN_SIZE] = {NULL};
        int asyncConnSuccess = 0;
        for (int32_t loop = 0; loop < (MAX_CONN_SIZE - 2); loop++) {
            ret = testGmcConnect(&asyncConnT[loop], &asyncStmtT[loop], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
            if (ret == GMERR_TOO_MANY_CONNECTIONS)  //直到达到该连接池连接数的上限
            {
                break;
            } else if (ret == GMERR_OK) {
                asyncConnSuccess++;
            } else {
                printf("[Info] [connections async full] create async loop = %d,status = %d \n", loop, ret);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        //循环释放之前创建的obj_num个object
        for (int32_t loop = 0; loop < asyncConnSuccess; loop++) {
            testGmcDisconnect(asyncConnT[loop], asyncStmtT[loop]);
        }
        // sleep(5);

        //异步更新
        ret = GmcAllocStmt(asyncConn, &asyncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        srand((uint32_t)time(0));  //随机产生一个数进行更新操作
        int start = rand() % GRAPH_WRITE_NUM;
        if_stability_update_async(asyncStmt, start, (start + OP_NUM));
        GmcFreeStmt(asyncStmt);
        // sleep(2);

        //异步连接，创建完立马释放
        for (int loop = 0; loop < asyncConnSuccess; loop++) {
            ret = testGmcConnect(&asyncConnT[loop], &asyncStmtT[loop], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
            if (ret == GMERR_OK) {
                // sleep(5);
                testGmcDisconnect(asyncConnT[loop], asyncStmtT[loop]);
            } else if (ret != GMERR_OK && ret != GMERR_TOO_MANY_CONNECTIONS) {
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
        // sleep(ST);
        // printf("[graph Info]:ddl_and_conn_disconn \n");
    }
    testGmcDisconnect(asyncConn);
    testGmcDisconnect(syncConn, syncStmt);
}

#endif  //_IF_STABILITY_TEST_H_
