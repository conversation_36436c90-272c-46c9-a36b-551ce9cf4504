/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: rel_yang
 * Author: hanyang
 * Create: 2023-04-25
 * Description: 用例004~031需要使用CFE工具，把工具拷贝到root\CFE_Tool,
                然后chmod 777 -R  CFE_Tool加权限
 */
#include "rel_yang.h"

class rel_yang : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void rel_yang::SetUpTestCase()
{
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void rel_yang::TearDownTestCase()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void rel_yang::SetUp()
{
}

void rel_yang::TearDown()
{
}

/*****************************************************************************
 Description  : 006.进程异常退出 
1.客户端1创建100张表，客户端2创建100张YANG表（包含边表）
2.客户端2写入到50张表时，客户端2异常退出
3.重启客户端2，重新创建100张YANG表
4.检查表都是都创建成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    // 客户端2异常退出
    system("./rel_yang_006_01 ");
    system("./rel_yang_006_02 ");

    sleep(5);
    system("./rel_yang_006_03 ");

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 007.进程挂起/解挂
1.客户端1循环建表和删表，客户端2循环建YANG表和删表
2.挂起服务端进程
3.客户端1和2等待服务端业务处理
4.5秒后解挂服务端进程
5.客户端1和2业务恢复
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    pthread_t Thread[3] = {0};
    pthread_create(&Thread[0], NULL, ThreadCreateVertex100Client, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYang100Client, NULL);
    pthread_create(&Thread[2], NULL, ThreadProcessSuspended, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 008.进程挂起/解挂
1.客户端1循环建表和删表，客户端2循环建YANG表和删表
2.挂起客户端2进程
3.5秒后解挂客户端2进程
4.客户端1无影响，客户端2业务恢复
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    pthread_t Thread[3] = {0};
    pthread_create(&Thread[0], NULL, ThreadCreateVertex100Client, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYang100Client008, NULL);
    pthread_create(&Thread[2], NULL, ThreadClientSuspended, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 009.进程挂起/解挂
 1.设备启动过程中，客户端1循环建表和删表，客户端2循环建YANG表和删表
2.挂起客户端2进程
3.5秒后解挂客户端2进程
4.客户端1无影响，客户端2业务恢复
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    pthread_t Thread[3] = {0};
    pthread_create(&Thread[0], NULL, ThreadCreateVertex100Client, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYang100Client008, NULL);
    pthread_create(&Thread[2], NULL, ThreadClientSuspended, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 010.线程异常退出
 1.启动客户端1，建vertext表，正常的生产和消费（发送异步请求消息，接受响应消息）
2.启动客户端2，建YANG表，建vertext表，正常的生产、不消费（发送异步请求消息，不接受响应消息）
3.执行2分钟
4.客户端1业务处理正常
5.客户端2开始处理正常，后面报队列满错误码
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    pthread_t Thread[3] = {0};
    pthread_create(&Thread[0], NULL, ThreadCreateVertexAsync, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYangQueue, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 011.线程异常退出
 1.启动客户端1，建vertext表，正常的生产和消费（发送异步请求消息，接受响应消息）
2.启动客户端2，建YANG表，正常的生产和消费（发送异步请求消息，接受响应消息）
3.执行2分钟
4.挂起客户端2进程
5.5秒后解挂客户端2进程
6.客户端1业务处理正常
7.客户端2开始处理正常，后面报队列满错误码，然后恢复处理正常
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    system("sh create_multi_label_normal.sh 100");
    system("sh create_multi_label_yang.sh 100");

    pthread_t Thread[3] = {0};
    pthread_create(&Thread[0], NULL, ThreadCreateVertexAsync, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYangQueue, NULL);
    pthread_create(&Thread[2], NULL, ThreadClientSuspended011, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 012.超大规格表
1.建立超规格字段vertext表和YANG表（包含边表），失败
2.建立规格内最大vertext表和YANG表（包含边表），成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    AsyncUserDataT data = {0};
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;

    // 创建1025个字段的root表
    string labelNames[1] = {"root"};
    ret = TryCreateVertexLabelAsync(g_stmt_async, "schema_file/root_1025.gmjson", g_msConfigTrans, 1, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    // 创建1025个字段的con节点
    ret = TryCreateVertexLabelAsync(g_stmt_async, "schema_file/root_con_1025.gmjson", g_msConfigTrans, 1, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    // 创建1025个字段的choice case节点
    ret = TryCreateVertexLabelAsync(g_stmt_async, "schema_file/root_choice_case_1025.gmjson",
                                    g_msConfigTrans, 1, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    // 创建1025个字段的list表
    ret = TryCreateVertexLabelAsync(g_stmt_async, "schema_file/root_list_1025.gmjson",
                                    g_msConfigTrans, 1, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);

    // 先尝试删边表
    ret = GmcDropEdgeLabelAsync(g_stmt_async, "root_to_list", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
    if (data.status != GMERR_OK && data.status != GMERR_UNDEFINED_TABLE) {
        cout << endl << __FILE__ << ":" << __LINE__ << ":" << "ret:" << ret << endl;
    }
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建1024个字段的全类型表和edge表
    ret = TryCreateVertexLabelAsync(g_stmt_async, "schema_file/yang_1024_full.gmjson",
                                    g_msConfigTrans, 1, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/yang_1024_full_edge.gmjson", &edgeSchema);
    ASSERT_NE((void *)NULL, edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edgeSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(edgeSchema);
    edgeSchema = NULL;

    // 删除表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.表的个数超系统限制
1.循环创建不同的vertex表和YANG表（包含边表），直到创建失败
2.删除1个存在的vertex表，新创建1个YANG表，失败，新创建1个vertex表，成功。
3.删除1个存在的YANG表，新创建1个vertext表，失败，新创建1个YANG表，成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh create_multi_label_normal.sh 2500");
    system("sh create_multi_label_yang.sh 2500");
    int ret = 0;

    // 建立链接
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    AsyncUserDataT data = {0};
    char labelName[1024] = {0};
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    char rootName[1024] = {0};
    char listName[1024] = {0};
    char schemaPath[1024];
    char *schemaJson = NULL;
    uint32_t tableNum = 2500;
    uint32_t i, j;

    // 创建Vertex
    for (i = 0; i < tableNum; i++) {
        ret = sprintf(schemaPath, "./multi_vertexlabel/Vertex_%d.gmjson", i);
        EXPECT_LT(0, ret);
        ret = sprintf(labelName, "Vertex_%d", i);
        EXPECT_LT(0, ret);
        ret = TryCreateVertexLabel(g_stmt, schemaPath, g_msConfigFast, labelName);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
            AW_FUN_Log(LOG_INFO, "Create Vertex end. The number of create vertex failed  is %d, ret is %d.", i, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 创建Yang, 一个schema里面有2张表
    for (j = 0; j < tableNum; j++) {
        ret = sprintf(schemaPath, "./multi_vertexlabel/yang_tree_%d.gmjson", j);
        EXPECT_LT(0, ret);
        ret = sprintf(rootName, "root_%d", j);
        EXPECT_LT(0, ret);
        ret = sprintf(listName, "list_%d", j);
        EXPECT_LT(0, ret);
        string labelNames[2] = {rootName, listName};
        ret = TryCreateVertexLabelAsync(g_stmt_async, schemaPath, g_msConfigTrans, 2, labelNames);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
            AW_FUN_Log(LOG_INFO, "Create Yang end. The number of create Yang failed  is %d, data.status is %d.",
                j, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
            free(vertexSchema);
            vertexSchema = NULL;
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
            free(vertexSchema);
            vertexSchema = NULL;
        }
    }

    // 删除1个存在的vertex表，新创建1个YANG表，失败，新创建1个vertex表，成功。
    ret = GmcDropVertexLabel(g_stmt, "Vertex_1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("./multi_vertexlabel/yang_tree_2400.gmjson", &vertexSchema);
    ASSERT_NE((void *)NULL, vertexSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexSchema, g_msConfigTrans,
        create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    vertexSchema = NULL;

    readJanssonFile("./multi_vertexlabel/Vertex_1.gmjson", &schemaJson);
    EXPECT_NE((void *)NULL, schemaJson);
    ret = GmcCreateVertexLabel(g_stmt, schemaJson, g_msConfigFast);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaJson);
    schemaJson = NULL;

    // 删除1个存在的YANG表，新创建1个vertext表，失败，新创建1个YANG表，成功。
    ret = GmcDropVertexLabelAsync(g_stmt_async, "root_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropVertexLabelAsync(g_stmt_async, "list_1", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("./multi_vertexlabel/Vertex_2400.gmjson", &schemaJson);
    EXPECT_NE((void *)NULL, schemaJson);
    ret = GmcCreateVertexLabel(g_stmt, schemaJson, g_msConfigFast);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    free(schemaJson);
    schemaJson = NULL;

    readJanssonFile("./multi_vertexlabel/yang_tree_1.gmjson", &vertexSchema);
    ASSERT_NE((void *)NULL, vertexSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexSchema, g_msConfigTrans,
        create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    vertexSchema = NULL;

    // 删除表
    for (j = 0; j < tableNum; j++) {
        ret = sprintf(rootName, "root_%d", j);
        EXPECT_LT(0, ret);
        ret = sprintf(listName, "list_%d", j);
        EXPECT_LT(0, ret);

        ret = GmcDropVertexLabelAsync(g_stmt_async, rootName, drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }

        ret = GmcDropVertexLabelAsync(g_stmt_async, listName, drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
    }

    for (i = 0; i < tableNum; i++) {
        ret = sprintf(labelName, "Vertex_%d", i);
        EXPECT_LT(0, ret);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 014.连接池耗尽
1.客户端线程功能：连接服务端，循环1万次建表和删表，建表，退出
2.启动maxConnSize/2个线程建YANG表（包含边表），maxConnSize/2 + 1个线程建vertext表
3.maxConnSize个业务线程处理正常，无报错
4.最后一个线程连接服务端失败
5.检查maxConnSiz个表，创建成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    // 这里要建的表数量大于(MAX_CONN_SIZE / 2)即可
    if (g_envType == 2) {
        system("sh create_multi_label_normal.sh 40");
        system("sh create_multi_label_yang.sh 40");
    } else {
        system("sh create_multi_label_normal.sh 770");
        system("sh create_multi_label_yang.sh 770");
    }

    // 获取当前剩余最大连接数
    uint32_t maxYangConnNum = 0;
    uint32_t maxVertexConnNum = 0;
    TestGetConnNum(&maxYangConnNum, &maxVertexConnNum);

    pthread_t thread[2];
    pthread_t threadVertex[maxVertexConnNum];

    pthread_create(&thread[0], NULL, ThreadCreateYang014, (void *)&maxYangConnNum);
    pthread_create(&thread[1], NULL, ThreadCreateVertex014, (void *)&maxVertexConnNum);

    pthread_join(thread[0], NULL);
    pthread_join(thread[1], NULL);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 015.异步消息队列写满
1.启动客户端1，操作vertext表，正常的生产、消费；
客户端2，操作YANG表（包含边表），正常的生产、不消费（发送异步请求消息，不接受响应消息）
2.客户端2开始处理正常，后面报队列满错误码；客户端1无影响
3.恢复消费后继续发送请求，不会报错
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);
    TestCreateLabelSingle(g_stmt_async);
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    pthread_t thread[2];

    pthread_create(&thread[0], NULL, ThreadCreateVertexCycle, NULL);
    pthread_create(&thread[1], NULL, ThreadYangDML015, NULL);

    pthread_join(thread[0], NULL);
    pthread_join(thread[1], NULL);

    // 删除namespace
    TestDropLabelSingle(g_stmt_async);
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.表空间满
1.客户端创建10张vertext表和YANG表（包含边表）并写入数据到表空间满
2.删除部分数据
3.继续写入数据，写入成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace 和 Tablespace
    TestCreateNamespaceTabelspace(g_stmt_async, g_namespace, g_namespaceUserName, g_tablespace);
    TestUseNamespace(g_stmt_async, g_namespace);
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表
    TestCreateVertexLabelBig(g_stmt);
    TestCreateLabelSingleBig(g_stmt_async);
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    uint32_t listNum = 10000;
    uint32_t initValue = 0;
    for (int i = 1; i <= 10; i++) {
        // insert vertex直到tablespace满
        AW_FUN_Log(LOG_STEP, "Insert vertex %d times.", i);
        TestInsertVertexMem(g_stmt);

        // 删除数据
        AW_FUN_Log(LOG_STEP, "Delete vertex %d times.", i);
        TestDeleteVertexMem(g_stmt);
        sleep(2);

        // insert yang直到tablespace满
        AW_FUN_Log(LOG_STEP, "Insert yang %d times.", i);
        TestInsertYangMem(g_conn_async, "root", "list", initValue, listNum);

        // 删除数据
        AW_FUN_Log(LOG_STEP, "Remove yang %d times.", i);
        TestRemoveListYangMem(g_conn_async, "root", "list", initValue, listNum);
        TestRemoveRoot(g_conn_async, "root");
    }

    // 删除表
    TestDropVertexLabel(g_stmt, "Vertex_big");
    TestDropLabelSingleBig(g_stmt_async);

    // 删除namespace 和 Tablespace
    TestDropNamespace(g_stmt_async, g_namespace);
    TestDropTablespace(g_stmt_async, g_tablespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.文件丢失或被删除
1.删除审计日志文件
2.创建一张新YANG表
3.重新生成日志文件，工具可查看该审计日志
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang, rel_yang_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    ret = GtClearAuditLog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表
    TestCreateLabelSingle(g_stmt_async);

    if (g_envType != 2) {
        ret = GtCheckAuditLog("DDL", "type: VERTEX_LABEL name: \"root\" type: VERTEX_LABEL name: \"list\"",
        "success", "CREATE VERTEX LABEL");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删除表
    TestDropLabelSingle(g_stmt_async);

    // 删除namespace
    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class rel_yang_restart : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void rel_yang_restart::SetUpTestCase()
{
}

void rel_yang_restart::TearDownTestCase()
{
}

void rel_yang_restart::SetUp()
{
}

void rel_yang_restart::TearDown()
{
}

/*****************************************************************************
 Description  : 001.系统CPU过载
1.数据库进程绑核启动（假设绑定在CPU1上）
2.CPU1使用率为90%
3.进程1导入R21版本的530张表，进程2同时创建多个namespace并创建Yang的全量典配表数据（包含边表）
4.所有表导入成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang_restart, rel_yang_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 绑定服务的cpu
    system("taskset -pc 0 `pidof gmserver`");

    // 启动服务
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxYangTableNum=10000\"");
    TestStartServer();

    // 启动故障：系统CPU过载
    system("sh ./cfeTool.sh CpuOverload90");
    sleep(2);

    int ret = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开链接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t Thread[2] = {0};
    pthread_create(&Thread[0], NULL, ThreadImportR21, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYangFull, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);

    // 取消故障
    system("sh ./cfeTool.sh CleanCpuOverload");

    // 停止服务
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    if (g_envType != 2) {
        TestStopServer();
    }
}

/*****************************************************************************
 Description  : 002.系统CPU过载
1.数据库进程绑核启动（假设绑定在CPU1上）
2.CPU1使用率为90%
3.进程1导入R21版本的530张表，导入完成后，进程2再导入Yang的全量典配表数据（包含边表）
4.所有表导入成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang_restart, rel_yang_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 绑定服务的cpu
    system("taskset -pc 0 `pidof gmserver`");

    // 启动服务
    TestStartServer();

    // 启动故障：系统CPU过载
    system("sh ./cfeTool.sh CpuOverload90");
    sleep(2);

    int ret = 0;

    AW_FUN_Log(LOG_INFO, "=================Create R21 Table========================");
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 部分表重名，530张实际只能导入525张，只导入不清除类型
    char cmd[MAX_CMD_SIZE];
    char schemaFile[128] = "$TEST_HOME/schema_file/r21_ndb/gmjson/";
    ret = snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c cache -f %s -ns %s -s %s", g_toolPath, schemaFile,
        g_testNameSpace, g_connServer);
    EXPECT_LT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    sleep(5);

    AW_FUN_Log(LOG_INFO, "=================Create Yang Table========================");
    AsyncUserDataT data = {0};
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcTxConfigT g_mSTrxConfig;
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    char edgeName[221][1024] = {0};
    char vertexName[275][1024] = {0};
    GetEdgeNum(edgeName);
    GetVertexNum(vertexName);

    readJanssonFile("schema_file/SOHO_S380_VertexLabel.gmjson", &vertexSchema);
    ASSERT_NE((void *)NULL, vertexSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexSchema, g_msConfigTrans,
        create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    vertexSchema = NULL;

    readJanssonFile("schema_file/SOHO_S380_EdgeLabel.gmjson", &edgeSchema);
    ASSERT_NE((void *)NULL, edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edgeSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(edgeSchema);
    edgeSchema = NULL;

    // todo: DML操作
    TestInsertHuaweiAcl(g_conn_async);

    // 删除r21的表
    ret = DeleteVertexLabelR21(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除Yang表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开链接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消故障
    system("sh ./cfeTool.sh CleanCpuOverload");

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
}

/*****************************************************************************
 Description  : 003.系统CPU过载
1.数据库进程绑核启动（假设绑定在CPU1上）
2.CPU1使用率为100%
3.进程1导入R21版本的530张表，进程2并发导入Yang的全量表（包含边表）
4.所有表导入成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang_restart, rel_yang_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 绑定服务的cpu
    system("taskset -pc 0 `pidof gmserver`");

    // 启动服务
    TestStartServer();

    // 启动故障：系统CPU过载
    system("sh ./cfeTool.sh CpuOverload");
    sleep(2);

    int ret = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开链接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t Thread[2] = {0};
    pthread_create(&Thread[0], NULL, ThreadImportR21, NULL);
    pthread_create(&Thread[1], NULL, ThreadCreateYangSingle, NULL);
    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);

    // 取消故障
    system("sh ./cfeTool.sh CleanCpuOverload");

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
}

/*****************************************************************************
 Description  : 004.系统共享内存不足
1.使用工具消耗掉系统的剩余空闲内容
2.导入Yang的全量典配表数据（包含边表）
3.导入失败，报内存不足
4.故障恢复后，重新导入
5.所有表导入成功
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang_restart, rel_yang_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 启动服务
    system(
        "sh $TEST_HOME/tools/modifyCfg.sh "
        "\"workerHungThreshold=6,200,300\" "
        "\"maxSysDynSize=483\" "
        "\"pageSize=8\" "
        "\"planCacheSize=8\" "
        "\"maxSortBufferSize=1\" "
        "\"maxTotalDynSize=500\" "
        );
    TestStartServer();
    int ret = 0;
    system("sh create_multi_label_dyn.sh 1000");

    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace
    AsyncUserDataT data = {0};
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcTxConfigT g_mSTrxConfig;
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;

    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    // 启动故障：占满共享内存
    uint32_t tableNum = 1000;
    TestCreateMultiLabel(g_stmt, tableNum);
    AW_FUN_Log(LOG_INFO, "=================after testDynMemFull====================");

    // 导入表
    char cmd[MAX_CMD_SIZE];
    char schemaFile[128] = "schema_file/SOHO_S380_VertexLabel.gmjson";
    ret = snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schemaFile,
        g_namespace, g_connServer);
    EXPECT_LT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    ret = executeCommand(cmd, "import batch exec unsucc. ret = 1010001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消故障 20230804 共享内存不可复用
    TestDropMultiLabel(g_stmt, tableNum);

    // 再次导入前先删除
    char vertexName[275][1024] = {0};
    GetVertexNum(vertexName);
    for (int k = 1; k <= 274; k++) {
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexName[k], drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 再次导入
    ret = executeCommand(cmd, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 删除Yang表
    for (int k = 1; k <= 274; k++) {
        ret = GmcDropVertexLabelAsync(g_stmt_async, vertexName[k], drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    system("rm -rf multi_vertexlabel/");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    if (g_envType != 2) {
        TestStopServer();
    }
}

/*****************************************************************************
 Description  : 005.进程异常退出 
1.客户端1循环建表和删表，客户端2循环建YANG表和删表
2.服务端异常退出
3.客户端1和2收到异常通知
4.客户端1和2关闭连接资源，正常
 Author       : hanyang
*****************************************************************************/
TEST_F(rel_yang_restart, rel_yang_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 启动服务
    TestStartServer();

    // 并发kill 服务端
    int ret = 0;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开链接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("./rel_yang_005_01 ");
    system("./rel_yang_005_02 ");

    sleep(10);
    system("kill -9 `pidof gmserver`");

    sleep(10);
    // 重新启动服务
    TestStartServer();

    // 执行DML和订阅用例
    system("./rel_yang_005_01 ");
    system("./rel_yang_005_02 ");

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
}
