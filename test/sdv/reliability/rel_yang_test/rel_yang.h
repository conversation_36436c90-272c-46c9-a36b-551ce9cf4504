/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: rel_yang
 * Author: hanyang
 * Create: 2023-04-25
 */
#ifndef REL_YANG_TEST_H
#define REL_YANG_TEST_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <sys/ipc.h> // 共享内存需要的头文件wait
#include <sys/shm.h> // 共享内存需要的头文件
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <semaphore.h>
#include <string>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
#include "../try.h"
using namespace std;

// 公共变量
#define MAX_LABELNAME_LEN 128
#define MAX_CMD_SIZE 1024
#define MAX_PROPERTY_NAME_LEN 1024
#define RESTART_SERVER 0

// 轻量化事务
const char *g_msConfigFast = "{\"max_record_count\" : 1000000}";
const char *g_msConfigTrans = R"(
{
    "max_record_count":100000000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
const char *g_msConfigTransVertex = R"(
{
    "max_record_count":100000000,
    "isFastReadUncommitted":0
})";

const char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
const char *g_namespace = "Namespace1";
const char *g_namespaceUserName = "abc";
const char *g_tablespace = "Tablespace1";

#define MAX_CMD_SIZE 1024

void TestCreateMultiLabel(GmcStmtT *stmt, uint32_t tableNum)
{
    int ret = 0;
    char labelName[1024];
    char schemaPath[1024];
    char *schemaJson = NULL;

    for (uint32_t i = 0; i < tableNum; i++) {
        ret = sprintf(labelName, "Vertex_dyn_%d", i);
        ASSERT_LT(0, ret);
        ret = sprintf(schemaPath, "./multi_vertexlabel/Vertex_dyn_%d.gmjson", i);
        ASSERT_LT(0, ret);
        readJanssonFile(schemaPath, &schemaJson);
        ASSERT_NE((void *)NULL, schemaJson);

        ret = GmcCreateVertexLabel(stmt, schemaJson, g_msConfigFast);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "GmcCreateVertexLabel=%d, ret = %d.\n", i, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            free(schemaJson);
            schemaJson = NULL;
            break;
        } else {
            ASSERT_EQ(GMERR_OK, ret);
            free(schemaJson);
            schemaJson = NULL;
        }
    }
}

void TestDropMultiLabel(GmcStmtT *stmt, uint32_t tableNum)
{
    int ret = 0;
    char labelName[1024];

    for (uint32_t i = 0; i < tableNum; i++) {
        ret = sprintf(labelName, "Vertex_dyn_%d", i);
        ASSERT_LT(0, ret);
        ret = GmcDropVertexLabel(stmt, labelName);
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}

void TestCreateVertexLabelBig(GmcStmtT *stmt)
{
    int ret = 0;
    ret = TryCreateVertexLabel(stmt, "./schema_file/Vertex_big.gmjson", g_msConfigTransVertex, "Vertex_big");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestDropVertexLabel(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;

    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestCreateLabelSingle(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    // 先尝试删除边表
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_list", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
    if (data.status != GMERR_OK && data.status != GMERR_UNDEFINED_TABLE) {
        cout << endl << __FILE__ << ":" << __LINE__ << ":ret:" << ret << endl;
    }

    string labelNames[2] = {"root", "list"};
    ret = TryCreateVertexLabelAsync(stmt, "schema_file/yang_tree.gmjson", g_msConfigTrans, 2, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelSingle(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void TestCreateLabelSingleBig(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    // 先尝试删除edge表
    ret = GmcDropEdgeLabelAsync(stmt, "root_to_list", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_EQ(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
    if (data.status != GMERR_OK && data.status != GMERR_UNDEFINED_TABLE) {
        cout << endl << __FILE__ << ":" << __LINE__ << ":ret:" << ret << endl;
    }

    string labelNames[2] = {"root", "list"};
    ret = TryCreateVertexLabelAsync(stmt, "schema_file/yang_tree_big.gmjson", g_msConfigTrans, 2, labelNames);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/yang_tree_edge.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabelSingleBig(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/**************************************Server*******************************************/
void TestStartServer()
{
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void TestStopServer()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
}

int TestCheckNull(GmcStmtT *stmt)
{
    if (stmt == NULL) {
        return -1;
    } else {
        return GMERR_OK;
    }
}

void TestGetConnNum(uint32_t *yangConnNum, uint32_t *vertexConnNum)
{
    int ret = 0;

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
    uint32_t maxConnNum = MAX_CONN_SIZE - existConnNum;
    uint32_t maxYangConnNum = 0;
    uint32_t maxVertexConnNum = 0;
    if ((maxConnNum % 2) == 0) {
        maxYangConnNum = maxConnNum / 2;
        maxVertexConnNum = maxConnNum / 2 + 1;
    } else {
        maxYangConnNum = maxConnNum / 2 + 1;
        maxVertexConnNum = maxConnNum / 2 + 1;
    }
    AW_FUN_Log(LOG_INFO, "existConnNum = %d, maxConnNum = %d, maxYangConnNum = %d, maxVertexConnNum = %d",
        existConnNum, maxConnNum, maxYangConnNum, maxVertexConnNum);

    *yangConnNum = maxYangConnNum;
    *vertexConnNum = maxVertexConnNum;
}

#ifdef ENV_RTOSV2X
// 鸿蒙完整路径: /var/log/hlog/info/hloglog.csv
#define GT_STLM_AUDIT_LOG_PATH \
    "$TEST_HOME/testcases/reliability/rel_yang_test/log/"
#else
// stlm日志完整路径: /opt/vrpv8/var/log/stlm/info/stlmlog.csv
// QEMU临时日志完整路径: /var/run/start_hpe.log
#define GT_STLM_AUDIT_LOG_PATH "/opt/vrpv8/var/log/stlm/info/"
#endif

#if ENABLE_INFO
#define TEST_INFO(log, args...)                                                                 \
    do {                                                                                        \
        fprintf(stdout, "Info: pid-%d %s:%d: " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)
#else
#define TEST_INFO(log, args...)
#endif

#define TEST_ERROR(log, args...)                                                                \
    do {                                                                                        \
        fprintf(stdout, "Error: pid-%d %s:%d " log "\n", getpid(), __FILE__, __LINE__, ##args); \
    } while (0)

#define LOG_IFERR(ret, log, args...)                                                                     \
    do {                                                                                                 \
        if ((ret) != GMERR_OK) {                                                                         \
            fprintf(stdout, "Error: %s:%d " log ", " #ret " = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        }                                                                                                \
    } while (0)

// sysconfig.ini -> 0:EULER, 1:docker+DAP(lite), 2:docker(yang)
typedef enum TagRunMode { GT_RUN_MODE_EULER = 0, GT_RUN_MODE_DAP = 1, GT_RUN_MODE_YANG = 2 } GtRunModeE;

// 审计类型
typedef enum TagAuditType { GT_AUDIT_TYPE_DCL = 0, GT_AUDIT_TYPE_DDL = 1, GT_AUDIT_TYPE_DML = 2 } GtAuditTypeE;

// 审计开关
typedef enum TagAuditSwitch { GT_AUDIT_SWITCH_ON = 0, GT_AUDIT_SWITCH_OFF = 1 } GtAuditSwitchE;

// 清除运行时日志
int GtClearAuditLog()
{
    int ret;
    if (g_runMode == GT_RUN_MODE_DAP) {
#ifdef ENV_RTOSV2X
        ret = GtExecSystemCmd("rm -rf %s/*", GT_STLM_AUDIT_LOG_PATH);
        LOG_IFERR(ret, "clear stlm info log, %s", strerror(ret));
#endif
    } else {
        ret = GtExecSystemCmd("cat /dev/null > ${TEST_HOME}/log/secure/sgmserver/sgmserver.log");
        LOG_IFERR(ret, "clear local log, %s", strerror(ret));
    }
    return GMERR_OK;
}

// [out] result: 执行系统调用的结果, 使用结束后必须调用free()释放内存
int GtExecSystemCmd(char **result, const char *format, ...)
{
    int ret;
    errno = 0;
    va_list args;
    va_start(args, format);
    char cmd[1024] = {0};
    ret = vsnprintf(cmd, sizeof(cmd), format, args);
    if (ret < 0) {
        TEST_ERROR("execute vsnprintf failed, ret = %d, %s.", ret, strerror(errno));
        va_end(args);
        return FAILED;
    }
    va_end(args);

    TEST_INFO("cmd = \"%s\"", cmd);
    FILE *fd = popen(cmd, "r");
    if (fd == NULL) {
        TEST_ERROR("popen failed, %s.", strerror(errno));
        return FAILED;
    }

    // XXX 优化为动态获取流长度
    int size = 1024 * 100;
    char *tmpResult = (char *)malloc(sizeof(char) * size);
    if (tmpResult == NULL) {
        TEST_ERROR("malloc failed, %s.", strerror(errno));
        return FAILED;
    }
    memset(tmpResult, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat(tmpResult, buf);
    }

    ret = pclose(fd);
    if (ret == -1) {
        TEST_ERROR("pclose failed, %s.", strerror(errno));
        free(tmpResult);
        return FAILED;
    }
    *result = tmpResult;
    return GMERR_OK;
}

// 检查本地日志中的慢操作场景日志
int GtCheckLocalAuditLog(const char *auditType, const char *resource, const char *status, const char *keyWords)
{
    int ret = GMERR_OK;
    char *result = NULL;

    // 过滤审计日志关键字 DCL/DDL/DML, 提取关键信息 awk -F '[][]'  '{print $4}'
#if READ_LOG_BY_GREP
    ret = GtExecSystemCmd(&result, "grep -Er \"\\[%s\\].*\" ${TEST_HOME}/log/secure/sgmserver/*", auditType);
    if (ret != GMERR_OK) {
        TEST_ERROR("execute system command failed, ret = %d", ret);
        free(result);
        return FAILED;
    }
    if (strlen(result) > 1) {
        result[strlen(result) - 1] = '\0';
    }
#else
    char logFile[256];
    char *dir = getenv("TEST_HOME");
    if (dir) {
        (void)snprintf(logFile, sizeof(logFile), "%s/log/secure/sgmserver/sgmserver.log", dir);
    } else {
        (void)snprintf(logFile, sizeof(logFile), "./log/secure/sgmserver/sgmserver.log");
    }
    readJanssonFile(logFile, &result);
    if (result == NULL) {
        TEST_ERROR("read log file failed");
        return FAILED;
    }

    if (strstr(result, auditType) == NULL) {
        TEST_ERROR("check audit log faield, expect auditType is \"%s\"", auditType);
        ret = FAILED;
    }
#endif

    TEST_INFO("audit log is:\n\"\n%s\n\"", result);

    // NOTICE 2021-06-09 当前版本部分审计日志资源字段不支持设置资源字段
    if (resource == NULL) {
        resource = "query";
    }
    if (strstr(result, resource) == NULL) {
        TEST_ERROR("check audit log faield, expect resouce is \"%s\"", resource);
        ret = FAILED;
    }
    if (strstr(result, status) == NULL) {
        TEST_ERROR("check audit log faield, expect status is \"%s\"", status);
        ret = FAILED;
    }
    if (strstr(result, keyWords) == NULL) {
        TEST_ERROR("check audit log faield, expect keyWords is \"%s\"", keyWords);
        ret = FAILED;
    }

    if (ret != GMERR_OK) {
        TEST_ERROR("audit log is:\n\"\n%s\n\"", result);
    }

    free(result);
    return ret;
}

// 检查stlm日志中的慢审计日志
int GtCheckStlmAuditLog(const char *auditType, const char *resource, const char *status, const char *keyWords)
{
    int ret;

#if IS_SUPPORT_STLM_LOG
    // 检查 stlm 日志是否被抑制
    char *logModeShow = NULL;
    ret = GtExecSystemCmd(&logModeShow, "/usr/local/bin/stlmbox --logmodshow | grep -E \"ModId:(154|155)\" "
                                        "| awk -F [,:] 'BEGIN{total=0}{total+=$6}END{print total}'");
    if (ret != GMERR_OK) {
        TEST_ERROR("execute system command failed, ret = %d", ret);
        free(logModeShow);
        return FAILED;
    }

    if (strlen(logModeShow) > 1) {
        logModeShow[strlen(logModeShow) - 1] = '\0';
    }

    /* 查询日志抑制状态, 当logsuppressed为: 1时抑制err日志,
    2时抑制info日志，3同时抑制info和err日志，审计日志属于info级别日志 */
    int32_t logSuppressed = atoi(logModeShow);
    if (logSuppressed > 1) {
        TEST_ERROR("stlm log is suppressed, logModeShow = \"%s\"", logModeShow);
        GtExecSystemCmd("/usr/local/bin/stlmbox --logmodshow");
        free(logModeShow);
        return GMERR_OK;
    }

    free(logModeShow);
#endif

    // NOTICE 2021-06-09 当前版本部分审计日志资源字段不支持设置资源字段
    if (resource == NULL) {
        resource = "unknown resource";
    }

    // stlm 日志非实时刷新, 设置 8s 的超时时间
    const int32_t timeout = 8;
    int resRet, statusRet, keyWordsRet;
    for (int32_t i = 0; i < timeout; i++) {
        char *csvResult = NULL;
        char *gzResult = NULL;

        // 过滤审计日志关键字 DCL/DDL/DML, awk -F '[][]'  '{print $4}' | tr -d '[:cntrl:]'
#ifdef ENV_RTOSV2X
        ret = GtExecSystemCmd(
            &csvResult, "cat %s/sgmserver/sgmserver.log 2>/dev/null | grep -Er \"\\[%s\\].*\"",
            GT_STLM_AUDIT_LOG_PATH, auditType);
#else
        ret = GtExecSystemCmd(&csvResult, "cat %s/start_hpe.log 2>/dev/null | grep -Er \"\\[%s\\].*\"",
            GT_STLM_AUDIT_LOG_PATH, auditType);
#endif
        if (ret != GMERR_OK) {
            TEST_ERROR("execute system command failed, ret = %d", ret);
            free(csvResult);
            return FAILED;
        }
        ret = GtExecSystemCmd(
            &gzResult, "zcat %s/*.gz 2>/dev/null | grep -Er \"\\[%s\\].*\"", GT_STLM_AUDIT_LOG_PATH, auditType);
        if (ret != GMERR_OK) {
            TEST_ERROR("execute system command failed, ret = %d", ret);
            free(csvResult);
            free(gzResult);
            return FAILED;
        }
        // 执行 GtExecSystemCmd 拿到的日志信息
        AW_FUN_Log(LOG_INFO, "************************ result is %s *******************\n", csvResult);
        if ((strstr(csvResult, resource) == NULL) && (strstr(gzResult, resource) == NULL)) {
            resRet = FAILED;
        } else {
            resRet = GMERR_OK;
        }
        if ((strstr(csvResult, status) == NULL) && (strstr(gzResult, status) == NULL)) {
            statusRet = FAILED;
        } else {
            statusRet = GMERR_OK;
        }
        if ((strstr(csvResult, keyWords) == NULL) && (strstr(gzResult, keyWords) == NULL)) {
            keyWordsRet = FAILED;
        } else {
            keyWordsRet = GMERR_OK;
        }

        free(csvResult);
        free(gzResult);

        if (ret == GMERR_OK && resRet == GMERR_OK && statusRet == GMERR_OK && keyWordsRet == GMERR_OK) {
            break;
        }
        sleep(1);
    }

    // 循环外打印检查结果,避免过多重复日志
    if (resRet != GMERR_OK) {
        ret = FAILED;
        TEST_ERROR("check audit log faield, expect resouce is \"%s\"", resource);
    }
    if (statusRet != GMERR_OK) {
        ret = FAILED;
        TEST_ERROR("check audit log faield, expect status is \"%s\"", status);
    }
    if (keyWordsRet != GMERR_OK) {
        ret = FAILED;
        TEST_ERROR("check audit log faield, expect keyWords is \"%s\"", keyWords);
    }
    if (ret != GMERR_OK) {
        TEST_ERROR("actual stlm log is:");
        GtExecSystemCmd("cat %s/*.csv 2>/dev/null | grep -Er \"\\[%s\\].*\"", GT_STLM_AUDIT_LOG_PATH, auditType);
        GtExecSystemCmd("zcat %s/*.gz 2>/dev/null | grep -Er \"\\[%s\\].*\"", GT_STLM_AUDIT_LOG_PATH, auditType);
    }

    return ret;
}

// 检查审计日志
int GtCheckAuditLog(const char *auditType, const char *resource, const char *status, const char *keyWords)
{
    if (g_runMode == GT_RUN_MODE_DAP) {
        return GtCheckStlmAuditLog(auditType, resource, status, keyWords);
    } else {
        return GtCheckLocalAuditLog(auditType, resource, status, keyWords);
    }
}

/**************************************DML*******************************************/
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestBatchPrepareBig(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_OFF)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 30000);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

void TestCreateNamespace(GmcStmtT *stmt, const char * namespaceName, const char * namespaceUserName)
{
    int ret;
    AsyncUserDataT data = {0};

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespaceName;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "create namespaceName: %s\n", namespaceName);

    ret = TryDropNameSpace(stmt, namespaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespaceWithCfgAsync(stmt, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestUseNamespace(GmcStmtT *stmt, const char * namespaceName)
{
    int ret;
    AsyncUserDataT data = {0};

    ret = GmcUseNamespaceAsync(stmt, namespaceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestCreateNamespaceTabelspace(GmcStmtT *stmt, const char * namespaceName, const char * namespaceUserName,
    const char * tablespaceName)
{
    int ret;
    AsyncUserDataT data = {0};

    // 创建tsp
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 4;
    tspCfg.stepSize = 4;
    tspCfg.maxSize = 24;

    ret = GmcCreateTablespaceAsync(stmt, &tspCfg, create_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespaceName;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    AW_FUN_Log(LOG_INFO, "create namespaceName: %s\n", namespaceName);

    ret = TryDropNameSpace(stmt, namespaceName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespaceWithCfgAsync(stmt, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestDropNamespace(GmcStmtT *stmt, const char * namespaceName)
{
    int ret;
    AsyncUserDataT data = {0};

    ret = GmcDropNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestDropTablespace(GmcStmtT *stmt, const char * tablespaceName)
{
    int ret;
    AsyncUserDataT data = {0};

    ret = GmcDropTablespaceAsync(stmt, tablespaceName, drop_tablespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void CreateSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d\n", userData.status);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void ReleaseSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransReleaseSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "release savepoint error code:%d\n", userData.status);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT userData = {0};

    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "rollback savepoint error code:%d\n", userData.status);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TestQuerySPNum(uint32_t expectSPNum)
{
    int ret;
    char command[MAX_CMD_SIZE];

    char const *viewName = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);

    char expect[1024] = {0};
    ret = snprintf(expect, 1024, "SAVE_POINT_NUM: %d", expectSPNum);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = executeCommand(command, expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestQueryTrxState(uint32_t expectTrxState)
{
    int ret;
    char command[MAX_CMD_SIZE];

    char const *viewName = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, viewName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);

    char expect[1024] = {0};
    ret = snprintf(expect, 1024, "TRX_STATE: %d", expectTrxState);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = executeCommand(command, expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int ExecuteCommandValue(char *cmd, const char *name, uint64_t *value)
{
    char cmdOutput[128] = {0};
    uint64_t getvalue = 0;
    FILE *pf = popen(cmd, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_INFO, "popen(%s) error.\n", cmd);
        return -1;
    }
    while (NULL != fgets(cmdOutput, 128, pf)) {
        getvalue = atoi(cmdOutput);
        *value = getvalue;
    }
    pclose(pf);
    pf = NULL;
    return 0;
}

void getSysView(const char *viewname = NULL, const char *name = NULL, uint64_t *value = NULL)
{
    int ret = 0;
    char command[MAX_CMD_SIZE];

    if (name == NULL) {
        ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, viewname, g_connServer);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        AW_FUN_Log(LOG_INFO, "%s\n", command);
        system(command);
    } else {
        ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s | grep %s |awk '{print $2}'", g_toolPath,
            viewname, g_connServer, name);
        AW_MACRO_EXPECT_NE_INT(0, ret);
        system(command);

        if ((name != NULL) && (value != NULL)) {
            ret = ExecuteCommandValue(command, name, value);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    memset(command, 0, sizeof(command));
}

int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

// 只有list类型需要设置主键
void TestYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIdentity(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    uint32_t value = i;
    char identity[20];
    ret = snprintf(identity, 20, "%d", value);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, identity, (strlen(identity)), "identity", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetRuleBasic(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    uint32_t nameValue = i;
    char name[20];
    ret = snprintf(name, 20, "rule%d", nameValue);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, name, (strlen(name)), "name", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t id = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char ipaddr[20] = "***********";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, ipaddr, (strlen(ipaddr)), "source-ipaddr", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char wild[20] = "0.0.0.0";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, wild, (strlen(wild)), "source-wild", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetRuleAdvance(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    uint32_t nameValue = i;
    char name[20];
    ret = snprintf(name, 20, "rule%d", nameValue);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, name, (strlen(name)), "name", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t id = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char action[20] = "permit";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, action, (strlen(action)), "action", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetProtocol(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    uint8_t protocol = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT8, &protocol, sizeof(uint8_t), "protocol", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetSrcIP(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    char ipaddr[20] = "***********";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, ipaddr, (strlen(ipaddr)), "source-ipaddr", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char wild[20] = "0.0.0.0";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, wild, (strlen(wild)), "source-wild", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetDstIP(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    char ipaddr[20] = "***********";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, ipaddr, (strlen(ipaddr)), "dest-ipaddr", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char wild[20] = "0.0.0.0";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, wild, (strlen(wild)), "dest-wild", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyWithoutF0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyBigData(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t superSize = 1024;
    char *superValue = (char *)malloc(superSize);
    if (superValue == NULL) {
        ASSERT_NE((void *)NULL, superValue);
    }
    memset(superValue, 'B', (superSize - 1));
    superValue[superSize - 1] = '\0';

    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, superValue, (superSize - 1), "P6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(superValue);
}

void TestSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // yang模型现在修改为只能ID或PID为主键，只有list可以有PID+其他属性为主键
    if (numPID != 0) {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 预先插入数据
void TestInsertRoot(GmcConnT *conn, const char * vertexName, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodeProperty(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void TestInsertConChild(GmcConnT *conn, const char * vertexName, const char * nodeName, uint32_t keyvalue)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(rootNode, nodeName, GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    TestYangSetNodeProperty(childNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_root);
}

void TestInsertListChild(GmcConnT *conn, const char * vertexName, const char * nodeName, uint32_t initValue,
    uint32_t listNum, uint32_t isBigData = 0)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    if (isBigData == 1) {
        ret = TestBatchPrepareBig(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < listNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, nodeName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (isBigData == 1) {
            TestYangSetNodePropertyBigData(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT((listNum + 1), data.totalNum);
    AW_MACRO_EXPECT_EQ_INT((listNum + 1), data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

void TestInsertYangMem(GmcConnT *conn, const char * vertexName, const char * nodeName, uint32_t initValue,
    uint32_t listNum, uint32_t isBigData = 0)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    TestInsertRoot(conn, vertexName, keyvalue);

    for (uint32_t i = 0; i < listNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(conn, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_root, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(stmt_child, nodeName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBigData(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        if (data.status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, data.status);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcBatchDestroy(batch);
            memset(&data, 0, sizeof(AsyncUserDataT));
            AW_FUN_Log(LOG_INFO, "actul insert yang records is:%d\n", i);

            // 回滚事务
            ret = TestTransRollBackAsync(conn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
            AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
            GmcBatchDestroy(batch);
            memset(&data, 0, sizeof(AsyncUserDataT));

            // 提交事务
            ret = TestTransCommitAsync(conn);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

void TestInsertListChildFail(GmcConnT *conn, const char * vertexName, const char * nodeName, uint32_t initValue,
    uint32_t listNum)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < listNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child, nodeName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status == GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
        AW_MACRO_EXPECT_EQ_INT((listNum + 1), data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
        AW_MACRO_EXPECT_EQ_INT((listNum + 1), data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    }
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

void TestInsertHuaweiAcl(GmcConnT *conn)
{
    int ret;
    int i = 0;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child[10] = {0};
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode[10] = {0};

    GmcTxConfigT g_mSTrxConfig;
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(conn, &stmt_child[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "huawei-acl:acl", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    ret = GmcYangEditChildNode(rootNode, "groups", GMC_OPERATION_INSERT, &childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    uint32_t listNum = 128;
    for (i = 0; i < listNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child[1], "huawei-acl:acl::groups::group", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child[1], &childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置identity
        uint32_t pkValue = 2000 + i;
        TestYangSetIdentity(childNode[1], pkValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(childNode[1], "rule-basics", GMC_OPERATION_INSERT, &childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int j = 0; j < 4; j++) {
            // 设置child节点
            ret = testGmcPrepareStmtByLabelName(stmt_child[2], "huawei-acl:acl::groups::group::rule-basics::rule-basic",
                GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt_child[1], stmt_child[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_child[2], &childNode[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            TestYangSetRuleBasic(childNode[2], j, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_child[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (i = 0; i < listNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_child[1], "huawei-acl:acl::groups::group", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child[1], &childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置identity
        uint32_t pkValue = 3000 + i;
        TestYangSetIdentity(childNode[1], pkValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(childNode[1], "rule-advances", GMC_OPERATION_INSERT, &childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int j = 0; j < 4; j++) {
            // 设置child节点
            ret = testGmcPrepareStmtByLabelName(
                stmt_child[2], "huawei-acl:acl::groups::group::rule-advances::rule-advance", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt_child[1], stmt_child[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_child[2], &childNode[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            TestYangSetRuleAdvance(childNode[2], j, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置child节点
            ret = GmcYangEditChildNode(childNode[2], "protocol-type", GMC_OPERATION_INSERT, &childNode[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode[3], "single", GMC_OPERATION_INSERT, &childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestYangSetProtocol(childNode[4], j, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置child节点
            ret = GmcYangEditChildNode(childNode[2], "source", GMC_OPERATION_INSERT, &childNode[5]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode[5], "ip", GMC_OPERATION_INSERT, &childNode[6]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestYangSetSrcIP(childNode[6], j, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置child节点
            ret = GmcYangEditChildNode(childNode[2], "dest", GMC_OPERATION_INSERT, &childNode[7]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode[7], "ip", GMC_OPERATION_INSERT, &childNode[8]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestYangSetDstIP(childNode[8], j, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_child[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT((listNum * 10 + 1), data.totalNum);
    AW_MACRO_EXPECT_EQ_INT((listNum * 10 + 1), data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(stmt_child[i]);
    }
}

// remove root
void TestRemoveRoot(GmcConnT *conn, const char * vertexName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

void TestRemoveListYangMem(GmcConnT *conn, const char * vertexName, const char * nodeName, uint32_t initValue,
    uint32_t listNum)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < listNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(conn, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = TestBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_root, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(stmt_child, nodeName, GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        uint32_t pid = 1;
        TestSetKeyNameAndValue(stmt_child, fieldValue, pid, true);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = TestTransCommitAsync(conn);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
}

// insert Vertex
void TestInsertVertexMem(GmcStmtT *stmt)
{
    int ret = 0;
    uint32_t i = 0;
    uint32_t value = 0;
    while (ret == 0) {
        // 打开Vertex Label
        ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_big", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写数据
        value = i;
        ret = GmcSetVertexProperty(stmt, "PK", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 写string数据
        uint32_t superSize = 1024;
        char *superValue = (char *)malloc(superSize);
        (void)memset(superValue, 'B', (superSize - 1));
        superValue[superSize - 1] = '\0';

        ret = GmcSetVertexProperty(stmt, "P0", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P1", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P2", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P3", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P4", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P5", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "P6", GMC_DATATYPE_STRING, superValue, (superSize - 1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        free(superValue);

        ret = GmcExecute(stmt);

        i++;
        if ((i % 50000) == 0) {
            AW_FUN_Log(LOG_INFO, "till now:insert records %d\n", i);
        }
    }
    AW_FUN_Log(LOG_INFO, "actul insert records is:%d\n", (i - 1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// delete Vertex
void TestDeleteVertexMem(GmcStmtT *stmt)
{
    int ret;
    uint32_t i = 0;
    uint32_t value = 0;

    // scan vertex
    ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_big", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_FUN_Log(LOG_INFO, "scan vertex, count is:%d\n", cnt);

    // delete vertex
    for (i = 0; i < cnt; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_big", GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        value = i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 删除
        ret = GmcSetIndexKeyName(stmt, "Vertex_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询表记录数
    uint64_t count;
    ret = testGmcPrepareStmtByLabelName(stmt, "Vertex_big", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
}


/**************************************sub-func********************************************/


/**************************************flow-func********************************************/
#define TIMEOUT 100
typedef struct {
    bool wait;
    uint32_t waitTime = 0;
    uint32_t count = 0;
} FlowCtrCallDataT;

// 推送回调中设置等待以构造数据挤压
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    FlowCtrCallDataT *para = (FlowCtrCallDataT *)userData;

    if (para->wait) {
        sleep(para->waitTime);
        para->wait = false;
    }
    para->count++;
}

bool g_waitFlag = false;
uint32_t g_waitTimes = 0;
void CreateLabelCallbackWait(void *userData, int32_t status, const char *errMsg)
{
    while (g_waitFlag) {
        sleep(1);
        g_waitTimes++;
        AW_FUN_Log(LOG_INFO, "g_waitTimes: %d\n", g_waitTimes);
    }

    if (userData) {
        AsyncUserDataT *userDataGet = (AsyncUserDataT *)userData;
        userDataGet->status = status;
        userDataGet->historyRecvNum++;
        if (userDataGet->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(userDataGet->lastError, errMsg);
                AW_FUN_Log(LOG_INFO, "errMsg: %s\n", errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", userDataGet->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userDataGet->recvNum++;
    }
}

void BatchExecuteCallbackWait(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    while (g_waitFlag) {
        sleep(1);
        g_waitTimes++;
        AW_FUN_Log(LOG_INFO, "g_waitTimes: %d\n", g_waitTimes);
    }

    if (userData) {
        AsyncUserDataT *userDataGet = (AsyncUserDataT *)userData;
        userDataGet->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(userDataGet->totalNum), (uint32_t *)&(userDataGet->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        userDataGet->historyRecvNum++;
        if (userDataGet->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(userDataGet->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_FUN_Log(LOG_INFO, "expect lastError: %s,  actual: null\n", userDataGet->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        userDataGet->recvNum++;
        if (userDataGet->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            // 结果检查
            AW_MACRO_EXPECT_EQ_INT(userDataGet->expectedErrorCode, msg.errorCode);
            EXPECT_STREQ(userDataGet->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(userDataGet->expectedErrPath, msg.errorPath);
        }
    }
}

/**************************************config online********************************************/


/**************************************Thread Functions********************************************/
void GetEdgeNum(char yangName[221][1024])
{
    char cmdOutput[1024] = {0};
    char cmd[221][1024] = {0};
    int length = 0;
    for (uint32_t i = 1; i <= 221; i++) {
        (void)snprintf(cmd[i], 1024, "sed -n '%dp' ./edgeName.txt", i);
    }
    for (uint32_t i = 1; i <= 221; i++) {
        FILE *pf = popen(cmd[i], "r");
        if (pf == NULL) {
            AW_FUN_Log(LOG_INFO, "popen(%s) error.\n", cmd);
        }
        while (NULL != fgets(cmdOutput, 1024, pf)) {
            strcpy(yangName[i], cmdOutput);
        }
        length = strlen(yangName[i]);
        yangName[i][length - 1] = '\0';
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        pf = NULL;
    }
}
void GetVertexNum(char yangName[275][1024])
{
    char cmdOutput[1024] = {0};
    char cmd[275][1024] = {0};
    int length = 0;
    for (uint32_t i = 1; i <= 275; i++) {
        (void)snprintf(cmd[i], 1024, "sed -n '%dp' ./vertexName.txt", i);
    }
    for (uint32_t i = 1; i <= 275; i++) {
        FILE *pf = popen(cmd[i], "r");
        if (pf == NULL) {
            AW_FUN_Log(LOG_INFO, "popen(%s) error.\n", cmd);
        }
        while (NULL != fgets(cmdOutput, 1024, pf)) {
            strcpy(yangName[i], cmdOutput);
        }
        length = strlen(yangName[i]);
        yangName[i][length - 1] = '\0';
        if (pclose(pf) == -1) {
            perror("pclose fail");
        }
        pf = NULL;
    }
}

// 获取表名称
char *Fun(const char *str)
{
    char newStr[100] = "";
    AW_FUN_Log(LOG_STEP, "%s\n", str);
    int i = 1;
    int j = 0;
    while (str[i] != '\n') {
        if (str[i] != ' ' and str[i] != '.') {
            newStr[j] = str[i];
            j++;
        }
        i += 1;
    }
    char *tmp = (char *)malloc(100 * sizeof(char));
    if (tmp == NULL) {
        EXPECT_NE((void *)NULL, tmp);
    }
    tmp[0] = newStr[0];
    AW_FUN_Log(LOG_STEP, "newStr = %s\n", newStr);
    strcpy(tmp, newStr);
    AW_FUN_Log(LOG_STEP, "tmp = %s\n", newStr);
    return tmp;
}

// 删vertexlabel
int DeleteVertexLabelR21(GmcStmtT *stmt)
{
    // 查看视图
    char const *viewName = "V\\$STORAGE_VERTEX_COUNT";
    char cmd[256] = "\n";
    (void)snprintf(cmd, 256, "%s/gmsysview -s %s -q %s -ns %s", g_toolPath, g_connServer, viewName, g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "%s\n", cmd);

    FILE *fin = popen(cmd, "r");
    char out[4000] = {0};
    int ret = 0;
    char *labelname = {0};
    char *pos = {0};
    int count = 0;
    AW_FUN_Log(LOG_STEP, "deleteVertexLabel\n");
    while (fgets(out, 4000 - 1, fin) != NULL) {
        if (strstr(out, "gmdbtest") != NULL) {
            pos = strstr(out, ".");
            labelname = (char *)Fun(pos);
            // testforward和if表是简化长稳中的表，可靠性叠加长稳的时候不可以删除这两张表
            if (strcmp(labelname, "testforward") != 0 and strcmp(labelname, "if") != 0) {
                AW_FUN_Log(LOG_STEP, "labelname = %s\n", labelname);
                ret = GmcDropVertexLabel(stmt, labelname);
                if (ret == GMERR_UNDEFINED_TABLE) {
                    count++;
                } else {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                }
            }
            free(labelname);
        }
    }
    GmcDropVertexLabel(stmt, "frm_svc");
    pclose(fin);
    memset(cmd, 0, sizeof(cmd));
    // iot设备原有56张表无法删除
    if (count > 179) {
        AW_FUN_Log(LOG_ERROR, "count is %d", count);
        return -1;
    }
    return ret;
}

void *ThreadImportR21(void *args)
{
    int ret;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;

    // 建立链接
    ret = TestYangGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 部分表重名，530张实际只能导入525张，只导入不清除类型
    char cmd[MAX_CMD_SIZE];
    char schemaFile[128] = "$TEST_HOME/schema_file/r21_ndb/gmjson/";
    ret = snprintf(cmd, MAX_CMD_SIZE, "%s/gmimport -c cache -f %s -ns %s -s %s", g_toolPath, schemaFile,
        g_testNameSpace, g_connServer);
    EXPECT_LT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", cmd);
    system(cmd);

    sleep(5);

    // 删除r21的表
    ret = DeleteVertexLabelR21(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开链接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadCreateYangFull(void *args)
{
    AsyncUserDataT data = {0};
    GmcConnT *g_conn_async[10] = {0};
    GmcStmtT *g_stmt_async[10] = {0};
    GmcTxConfigT g_mSTrxConfig;
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int n = 0;
    int ret = 0;
    char namespaceName[1024] = {0};

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    for (n = 0; n < 10; n++) {
        ret = TestYangGmcConnect(&g_conn_async[n], &g_stmt_async[n], GMC_CONN_TYPE_ASYNC);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 异步创建namespace
        ret = sprintf(namespaceName, "Namespace_%d", n);
        EXPECT_LT(0, ret);
        TestCreateNamespace(g_stmt_async[n], namespaceName, g_namespaceUserName);
        TestUseNamespace(g_stmt_async[n], namespaceName);
    }

    char edgeName[221][1024] = {0};
    char vertexName[275][1024] = {0};
    GetEdgeNum(edgeName);
    GetVertexNum(vertexName);
    for (n = 0; n < 10; n++) {
        ret = sprintf(namespaceName, "Namespace_%d", n);
        EXPECT_LT(0, ret);
        AW_FUN_Log(LOG_INFO, "namespace %s create table start.\n", namespaceName);
        readJanssonFile("schema_file/SOHO_S380_VertexLabel.gmjson", &vertexSchema);
        EXPECT_NE((void *)NULL, vertexSchema);
        ret = GmcCreateVertexLabelAsync(g_stmt_async[n], vertexSchema, g_msConfigTrans,
            create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
        free(vertexSchema);
        vertexSchema = NULL;

        readJanssonFile("schema_file/SOHO_S380_EdgeLabel.gmjson", &edgeSchema);
        EXPECT_NE((void *)NULL, edgeSchema);
        ret = GmcCreateEdgeLabelAsync(g_stmt_async[n], edgeSchema, g_msConfigTrans,
            create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
        free(edgeSchema);
        edgeSchema = NULL;

        // DML操作
        TestInsertHuaweiAcl(g_conn_async[n]);

        AW_FUN_Log(LOG_INFO, "namespace %s create table end.\n", namespaceName);
    }

    for (n = 0; n < 10; n++) {
        ret = sprintf(namespaceName, "Namespace_%d", n);
        EXPECT_LT(0, ret);
        AW_FUN_Log(LOG_INFO, "namespace %s drop table start.\n", namespaceName);

        ret = GmcClearNamespaceAsync(g_stmt_async[n], namespaceName, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        AW_FUN_Log(LOG_INFO, "namespace %s drop table end.\n", namespaceName);
    }

    for (n = 0; n < 10; n++) {
        ret = sprintf(namespaceName, "Namespace_%d", n);
        EXPECT_LT(0, ret);
        TestDropNamespace(g_stmt_async[n], namespaceName);

        ret = testGmcDisconnect(g_conn_async[n], g_stmt_async[n]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void *ThreadCreateYangSingle(void *args)
{
    AsyncUserDataT data = {0};
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcTxConfigT g_mSTrxConfig;
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret = 0;

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TestCreateNamespace(g_stmt_async, g_namespace, g_namespaceUserName);
    TestUseNamespace(g_stmt_async, g_namespace);

    char edgeName[221][1024] = {0};
    char vertexName[275][1024] = {0};
    GetEdgeNum(edgeName);
    GetVertexNum(vertexName);

    AW_FUN_Log(LOG_INFO, "namespace %s create table start.\n", g_namespace);
    readJanssonFile("schema_file/SOHO_S380_VertexLabel.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexSchema, g_msConfigTrans,
        create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(vertexSchema);
    vertexSchema = NULL;

    readJanssonFile("schema_file/SOHO_S380_EdgeLabel.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, edgeSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(edgeSchema);
    edgeSchema = NULL;

    // DML操作
    TestInsertHuaweiAcl(g_conn_async);
    AW_FUN_Log(LOG_INFO, "namespace %s create table end.\n", g_namespace);

    AW_FUN_Log(LOG_INFO, "namespace %s drop table start.\n", g_namespace);
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_INFO, "namespace %s drop table end.", g_namespace);

    TestDropNamespace(g_stmt_async, g_namespace);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadCreateYangSingleCycle(void *args)
{
    AsyncUserDataT data = {0};
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    GmcTxConfigT g_mSTrxConfig;
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret = 0;
    int i = 0;

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    for (i = 0; i < 50; i++) {
        ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC);
        BREAK_IFERR(ret);

        // 设置namespace级别为：可重复读 + 乐观事务
        GmcNspCfgT nspCfg;
        nspCfg.namespaceName = g_namespace;
        nspCfg.tablespaceName = NULL;
        nspCfg.userName = g_namespaceUserName;
        nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

        // 先尝试删除namespace
        ret = TryDropNameSpace(g_stmt_async, g_namespace);
        BREAK_IFERR(ret);

        ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
        BREAK_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        BREAK_IFERR(ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
        BREAK_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        BREAK_IFERR(ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        char edgeName[221][1024] = {0};
        char vertexName[275][1024] = {0};
        GetEdgeNum(edgeName);
        GetVertexNum(vertexName);

        AW_FUN_Log(LOG_INFO, "namespace %s create table start.\n", g_namespace);
        readJanssonFile("schema_file/SOHO_S380_VertexLabel.gmjson", &vertexSchema);
        EXPECT_NE((void *)NULL, vertexSchema);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexSchema, g_msConfigTrans,
            create_vertex_label_callback, &data);
        BREAK_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        BREAK_IFERR(ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        free(vertexSchema);
        vertexSchema = NULL;

        readJanssonFile("schema_file/SOHO_S380_EdgeLabel.gmjson", &edgeSchema);
        EXPECT_NE((void *)NULL, edgeSchema);
        ret = GmcCreateEdgeLabelAsync(g_stmt_async, edgeSchema, g_msConfigTrans, create_vertex_label_callback, &data);
        BREAK_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        BREAK_IFERR(ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        free(edgeSchema);
        edgeSchema = NULL;

        // DML操作
        TestInsertHuaweiAcl(g_conn_async);
        AW_FUN_Log(LOG_INFO, "namespace %s create table end.\n", g_namespace);

        AW_FUN_Log(LOG_INFO, "namespace %s drop table start.\n", g_namespace);
        ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        AW_FUN_Log(LOG_INFO, "namespace %s drop table end.\n", g_namespace);

        ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
        BREAK_IFERR(ret);
        ret = testWaitAsyncRecv(&data);
        BREAK_IFERR(ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        BREAK_IFERR(ret);
    }
}

void *ThreadCreateVertexCycle(void *args)
{
    int ret;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    system("sh create_multi_label_normal.sh 1000");

    char labelName[1024];
    char schemaPath[1024];
    char *schemaJson = NULL;

    uint32_t tableNum = 1000;
    for (uint32_t j = 0; j < 100; j++) {
        // 建立链接
        ret = TestYangGmcConnect(&g_conn, &g_stmt);
        BREAK_IFERR(ret);

        for (uint32_t i = 0; i < tableNum; i++) {
            ret = sprintf(labelName, "Vertex_%d", i);
            EXPECT_LT(0, ret);
            ret = sprintf(schemaPath, "./multi_vertexlabel/Vertex_%d.gmjson", i);
            EXPECT_LT(0, ret);
            ret = TryCreateVertexLabel(g_stmt, schemaPath, g_msConfigFast, labelName);
            BREAK_IFERR(ret);
        }

        for (uint32_t i = 0; i < tableNum; i++) {
            ret = sprintf(labelName, "Vertex_%d", i);
            EXPECT_LT(0, ret);
            ret = GmcDropVertexLabel(g_stmt, labelName);
            BREAK_IFERR(ret);
        }

        // 断开链接
        ret = testGmcDisconnect(g_conn, g_stmt);
        BREAK_IFERR(ret);
    }

    system("rm -rf multi_vertexlabel/");
}

void *ThreadCreateVertex100Client(void *args)
{
    uint32_t cycle = 0;
    if (g_envType == 0) {
        cycle = 5;
    } else {
        cycle = 1;
    }

    for (uint32_t i = 0; i < cycle; i++) {
        system("./rel_yang_007_01 ");
    }
}

void *ThreadCreateYang100Client(void *args)
{
    uint32_t cycle = 0;
    if (g_envType == 0) {
        cycle = 5;
    } else {
        cycle = 1;
    }

    for (uint32_t i = 0; i < cycle; i++) {
        system("./rel_yang_007_02 ");
    }
}

void *ThreadProcessSuspended(void *args)
{
    // 服务端挂起
    sleep(1);
    AW_FUN_Log(LOG_INFO, "===================ProcessSuspended start=================.\n");
    system("sh ./cfeTool.sh ProcessSuspended");

    sleep(6);
    // 服务端解挂
    system("sh ./cfeTool.sh CleanProcessSuspended");
    AW_FUN_Log(LOG_INFO, "====================ProcessSuspended end================.\n");
}

void *ThreadCreateYang100Client008(void *args)
{
    uint32_t cycle = 0;
    if (g_envType == 0) {
        cycle = 5;
    } else {
        cycle = 1;
    }

    for (uint32_t i = 0; i < cycle; i++) {
        system("./rel_yang_008_02 ");
    }
}

void *ThreadClientSuspended(void *args)
{
    // 服务端挂起
    sleep(2);
    system("sh ./cfeTool.sh ClientSuspended");
    AW_FUN_Log(LOG_INFO, "===================ClientSuspended start=================.\n");

    sleep(5);
    // 服务端解挂
    system("sh ./cfeTool.sh CleanClientSuspended");
    AW_FUN_Log(LOG_INFO, "====================ClientSuspended end================.\n");
}

void *ThreadCreateVertexAsync(void *args)
{
    system("./rel_yang_010_01 ");
}

void *ThreadCreateYangQueue(void *args)
{
    system("./rel_yang_010_02 ");
}

void *ThreadClientSuspended011(void *args)
{
    // 服务端挂起
    sleep(60);
    system("sh ./cfeTool.sh ClientSuspended010");
    AW_FUN_Log(LOG_INFO, "===================ClientSuspended010 start=================.\n");

    sleep(60);
    // 服务端解挂
    system("sh ./cfeTool.sh CleanClientSuspended010");
    AW_FUN_Log(LOG_INFO, "====================ClientSuspended010 end================.\n");
}

void *ThreadCreateYang014(void *args)
{
    int ret = 0;
    int i = 0;
    AsyncUserDataT data = {0};
    char *vertexSchema = NULL;
    char rootName[1024] = {0};
    char listName[1024] = {0};
    char schemaPath[1024];

    uint32_t connNum = *((uint32_t *)args);
    GmcConnT *g_conn_async[connNum] = {0};
    GmcStmtT *g_stmt_async[connNum] = {0};

    for (i = 0; i < connNum; i++) {
        ret = TestYangGmcConnect(&g_conn_async[i], &g_stmt_async[i], GMC_CONN_TYPE_ASYNC);
        if ((ret == GMERR_TOO_MANY_CONNECTIONS) || (ret == GMERR_INSUFFICIENT_PRIVILEGE)) {
            AW_FUN_Log(LOG_INFO, "Yang Connect fail, conn_id is %d, ret is %d.", i, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "Yang Connect succ, conn_id is %d.", i);
            TestUseNamespace(g_stmt_async[i], g_namespace);
        }
    }

    for (i = 0; i < connNum; i++) {
        if (g_stmt_async[i] == NULL) {
            break;
        }

        // 创建表
        ret = sprintf(schemaPath, "./multi_vertexlabel/yang_tree_%d.gmjson", i);
        EXPECT_LT(0, ret);
        readJanssonFile(schemaPath, &vertexSchema);
        EXPECT_NE((void *)NULL, vertexSchema);

        ret = GmcCreateVertexLabelAsync(g_stmt_async[i], vertexSchema, g_msConfigTrans,
            create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
        free(vertexSchema);
        vertexSchema = NULL;

        ret = sprintf(rootName, "root_%d", i);
        EXPECT_LT(0, ret);
        ret = sprintf(listName, "list_%d", i);
        EXPECT_LT(0, ret);

        // 删除表
        ret = GmcDropVertexLabelAsync(g_stmt_async[i], rootName, drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = GmcDropVertexLabelAsync(g_stmt_async[i], listName, drop_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    for (i = 0; i < connNum; i++) {
        ret = testGmcDisconnect(g_conn_async[i], g_stmt_async[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "Yang DisConnect succ, conn_id is %d.", i);
    }
}

void *ThreadCreateVertex014(void *args)
{
    int ret;
    int i = 0;
    char labelName[1024];
    char schemaPath[1024];
    char *schemaJson = NULL;

    uint32_t connNum = *((uint32_t *)args);
    GmcConnT *g_conn[connNum] = {0};
    GmcStmtT *g_stmt[connNum] = {0};

    for (i = 0; i < connNum; i++) {
        // 建立链接
        ret = TestYangGmcConnect(&g_conn[i], &g_stmt[i]);
        if ((ret == GMERR_TOO_MANY_CONNECTIONS) || (ret == GMERR_INSUFFICIENT_PRIVILEGE)) {
            AW_FUN_Log(LOG_INFO, "Vertex Connect fail, conn_id is %d, ret is %d.", i, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_FUN_Log(LOG_INFO, "Vertex Connect succ, conn_id is %d.", i);
        }
    }

    for (i = 0; i < connNum; i++) {
        if (g_stmt[i] == NULL) {
            break;
        }

        // 创建表
        ret = sprintf(schemaPath, "./multi_vertexlabel/Vertex_%d.gmjson", i);
        EXPECT_LT(0, ret);
        readJanssonFile(schemaPath, &schemaJson);
        EXPECT_NE((void *)NULL, schemaJson);

        ret = GmcCreateVertexLabel(g_stmt[i], schemaJson, g_msConfigFast);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(schemaJson);
        schemaJson = NULL;

        // 删除表
        ret = sprintf(labelName, "Vertex_%d", i);
        EXPECT_LT(0, ret);
        ret = GmcDropVertexLabel(g_stmt[i], labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (i = 0; i < connNum; i++) {
        // 断开链接
        ret = testGmcDisconnect(g_conn[i], g_stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, "Vertex DisConnect succ, conn_id is %d.", i);
    }
}

void *ThreadYangDML015(void *args)
{
    int ret = 0;
    int i, j;
    AsyncUserDataT data = {0};
    uint32_t fieldValue = 100;

    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;

    YangConnOptionT connOptionsT;
    connOptionsT.msgWriteTimeout = 1;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptionsT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUseNamespace(g_stmt_async, g_namespace);
    AW_FUN_Log(LOG_INFO, "Yang DML start.");

    // insert root
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // insert list
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;

    ret = GmcAllocStmt(g_conn_async, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    g_waitFlag = true;
    uint32_t listNum = 10000;
    for (i = 0; i < listNum; i++) {
        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(stmt_root, "root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_root, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(stmt_child, "list", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i * listNum + j;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, BatchExecuteCallbackWait, &data);
        if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            AW_FUN_Log(LOG_INFO, "GMERR_CONNECTION_SEND_BUFFER_FULL, Execute count = %d\n", i);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        GmcBatchDestroy(batch);
    }

    if (g_envType != 2) {
        sleep(15);
        g_waitFlag = false;
        ret = testWaitAsyncRecv(&data, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "data.totalNum = %d\n", data.totalNum);
        AW_FUN_Log(LOG_INFO, "data.succNum = %d\n", data.succNum);
        sleep(5);
    } else {
        sleep(5);
        g_waitFlag = false;
        ret = testWaitAsyncRecv(&data, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "data.totalNum = %d\n", data.totalNum);
        AW_FUN_Log(LOG_INFO, "data.succNum = %d\n", data.succNum);
        sleep(2);
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);

    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "Yang DML end.");
}

#endif /* REL_YANG_TEST_H */
