/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: rel_yang_FMEA
 * Author: liaoxiang
 * Create: 2023-05-06
 * Description: 用例需要使用CFE工具，把工具拷贝到root\CFE_Tool,然后chmod 777 -R  CFE_Tool加权限
 */
#include "rel_yang_trx.h"

class rel_yang_FMEA : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void rel_yang_FMEA::SetUpTestCase()
{
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void rel_yang_FMEA::TearDownTestCase()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void rel_yang_FMEA::SetUp()
{}

void rel_yang_FMEA::TearDown()
{}

/*****************************************************************************
 Description  : 001.内存资源不足
1.通过GmcConnOptionsSetConnMemCtxLimit设置连接的动态内存上限
2.批量写入超大对象数据
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_001)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    connOptions.connMemCtxLimit = 4; // 设置连接的动态内存上限
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // create vertexlabel
    CreateYangVertexLabel(g_stmt_async, g_conListVertexPath, g_conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    /******** 预置数据 *****************/
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 1; i++) {
        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child的属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    }

    // 提交批处理
    BatchExecute(batch, 2, 2);

    // diff查询（因连接内存太小，若数据报文太大，则查询diff失败）
    AsyncUserDataT userData = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diff/diff_over_dyn.json", &diffreply01);
    std::vector<std::string> reply(2);
    reply[0] = diffreply01;

    testFetchAndDeparseDiff(g_stmt_root, batch, reply, userData);

    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i < 100; i++) {
        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child的属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        if (ret == GMERR_MEMORY_OPERATE_FAILED) {
            testGmcGetLastError();
            AW_FUN_Log(LOG_INFO, "插入的数据超过连接的动态内存上限\n");
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 提交批处理
    BatchExecute(batch, 30, 30);

    // 提交事务
    TransCommit(g_conn_async);

    // drop
    DropYangVertexLabel(g_stmt_async, g_graphConListEdgeName, g_graphConListRootName, g_graphConListChildName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    free(diffreply01);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 002.将内存上限设为2M, 写入6M的数据
超大规格表，写入超过上限的数据，并开启事务，批次超大报文请求，diff操作、查询subtree，回滚。
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_002)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;
    const char *vertexLabelPath = "schemafile/fields_1023_schema.gmjson";
    const char *vertexName = "fields_1023_schema";

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    connOptions.connMemCtxLimit = 2; // 设置连接的动态内存上限
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // create vertexlabel
    CreateVertexLabel(g_stmt_async, vertexLabelPath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // create sp
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, vertexName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 通过stmt句柄拿到Treenode
    GmcNodeT *vertexLabel7Node = NULL;
    ret = GmcGetRootNode(g_stmt_async, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testYangFiled1023(g_stmt_async, 1, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // diff查询
    AsyncUserDataT userData = {0};
    testFetchAndDeparseDiff(g_stmt_root, batch, expectDiffFiled1023, userData);

    // SubTree 查询 -- 容器过滤
    const char *g_conNodeListReplyPath1 = "subtreefile/filed1023.json";
    subTreeNode(g_stmt_async, g_conNodeListReplyPath1, g_conNodeListFilterPath, vertexName);

    // rollback sp
    RollbackSavepoint(g_conn_async, g_savepointName);

    // diff查询
    testFetchAndDeparseDiff(g_stmt_root, batch, expectDiffNullBase, userData);

    // 提交事务
    TransCommit(g_conn_async);

    // drop vertex
    DropVertexLabel(g_stmt_async, vertexName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 003.“超大规格数据写”，需进一步优化用例，超限后持续新增（list元素）/覆盖（索引值变化）
1.设置yang list表记录数为最大值9223372036854775807，并写入对应数量数据
2.删除数据，然后重复写入
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_003)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    // 关闭长事务监控
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    TestStartServer();

    int ret = 0, fieldValue = 0, newvalue = 0;
    GmcBatchT *batch;
    AsyncUserDataT userData = {0};

    const char *sohoS380VertexLabelPath = "schemafile/SOHO_S380_VertexLabel.json";
    const char *sohoS380EdgePath = "schemafile/SOHO_S380_EdgeLabel.json";
    const char *vertexLabel7 = "huawei-acl:acl";
    const char *vertexLabel8 = "huawei-acl:acl::ip-pools::ip-pool";
    const char *vertexLabel9 = "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr";
    const char *vertexLabel10 = "huawei-acl:acl::port-pools::port-pool";
    const char *vertexLabel11 = "huawei-acl:acl::port-pools::port-pool::ports::port";
    const char *vertexLabel12 = "huawei-acl:acl::groups::group";
    const char *vertexLabel13 = "huawei-acl:acl::groups::group::rule-basics::rule-basic";
    const char *vertexLabel14 = "huawei-acl:acl::groups::group::rule-advances::rule-advance";
    const char *vertexLabel15 = "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet";
    const char *vertexLabel16 = "ietf-yang-library:yang-library";

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    connOptions.requestTimeout = 300000;
    connOptions.msgReadTimeout = 300000;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // create s380
    CreateYangS380VertexLabel(g_stmt_async, sohoS380VertexLabelPath, sohoS380EdgePath);

    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;

    // 开启乐观事务
    TransStart(g_conn_async);
    TestYangAllocAllstmt(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#ifdef ENV_RTOSV2X
    int cycle = 1;
#else
    int cycle = 2;
#endif
    for (int j = 0; j < cycle; j++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, vertexLabel12, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
        char namevalue3[20];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 20, "identity%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "identity",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char namevalue4[20];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)snprintf(namevalue4, 20, "type%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue4,
            (strlen(namevalue4)),
            "type",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t step = j;
        ret = testYangSetField(
            g_stmt_sync_12, GMC_DATATYPE_UINT32, &step, sizeof(uint32_t), "step", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char namevalue6[20];
        memset(namevalue6, 0, sizeof(namevalue6));
        (void)snprintf(namevalue6, 20, "description%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue6,
            (strlen(namevalue6)),
            "description",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t number = j;
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_UINT32,
            &number,
            sizeof(uint32_t),
            "number",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_INSERT, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_INSERT, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_INSERT, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    // 提交批处理
#ifdef ENV_RTOSV2X
    BatchExecute(batch, 2, 2);
#else
    BatchExecute(batch, 3, 3);
#endif

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    int k = 0;
    while(1) {
        // 设置vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)snprintf(namevalue1, 10, "identity%d", 0);
        ret = GmcSetIndexKeyValue(g_stmt_sync_12, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(g_stmt_sync_12, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_NONE, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_NONE, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_NONE, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (int l = 2000 + (2000 * k); l < 4000 + (2000 * k); l++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));

            (void)snprintf(namevalue5, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (l % 10000 == 0) {
                AW_FUN_Log(LOG_INFO, "成功写入 %d 条数据\n", l -2000);
            }
        }

        k++;
        
        if (k % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "第 %d 次循环写\n", k);
        }

        // 提交批处理
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testWaitAsyncRecv(&userData);
        if(userData.status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, userData.status);
            sleep(1);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
    }

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建savepoint2失败
    CreateSavepoint(g_conn_async, g_savepointName2, GMERR_TRANSACTION_ROLLBACK);

    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 释放savepoint
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 创建savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, vertexLabel7, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 groups
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_REPLACE_GRAPH, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 ip-pools
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_REPLACE_GRAPH, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建child节点 port-pools
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_REPLACE_GRAPH, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int j = 0; j < 2; j++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, vertexLabel12, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
        char namevalue3[20];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 20, "identity%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "identity",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char namevalue4[20];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)snprintf(namevalue4, 20, "type%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue4,
            (strlen(namevalue4)),
            "type",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t step = j;
        ret = testYangSetField(
            g_stmt_sync_12, GMC_DATATYPE_UINT32, &step, sizeof(uint32_t), "step", GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char namevalue6[20];
        memset(namevalue6, 0, sizeof(namevalue6));
        (void)snprintf(namevalue6, 20, "description%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue6,
            (strlen(namevalue6)),
            "description",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t number = j;
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_UINT32,
            &number,
            sizeof(uint32_t),
            "number",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_REPLACE_GRAPH, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_REPLACE_GRAPH, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_REPLACE_GRAPH, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 释放savepoint2
    ReleaseSavepoint(g_conn_async, g_savepointName2);

    // 提交批处理
    BatchExecute(batch, 3, 3);

    sleep(1);
    // 提交事务
    TransCommit(g_conn_async);

    TestYangFreeAllstmt();

    // 异步清除命名空间下所有表数据以及表
    sleep(1);
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/start.sh");

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 004.内存资源不足
1.创建savepoint, 重复更新同一条数据, 主键不变, 别的索引反复变, undo空间, heap空间都不会膨胀, 只有索引的空间会膨胀，通过视图查询
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_004)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    // 关闭长事务监控
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    TestStartServer();

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;
    const char *conListVertexPath = "schemafile/Con_List_Local.gmjson";
    const char *conListEdgePath = "schemafile/Con_List_Local_Edge.gmjson";
    const char *conListRootName = "Con_List_Root_Local";
    const char *conListChileName = "Con_List_Child_Local";
    const char *conListEdgeName = "Con_List_Local_Edge";

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // 创建 con-list 表
    CreateYangVertexLabel(g_stmt_async, conListVertexPath, conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // create sp
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, conListRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, conListChileName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // diff查询
    AsyncUserDataT userData = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diff/diff_list_localhash.json", &diffreply01);
    std::vector<std::string> reply(2);
    reply[0] = diffreply01;

    testFetchAndDeparseDiff(g_stmt_root, batch, reply, userData);

    // 视图 V$STORAGE_HASH_INDEX_STAT 查询list_localhash
    char const *viewName = "V\\$STORAGE_HASH_INDEX_STAT";
    char cmd[1024];
    int32_t beforeReplaceCnt = 0;
    (void)snprintf(cmd, 1024,
        "%s/gmsysview -s %s -q %s -f LABEL_NAME=Con_List_Root_Local | grep USED_MEM_SIZE |awk '{print $2}'", g_toolPath,
        g_connServer, viewName); 
    ret = system(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetResultCommand(cmd, &beforeReplaceCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#ifdef ENV_RTOSV2X
    int endNum = 1000;
#else
    int endNum = 100000;
#endif

    // 重复更新同一条数据, 主键不变, 别的索引反复变
    for (int j = 0; j < endNum; j++) {
        // 设置批处理
        ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, conListRootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (uint32_t i = 0; i < 10; i++) {
            // 设置child节点
            ret = testGmcPrepareStmtByLabelName(g_stmt_list, conListChileName, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置child的属性值
            fieldValue = i;
            testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
            newvalue = i + (30000 * (j + 1));
            testYangSetVertexPropertyWithoutF0(g_stmt_list, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 提交批处理
        BatchExecute(batch, 11, 11);

        if (j % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "第 %d 次循环replace\n", j);
        }
    }

    ret = system(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t afterReplaceCnt = 0;
    ret = TestGetResultCommand(cmd, &afterReplaceCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "before beforeReplaceCnt is %d", beforeReplaceCnt);
    AW_FUN_Log(LOG_DEBUG, "after afterReplaceCnt is %d", afterReplaceCnt);
    AW_MACRO_EXPECT_EQ_INT(0, afterReplaceCnt - beforeReplaceCnt);

    // 提交事务
    TransCommit(g_conn_async);

    // drop vertex
    DropYangVertexLabel(g_stmt_async, conListEdgeName, conListRootName, conListChileName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/start.sh");

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 005.List节点数据量大，且非唯一索引冲突量大
1.list节点包含hash索引，插入大量数据，hash索引冲突高
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_005)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    // 关闭长事务监控
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=0\"");
    system("gmadmin -cfgName pageSize");
    TestStartServer();

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;
    const char *conListVertexPath = "schemafile/Con_List_Local.gmjson";
    const char *conListEdgePath = "schemafile/Con_List_Local_Edge.gmjson";
    const char *conListRootName = "Con_List_Root_Local";
    const char *conListChileName = "Con_List_Child_Local";
    const char *conListEdgeName = "Con_List_Local_Edge";

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // 创建 con-list 表
    CreateYangVertexLabel(g_stmt_async, conListVertexPath, conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, conListRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, conListChileName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // diff查询
    AsyncUserDataT userData = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diff/diff_list_localhash.json", &diffreply01);
    std::vector<std::string> reply(2);
    reply[0] = diffreply01;

    testFetchAndDeparseDiff(g_stmt_root, batch, reply, userData);

    // create sp
    CreateSavepoint(g_conn_async, g_savepointName);

    int j = 10;
    while(1) {
        // 设置批处理
        ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, conListRootName, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, conListChileName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点属性值
        fieldValue = j;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 提交批处理
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        if (userData.status == GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(2, userData.totalNum);
            AW_MACRO_EXPECT_EQ_INT(2, userData.succNum);
            GmcBatchDestroy(batch);
            memset(&userData, 0, sizeof(AsyncUserDataT));
            j++;
        } else if (userData.status == GMERR_OUT_OF_MEMORY) {
            GmcBatchDestroy(batch);
            memset(&userData, 0, sizeof(AsyncUserDataT));
            AW_FUN_Log(LOG_INFO, "最终写入 %d 条数据\n", j);
            break;
        }

        if (j % 1000 == 0) {
            AW_FUN_Log(LOG_INFO, "对list写入 %d 条数据\n", j);
        }
    }

    // 视图 V$STORAGE_HASH_COLLISION_STAT 查询hash冲突
    char const *viewName = "V\\$STORAGE_HASH_COLLISION_STAT";
    char cmd[1024];
    snprintf(cmd, 1024, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, viewName);
    AW_FUN_Log(LOG_INFO, "cmd = %s\n", cmd);
    ret = system(cmd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Rollback sp
    RollbackSavepoint(g_conn_async, g_savepointName);

    // 提交事务
    sleep(1);
    TransCommit(g_conn_async);

    // drop vertex
    DropYangVertexLabel(g_stmt_async, conListEdgeName, conListRootName, conListChileName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/start.sh");
    if (g_envType != 2) {
        TestStopServer();
    }

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 006.盖超长事务（包含大量请求和耗时处理）
线程一：长事务，大数据量
线程二：大数据量
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_006)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // create vertexlabel
    CreateYangVertexLabel(g_stmt_async, g_conListVertexPath, g_conListEdgePath);

    pthread_t thr_long_trans;
    pthread_t multi[64];
    void *err[64];
    int index[64];

#ifdef ENV_RTOSV2X
    int threadNum = 5;
#else
    int threadNum = 60;
#endif

    // 并发 长事务
    pthread_create(&thr_long_trans, NULL, TestLongTrans, NULL);

    // 多线程，构建事务冲突
    for (int i = 0; i < threadNum; i++) {
        index[i] = i;
        pthread_create(&multi[i], NULL, TestTransConflict, (void *)&index[i]);
    }
    for (int i = 0; i < threadNum; i++) {
        pthread_join(multi[i], &err[i]);
    }

    pthread_join(thr_long_trans, NULL);

    // drop
    DropYangVertexLabel(g_stmt_async, g_graphConListEdgeName, g_graphConListRootName, g_graphConListChildName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.\n");
}

/*****************************************************************************
 Description  : 007.乐观事务：同一条数据重复插入20W次（没有删除操作），然后diff、SubTree查询
2.批量写入超大对象数据
 Author       : liaoxiang
*****************************************************************************/
TEST_F(rel_yang_FMEA, rel_yang_FMEA_007)
{
    AW_FUN_Log(LOG_STEP, "test start.\n");

    int ret = 0, fieldValue = 0, newvalue = 0, PID = 0;
    GmcBatchT *batch = NULL;

    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    // create vertexlabel
    CreateYangVertexLabel(g_stmt_async, g_conListVertexPath, g_conListEdgePath);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 同一条数据重复插入20W次（没有删除操作）
    for (int i = 0; i < 200000; i++) {
        // 设置批处理
        ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根的属性值
        fieldValue = i;
        testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 提交批处理
        BatchExecute(batch, 1, 1);

        if (i % 2000 == 0) {
            AW_FUN_Log(LOG_INFO, "第 %d 次重复写\n", i);
        }
    }

    // diff查询
    AsyncUserDataT userData = {0};
    char *diffreply01 = NULL;
    readJanssonFile("diff/diff_repeat_insert.json", &diffreply01);
    std::vector<std::string> reply(2);
    reply[0] = diffreply01;
    testFetchAndDeparseDiff(g_stmt_root, batch, reply, userData);

    // SubTree 查询 -- 容器过滤
    const char *g_conNodeListReplyPath1 = "subtreefile/RepeatInsert.json";
    subTreeNode(g_stmt_async, g_conNodeListReplyPath1, g_conNodeListFilterPath, g_graphConListRootName);

    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConListRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#ifdef ENV_RTOSV2X
    int endNum = 10;
#else
    int endNum = 478;
#endif

    for (uint32_t i = 0; i < endNum; i++) {
        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_graphConListChildName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child的属性值
        fieldValue = i;
        testYangSetVertexProperty_PK(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexPropertyWithoutF0(g_stmt_list, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, (endNum + 1), (endNum + 1));

    // 提交事务
    TransCommit(g_conn_async);

    // drop
    DropYangVertexLabel(g_stmt_async, g_graphConListEdgeName, g_graphConListRootName, g_graphConListChildName);

    // 删除namespace
    dropNameSpace(g_stmt_async, g_namespace);

    // 断连
    free(diffreply01);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.\n");
}


