/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: rel_yang_trx
 * Author: liaoxiang
 * Create: 2023-05-15
 */
#include "rel_yang_trx.h"

// 客户端进程1操作vertext表，循环执行读写操作
int main()
{
    GmcConnT *conn_async1 = NULL, *conn1 = NULL;
    GmcStmtT *stmt_async1 = NULL, *stmt1 = NULL;
    int ret = 0, fieldValue = 0, newvalue = 0;
    const char *labelname = "OP_T0";
    char pkName[] = "pk";
    const char *vertexLabelPath = "schemafile/NormalVertexLabel.gmjson";
    const char *namespaceName = (const char *)"user032";
    GmcBatchT *batch;
    AsyncUserDataT userData = {0};

    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &userData;

    GmcAsyncRequestDoneContextT mergeRequestCtx;
    mergeRequestCtx.mergeCb = merge_vertex_callback;
    mergeRequestCtx.userData = &userData;

    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_callback;
    updateRequestCtx.userData = &userData;

    GmcAsyncRequestDoneContextT replaceRequestCtx;
    replaceRequestCtx.replaceCb = replace_vertex_callback;
    replaceRequestCtx.userData = &userData;

    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &userData;

    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建连
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn_async1, &stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(stmt_async1, namespaceName);

    // use namespace
    useNameSpace(stmt_async1, namespaceName);

    // create vertexlabel
    CreateVertexLabel(stmt_async1, vertexLabelPath);

    // 开启乐观事务
    TransStart(conn_async1);

    // 循环执行dml操作
    for (int j = g_startNum; j < g_endNum; j++) {
        /*************** savepoint1 insert 操作 *******************/
        // craete savepoint1
        CreateSavepoint(conn_async1, g_savepointName);

        // insert
        for (int i = g_startNum; i < g_endNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_async1, labelname, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            set_VertexProperty_PK(stmt_async1, i);
            set_VertexProperty(stmt_async1, i);
            ret = GmcExecuteAsync(stmt_async1, &insertRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            EXPECT_EQ(GMERR_OK, userData.status);
            EXPECT_EQ(1, userData.affectRows);
            memset(&userData, 0, sizeof(AsyncUserDataT));

            // 读
            ret = GmcUseNamespace(stmt1, namespaceName); // 必须use，不然去默认namespace读数据
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            QueryVertexProperty(stmt1, i, labelname, pkName);
        }

        /*************** savepoint2 merge 操作 *******************/
        // craete savepoint2
        CreateSavepoint(conn_async1, g_savepointName2);

        // merge
        for (int i = g_startNum; i < g_endNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_async1, labelname, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t g_pk_value = i;
            ret = GmcSetIndexKeyName(stmt_async1, pkName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt_async1, 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            set_VertexProperty(stmt_async1, i + g_endNum);
            ret = GmcExecuteAsync(stmt_async1, &mergeRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            EXPECT_EQ(GMERR_OK, userData.status);
            EXPECT_EQ(2, userData.affectRows);
            memset(&userData, 0, sizeof(AsyncUserDataT));

            // 读
            ret = GmcUseNamespace(stmt1, namespaceName); // 必须use，不然去默认namespace读数据
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            QueryVertexProperty(stmt1, i + g_endNum, labelname, pkName);
        }

        /*************** savepoint3 replace 操作 *******************/
        // craete savepoint3
        CreateSavepoint(conn_async1, g_savepointName3);

        // replace
        for (int i = g_startNum; i < g_endNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_async1, labelname, GMC_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            set_VertexProperty_PK(stmt_async1, i);
            set_VertexProperty(stmt_async1, i + g_endNum * 2);
            ret = GmcExecuteAsync(stmt_async1, &replaceRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            EXPECT_EQ(GMERR_OK, userData.status);
            EXPECT_EQ(2, userData.affectRows);
            memset(&userData, 0, sizeof(AsyncUserDataT));

            // 读
            ret = GmcUseNamespace(stmt1, namespaceName); // 必须use，不然去默认namespace读数据
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            QueryVertexProperty(stmt1, i + g_endNum * 2, labelname, pkName);
        }

        /*************** savepoint4 update 操作 *******************/
        // craete savepoint4
        CreateSavepoint(conn_async1, g_savepointName4);

        // update
        for (int i = g_startNum; i < g_endNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_async1, labelname, GMC_OPERATION_UPDATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcSetIndexKeyValue(stmt_async1, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt_async1, pkName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            set_VertexProperty(stmt_async1, i + g_endNum * 3);
            ret = GmcExecuteAsync(stmt_async1, &updateRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            EXPECT_EQ(GMERR_OK, userData.status);
            EXPECT_EQ(1, userData.affectRows);
            memset(&userData, 0, sizeof(AsyncUserDataT));

            // 读
            ret = GmcUseNamespace(stmt1, namespaceName); // 必须use，不然去默认namespace读数据
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            QueryVertexProperty(stmt1, i + g_endNum * 3, labelname, pkName);
        }

        /*************** savepoint5 delete 操作 *******************/
        // craete savepoint5
        CreateSavepoint(conn_async1, g_savepointName5);

        // delete
        for (int i = g_startNum; i < g_endNum; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_async1, labelname, GMC_OPERATION_DELETE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 主键删除
            ret = GmcSetIndexKeyValue(stmt_async1, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt_async1, pkName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecuteAsync(stmt_async1, &deleteRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            EXPECT_EQ(GMERR_OK, userData.status);
            EXPECT_EQ(1, userData.affectRows);
            memset(&userData, 0, sizeof(AsyncUserDataT));
        }

        if (j % 10 == 0) {
            AW_FUN_Log(LOG_INFO, "vertex: 第 %d 次循环执行dml操作\n", j);
        }
    }

    // 提交事务
    TransCommit(conn_async1);

    // drop vertexlabel
    DropVertexLabel(stmt_async1, labelname);

    // 删除namespace
    dropNameSpace(stmt_async1, namespaceName);

    // 断连
    ret = testGmcDisconnect(conn_async1, stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}
