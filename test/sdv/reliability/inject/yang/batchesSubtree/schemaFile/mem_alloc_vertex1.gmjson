[{"type": "container", "name": "main_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "root_F1", "type": "uint32", "nullable": true, "default": 1}, {"name": "root_F2", "type": "uint32", "nullable": true, "default": 2}, {"name": "root_F3", "type": "uint32", "nullable": true, "default": 3}, {"type": "container", "name": "con_0", "is_config": false, "fields": [{"name": "con_0_F1", "type": "uint32", "nullable": true, "default": 1}, {"name": "con_0_F2", "type": "uint32", "nullable": true, "default": 2}, {"name": "con_0_F3", "type": "uint32", "nullable": true, "default": 3}]}, {"type": "container", "name": "con_1", "is_config": true, "fields": [{"name": "con_1_F1", "type": "uint32", "nullable": true, "default": 11}, {"name": "con_1_F2", "type": "uint32", "nullable": true, "default": 12}, {"name": "con_1_F3", "type": "uint32", "nullable": true, "default": 13}]}, {"type": "container", "name": "con_2", "is_config": false, "fields": [{"name": "con_2_F1", "type": "uint32", "nullable": true, "default": 21}, {"name": "con_2_F2", "type": "uint32", "nullable": true, "default": 22}, {"name": "con_2_F3", "type": "uint32", "nullable": true, "default": 23}]}, {"type": "container", "name": "con_3", "is_config": true, "fields": [{"name": "con_3_F1", "type": "uint32", "nullable": true, "default": 31}, {"name": "con_3_F2", "type": "uint32", "nullable": true, "default": 32}, {"name": "con_3_F3", "type": "uint32", "nullable": true, "default": 33}]}, {"type": "container", "name": "con_4", "is_config": false, "fields": [{"name": "con_4_F1", "type": "uint32", "nullable": true, "default": 41}, {"name": "con_4_F2", "type": "uint32", "nullable": true, "default": 42}, {"name": "con_4_F3", "type": "uint32", "nullable": true, "default": 43}]}, {"type": "container", "name": "con_5", "is_config": true, "fields": [{"name": "con_5_F1", "type": "uint32", "nullable": true, "default": 51}, {"name": "con_5_F2", "type": "uint32", "nullable": true, "default": 52}, {"name": "con_5_F3", "type": "uint32", "nullable": true, "default": 53}]}, {"type": "container", "name": "con_6", "is_config": false, "fields": [{"name": "con_6_F1", "type": "uint32", "nullable": true, "default": 61}, {"name": "con_6_F2", "type": "uint32", "nullable": true, "default": 62}, {"name": "con_6_F3", "type": "uint32", "nullable": true, "default": 63}]}, {"type": "container", "name": "con_7", "is_config": true, "fields": [{"name": "con_7_F1", "type": "uint32", "nullable": true, "default": 71}, {"name": "con_7_F2", "type": "uint32", "nullable": true, "default": 72}, {"name": "con_7_F3", "type": "uint32", "nullable": true, "default": 73}]}, {"type": "container", "name": "con_8", "is_config": false, "fields": [{"name": "con_8_F1", "type": "uint32", "nullable": true, "default": 81}, {"name": "con_8_F2", "type": "uint32", "nullable": true, "default": 82}, {"name": "con_8_F3", "type": "uint32", "nullable": true, "default": 83}]}, {"type": "container", "name": "con_9", "is_config": true, "fields": [{"name": "con_9_F1", "type": "uint32", "nullable": true, "default": 91}, {"name": "con_9_F2", "type": "uint32", "nullable": true, "default": 92}, {"name": "con_9_F3", "type": "uint32", "nullable": true, "default": 93}]}, {"type": "container", "name": "con_10", "is_config": false, "fields": [{"name": "con_10_F1", "type": "uint32", "nullable": true, "default": 101}, {"name": "con_10_F2", "type": "uint32", "nullable": true, "default": 102}, {"name": "con_10_F3", "type": "uint32", "nullable": true, "default": 103}]}, {"type": "container", "name": "con_11", "is_config": true, "fields": [{"name": "con_11_F1", "type": "uint32", "nullable": true, "default": 111}, {"name": "con_11_F2", "type": "uint32", "nullable": true, "default": 112}, {"name": "con_11_F3", "type": "uint32", "nullable": true, "default": 113}]}, {"type": "container", "name": "con_12", "is_config": false, "fields": [{"name": "con_12_F1", "type": "uint32", "nullable": true, "default": 121}, {"name": "con_12_F2", "type": "uint32", "nullable": true, "default": 122}, {"name": "con_12_F3", "type": "uint32", "nullable": true, "default": 123}]}, {"type": "container", "name": "con_13", "is_config": true, "fields": [{"name": "con_13_F1", "type": "uint32", "nullable": true, "default": 131}, {"name": "con_13_F2", "type": "uint32", "nullable": true, "default": 132}, {"name": "con_13_F3", "type": "uint32", "nullable": true, "default": 133}]}, {"type": "container", "name": "con_14", "is_config": false, "fields": [{"name": "con_14_F1", "type": "uint32", "nullable": true, "default": 141}, {"name": "con_14_F2", "type": "uint32", "nullable": true, "default": 142}, {"name": "con_14_F3", "type": "uint32", "nullable": true, "default": 143}]}, {"type": "container", "name": "con_15", "is_config": true, "fields": [{"name": "con_15_F1", "type": "uint32", "nullable": true, "default": 151}, {"name": "con_15_F2", "type": "uint32", "nullable": true, "default": 152}, {"name": "con_15_F3", "type": "uint32", "nullable": true, "default": 153}]}, {"type": "container", "name": "con_16", "is_config": false, "fields": [{"name": "con_16_F1", "type": "uint32", "nullable": true, "default": 161}, {"name": "con_16_F2", "type": "uint32", "nullable": true, "default": 162}, {"name": "con_16_F3", "type": "uint32", "nullable": true, "default": 163}]}, {"type": "container", "name": "con_17", "is_config": true, "fields": [{"name": "con_17_F1", "type": "uint32", "nullable": true, "default": 171}, {"name": "con_17_F2", "type": "uint32", "nullable": true, "default": 172}, {"name": "con_17_F3", "type": "uint32", "nullable": true, "default": 173}]}, {"type": "container", "name": "con_18", "is_config": false, "fields": [{"name": "con_18_F1", "type": "uint32", "nullable": true, "default": 181}, {"name": "con_18_F2", "type": "uint32", "nullable": true, "default": 182}, {"name": "con_18_F3", "type": "uint32", "nullable": true, "default": 183}]}, {"type": "container", "name": "con_19", "is_config": true, "fields": [{"name": "con_19_F1", "type": "uint32", "nullable": true, "default": 191}, {"name": "con_19_F2", "type": "uint32", "nullable": true, "default": 192}, {"name": "con_19_F3", "type": "uint32", "nullable": true, "default": 193}]}, {"type": "container", "name": "con_20", "is_config": false, "fields": [{"name": "con_20_F1", "type": "uint32", "nullable": true, "default": 201}, {"name": "con_20_F2", "type": "uint32", "nullable": true, "default": 202}, {"name": "con_20_F3", "type": "uint32", "nullable": true, "default": 203}]}, {"type": "container", "name": "con_21", "is_config": true, "fields": [{"name": "con_21_F1", "type": "uint32", "nullable": true, "default": 211}, {"name": "con_21_F2", "type": "uint32", "nullable": true, "default": 212}, {"name": "con_21_F3", "type": "uint32", "nullable": true, "default": 213}]}, {"type": "container", "name": "con_22", "is_config": false, "fields": [{"name": "con_22_F1", "type": "uint32", "nullable": true, "default": 221}, {"name": "con_22_F2", "type": "uint32", "nullable": true, "default": 222}, {"name": "con_22_F3", "type": "uint32", "nullable": true, "default": 223}]}, {"type": "container", "name": "con_23", "is_config": true, "fields": [{"name": "con_23_F1", "type": "uint32", "nullable": true, "default": 231}, {"name": "con_23_F2", "type": "uint32", "nullable": true, "default": 232}, {"name": "con_23_F3", "type": "uint32", "nullable": true, "default": 233}]}, {"type": "container", "name": "con_24", "is_config": false, "fields": [{"name": "con_24_F1", "type": "uint32", "nullable": true, "default": 241}, {"name": "con_24_F2", "type": "uint32", "nullable": true, "default": 242}, {"name": "con_24_F3", "type": "uint32", "nullable": true, "default": 243}]}], "keys": [{"name": "pk", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_label", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "list_F1", "type": "uint32", "nullable": false}, {"name": "list_F2", "type": "uint32", "nullable": true, "default": 102, "is_config": true}, {"name": "list_F3", "type": "uint32", "nullable": true, "default": 103, "is_config": false}, {"name": "list_F4", "type": "uint32", "nullable": true, "default": 104, "is_config": true}, {"name": "list_F5", "type": "uint32", "nullable": true, "default": 105, "is_config": false}, {"name": "list_F6", "type": "uint32", "nullable": true, "default": 106, "is_config": true}, {"name": "list_F7", "type": "uint32", "nullable": true, "default": 107, "is_config": false}], "keys": [{"name": "pk", "fields": ["PID", "list_F1"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]