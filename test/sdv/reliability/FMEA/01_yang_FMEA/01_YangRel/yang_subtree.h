/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: rel_yang_subtree
 * Author: hanyang
 * Create: 2025-06-05
 */
#ifndef SUBTREE_ENCODE_H
#define SUBTREE_ENCODE_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"
#include "t_rd_inject.h"

// MS config
char *g_msConfig = "{\"max_record_count\" : 1000000}";
char *g_msConfigTrans = R"(
{
    "max_record_count":1000000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
char *g_msConfigCCEH = "{\"max_record_count\" : 1000000, \"hash_type\":\"cceh\"}";

char *g_keyName = "PK";

GmcTxConfigT g_mSTrxConfig;
char *g_namespace = "NamespaceABCRelSubtree";
char *g_namespaceUserName = "abc";

void TestCreateLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/yang_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_01.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_02.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_02.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_03.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_03.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_04.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/edge_04.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestCreateLabelSub(GmcStmtT *stmt)
{
    int ret = 0;
    char *vLabelSchema = NULL;
    AsyncUserDataT data = {0};

    readJanssonFile("./schema_file/SubTreeVertexLabel3.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;

    readJanssonFile("./schema_file/SubTreeEdgelLabel.gmjson", &vLabelSchema);
    AW_MACRO_ASSERT_NOTNULL(vLabelSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
}

void TestDropLabel(GmcStmtT *stmt)
{
    int ret = 0;
    bool retry = true;
    uint32_t retryTimes = 0;
    AsyncUserDataT data = {0};

    sleep(2);

    ret = GmcClearNamespaceAsync(stmt, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

int TestCheckNull(GmcStmtT *stmt)
{
    if (stmt == NULL) {
        return -1;
    } else {
        return GMERR_OK;
    }
}

void TestUseNamespace(GmcStmtT *stmt, const char * namespaceName)
{
    int ret;
    AsyncUserDataT data = {0};

    ret = GmcUseNamespaceAsync(stmt, namespaceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void TestClearNamespace(GmcStmtT *stmt, const char *nsName)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcClearNamespaceAsync(stmt, nsName, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/******************************DML*******************************************/
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int TestTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

AsyncUserDataT data1;
int TestTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_TRANSACTION_ROLLBACK) {
            AW_FUN_Log(LOG_INFO, "data.status = GMERR_TRANSACTION_ROLLBACK, the transaction will rollback.");
            memset(&data1, 0, sizeof(AsyncUserDataT));
            int ret1 = GmcTransRollBackAsync(conn, trans_rollback_callback, &data1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        return ret;
    }
}

int TestTransRollBackAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransRollBackAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        return ret;
    }
}

int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

int TestYangSetFieldID(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType, GmcAttributeTypeE attrType = GMC_ATTRIBUTE_NAME)
{
    int ret = 0;
    int ret1 = 0;

    GmcAttributePropertyT attrProperty;
    attrProperty.type = attrType;
    attrProperty.size = size;
    attrProperty.value = value;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = (void *)&attrProperty;
    propValue.size = sizeof(attrProperty);
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}

void TestYangSetIDName(GmcNodeT *node, const char *fieldName, const char* name, uint32_t size,
    GmcYangPropOpTypeE opType)
{
    int ret = 0;
    AW_MACRO_EXPECT_NE_INT(0, size);

    char nameSet[100] = {0};
    (void)memcpy_s(nameSet, size, name, size);
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, nameSet, size, fieldName, opType, GMC_ATTRIBUTE_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetIDValue(GmcNodeT *node, const char *fieldName, int32_t value, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    int32_t valueSet = value;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, &valueSet, sizeof(int32_t), fieldName,
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 只有list类型需要设置主键
void TestYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyUnion(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    char empValue[] = "";
    ret = TestYangSetField(node, GMC_DATATYPE_EMPTY, empValue, (strlen(empValue)), "F25", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t), "F26", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F27 union默认值string类型，写入和默认值不同值
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t), "F27", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // F28 union默认值string类型，不写值
}

void TestYangSetNodePropertyDefault(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = 0;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 111;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = 222;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = 333;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 444;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyAllType(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;

    // 设置all type属性值
    int8_t value8 = i;
    uint8_t valueU8 = i;
    int16_t value16 = i;
    uint16_t valueU16 = i;

    char valueF5 = value8;
    ret = TestYangSetField(node, GMC_DATATYPE_CHAR, &valueF5, sizeof(char), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned char valueF6 = valueU8;
    ret = TestYangSetField(node, GMC_DATATYPE_UCHAR, &valueF6, sizeof(unsigned char), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t valueF7 = value8;
    ret = TestYangSetField(node, GMC_DATATYPE_INT8, &valueF7, sizeof(int8_t), "F7", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t valueF8 = valueU8;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT8, &valueF8, sizeof(uint8_t), "F8", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t valueF9 = value16;
    ret = TestYangSetField(node, GMC_DATATYPE_INT16, &valueF9, sizeof(int16_t), "F9", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t valueF10 = valueU16;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT16, &valueF10, sizeof(uint16_t), "F10", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t valueF11 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_INT32, &valueF11, sizeof(int32_t), "F11", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF12 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF12, sizeof(uint32_t), "F12", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t valueF13 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_INT64, &valueF13, sizeof(int64_t), "F13", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t valueF14 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT64, &valueF14, sizeof(uint64_t), "F14", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool valueF15 = true;
    ret = TestYangSetField(node, GMC_DATATYPE_BOOL, &valueF15, sizeof(bool), "F15", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float valueF16 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_FLOAT, &valueF16, sizeof(float), "F16", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double valueF17 = i;
    ret = TestYangSetField(node, GMC_DATATYPE_DOUBLE, &valueF17, sizeof(double), "F17", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 由于S380，S310S，ap，仿真环境的该字段查询值不同，无法区分，所以不写该字段
    if (g_envType != 2) {
        uint64_t valueF18 = i;
        ret = TestYangSetField(node, GMC_DATATYPE_TIME, &valueF18, sizeof(uint64_t), "F18", opType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    char stringValue[8] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, stringValue, (strlen(stringValue)), "F19", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(node, GMC_DATATYPE_BYTES, stringValue, (strlen(stringValue)), "F20", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangSetField(node, GMC_DATATYPE_FIXED, stringValue, 7, "F21", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};  // 128 10000000  1024可以存储8*128个1
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;

    ret = TestYangSetField(node, GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap), "F22", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t valueSet = -1;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_ENUM, &valueSet, sizeof(int32_t), "F23",
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueSet = 1;
    ret = TestYangSetFieldID(node, GMC_DATATYPE_IDENTITY, &valueSet, sizeof(int32_t), "F24",
        opType, GMC_ATTRIBUTE_VALUE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char stringEmpty[] = "";
    ret = TestYangSetField(node, GMC_DATATYPE_EMPTY, stringEmpty, (strlen(stringEmpty)), "F25", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t unionType = i % 7;
    switch (unionType) {
        case 0: {
            int32_t int32Value = i;
            ret = TestYangSetField(node, GMC_DATATYPE_INT32, &int32Value, sizeof(int32_t), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 1: {
            uint32_t uint32Value = i;
            ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 2: {
            int64_t int64Value = i;
            ret = TestYangSetField(node, GMC_DATATYPE_INT64, &int64Value, sizeof(int64_t), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 3: {
            uint64_t uint64Value = i;
            ret = TestYangSetField(node, GMC_DATATYPE_UINT64, &uint64Value, sizeof(uint64_t), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 4: {
            char strValue[8] = "string";
            ret = TestYangSetField(node, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 5: {
            bool boolValue = true;
            ret = TestYangSetField(node, GMC_DATATYPE_BOOL, &boolValue, sizeof(bool), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        case 6: {
            char empValue[] = "";
            ret = TestYangSetField(node, GMC_DATATYPE_EMPTY, empValue, (strlen(empValue)), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        default: {
            int32_t int32Value = i;
            ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &int32Value, sizeof(int32_t), "F26", opType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
    }
}

// yang set T0层f0
void testYangSetVertexProperty_F0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// yang set T0层fx
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue)
{
    int ret;

    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// remove root
void TestRemoveRoot(GmcConnT *conn, const char * vertexName)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;

    ret = GmcAllocStmt(conn, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, vertexName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeStmt(stmt_root);
}

/******************************Subtree obj模式*******************************************/
void AsyncFetchRetCbInj(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    // 由于注入故障后错误码不唯一，这里不校验预期错误码
    if (status != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "AsyncFetchRetCbInj status is %d.", status);
        ASSERT_NE((errMsg != NULL), 0);
        param->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    uint32_t index = param->lastExpectIdx;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(jsonReply != NULL);
        if (param->expectReply.size() != 0) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], param->expectReply[index].c_str())) <<
                "replyJson:\n" << jsonReply[0] << "\nexpectJson:\n" << param->expectReply[index] << endl;
        }

        if (isEnd) {
            param->step++;
            if (param->data != NULL) {
                param->data->recvNum++;
            }
            GmcYangFreeFetchRet(fetchRet);
            return;
        }

        param->lastExpectIdx = index + count;
        ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCb, param));
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "{}");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[i].c_str())) << "\nreplyJson:\n" << reply <<
            "\nexpectJson:\n" << param->expectReply[i] << endl;
        GmcYangFreeTree(yangTree[i]);
    }
    param->step++;
    if (param->data != NULL) {
        param->data->recvNum++;
    }
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void AsyncFetchRetCbEncodeInj(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    // 由于注入故障后错误码不唯一，这里不校验预期错误码
    if (status != GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "AsyncFetchRetCbEncodeInj status is %d.", status);
        ASSERT_NE((errMsg != NULL), 0);
        param->step++;
        return;
    }

    bool isEnd = false;
    uint32_t count = 0;
    uint32_t index = param->lastExpectIdx;
    if ((param->filterMode == GMC_FETCH_JSON) || (param->filterMode == GMC_FETCH_JSON_RFC7951)
        || (param->filterMode == GMC_FETCH_FULL_JSON_RFC7951)) {
        const char **jsonReply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(jsonReply != NULL);

        if (param->expectReply.size() != 0) {
            ASSERT_TRUE(testYangJsonIsEqual(jsonReply[0], param->expectReply[index].c_str())) <<
                "replyJson:\n" << jsonReply[0] << "\nexpectJson:\n" << param->expectReply[index] << endl;
        }

        if (isEnd) {
            param->step++;
            if (param->data != NULL) {
                param->data->recvNum++;
            }
            GmcYangFreeFetchRet(fetchRet);
            return;
        }

        param->lastExpectIdx = index + count;
        ASSERT_EQ(GMERR_OK,
            GmcYangSubtreeFilterExtExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCbEncode, param));
        return;
    }
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "{}");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));
        ASSERT_TRUE(testYangJsonIsEqual(reply, param->expectReply[i].c_str())) << "\nreplyJson:\n" << reply <<
            "\nexpectJson:\n" << param->expectReply[i] << endl;
        GmcYangFreeTree(yangTree[i]);
    }
    param->step++;
    if (param->data != NULL) {
        param->data->recvNum++;
    }
    GmcYangFreeFetchRet(fetchRet);
    return;
}

void TestSubtreeFilterEncodeAll(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    const char * nameSpace = "gmdb",
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
    uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(0);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON_RFC7951;
    filters.filter = &filter;

    GmcSubtreeOptionT *option = NULL;
    ret = GmcYangCreateSubtreeOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (nameSpace != NULL) {
        // nameSpace可以不设置
        ret = GmcYangSubtreeOptionSetJsonDefaultPrefix(option, nameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcYangSubtreeOptionSetFilter(option, &filters);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbEncode, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

    GmcYangDestroySubtreeOption(option);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterEncodeAllInj(GmcStmtT *stmt, const char * rootName, const char * jsonName,
    const char * nameSpace = "gmdb",
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
    uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT,
    uint32_t injMode = 0)
{
    int ret;

    GmcNodeT *rootNode = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(0);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON_RFC7951;
    filters.filter = &filter;

    GmcSubtreeOptionT *option = NULL;
    ret = GmcYangCreateSubtreeOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (nameSpace != NULL) {
        // nameSpace可以不设置
        ret = GmcYangSubtreeOptionSetJsonDefaultPrefix(option, nameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcYangSubtreeOptionSetFilter(option, &filters);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);

    if (injMode == 0) {
        ret = TestCollectlibPath((char *)"memory_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (injMode == 1) {
        ret = TestCollectlibPath((char *)"shm_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;

    // 先执行1次subtree查询，获取totalCnt
    if (!equal) {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbEncode, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

    printf("====[1]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n", injectInfo->isFirstCal,
        injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);

    for (int i = 0; i < injectInfo->totalCnt; i++) {
        printf("--------------------i : %d--------------------\n", i);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbEncodeInj, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

        printf("====[2]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n ", injectInfo->isFirstCal,
            injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    }
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcYangDestroySubtreeOption(option);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterEncode(GmcStmtT *stmt, GmcNodeT *rootNode, const char * jsonName,
    const char * nameSpace = "gmdb",
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED,
    uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(0);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;

    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON_RFC7951;
    filters.filter = &filter;

    GmcSubtreeOptionT *option = NULL;
    ret = GmcYangCreateSubtreeOption(&option);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (nameSpace != NULL) {
        // nameSpace可以不设置
        ret = GmcYangSubtreeOptionSetJsonDefaultPrefix(option, nameSpace);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcYangSubtreeOptionSetFilter(option, &filters);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbEncode, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExtExecuteAsync(stmt, option, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterObj(GmcStmtT *stmt, GmcNodeT *rootNode, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED, uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT)
{
    int ret;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);
    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

void TestSubtreeFilterObjInj(GmcStmtT *stmt, GmcNodeT *rootNode, const char * jsonName,
    GmcSubtreeWithDefaultModeE defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED, uint32_t depth = 0,
    GmcSubtreeWithConfigModeE configMode = GMC_SUBTREE_FILTER_DEFAULT,
    uint32_t injMode = 0)
{
    int ret;

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = NULL;
    filter.subtree.obj = rootNode;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = defaultMode;
    filter.maxDepth = depth;
    filter.configFlag = configMode;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_OBJ;
    filters.filter = &filter;

    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);
    std::vector<std::string> reply(1);
    reply[0] = replyJson;

    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };

    char *noDataJson = NULL;
    readJanssonFile("SubtreeReplyJson/NoData.json", &noDataJson);
    ASSERT_NE((void *)NULL, noDataJson);
    bool equal = testYangJsonIsEqual(replyJson, noDataJson);

    if (injMode == 0) {
        ret = TestCollectlibPath((char *)"memory_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (injMode == 1) {
        ret = TestCollectlibPath((char *)"shm_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;

    // 先执行1次subtree查询，获取totalCnt
    if (!equal) {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbNoData, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

    printf("====[1]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n", injectInfo->isFirstCal,
        injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);

    for (int i = 0; i < injectInfo->totalCnt; i++) {
        printf("--------------------i : %d--------------------\n", i);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCbInj, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param, 1, 100000);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.expectStatus);

        printf("====[2]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n ", injectInfo->isFirstCal,
            injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    }
    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(replyJson);
    free(noDataJson);
    replyJson = NULL;
    noDataJson = NULL;
}

/******************************校验*******************************************/
struct ValidateParam {
    int step;
    int32_t exceptStatus;  // 预期的操作状态
    GmcValidateResT validateRes;     // 预期返回的mandatory校验结果
    bool isValidateErrorPath;
    GmcErrorPathCodeE expectedErrCode;
    uint32_t expectedErrClauseIndex;
    const char *expectedErrMsg;
    const char *expectedErrPath;
    uint64_t startTime;
    bool printTime;
    bool printSize;
};

void AsyncValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    EXPECT_EQ(param->exceptStatus, status) << errMsg;
    // 只有返回无异常时才去校验mandatory
    if (GMERR_OK == status) {
        AW_MACRO_EXPECT_EQ_INT(param->validateRes.validateRes, validateRes.validateRes);
        AW_MACRO_EXPECT_EQ_INT(param->validateRes.failCount, validateRes.failCount);
        if (validateRes.validateRes != param->validateRes.validateRes) {
            GmcErrorPathInfoT msg;
            TEST_EXPECT_INT32(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            AW_FUN_Log(LOG_INFO, "msg.errorMsg is %s\n", msg.errorMsg);
            AW_FUN_Log(LOG_INFO, "msg.errorPath is %s\n", msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }
    }

    if (param->isValidateErrorPath) {
        GmcErrorPathInfoT msg;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
        // 结果检查
        AW_MACRO_EXPECT_EQ_INT(param->expectedErrCode, msg.errorCode);
        if (msg.errorCode != GMC_VIOLATES_BUTT) {
            AW_MACRO_EXPECT_EQ_INT(param->expectedErrClauseIndex, msg.errorClauseIndex);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrMsg, msg.errorMsg);
            AW_MACRO_EXPECT_EQ_STR(param->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }
    }
    param->step++;
}

void AsyncValidateCbInj(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    AW_FUN_Log(LOG_INFO, "AsyncValidateCbInj status is %d.", status);

    param->step++;
}

int testWaitValidateAsyncRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    ValidateParam *userdata1 = (ValidateParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            AW_FUN_Log(LOG_INFO, "[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

// exceptRes 预期mandatory校验结果，exceptStatus 预期返回状态，apiSupport 校验接口返回状态
void WhenDataCheck(GmcStmtT *stmt, bool exceptRes, int32_t exceptStatus = GMERR_OK,
    uint32_t apiSupport = GMERR_OK, uint32_t checkType = GMC_YANG_VALIDATION_ALL_FORCE)
{
    int ret = 0;
    bool isDataService = true;

    GmcValidateResT validateRes {.validateRes = exceptRes};
    ValidateParam param = {.step = 0, .exceptStatus = exceptStatus, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = checkType, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(exceptStatus, param.exceptStatus);
}

void WhenDataCheckInj(GmcStmtT *stmt, bool exceptRes, int32_t exceptStatus = GMERR_OK,
    uint32_t apiSupport = GMERR_OK, uint32_t checkType = GMC_YANG_VALIDATION_ALL_FORCE,
    uint32_t injMode = 0)
{
    int ret = 0;
    bool isDataService = true;

    GmcValidateResT validateRes {.validateRes = exceptRes};
    ValidateParam param = {.step = 0, .exceptStatus = exceptStatus, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = checkType, .cfgJson = NULL};

    if (injMode == 0) {
        ret = TestCollectlibPath((char *)"memory_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (injMode == 1) {
        ret = TestCollectlibPath((char *)"shm_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;

    ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(exceptStatus, param.exceptStatus);

    printf("====[1]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n", injectInfo->isFirstCal,
        injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);

    for (int i = 0; i < injectInfo->totalCnt; i++) {
        printf("--------------------i : %d--------------------\n", i);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        ret = GmcYangValidateAsync(stmt, &cfg, AsyncValidateCbInj, &param);
        AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);
        ret = testWaitValidateAsyncRecv(&param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        printf("====[2]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n ", injectInfo->isFirstCal,
            injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    }

    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ModelCheck(GmcStmtT *stmt)
{
    int ret = 0;

    YangValidateUserDataT checkData = {0};
    ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    if (checkData.validateRes == false) {
        AW_FUN_Log(LOG_INFO, "GmcYangValidateModelAsync result is false, failcount is %d.", checkData.failCount);
    }
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

void ModelCheckAndDataCheck(GmcConnT *conn, GmcStmtT *stmt)
{
    int ret;

    // 模型校验
    ModelCheck(stmt);

    // 启动事务
    ret = TestTransStartAsync(conn, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 数据校验
    WhenDataCheck(stmt, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_ALL_FORCE);

    // 提交事务
    ret = TestTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/******************************Diff*******************************************/
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", i);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiffCallback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
void TestFetchAndDeparseDiff(GmcStmtT *stmt, vector<string> &expectDiff, int rets = GMERR_OK)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiffCallback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void FetchDiffCallbackInj(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        } else {
            AW_FUN_Log(LOG_INFO, "FetchDiffCallbackInj status is %d.", status);
        }
        userData1->recvNum++;
    }
}
void TestFetchAndDeparseDiffInj(GmcStmtT *stmt, vector<string> &expectDiff, int rets = GMERR_OK,
    uint32_t injMode = 0)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    data.stmt = stmt;
    data.expectDiff = &expectDiff;

    if (injMode == 0) {
        ret = TestCollectlibPath((char *)"memory_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (injMode == 1) {
        ret = TestCollectlibPath((char *)"shm_traverse_inject");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int shmId;
    char *shmaddr;
    InjectInfoT *injectInfo = NULL;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, CreateShmVar(&shmId, &shmaddr, &injectInfo));
    injectInfo->isFirstCal = true;

    ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiffCallback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);

    printf("====[1]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n", injectInfo->isFirstCal,
        injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);

    for (int i = 0; i < injectInfo->totalCnt; i++) {
        printf("--------------------i : %d--------------------\n", i);
        SpinlockLock(&injectInfo->lk);
        injectInfo->injectCnt = i;
        injectInfo->curCnt = 0;
        injectInfo->isFirstCal = false;
        SpinlockUnlock(&injectInfo->lk);
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiffCallbackInj, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        printf("====[2]==== isFirstCal : %d, totalCnt : %d, curCnt : %d, injectCnt: %d\n ", injectInfo->isFirstCal,
            injectInfo->totalCnt, injectInfo->curCnt, injectInfo->injectCnt);
    }

    ret = TestUnInjectCommand();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestRemoveInjectFile();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

static vector<string> expectDiff003 = {
    "root_union:create[(priKey(ID:1)),(NULL)]\n"
    "root_union.F0:create(100)\n"
    "root_union.F1:create(100)\n"
    "root_union.F2:create(100)\n"
    "root_union.F3:create(100)\n"
    "root_union.F4:create(100)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:0)),(NULL)]\n"
    "list_union_1.ID:create(1)\n"
    "list_union_1.F0:create(0)\n"
    "list_union_1.F1:create(0)\n"
    "list_union_1.F2:create(0)\n"
    "list_union_1.F3:create(0)\n"
    "list_union_1.F4:create(0)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(0)\n"
    "list_union_1.F27:create(0)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:1), preKey(PID:1,PK:0)),(NULL)]\n"
    "list_union_1.ID:create(2)\n"
    "list_union_1.F0:create(1)\n"
    "list_union_1.F1:create(1)\n"
    "list_union_1.F2:create(1)\n"
    "list_union_1.F3:create(1)\n"
    "list_union_1.F4:create(1)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(1)\n"
    "list_union_1.F27:create(1)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:2), preKey(PID:1,PK:1)),(NULL)]\n"
    "list_union_1.ID:create(3)\n"
    "list_union_1.F0:create(2)\n"
    "list_union_1.F1:create(2)\n"
    "list_union_1.F2:create(2)\n"
    "list_union_1.F3:create(2)\n"
    "list_union_1.F4:create(2)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(2)\n"
    "list_union_1.F27:create(2)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:3), preKey(PID:1,PK:2)),(NULL)]\n"
    "list_union_1.ID:create(4)\n"
    "list_union_1.F0:create(3)\n"
    "list_union_1.F1:create(3)\n"
    "list_union_1.F2:create(3)\n"
    "list_union_1.F3:create(3)\n"
    "list_union_1.F4:create(3)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(3)\n"
    "list_union_1.F27:create(3)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:4), preKey(PID:1,PK:3)),(NULL)]\n"
    "list_union_1.ID:create(5)\n"
    "list_union_1.F0:create(4)\n"
    "list_union_1.F1:create(4)\n"
    "list_union_1.F2:create(4)\n"
    "list_union_1.F3:create(4)\n"
    "list_union_1.F4:create(4)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(4)\n"
    "list_union_1.F27:create(4)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:5), preKey(PID:1,PK:4)),(NULL)]\n"
    "list_union_1.ID:create(6)\n"
    "list_union_1.F0:create(5)\n"
    "list_union_1.F1:create(5)\n"
    "list_union_1.F2:create(5)\n"
    "list_union_1.F3:create(5)\n"
    "list_union_1.F4:create(5)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(5)\n"
    "list_union_1.F27:create(5)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:6), preKey(PID:1,PK:5)),(NULL)]\n"
    "list_union_1.ID:create(7)\n"
    "list_union_1.F0:create(6)\n"
    "list_union_1.F1:create(6)\n"
    "list_union_1.F2:create(6)\n"
    "list_union_1.F3:create(6)\n"
    "list_union_1.F4:create(6)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(6)\n"
    "list_union_1.F27:create(6)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:7), preKey(PID:1,PK:6)),(NULL)]\n"
    "list_union_1.ID:create(8)\n"
    "list_union_1.F0:create(7)\n"
    "list_union_1.F1:create(7)\n"
    "list_union_1.F2:create(7)\n"
    "list_union_1.F3:create(7)\n"
    "list_union_1.F4:create(7)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(7)\n"
    "list_union_1.F27:create(7)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:8), preKey(PID:1,PK:7)),(NULL)]\n"
    "list_union_1.ID:create(9)\n"
    "list_union_1.F0:create(8)\n"
    "list_union_1.F1:create(8)\n"
    "list_union_1.F2:create(8)\n"
    "list_union_1.F3:create(8)\n"
    "list_union_1.F4:create(8)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(8)\n"
    "list_union_1.F27:create(8)\n"
    "list_union_1.F28:create(strdefault)\n"
    "root_union.list_union_1:create[(priKey(PID:1,PK:9), preKey(PID:1,PK:8)),(NULL)]\n"
    "list_union_1.ID:create(10)\n"
    "list_union_1.F0:create(9)\n"
    "list_union_1.F1:create(9)\n"
    "list_union_1.F2:create(9)\n"
    "list_union_1.F3:create(9)\n"
    "list_union_1.F4:create(9)\n"
    "list_union_1.F25:create(NIL:27)\n"
    "list_union_1.F26:create(9)\n"
    "list_union_1.F27:create(9)\n"
    "list_union_1.F28:create(strdefault)\n"
};

#endif /* SUBTREE_ENCODE_H */
