/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef YANG_RELIABILITY_SCENE_H
#define YANG_RELIABILITY_SCENE_H
extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
#include "../try.h"
#include "t_rd_assert.h"

#define MAX_CMD_SIZE 2048
#define MAX_LABELNAME_LEN 128
#define RESTART_SERVER 0
char g_command[MAX_CMD_SIZE];
char g_viewCommand[MAX_CMD_SIZE];
struct FetchRetCbParam01 {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};

int g_subtreecount = 0;
bool g_server_exit = false;
bool g_server_hang = false;

const char *g_vertexLabel1 = "ietf-yang-library:yang-library";
const char *g_vertexLabel2 = "ietf-yang-library:yang-library::module-set";
const char *g_vertexLabel3 = "ietf-yang-library:yang-library::module-set::module";
const char *g_vertexLabel4 = "ietf-yang-library:yang-library::module-set::module::location";
const char *g_vertexLabel5 = "ietf-yang-library:yang-library::module-set::module::submodule";
const char *g_vertexLabel6 = "ietf-yang-library:yang-library::module-set::module::submodule::location";
const char *g_vertexLabel7 = "huawei-acl:acl";
const char *g_vertexLabel8 = "huawei-acl:acl::ip-pools::ip-pool";
const char *g_vertexLabel9 = "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr";
const char *g_vertexLabel10 = "huawei-acl:acl::port-pools::port-pool";
const char *g_vertexLabel11 = "huawei-acl:acl::port-pools::port-pool::ports::port";
const char *g_vertexLabel12 = "huawei-acl:acl::groups::group";
const char *g_vertexLabel13 = "huawei-acl:acl::groups::group::rule-basics::rule-basic";
const char *g_vertexLabel14 = "huawei-acl:acl::groups::group::rule-advances::rule-advance";
const char *g_vertexLabel15 = "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet";

const char *g_vertexLabel16 = "ietf-yang-library:yang-library";

const char *g_tabelConfig =
    "{\"max_record_count\" : 10000000, \"isFastReadUncommitted\":0, \"auto_increment\":1, \"yang_model\":1}";
char *g_schema = NULL, *g_edgeSchema = NULL, *g_diffReturnJson = NULL;
const char *g_namespaceUserName = (const char *)"abc";
const char *g_savepointName = "sp", *g_savepointName2 = "sp2", *g_savepointName3 = "sp3";
int g_startNum = 0, g_endNum = 100;

string WriteStringToSpecialSize(int length, string &str)
{
    while (str.length() < length) {
        str += "a";
    }
    return str;
}
const char *g_namespace = (const char *)"Yang380";

GmcStmtT *Yang_stmt_async = NULL;
GmcConnT *Yang_conn_async = NULL;
AsyncUserDataT userData = {0};
// 申请stmt
GmcStmtT *g_stmt_sync_1 = NULL;
GmcStmtT *g_stmt_sync_2 = NULL;
GmcStmtT *g_stmt_sync_3 = NULL;
GmcStmtT *g_stmt_sync_4 = NULL;
GmcStmtT *g_stmt_sync_5 = NULL;
GmcStmtT *g_stmt_sync_6 = NULL;
GmcStmtT *g_stmt_sync_7 = NULL;
GmcStmtT *g_stmt_sync_8 = NULL;
GmcStmtT *g_stmt_sync_9 = NULL;
GmcStmtT *g_stmt_sync_10 = NULL;
GmcStmtT *g_stmt_sync_11 = NULL;
GmcStmtT *g_stmt_sync_12 = NULL;
GmcStmtT *g_stmt_sync_13 = NULL;
GmcStmtT *g_stmt_sync_14 = NULL;
GmcStmtT *g_stmt_sync_15 = NULL;

// stmt申请
void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(Yang_conn_async, &g_stmt_sync_15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestYangFreeAllstmt()
{
    GmcFreeStmt(g_stmt_sync_1);
    GmcFreeStmt(g_stmt_sync_2);
    GmcFreeStmt(g_stmt_sync_3);
    GmcFreeStmt(g_stmt_sync_4);
    GmcFreeStmt(g_stmt_sync_5);
    GmcFreeStmt(g_stmt_sync_6);
    GmcFreeStmt(g_stmt_sync_7);
    GmcFreeStmt(g_stmt_sync_8);
    GmcFreeStmt(g_stmt_sync_9);
    GmcFreeStmt(g_stmt_sync_10);
    GmcFreeStmt(g_stmt_sync_11);
    GmcFreeStmt(g_stmt_sync_12);
    GmcFreeStmt(g_stmt_sync_13);
    GmcFreeStmt(g_stmt_sync_14);
    GmcFreeStmt(g_stmt_sync_15);
}

void TransStart(GmcConnT *conn)
{
    int ret = 0;
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;  // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX;  // 乐观事务

    // 开启乐观事务
    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans start error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TransCommit(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}
void TransRollback(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expectStatus) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans rollback error code:%d.", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchtype = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1024);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchtype);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void BatchExecute(GmcBatchT *batch, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue)
                            : GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}
void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}
// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}
void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        // ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

// diff 回调
void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));

            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

// 获取diff
void testFetchAndDeparseDiff(
    GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data, int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

int testYangSetField(
    GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}
int testYangSetNodeField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}

// 批量导入gmjosn目录下的所有表
void BatchExportVextexLabel()
{
    char schemafile[256] = "\n";
    char currentpath[256] = "\n";
    char command[256] = "\n";
    // 获取当前绝对路径
    getcwd(currentpath, 256);
    // 将jmjson的路径赋值给schema_file
    (void)(void)snprintf(schemafile, 256, "%s/../../schema_file/r21_ndb/gmjson", currentpath);
    // 执行批量导入命令 将gmjson目录的所有表批量导入
    (void)(void)snprintf(command, 256, "%s/gmimport -c cache -f %s", g_toolPath, schemafile);
    system(command);
    memset(command, 0, sizeof(command));
}

// 获取表名称
char *Fun(char *str)
{
    char newStr[400] = {'\0'};

    int i = 1;
    int j = 0;
    while (str[i] != '\n') {
        if (str[i] != ' ' and str[i] != '.') {
            newStr[j] = str[i];
            j++;
        }
        i += 1;
    }
    char *tmp = (char *)malloc(400 * sizeof(char));
    EXPECT_NE((void *)NULL, tmp);
    tmp[0] = newStr[0];
    strcpy(tmp, newStr);
    return tmp;
}
// 删vertexlabel yang
int deleteYangLabel(GmcStmtT *stmt)
{
    // 查看视图
    char const *viewname = "V\\$STORAGE_VERTEX_COUNT";
    char const *edgelabelname = "V\\$CATA_EDGE_LABEL_INFO";
    char cmd[256] = "\n";

    int ret = 0;
    GmcConnT *conn_sync = NULL;
    GmcStmtT *stmt_sync = NULL;

    ret = testGmcConnect(&conn_sync, &stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(stmt_sync, "NamespaceYang");
    EXPECT_EQ(GMERR_OK, ret);
    (void)snprintf(cmd, 256, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, edgelabelname);
    AW_FUN_Log(LOG_STEP, "%s\n", cmd);
    FILE *finEdge = popen(cmd, "r");
    char outedge[4000] = {0};

    char *edgename = {0};
    char *posedge = {0};
    AW_FUN_Log(LOG_STEP, "deleteVertexLabel\n");
    while (fgets(outedge, 4000 - 1, finEdge) != NULL) {
        if (strstr(outedge, "EDGE_LABEL_NAME") != NULL) {
            posedge = strstr(outedge, ":");
            edgename = (char *)Fun(posedge);
            ret = GmcDropEdgeLabel(stmt_sync, edgename);
            EXPECT_EQ(GMERR_OK, ret);
            free(edgename);
        }
    }
    pclose(finEdge);

    (void)snprintf(cmd, 256, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, viewname);
    AW_FUN_Log(LOG_STEP, "%s\n", cmd);
    FILE *fin = popen(cmd, "r");
    char out[4000] = {0};

    char *labelname = {0};
    char *pos = {0};
    int count = 0;
    AW_FUN_Log(LOG_STEP, "deleteVertexLabel\n");
    while (fgets(out, 4000 - 1, fin) != NULL) {
        if (strstr(out, "NamespaceYang") != NULL) {
            pos = strstr(out, ".");
            labelname = (char *)Fun(pos);
            if (strstr(out, "NamespaceYang") != NULL) {
                pos = strstr(out, ".");
                labelname = (char *)Fun(pos);
            }
            ret = GmcDropVertexLabel(stmt_sync, labelname);
            EXPECT_EQ(GMERR_OK, ret);
            free(labelname);
        }
    }
    pclose(fin);
    memset(cmd, 0, sizeof(cmd));
    ret = testGmcDisconnect(conn_sync, stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int deleteVertexLabel(GmcStmtT *stmt)
{
    // 查看视图
    char const *viewname = "V\\$STORAGE_VERTEX_COUNT";
    char cmd[256] = "\n";
    (void)snprintf(cmd, 256, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, viewname);
    AW_FUN_Log(LOG_STEP, "%s\n", cmd);

    FILE *fin = popen(cmd, "r");
    char out[4000] = {0};
    int ret = 0;
    char *labelname = {0};
    char *pos = {0};
    int count = 0;
    AW_FUN_Log(LOG_STEP, "deleteVertexLabel\n");
    while (fgets(out, 4000 - 1, fin) != NULL) {
        if (strstr(out, "table") != NULL) {
            pos = strstr(out, ":");
            labelname = (char *)Fun(pos);
            AW_FUN_Log(LOG_STEP, "labelname = %s\n", labelname);
            ret = GmcDropVertexLabelAsync(stmt, labelname, drop_vertex_label_callback, &userData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            if (userData.status == GMERR_UNDEFINED_TABLE) {
                count++;
            } else {
                EXPECT_EQ(GMERR_OK, userData.status);
            }
            free(labelname);
        }
    }
    pclose(fin);
    memset(cmd, 0, sizeof(cmd));
    return ret;
}

// 删kvlabel
int deleteKvLabel(GmcStmtT *stmt)
{
    // 查看视图
    AW_FUN_Log(LOG_STEP, "delete kv start\n");
    char const *viewname = "V\\$STORAGE_KV_COUNT";
    char cmd[256] = "\n";
    (void)snprintf(cmd, 256, "%s/gmsysview -s %s  -q %s", g_toolPath, g_connServer, viewname);
    AW_FUN_Log(LOG_STEP, "%s\n", cmd);

    FILE *fin = popen(cmd, "r");
    char out[4000] = {0};
    int ret = 0;
    char *labelname = {0};
    char *pos = {0};

    int count = 0;
    AW_FUN_Log(LOG_STEP, "fgets start\n");
    while (fgets(out, 4000 - 1, fin) != NULL) {
        if (strstr(out, "gmdbtest") != NULL) {
            pos = strstr(out, ".");
            labelname = (char *)Fun(pos);
            // testforward和if表是简化长稳中的表，可靠性叠加长稳的时候不可以删除这两张表
            if (strcmp(labelname, "T_GMDB") != 0 and strcmp(labelname, "testforward") != 0 and
                strcmp(labelname, "if") != 0) {
                ret = GmcKvDropTable(stmt, labelname);
                AW_FUN_Log(LOG_STEP, "labelname = %s\n", labelname);
                if (ret == GMERR_UNDEFINED_TABLE) {
                    count++;
                } else {
                    EXPECT_EQ(GMERR_OK, ret);
                }
            }
            free(labelname);
        }
    }
    GmcKvDropTable(stmt, "system_info");
    pclose(fin);
    memset(cmd, 0, sizeof(cmd));
    // iot设备原有表无法删除
    if (count > 4) {
        AW_FUN_Log(LOG_ERROR, "count is %d", count);
        return -1;
    }
    return ret;
}
int deleteLabel(GmcStmtT *stmt)
{
    int ret = 0;
    ret = deleteVertexLabel(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}
int deleteYang(GmcStmtT *stmt)
{
    int ret = 0;
    ret = deleteYangLabel(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

string testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res;
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
        res = string(replyJson);
        return res;
    }
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, FetchRetCbParam01 *param01, bool isDiff = false)
{
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_TRUE(param01->expectReply.size() >= (idx + count));  // 断言防止越界
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param01->expectReply[idx + i].c_str(), "{}");
            continue;
        }
        std::string reply = testYangTreeToStr(param01->stmt, yangTree[i], isDiff);
        EXPECT_TRUE(testYangJsonIsEqual(reply.c_str(), param01->expectReply[idx + i].c_str()))
            << "replyJson:\n"
            << reply << endl;
        GmcYangFreeTree(yangTree[i]);
    }
}
void AsyncFetchRetCb01(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    if (g_server_exit == true) {
        ASSERT_EQ(GMERR_REQUEST_TIME_OUT, status) << errMsg;
    } else {
        if (param01->expectStatus != status) {
        }
        ASSERT_EQ(param01->expectStatus, status) << errMsg;
    }
    if (status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        param01->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
int testWaitAsyncSubtreeRecv_APP(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    FetchRetCbParam01 *userdata1 = (FetchRetCbParam01 *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            AW_FUN_Log(LOG_INFO, "[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}
int testsubtreeSetvalue(GmcNodeT * Node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(Node, &propValue, optype);
    return ret;
}
void testYanginsertacl(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    AW_FUN_Log(LOG_INFO, "sync batch insert begin.");
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "sync batch add %s object end.", g_vertexLabel7);
    for (int i = 0; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8,
            GMC_DATATYPE_STRING,
            &namevalue,
            (strlen(namevalue)),
            "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 apply-type
        GmcNodeT *applytypeNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_INSERT, &applytypeNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 apply-ip
        GmcNodeT *applyipNode = NULL;
        ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_INSERT, &applyipNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ipaddrs
        GmcNodeT *ipaddrsNode = NULL;
        ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_INSERT, &ipaddrsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        for (int k = 0; k < 2; k++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_9, g_vertexLabel9, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_8, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_9, &vertexLabel9Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 10, "address%d", k);
            ret = testYangSetField(g_stmt_sync_9,
                GMC_DATATYPE_STRING,
                &namevalue1,
                (strlen(namevalue1)),
                "address",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[10];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 10, "mask%d", k);
            ret = testYangSetField(g_stmt_sync_9,
                GMC_DATATYPE_STRING,
                &namevalue2,
                (strlen(namevalue2)),
                "mask",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_INFO, "sync batch add 200 %s object, 200 * 2 %s object end.", g_vertexLabel8, g_vertexLabel9);
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_10, g_vertexLabel10, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_10, &vertexLabel10Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 10, "name%d", j);
        ret = testYangSetField(g_stmt_sync_10,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ports
        GmcNodeT *portsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel10Node, "ports", GMC_OPERATION_INSERT, &portsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_11, g_vertexLabel11, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_10, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_11, &vertexLabel11Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[10];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 10, "operation%d", l);
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "operation",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberbegin = l;
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_UINT16,
                &numberbegin,
                sizeof(uint16_t),
                "number-begin",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberend = l;
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_UINT16,
                &numberend,
                sizeof(uint16_t),
                "number-end",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_INFO, "sync batch add 2 %s object, 2 * 2 %s object end.", g_vertexLabel10, g_vertexLabel11);
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[20];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 20, "identity%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "identity",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[20];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)snprintf(namevalue4, 20, "type%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue4,
            (strlen(namevalue4)),
            "type",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t step = j;
        ret = testYangSetField(
            g_stmt_sync_12, GMC_DATATYPE_UINT32, &step, sizeof(uint32_t), "step", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue6[20];
        memset(namevalue6, 0, sizeof(namevalue6));
        (void)snprintf(namevalue6, 20, "description%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue6,
            (strlen(namevalue6)),
            "description",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t number = j;
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_UINT32,
            &number,
            sizeof(uint32_t),
            "number",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_INSERT, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_INSERT, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_INSERT, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_14, g_vertexLabel14, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel14Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_14, &vertexLabel14Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_14, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 protocol-type
            GmcNodeT *protocoltypeNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel14Node, "protocol-type", GMC_OPERATION_INSERT, &protocoltypeNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *singleNode = NULL;
            ret = GmcYangEditChildNode(protocoltypeNode, "single", GMC_OPERATION_INSERT, &singleNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint8_t protocol = l;
            ret = testYangSetNodeField(singleNode,
                GMC_DATATYPE_UINT8,
                &protocol,
                sizeof(uint8_t),
                "protocol",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 source
            GmcNodeT *sourceNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel14Node, "source", GMC_OPERATION_INSERT, &sourceNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *ipNode = NULL;
            ret = GmcYangEditChildNode(sourceNode, "ip", GMC_OPERATION_INSERT, &ipNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetNodeField(ipNode,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetNodeField(ipNode,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_15, g_vertexLabel15, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_15);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel15Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_15, &vertexLabel15Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_15, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_15);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_INFO, "sync batch add 2 %s object, 2 * 2 %s, %s, %s object end.", g_vertexLabel12, g_vertexLabel13,
        g_vertexLabel14, g_vertexLabel15);
    // 提交批处理
    BatchExecute(batch);
    AW_FUN_Log(LOG_INFO, "sync batch insert end.");
    // 提交事务
    TransCommit(conn);
}
void testYanginsertacl10W(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
#if defined(RTOSV2X)
    uint32_t cycleTimes = 5;
#else
    uint32_t cycleTimes = 50;
#endif
    for (int k = 0; k < cycleTimes; k++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)snprintf(namevalue1, 10, "identity%d", 0);
        ret = GmcSetIndexKeyValue(g_stmt_sync_12, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_12, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_NONE, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_NONE, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_NONE, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 2000 + (2000 * k); l < 4000 + (2000 * k); l++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置g_vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        BatchExecute(batch);
    }
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 提交事务
    TransCommit(conn);
}
void testYanginsertacltabelspace(GmcConnT *conn, bool isRollBackTimeout = false)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    int k = 0;
    bool tabelspaceoom = false;
    while (1) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)snprintf(namevalue1, 10, "identity%d", 0);
        ret = GmcSetIndexKeyValue(g_stmt_sync_12, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_12, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_NONE, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_NONE, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_NONE, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 2000 + (2000 * k); l < 4000 + (2000 * k); l++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置g_vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        // 数据超过表空间大小 或者超过表最大记录数
        if ((userData.status == GMERR_OUT_OF_MEMORY) || (userData.status == GMERR_RECORD_COUNT_LIMIT_EXCEEDED) ||
            (userData.status == GMERR_TRANSACTION_ROLLBACK)) {
            AW_FUN_Log(LOG_INFO, "batch ret is: %d.", userData.status);
            tabelspaceoom = true;
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            k++;
        }
    }
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 写数据超过表空间大小 事务回滚
    if (tabelspaceoom == true) {
#if defined(RTOSV2X)
        if (isRollBackTimeout) {
            TransRollback(conn, GMERR_REQUEST_TIME_OUT);
        } else {
            TransRollback(conn);
        }
#else
        TransRollback(conn);
#endif
    } else {
        TransCommit(conn);
    }
}
void fetchSubtreeacl(GmcStmtT *stmt)
{
    // subtree全量查询
    int ret = 0;
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node,
        GMC_DATATYPE_STRING,
        &namevalue1,
        (strlen(namevalue1)),
        "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam01 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb01, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
}
static vector<string> expectDiff54 = {
    "SubT0Con:create[(priKey(ID:1)),(NULL)]\n"
    "SubT0Con.F0:create(1)\n"
    "SubT0Con.F1:create(1)\n"
    "SubT0Con.F2:create(NIL:8)\n"
    "SubT0Con.F3:create(1.000000)\n"
    "SubT0Con.F4:create(NIL:8)\n"
    "SubT0Con.F5:create(1.000000)\n"
    "SubT0Con.F6:create(string)\n"
    "SubT0Con.F7:create(65)\n"
    "SubT0Con.F8:create(98)\n"
    "SubT0Con.F9:create(1)\n"
    "SubT0Con.F10:create(1)\n"
    "SubT0Con.F11:create(1)\n"
    "SubT0Con.F12:create(1)\n"
    "SubT0Con.F13:create(1)\n"
    "SubT0Con.F14:create(1)\n"
    "SubT0Con.F15:create(1)\n"
    "SubT0Con.F16:create(1)\n"
    "SubT0Con.F17:create(NIL:15)\n"
    "SubT0Con.F18:create(NIL:16)\n"
    "SubT0Con.F19:create(NIL:17)\n"
    "SubT0Con.F20:create(NIL:21)\n"
    "SubT0Con.F21:create(NIL:22)\n"
    "SubT0Con.F22:create(NIL:23)\n"
    "SubT0Con.F23:create(NIL:18)\n"
    "SubT0Con.SubT1container:create\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.SubT1choice:create\n"
    "SubT1choice.SubT1choiceCasedefeault:create\n"
    "SubT1choiceCasedefeault.F0:create(1)\n"
    "SubT1choiceCasedefeault.F1:create(2)\n"
    "SubT1choiceCasedefeault.F2:create(NIL:8)\n"
    "SubT1choiceCasedefeault.F3:create(1.000000)\n"
    "SubT1choiceCasedefeault.F4:create(NIL:8)\n"
    "SubT1choiceCasedefeault.F5:create(1.000000)\n"
    "SubT1choiceCasedefeault.F6:create(string)\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT1choiceCasedefeault.leaf_casedef:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_uint8:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_int8:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_uint16:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_int16:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_uint32:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_int32:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_uint64:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
    "SubT0Con.leaf_int64:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"};
void CreateSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void ReleaseSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransReleaseSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "release savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "rollback savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}
void testYanginsertacltabelspacediff(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    int k = 0;
    bool tabelspaceoom = false;
    char savepointName[20];
    memset(savepointName, 0, sizeof(savepointName));
    (void)snprintf(savepointName, 20, "spname%d", k);
    while (1) {
        CreateSavepoint(conn, savepointName);
        AW_FUN_Log(LOG_INFO, "create savepoint %s.", savepointName);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel2 list
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)snprintf(namevalue1, 10, "identity%d", 0);
        ret = GmcSetIndexKeyValue(g_stmt_sync_12, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_12, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_NONE, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_NONE, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_NONE, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 2000 + (2000 * k); l < 4000 + (2000 * k); l++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置g_vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[20];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        // 数据超过表空间大小 或者超过表最大记录数
        if ((userData.status == GMERR_OUT_OF_MEMORY) || (userData.status == GMERR_RECORD_COUNT_LIMIT_EXCEEDED)) {
            tabelspaceoom = true;
            // Rollback savepoint
            AW_FUN_Log(LOG_INFO, "batch ret is %d, savepoint is %s.", userData.status, savepointName);
            RollbackSavepoint(conn, savepointName);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
#if defined(RTOSV2X)
            // rtosv2x 上面资源不够，适配成循环10次退出
            if (k >= 5) {
                break;
            }
#endif
            k++;
            memset(savepointName, 0, sizeof(savepointName));
            (void)snprintf(savepointName, 20, "spname%d", k);
        }
    }
    // 获取diff
    AsyncUserDataT data = {0};
    testFetchAndDeparseDiff(g_stmt_sync_13, batch, expectDiff54, data);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TransCommit(conn);
}
void testYanginsertacldiff(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8,
            GMC_DATATYPE_STRING,
            &namevalue,
            (strlen(namevalue)),
            "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 apply-type
        GmcNodeT *applytypeNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_INSERT, &applytypeNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 apply-ip
        GmcNodeT *applyipNode = NULL;
        ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_INSERT, &applyipNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ipaddrs
        GmcNodeT *ipaddrsNode = NULL;
        ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_INSERT, &ipaddrsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        for (int k = 0; k < 2; k++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_9, g_vertexLabel9, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_8, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_9, &vertexLabel9Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 10, "address%d", k);
            ret = testYangSetField(g_stmt_sync_9,
                GMC_DATATYPE_STRING,
                &namevalue1,
                (strlen(namevalue1)),
                "address",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[10];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 10, "mask%d", k);
            ret = testYangSetField(g_stmt_sync_9,
                GMC_DATATYPE_STRING,
                &namevalue2,
                (strlen(namevalue2)),
                "mask",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_10, g_vertexLabel10, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_10, &vertexLabel10Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 10, "name%d", j);
        ret = testYangSetField(g_stmt_sync_10,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ports
        GmcNodeT *portsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel10Node, "ports", GMC_OPERATION_INSERT, &portsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_11, g_vertexLabel11, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_10, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_11, &vertexLabel11Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[10];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 10, "operation%d", l);
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_STRING,
                &namevalue5,
                (strlen(namevalue5)),
                "operation",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberbegin = l;
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_UINT16,
                &numberbegin,
                sizeof(uint16_t),
                "number-begin",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberend = l;
            ret = testYangSetField(g_stmt_sync_11,
                GMC_DATATYPE_UINT16,
                &numberend,
                sizeof(uint16_t),
                "number-end",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel12Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[20];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 20, "identity%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue3,
            (strlen(namevalue3)),
            "identity",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[20];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)snprintf(namevalue4, 20, "type%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue4,
            (strlen(namevalue4)),
            "type",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t step = j;
        ret = testYangSetField(
            g_stmt_sync_12, GMC_DATATYPE_UINT32, &step, sizeof(uint32_t), "step", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue5[20];
        memset(namevalue5, 0, sizeof(namevalue5));
        (void)snprintf(namevalue5, 20, "description%d", j);
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_STRING,
            &namevalue5,
            (strlen(namevalue5)),
            "description",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t number = j;
        ret = testYangSetField(g_stmt_sync_12,
            GMC_DATATYPE_UINT32,
            &number,
            sizeof(uint32_t),
            "number",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-basics
        GmcNodeT *rulebasicsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-basics", GMC_OPERATION_INSERT, &rulebasicsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-advances
        GmcNodeT *ruleadvancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-advances", GMC_OPERATION_INSERT, &ruleadvancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 rule-ethernets
        GmcNodeT *ruleethernetsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "rule-ethernets", GMC_OPERATION_INSERT, &ruleethernetsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel13Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue11[20];
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_13, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "sourceipaddr%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "sourcewild%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "fragmenttype%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "fragment-type",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "timerangename%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "time-range-name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue11, 0, sizeof(namevalue11));
            (void)snprintf(namevalue11, 20, "description%d", l);
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_STRING,
                &namevalue11,
                (strlen(namevalue11)),
                "description",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t priority = l;
            ret = testYangSetField(g_stmt_sync_13,
                GMC_DATATYPE_UINT32,
                &id,
                sizeof(uint32_t),
                "priority",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_13);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_14, g_vertexLabel14, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel14Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_14, &vertexLabel14Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue7[20];
            memset(namevalue7, 0, sizeof(namevalue7));
            (void)snprintf(namevalue7, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue7,
                (strlen(namevalue7)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_14, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue7, 0, sizeof(namevalue7));
            (void)snprintf(namevalue7, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue7,
                (strlen(namevalue7)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue7, 0, sizeof(namevalue7));
            (void)snprintf(namevalue7, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_14,
                GMC_DATATYPE_STRING,
                &namevalue7,
                (strlen(namevalue7)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 protocol-type
            GmcNodeT *protocoltypeNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel14Node, "protocol-type", GMC_OPERATION_INSERT, &protocoltypeNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *singleNode = NULL;
            ret = GmcYangEditChildNode(protocoltypeNode, "single", GMC_OPERATION_INSERT, &singleNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint8_t protocol = l;
            ret = testYangSetNodeField(singleNode,
                GMC_DATATYPE_UINT8,
                &protocol,
                sizeof(uint8_t),
                "protocol",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 source
            GmcNodeT *sourceNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel14Node, "source", GMC_OPERATION_INSERT, &sourceNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *ipNode = NULL;
            ret = GmcYangEditChildNode(sourceNode, "ip", GMC_OPERATION_INSERT, &ipNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue7, 0, sizeof(namevalue7));
            (void)snprintf(namevalue7, 20, "sourceipaddr%d", l);
            ret = testYangSetNodeField(ipNode,
                GMC_DATATYPE_STRING,
                &namevalue7,
                (strlen(namevalue7)),
                "source-ipaddr",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue7, 0, sizeof(namevalue7));
            (void)snprintf(namevalue7, 20, "sourcewild%d", l);
            ret = testYangSetNodeField(ipNode,
                GMC_DATATYPE_STRING,
                &namevalue7,
                (strlen(namevalue7)),
                "source-wild",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_15, g_vertexLabel15, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_15);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            GmcNodeT *vertexLabel15Node = NULL;
            ret = GmcGetRootNode(g_stmt_sync_15, &vertexLabel15Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue8[20];
            memset(namevalue8, 0, sizeof(namevalue8));
            (void)snprintf(namevalue8, 20, "name%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue8,
                (strlen(namevalue8)),
                "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t id = l;
            ret = testYangSetField(
                g_stmt_sync_15, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t), "id", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue8, 0, sizeof(namevalue8));
            (void)snprintf(namevalue8, 20, "action%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue8,
                (strlen(namevalue8)),
                "action",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(namevalue8, 0, sizeof(namevalue8));
            (void)snprintf(namevalue8, 20, "activestatus%d", l);
            ret = testYangSetField(g_stmt_sync_15,
                GMC_DATATYPE_STRING,
                &namevalue8,
                (strlen(namevalue8)),
                "active-status",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_15);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 获取diff
    AsyncUserDataT data = {0};
    testFetchAndDeparseDiff(g_stmt_sync_7, batch, expectDiff54, data);
    // 提交事务
    TransCommit(conn);
}
void testYanginsertietf(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    GmcNodeT *vertexLabel3Node = NULL;
    GmcNodeT *vertexLabel4Node = NULL;
    GmcNodeT *vertexLabel5Node = NULL;
    GmcNodeT *vertexLabel6Node = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(g_stmt_sync_1, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "content-id",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groupsAA
    GmcNodeT *groupAANode = NULL;
    ret = GmcYangEditChildNode(vertexLabel1Node, "groupAA", GMC_OPERATION_INSERT, &groupAANode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_2, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int m = 0; m < 2; m++) {
            // 设置g_vertexLabel3 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_3, g_vertexLabel3, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_2, g_stmt_sync_3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_3, &vertexLabel3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[15];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 15, "name%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[15];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 15, "revision%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue2, (strlen(namevalue2)), "revision",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue3[15];
            memset(namevalue3, 0, sizeof(namevalue3));
            (void)snprintf(namevalue3, 15, "namespace%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)), "namespace",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int n = 0; n < 2; n++) {
                // 设置g_vertexLabel4 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_4, g_vertexLabel4, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_3, g_stmt_sync_4);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_4, &vertexLabel4Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue4[15];
                memset(namevalue4, 0, sizeof(namevalue4));
                (void)snprintf(namevalue4, 15, "location%d", n);
                ret = testYangSetField(g_stmt_sync_4, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)),
                    "location", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_4);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            for (int k = 0; k < 2; k++) {
                // 设置g_vertexLabel5 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_5, g_vertexLabel5, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_3, g_stmt_sync_5);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_5, &vertexLabel5Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue5[15];
                memset(namevalue5, 0, sizeof(namevalue5));
                (void)snprintf(namevalue5, 15, "name%d", k);
                ret = testYangSetField(g_stmt_sync_5, GMC_DATATYPE_STRING, &namevalue5, (strlen(namevalue5)),
                    "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue6[15];
                memset(namevalue6, 0, sizeof(namevalue6));
                (void)snprintf(namevalue6, 15, "revision%d", k);
                ret = testYangSetField(g_stmt_sync_5, GMC_DATATYPE_STRING, &namevalue6, (strlen(namevalue6)),
                    "revision", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_5);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                for (int l = 0; l < 2; l++) {
                // 设置g_vertexLabel5 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_6, g_vertexLabel6, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_5, g_stmt_sync_6);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_6, &vertexLabel6Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue7[15];
                memset(namevalue7, 0, sizeof(namevalue7));
                (void)snprintf(namevalue7, 15, "location%d", l);
                ret = testYangSetField(g_stmt_sync_6, GMC_DATATYPE_STRING, &namevalue7, (strlen(namevalue7)),
                    "location", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_6);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                }
            }
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(conn);
}
// vertex表
void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t f7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret;
    char f0Value = (char)i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char f1Value = (unsigned char)i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t f2Value = (int8_t)i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t f4Value = (int16_t)i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool f8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void QueryVertexProperty(GmcStmtT *stmt, int i, const char *labelName, const char *pkName)
{
    int ret = 0;

    // scan
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t pkValue = i;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, pkName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // query
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        // Get F0
        char f0Value = i;
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &f0Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F1
        unsigned char f1Value = i;
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F2
        int8_t f2Value = i;
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &f2Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F3
        uint8_t f3Value = i;
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F4
        int16_t f4Value = i;
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F5
        uint16_t f5Value = i;
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F6
        int32_t f6Value = i;
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &f6Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Get F8
        bool f8Value = false;
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void CreateYangVertexLabel(GmcStmtT *stmt, const char *vertexpath, const char *edgepath,
    const char *tabelConfig = g_tabelConfig)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    readJanssonFile(vertexpath, &g_schema);
    AW_MACRO_ASSERT_NOTNULL(g_schema);
    ret = GmcCreateVertexLabelAsync(stmt, g_schema, g_tabelConfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create yang vertexlabel error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema);

    readJanssonFile(edgepath, &g_edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(stmt, g_edgeSchema, g_tabelConfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create yang edgelabel error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeSchema);
}

void DropYangVertexLabel(GmcStmtT *stmt, const char *edgelabelname,
    const char *rootlabelname, const char *chilelabelname)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    // drop edge表
    ret = GmcDropEdgeLabelAsync(stmt, edgelabelname, drop_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "drop yang edge error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // drop vertex表
    ret = GmcDropVertexLabelAsync(stmt, rootlabelname, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "drop yang vertexlabel1 error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropVertexLabelAsync(stmt, chilelabelname, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "drop yang vertexlabel2 error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void testYangSetVertexProperty(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType, int expstatus = GMERR_OK)
{
    int ret = 0;
    uint32_t value = i;

    // 构造超大字符串，从而构造一个字段有10k大小
    int length = g_endNum * g_endNum;
    string mark = "string" + to_string(i + 1) + ":";
    string str = WriteStringToSpecialSize(length, mark);
    char *strValue = (char *)str.c_str();

    uint32_t valueF0 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);

    uint32_t valueF1 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(expstatus, ret);
}

void testYangSetVertexPropertyWithoutF0(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    // 构造超大字符串，从而构造一个字段有10k大小
    int length = g_endNum * g_endNum;
    string mark = "string" + to_string(i + 1) + ":";
    string str = WriteStringToSpecialSize(length, mark);
    char *strValue = (char *)str.c_str();

    uint32_t valueF1 = value;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F7", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_STRING, strValue, (strlen(strValue)), "F8", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetVertexProperty_PK(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void useNameSpace(GmcStmtT *stmt, const char *nameSpace)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    ret = GmcUseNamespaceAsync(stmt, nameSpace, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "use namespace error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void dropNameSpace(GmcStmtT *stmt, const char *nameSpace)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    ret = GmcDropNamespaceAsync(stmt, nameSpace, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "drop %s error code:%d\n", nameSpace, userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}


void CreateVertexLabel(GmcStmtT *stmt, const char *filepath, const char * config = g_tabelConfig)
{
    int ret = 0;
    char *schema = NULL;
    AsyncUserDataT userData = {0};
    readJanssonFile(filepath, &schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, schema);

    ret = GmcCreateVertexLabelAsync(stmt, schema, config, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create vertexlabel error code:%d\n", userData.status);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(schema);
}

void DropVertexLabel(GmcStmtT *stmt, const char *labelname)
{
    int ret = 0;
    AsyncUserDataT userData = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelname, drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "drop vertexlabel error code:%d\n", userData.status);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TestStopServer()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh -f");
}
#endif
