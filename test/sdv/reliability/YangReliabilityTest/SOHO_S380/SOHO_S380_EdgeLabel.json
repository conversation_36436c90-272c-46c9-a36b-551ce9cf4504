[{"name": "ietf-yang-library:yang-library::module-set", "source_vertex_label": "ietf-yang-library:yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::feature", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "source_vertex_label": "ietf-yang-library:yang-library::module-set::module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::module::deviation", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "source_vertex_label": "ietf-yang-library:yang-library::module-set", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "dest_vertex_label": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema", "source_vertex_label": "ietf-yang-library:yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::schema", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::schema::module-set", "source_vertex_label": "ietf-yang-library:yang-library::schema", "dest_vertex_label": "ietf-yang-library:yang-library::schema::module-set", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "ietf-yang-library:yang-library::datastore", "source_vertex_label": "ietf-yang-library:yang-library", "dest_vertex_label": "ietf-yang-library:yang-library::datastore", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_vertex_label": "huawei-aaa:aaa", "dest_vertex_label": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "source_node_path": "/alive-user-qrys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aaa:aaa::lam::users::user", "source_vertex_label": "huawei-aaa:aaa", "dest_vertex_label": "huawei-aaa:aaa::lam::users::user", "source_node_path": "/lam/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_vertex_label": "huawei-time-range:time-range", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "source_node_path": "/time-range-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "source_node_path": "/absolute-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance", "dest_vertex_label": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "source_node_path": "/period-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group", "source_vertex_label": "huawei-acl:acl", "dest_vertex_label": "huawei-acl:acl::groups::group", "source_node_path": "/groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "source_node_path": "/rule-basics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "source_node_path": "/rule-advances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_vertex_label": "huawei-acl:acl::groups::group", "dest_vertex_label": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "source_node_path": "/rule-ethernets", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::ip-pools::ip-pool", "source_vertex_label": "huawei-acl:acl", "dest_vertex_label": "huawei-acl:acl::ip-pools::ip-pool", "source_node_path": "/ip-pools", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr", "source_vertex_label": "huawei-acl:acl::ip-pools::ip-pool", "dest_vertex_label": "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr", "source_node_path": "/apply-type/apply-ip/ipaddrs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::port-pools::port-pool", "source_vertex_label": "huawei-acl:acl", "dest_vertex_label": "huawei-acl:acl::port-pools::port-pool", "source_node_path": "/port-pools", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-acl:acl::port-pools::port-pool::ports::port", "source_vertex_label": "huawei-acl:acl::port-pools::port-pool", "dest_vertex_label": "huawei-acl:acl::port-pools::port-pool::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance", "source_vertex_label": "huawei-network-instance:network-instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance", "source_node_path": "/instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "source_node_path": "/huawei-l3vpn:afs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "source_node_path": "/huawei-routing:routing/routing-manage/topologys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "source_node_path": "/routes/ipv4-unicast-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "source_node_path": "/routes/ipv4-route-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "source_node_path": "/huawei-routing:routing/static-routing/unicast-route2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "source_node_path": "/nexthop-interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "source_node_path": "/nexthop-interface-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "source_node_path": "/nexthop-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "dest_vertex_label": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "source_node_path": "/huawei-routing:routing/static-routing/ipv4-routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface", "source_vertex_label": "huawei-ifm:ifm", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface", "source_node_path": "/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "source_node_path": "/huawei-ip:ipv4/address/common-address/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "source_node_path": "/huawei-ip:ipv4/state/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "source_node_path": "/huawei-ip:ipv4/huawei-arp:static-arps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/dns-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/excluded-ip-addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/static-binds", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "source_node_path": "/huawei-dhcp:interface-ip-pool/select-type/interface/select-interface/options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "source_node_path": "/option-format/ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "source_node_path": "/option-format/sub-options-format/sub-options", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "source_node_path": "/option-format/sub-ip-format", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "source_node_path": "/huawei-ethernet:ethernet/main-interface/port-isolate-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "source_node_path": "/huawei-lldp:lldp/session/neighbors", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "source_node_path": "/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "source_node_path": "/protocol-vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "source_node_path": "/vlan-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "source_node_path": "/unknown-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "source_node_path": "/unknown-organizationally-defined-tlvs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "source_node_path": "/med-tlv/capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "source_node_path": "/med-tlv/network-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "source_node_path": "/legacy-power-capability", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "source_node_path": "/huawei-mirror:mirror/observe-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "source_node_path": "/huawei-pppoe-client:pppoe-client-session-summarys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "source_node_path": "/huawei-sacl:traffic-filter-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "source_node_path": "/huawei-sacl:traffic-remark-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "source_node_path": "/huawei-sacl:traffic-redirect-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "source_node_path": "/huawei-sacl:traffic-mirror-applys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "source_node_path": "/acl-instances", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "source_node_path": "/statuses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-storm-control:storm-control::storm-rates::storm-rate", "source_vertex_label": "huawei-ifm:ifm::interfaces::interface", "dest_vertex_label": "huawei-ifm:ifm::interfaces::interface::huawei-storm-control:storm-control::storm-rates::storm-rate", "source_node_path": "/huawei-storm-control:storm-control/storm-rates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::query-entries::query-entry", "source_vertex_label": "huawei-arp:arp", "dest_vertex_label": "huawei-arp:arp::query-entries::query-entry", "source_node_path": "/query-entries", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-arp:arp::statistics::statistic", "source_vertex_label": "huawei-arp:arp", "dest_vertex_label": "huawei-arp:arp::statistics::statistic", "source_node_path": "/statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_vertex_label": "huawei-dhcp:dhcp", "dest_vertex_label": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "source_node_path": "/server/ip-pool-querys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_vertex_label": "huawei-lldp:lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-supported", "source_node_path": "/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_vertex_label": "huawei-lldp:lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "source_node_path": "/local-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_vertex_label": "huawei-lldp:lldp", "dest_vertex_label": "huawei-lldp:lldp::local-info::management-addresss::management-address", "source_node_path": "/local-info/management-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mirror:mirror::global-observers::global-observer", "source_vertex_label": "huawei-mirror:mirror", "dest_vertex_label": "huawei-mirror:mirror::global-observers::global-observer", "source_node_path": "/global-observers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-storm-control:storm-control::storm-rates::storm-rate", "source_vertex_label": "huawei-storm-control:storm-control", "dest_vertex_label": "huawei-storm-control:storm-control::storm-rates::storm-rate", "source_node_path": "/storm-rates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan", "source_vertex_label": "huawei-vlan:vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan", "source_node_path": "/vlans", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "source_node_path": "/member-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_vertex_label": "huawei-vlan:vlan::vlans::vlan", "dest_vertex_label": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "source_node_path": "/huawei-mac:mac-addresss", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_vertex_label": "huawei-mac:mac", "dest_vertex_label": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "source_node_path": "/vlan-dynamic-macs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "source_vertex_label": "huawei-mac:mac", "dest_vertex_label": "huawei-mac:mac::mac-statistics::mac-statistic", "source_node_path": "/mac-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-aspf:aspf::protocol", "source_vertex_label": "huawei-aspf:aspf", "dest_vertex_label": "huawei-aspf:aspf::protocol", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "source_vertex_label": "huawei-cfg:cfg", "dest_vertex_label": "huawei-cfg:cfg::startup-infos::startup-info", "source_node_path": "/startup-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "source_vertex_label": "huawei-cfg:cfg", "dest_vertex_label": "huawei-cfg:cfg::cfg-files::cfg-file", "source_node_path": "/cfg-files", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "source_vertex_label": "huawei-codesign:codesign", "dest_vertex_label": "huawei-codesign:codesign::software-crls::software-crl", "source_node_path": "/software-crls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "source_vertex_label": "huawei-codesign:codesign", "dest_vertex_label": "huawei-codesign:codesign::crl-names::crl-name", "source_node_path": "/crl-names", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_vertex_label": "huawei-cpu-memory:cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "source_node_path": "/board-cpu-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_vertex_label": "huawei-cpu-memory:cpu-memory", "dest_vertex_label": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "source_node_path": "/board-memory-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "source_vertex_label": "huawei-devm-poe:devm-poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "source_node_path": "/poes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_vertex_label": "huawei-devm-poe:devm-poe::poes::poe", "dest_vertex_label": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "source_vertex_label": "huawei-devm:devm", "dest_vertex_label": "huawei-devm:devm::physical-entitys::physical-entity", "source_node_path": "/physical-entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "source_vertex_label": "huawei-devm:devm", "dest_vertex_label": "huawei-devm:devm::mpu-boards::mpu-board", "source_node_path": "/mpu-boards", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-devm:devm::ports::port", "source_vertex_label": "huawei-devm:devm", "dest_vertex_label": "huawei-devm:devm::ports::port", "source_node_path": "/ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "source_node_path": "/huawei-diagnose-ip:ipv4/addresses", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "source_node_path": "/huawei-diagnose-lsw-chip:lsw-chip/mac-learnings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "source_node_path": "/huawei-diagnose-lsw-chip:lsw-chip/vlan-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "source_node_path": "/", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "source_node_path": "/huawei-diagnose-lsw-chip:lsw-chip/car-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "source_node_path": "/huawei-diagnose-lsw-chip:lsw-chip/acl-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "source_node_path": "/acl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "source_node_path": "/huawei-diagnose-nctl:nctl/innertables", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "source_node_path": "/huawei-diagnose-omu:omu/notif/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "source_node_path": "/huawei-diagnose-omu:omu/notif/subscriptions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "source_node_path": "/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "source_node_path": "/indicators", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "source_node_path": "/huawei-diagnose-routing-lite:routing/routing-manage/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "source_node_path": "/huawei-diagnose-routing-lite:routing/direct-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "source_node_path": "/huawei-diagnose-routing-lite:routing/static-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_vertex_label": "huawei-diagnose:diagnose", "dest_vertex_label": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "source_node_path": "/huawei-diagnose-routing-lite:routing/user-network-routing/routes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "source_node_path": "/ipv4/ping-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "source_node_path": "/ipv4/trace-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "dest_vertex_label": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "source_node_path": "/details", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::domains::domain", "source_vertex_label": "huawei-dns:dns", "dest_vertex_label": "huawei-dns:dns::domains::domain", "source_node_path": "/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_vertex_label": "huawei-dns:dns", "dest_vertex_label": "huawei-dns:dns::ipv4-hosts::ipv4-host", "source_node_path": "/ipv4-hosts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_vertex_label": "huawei-dns:dns", "dest_vertex_label": "huawei-dns:dns::ipv4-servers::ipv4-server", "source_node_path": "/ipv4-servers", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "source_vertex_label": "huawei-dns:dns", "dest_vertex_label": "huawei-dns:dns::query-host-ips::query-host-ip", "source_node_path": "/query-host-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::temperature2s::temperature2", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver:driver", "dest_vertex_label": "huawei-driver:driver::temperature2s::temperature2", "source_node_path": "/temperature2s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver:driver", "dest_vertex_label": "huawei-driver:driver::device-health-checks::device-health-check", "source_node_path": "/device-health-checks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "source_vertex_label": "<PERSON><PERSON><PERSON>-driver:driver", "dest_vertex_label": "huawei-driver:driver::electronic-labels::electronic-label", "source_node_path": "/electronic-labels", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-file-operation:file-operation::dirs::dir", "source_vertex_label": "huawei-file-operation:file-operation", "dest_vertex_label": "huawei-file-operation:file-operation::dirs::dir", "source_node_path": "/dirs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::alarms::alarm", "source_vertex_label": "huawei-fm:fm", "dest_vertex_label": "huawei-fm:fm::alarms::alarm", "source_node_path": "/alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "source_vertex_label": "huawei-fm:fm", "dest_vertex_label": "huawei-fm:fm::active-alarms::active-alarm", "source_node_path": "/active-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "source_vertex_label": "huawei-fm:fm", "dest_vertex_label": "huawei-fm:fm::history-alarms::history-alarm", "source_node_path": "/history-alarms", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-ftpc:ftpc", "dest_vertex_label": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "source_node_path": "/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-gnss:gnss::gnss-satellite-infos::gnss-satellite-info", "source_vertex_label": "huawei-gnss:gnss", "dest_vertex_label": "huawei-gnss:gnss::gnss-satellite-infos::gnss-satellite-info", "source_node_path": "/gnss-satellite-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-gnss:gnss::gnss-info::gnss-positions::gnss-position", "source_vertex_label": "huawei-gnss:gnss", "dest_vertex_label": "huawei-gnss:gnss::gnss-info::gnss-positions::gnss-position", "source_node_path": "/gnss-info/gnss-positions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_vertex_label": "huawei-host-security:host-security", "dest_vertex_label": "huawei-host-security:host-security::anti-attacks::anti-attack", "source_node_path": "/anti-attacks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_vertex_label": "huawei-host-security:host-security", "dest_vertex_label": "huawei-host-security:host-security::packet-statistics::packet-statistic", "source_node_path": "/packet-statistics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_vertex_label": "huawei-host-security:host-security", "dest_vertex_label": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "source_node_path": "/adjust-car", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy", "source_vertex_label": "huawei-host-security:host-security", "dest_vertex_label": "huawei-host-security:host-security::policys::policy", "source_node_path": "/policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::filters::filter", "source_node_path": "/filters", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "source_node_path": "/cpcars", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "source_node_path": "/auto-defend", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_vertex_label": "huawei-host-security:host-security::policys::policy", "dest_vertex_label": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "source_node_path": "/applied-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_vertex_label": "huawei-ssl:ssl", "dest_vertex_label": "huawei-ssl:ssl::ssl-policys::ssl-policy", "source_node_path": "/ssl-policys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "source_vertex_label": "huawei-http:http", "dest_vertex_label": "huawei-http:http::transfer-tasks::transfer-task", "source_node_path": "/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::end-devices::end-device", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::end-devices::end-device", "source_node_path": "/end-devices", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apps::app", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::apps::app", "source_node_path": "/iotbus-broker/apps", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apps::app::app-services::app-service", "source_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::apps::app", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::apps::app::app-services::app-service", "source_node_path": "/app-services", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::discovery-requests::discovery-request", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::discovery-requests::discovery-request", "source_node_path": "/iotbus-broker/discovery-requests", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::reg-fail-records::reg-fail-record", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::reg-fail-records::reg-fail-record", "source_node_path": "/iotbus-broker/reg-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apply-certificate-fail-records::apply-certificate-fail-record", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::apply-certificate-fail-records::apply-certificate-fail-record", "source_node_path": "/iotbus-broker/apply-certificate-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client", "source_vertex_label": "huawei-iotbus:iotbus", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client", "source_node_path": "/iotbus-broker/mqtt-clients", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client::sub-topics::sub-topic", "source_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client", "dest_vertex_label": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client::sub-topics::sub-topic", "source_node_path": "/sub-topics", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-module-management:module-management::module-infos::module-info", "source_vertex_label": "huawei-module-management:module-management", "dest_vertex_label": "huawei-module-management:module-management::module-infos::module-info", "source_node_path": "/module-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_vertex_label": "huawei-nat-address-group:nat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "source_node_path": "/snat-address-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "source_node_path": "/sections", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "source_node_path": "/exclude-ips", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "dest_vertex_label": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "source_node_path": "/exclude-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule", "source_vertex_label": "huawei-nat-policy:nat-policy", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "source_node_path": "/rules", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "source_node_path": "/egress/interfaces", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "source_node_path": "/source-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/source-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/source-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/source-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "source_node_path": "/destination-address/address-ipv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "source_node_path": "/destination-address/address-ipv4-ranges", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "source_node_path": "/destination-address/address-ipv4-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "source_node_path": "/destination-address/address-ipv4-range-excludes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "source_node_path": "/service/service-items/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "source_node_path": "/service/service-items/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "source_node_path": "/service/service-items-exclude/protocol-and-ports", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "source_node_path": "/service/service-items-exclude/icmpv4s", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_vertex_label": "huawei-nat-policy:nat-policy::rules::rule", "dest_vertex_label": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "source_node_path": "/service/service-items-exclude/protocol", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_vertex_label": "huawei-nat-server:nat-server", "dest_vertex_label": "huawei-nat-server:nat-server::server-mappings::server-mapping", "source_node_path": "/server-mappings", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::unicasts::unicast", "source_vertex_label": "huawei-ntp:ntp", "dest_vertex_label": "huawei-ntp:ntp::unicasts::unicast", "source_node_path": "/unicasts", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::authentications::authentication", "source_vertex_label": "huawei-ntp:ntp", "dest_vertex_label": "huawei-ntp:ntp::authentications::authentication", "source_node_path": "/authentications", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "source_vertex_label": "huawei-ntp:ntp", "dest_vertex_label": "huawei-ntp:ntp::full-sessions::full-session", "source_node_path": "/full-sessions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_vertex_label": "openconfig-telemetry:telemetry-system", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "source_node_path": "/sensor-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "source_node_path": "/sensor-paths", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry:telemetry-system", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "dest_vertex_label": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "source_node_path": "/destinations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_vertex_label": "openconfig-telemetry:telemetry-system", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "source_node_path": "/subscriptions/persistent", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "source_node_path": "/sensor-profiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "dest_vertex_label": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "source_node_path": "/destination-groups", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info", "source_vertex_label": "huawei-patch:patch", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "source_node_path": "/patch-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_vertex_label": "huawei-patch:patch::patch-infos::patch-info", "dest_vertex_label": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "source_node_path": "/operations", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_vertex_label": "huawei-patch:patch", "dest_vertex_label": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "source_node_path": "/next-startup-patchs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::entitys::entity", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::entitys::entity", "source_node_path": "/entitys", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::domains::domain", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::domains::domain", "source_node_path": "/domains", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "source_node_path": "/certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info", "dest_vertex_label": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "source_node_path": "/preset-certificate-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "dest_vertex_label": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "source_node_path": "/certificates", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::crl-infos::crl-info", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::crl-infos::crl-info", "source_node_path": "/crl-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::key-pair-infos::key-pair-info", "source_node_path": "/key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_vertex_label": "huawei-pki:pki", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "source_node_path": "/cert-key-pair-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "dest_vertex_label": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "source_node_path": "/cert-key-pairs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "source_node_path": "/protocol-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt", "dest_vertex_label": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "source_node_path": "/application-ttls", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_vertex_label": "huawei-smart-upgrade:smart-upgrade", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "source_node_path": "/smart-upgrade-info/download-infos", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "dest_vertex_label": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "source_node_path": "/download-lists", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::versions::version", "source_vertex_label": "huawei-software:software", "dest_vertex_label": "huawei-software:software::versions::version", "source_node_path": "/versions", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-software:software::startup-packages::startup-package", "source_vertex_label": "huawei-software:software", "dest_vertex_label": "huawei-software:software::startup-packages::startup-package", "source_node_path": "/startup-packages", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "source_vertex_label": "huawei-sshc:sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-results::transfer-result", "source_node_path": "/transfer-results", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_vertex_label": "huawei-sshc:sshc", "dest_vertex_label": "huawei-sshc:sshc::transfer-tasks::transfer-task", "source_node_path": "/transfer-tasks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::users::user", "source_vertex_label": "huawei-sshs:sshs", "dest_vertex_label": "huawei-sshs:sshs::users::user", "source_node_path": "/users", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_vertex_label": "huawei-sshs:sshs", "dest_vertex_label": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "source_node_path": "/ipv4-server-sources", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home", "source_vertex_label": "huawei-sshs:sshs", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "source_node_path": "/call-homes", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_vertex_label": "huawei-sshs:sshs::call-homes::call-home", "dest_vertex_label": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "source_node_path": "/end-points", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "source_vertex_label": "huawei-syslog:syslog", "dest_vertex_label": "huawei-syslog:syslog::log-switch-list::log-switch", "source_node_path": "/log-switch-list", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile", "source_vertex_label": "huawei-syslog:syslog", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "source_node_path": "/logfiles", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_vertex_label": "huawei-syslog:syslog::logfiles::logfile", "dest_vertex_label": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "source_node_path": "/latest-logs", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_vertex_label": "huawei-system:system", "dest_vertex_label": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "source_node_path": "/system-info/huawei-system-controller:upstream-info", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::security-risks::security-risk", "source_vertex_label": "huawei-system:system", "dest_vertex_label": "huawei-system:system::security-risks::security-risk", "source_node_path": "/security-risks", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system:system::weak-passwords::weak-password", "source_vertex_label": "huawei-system:system", "dest_vertex_label": "huawei-system:system::weak-passwords::weak-password", "source_node_path": "/weak-passwords", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "source_vertex_label": "huawei-system-controller:system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::offline-records::offline-record", "source_node_path": "/offline-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_vertex_label": "huawei-system-controller:system-controller", "dest_vertex_label": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "source_node_path": "/register-fail-records", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}, {"name": "nc-notifications:netconf::streams::stream", "source_vertex_label": "nc-notifications:netconf", "dest_vertex_label": "nc-notifications:netconf::streams::stream", "source_node_path": "/streams", "dest_node_path": "/", "constraint": {"operator_type": "and", "conditions": [{"source_property": ":id", "dest_property": ":pid"}]}}]