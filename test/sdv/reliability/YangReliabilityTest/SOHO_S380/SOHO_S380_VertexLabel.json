[{"name": "ietf-yang-library:yang-library", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "content-id", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}, {"name": "namespace", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::location", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::submodule::location", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::feature", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "feature"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::module::deviation", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "deviation", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "deviation"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false, "nullable": false}, {"name": "namespace", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "revision"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::location", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "revision", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::module-set::import-only-module::submodule::location", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "location", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "location"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::schema::module-set", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "module-set", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "module-set"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "ietf-yang-library:yang-library::datastore", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "schema", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alive-user-qrys", "type": "container", "is_config": false, "fields": []}, {"name": "lam", "type": "container", "fields": [{"name": "users", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::alive-user-qrys::alive-user-qry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "user-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "user-name", "type": "string", "is_config": false}, {"name": "ip", "type": "string", "is_config": false}, {"name": "access-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "user-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aaa:aaa::lam::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "group-name", "type": "string", "default": "admin"}, {"name": "password", "type": "string"}, {"name": "level", "type": "uint32"}, {"name": "service-terminal", "type": "boolean", "default": false}, {"name": "service-api", "type": "boolean", "default": false}, {"name": "password-force-change", "type": "boolean", "default": true}, {"name": "left-lock-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "time-range-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "absolute-ranges", "type": "container", "fields": []}, {"name": "period-ranges", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::absolute-ranges::absolute-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-time-range:time-range::time-range-instances::time-range-instance::period-ranges::period-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "day-of-week", "type": "string", "nullable": false}, {"name": "start-time", "type": "string", "nullable": false}, {"name": "end-time", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "day-of-week", "start-time", "end-time"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "groups", "type": "container", "fields": []}, {"name": "ip-pools", "type": "container", "fields": []}, {"name": "port-pools", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "identity", "type": "string", "nullable": false}, {"name": "type", "type": "string"}, {"name": "step", "type": "uint32", "default": 5}, {"name": "description", "type": "string"}, {"name": "number", "type": "uint32"}, {"name": "rule-basics", "type": "container", "fields": []}, {"name": "rule-advances", "type": "container", "fields": []}, {"name": "rule-ethernets", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "identity"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-basics::rule-basic", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "active"}, {"name": "source-ipaddr", "type": "string"}, {"name": "source-wild", "type": "string"}, {"name": "fragment-type", "type": "string"}, {"name": "time-range-name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "priority", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-advances::rule-advance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "active"}, {"name": "protocol-type", "type": "choice", "fields": [{"name": "single", "type": "case", "fields": [{"name": "protocol", "type": "uint8"}]}, {"name": "zero", "type": "case", "fields": [{"name": "protocol-zero", "type": "string"}]}]}, {"name": "source", "type": "choice", "fields": [{"name": "ip", "type": "case", "fields": [{"name": "source-ipaddr", "type": "string"}, {"name": "source-wild", "type": "string"}]}, {"name": "ip-pool", "type": "case", "fields": [{"name": "source-pool-name", "type": "string"}]}]}, {"name": "dest", "type": "choice", "fields": [{"name": "ip", "type": "case", "fields": [{"name": "dest-ipad<PERSON>", "type": "string"}, {"name": "dest-wild", "type": "string"}]}, {"name": "ip-pool", "type": "case", "fields": [{"name": "dest-pool-name", "type": "string"}]}]}, {"name": "packets-priority", "type": "choice", "fields": [{"name": "tos", "type": "case", "fields": [{"name": "precedence", "type": "uint8"}, {"name": "tos", "type": "uint8"}]}, {"name": "dscp", "type": "case", "fields": [{"name": "dscp", "type": "uint8"}]}]}, {"name": "ttl-expired", "type": "boolean", "default": false}, {"name": "tcp-flag", "type": "choice", "fields": [{"name": "mask", "type": "case", "fields": [{"name": "tcp-flag-value", "type": "uint8", "nullable": false}]}]}, {"name": "source-port", "type": "choice", "fields": [{"name": "range", "type": "case", "fields": [{"name": "source-port-begin", "type": "uint16"}, {"name": "source-port-end", "type": "uint16"}]}, {"name": "port-pool", "type": "case", "fields": [{"name": "src-port-pool-name", "type": "string"}]}]}, {"name": "dest-port", "type": "choice", "fields": [{"name": "range", "type": "case", "fields": [{"name": "dest-port-begin", "type": "uint16"}, {"name": "dest-port-end", "type": "uint16"}]}, {"name": "port-pool", "type": "case", "fields": [{"name": "dest-port-pool-name", "type": "string"}]}]}, {"name": "description", "type": "string"}, {"name": "fragment-type", "type": "string"}, {"name": "igmp-type", "type": "uint16"}, {"name": "icmp-type", "type": "uint16"}, {"name": "icmp-code", "type": "uint16"}, {"name": "priority", "type": "uint32", "is_config": false}, {"name": "time-range-name", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::groups::group::rule-ethernets::rule-ethernet", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "id", "type": "uint32"}, {"name": "action", "type": "string", "nullable": false}, {"name": "active-status", "type": "string", "is_config": false, "default": "active"}, {"name": "frame-type", "type": "string"}, {"name": "frame-mask", "type": "string", "default": "0xffff"}, {"name": "source-mac", "type": "string"}, {"name": "source-mac-mask", "type": "string", "default": "ffff-ffff-ffff"}, {"name": "dest-mac", "type": "string"}, {"name": "dest-mac-mask", "type": "string", "default": "ffff-ffff-ffff"}, {"name": "vlan-id", "type": "uint16"}, {"name": "vlan-id-mask", "type": "string", "default": "0xfff"}, {"name": "value-8021p", "type": "uint8"}, {"name": "time-range-name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "priority", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::ip-pools::ip-pool", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "apply-type", "type": "choice", "fields": [{"name": "apply-interface", "type": "case", "fields": [{"name": "interface-type", "type": "string"}]}, {"name": "apply-ip", "type": "case", "default": true, "fields": [{"name": "ipaddrs", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "address", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::port-pools::port-pool", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "ports", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-acl:acl::port-pools::port-pool::ports::port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "operation", "type": "string", "nullable": false}, {"name": "number-begin", "type": "uint16", "nullable": false}, {"name": "number-end", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "operation", "number-begin", "number-end"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "huawei-l3vpn:afs", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "nullable": false}, {"name": "huawei-routing:routing", "type": "container", "fields": [{"name": "routing-manage", "type": "container", "fields": [{"name": "topologys", "type": "container", "fields": []}]}, {"name": "static-routing", "type": "container", "fields": [{"name": "unicast-route2s", "type": "container", "fields": []}, {"name": "ipv4-routes", "type": "container", "is_config": false, "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "routes", "type": "container", "is_config": false, "fields": [{"name": "ipv4-unicast-routes", "type": "container", "is_config": false, "fields": []}, {"name": "ipv4-route-statistics", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-unicast-routes::ipv4-unicast-route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "process-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "direct-nexthop", "type": "string", "is_config": false, "nullable": false}, {"name": "indirect-id", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop", "type": "string", "is_config": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "active", "type": "boolean", "is_config": false, "default": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "age", "type": "string", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "protocol-type", "interface-name", "process-id", "direct-nexthop", "indirect-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::routing-manage::topologys::topology::routes::ipv4-route-statistics::ipv4-route-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "string", "is_config": false, "nullable": false}, {"name": "total-num", "type": "uint32", "is_config": false}, {"name": "active-num", "type": "uint32", "is_config": false}, {"name": "added-num", "type": "uint32", "is_config": false}, {"name": "deleted-num", "type": "uint32", "is_config": false}, {"name": "freed-num", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "topology-name", "type": "string", "nullable": false}, {"name": "prefix", "type": "string", "nullable": false}, {"name": "mask-length", "type": "uint8", "nullable": false}, {"name": "nexthop-interfaces", "type": "container", "fields": []}, {"name": "nexthop-interface-addresses", "type": "container", "fields": []}, {"name": "nexthop-addresses", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "topology-name", "prefix", "mask-length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interfaces::nexthop-interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false}, {"name": "preference", "type": "int32"}, {"name": "cost", "type": "uint32"}, {"name": "dhcp-enable", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-interface-addresses::nexthop-interface-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false}, {"name": "address", "type": "string", "nullable": false}, {"name": "preference", "type": "int32"}, {"name": "cost", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "interface-name", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::unicast-route2s::unicast-route2::nexthop-addresses::nexthop-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "nullable": false}, {"name": "preference", "type": "int32"}, {"name": "inherit-cost", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::huawei-routing:routing::static-routing::ipv4-routes::ipv4-route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "topology-name", "type": "string", "is_config": false, "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "vpn-destination-name", "type": "string", "is_config": false, "nullable": false}, {"name": "next-hop", "type": "string", "is_config": false, "nullable": false}, {"name": "relay-next-hop", "type": "string", "is_config": false, "nullable": false}, {"name": "relay-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "tunnel-id", "type": "string", "is_config": false, "nullable": false}, {"name": "color", "type": "uint32", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "interface-state", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "inherit-cost", "type": "boolean", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "topology-name", "prefix", "mask-length", "interface-name", "vpn-destination-name", "next-hop", "relay-next-hop", "relay-interface-name", "tunnel-id", "color"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interfaces", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "class", "type": "string"}, {"name": "type", "type": "string"}, {"name": "description", "type": "string"}, {"name": "admin-status", "type": "string"}, {"name": "link-protocol", "type": "string"}, {"name": "mtu", "type": "uint32"}, {"name": "vrf-name", "type": "string", "default": "_public_"}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "is-l2-switch", "type": "boolean", "is_config": false, "default": false}, {"name": "service-type", "type": "string", "is_config": false}, {"name": "dynamic", "type": "container", "is_config": false, "fields": [{"name": "oper-status", "type": "string", "is_config": false}, {"name": "physical-status", "type": "string", "is_config": false}, {"name": "link-status", "type": "string", "is_config": false}, {"name": "mtu", "type": "uint32", "is_config": false}, {"name": "bandwidth", "type": "uint64", "is_config": false}, {"name": "ipv4-status", "type": "string", "is_config": false}, {"name": "ipv6-status", "type": "string", "is_config": false}, {"name": "is-control-flap-damp", "type": "boolean", "is_config": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "is-offline", "type": "boolean", "is_config": false}, {"name": "link-quality-grade", "type": "string", "is_config": false}, {"name": "sub-if-counts", "type": "uint32", "is_config": false}]}, {"name": "mib-statistics", "type": "container", "is_config": false, "fields": [{"name": "receive-byte", "type": "uint64", "is_config": false}, {"name": "send-byte", "type": "uint64", "is_config": false}, {"name": "receive-packet", "type": "uint64", "is_config": false}, {"name": "send-packet", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-broad-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet", "type": "uint64", "is_config": false}, {"name": "send-broad-packet", "type": "uint64", "is_config": false}, {"name": "receive-error-packet", "type": "uint64", "is_config": false}, {"name": "receive-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-error-packet", "type": "uint64", "is_config": false}, {"name": "send-drop-packet", "type": "uint64", "is_config": false}, {"name": "huawei-pic:eth-port-err-sts", "type": "container", "is_config": false, "fields": [{"name": "rx-crc", "type": "uint64", "is_config": false, "default": 0}]}]}, {"name": "common-statistics", "type": "container", "is_config": false, "fields": [{"name": "stati-interval", "type": "uint32", "is_config": false, "default": 300}, {"name": "in-byte-rate", "type": "uint64", "is_config": false}, {"name": "in-bit-rate", "type": "uint64", "is_config": false}, {"name": "in-packet-rate", "type": "uint64", "is_config": false}, {"name": "in-use-rate", "type": "string", "is_config": false}, {"name": "out-byte-rate", "type": "uint64", "is_config": false}, {"name": "out-bit-rate", "type": "uint64", "is_config": false}, {"name": "out-packet-rate", "type": "uint64", "is_config": false}, {"name": "out-use-rate", "type": "string", "is_config": false}, {"name": "receive-byte", "type": "uint64", "is_config": false}, {"name": "send-byte", "type": "uint64", "is_config": false}, {"name": "receive-packet", "type": "uint64", "is_config": false}, {"name": "send-packet", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet", "type": "uint64", "is_config": false}, {"name": "receive-broad-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet", "type": "uint64", "is_config": false}, {"name": "send-broad-packet", "type": "uint64", "is_config": false}, {"name": "receive-error-packet", "type": "uint64", "is_config": false}, {"name": "receive-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-error-packet", "type": "uint64", "is_config": false}, {"name": "send-drop-packet", "type": "uint64", "is_config": false}, {"name": "send-unicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-unicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-multicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-multicast-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-broad-bit-rate", "type": "uint64", "is_config": false}, {"name": "receive-broad-bit-rate", "type": "uint64", "is_config": false}, {"name": "send-unicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-unicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "send-multicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-multicast-packet-rate", "type": "uint64", "is_config": false}, {"name": "send-broadcast-packet-rate", "type": "uint64", "is_config": false}, {"name": "receive-broadcast-packet-rate", "type": "uint64", "is_config": false}]}, {"name": "huawei-ip:ipv4", "type": "container", "fields": [{"name": "address", "type": "choice", "fields": [{"name": "common-address", "type": "case", "fields": [{"name": "addresses", "type": "container", "fields": []}]}, {"name": "negotiate-address", "type": "case", "fields": [{"name": "negotiation-address", "type": "container", "fields": [{"name": "negotiation-type", "type": "string", "nullable": false}]}]}]}, {"name": "state", "type": "container", "is_config": false, "fields": [{"name": "addresses", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-arp:static-arps", "type": "container", "fields": []}]}, {"name": "huawei-arp:arp-entry", "type": "container", "fields": [{"name": "expire-time", "type": "uint32"}, {"name": "arp-learn-disable", "type": "boolean"}, {"name": "route-proxy-enable", "type": "boolean"}, {"name": "inner-proxy-enable", "type": "boolean"}, {"name": "dest-mac-check", "type": "boolean"}, {"name": "src-mac-check", "type": "boolean"}]}, {"name": "huawei-dhcp:interface-ip-pool", "type": "container", "fields": [{"name": "select-type", "type": "choice", "fields": [{"name": "interface", "type": "case", "fields": [{"name": "select-interface", "type": "container", "presence": true, "fields": [{"name": "server-name", "type": "string"}, {"name": "domain-name", "type": "string"}, {"name": "next-server", "type": "string"}, {"name": "lease", "type": "container", "fields": [{"name": "time-type", "type": "choice", "fields": [{"name": "limited", "type": "case", "fields": [{"name": "day", "type": "uint16", "default": 1}, {"name": "hour", "type": "uint8", "default": 0}, {"name": "minute", "type": "uint8", "default": 0}]}, {"name": "unlimited", "type": "case", "fields": [{"name": "unlimited", "type": "string"}]}]}]}, {"name": "dns-list", "type": "container", "fields": []}, {"name": "sip-server", "type": "container", "fields": [{"name": "sip-server-format", "type": "choice", "fields": [{"name": "ip-format", "type": "case", "fields": [{"name": "sip-server-ip1", "type": "string", "nullable": false}, {"name": "sip-server-ip2", "type": "string"}]}, {"name": "string-format", "type": "case", "fields": [{"name": "sip-server-name1", "type": "string", "nullable": false}, {"name": "sip-server-name2", "type": "string"}]}]}]}, {"name": "excluded-ip-addresses", "type": "container", "fields": []}, {"name": "static-binds", "type": "container", "fields": []}, {"name": "options", "type": "container", "fields": []}, {"name": "auto-recycle", "type": "container", "fields": [{"name": "day", "type": "uint16", "default": 0}, {"name": "hour", "type": "uint8", "default": 0}, {"name": "minute", "type": "uint8", "default": 0}]}, {"name": "ip-pool-statistics", "type": "container", "is_config": false, "fields": [{"name": "used-ip-count", "type": "uint32", "is_config": false}, {"name": "idle-ip-count", "type": "uint32", "is_config": false}, {"name": "expired-ip-count", "type": "uint32", "is_config": false}, {"name": "conflict-ip-count", "type": "uint32", "is_config": false}, {"name": "disable-ip-count", "type": "uint32", "is_config": false}, {"name": "total-ip-count", "type": "uint32", "is_config": false}, {"name": "start-ip", "type": "string", "is_config": false}, {"name": "end-ip", "type": "string", "is_config": false}]}]}]}]}]}, {"name": "huawei-dhcp:dhcp-client-if", "type": "container", "fields": [{"name": "address-allocation", "type": "string"}, {"name": "client-status", "type": "container", "is_config": false, "fields": [{"name": "fsm-state", "type": "string", "is_config": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "ip-address", "type": "string", "is_config": false}, {"name": "ip-mask", "type": "string", "is_config": false}, {"name": "server-address", "type": "string", "is_config": false}, {"name": "lease-obtained-time", "type": "string", "is_config": false}, {"name": "lease-expire-time", "type": "string", "is_config": false}, {"name": "lease-renew-time", "type": "string", "is_config": false}, {"name": "lease-rebind-time", "type": "string", "is_config": false}]}, {"name": "client-statistics", "type": "container", "is_config": false, "fields": [{"name": "total-packets-received", "type": "uint32", "is_config": false}, {"name": "bootp-reply-packets-received", "type": "uint32", "is_config": false}, {"name": "offer-packets-received", "type": "uint32", "is_config": false}, {"name": "ack-packets-received", "type": "uint32", "is_config": false}, {"name": "nak-packets-received", "type": "uint32", "is_config": false}, {"name": "total-packets-send", "type": "uint32", "is_config": false}, {"name": "bootp-request-packets-send", "type": "uint32", "is_config": false}, {"name": "discovery-packets-send", "type": "uint32", "is_config": false}, {"name": "request-packets-send", "type": "uint32", "is_config": false}, {"name": "reboot-request-packets-send", "type": "uint32", "is_config": false}, {"name": "select-request-packets-send", "type": "uint32", "is_config": false}, {"name": "renew-request-packets-send", "type": "uint32", "is_config": false}, {"name": "rebind-request-packets-send", "type": "uint32", "is_config": false}, {"name": "decline-packets-send", "type": "uint32", "is_config": false}, {"name": "release-packets-send", "type": "uint32", "is_config": false}]}]}, {"name": "huawei-ethernet:ethernet", "type": "container", "fields": [{"name": "main-interface", "type": "container", "fields": [{"name": "l2-attribute", "type": "container", "presence": true, "fields": [{"name": "link-type", "type": "string", "default": "hybrid"}, {"name": "pvid", "type": "uint16"}, {"name": "trunk-vlans", "type": "string"}, {"name": "untag-vlans", "type": "string"}]}, {"name": "port-isolate-groups", "type": "container", "fields": []}]}]}, {"name": "huawei-lldp:lldp", "type": "container", "fields": [{"name": "session", "type": "container", "fields": [{"name": "admin-status", "type": "string", "default": "tx-rx"}, {"name": "tlv-enable", "type": "container", "fields": [{"name": "management-address", "type": "boolean", "default": true}, {"name": "port-description", "type": "boolean", "default": true}, {"name": "system-capability", "type": "boolean", "default": true}, {"name": "system-description", "type": "boolean", "default": true}, {"name": "system-name", "type": "boolean", "default": true}, {"name": "mac-physic", "type": "boolean", "default": true}, {"name": "link-aggregation", "type": "boolean", "default": true}, {"name": "max-frame-size", "type": "boolean", "default": true}]}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "port-id-sub-type", "type": "string", "is_config": false}, {"name": "port-id", "type": "string", "is_config": false}, {"name": "port-description", "type": "string", "is_config": false}, {"name": "auto-negotiation-supported", "type": "string", "is_config": false}, {"name": "auto-negotiation-enabled", "type": "string", "is_config": false}, {"name": "auto-negotiation-capability", "type": "string", "is_config": false}, {"name": "oper-mau-type", "type": "string", "is_config": false}, {"name": "link-aggregation-supported", "type": "string", "is_config": false}, {"name": "link-aggregation-enabled", "type": "string", "is_config": false}, {"name": "aggregation-port-id", "type": "int32", "is_config": false}, {"name": "maximum-frame-size", "type": "int32", "is_config": false}]}, {"name": "neighbors", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": [{"name": "total-neighbors", "type": "uint32", "is_config": false}, {"name": "transmitted-frames", "type": "uint32", "is_config": false}, {"name": "received-frames", "type": "uint32", "is_config": false}, {"name": "discarded-frames", "type": "uint32", "is_config": false}, {"name": "error-frames", "type": "uint32", "is_config": false}, {"name": "discarded-tlvs", "type": "uint32", "is_config": false}, {"name": "unrecognized-tlvs", "type": "uint32", "is_config": false}, {"name": "expired-neighbors", "type": "uint32", "is_config": false}, {"name": "received-dcbx-tlvs", "type": "uint32", "is_config": false}, {"name": "received-med-tlvs", "type": "uint32", "is_config": false}, {"name": "received-network-card-id-tlvs", "type": "uint32", "is_config": false}]}]}]}, {"name": "huawei-mirror:mirror", "type": "container", "fields": [{"name": "observe-ports", "type": "container", "fields": []}]}, {"name": "huawei-nat:nat", "type": "container", "fields": [{"name": "nat-enable", "type": "boolean", "default": true}]}, {"name": "huawei-ppp-net:ppp-net", "type": "container", "fields": [{"name": "ppp", "type": "container", "fields": [{"name": "ppp-base", "type": "container", "fields": [{"name": "pap-authen-flag", "type": "string", "default": "disable"}, {"name": "chap-authen-flag", "type": "string", "default": "disable"}, {"name": "pap-user-name", "type": "string"}, {"name": "pap-password", "type": "string"}, {"name": "chap-user-name", "type": "string"}, {"name": "chap-password", "type": "string"}]}, {"name": "ppp-link-info", "type": "container", "is_config": false, "fields": [{"name": "peer-mru", "type": "uint32", "is_config": false}, {"name": "magic-number", "type": "uint32", "is_config": false}, {"name": "local-ip-address", "type": "string", "is_config": false}, {"name": "peer-ip-address", "type": "string", "is_config": false}, {"name": "phase", "type": "string", "is_config": false}, {"name": "primary-dns-address", "type": "string", "is_config": false}, {"name": "second-dns-address", "type": "string", "is_config": false}, {"name": "terminate-cause", "type": "string", "is_config": false}, {"name": "lcp-sent", "type": "uint32", "is_config": false}, {"name": "lcp-recieved", "type": "uint32", "is_config": false}, {"name": "ipcp-sent", "type": "uint32", "is_config": false}, {"name": "ipcp-recieved", "type": "uint32", "is_config": false}, {"name": "ip6cp-sent", "type": "uint32", "is_config": false}, {"name": "ip6cp-recieved", "type": "uint32", "is_config": false}, {"name": "drop-packets", "type": "uint32", "is_config": false}, {"name": "keepalive-packets", "type": "uint32", "is_config": false}]}]}]}, {"name": "huawei-pppoe-client:pppoe-client-session-summarys", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-sacl:traffic-filter-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-remark-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-redirect-applys", "type": "container", "fields": []}, {"name": "huawei-sacl:traffic-mirror-applys", "type": "container", "fields": []}, {"name": "huawei-storm-control:storm-control", "type": "container", "fields": [{"name": "storm-rates", "type": "container", "fields": []}, {"name": "storm-control-action", "type": "container", "fields": [{"name": "action", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::address::common-address::addresses::address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::state::addresses::address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip", "type": "string", "is_config": false, "nullable": false}, {"name": "mask", "type": "string", "is_config": false}, {"name": "type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ip:ipv4::huawei-arp:static-arps::static-arp", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addr", "type": "string", "nullable": false}, {"name": "mac-addr", "type": "string", "nullable": false}, {"name": "pevid", "type": "uint16"}], "keys": [{"name": "k0", "fields": [":pid", "ip-addr"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::gateway-list", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "gateway-list", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "gateway-list"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::dns-list::ip-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::excluded-ip-addresses::excluded-ip-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ip-address", "type": "string", "nullable": false}, {"name": "end-ip-address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ip-address", "end-ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::static-binds::static-bind", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "static-bind-ip", "type": "string", "nullable": false}, {"name": "static-bind-mac", "type": "string", "nullable": false}, {"name": "description", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "static-bind-ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "option-code", "type": "uint32", "nullable": false}, {"name": "option-format", "type": "choice", "fields": [{"name": "ip-format", "type": "case", "fields": []}, {"name": "ascii-format", "type": "case", "fields": [{"name": "ascii-string", "type": "string"}]}, {"name": "hex-format", "type": "case", "fields": [{"name": "hex-string", "type": "string"}]}, {"name": "sub-options-format", "type": "case", "fields": [{"name": "sub-options", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "option-code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::ip-format::ip-addresses", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addresses", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-addresses"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sub-option-code", "type": "uint32", "nullable": false}, {"name": "option-format", "type": "choice", "fields": [{"name": "sub-ip-format", "type": "case", "fields": []}, {"name": "sub-ascii-format", "type": "case", "fields": [{"name": "ascii-string", "type": "string"}]}, {"name": "sub-hex-format", "type": "case", "fields": [{"name": "hex-string", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "sub-option-code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-dhcp:interface-ip-pool::select-type::interface::select-interface::options::option::option-format::sub-options-format::sub-options::sub-option::option-format::sub-ip-format::ip-addresses", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-addresses", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ip-addresses"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-ethernet:ethernet::main-interface::port-isolate-groups::port-isolate-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "int32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "chassis-id-sub-type", "type": "string", "is_config": false}, {"name": "chassis-id", "type": "string", "is_config": false}, {"name": "port-id-sub-type", "type": "string", "is_config": false}, {"name": "port-id", "type": "string", "is_config": false}, {"name": "port-description", "type": "string", "is_config": false}, {"name": "system-name", "type": "string", "is_config": false}, {"name": "system-description", "type": "string", "is_config": false}, {"name": "system-capabilities-enabled", "type": "string", "is_config": false}, {"name": "system-capabilities-supported", "type": "string", "is_config": false}, {"name": "expired-time", "type": "int32", "is_config": false}, {"name": "port-vlan-id", "type": "int32", "is_config": false}, {"name": "protocol-identity", "type": "string", "is_config": false}, {"name": "auto-negotiation-supported", "type": "string", "is_config": false}, {"name": "auto-negotiation-enabled", "type": "string", "is_config": false}, {"name": "auto-negotiation-capability", "type": "string", "is_config": false}, {"name": "oper-mau-type", "type": "string", "is_config": false}, {"name": "link-aggregation-supported", "type": "string", "is_config": false}, {"name": "link-aggregation-enabled", "type": "string", "is_config": false}, {"name": "aggregation-port-id", "type": "int32", "is_config": false}, {"name": "maximum-frame-size", "type": "int32", "is_config": false}, {"name": "discovered-time", "type": "string", "is_config": false}, {"name": "management-addresss", "type": "container", "is_config": false, "fields": []}, {"name": "protocol-vlans", "type": "container", "is_config": false, "fields": []}, {"name": "vlan-names", "type": "container", "is_config": false, "fields": []}, {"name": "unknown-tlvs", "type": "container", "is_config": false, "fields": []}, {"name": "unknown-organizationally-defined-tlvs", "type": "container", "is_config": false, "fields": []}, {"name": "power", "type": "container", "is_config": false, "fields": [{"name": "port-class", "type": "string", "is_config": false}, {"name": "pse-support", "type": "string", "is_config": false}, {"name": "pse-state", "type": "string", "is_config": false}, {"name": "pse-pairs-control-ability", "type": "string", "is_config": false}, {"name": "pse-pairs", "type": "string", "is_config": false}, {"name": "classification", "type": "string", "is_config": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "pd-source", "type": "string", "is_config": false}, {"name": "pse-source", "type": "string", "is_config": false}, {"name": "priority", "type": "string", "is_config": false}, {"name": "pd-requested-value", "type": "string", "is_config": false}, {"name": "pse-allocated-value", "type": "string", "is_config": false}, {"name": "pd-requested-mode-a-value", "type": "string", "is_config": false}, {"name": "pd-requested-mode-b-value", "type": "string", "is_config": false}, {"name": "pse-allocated-mode-a-value", "type": "string", "is_config": false}, {"name": "pse-allocated-mode-b-value", "type": "string", "is_config": false}, {"name": "pse-powering-status", "type": "string", "is_config": false}, {"name": "pd-powered-status", "type": "string", "is_config": false}, {"name": "pairsx", "type": "string", "is_config": false}, {"name": "class", "type": "string", "is_config": false}, {"name": "class-ext-mode-a", "type": "string", "is_config": false}, {"name": "class-ext-mode-b", "type": "string", "is_config": false}, {"name": "class-ext", "type": "string", "is_config": false}, {"name": "typex", "type": "string", "is_config": false}, {"name": "type-ext", "type": "string", "is_config": false}, {"name": "pd-4pid", "type": "string", "is_config": false}, {"name": "pd-load", "type": "string", "is_config": false}, {"name": "pse-max-available", "type": "string", "is_config": false}, {"name": "pse-autoclass-support", "type": "string", "is_config": false}, {"name": "autoclass-completed", "type": "string", "is_config": false}, {"name": "autoclass-request", "type": "string", "is_config": false}, {"name": "power-down", "type": "boolean", "is_config": false}, {"name": "power-down-time", "type": "uint32", "is_config": false}]}, {"name": "identity-tlv", "type": "container", "is_config": false, "fields": [{"name": "identity", "type": "string", "is_config": false}]}, {"name": "med-tlv", "type": "container", "is_config": false, "fields": [{"name": "capability", "type": "container", "is_config": false, "fields": [{"name": "device-type", "type": "string", "is_config": false}]}, {"name": "network-policys", "type": "container", "is_config": false, "fields": []}, {"name": "extended-power", "type": "container", "is_config": false, "fields": [{"name": "type", "type": "string", "is_config": false}, {"name": "pse-source", "type": "string", "is_config": false}, {"name": "pd-source", "type": "string", "is_config": false}, {"name": "priority", "type": "string", "is_config": false}, {"name": "value", "type": "string", "is_config": false}]}]}, {"name": "legacy-power-capability", "type": "container", "is_config": false, "fields": [{"name": "mode", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::management-addresss::management-address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false, "nullable": false}, {"name": "length", "type": "int32", "is_config": false, "nullable": false}, {"name": "if-sub-type", "type": "string", "is_config": false}, {"name": "if-id", "type": "int32", "is_config": false}, {"name": "oid", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "value", "length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::protocol-vlans::protocol-vlan", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "int32", "is_config": false, "nullable": false}, {"name": "supported", "type": "string", "is_config": false}, {"name": "enabled", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::vlan-names::vlan-name", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "int32", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-tlvs::unknown-tlv", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "int32", "is_config": false, "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::unknown-organizationally-defined-tlvs::unknown-organizationally-defined-tlv", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "oui", "type": "string", "is_config": false, "nullable": false}, {"name": "sub-type", "type": "int32", "is_config": false, "nullable": false}, {"name": "index", "type": "int32", "is_config": false, "nullable": false}, {"name": "info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "oui", "sub-type", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::capability::capabilities", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "capabilities", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "capabilities"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::med-tlv::network-policys::network-policy", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "unknown-policy", "type": "string", "is_config": false}, {"name": "vlan-tagged", "type": "boolean", "is_config": false}, {"name": "vlan-id", "type": "uint16", "is_config": false}, {"name": "cos", "type": "uint8", "is_config": false}, {"name": "dscp", "type": "uint8", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-lldp:lldp::session::neighbors::neighbor::legacy-power-capability::capability", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "capability", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "capability"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-mirror:mirror::observe-ports::observe-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "observe-index", "type": "uint8", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "observe-index", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-pppoe-client:pppoe-client-session-summarys::pppoe-client-session-summary", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "session-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "client-mac", "type": "string", "is_config": false}, {"name": "server-mac", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "session-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false}, {"name": "enable-statistic", "type": "boolean", "default": false}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-filter-applys::traffic-filter-apply::acl-instances::acl-instance::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}, {"name": "match-permit-packet", "type": "uint64", "is_config": false}, {"name": "match-permit-byte", "type": "uint64", "is_config": false}, {"name": "match-discarded-packet", "type": "uint64", "is_config": false}, {"name": "match-discarded-byte", "type": "uint64", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false}, {"name": "dot1p-value", "type": "uint8"}, {"name": "dscp-value", "type": "uint8"}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-remark-applys::traffic-remark-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl", "type": "string", "nullable": false}, {"name": "direction", "type": "choice", "fields": [{"name": "nexthop", "type": "case", "default": true, "fields": [{"name": "ip-nexthop", "type": "string"}]}]}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-redirect-applys::traffic-redirect-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "direction", "type": "string", "nullable": false}, {"name": "acl-instances", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "direction"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "observe-index", "type": "uint8", "nullable": false}, {"name": "acl", "type": "string", "nullable": false}, {"name": "statuses", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "observe-index", "acl"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-sacl:traffic-mirror-applys::traffic-mirror-apply::acl-instances::acl-instance::statuses::status", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rule", "type": "string", "is_config": false, "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "apply-status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "rule", "slot"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ifm:ifm::interfaces::interface::huawei-storm-control:storm-control::storm-rates::storm-rate", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "packet-type", "type": "string", "nullable": false}, {"name": "rate-type", "type": "choice", "fields": [{"name": "packet", "type": "case", "fields": [{"name": "pps-min-rate", "type": "uint32"}, {"name": "pps-max-rate", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global", "type": "container", "fields": [{"name": "strict-learn-enable", "type": "boolean", "default": false}, {"name": "expire-time", "type": "uint32", "default": 1200}, {"name": "gateway-dup-enable", "type": "boolean", "default": false}]}, {"name": "query-entries", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::query-entries::query-entry", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ni-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-addr", "type": "string", "is_config": false}, {"name": "style-type", "type": "string", "is_config": false}, {"name": "if-name", "type": "string", "is_config": false}, {"name": "pe-vlan", "type": "uint16", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "ni-name", "ip-addr"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-arp:arp::statistics::statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "packets-received", "type": "uint32", "is_config": false}, {"name": "miss-received", "type": "uint32", "is_config": false}, {"name": "learn-count", "type": "uint32", "is_config": false}, {"name": "packets-drop-limit", "type": "uint32", "is_config": false}, {"name": "packets-drop-other", "type": "uint32", "is_config": false}, {"name": "miss-drop-limit", "type": "uint32", "is_config": false}, {"name": "miss-drop-other", "type": "uint32", "is_config": false}, {"name": "packets-drop-speedlmt", "type": "uint32", "is_config": false}, {"name": "packets-proxy-supp", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dhcp:dhcp", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "common", "type": "container", "fields": [{"name": "global", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}]}, {"name": "server", "type": "container", "fields": [{"name": "common", "type": "container", "fields": [{"name": "ping-packet-nub", "type": "uint32", "default": 2}, {"name": "ping-packet-timeout", "type": "uint32", "default": 500}, {"name": "bootp-enable", "type": "boolean", "default": true}, {"name": "bootp-auto-enable", "type": "boolean", "default": true}]}, {"name": "packet-statistics", "type": "container", "is_config": false, "fields": [{"name": "client-request-count", "type": "uint32", "is_config": false}, {"name": "discover-count", "type": "uint32", "is_config": false}, {"name": "request-count", "type": "uint32", "is_config": false}, {"name": "decline-count", "type": "uint32", "is_config": false}, {"name": "release-count", "type": "uint32", "is_config": false}, {"name": "inform-count", "type": "uint32", "is_config": false}, {"name": "server-reply-count", "type": "uint32", "is_config": false}, {"name": "offer-count", "type": "uint32", "is_config": false}, {"name": "ack-count", "type": "uint32", "is_config": false}, {"name": "nak-count", "type": "uint32", "is_config": false}, {"name": "force-renew-count", "type": "uint32", "is_config": false}, {"name": "bad-message-count", "type": "uint32", "is_config": false}, {"name": "bootp-request-count", "type": "uint32", "is_config": false}, {"name": "bootp-reply-count", "type": "uint32", "is_config": false}]}, {"name": "ip-pool-querys", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dhcp:dhcp::server::ip-pool-querys::ip-pool-query", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "pool-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false, "nullable": false}, {"name": "mac-address", "type": "string", "is_config": false}, {"name": "lease-time", "type": "uint32", "is_config": false}, {"name": "status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "pool-name", "ip-address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp", "type": "container", "presence": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global-attribute", "type": "container", "fields": [{"name": "message-transmission-interval", "type": "int32", "default": 30}, {"name": "message-transmission-delay", "type": "int32", "default": 2}, {"name": "message-transmission-hold-multiplier", "type": "int32", "default": 4}, {"name": "restart-delay", "type": "int32", "default": 2}]}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "chassis-id-sub-type", "type": "string", "is_config": false}, {"name": "chassis-id", "type": "string", "is_config": false}, {"name": "system-name", "type": "string", "is_config": false}, {"name": "system-description", "type": "string", "is_config": false}, {"name": "up-time", "type": "string", "is_config": false}, {"name": "management-addresss", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-supported", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "system-capabilities-supported", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "system-capabilities-supported"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::system-capabilities-enabled", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "system-capabilities-enabled", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "system-capabilities-enabled"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-lldp:lldp::local-info::management-addresss::management-address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "value", "type": "string", "is_config": false, "nullable": false}, {"name": "length", "type": "int32", "is_config": false, "nullable": false}, {"name": "if-sub-type", "type": "string", "is_config": false}, {"name": "if-id", "type": "int32", "is_config": false}, {"name": "oid", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "value", "length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mirror:mirror", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global-observers", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mirror:mirror::global-observers::global-observer", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "observe-index", "type": "uint8", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "observe-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sacl:sacl", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-storm-control:storm-control", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "storm-rates", "type": "container", "fields": []}, {"name": "storm-control-action", "type": "container", "fields": [{"name": "action", "type": "string"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-storm-control:storm-control::storm-rates::storm-rate", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "packet-type", "type": "string", "nullable": false}, {"name": "rate-type", "type": "choice", "fields": [{"name": "packet", "type": "case", "fields": [{"name": "pps-min-rate", "type": "uint32"}, {"name": "pps-max-rate", "type": "uint32"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vlans", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlan", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "type", "type": "string", "default": "common"}, {"name": "member-ports", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-arp:arp-security", "type": "container", "fields": [{"name": "l2proxy-enable", "type": "boolean", "default": false}]}, {"name": "huawei-mac:mac-addresss", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlan::member-ports::member-port", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "access-type", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "tag-mode", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-vlan:vlan::vlans::vlan::huawei-mac:mac-addresss::mac-address", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "nullable": false}, {"name": "mac-type", "type": "choice", "fields": [{"name": "black-hole", "type": "case", "fields": [{"name": "black-hole", "type": "string", "nullable": false}]}, {"name": "static", "type": "case", "fields": [{"name": "out-interface-name", "type": "string", "nullable": false}]}]}], "keys": [{"name": "k0", "fields": [":pid", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global-attribute", "type": "container", "fields": [{"name": "aging-time", "type": "uint32", "default": 300}]}, {"name": "global-mac-usage", "type": "container", "fields": [{"name": "mac-threshold", "type": "uint32", "default": 90}]}, {"name": "vlan-dynamic-macs", "type": "container", "is_config": false, "fields": []}, {"name": "mac-statistics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::vlan-dynamic-macs::vlan-dynamic-mac", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vlan-id", "type": "uint16", "is_config": false, "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false}, {"name": "out-interface-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "vlan-id", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-mac:mac::mac-statistics::mac-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "total", "type": "uint32", "is_config": false}, {"name": "black-hole", "type": "uint32", "is_config": false}, {"name": "static", "type": "uint32", "is_config": false}, {"name": "dynamic", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aspf:aspf", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-aspf:aspf::protocol", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg:cfg", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "autosave", "type": "container", "presence": true, "fields": [{"name": "interval-time", "type": "uint32", "default": 30}, {"name": "delay-time", "type": "uint32", "default": 5}]}, {"name": "startup-infos", "type": "container", "is_config": false, "fields": []}, {"name": "cfg-files", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg:cfg::startup-infos::startup-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "current-cfg-file", "type": "string", "is_config": false}, {"name": "next-cfg-file", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cfg:cfg::cfg-files::cfg-file", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "filename", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "filename"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cli:cli", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "header", "type": "container", "fields": [{"name": "login-information", "type": "choice", "fields": [{"name": "head-login-text", "type": "case", "fields": [{"name": "login-text", "type": "string"}]}]}]}, {"name": "query-diagnose-information-generate-process", "type": "container", "is_config": false, "fields": [{"name": "processsing-status", "type": "string", "is_config": false}, {"name": "fail-reason", "type": "string", "is_config": false}, {"name": "last-generate-time", "type": "string", "is_config": false}]}, {"name": "huawei-cli-lite:terminal", "type": "container", "fields": [{"name": "history-cmd-size", "type": "uint16", "default": 10}, {"name": "idle-timeout", "type": "uint32", "default": 300}, {"name": "split-screen", "type": "boolean", "default": true}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign:codesign", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "software-crls", "type": "container", "is_config": false, "fields": []}, {"name": "crl-names", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign:codesign::software-crls::software-crl", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "publisher", "type": "string", "is_config": false, "nullable": false}, {"name": "date", "type": "string", "is_config": false}, {"name": "valid-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot", "publisher"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-codesign:codesign::crl-names::crl-name", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "board-cpu-infos", "type": "container", "is_config": false, "fields": []}, {"name": "board-memory-infos", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-cpu-infos::board-cpu-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "overload-threshold", "type": "uint32", "is_config": false, "default": 90}, {"name": "system-cpu-usage", "type": "uint32", "is_config": false, "default": 0}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-cpu-memory:cpu-memory::board-memory-infos::board-memory-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "cpu-id", "type": "string", "is_config": false, "nullable": false}, {"name": "overload-threshold", "type": "uint32", "is_config": false}, {"name": "os-memory-total", "type": "uint32", "is_config": false}, {"name": "os-memory-use", "type": "uint32", "is_config": false}, {"name": "os-memory-free", "type": "uint32", "is_config": false}, {"name": "os-memory-usage", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id", "cpu-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe:devm-poe", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "poes", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe:devm-poe::poes::poe", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "nullable": false}, {"name": "threshold-power", "type": "uint32", "default": 90}, {"name": "supply-power", "type": "uint32", "is_config": false}, {"name": "available-power", "type": "uint32", "is_config": false}, {"name": "consumption-power", "type": "uint32", "is_config": false}, {"name": "peak-power", "type": "uint32", "is_config": false}, {"name": "support-flag", "type": "boolean", "is_config": false}, {"name": "ports", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm-poe:devm-poe::poes::poe::ports::port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string", "nullable": false}, {"name": "power-off-time-range", "type": "string"}, {"name": "power-enable", "type": "boolean", "default": true}, {"name": "power-status", "type": "string", "is_config": false}, {"name": "port-power", "type": "uint32", "is_config": false}, {"name": "port-peak-power", "type": "uint32", "is_config": false}, {"name": "port-average-power", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "interface-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "physical-entitys", "type": "container", "is_config": false, "fields": []}, {"name": "mpu-boards", "type": "container", "is_config": false, "fields": []}, {"name": "ports", "type": "container", "fields": []}, {"name": "schedule-reboot", "type": "container", "is_config": false, "fields": [{"name": "datetime", "type": "string", "is_config": false}, {"name": "delay-time", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::physical-entitys::physical-entity", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "class", "type": "string", "is_config": false, "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false}, {"name": "entity-description", "type": "string", "is_config": false}, {"name": "alias", "type": "string", "is_config": false}, {"name": "vendor-type", "type": "string", "is_config": false}, {"name": "hardware-version", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "module-name", "type": "string", "is_config": false}, {"name": "esn", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "class", "position", "serial-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::mpu-boards::mpu-board", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "index", "type": "uint32", "is_config": false}, {"name": "board-type", "type": "string", "is_config": false}, {"name": "up-time", "type": "uint32", "is_config": false}, {"name": "sdram-size", "type": "uint32", "is_config": false}, {"name": "flash-size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-devm:devm::ports::port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "nullable": false}, {"name": "loopback-mode", "type": "string", "default": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "huawei-pic:ethernet", "type": "container", "fields": [{"name": "speed", "type": "string"}, {"name": "duplex", "type": "string"}, {"name": "negotiation", "type": "string", "default": "enabled"}]}, {"name": "huawei-pic:optical-module", "type": "container", "fields": [{"name": "rx-high-power-warn-en", "type": "boolean", "default": true}, {"name": "rx-low-power-warn-en", "type": "boolean", "default": true}, {"name": "tx-high-power-warn-en", "type": "boolean", "default": true}, {"name": "tx-low-power-warn-en", "type": "boolean", "default": true}, {"name": "vendor-name", "type": "string", "is_config": false}, {"name": "vendor-pn", "type": "string", "is_config": false}, {"name": "tx-power", "type": "string", "is_config": false}, {"name": "rx-power", "type": "string", "is_config": false}, {"name": "rx-high-warn-power", "type": "string", "is_config": false}, {"name": "rx-low-warn-power", "type": "string", "is_config": false}, {"name": "tx-high-warn-power", "type": "string", "is_config": false}, {"name": "tx-low-warn-power", "type": "string", "is_config": false}, {"name": "bias", "type": "int32", "is_config": false}, {"name": "temperature", "type": "int32", "is_config": false}, {"name": "voltage-float", "type": "string", "is_config": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "certified-state", "type": "string", "is_config": false}, {"name": "bias-high-threshold", "type": "string", "is_config": false}, {"name": "bias-low-threshold", "type": "string", "is_config": false}, {"name": "voltage-high-threshold-float", "type": "string", "is_config": false}, {"name": "voltage-low-threshold-float", "type": "string", "is_config": false}, {"name": "temperature-high-threshold-float", "type": "string", "is_config": false}, {"name": "temperature-low-threshold-float", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "terminal", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-acl:acl", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-arp:arp", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-device-info:driver-dfx-infos", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-device-info:device-management", "type": "container", "is_config": false, "fields": [{"name": "harddisk-info", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-dhcp:dhcp", "type": "container", "is_config": false, "fields": [{"name": "am", "type": "container", "is_config": false, "fields": []}, {"name": "dhcp-stack", "type": "container", "is_config": false, "fields": []}, {"name": "dhcp-client", "type": "container", "is_config": false, "fields": []}, {"name": "dhcp-server", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-dns:dns4c", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-hpe:hpe", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-hpf:hpf", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-hps:hps", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-hsec:hsec", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-icmp:ping", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-icmp:traceroute", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-ifm:ifm", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-iotbus:iotbus", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-ip:ipv4", "type": "container", "is_config": false, "fields": [{"name": "addresses", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-lldp:lldp", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-lsw-chip:lsw-chip", "type": "container", "is_config": false, "fields": [{"name": "mac-learnings", "type": "container", "is_config": false, "fields": []}, {"name": "vlan-infos", "type": "container", "is_config": false, "fields": []}, {"name": "car-infos", "type": "container", "is_config": false, "fields": []}, {"name": "acl-ranges", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-nat:nat", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-nctl:nctl", "type": "container", "is_config": false, "fields": [{"name": "innertables", "type": "container", "is_config": false, "fields": []}]}, {"name": "huawei-diagnose-ndb:ndb", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-omu:omu", "type": "container", "is_config": false, "fields": [{"name": "yangdb", "type": "container", "is_config": false, "fields": []}, {"name": "notif", "type": "container", "is_config": false, "fields": [{"name": "sensor-paths", "type": "container", "is_config": false, "fields": []}, {"name": "subscriptions", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-diagnose-poe:poe", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-ppp:ppp", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-pppoe-client:pppoe-client", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-routing-lite:routing", "type": "container", "is_config": false, "fields": [{"name": "routing-manage", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}]}, {"name": "direct-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}]}, {"name": "static-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}]}, {"name": "user-network-routing", "type": "container", "is_config": false, "fields": [{"name": "routes", "type": "container", "is_config": false, "fields": []}]}]}, {"name": "huawei-diagnose-sacl:sacl", "type": "container", "is_config": false, "fields": []}, {"name": "huawei-diagnose-system:sysdiag", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-ip:ipv4::addresses::address", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "type", "type": "uint8", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "if-index", "if-name", "ip", "mask-length"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::mac-learnings::mac-learning", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}, {"name": "enable-status", "type": "boolean", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::vlan-infos::vlan-info::vlan-id", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vlan-id", "type": "uint32", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vlan-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::car-infos::car-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "unit", "type": "uint32", "is_config": false, "nullable": false}, {"name": "car-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "cbs", "type": "uint32", "is_config": false}, {"name": "cir", "type": "uint32", "is_config": false}, {"name": "pbs", "type": "uint32", "is_config": false}, {"name": "pir", "type": "uint32", "is_config": false}, {"name": "car-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "unit", "car-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "unit", "type": "uint32", "is_config": false, "nullable": false}, {"name": "begin-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "end-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "acl-infos", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "unit", "begin-index", "end-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-lsw-chip:lsw-chip::acl-ranges::acl-range::acl-infos::acl-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "acl-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "key-info", "type": "string", "is_config": false}, {"name": "action-info", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "acl-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-nctl:nctl::innertables::innertable", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "table-type", "type": "string", "is_config": false, "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false, "nullable": false}, {"name": "last-time", "type": "uint32", "is_config": false}, {"name": "total-cnt", "type": "uint32", "is_config": false}, {"name": "fail-cnt", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "table-type", "operation-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::sensor-paths::sensor-path", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-path", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "sensor-path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "trigger-send-count", "type": "uint32", "is_config": false}, {"name": "trigger-send-error", "type": "uint32", "is_config": false}, {"name": "sensor-groups", "type": "container", "is_config": false, "fields": []}, {"name": "destination-groups", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "is_config": false, "nullable": false}, {"name": "collect-count", "type": "uint32", "is_config": false}, {"name": "receive-count", "type": "uint32", "is_config": false}, {"name": "receive-error-count", "type": "uint32", "is_config": false}, {"name": "sensor-paths", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "is_config": false, "nullable": false}, {"name": "indicators", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::sensor-groups::sensor-group::sensor-paths::sensor-path::indicators::indicator", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "is_config": false, "nullable": false}, {"name": "collect-count", "type": "uint32", "is_config": false}, {"name": "receive-instance-count", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "is_config": false, "nullable": false}, {"name": "destinations", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-omu:omu::notif::subscriptions::subscription::destination-groups::destination-group::destinations::destination", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "port", "type": "uint16", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "send-count", "type": "uint32", "is_config": false}, {"name": "drop-count", "type": "uint32", "is_config": false}, {"name": "last-send-time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "address", "port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::routing-manage::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::direct-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::static-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnose:diagnose::huawei-diagnose-routing-lite:routing::user-network-routing::routes::route", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "prefix", "type": "string", "is_config": false, "nullable": false}, {"name": "mask-length", "type": "uint8", "is_config": false, "nullable": false}, {"name": "nexthop-interface-name", "type": "string", "is_config": false, "nullable": false}, {"name": "nexthop-address", "type": "string", "is_config": false, "nullable": false}, {"name": "protocol-type", "type": "uint8", "is_config": false, "nullable": false}, {"name": "preference", "type": "uint32", "is_config": false}, {"name": "cost", "type": "uint32", "is_config": false}, {"name": "relay-nexthop", "type": "string", "is_config": false}, {"name": "direct-nexthop-address", "type": "string", "is_config": false}, {"name": "direct-nexthop-interface-name", "type": "string", "is_config": false}, {"name": "flag", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "indirect-id", "type": "string", "is_config": false}, {"name": "input-time", "type": "uint32", "is_config": false}, {"name": "output-time", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "prefix", "mask-length", "nexthop-interface-name", "nexthop-address", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "container", "is_config": false, "fields": [{"name": "ping-results", "type": "container", "is_config": false, "fields": []}, {"name": "trace-results", "type": "container", "is_config": false, "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-recv", "type": "uint32", "is_config": false}, {"name": "packet-send", "type": "uint32", "is_config": false}, {"name": "loss-ratio", "type": "uint8", "is_config": false}, {"name": "rtt-min", "type": "uint32", "is_config": false}, {"name": "rtt-max", "type": "uint32", "is_config": false}, {"name": "average-rtt", "type": "uint32", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "error-type", "type": "string", "is_config": false}, {"name": "details", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "test-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::ping-results::ping-result::details::detail", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "packet-size", "type": "uint32", "is_config": false}, {"name": "ttl", "type": "uint8", "is_config": false}, {"name": "rtt", "type": "uint32", "is_config": false}, {"name": "result-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "test-name", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "error-type", "type": "string", "is_config": false}, {"name": "details", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "test-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-diagnostic-tools:diagnostic-tools::ipv4::trace-results::trace-result::details::detail", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "hop-index", "type": "uint32", "is_config": false, "nullable": false}, {"name": "ttl", "type": "uint8", "is_config": false}, {"name": "rtt", "type": "uint32", "is_config": false}, {"name": "ds-ip-addr", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "hop-index"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domains", "type": "container", "fields": []}, {"name": "ipv4-hosts", "type": "container", "fields": []}, {"name": "ipv4-servers", "type": "container", "fields": []}, {"name": "query-host-ips", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::domains::domain", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false}, {"name": "name", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::ipv4-hosts::ipv4-host", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false}, {"name": "host", "type": "string", "nullable": false}, {"name": "address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "host"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::ipv4-servers::ipv4-server", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "vpn", "type": "string", "nullable": false}, {"name": "address", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "vpn", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-dns:dns::query-host-ips::query-host-ip", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "host-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ip-address", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "host-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "<PERSON><PERSON><PERSON>-driver:driver", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global-attribute", "type": "container", "fields": [{"name": "system-mac-address", "type": "string", "is_config": false}, {"name": "system-mac-number", "type": "uint32", "is_config": false}, {"name": "factory-configuration-button-switch", "type": "string", "default": "enable"}, {"name": "huawei-pic:global", "type": "container", "fields": [{"name": "non-certified-optical-status-alarm", "type": "boolean", "default": true}]}]}, {"name": "temperature2s", "type": "container", "is_config": false, "fields": []}, {"name": "device-health-checks", "type": "container", "is_config": false, "fields": []}, {"name": "electronic-labels-diagnose", "type": "container", "is_config": false, "fields": []}, {"name": "electronic-labels", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::temperature2s::temperature2", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "sensor-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "sensor-name", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "current-temperature", "type": "int32", "is_config": false}, {"name": "minor-threshold", "type": "int32", "is_config": false}, {"name": "major-threshold", "type": "int32", "is_config": false}, {"name": "fatal-threshold", "type": "int32", "is_config": false}, {"name": "low-threshold", "type": "int32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position", "sensor-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::device-health-checks::device-health-check", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "item", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "item"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-driver:driver::electronic-labels::electronic-label", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "entity-class", "type": "string", "is_config": false, "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "entity-serial-number", "type": "uint32", "is_config": false, "nullable": false}, {"name": "entity-bomid", "type": "string", "is_config": false}, {"name": "barcode", "type": "string", "is_config": false}, {"name": "bom-english-desc", "type": "string", "is_config": false}, {"name": "manufacturer-name", "type": "string", "is_config": false}, {"name": "manufacturer-date", "type": "string", "is_config": false}, {"name": "manufacturer-code", "type": "string", "is_config": false}, {"name": "board-type", "type": "string", "is_config": false}, {"name": "entity-clei-code", "type": "string", "is_config": false}, {"name": "entity-open-bomid", "type": "string", "is_config": false}, {"name": "entity-issue-number", "type": "string", "is_config": false}, {"name": "entity-model", "type": "string", "is_config": false}, {"name": "entity-elabel-version", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "entity-class", "position", "entity-serial-number"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-file-operation:file-operation", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "dirs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-file-operation:file-operation::dirs::dir", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "dir-name", "type": "string", "is_config": false, "nullable": false}, {"name": "attribute", "type": "string", "is_config": false}, {"name": "modify-time", "type": "string", "is_config": false}, {"name": "size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "file-name", "dir-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alarms", "type": "container", "is_config": false, "fields": []}, {"name": "active-alarms", "type": "container", "is_config": false, "fields": []}, {"name": "history-alarms", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::alarms::alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false, "nullable": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "clear-suppress-time", "type": "uint32", "is_config": false}, {"name": "cause-suppress-time", "type": "uint32", "is_config": false}, {"name": "module-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "alarm-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::active-alarms::active-alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false}, {"name": "alarm-id", "type": "uint32", "is_config": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "generated-time", "type": "string", "is_config": false}, {"name": "description", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-fm:fm::history-alarms::history-alarm", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "alarm-sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "alarm-name", "type": "string", "is_config": false}, {"name": "alarm-id", "type": "uint32", "is_config": false}, {"name": "level", "type": "string", "is_config": false}, {"name": "generated-time", "type": "string", "is_config": false}, {"name": "cleared-time", "type": "string", "is_config": false}, {"name": "description", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "alarm-sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ftpc:ftpc", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "client", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "default": false}]}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ftpc:ftpc::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "command-type", "type": "string", "is_config": false}, {"name": "server-address", "type": "string", "is_config": false}, {"name": "server-port", "type": "uint16", "is_config": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-gnss:gnss", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "gnss-location", "type": "container", "is_config": false, "fields": [{"name": "longitude", "type": "string", "is_config": false}, {"name": "latitude", "type": "string", "is_config": false}, {"name": "altitude", "type": "string", "is_config": false}]}, {"name": "gnss-satellite-infos", "type": "container", "is_config": false, "fields": []}, {"name": "gnss-info", "type": "container", "fields": [{"name": "gnss-clocksync", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}, {"name": "gnss-positions", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-gnss:gnss::gnss-satellite-infos::gnss-satellite-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "gnss-type", "type": "string", "is_config": false, "nullable": false}, {"name": "satellite-id", "type": "string", "is_config": false, "nullable": false}, {"name": "carrier-noise-ratio", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "gnss-type", "satellite-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-gnss:gnss::gnss-info::gnss-positions::gnss-position", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "satellite", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "satellite"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "anti-attacks", "type": "container", "fields": []}, {"name": "packet-statistics", "type": "container", "is_config": false, "fields": []}, {"name": "adjust-car", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": false}]}, {"name": "policys", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::anti-attacks::anti-attack", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "anti-attack-type", "type": "string", "nullable": false}, {"name": "enable", "type": "boolean"}, {"name": "para", "type": "uint32"}], "keys": [{"name": "k0", "fields": [":pid", "anti-attack-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::packet-statistics::packet-statistic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "packet-type", "type": "string", "is_config": false, "nullable": false}, {"name": "pass-packets", "type": "uint64", "is_config": false}, {"name": "drop-packets", "type": "uint64", "is_config": false}, {"name": "pass-bytes", "type": "uint64", "is_config": false}, {"name": "drop-bytes", "type": "uint64", "is_config": false}, {"name": "protocol-status", "type": "container", "is_config": false, "fields": [{"name": "status", "type": "boolean", "is_config": false}, {"name": "car-value", "type": "uint32", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "slot", "packet-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::adjust-car::adjust-protocol-type", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "adjust-protocol-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "adjust-protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "filters", "type": "container", "fields": []}, {"name": "cpcars", "type": "container", "fields": []}, {"name": "auto-defend", "type": "container", "fields": [{"name": "enable", "type": "boolean", "default": true}, {"name": "alarm-enable", "type": "boolean", "default": true}, {"name": "alarm-threshold", "type": "uint32", "default": 128}, {"name": "penalty-enable", "type": "boolean", "default": false}, {"name": "penalty-threshold", "type": "uint32", "default": 128}]}, {"name": "applied-policys", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::filters::filter", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "filter-id", "type": "uint32", "nullable": false}, {"name": "acl-type", "type": "choice", "fields": [{"name": "ipv4", "type": "case", "fields": [{"name": "ipv4-acl", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "filter-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::cpcars::cpcar", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-type", "type": "string", "nullable": false}, {"name": "cir", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::auto-defend::defend-type", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "defend-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "defend-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-host-security:host-security::policys::policy::applied-policys::applied-policy", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "applied-type", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "applied-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ssl:ssl", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ssl-policys", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ssl:ssl::ssl-policys::ssl-policy", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "policy-name", "type": "string", "nullable": false}, {"name": "pki-realm", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "policy-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-http:http", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-http:http::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "operation-type", "type": "string", "is_config": false}, {"name": "file-url", "type": "string", "is_config": false}, {"name": "file-full-path", "type": "string", "is_config": false}, {"name": "transfer-status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-code", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "iotbus-hub", "type": "container", "fields": [{"name": "address", "type": "string"}, {"name": "port", "type": "uint16"}, {"name": "enabled", "type": "boolean", "default": false}, {"name": "link-monitor", "type": "container", "fields": [{"name": "timeout", "type": "int8", "default": 0}, {"name": "connected-check", "type": "boolean", "default": true}]}, {"name": "connection-state", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "offline-reason", "type": "string", "is_config": false}]}]}, {"name": "end-devices", "type": "container", "is_config": false, "fields": []}, {"name": "iotbus-broker", "type": "container", "fields": [{"name": "apps", "type": "container", "is_config": false, "fields": []}, {"name": "discovery-requests", "type": "container", "is_config": false, "fields": []}, {"name": "reg-fail-records", "type": "container", "is_config": false, "fields": []}, {"name": "apply-certificate-fail-records", "type": "container", "is_config": false, "fields": []}, {"name": "mqtt-clients", "type": "container", "is_config": false, "fields": []}, {"name": "statistics", "type": "container", "is_config": false, "fields": [{"name": "dev-register-num", "type": "uint64", "is_config": false}, {"name": "dev-update-num", "type": "uint64", "is_config": false}, {"name": "dev-delete-num", "type": "uint64", "is_config": false}, {"name": "cmd-recv-num", "type": "uint64", "is_config": false}, {"name": "send-success-num", "type": "uint64", "is_config": false}, {"name": "send-failed-num", "type": "uint64", "is_config": false}, {"name": "send-timeout-num", "type": "uint64", "is_config": false}]}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::end-devices::end-device", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "node-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vender-name", "type": "string", "is_config": false, "nullable": false}, {"name": "model", "type": "string", "is_config": false, "nullable": false}, {"name": "device-id", "type": "string", "is_config": false}, {"name": "protocol", "type": "string", "is_config": false}, {"name": "broker-state", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}]}, {"name": "hub-register-state", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}]}, {"name": "hub-online-state", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}]}, {"name": "statistics", "type": "container", "is_config": false, "fields": [{"name": "send-success-num", "type": "uint32", "is_config": false}, {"name": "send-failed-num", "type": "uint32", "is_config": false}, {"name": "send-timeout-num", "type": "uint32", "is_config": false}, {"name": "send-total-num", "type": "uint64", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "node-id", "vender-name", "model"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apps::app", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "app-name", "type": "string", "is_config": false, "nullable": false}, {"name": "app-services", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "app-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apps::app::app-services::app-service", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "service-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "service-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::discovery-requests::discovery-request", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "address", "type": "string", "is_config": false, "nullable": false}, {"name": "port", "type": "uint16", "is_config": false}, {"name": "service-name", "type": "string", "is_config": false}, {"name": "time", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "address"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::reg-fail-records::reg-fail-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "node-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vender-name", "type": "string", "is_config": false, "nullable": false}, {"name": "model", "type": "string", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "node-id", "vender-name", "model"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::apply-certificate-fail-records::apply-certificate-fail-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "node-id", "type": "string", "is_config": false, "nullable": false}, {"name": "vender-name", "type": "string", "is_config": false, "nullable": false}, {"name": "model", "type": "string", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "node-id", "vender-name", "model"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "sub-topics", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-iotbus:iotbus::iotbus-broker::mqtt-clients::mqtt-client::sub-topics::sub-topic", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "topic-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "topic-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-loadbalance:loadbalance", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-load-balance", "type": "container", "fields": [{"name": "mode", "type": "string", "default": "flow"}, {"name": "src-ip", "type": "boolean", "default": true}, {"name": "dst-ip", "type": "boolean", "default": true}, {"name": "src-port", "type": "boolean", "default": false}, {"name": "dst-port", "type": "boolean", "default": false}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-masterkey:masterkey", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "current-type", "type": "container", "is_config": false, "fields": [{"name": "type", "type": "string", "is_config": false}]}, {"name": "modify-result", "type": "container", "is_config": false, "fields": [{"name": "modify-start-time", "type": "string", "is_config": false}, {"name": "modify-end-time", "type": "string", "is_config": false}, {"name": "modify-result", "type": "string", "is_config": false}, {"name": "modify-error-reason", "type": "string", "is_config": false}]}, {"name": "auto-update", "type": "container", "fields": [{"name": "interval", "type": "uint32"}]}, {"name": "current-master<PERSON>", "type": "container", "is_config": false, "fields": [{"name": "domain-id", "type": "uint32", "is_config": false}, {"name": "key-id", "type": "uint32", "is_config": false}, {"name": "create-time", "type": "string", "is_config": false}, {"name": "expired-time", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-module-management:module-management", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "module-infos", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-module-management:module-management::module-infos::module-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "package-name", "type": "string", "is_config": false, "nullable": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "runtime", "type": "string", "is_config": false}, {"name": "path", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "package-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "snat-address-groups", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "mode", "type": "string", "default": "pat"}, {"name": "sections", "type": "container", "fields": []}, {"name": "exclude-ips", "type": "container", "fields": []}, {"name": "exclude-ports", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::sections::section", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint16", "nullable": false}, {"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ips::exclude-ip", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip-or-mask", "type": "choice", "fields": [{"name": "end-ip", "type": "case", "fields": [{"name": "end-ip", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "start-ip"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-address-group:nat-address-group::snat-address-groups::snat-address-group::exclude-ports::exclude-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-port", "type": "uint16", "nullable": false}, {"name": "end-port", "type": "uint16"}], "keys": [{"name": "k0", "fields": [":pid", "start-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "rules", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "action", "type": "container", "fields": [{"name": "action", "type": "choice", "fields": [{"name": "no-nat", "type": "case", "fields": [{"name": "no-nat", "type": "string"}]}, {"name": "do-nat", "type": "case", "fields": [{"name": "source-nat", "type": "container", "fields": [{"name": "mode", "type": "choice", "fields": [{"name": "address-group", "type": "case", "fields": [{"name": "address-group-name", "type": "string"}]}, {"name": "easy-ip", "type": "case", "fields": [{"name": "easy-ip", "type": "string"}]}]}]}]}]}]}, {"name": "egress", "type": "choice", "fields": [{"name": "interfaces", "type": "case", "fields": []}]}, {"name": "source-address", "type": "container", "fields": [{"name": "address-ipv4s", "type": "container", "fields": []}, {"name": "address-ipv4-ranges", "type": "container", "fields": []}, {"name": "address-ipv4-excludes", "type": "container", "fields": []}, {"name": "address-ipv4-range-excludes", "type": "container", "fields": []}]}, {"name": "destination-address", "type": "container", "fields": [{"name": "address-ipv4s", "type": "container", "fields": []}, {"name": "address-ipv4-ranges", "type": "container", "fields": []}, {"name": "address-ipv4-excludes", "type": "container", "fields": []}, {"name": "address-ipv4-range-excludes", "type": "container", "fields": []}]}, {"name": "service", "type": "container", "fields": [{"name": "service-items", "type": "container", "fields": [{"name": "protocol-and-ports", "type": "container", "fields": []}, {"name": "icmpv4s", "type": "container", "fields": []}, {"name": "protocol", "type": "container", "fields": []}]}, {"name": "service-items-exclude", "type": "container", "fields": [{"name": "protocol-and-ports", "type": "container", "fields": []}, {"name": "icmpv4s", "type": "container", "fields": []}, {"name": "protocol", "type": "container", "fields": []}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::egress::interfaces::egress-interface", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "egress-interface", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "egress-interface"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4s::address-ipv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-ranges::address-ipv4-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-excludes::address-ipv4-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::source-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4s::address-ipv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-ranges::address-ipv4-range", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-excludes::address-ipv4-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ipv4", "type": "string", "nullable": false}, {"name": "mask", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "ipv4", "mask"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::destination-address::address-ipv4-range-excludes::address-ipv4-range-exclude", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "start-ipv4", "type": "string", "nullable": false}, {"name": "end-ipv4", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "start-ipv4", "end-ipv4"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol-and-ports::protocol-and-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "source-port", "type": "string", "nullable": false}, {"name": "dest-port", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol", "source-port", "dest-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::icmpv4s::icmpv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "uint8", "nullable": false}, {"name": "code", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items::protocol::protocol-id", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-id", "type": "uint8", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol-and-ports::protocol-and-port", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "source-port", "type": "string", "nullable": false}, {"name": "dest-port", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol", "source-port", "dest-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::icmpv4s::icmpv4", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "uint8", "nullable": false}, {"name": "code", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "type", "code"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-policy:nat-policy::rules::rule::service::service-items-exclude::protocol::protocol-id", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-id", "type": "uint8", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-server:nat-server", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "server-mappings", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-nat-server:nat-server::server-mappings::server-mapping", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "protocol", "type": "string"}, {"name": "global", "type": "container", "fields": [{"name": "global-element", "type": "choice", "fields": [{"name": "global-ip", "type": "case", "fields": [{"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}]}]}]}, {"name": "global-port", "type": "container", "fields": [{"name": "start-port", "type": "uint16"}, {"name": "end-port", "type": "uint16"}]}, {"name": "inside", "type": "container", "fields": [{"name": "start-ip", "type": "string", "nullable": false}, {"name": "end-ip", "type": "string"}]}, {"name": "inside-port", "type": "container", "fields": [{"name": "start-port", "type": "uint16"}, {"name": "end-port", "type": "uint16"}]}, {"name": "reverse-enable", "type": "boolean", "default": true}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "unicasts", "type": "container", "fields": []}, {"name": "authentications", "type": "container", "fields": []}, {"name": "status", "type": "container", "is_config": false, "fields": [{"name": "clock-status", "type": "string", "is_config": false}, {"name": "stratum", "type": "int32", "is_config": false}, {"name": "source", "type": "string", "is_config": false}, {"name": "precision", "type": "string", "is_config": false}, {"name": "offset", "type": "string", "is_config": false}, {"name": "nominal-frequence", "type": "string", "is_config": false}, {"name": "actual-frequence", "type": "string", "is_config": false}, {"name": "root-delay", "type": "string", "is_config": false}, {"name": "root-dispersion", "type": "string", "is_config": false}, {"name": "peer-dispersion", "type": "string", "is_config": false}, {"name": "reference-time", "type": "string", "is_config": false}, {"name": "sync-state", "type": "string", "is_config": false}]}, {"name": "full-sessions", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::unicasts::unicast", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}, {"name": "vpn-name", "type": "string", "nullable": false}, {"name": "is-preferred", "type": "boolean", "default": false}, {"name": "ifname", "type": "string"}, {"name": "key-id", "type": "uint32"}, {"name": "max-poll-interval", "type": "uint8", "default": 10}, {"name": "min-poll-interval", "type": "uint8", "default": 6}], "keys": [{"name": "k0", "fields": [":pid", "ip-address", "type", "vpn-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::authentications::authentication", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "key-id", "type": "uint32", "nullable": false}, {"name": "mode", "type": "string", "nullable": false}, {"name": "key-value", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "key-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-ntp:ntp::full-sessions::full-session", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "source", "type": "string", "is_config": false, "nullable": false}, {"name": "local-mode", "type": "string", "is_config": false, "nullable": false}, {"name": "vpn-name", "type": "string", "is_config": false, "nullable": false}, {"name": "ifname", "type": "string", "is_config": false, "nullable": false}, {"name": "stratum", "type": "uint8", "is_config": false}, {"name": "current-poll", "type": "string", "is_config": false}, {"name": "offset", "type": "string", "is_config": false}, {"name": "delay", "type": "string", "is_config": false}, {"name": "reachable", "type": "uint8", "is_config": false}, {"name": "when", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "source", "local-mode", "vpn-name", "ifname"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-groups", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}, {"name": "subscriptions", "type": "container", "fields": [{"name": "persistent", "type": "container", "fields": []}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-group-id", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group-id", "type": "string"}]}, {"name": "sensor-paths", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "sensor-group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::sensor-groups::sensor-group::sensor-paths::sensor-path", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "path", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "path", "type": "string"}]}], "keys": [{"name": "k0", "fields": [":pid", "path"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string"}]}, {"name": "destinations", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::destination-groups::destination-group::destinations::destination", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "destination-address", "type": "string", "nullable": false}, {"name": "destination-port", "type": "uint16", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "destination-address", "type": "string"}, {"name": "destination-port", "type": "uint16"}, {"name": "huawei-openconfig-telemetry-ext-lite:ssl-policy-name", "type": "string", "default": "default"}]}], "keys": [{"name": "k0", "fields": [":pid", "destination-address", "destination-port"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "subscription-name", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "subscription-name", "type": "string"}, {"name": "local-source-address", "type": "string"}]}, {"name": "sensor-profiles", "type": "container", "fields": []}, {"name": "destination-groups", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "subscription-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::sensor-profiles::sensor-profile", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sensor-group", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "sensor-group", "type": "string"}, {"name": "sample-interval", "type": "uint64", "default": 60000}]}], "keys": [{"name": "k0", "fields": [":pid", "sensor-group"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "openconfig-telemetry:telemetry-system::subscriptions::persistent::subscription::destination-groups::destination-group", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "group-id", "type": "string", "nullable": false}, {"name": "config", "type": "container", "fields": [{"name": "group-id", "type": "string"}]}], "keys": [{"name": "k0", "fields": [":pid", "group-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "patch-infos", "type": "container", "is_config": false, "fields": []}, {"name": "next-startup-patchs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::patch-infos::patch-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "state", "type": "string", "is_config": false}, {"name": "runtime", "type": "string", "is_config": false}, {"name": "path", "type": "string", "is_config": false}, {"name": "operations", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::patch-infos::patch-info::operations::operation", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "position", "type": "string", "is_config": false, "nullable": false}, {"name": "position-type", "type": "string", "is_config": false}, {"name": "upgrade-mode", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "position"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-patch:patch::next-startup-patchs::next-startup-patch", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global", "type": "container", "fields": [{"name": "certificate-check", "type": "container", "fields": [{"name": "validate-method", "type": "string", "default": "crl-none"}]}]}, {"name": "entitys", "type": "container", "fields": []}, {"name": "domains", "type": "container", "fields": []}, {"name": "certificate-infos", "type": "container", "is_config": false, "fields": []}, {"name": "preset-certificate-infos", "type": "container", "is_config": false, "fields": []}, {"name": "crl-infos", "type": "container", "is_config": false, "fields": []}, {"name": "key-pair-infos", "type": "container", "is_config": false, "fields": []}, {"name": "cert-key-pair-infos", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::entitys::entity", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "common-name", "type": "string"}, {"name": "fqdn", "type": "string"}, {"name": "department", "type": "string"}, {"name": "organization", "type": "string"}, {"name": "locality", "type": "string"}, {"name": "state", "type": "string"}, {"name": "country", "type": "string"}, {"name": "email", "type": "string"}, {"name": "address", "type": "choice", "fields": [{"name": "ip-address", "type": "case", "fields": [{"name": "ip-address", "type": "string"}]}]}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::domains::domain", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "entity", "type": "string"}, {"name": "digest-algorithm", "type": "string", "default": "sha-256"}, {"name": "key-pair", "type": "container", "presence": true, "fields": [{"name": "name", "type": "string", "nullable": false}, {"name": "type", "type": "string", "nullable": false}]}, {"name": "key-usage", "type": "string"}, {"name": "validate-method", "type": "string"}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::certificate-infos::certificate-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "certificates", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "type", "domain-name", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::certificate-infos::certificate-info::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "not-before", "type": "string", "is_config": false}, {"name": "not-after", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}, {"name": "fingerprint", "type": "string", "is_config": false}, {"name": "key-pair-type", "type": "string", "is_config": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot", "type": "string", "is_config": false, "nullable": false}, {"name": "cert-type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-type", "type": "string", "is_config": false, "nullable": false}, {"name": "certificates", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "slot", "cert-type", "key-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::preset-certificate-infos::preset-certificate-info::certificates::certificate", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "serial-number", "type": "string", "is_config": false}, {"name": "version", "type": "string", "is_config": false}, {"name": "not-before", "type": "string", "is_config": false}, {"name": "not-after", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}, {"name": "fingerprint", "type": "string", "is_config": false}, {"name": "key-pair-type", "type": "string", "is_config": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}, {"name": "device-info", "type": "container", "is_config": false, "fields": [{"name": "cpu-id", "type": "string", "is_config": false}, {"name": "np-id", "type": "string", "is_config": false}, {"name": "htm-id", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::crl-infos::crl-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "domain-name", "type": "string", "is_config": false, "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "crl", "type": "container", "is_config": false, "fields": [{"name": "version", "type": "string", "is_config": false}, {"name": "issuer", "type": "string", "is_config": false}, {"name": "last-update", "type": "string", "is_config": false}, {"name": "next-update", "type": "string", "is_config": false}, {"name": "signature-algorithm", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":pid", "domain-name", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::key-pair-infos::key-pair-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-size", "type": "uint32", "is_config": false}, {"name": "curve-type", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name", "type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "cert-name", "type": "string", "is_config": false, "nullable": false}, {"name": "cert-key-pairs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "cert-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-pki:pki::cert-key-pair-infos::cert-key-pair-info::cert-key-pairs::cert-key-pair", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "issuer", "type": "string", "is_config": false, "nullable": false}, {"name": "subject", "type": "string", "is_config": false, "nullable": false}, {"name": "key-type", "type": "string", "is_config": false, "nullable": false}, {"name": "key-name", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "issuer", "subject", "key-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol-ttls", "type": "container", "fields": []}, {"name": "application-ttls", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::protocol-ttls::protocol-ttl", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string", "nullable": false}, {"name": "aging-time", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "protocol"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sec-session-mgmt:sec-session-mgmt::application-ttls::application-ttl", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "application", "type": "string", "nullable": false}, {"name": "aging-time", "type": "uint16", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "application"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:smart-upgrade", "type": "container", "presence": true, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "global", "type": "container", "fields": [{"name": "transport", "type": "choice", "fields": [{"name": "http", "type": "case", "fields": [{"name": "http-url", "type": "string"}, {"name": "http-port", "type": "uint32", "default": 443}, {"name": "ssl-policy", "type": "string"}]}]}]}, {"name": "smart-upgrade-info", "type": "container", "is_config": false, "fields": [{"name": "version-info", "type": "container", "is_config": false, "fields": [{"name": "refresh-time", "type": "string", "is_config": false}, {"name": "check-version", "type": "string", "is_config": false}, {"name": "upgrade-description", "type": "string", "is_config": false}, {"name": "recommended-software-version", "type": "string", "is_config": false}, {"name": "recommended-patch-version", "type": "string", "is_config": false}]}, {"name": "upgrade-info", "type": "container", "is_config": false, "fields": [{"name": "upgrade-status", "type": "string", "is_config": false}]}, {"name": "download-infos", "type": "container", "is_config": false, "fields": []}, {"name": "local-info", "type": "container", "is_config": false, "fields": [{"name": "device-name", "type": "string", "is_config": false}, {"name": "device-esn", "type": "string", "is_config": false}, {"name": "software-version", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}]}]}, {"name": "scheduled-upgrade-info", "type": "container", "fields": [{"name": "scheduled-time", "type": "string"}, {"name": "software-version", "type": "string"}, {"name": "patch-version", "type": "string"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "product-type", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "failed-reason", "type": "string", "is_config": false}, {"name": "download-lists", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "product-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-smart-upgrade:smart-upgrade::smart-upgrade-info::download-infos::download-info::download-lists::download-list", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "version", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "download-file-name", "type": "string", "is_config": false}, {"name": "download-file-size", "type": "uint32", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "version"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "versions", "type": "container", "is_config": false, "fields": []}, {"name": "startup-packages", "type": "container", "is_config": false, "fields": []}, {"name": "upgrade-rollback-timer", "type": "container", "is_config": false, "fields": [{"name": "timer-enable", "type": "boolean", "is_config": false}, {"name": "time", "type": "uint32", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::versions::version", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "base", "type": "string", "is_config": false, "nullable": false}, {"name": "patch", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "base"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-software:software::startup-packages::startup-package", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "slot-id", "type": "string", "is_config": false, "nullable": false}, {"name": "current-package", "type": "string", "is_config": false}, {"name": "next-package", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "slot-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc:sshc", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-results", "type": "container", "is_config": false, "fields": []}, {"name": "transfer-tasks", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc:sshc::transfer-results::transfer-result", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "command-type", "type": "string", "is_config": false, "nullable": false}, {"name": "host-addr", "type": "string", "is_config": false, "nullable": false}, {"name": "server-port", "type": "uint32", "is_config": false, "nullable": false}, {"name": "vpn-name", "type": "string", "is_config": false, "nullable": false}, {"name": "local-file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "remote-file-name", "type": "string", "is_config": false, "nullable": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-tag", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "command-type", "host-addr", "server-port", "vpn-name", "local-file-name", "remote-file-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshc:sshc::transfer-tasks::transfer-task", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "transfer-id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "command-type", "type": "string", "is_config": false}, {"name": "host-addr", "type": "string", "is_config": false}, {"name": "server-port", "type": "uint32", "is_config": false}, {"name": "local-file-name", "type": "string", "is_config": false}, {"name": "remote-file-name", "type": "string", "is_config": false}, {"name": "status", "type": "string", "is_config": false}, {"name": "percentage", "type": "uint32", "is_config": false}, {"name": "error-code", "type": "uint32", "is_config": false}, {"name": "error-message", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "transfer-id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "server", "type": "container", "fields": [{"name": "pki-domain", "type": "string"}]}, {"name": "users", "type": "container", "fields": []}, {"name": "server-enable", "type": "container", "fields": [{"name": "stelnet-ipv4-enable", "type": "string", "default": "disable"}]}, {"name": "server-port", "type": "container", "fields": [{"name": "ipv4-port-number", "type": "int32", "default": 22}]}, {"name": "ipv4-server-sources", "type": "container", "fields": []}, {"name": "call-homes", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::users::user", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "nullable": false}, {"name": "key-name", "type": "string"}, {"name": "pub-key-type", "type": "string", "default": "not-set"}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::ipv4-server-sources::ipv4-server-source", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "src-interface", "type": "string", "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "src-interface"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::call-homes::call-home", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "call-home-name", "type": "string", "nullable": false}, {"name": "end-points", "type": "container", "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "call-home-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-sshs:sshs::call-homes::call-home::end-points::end-point", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "end-point-name", "type": "string", "nullable": false}, {"name": "address-hostname", "type": "choice", "fields": [{"name": "ip-address", "type": "case", "fields": [{"name": "address", "type": "string"}]}, {"name": "host-name", "type": "case", "fields": [{"name": "host-name", "type": "string"}]}]}, {"name": "port", "type": "uint16"}, {"name": "connection-status", "type": "string", "is_config": false}, {"name": "enabled", "type": "boolean", "default": true}], "keys": [{"name": "k0", "fields": [":pid", "end-point-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "log-switch-list", "type": "container", "fields": []}, {"name": "logfiles", "type": "container", "is_config": false, "fields": []}, {"name": "collect-log-process", "type": "container", "is_config": false, "fields": [{"name": "state", "type": "string", "is_config": false}, {"name": "operate-progress", "type": "uint8", "is_config": false}]}, {"name": "log-storage-time", "type": "container", "fields": [{"name": "storage-time", "type": "uint32"}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::log-switch-list::log-switch", "type": "list", "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "feature-name", "type": "string", "nullable": false}, {"name": "log-name", "type": "string", "nullable": false}, {"name": "security-log-flag", "type": "boolean", "is_config": false}, {"name": "log-type", "type": "string", "is_config": false}, {"name": "suppress", "type": "boolean", "default": false}], "keys": [{"name": "k0", "fields": [":pid", "feature-name", "log-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "logfile-type", "type": "string", "is_config": false, "nullable": false}, {"name": "latest-logs", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":pid", "logfile-type"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-syslog:syslog::logfiles::logfile::latest-logs::latest-log", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "system-info", "type": "container", "fields": [{"name": "sys-name", "type": "string", "default": "HUAWEI"}, {"name": "sys-contact", "type": "string", "default": "R&D Beijing, Huawei Technologies co.,Ltd."}, {"name": "sys-location", "type": "string", "default": "Beijing China"}, {"name": "sys-desc", "type": "string", "is_config": false}, {"name": "sys-object-id", "type": "string", "is_config": false}, {"name": "system-gmt-time", "type": "uint32", "is_config": false}, {"name": "platform-name", "type": "string", "is_config": false}, {"name": "platform-version", "type": "string", "is_config": false}, {"name": "product-name", "type": "string", "is_config": false}, {"name": "product-version", "type": "string", "is_config": false}, {"name": "uname", "type": "string", "is_config": false}, {"name": "hardware-model", "type": "string", "is_config": false}, {"name": "patch-version", "type": "string", "is_config": false}, {"name": "esn", "type": "string", "is_config": false}, {"name": "mac", "type": "string", "is_config": false}, {"name": "software-name", "type": "string", "is_config": false}, {"name": "boot-time", "type": "string", "is_config": false}, {"name": "configuration-restoring", "type": "boolean", "is_config": false}, {"name": "huawei-system-controller:upstream-info", "type": "container", "is_config": false, "fields": [{"name": "ip-address", "type": "string", "is_config": false}]}]}, {"name": "security-risks", "type": "container", "is_config": false, "fields": []}, {"name": "weak-passwords", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::system-info::huawei-system-controller:upstream-info::if-name", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "if-name", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "if-name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::security-risks::security-risk", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "level", "type": "string", "is_config": false, "nullable": false}, {"name": "feature-name", "type": "string", "is_config": false, "nullable": false}, {"name": "type", "type": "string", "is_config": false, "nullable": false}, {"name": "content", "type": "string", "is_config": false, "nullable": false}, {"name": "repair-action", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "level", "feature-name", "type", "content"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system:system::weak-passwords::weak-password", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "password", "type": "string", "is_config": false, "nullable": false}], "keys": [{"name": "k0", "fields": [":pid", "password"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "offline-records", "type": "container", "is_config": false, "fields": []}, {"name": "register-fail-records", "type": "container", "is_config": false, "fields": []}, {"name": "online-robustness-enhancement", "type": "container", "fields": [{"name": "enabled", "type": "boolean", "default": false}, {"name": "stable-time", "type": "uint32", "default": 5}, {"name": "retry-time", "type": "uint32", "default": 5}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::offline-records::offline-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-system-controller:system-controller::register-fail-records::register-fail-record", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "id", "type": "uint32", "is_config": false, "nullable": false}, {"name": "time", "type": "string", "is_config": false}, {"name": "reason", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-tm:tm", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "timezone-configuration", "type": "container", "fields": [{"name": "timezone-name", "type": "string", "default": "DefaultZoneName"}, {"name": "option", "type": "string", "default": "add"}, {"name": "timezone-offset", "type": "string", "default": "00:00:00"}]}, {"name": "dst-configuration", "type": "container", "fields": [{"name": "dst-name", "type": "string"}, {"name": "type", "type": "string"}, {"name": "start-year", "type": "uint16"}, {"name": "start-month", "type": "string"}, {"name": "start-day", "type": "string"}, {"name": "start-weeknum", "type": "string"}, {"name": "start-weekday", "type": "string"}, {"name": "start-time", "type": "string"}, {"name": "end-year", "type": "uint16"}, {"name": "end-month", "type": "string"}, {"name": "end-day", "type": "string"}, {"name": "end-weeknum", "type": "string"}, {"name": "end-weekday", "type": "string"}, {"name": "end-time", "type": "string"}, {"name": "dst-offset", "type": "string"}]}, {"name": "date-and-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}, {"name": "local-time", "type": "container", "is_config": false, "fields": [{"name": "current-time", "type": "string", "is_config": false}, {"name": "weekday", "type": "string", "is_config": false}]}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "huawei-tty:tty", "type": "container", "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications:netconf", "type": "container", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "streams", "type": "container", "is_config": false, "fields": []}], "keys": [{"name": "k0", "fields": [":id"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "nc-notifications:netconf::streams::stream", "type": "list", "is_config": false, "fields": [{"name": ":id", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": ":pid", "type": "uint32", "nullable": false}, {"name": "name", "type": "string", "is_config": false, "nullable": false}, {"name": "description", "type": "string", "is_config": false, "nullable": false}, {"name": "replaySupport", "type": "boolean", "is_config": false, "nullable": false}, {"name": "replayLogCreationTime", "type": "string", "is_config": false}], "keys": [{"name": "k0", "fields": [":pid", "name"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]