/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <malloc.h>

#include "gtest/gtest.h"
#include "Yang_reliability_common.h"

class Yang_reliability : public testing::Test {
public:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
};

void *thread_rCPU_Overloadl_value90(void *args)
{
    printf("aaaaaaaaaaaaa\n");
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rCPU_Overloadl_value90");
    printf("bbbbbbbbbbbbbbb\n");
}
void *thread_rCPU_Overloadl_value100(void *args)
{
    printf("aaaaaaaaaaaaa\n");
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rCPU_Overloadl_value");
    printf("bbbbbbbbbbbbbbb\n");
}
void *thread_rProc_k(void *args)
{
    printf("aaaaaaaaaaaaa\n");
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rProc_k");
    printf("bbbbbbbbbbbbbbb\n");
    g_server_exit = true;
}

void *thread_rProc_kill19_18_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rProc_kill19_18_server");
    g_server_exit = true;
}

void *thread_rProc_kill19_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rProc_kill19_server");
    printf("进程挂起 \n");
    sleep(80);
    g_server_hang = true;
}

void *thread_rProc_kill18_server(void *args)
{
    system("sh ${TEST_HOME}/reliability/YangReliabilityTest/CFE.sh rProc_kill18_server");
    printf("进程解挂 \n");
}
void *thread_reliability_while_creat_delete_Yangvertexlabel(void *args)
{
    AsyncUserDataT YanguserData = {0};
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    YangConnOptionT connOptions = {0};
    int ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
    memset(&YanguserData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
    memset(&YanguserData, 0, sizeof(AsyncUserDataT));
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
        ret = GmcCreateVertexLabelAsync(
            Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &YanguserData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&YanguserData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端已退出
        if ((YanguserData.status == GMERR_CONNECTION_RESET_BY_PEER) || (YanguserData.status == GMERR_INTERNAL_ERROR) ||
            (YanguserData.status == GMERR_REQUEST_TIME_OUT)) {

            // 断开链接
            ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
            // clear成功
            ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &YanguserData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&YanguserData);
            // 服务端异常退出
            if ((YanguserData.status == GMERR_CONNECTION_RESET_BY_PEER) ||
                (YanguserData.status == GMERR_INTERNAL_ERROR) || (YanguserData.status == GMERR_REQUEST_TIME_OUT)) {

                // 断开链接
                ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(&YanguserData, 0, sizeof(AsyncUserDataT));
        }
    }
}
void *thread_reliability_while_creat_delete_vertexlabel(void *args)
{
    AsyncUserDataT userData = {0};
    char *vertexschema = NULL;
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    YangConnOptionT connOptions = {0};
    int ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace2 = "NamespaceV5";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_async, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/1023_schema.gmjson", &vertexschema);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexschema, NULL, create_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端已退出
        if ((userData.status == GMERR_CONNECTION_RESET_BY_PEER) || (userData.status == GMERR_INTERNAL_ERROR) ||
            (userData.status == GMERR_REQUEST_TIME_OUT)) {

            // 断开链接
            ret = testGmcDisconnect(g_conn_async, g_stmt_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            // clear成功
            ret = GmcClearNamespaceAsync(g_stmt_async, namespace2, drop_namespace_callback, &userData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            // 服务端异常退出
            if ((userData.status == GMERR_CONNECTION_RESET_BY_PEER) || (userData.status == GMERR_INTERNAL_ERROR) ||
                (userData.status == GMERR_REQUEST_TIME_OUT)) {

                // 断开链接
                ret = testGmcDisconnect(g_conn_async, g_stmt_async);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(&userData, 0, sizeof(AsyncUserDataT));
        }
    }
}
void *thread_reliability_server_Yanghang_up(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = 0;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    bool isGmserver = true;
    int count = 0;
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
        ret = GmcCreateVertexLabelAsync(
            Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端挂起 客户端收不到消息
        if (userData.status == GMERR_REQUEST_TIME_OUT) {
            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {
            isGmserver = true;  // 正常
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // clear成功
        ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        // 服务端挂起
        if (userData.status == GMERR_REQUEST_TIME_OUT) {
            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {

            isGmserver = true;  // 正常
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if ((isGmserver == true) && (count == 1)) {
            // 服务恢复正常
            break;
        }
    }
}
void *thread_reliability_server_hang_up(void *args)
{
    AsyncUserDataT userData = {0};
    char *vertexschema = NULL;
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    YangConnOptionT connOptions = {0};
    int ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace2 = "NamespaceV5";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_async, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    bool isGmserver = true;
    int count = 0;
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/1023_schema.gmjson", &vertexschema);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexschema, NULL, create_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端挂起 客户端收不到消息
        if (userData.status == GMERR_REQUEST_TIME_OUT) {

            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {
            isGmserver = true;  // 正常
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // clear成功
        ret = GmcClearNamespaceAsync(g_stmt_async, namespace2, drop_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        // 服务端挂起
        if (userData.status == GMERR_REQUEST_TIME_OUT) {

            isGmserver = false;  // 挂起状态
            count = 1;
            ret = GMERR_OK;
        } else {

            isGmserver = true;  // 正常
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if ((isGmserver == true) && (count == 1)) {
            // 服务恢复正常
            break;
        }
    }
}

void *thread_reliability_server_hang_up_subtree(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = 0;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    EXPECT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    EXPECT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    bool isGmserver = true;
    int count = 0;
    while (1) {
        // 创建乐观事务
        TransStart(Yang_conn_async);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam01 param = {
            .step = 0,
            .stmt = Yang_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };
        if (g_server_hang == true) {
            param.expectStatus = GMERR_REQUEST_TIME_OUT;
        } else {
            param.expectStatus = GMERR_OK;
        }

        ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb01, &param);
        if (g_server_exit == true) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
            param.step = 1;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = testWaitAsyncSubtreeRecv_APP(&param);
        if (param.step == 1) {
            free(suntreeReturnJson);
            // 提交事务
            TransCommit(Yang_conn_async);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交事务
        TransCommit(Yang_conn_async);
        if (g_subtreecount == 25000) {
            break;
        }
    }
}

void *thread_reliability_while_diff(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = 0;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    EXPECT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    EXPECT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    // 创建乐观事务
    TransStart(Yang_conn_async);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel16Node = NULL;
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 300;
    while (1) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel16, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel16Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_1, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "contentid%d", i);
        ret = testYangSetField(g_stmt_sync_1, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "content-id",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = testWaitAsyncRecv(&userData);
        if (userData.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        i++;
        // 获取diff
        AsyncUserDataT data = {0};
        data.stmt = g_stmt_sync_1;
        data.expectDiff = &expectDiff54;
        ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_1, NULL, FetchDiff_callback, &data);
        if (ret != GMERR_OK) {
            testGmcGetLastError(NULL);
        }
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }
}
void *thread_reliability_hang_up_diff(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    EXPECT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    EXPECT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    // 创建乐观事务
    TransStart(Yang_conn_async);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel16Node = NULL;
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 300;
    while (1) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel16, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel16Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_1, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "contentid%d", i);
        ret = testYangSetField(g_stmt_sync_1, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "content-id",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        if (userData.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        i++;
        // 获取diff
        AsyncUserDataT data = {0};
        data.stmt = g_stmt_sync_1;
        data.expectDiff = &expectDiff54;
        ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_1, NULL, FetchDiff_callback, &data);
        if (ret != GMERR_OK) {
            testGmcGetLastError(NULL);
        }
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            // 提交事务
            TransCommit(Yang_conn_async);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
        if (i == 400) {
            // 提交事务
            TransCommit(Yang_conn_async);
            break;
        }
    }
}
void *thr_arr_Subtree(void *args)
{
    system("gmsysview subtree -ns NamespaceYang");
}
/*****************************************************************************
 * Description  : 系统CPU过载  写入典配数据
 * Input        : 1.数据库进程绑核启动（假设绑定在CPU1上）
                    2.CPU1使用率为100%
                    3.进程1导入R21版本的530张表，进程2并发导入Yang的全量典配表数据
                    4.所有表导入成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_001)
{
    AW_FUN_Log(LOG_STEP, "create yang label start.");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建异步连接
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 服务端进程绑定CPU指定核心
    system("taskset -pc 1 `pidof gmserver`");
    // FLT 将CPU过载100%
    pthread_t FLT;
    pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value100, NULL);
    sleep(1);

    system("./Yang_reliability_001_tools &");
    system("./YangRel05 &");
    sleep(10);

    pthread_join(FLT, NULL);
    // 断开链接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "create yang label end.");
}
/*****************************************************************************
 * Description  : 进程挂起
 * Input : 1.客户端进程1操作vertext表，循环删表建表写入和删除操作；客户端进程2操作YANG表，循环创建yang表和删除操作
                    2.服务端进程挂起
                    3.客户端收到异常通知
                    4.客户端关闭连接资源，正常
                    5.服务端恢复后，客户端1和2连接正常，操作正常进行
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret;
    ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t FLT, DB, DB1;
    // 循环创建yang表 接受异常通知 以及重新建表
    pthread_create(&DB, NULL, thread_reliability_server_Yanghang_up, NULL);
    // 循环创建普通表 接受异常通知 以及重新建表
    pthread_create(&DB1, NULL, thread_reliability_server_hang_up, NULL);
    // 服务进程挂起和恢复
    pthread_create(&FLT, NULL, thread_rProc_kill19_18_server, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);
    const char *namespace1 = "NamespaceYang";
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace1
    ret = GmcDropNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace2 = "NamespaceV5";
    ret = GmcUseNamespaceAsync(stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(stmt_async, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace2
    ret = GmcDropNamespaceAsync(stmt_async, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 建立超规格字段YANG表
 * Input        :1.建立超规格字段YANG表（含边表），失败
                2.建立规格内最大YANG表（含边表），成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_005)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    void *g_label = NULL;
    char *g_schema_1024 = NULL;
    char *g_schema_1023 = NULL;

    // 超规格字段yang表 创建失败
    readJanssonFile("SOHO_S380/fields_1024_schema.gmjson", &g_schema_1024);
    EXPECT_NE((void *)NULL, g_schema_1024);
    ret = GmcCreateVertexLabelAsync(g_stmt_sync, g_schema_1024, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema_1024);
    int k = 0;
    while (1) {
        // 最大规格字段yang表 创建成功
        readJanssonFile("SOHO_S380/fields_1023_schema.gmjson", &g_schema_1023);
        EXPECT_NE((void *)NULL, g_schema_1023);
        ret = GmcCreateVertexLabelAsync(
            g_stmt_sync, g_schema_1023, g_labelconfig, create_vertex_label_callback, &userData);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        // 创建乐观事务
        TransStart(g_conn_sync);
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(g_conn_sync, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "fields_1023_schema", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t num = 1;
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0001", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0002", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0003", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0004", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0005", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0006", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0007", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0008", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0009", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0010", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 提交批处理
        BatchExecute(batch);
        GmcBatchDestroy(batch);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        // 提交事务
        TransCommit(g_conn_sync);
        ret = GmcDropVertexLabelAsync(g_stmt_sync, "fields_1023_schema", drop_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        free(g_schema_1023);
        k++;
        if (k == 20) {
            break;
        }
    }
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 表的个数超系统限制
 * Input        :1.建立超规格字段YANG表（含边表），失败
                2.建立规格内最大YANG表（含边表），成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_006)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char labelName[128] = "OP_T0";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    uint32_t tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    int k = 0;
    system("rm -rf yangdbSchema");
    uint32_t existTableNum = 0;
    ret = TestGetYangTableNum(&existTableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char shellCmd[128] = {0};
    sprintf(shellCmd, "sh create_multi_yangdb_schema.sh %d", tableNum - existTableNum);
    system(shellCmd);
    // 循环创建 最大yang表 +1 写数据
    while (1) {
        // 创建yang表个数最大
        for (uint32_t i = 0; i < tableNum - existTableNum; i++) {
            char yangSchemaPath[128] = {0};
            sprintf(yangSchemaPath, "yangdbSchema/yang_vertex_tosand_%d.gmjson", i);
            readJanssonFile(yangSchemaPath, &schema);
            ASSERT_NE((void *)NULL, schema);
            ret =
                GmcCreateVertexLabelAsync(g_stmt_sync, schema, g_labelconfig, create_vertex_label_callback, &userData);
            ASSERT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            ASSERT_EQ(GMERR_OK, userData.status);
            free(schema);
        }

        // 再创建+1个yang表失败
        readJanssonFile("SOHO_S380/yang_vertex.gmjson", &schema2);
        ASSERT_NE((void *)NULL, schema2);
        ret = GmcCreateVertexLabelAsync(g_stmt_sync, schema2, g_labelconfig, create_vertex_label_callback, &userData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
        free(schema2);

        // 创建乐观事务
        TransStart(g_conn_sync);
        GmcBatchT *batch = NULL;
        ret = testBatchPrepareAndSetDiff(g_conn_sync, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t num = 1;
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 提交批处理
        BatchExecute(batch);
        GmcBatchDestroy(batch);
        // 提交事务
        TransCommit(g_conn_sync);

        // 删除yang表
        for (int i = 0; i < tableNum - existTableNum; i++) {
            sprintf(labelName, "T%d", i);
            ret = GmcDropVertexLabelAsync(g_stmt_sync, labelName, drop_vertex_label_callback, &userData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
            memset(&userData, 0, sizeof(AsyncUserDataT));
        }
        k++;
        if (k == 20) {
            break;
        }
    }
    system("rm -rf yangdbSchema");
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 超大规格数据写
 * Input
 * :1.客户端操作YANG表，循环创建namespace并写入数据，删除namespace并重新写入数据（升级表定义）再新增数据，直到写失败
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_007)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 写数据到超过表记录数
    testYanginsertacltabelspace(Yang_conn_async, true);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
bool bufferfull = false;
bool bufferfullnext = false;
bool fullbreak = false;
void batch_execute_callback_buffer_full(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    // 回调一直不接受服务端返回消息 等待 队列满以后 开始接受消息
    while (1) {
        sleep(1);
        if (bufferfull == true)
            break;
    }
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        AW_FUN_Log(LOG_INFO, "user_data status: %d, recv: %d, succ: %d.", user_data->status, user_data->recvNum,
            user_data->succNum);
        if (user_data->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            // 结果检查
            EXPECT_EQ(user_data->expectedErrorCode, msg.errorCode);
            EXPECT_STREQ(user_data->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(user_data->expectedErrPath, msg.errorPath);
        }
    }
}
void BatchExecute_buffer_full(GmcBatchT *batch, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback_buffer_full, &userData);
    // 之道batch提交 返回 队列满以后 放开回调从服务端接受消息
    if (ret != GMERR_OK) {

        AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
        bufferfull = true;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 回调开始收消息 用例开始wait 看成功数量
    if (bufferfull == true) {
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);

        AW_MACRO_EXPECT_EQ_INT(userData.totalNum, userData.succNum);
        if (1 == userData.succNum) {
            bufferfullnext = true;
        }
    }
}
/*****************************************************************************
 * Description  : 异步消息队列写满
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_008)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 创建乐观事务
    TransStart(Yang_conn_async);
    uint32_t k = 0;
    uint32_t num = 0;
    while (1) {
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(Yang_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int m = 200 + k * 2200; m < 2200 * (k + 1); m++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置g_vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue3[100];
            memset(namevalue3, 0, sizeof(namevalue3));
            (void)snprintf(namevalue3, 100, "identity%d", m);
            ret = testYangSetField(g_stmt_sync_12, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)), "identity",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_12);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "%s batch add ret is: %d, index is %d, cycle is %d.", g_vertexLabel7, ret, m, k);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        k++;
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_buffer_full, &userData);
        // 直到batch提交 返回 队列满以后 放开回调从服务端接受消息
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            bufferfull = true;
            AW_FUN_Log(LOG_INFO, "%s batch exec ret is: %d, cycle is %d.", g_vertexLabel7, ret, k);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (k > 30) {
            bufferfull = true;
            break;
        };

        AW_FUN_Log(LOG_INFO, "%s batch exec ret is: %d, cycle is %d.", g_vertexLabel7, ret, k);
        GmcBatchDestroy(batch);
    }

    // 回调开始收消息 用例开始wait 看成功数量 将队列中的数据全部wait
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testWaitAsyncRecv(&userData, k - 1, 60000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TransRollback(Yang_conn_async);
    sleep(10);

    // clear成功
    AW_FUN_Log(LOG_INFO, "clear namespace.");
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData, 1, 12000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, userData.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    }

    // 异步删除namespace
    AW_FUN_Log(LOG_INFO, "drop namespace.");
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData, 1, 12000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    AW_FUN_Log(LOG_INFO, "dis conn.");
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 表空间满
 * Input        :1.客户端1建vertext表，写入部分数据。客户端2创建10张YANG表（包含边表）并写入数据到表空间满
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_010)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 4;

    ret = GmcCreateTablespaceAsync(Yang_stmt_async, &tspCfg, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 写数据到表空间满
    testYanginsertacltabelspace(Yang_conn_async);
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();

    ret = GmcDropTablespaceAsync(Yang_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
bool sleepwait = true;
void batch_execute_callback_sleep(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    sleep(120);
    sleepwait = false;
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        if (status != GMERR_REQUEST_TIME_OUT) {
            int ret =
                GmcBatchDeparseRet(batchRet, (uint32_t *)&(user_data->totalNum), (uint32_t *)&(user_data->succNum));
            ASSERT_EQ(GMERR_OK, ret);
        }
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            if (errMsg) {
                int ret = strcmp(user_data->lastError, errMsg);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                printf("expect lastError: %s,  actual: null\n", user_data->lastError);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            }
        }
        user_data->recvNum++;
        if (user_data->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));
            // 结果检查
            EXPECT_EQ(user_data->expectedErrorCode, msg.errorCode);
            EXPECT_STREQ(user_data->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(user_data->expectedErrPath, msg.errorPath);
        }
    }
}
/*****************************************************************************
 * Description  : 客户端epoll线程挂死
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_011)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    char *g_schema_1023 = NULL;
    // 最大规格字段yang表 创建成功
    readJanssonFile("SOHO_S380/fields_1023_schema.gmjson", &g_schema_1023);
    EXPECT_NE((void *)NULL, g_schema_1023);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, g_schema_1023, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 创建乐观事务
    TransStart(Yang_conn_async);
    uint32_t k = 0;
    while (1) {
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(Yang_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, "fields_1023_schema", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, Yang_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(Yang_stmt_async, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0001", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0002", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0003", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0004", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0005", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0006", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0007", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0008", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0009", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            Yang_stmt_async, GMC_DATATYPE_UINT32, &k, sizeof(uint32_t), "F0010", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, Yang_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 提交批处理 回调函数 先不处理消息 120s以后处理消息
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_sleep, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        sleep(120);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        GmcBatchDestroy(batch);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        k++;
        if (sleepwait == false) {
            break;
        }
    }
    // 提交事务
    TransCommit(Yang_conn_async);
    ret = GmcDropVertexLabelAsync(Yang_stmt_async, "fields_1023_schema", drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema_1023);
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 客户端epoll线程处理慢
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_012)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 创建乐观事务
    TransStart(Yang_conn_async);
    uint32_t k = 0;
    uint32_t num = 0;
    while (1) {
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(Yang_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int m = 200 + k * 2200; m < 2200 * (k + 1); m++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 设置g_vertexLabel2 list
            ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue3[100];
            memset(namevalue3, 0, sizeof(namevalue3));
            (void)snprintf(namevalue3, 100, "identity%d", m);
            ret = testYangSetField(g_stmt_sync_12, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)), "identity",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_12);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_INFO, "%s batch add ret is: %d, index is %d, cycle is %d.", g_vertexLabel7, ret, m, k);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        k++;
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback_buffer_full, &userData);
        // 直到batch提交 返回 队列满以后 放开回调从服务端接受消息
        if (ret != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
            bufferfull = true;
            AW_FUN_Log(LOG_INFO, "%s batch exec ret is: %d, cycle is %d.", g_vertexLabel7, ret, k);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (k > 30) {
            bufferfull = true;
            break;
        };
        GmcBatchDestroy(batch);
    }

    // 回调开始收消息 用例开始wait 看成功数量 将队列中的数据全部wait
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testWaitAsyncRecv(&userData, k - 1, 60000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TransRollback(Yang_conn_async);
    sleep(10);

    // clear成功
    AW_FUN_Log(LOG_INFO, "clear namespace.");
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData, 1, 12000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, userData.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    }

    // 异步删除namespace
    AW_FUN_Log(LOG_INFO, "drop namespace.");
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData, 1, 12000000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/*****************************************************************************
 * Description  : 系统CPU过载 subtree查询
 * Input        : 1.使用工具占用cpu（CPU1使用率为100%）
2.启动服务端进程（绑定到CPU1）
3.服务启动成功，耗时较长。
4.客户端进程1操作vertext表，循环执行读写操作；客户端进程2操作YANG表（满配），循环执行subtree全量查询，成功返回
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_013)
{
    AW_FUN_Log(LOG_STEP, "create yang label start.");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    // 创建异步连接
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 服务端进程绑定CPU指定核心
    system("taskset -pc 1 `pidof gmserver`");
    // FLT 将CPU过载90%
    pthread_t FLT, DB_1, DB_2;
    pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value100, NULL);
    // 等待CPU过载注入成功
    sleep(1);

    system("./Yang_reliability_013_tools &");
    system("./YangRel05 &");
    sleep(10);

    pthread_join(FLT, NULL);
    // 断开链接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "create yang label end.");
}
/*****************************************************************************
 * Description  : 进程挂起 subtree查询
 * Input
 : 1.客户端进程1操作vertext表，循环删表建表写入和删除操作；客户端进程2操作YANG表，循环创建namespace并写入数据，删除namespace
                    2.服务端进程挂起
                    3.客户端收到异常通知
                    4.客户端关闭连接资源，正常
                    5.服务端恢复后，客户端1和2连接正常，操作正常进行
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh ");
    g_server_exit = false;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, FLT1, DB1;
    pthread_create(&DB, NULL, thread_reliability_server_hang_up_subtree, NULL);
    // 循环创建删除普通表
    pthread_create(&DB1, NULL, thread_reliability_server_hang_up, NULL);
    pthread_create(&FLT, NULL, thread_rProc_kill19_18_server, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);

    const char *namespace1 = "NamespaceYang";
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace1
    ret = GmcDropNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace2 = "NamespaceV5";
    ret = GmcUseNamespaceAsync(stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(stmt_async, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace2
    ret = GmcDropNamespaceAsync(stmt_async, namespace2, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 建立超规格字段YANG表 subtree查询
 * Input        :1.建立超规格字段YANG表（含边表），失败
                2.建立规格内最大YANG表（含边表），成功
                3.循环执行单表subtree查询和namespace全量数据查询操作，都成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_017)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    void *g_label = NULL;
    char *g_schema_1024 = NULL;
    char *g_schema_1023 = NULL;

    readJanssonFile("SOHO_S380/fields_1024_schema.gmjson", &g_schema_1024);
    EXPECT_NE((void *)NULL, g_schema_1024);
    ret = GmcCreateVertexLabelAsync(g_stmt_sync, g_schema_1024, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema_1024);
    int k = 0;
    while (1) {
        // 最大规格字段yang表 创建成功
        readJanssonFile("SOHO_S380/fields_1023_schema.gmjson", &g_schema_1023);
        EXPECT_NE((void *)NULL, g_schema_1023);
        ret = GmcCreateVertexLabelAsync(
            g_stmt_sync, g_schema_1023, g_labelconfig, create_vertex_label_callback, &userData);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        // 创建乐观事务
        TransStart(g_conn_sync);
        GmcBatchT *batch = NULL;
        ret = testBatchPrepare(g_conn_sync, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "fields_1023_schema", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        GmcNodeT *vertexLabel7Node = NULL;
        ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t num = 1;
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0001", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0002", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0003", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0004", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0005", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0006", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0007", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0008", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0009", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0010", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 提交批处理
        BatchExecute(batch);
        GmcBatchDestroy(batch);
        memset(&userData, 0, sizeof(AsyncUserDataT));

        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "fields_1023_schema", GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 2,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        char *subtreeReturnJson = NULL;
        readJanssonFile("SubtreeReplyJson/EXPLICIT29.json", &subtreeReturnJson);
        std::vector<std::string> reply(1);
        reply[0] = subtreeReturnJson;
        FetchRetCbParam param = {
            .step = 0,
            .stmt = g_stmt_sync,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .expectReply = reply,
        };
        ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_sync, &filters, NULL, AsyncFetchRetCb, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(subtreeReturnJson);

        // 提交事务
        TransCommit(g_conn_sync);
        ret = GmcDropVertexLabelAsync(g_stmt_sync, "fields_1023_schema", drop_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        free(g_schema_1023);
        k++;
        if (k == 20) {
            break;
        }
    }
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 表的个数超系统限制 subtree查询
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_018)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char labelName[128] = "OP_T0";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL, *schema3 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        char namespace1[20];
        const char *namespaceUserName = "abc";
        memset(namespace1, 0, sizeof(namespace1));
        sprintf(namespace1, "NamespaceYang%d", i);
        GmcNspCfgT nspCfg;
        nspCfg.tablespaceName = NULL;
        nspCfg.namespaceName = namespace1;
        nspCfg.userName = namespaceUserName;
        nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
        ret = TryDropNameSpace(g_stmt_sync, namespace1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
    }

    system("rm -rf yangdbSchema");
    uint32_t existTableNum = 0;
    ret = TestGetYangTableNum(&existTableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char shellCmd[128] = {0};
    sprintf(shellCmd, "sh create_multi_yangdb_schema.sh %d", tableNum - existTableNum);
    system(shellCmd);

    for (int i = 0; i < 10; i++) {
        char namespace1[20];
        memset(namespace1, 0, sizeof(namespace1));
        sprintf(namespace1, "NamespaceYang%d", i);
        ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
        // 创建yang表 part1
        for (uint32_t j = 0; j < tableNum - existTableNum; j++) {
            char yangSchemaPath[128] = {0};
            sprintf(yangSchemaPath, "yangdbSchema/yang_vertex_tosand_%d.gmjson", j);
            readJanssonFile(yangSchemaPath, &schema);
            ASSERT_NE((void *)NULL, schema);
            ret =
                GmcCreateVertexLabelAsync(g_stmt_sync, schema, g_labelconfig, create_vertex_label_callback, &userData);
            ASSERT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            free(schema);
            if (i > 0) {
                ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
                break;
            } else {
                if (userData.status != GMERR_OK) {
                    AW_FUN_Log(LOG_INFO, "namespace is: %s, table index is :%d.", namespace1, j);
                }
                ASSERT_EQ(GMERR_OK, userData.status);
            }
        }

        // 创建yang表失败
        readJanssonFile("SOHO_S380/yang_vertex.gmjson", &schema2);
        ASSERT_NE((void *)NULL, schema2);
        ret = GmcCreateVertexLabelAsync(g_stmt_sync, schema2, g_labelconfig, create_vertex_label_callback, &userData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
        free(schema2);
    }
    // 对yang表做dml操作 和查询操作
    char namespace1[20] = "NamespaceYang0";
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 创建乐观事务
    TransStart(g_conn_sync);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_sync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    GmcNodeT *vertexLabel7Node = NULL;
    ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t num = 1;
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T1", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/EXPLICIT30.json", &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_sync, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    // 提交事务
    TransCommit(g_conn_sync);
    for (int j = 0; j < 10; j++) {
        char namespace1[20];
        memset(namespace1, 0, sizeof(namespace1));
        sprintf(namespace1, "NamespaceYang%d", j);
        ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));

        for (int i = 0; i < tableNum - existTableNum; i++) {
            sprintf(labelName, "T%d", i);
            ret = GmcDropVertexLabelAsync(g_stmt_sync, labelName, drop_vertex_label_callback, &userData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            if (j > 0) {
                AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, userData.status);
                break;
            } else {
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
            }
            memset(&userData, 0, sizeof(AsyncUserDataT));
        }

        system("rm -rf yangdbSchema");
        // 异步删除namespace
        ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
    }
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 超大规格数据写 subtree查询
 * Input
 :1.客户端操作YANG表，循环创建namespace并写入数据，删除namespace并重新写入数据（升级表定义）再新增数据，直到写失败
                    2.对该表进行修改、删除数据，都成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_019)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 创建乐观事务
    TransStart(Yang_conn_async);
    fetchSubtreeacl(Yang_stmt_async);
    // 提交事务
    TransCommit(Yang_conn_async);
    // 写数据到超过表记录数
    testYanginsertacltabelspace(Yang_conn_async, true);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    testEnvClean();
}
void AsyncFetchRetCb_full(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    while (1) {
        sleep(1);
        if (bufferfull == true) {
            break;
        }
    }
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    if (status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        param01->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
void AsyncFetchRetCb_sleep(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    while (1) {
        sleep(60);
        break;
    }
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    if (status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        param01->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
void AsyncFetchRetCb_sleep20(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    while (1) {
        sleep(20);
        break;
    }
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    if (status != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        param01->step++;
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
/*****************************************************************************
 * Description  : 异步消息队列写满  subtree查询
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_020)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    //开启事务进行subtree查询
    TransStart(Yang_conn_async);
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam01 param = {
        .step = 0,
        .stmt = Yang_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb_sleep20, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    TransCommit(Yang_conn_async);
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 结果报文超过大报文连接最大上限 subtree查询
 * Input        : 1.客户端1建vertext表，。客户端2创建10张YANG表（包含边表）
                2.客户端2循环进行全量查询，返回结果超过大报文最大上限
                3.客户端1不受影响，客户端2获得分批返回结果
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_021)
{
    AW_FUN_Log(LOG_STEP, "create yang label start.");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建异步连接
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 服务端进程绑定CPU指定核心
    system("taskset -pc 1 `pidof gmserver`");
    // FLT 将CPU过载90%
    pthread_t FLT, DB_1, DB_2;
    pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value100, NULL);
    // 等待CPU过载注入成功
    sleep(1);

    system("./Yang_reliability_021_tools &");
    system("./YangRel05 &");
    sleep(10);

    pthread_join(FLT, NULL);
    // 断开链接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "create yang label end.");
}
/*****************************************************************************
 * Description  : 表空间满 subtree查询
 * Input        :1.客户端1建vertext表，写入部分数据。客户端2创建10张YANG表（包含边表）并写入数据到表空间满
                    2.客户端2删除部分数据，继续执行subtree查询操作成功。客户端1执行读写操作成功。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_022)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/start.sh ");
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 4;

    ret = GmcCreateTablespaceAsync(Yang_stmt_async, &tspCfg, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 表空间满
    testYanginsertacltabelspace(Yang_conn_async);
    //开启事务进行subtree查询
    TransStart(Yang_conn_async);
    GmcNodeT *root = NULL;
    ret =
        testGmcPrepareStmtByLabelName(Yang_stmt_async, "ietf-yang-library:yang-library", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    std::vector<std::string> reply(1);
    reply[0] = "{}";
    FetchRetCbParam param = {
        .step = 0,
        .stmt = Yang_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交事务
    TransCommit(Yang_conn_async);
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();

    ret = GmcDropTablespaceAsync(Yang_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  :  客户端epoll线程挂死 subtree查询
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_023)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    //开启事务进行subtree查询
    TransStart(Yang_conn_async);
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam01 param = {
        .step = 0,
        .stmt = Yang_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb_sleep, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    TransCommit(Yang_conn_async);
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
} /*****************************************************************************
   * Description  :  客户端epoll线程处理慢 subtree查询
   * Input        :
   * Output       : None
   * Author       : wk/wwx1038088
   * Modification : Create function
   * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_024)
{
    AW_FUN_Log(LOG_STEP, "test start");
    g_server_exit = false;
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    //开启事务进行subtree查询
    TransStart(Yang_conn_async);
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam01 param = {
        .step = 0,
        .stmt = Yang_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb_sleep20, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson);
    // 提交事务
    TransCommit(Yang_conn_async);
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    testEnvClean();
}
/*****************************************************************************
 * Description  : 系统CPU过载  写入典配数据 获取diff
 * Input        : 1.数据库进程绑核启动（假设绑定在CPU1上）
                    2.CPU1使用率为90%
                    3.进程1导入R21版本的530张表，进程2并发导入Yang的全量典配表数据
                    4.所有表导入成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_025)
{
    AW_FUN_Log(LOG_STEP, "create yang label start.");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建异步连接
    GmcStmtT *g_stmt_async = NULL;
    GmcConnT *g_conn_async = NULL;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 服务端进程绑定CPU指定核心
    system("taskset -pc 1 `pidof gmserver`");
    // FLT 将CPU过载90%
    pthread_t FLT, DB_1, DB_2;
    pthread_create(&FLT, NULL, thread_rCPU_Overloadl_value100, NULL);
    // 等待CPU过载注入成功
    sleep(1);
    system("./Yang_reliability_025_tools &");
    system("./YangRel05 &");
    sleep(10);
    pthread_join(FLT, NULL);
    // 断开链接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_server_exit = false;
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "create yang label end.");
}

/*****************************************************************************
 * Description  : 进程挂起 获取diff
 * Input        : 1.客户端进程1操作vertext表，循环执行读写操作；客户端进程2操作YANG表，循环执行六原语操作，获取Diff
                2.服务端进程挂起
                3.客户端收到异常通知
                4.客户端关闭连接资源，正常
                5.服务端恢复后，客户端1和2连接正常，操作正常进行
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, FLT1;
    pthread_create(&DB, NULL, thread_reliability_hang_up_diff, NULL);
    pthread_create(&FLT, NULL, thread_rProc_kill19_server, NULL);
    pthread_join(FLT, NULL);
    pthread_create(&FLT1, NULL, thread_rProc_kill18_server, NULL);
    pthread_join(FLT1, NULL);
    pthread_join(DB, NULL);
    const char *namespace1 = "NamespaceYang";
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcClearNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace1
    ret = GmcDropNamespaceAsync(stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 超大规格表 获取diff
 * Input        :1.建立超规格字段YANG表（含边表），失败
                    2.建立规格内最大YANG表（含边表），成功
                    3.循环执行执行六原语操作，获取Diff，都成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_029)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    void *g_label = NULL;
    char *g_schema_1024 = NULL;
    char *g_schema_1023 = NULL;

    readJanssonFile("SOHO_S380/fields_1024_schema.gmjson", &g_schema_1024);
    EXPECT_NE((void *)NULL, g_schema_1024);
    ret = GmcCreateVertexLabelAsync(g_stmt_sync, g_schema_1024, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema_1024);
    readJanssonFile("SOHO_S380/fields_1023_schema.gmjson", &g_schema_1023);
    EXPECT_NE((void *)NULL, g_schema_1023);
    ret = GmcCreateVertexLabelAsync(g_stmt_sync, g_schema_1023, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 创建乐观事务
    TransStart(g_conn_sync);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepareAndSetDiff(g_conn_sync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "fields_1023_schema", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    GmcNodeT *vertexLabel7Node = NULL;
    ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t num = 1;
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0001", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0002", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0003", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0004", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0005", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0006", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0007", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0008", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0009", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_UINT32, &num, sizeof(uint32_t), "F0010", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch);
    AsyncUserDataT data = {0};
    testFetchAndDeparseDiff(g_stmt_sync, batch, expectDiff54, data);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 提交事务
    TransCommit(g_conn_sync);
    ret = GmcDropVertexLabelAsync(g_stmt_sync, "fields_1023_schema", drop_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_schema_1023);
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 表的个数超系统限制 获取diff
 * Input        :1.在10个namespace中循环创建不同的vertex表和YANG表（包含边表），直到创建失败
                2.删除1个存在的vertex表，新创建1个YANG表，失败，新创建1个vertex表，成功。
                3.删除1个存在的YANG表，新创建1个vertext表，失败，新创建1个YANG表，成功。
                4.循环执行执行六原语操作，获取Diff，都成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_030)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char labelName[128] = "OP_T0";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_sync, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_sync, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    system("rm -rf yangdbSchema");
    uint32_t existTableNum = 0;
    ret = TestGetYangTableNum(&existTableNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char shellCmd[128] = {0};
    sprintf(shellCmd, "sh create_multi_yangdb_schema.sh %d", tableNum - existTableNum);
    system(shellCmd);

    for (uint32_t i = 0; i < tableNum - existTableNum; i++) {
        char yangSchemaPath[128] = {0};
        sprintf(yangSchemaPath, "yangdbSchema/yang_vertex_tosand_%d.gmjson", i);
        readJanssonFile(yangSchemaPath, &schema);
        ASSERT_NE((void *)NULL, schema);
        ret = GmcCreateVertexLabelAsync(g_stmt_sync, schema, g_labelconfig, create_vertex_label_callback, &userData);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        ASSERT_EQ(GMERR_OK, userData.status);
        free(schema);
    }

    readJanssonFile("SOHO_S380/yang_vertex.gmjson", &schema2);
    ASSERT_NE((void *)NULL, schema2);
    ret = GmcCreateVertexLabelAsync(g_stmt_sync, schema2, g_labelconfig, create_vertex_label_callback, &userData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, userData.status);
    free(schema2);

    // 创建乐观事务
    TransStart(g_conn_sync);
    GmcBatchT *batch = NULL;
    ret = testBatchPrepareAndSetDiff(g_conn_sync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "T1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    GmcNodeT *vertexLabel7Node = NULL;
    ret = GmcGetRootNode(g_stmt_sync, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t num = 1;
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_sync, GMC_DATATYPE_INT32, &num, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    BatchExecute(batch);
    AsyncUserDataT data = {0};
    testFetchAndDeparseDiff(g_stmt_sync, batch, expectDiff54, data);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 提交事务
    TransCommit(g_conn_sync);

    for (int i = 0; i < tableNum - existTableNum; i++) {
        sprintf(labelName, "T%d", i);
        ret = GmcDropVertexLabelAsync(g_stmt_sync, labelName, drop_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, userData.status);
        memset(&userData, 0, sizeof(AsyncUserDataT));
    }

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_sync, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 超大规格数据写 获取diff
 * Input
 :1.客户端操作YANG表，循环创建namespace并写入数据，删除namespace并重新写入数据（升级表定义）再新增数据，直到写失败
                    2.对该表进行修改、删除数据，都成功
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_031)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表空间
    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 4;

    ret = GmcCreateTablespaceAsync(Yang_stmt_async, &tspCfg, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 写数据到超过表记录数 获取diff
    testYanginsertacltabelspacediff(Yang_conn_async);

    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcDropTablespaceAsync(Yang_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
void FetchDiff_callback_full(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    sleep(60);
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_TRUE(isEnd);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
void FetchDiff_callback_sleep(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    sleep(120);
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_TRUE(isEnd);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
/*****************************************************************************
 * Description  : 异步消息队列写满  获取diff
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_032)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    // 创建乐观事务
    TransStart(Yang_conn_async);
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2000; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchAddDML(batch, g_stmt_sync_8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch);
    int f = 0;
    int num = 0;
    // 获取diff
    AsyncUserDataT data = {0};
    data.stmt = g_stmt_sync_13;
    data.expectDiff = &expectDiff54;
    f++;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_13, NULL, FetchDiff_callback_full, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(ret, data.status);
    // 提交事务
    TransCommit(Yang_conn_async);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 事务savepoint超过上限 获取diff
 * Input        :1.客户端进程1操作vertext表，循环执行读写操作；客户端进程2操作YANG表
                2.客户端1循环执行执行六原语操作，创建saveponit达到上限，获取Diff成功，事务回滚成功。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_033)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 写一条数据创建一个savepoint 直到创建失败为止
    TransStart(Yang_conn_async);
    char savepointName[1024];
#if defined(RTOSV2X)
    uint32_t saveponitNumber = 200;
#else
    uint32_t saveponitNumber = 1024;
#endif
    for (int k = 0; k < saveponitNumber; k++) {
        memset(savepointName, 0, sizeof(savepointName));
        (void)snprintf(savepointName, 500, "spname%d", k);
        ret = GmcTransCreateSavepointAsync(Yang_conn_async, savepointName, TransSavePointCb, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        if (userData.status != GMERR_OK) {
            system("gmsysview  -q V\\$STORAGE_TRX_DETAIL");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d, savepoint num %d.", userData.status, k);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }

        if (k % 100 == 0) {
            AW_FUN_Log(LOG_INFO, "create %d savepoint ok.", k);
        }
    }
    AW_FUN_Log(LOG_INFO, "create %d savepoint ok.", saveponitNumber);
    // 提交事务
    TransCommit(Yang_conn_async);
    AW_FUN_Log(LOG_INFO, "commit trans.");
    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 表空间满 获取diff
 * Input        :1.客户端1建vertext表，写入部分数据。客户端2创建10张YANG表（包含边表）并写入数据到表空间满
                    2.客户端2删除部分数据，继续执行六原语操作和获取Diff成功。客户端1执行读写操作成功。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_034)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/start.sh ");
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建表空间
    const char *tablespaceName = "tsp1";
    GmcTspCfgT tspCfg;
    tspCfg.tablespaceName = tablespaceName;
    tspCfg.initSize = 0;
    tspCfg.stepSize = 0;
    tspCfg.maxSize = 4;

    ret = GmcCreateTablespaceAsync(Yang_stmt_async, &tspCfg, create_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.tablespaceName = tablespaceName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 表空间满 获取diff
    testYanginsertacltabelspacediff(Yang_conn_async);

    sleep(10);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();

    ret = GmcDropTablespaceAsync(Yang_stmt_async, tablespaceName, drop_tablespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 客户端epoll线程挂死  获取diff
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_035)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    // 创建乐观事务
    TransStart(Yang_conn_async);
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2000; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchAddDML(batch, g_stmt_sync_8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch);
    int f = 0;
    int num = 0;
    // 获取diff
    AsyncUserDataT data = {0};
    data.stmt = g_stmt_sync_13;
    data.expectDiff = &expectDiff54;
    f++;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_13, NULL, FetchDiff_callback_sleep, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(120);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(ret, data.status);
    // 提交事务
    TransCommit(Yang_conn_async);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 客户端epoll线程处理慢  获取diff
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_036)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    // 创建乐观事务
    TransStart(Yang_conn_async);
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2000; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchAddDML(batch, g_stmt_sync_8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch);
    int f = 0;
    int num = 0;
    // 获取diff
    AsyncUserDataT data = {0};
    data.stmt = g_stmt_sync_13;
    data.expectDiff = &expectDiff54;
    f++;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_13, NULL, FetchDiff_callback_full, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(ret, data.status);
    // 提交事务
    TransCommit(Yang_conn_async);
    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TestYangFreeAllstmt();
    sleep(5);
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
void *thr_yang001(void *args)
{
    system("./YangRel02 ");
}
void *thr_v5(void *args)
{
    system("./YangRel01 ");
}
/*****************************************************************************
 * Description  : 事务长时间不提交
 * Input        :1、启动4个客户端进程，进程1和2负责yang业务，进程3负责v5业务。
 yang典型配置下，进程1的yang连接开启“乐观+可重复读”事务，建表、编辑数据，然后事务一直保持不提交；
 进程2的yang连接，编辑同样的表数据，完成后事务立即提交。
 进程3使用悲观事务“读已提交”编辑v5表，然后事务提交。
 进程4事务“串行”事务，编辑数据，提交事务。
2、刚开始时，4个进程正常操作，无报错。进程1大量操作后，报错。其它进程报内存不足。
3、进程1事务abort后，能正常继续开启事务，编辑数据并获取Diff，其它进程恢复正常，能正常提交/回滚事务。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_037)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *a_conn_async = NULL;
    GmcStmtT *a_stmt_async = NULL;
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&a_conn_async, &a_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = g_namespace;
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(a_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(a_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    pthread_t thr_V5;
    pthread_t thr_yang01;
    pthread_t thr_yang02;
    // 写数据不提交事物 导致内存不足 事务状态为abort
    system("./YangRel03 ");

    // 重新开启新的进程 操作yang表 乐观 可重复读 提交事务 操作正常
    pthread_create(&thr_yang01, NULL, thr_yang001, NULL);
    // 并发 普通V5 vertex表 操作正常
    pthread_create(&thr_V5, NULL, thr_v5, NULL);
    pthread_join(thr_yang01, NULL);
    pthread_join(thr_V5, NULL);

    ret = GmcClearNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(a_conn_async, a_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
    AW_FUN_Log(LOG_STEP, "test end.\n");
    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
/*****************************************************************************
 * Description  :事务长时间不提交 yang事务一直不提交
 * Input        :1、启动4个客户端进程，进程1和2负责yang业务，进程3负责v5业务
 yang典型配置下，进程1的yang连接开启“乐观+可重复读”事务，建表、编辑数据，然后事务一直保持不提交，也不再编辑；
 进程2的yang连接，编辑同样的表数据，完成后事务立即提交。
 进程3使用悲观事务“读已提交”编辑v5表，然后事务提交。
2、进程正常，无报错。
3、进程1事务abort后，能正常继续开启事务，编辑数据并获取Diff，其它进程正常，能正常提交/回滚事务。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_038)
{

    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *a_conn_async = NULL;
    GmcStmtT *a_stmt_async = NULL;
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&a_conn_async, &a_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = g_namespace;
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(a_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(a_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    pthread_t thr_V5;
    pthread_t thr_yang01;
    pthread_t thr_yang02;
    // 写数据不提交事物 导致内存不足 事务状态为abort
    system("./YangRel03 ");

    // 重新开启新的进程 操作yang表 乐观 可重复读 提交事务 操作正常
    pthread_create(&thr_yang01, NULL, thr_yang001, NULL);
    // 并发 普通V5 vertex表 操作正常
    pthread_create(&thr_V5, NULL, thr_v5, NULL);
    pthread_join(thr_yang01, NULL);
    pthread_join(thr_V5, NULL);

    ret = GmcClearNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(a_conn_async, a_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
    AW_FUN_Log(LOG_STEP, "test end.\n");
    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
/*****************************************************************************
 Description  :事务长时间不提交 -- 表级别冲突
1、启动4个客户端进程，进程1和2负责yang业务，进程3负责v5业务
    yang典型配置下，进程1的yang连接开启“乐观+可重复读”事务，建表、编辑数据，然后事务一直保持不提交；
    进程2的yang连接，编辑同样的表数据，与进程1产生表级别冲突。
    进程3使用悲观事务“读已提交”编辑v5表，然后事务提交
2、刚开始时，4个进程正常操作，无报错。进程1大量操作后，报错。其它进程报内存不足。
3、进程1事务abort后，能正常回滚savepoint，回滚事务和继续开启事务，编辑数据并获取Diff，其它进程恢复正常，能正常提交/回滚事务。
 Author       : wk/wwx1038088
*****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_039)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *a_conn_async = NULL;
    GmcStmtT *a_stmt_async = NULL;
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&a_conn_async, &a_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = g_namespace;
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(a_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(a_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    pthread_t thr_V5;
    pthread_t thr_yang01;
    pthread_t thr_yang02;
    // 写数据不提交事物 导致内存不足 事务状态为abort
    system("./YangRel03 ");

    // 重新开启新的进程 操作yang表 乐观 可重复读 提交事务 操作正常
    pthread_create(&thr_yang02, NULL, thr_yang001, NULL);
    // 并发 普通V5 vertex表 操作正常
    pthread_create(&thr_V5, NULL, thr_v5, NULL);
    pthread_join(thr_yang02, NULL);
    pthread_join(thr_V5, NULL);

    ret = GmcClearNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(a_conn_async, a_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
    AW_FUN_Log(LOG_STEP, "test end.\n");
    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
/*****************************************************************************
 Description  :事务长时间不提交 -- savepoint
1、启动4个客户端进程，进程1和2负责yang业务，进程3负责v5业务
    yang典型配置下，进程1的yang连接开启“乐观+可重复读”事务，建表、编辑数据，然后事务一直保持不提交；
    进程2的yang连接，编辑不同的表数据，创建多个savepoint然后回滚。
    进程3使用悲观事务“读已提交”编辑v5表，然后事务提交。
2、刚开始时，3个进程正常操作，无报错。进程1大量操作后，报错。其它进程报内存不足。
3、进程1事务abort后，能正常回滚savepoint，提交事务和继续开启事务，编辑数据并获取Diff，其它进程恢复正常，能正常提交/回滚事务。
 Author       : wk/wwx1038088
*****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_040)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *a_conn_async = NULL;
    GmcStmtT *a_stmt_async = NULL;
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&a_conn_async, &a_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = g_namespace;
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(a_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(a_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    pthread_t thr_V5;
    pthread_t thr_yang01;
    pthread_t thr_yang02;
    // 写数据不提交事物 导致内存不足 事务状态为abort
    system("./YangRel03 ");

    // 重新开启新的进程 操作yang表 乐观 可重复读 提交事务 操作正常
    pthread_create(&thr_yang01, NULL, thr_yang001, NULL);
    // 并发 普通V5 vertex表 操作正常
    pthread_create(&thr_V5, NULL, thr_v5, NULL);
    pthread_join(thr_yang01, NULL);
    pthread_join(thr_V5, NULL);

    ret = GmcClearNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(a_conn_async, a_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
    AW_FUN_Log(LOG_STEP, "test end.\n");
    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
void *thread_reliability_while_creat_delete_Yangvertexlabel_exit(void *args)
{
    AsyncUserDataT YanguserData = {0};
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    YangConnOptionT connOptions = {0};
    int ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYangExit";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
    memset(&YanguserData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&YanguserData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
    memset(&YanguserData, 0, sizeof(AsyncUserDataT));
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
        ret = GmcCreateVertexLabelAsync(
            Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &YanguserData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&YanguserData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端已退出
        if ((YanguserData.status == GMERR_CONNECTION_RESET_BY_PEER) || (YanguserData.status == GMERR_INTERNAL_ERROR) ||
            (YanguserData.status == GMERR_REQUEST_TIME_OUT)) {

            // 断开链接
            ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
            // clear成功
            ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &YanguserData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&YanguserData);
            // 服务端异常退出
            if ((YanguserData.status == GMERR_CONNECTION_RESET_BY_PEER) ||
                (YanguserData.status == GMERR_INTERNAL_ERROR) || (YanguserData.status == GMERR_REQUEST_TIME_OUT)) {

                // 断开链接
                ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, YanguserData.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(&YanguserData, 0, sizeof(AsyncUserDataT));
        }
    }
}
void *thread_reliability_while_creat_delete_vertexlabel_exit(void *args)
{
    AsyncUserDataT userData = {0};
    char *vertexschema = NULL;
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    YangConnOptionT connOptions = {0};
    int ret = TestYangGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace2 = "NamespaceV5exit";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace2;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_PESSIMISITIC_TRX, GMC_TX_ISOLATION_COMMITTED};  // 可重复读+乐观
    ret = TryDropNameSpace(g_stmt_async, namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace2, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    while (1) {
        //.建表 建边
        readJanssonFile("SOHO_S380/1023_schema.gmjson", &vertexschema);
        ret = GmcCreateVertexLabelAsync(g_stmt_async, vertexschema, NULL, create_vertex_label_callback, &userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&userData);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 服务端已退出
        if ((userData.status == GMERR_CONNECTION_RESET_BY_PEER) || (userData.status == GMERR_INTERNAL_ERROR) ||
            (userData.status == GMERR_REQUEST_TIME_OUT)) {

            // 断开链接
            ret = testGmcDisconnect(g_conn_async, g_stmt_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            // clear成功
            ret = GmcClearNamespaceAsync(g_stmt_async, namespace2, drop_namespace_callback, &userData);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&userData);
            // 服务端异常退出
            if ((userData.status == GMERR_CONNECTION_RESET_BY_PEER) || (userData.status == GMERR_INTERNAL_ERROR) ||
                (userData.status == GMERR_REQUEST_TIME_OUT)) {

                // 断开链接
                ret = testGmcDisconnect(g_conn_async, g_stmt_async);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                break;
            }
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            memset(&userData, 0, sizeof(AsyncUserDataT));
        }
    }
}
/*****************************************************************************
 * Description  : 进程异常退出
 * Input        : 1.客户端进程1操作vertext表，循环删表建表和删除操作；客户端进程2操作YANG表，循环创建YANG表和删除操作
                2.服务端异常退出
                3.客户端收到异常通知
                4.客户端关闭连接资源，正常
                5.服务端恢复后，客户端1和2连接正常，操作正常进行
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, DB1;
    // 循环创建删除yang表
    pthread_create(&DB, NULL, thread_reliability_while_creat_delete_Yangvertexlabel_exit, NULL);
    // 循环创建删除普通表
    pthread_create(&DB1, NULL, thread_reliability_while_creat_delete_vertexlabel_exit, NULL);
    // 服务端退出
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 进程重复启动
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, DB1;
    // 循环创建删除yang表
    pthread_create(&DB, NULL, thread_reliability_while_creat_delete_Yangvertexlabel_exit, NULL);
    // 循环创建删除普通表
    pthread_create(&DB1, NULL, thread_reliability_while_creat_delete_vertexlabel_exit, NULL);
    // 服务端退出
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);
    // 重新拉起服务成功
    system("sh $TEST_HOME/tools/start.sh ");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
}
/*****************************************************************************
 * Description  : 事务锁资源耗尽
 * Input        :客户端1开启悲观事务（RC），异步操作530张vertext表，顺序对每张表写入200条数据、主键读和删除操作；
循环2W次；操作到一半，报事务锁获取失败，事务自动回滚。循环操作100次。
客户端2开启乐观事务（RR），异步操作300张YANG表（包含边表），全量读，回滚事务，再次开启乐观事务，
对全部的每一张表都写入200条数据，并执行namespace全量删除，提交事务。均成功。循环操作100次。
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_009)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"trxMonitorEnable=0\"");
    system("sh $TEST_HOME/tools/start.sh ");
    GmcConnT *g_conn_sync = NULL;
    GmcStmtT *g_stmt_sync = NULL;
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    char *schema = NULL, *schema1 = NULL, *schema2 = NULL;
    int tableNum = 2000;
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    GmcConnT *a_conn_async = NULL;
    GmcStmtT *a_stmt_async = NULL;
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    ret = TestYangGmcConnect(&a_conn_async, &a_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *namespace1 = g_namespace;
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(a_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(a_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    system("./YangRel01 ");
    system("./YangRel02 ");

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(a_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 断连
    ret = testGmcDisconnect(a_conn_async, a_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 停止服务
    if (g_envType != 2) {
        TestStopServer();
    }
    AW_FUN_Log(LOG_STEP, "test end.\n");
    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}
void *thread_reliability_while_subtree_exit(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = 0;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYangSubtreeExit";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    EXPECT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    EXPECT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    // 创建乐观事务
    TransStart(Yang_conn_async);
    // 使用接口完成subtree 查询
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(Yang_stmt_async, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(Yang_stmt_async, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(root, "ip-pools", GMC_OPERATION_SUBTREE_FILTER, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    ret = GmcYangEditChildNode(ippoolsNode, g_vertexLabel8, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel8Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-type
    GmcNodeT *applytypeNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_SUBTREE_FILTER, &applytypeNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 apply-ip
    GmcNodeT *applyipNode = NULL;
    ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_SUBTREE_FILTER, &applyipNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ipaddrs
    GmcNodeT *ipaddrsNode = NULL;
    ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_SUBTREE_FILTER, &ipaddrsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(ipaddrsNode, g_vertexLabel9, GMC_OPERATION_SUBTREE_FILTER, &vertexLabel9Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char namevalue1[10];
    memset(namevalue1, 0, sizeof(namevalue1));
    (void)snprintf(namevalue1, 10, "address%d", 0);
    ret = testsubtreeSetvalue(vertexLabel9Node, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "address",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/aclipaddr.json", &suntreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = suntreeReturnJson;
    // subtree查询 服务端异常退出
    while (1) {
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam01 param = {
            .step = 0,
            .stmt = Yang_stmt_async,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };

        // 判断 服务端是否退出
        if (g_server_exit == true) {
            param.expectStatus = GMERR_REQUEST_TIME_OUT;
        } else {
            param.expectStatus = GMERR_OK;
        }

        ret = GmcYangSubtreeFilterExecuteAsync(Yang_stmt_async, &filters, NULL, AsyncFetchRetCb01, &param);
        if (g_server_exit == true) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_RESET_BY_PEER, ret);
            param.step = 1;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = testWaitAsyncSubtreeRecv_APP(&param);
        if (param.step == 1) {
            free(suntreeReturnJson);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}
/*****************************************************************************
 * Description  : 进程异常退出 subtree查询
 * Input        :1.客户端进程1操作vertext表，循环执行读写操作；客户端进程2操作YANG表，循环执行subtree查询
                2.服务端异常退出
                3.客户端收到异常通知
                4.客户端关闭连接资源，正常
                5.服务端恢复后，客户端1和2连接正常，操作正常进行
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, DB1;
    pthread_create(&DB, NULL, thread_reliability_while_subtree_exit, NULL);
    // 循环创建删除普通表
    pthread_create(&DB1, NULL, thread_reliability_while_creat_delete_vertexlabel_exit, NULL);
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testEnvClean();
}
/*****************************************************************************
 * Description  : 进程重复启动 subtree查询
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB, DB1;
    // 循环subtree查询
    pthread_create(&DB, NULL, thread_reliability_while_subtree_exit, NULL);
    // 循环创建删除普通表
    pthread_create(&DB1, NULL, thread_reliability_while_creat_delete_vertexlabel_exit, NULL);
    // 杀掉服务
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(FLT, NULL);
    pthread_join(DB, NULL);
    pthread_join(DB1, NULL);
    // 重新拉起服务
    system("sh $TEST_HOME/tools/start.sh ");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
void *thread_reliability_while_diff_exit(void *args)
{
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    int ret = 0;
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYangdiffexit";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    EXPECT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    EXPECT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    // 创建乐观事务
    TransStart(Yang_conn_async);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel16Node = NULL;
    ret = testBatchPrepareAndSetDiff(Yang_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int i = 300;
    while (1) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel16, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel16Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_1, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "contentid%d", i);
        ret = testYangSetField(g_stmt_sync_1, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "content-id",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
        if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = testWaitAsyncRecv(&userData);
        if (userData.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        }
        i++;
        // 获取diff
        AsyncUserDataT data = {0};
        data.stmt = g_stmt_sync_1;
        data.expectDiff = &expectDiff54;
        ret = GmcYangFetchDiffExecuteAsync(g_stmt_sync_1, NULL, FetchDiff_callback, &data);
        if (ret != GMERR_OK) {
            testGmcGetLastError(NULL);
        }
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_REQUEST_TIME_OUT || userData.status == GMERR_CONNECTION_RESET_BY_PEER) {
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    }
    GmcTransRollBackAsync(Yang_conn_async, trans_rollback_callback, NULL);
}
/*****************************************************************************
 * Description  : 进程异常退出  获取diff
 * Input        : 1.客户端进程1操作vertext表，循环执行读写操作；客户端进程2操作YANG表，循环执行六原语操作，获取Diff
                2.服务端异常退出
                3.客户端收到异常通知
                4.客户端关闭连接资源，正常
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    pthread_create(&DB, NULL, thread_reliability_while_diff_exit, NULL);
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(DB, NULL);
    pthread_join(FLT, NULL);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  : 进程重复启动 获取diff
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t FLT, DB;
    // 循环建表 写数据 获取diff 杀掉服务后获取异常错误码通知 并退出
    pthread_create(&DB, NULL, thread_reliability_while_diff_exit, NULL);
    // 杀掉服务
    pthread_create(&FLT, NULL, thread_rProc_k, NULL);
    pthread_join(DB, NULL);
    pthread_join(FLT, NULL);
    // 重新拉起服务
    system("sh $TEST_HOME/tools/start.sh ");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/*****************************************************************************
 * Description  :并发gmsysview namespace查询
 * Input        :
 * Output       : None
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(Yang_reliability, Yang_reliability_041)
{
    AW_FUN_Log(LOG_STEP, "test start");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh ");
    const char *g_labelconfig = "{\"max_record_count\" : 100000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
                                "\"yang_model\":1}";
    char *Yang_vertexschema = NULL, *Yang_edgeschema = NULL;
    int ret = testEnvInit();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnect(&Yang_conn_async, &Yang_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *namespace1 = "NamespaceYang";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = TryDropNameSpace(Yang_stmt_async, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateNamespaceWithCfgAsync(Yang_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(Yang_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    //.建表 建边
    readJanssonFile("SOHO_S380/SOHO_S380_VertexLabel.json", &Yang_vertexschema);
    ASSERT_NE((void *)NULL, Yang_vertexschema);
    ret = GmcCreateVertexLabelAsync(
        Yang_stmt_async, Yang_vertexschema, g_labelconfig, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_vertexschema);
    readJanssonFile("SOHO_S380/SOHO_S380_EdgeLabel.json", &Yang_edgeschema);
    ASSERT_NE((void *)NULL, Yang_edgeschema);
    ret =
        GmcCreateEdgeLabelAsync(Yang_stmt_async, Yang_edgeschema, g_labelconfig, create_edge_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(Yang_edgeschema);
    TestYangAllocAllstmt();
    testYanginsertacl(Yang_conn_async);
    pthread_t thr_arr_subtree[800];
    int i = 0;
    for (i = 0; i < 800; i++) {
        pthread_create(&thr_arr_subtree[i], NULL, thr_arr_Subtree, NULL);
    }
    for (i = 0; i < 800; i++) {
        pthread_join(thr_arr_subtree[i], NULL);
    }

    // clear成功
    ret = GmcClearNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(Yang_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(Yang_conn_async, Yang_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
