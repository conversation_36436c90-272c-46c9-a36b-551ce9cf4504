/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构降级头文件
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/10/17
**************************************************************************** */
#ifndef VERTEXLABEL_DOWNGRADE_H
#define VERTEXLABEL_DOWNGRADE_H

#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;
char *g_labelName = (char *)"simpleLabel";
char *g_labelConfig = NULL;
char g_uWay[64] = "online";
char g_dWay[64] = "sync";
typedef struct TagSimplelabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    GmcOperationTypeE optType;      // vertex操作类型
} GtSimplelabelCfgT;

typedef struct TaglabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;  // 预期的affectRows
    bool fieldIsNull[8];    // 新增版本的字段是否空值
} GtSimplelabelCfgRead;

int TestGetAffactRows(GmcStmtT *stmt, int32_t expectValue)
{
    int32_t affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectValue, affectRows);
    return expectValue == affectRows ? GMERR_OK : 1;
}

int TestCreateLabel(GmcStmtT *stmt, char *schemaPath, char *labelName, char const *configJson = g_labelConfig)
{
    int ret = 0;
    char *testSchema = NULL;

    if (schemaPath) {
        readJanssonFile(schemaPath, &testSchema);
        EXPECT_NE((void *)NULL, testSchema);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, testSchema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret == GMERR_OK) {
        AW_FUN_Log(LOG_INFO, "[INFO]Test create label %s success \n", labelName);
    } else {
        testGmcGetLastError(NULL);
    }
    if (testSchema) {
        free(testSchema);
        testSchema = NULL;
    }
    return ret;
}

int TestUpdateVertexLabel(char *schemaPath, char *expectValue, char *labelName = NULL, char *uWay = g_uWay,
                          char *nsName = g_testNameSpace)
{
    // gmddl工具升级表操作
    char cmd[512] = {0};
    int ret = 0;
    if (labelName) {
        char *schema = NULL;
        readJanssonFile(schemaPath, &schema);
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -f %s -u %s -ns %s", g_toolPath, labelName, schemaPath, uWay,
            nsName);
    } else {
        (void)snprintf(cmd, 512, "%s/gmddl -c alter -f %s -u %s -ns %s", g_toolPath, schemaPath, uWay, nsName);
    }
    AW_FUN_Log(LOG_INFO, "cmd: %s\n", cmd);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}

int TestDownGradeVertexLabel(char *labelName, uint32_t schemaVersion, char *expectValue, char *dWay = g_dWay,
                             char *nsName = g_testNameSpace)
{
    char cmd[512] = {0};
    int ret = 0;
    (void)snprintf(cmd, 512, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s", g_toolPath, labelName, schemaVersion, dWay,
                   nsName);
    ret = executeCommand(cmd, expectValue);
    if (ret != GMERR_OK) {
        system(cmd);
    }
    return ret;
}


int TestParepareStmtByLabelName(GmcStmtT *stmt, const char *vertexLabelName, GmcOperationTypeE operationType,
    uint32_t versionId = 0xffffffff)
{
    int ret = 0;
    if (versionId == 0xffffffff) {
        ret = testGmcPrepareStmtByLabelName(stmt, vertexLabelName, operationType);
    } else {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, vertexLabelName, versionId, operationType);
    }
    return ret;
}

/**-------------------简单表-----------------------**/
#define SIMPLE_LABEL_FIXED_SIZE   9
#define SIMPLE_LABEL2_FIXED_SIZE  8
#define SIMPLE_LABEL2_BITMAP_SIZE 8
#define MAX_MASK_LEN_16 1000
#define MAX_MASK_LEN_24 2501000
#define LABEL_BIG_OBJ_FIXED_SIZE   13312
#define SIMPLE_LABEL_ADD_FIXED_SIZE 1331

void TestSimpleT1SetPk(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetVertexProperty(stmt, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1OldVersionSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefaultValue) {
        uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        uint8_t f9Value = i % 31;
        uint16_t f10Value = i % 1023;
        ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1UpdateSetProperty(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcSetVertexProperty(stmt, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewFieldSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f16 = value;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f17 = value;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f22 = value;
    ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f28 = value;
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f29 = value;
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f30 = value;
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = GmcSetVertexProperty(stmt, "F31", GMC_DATATYPE_BITMAP, &f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT3FullFieldUpdateSetOk(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    uint64_t i = value;
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    memset(f32, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
    f32[0] = 65;
    f32[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = i % 256;
    ret = GmcSetVertexProperty(stmt, "F32", GMC_DATATYPE_FIXED, f32, sizeof(f32));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT2NewFieldSetFailed(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15 = value;
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16 = value;
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17 = value;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22 = value;
    ret = GmcSetVertexProperty(stmt, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = GmcSetVertexProperty(stmt, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcSetVertexProperty(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcSetVertexProperty(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcSetVertexProperty(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcSetVertexProperty(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f28 = value;
    ret = GmcSetVertexProperty(stmt, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f29 = value;
    ret = GmcSetVertexProperty(stmt, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    double f30 = value;
    ret = GmcSetVertexProperty(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = GmcSetVertexProperty(stmt, "F31", GMC_DATATYPE_BITMAP, &f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT3FullFieldUpdateSetFailed(GmcStmtT *stmt, int64_t i)
{
    uint8_t f32Value[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F32", GMC_DATATYPE_FIXED, f32Value, sizeof(f32Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT1NewFieldSetFailed(GmcStmtT *stmt, int64_t i)
{
    int64_t f14Value = i;
    int32_t ret = GmcSetVertexProperty(stmt, (char *)"F14", GMC_DATATYPE_INT64, &f14Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT1LpmIndexSet(GmcStmtT *stmt, int64_t value)
{
    int ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    int64_t uiVrIndex = value;
    ret = GmcSetIndexKeyName(stmt, "lpm4_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (uiVrIndex <= MAX_MASK_LEN_16) {
        destIpAddr = ((uiVrIndex + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (uiVrIndex > MAX_MASK_LEN_16 && uiVrIndex <= MAX_MASK_LEN_24) {
        destIpAddr = ((uiVrIndex + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((uiVrIndex + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1SetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int32_t ret = 0;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    ret = GmcSetVertexProperty(stmt, (char *)"F3", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, (char *)"F11", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcSetVertexProperty(stmt, (char *)"F12", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalhashIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int16_t f4Value = i;
    uint16_t f5Value = i;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1HashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t f3Value = 0;
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSimpleT1LocalIndexRangeSet(GmcStmtT *stmt, int64_t startValue, int64_t endValue)
{
    int ret = 0;
    unsigned int arrLen = 1;
    uint32_t lValue = startValue;
    uint32_t rValue = endValue;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "leftKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    leftKeyProps[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps[0].value = &lValue;
    leftKeyProps[0].size = sizeof(uint32_t);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps == NULL) {
        AW_FUN_Log(LOG_ERROR, "rightKeyProps is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    rightKeyProps[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps[0].value = &rValue;
    rightKeyProps[0].size = sizeof(uint32_t);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = GmcSetKeyRange(stmt, items, arrLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(leftKeyProps);
    free(rightKeyProps);
}

void TestSimpleT1GetOldPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestSimpleT2NewVersionGetNewValue(GmcStmtT *stmt, int64_t value, bool fieldIsNull = false)
{
    if (!fieldIsNull) {
        int64_t f14 = value;
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value;
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value;
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value;
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value) & 0x7fff;
        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value) & 0xffff;
        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value) & 0x7f;
        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value) & 0xff;
        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value;
        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
        for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
            f23[j] = j;
        }
        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value) & 0x1f;
        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = (value) & 0x3ff;
        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = (value) & 0x1ffff;
        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = (value) & 0x1ffffffff;
        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = value;
        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value;
        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value;
        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0xff};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0x00};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT2NewVersionGetFaild(GmcStmtT *stmt, int64_t value)
{
    int64_t f14 = value;
    int32_t ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_INT64, &f14);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15 = value;
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_UINT64, &f15);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16 = value;
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_INT32, &f16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f17 = value;
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &f17);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_INT16, &f18);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_UINT16, &f19);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int8_t f20 = (value) & 0x7f;
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_INT8, &f20);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f21 = (value) & 0xff;
    ret = queryPropertyAndCompare(stmt, "F21", GMC_DATATYPE_UINT8, &f21);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f22 = value;
    ret = queryPropertyAndCompare(stmt, "F22", GMC_DATATYPE_TIME, &f22);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f23[SIMPLE_LABEL2_FIXED_SIZE] = {0};
    for (int j = 0; j < SIMPLE_LABEL2_FIXED_SIZE; j++) {
        f23[j] = j;
    }
    ret = queryPropertyAndCompare(stmt, "F23", GMC_DATATYPE_FIXED, f23);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = queryPropertyAndCompare(stmt, "F24", GMC_DATATYPE_BITFIELD8, &f24);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = queryPropertyAndCompare(stmt, "F25", GMC_DATATYPE_BITFIELD16, &f25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = queryPropertyAndCompare(stmt, "F26", GMC_DATATYPE_BITFIELD32, &f26);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = queryPropertyAndCompare(stmt, "F27", GMC_DATATYPE_BITFIELD64, &f27);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    bool f28 = value;
    ret = queryPropertyAndCompare(stmt, "F28", GMC_DATATYPE_BOOL, &f28);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    float f29 = value;
    ret = queryPropertyAndCompare(stmt, "F29", GMC_DATATYPE_FLOAT, &f29);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    double f30 = value;
    ret = queryPropertyAndCompare(stmt, "F30", GMC_DATATYPE_DOUBLE, &f30);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = queryPropertyAndCompare(stmt, "F31", GMC_DATATYPE_BITMAP, f31Bits);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT3FullfieldsUpdateGetNewValue(GmcStmtT *stmt, int64_t value, bool filedIsNull = false)
{
    int ret = GMERR_OK;
    if (!filedIsNull) {
        uint64_t i = value;
        uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
        memset(f32, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
        f32[0] = 65;
        f32[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = i % 256;
        ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, f32);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleT3FullfieldsUpdateGetFailed(GmcStmtT *stmt, int64_t value)
{
    int ret = GMERR_OK;
    bool isNull = false;
    uint64_t i = value;
    uint8_t f32[SIMPLE_LABEL_ADD_FIXED_SIZE] = {0};
    (void)snprintf((char *)f32, sizeof(f32), "f%01329d", i);
    ret = queryPropertyAndCompare(stmt, "F32", GMC_DATATYPE_FIXED, f32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcGetVertexPropertyByName(stmt, "F32", f32, SIMPLE_LABEL_ADD_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSimpleT1UpdateGetOldPropertyByName(GmcStmtT *stmt, int64_t i, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
}

void TestSimpleT1GetLpmProperty(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcGetVertexPropertyByName(stmt, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestSimpleT1NewVersionGetNewValue(GmcStmtT *stmt, int64_t i)
{
    uint64_t f14Value = i;
    uint64_t f14ValueR = 0;
    bool isNull = false;
    int32_t ret = GmcGetVertexPropertyByName(stmt, (char *)"F14", &f14ValueR, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f14Value, f14ValueR);
}

void TestSimpleT2Delete(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, uint32_t keyId,
                        uint32_t schemaVersion, int64_t updateValue = 0)
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        if (keyId == 0) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexRangeSet(stmt, i, endValue);
            ret = GmcExecute(stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            break;
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "i=%d\n", i);
        }
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleTNewOldVersionWrite(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                   bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            TestSimpleT1SetPk(stmt, i);
        }
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion >= 3) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSimpleTNewOldVersionWrite2(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion,
                                    bool isDefaultValue = true)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            TestSimpleT1SetPk(stmt, i);
        }
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion >= 3) {
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

int TestSimpleTNewOldVersionUpdate(GmcStmtT *stmt, GtSimplelabelCfgT vertexCfg, uint32_t schemaVersion, uint32_t keyId,
                                   bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    GmcOperationTypeE optType = vertexCfg.optType;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, optType);
    AW_FUN_Log(LOG_INFO, "update: labelName: %s schemaVersion: %d\n", g_labelName, schemaVersion);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (optType == GMC_OPERATION_MERGE) {
            TestSimpleT1PkIndexSet(stmt, i);
        } else {
            if (keyId == 0) {
            TestSimpleT1PkIndexSet(stmt, i);
            } else if (keyId == 1) {
                TestSimpleT1HashclusterIndexSet(stmt, i);
            } else if (keyId == 2) {
                TestSimpleT1LocalhashIndexSet(stmt, i);
            } else if (keyId == 3) {
                TestSimpleT1LocalIndexSet(stmt, i);
            } else if (keyId == 4) {
                TestSimpleT1LpmIndexSet(stmt, i);
            } else {
                AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
            }
        }
        TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i + updateValue);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        int ret1 = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
    }
    return 0;
}


int TestSimpleTNewOldVersionRead(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion, uint32_t keyId,
                                 bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d line: %d", g_labelName, schemaVersion, __LINE__);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < endValue) {
                    newFieldIsNull[0] = fieldIsNull[1];
                    newFieldIsNull[1] = fieldIsNull[2];
                } else {
                    if (f0Value >= 0 && f0Value < endValue || f0Value >= endValue * 3 && f0Value < endValue * 4) {
                        newFieldIsNull[0] = true;
                        newFieldIsNull[1] = true;
                    } else if (f0Value >= endValue && f0Value < endValue * 2 || f0Value >= endValue * 4 &&
                               f0Value < endValue * 5) {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = true;
                    } else {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = false;
                    }
                }
                if (f0Value >= startPkVal && f0Value < endValue) {
                    TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
                    TestSimpleT1GetLpmProperty(stmt, f0Value);
                    if (schemaVersion == 0) {
                        TestSimpleT2NewVersionGetFaild(stmt, f0Value + updateValue);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
                    } else if (schemaVersion == 2) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
                    } else if (schemaVersion >= 3) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value + updateValue, newFieldIsNull[1]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 0) {
                TestSimpleT2NewVersionGetFaild(stmt, i + updateValue);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue);
            } else if (schemaVersion >= 3) {
                TestSimpleT2NewVersionGetNewValue(stmt, i + updateValue, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i + updateValue, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return 0;
}

// 旧版本更新新版本写入的数据，新版本的字段不更新
int TestSimpleTNewOldVersionRead2(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion,
                                  uint32_t keyId, bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t coefficient = vertexCfg.coefficient;
    bool fieldIsNull[8] = {0};
    int32_t localFlag = 0;
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d line: %d", g_labelName, schemaVersion, __LINE__);
    RETURN_IFERR(ret);
    for (int i = startPkVal; i < endValue; i++) {
        if (keyId == 0) {
        TestSimpleT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestSimpleT1HashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestSimpleT1LocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestSimpleT1LocalIndexSet(stmt, i);
        } else if (keyId == 4) {
            TestSimpleT1LpmIndexSet(stmt, i);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        if (keyId == 3 && localFlag == 0) {
            localFlag = 1;
            int64_t f0Value = 0;
            bool isNull = false;
            bool newFieldIsNull[2] = {true};
            while (!isFinish) {
                fetchNum++;
                ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                RETURN_IFERR(ret);
                if (f0Value >= startPkVal && f0Value < endValue) {
                    newFieldIsNull[0] = fieldIsNull[1];
                    newFieldIsNull[1] = fieldIsNull[2];
                } else {
                    if (f0Value >= 0 && f0Value < endValue || f0Value >= endValue * 3 && f0Value < endValue * 4) {
                        newFieldIsNull[0] = true;
                        newFieldIsNull[1] = true;
                    } else if (f0Value >= endValue && f0Value < endValue * 2 || f0Value >= endValue * 4 &&
                               f0Value < endValue * 5) {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = true;
                    } else {
                        newFieldIsNull[0] = false;
                        newFieldIsNull[1] = false;
                    }
                }
                if (f0Value >= startPkVal && f0Value < endValue) {
                    TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
                    TestSimpleT1GetLpmProperty(stmt, f0Value);
                    if (schemaVersion == 0) {
                        TestSimpleT2NewVersionGetFaild(stmt, f0Value);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
                    } else if (schemaVersion == 2) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
                    } else if (schemaVersion >= 3) {
                        TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
                        TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value, newFieldIsNull[1]);
                    }
                }
                ret = GmcFetch(stmt, &isFinish);
                RETURN_IFERR(ret);
            }
            AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
            fetchNum = 0;
            return 0;
        } else if (keyId != 3) {
            TestSimpleT1UpdateGetOldPropertyByName(stmt, i + updateValue, isDefaultValue);
            TestSimpleT1GetLpmProperty(stmt, i);
            if (schemaVersion == 0) {
                TestSimpleT2NewVersionGetFaild(stmt, i + updateValue);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                AW_FUN_Log(LOG_INFO, "F0Value = %d line: %d\n", i, __LINE__);
                TestSimpleT2NewVersionGetNewValue(stmt, i, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetFailed(stmt, i);
            } else if (schemaVersion >= 3) {
                TestSimpleT2NewVersionGetNewValue(stmt, i, fieldIsNull[1]);
                TestSimpleT3FullfieldsUpdateGetNewValue(stmt, i, fieldIsNull[2]);
            }
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(true, isFinish);
            return ret;
        }
    }
    return 0;
}

int TestSimpleTWholeRead(GmcStmtT *stmt, GtSimplelabelCfgRead vertexCfg, uint32_t schemaVersion, bool isDefaultValue)
{
    int ret = 0;
    bool isFinish = false;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    bool fieldIsNull[8] = {0};
    uint32_t fetchNum = 0;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName = %s schemaVersion = %d", g_labelName, schemaVersion);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
        int64_t f0Value = 0;
        bool isNull = false;
        bool newFieldIsNull[2] = {true};
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        RETURN_IFERR(ret);
        if (f0Value >= 0 && f0Value < expAffectRows / 3) {
            newFieldIsNull[0] = true;
            newFieldIsNull[1] = true;
        } else if (f0Value >= (expAffectRows / 3) && f0Value < (expAffectRows / 3 * 2)) {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = true;
        } else {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = false;
        }
        TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, f0Value);
        if (schemaVersion == 0) {
            TestSimpleT2NewVersionGetFaild(stmt, f0Value);
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value, newFieldIsNull[0]);
            TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value, newFieldIsNull[1]);
        }
    }
    return GMERR_OK;
}

int CheckAccountStatus(GmcStmtT *stmt, const char *labelName)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo = NULL;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(checkStatus, GMC_CHECK_STATUS_NORMAL);
    return ret;
}

void TestSimpleT1InsertOrReplaceBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t startValue,
                                      int64_t endValue, uint32_t schemaVersion, GmcOperationTypeE operationType,
                                      bool isDefaultValue = true)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = endValue - startValue;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1SetPk(stmt, i);
        TestSimpleT1OldVersionSetProperty(stmt, i, isDefaultValue);
        if (schemaVersion == 0) {
            TestSimpleT2NewFieldSetFailed(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetFailed(stmt, i);
        } else if (schemaVersion == 3) {
            TestSimpleT2NewFieldSetOk(stmt, i);
            TestSimpleT3FullFieldUpdateSetOk(stmt, i);
        }
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
    AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
    AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
    GmcBatchDestroy(batch);
}

void TestSimpleT1MergeOrUpdateBatch(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int64_t startValue,
                                    int64_t endValue, uint32_t schemaVersion, GmcOperationTypeE operationType,
                                    bool isDefaultValue = true, int64_t updateValue = 0)
{
    int ret;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    unsigned int opNum = endValue - startValue;

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, "labelName = %s schemaVersion = %d line: %d\n", labelName, schemaVersion, __LINE__);
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    if (operationType == GMC_OPERATION_MERGE) {
        for (int i = startValue; i < endValue; i++) {
            TestSimpleT1PkIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);

            if (schemaVersion == 0) {
                AW_FUN_Log(LOG_INFO, "schemaVersion = %d i: %d line: %d\n", schemaVersion, i, __LINE__);
                TestSimpleT2NewFieldSetFailed(stmt, i + updateValue);
                TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
                TestSimpleT3FullFieldUpdateSetFailed(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
                TestSimpleT3FullFieldUpdateSetOk(stmt, i + updateValue);
            }

            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecute(batch, &batchRet);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_INFO, "startValue: %d schemaVersion: %d\n", startValue, schemaVersion);
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
        AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        GmcBatchDestroy(batch);
    } else {
            for (int i = startValue; i < endValue; i++) {
            TestSimpleT1HashclusterIndexSet(stmt, i);
            TestSimpleT1UpdateSetProperty(stmt, i + updateValue, isDefaultValue);
            if (schemaVersion == 0) {
                TestSimpleT1NewFieldSetFailed(stmt, i);
            } else if (schemaVersion == 2) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
            } else if (schemaVersion == 3) {
                TestSimpleT2NewFieldSetOk(stmt, i + updateValue);
                TestSimpleT3FullFieldUpdateSetOk(stmt, i + updateValue);
            }

            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(opNum, totalNum);
        AW_MACRO_EXPECT_EQ_INT(opNum, successNum);
        AW_FUN_Log(LOG_INFO, "successNum: %d\n", successNum);
        AW_FUN_Log(LOG_INFO, "totalNum: %d\n", totalNum);
        GmcBatchDestroy(batch);
    }
}

void TestSimpleT1LocalScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
                           uint32_t schemaVersion, int32_t *fetchNum, bool isDefaultValue = true,
                           int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestSimpleT1LocalIndexRangeSet(stmt, startValue, endValue);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        (*fetchNum)++;
        ret = GmcGetVertexPropertyByName(stmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestSimpleT1UpdateGetOldPropertyByName(stmt, f0Value + updateValue, isDefaultValue);
        TestSimpleT1GetLpmProperty(stmt, f0Value);
        if (schemaVersion == 0) {
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
        } else if (schemaVersion == 2) {
            TestSimpleT2NewVersionGetNewValue(stmt, f0Value + updateValue);
            TestSimpleT3FullfieldsUpdateGetFailed(stmt, f0Value + updateValue);
        } else if (schemaVersion == 3) {
            TestSimpleT3FullfieldsUpdateGetNewValue(stmt, f0Value + updateValue);
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void SubSimpleTCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 30;
    int updateValue = 500;
    char keyName[128] = {0};
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                         // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value + updateValue, true);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value + updateValue, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = false;
                            }
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSimpleT1UpdateGetOldPropertyByName(subStmt, f0Value, false);
                            TestSimpleT1GetLpmProperty(subStmt, f0Value);
                            TestSimpleT2NewVersionGetNewValue(subStmt, f0Value, newFieldIsNull[0]);
                            TestSimpleT3FullfieldsUpdateGetFailed(subStmt, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

/*-------------------------------------特殊复杂表-----------------------------------*/
typedef struct TagSpeciallabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t coefficient;    // 字段值生成系数, 通过startVal和coefficient组合生成不同的整形和浮点型字段值
    int32_t expAffectRows;    // 预期的affectRows
    int32_t threadId;    // 线程Id
    uint16_t t1VCount;
    uint16_t t2VCount;
    uint32_t schemaVersion;
    GmcOperationTypeE optType;
    bool fieldIsNull[8];
} GtSpeciallabelCfgT;

#define STRING_LEN   16
#define BYTES_LEN    256
#define STRING2_LEN   (13 * 1024)
#define STRING3_LEN   (32 * 1024)
char *g_labelName2 = (char *)"specialLabel";

void GtSpeciallabel2GetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    ret = GmcNodeGetChild(Root, "T1V", &t1);
    if (ret == GMERR_NO_DATA) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    *root = Root;
    *t1V = t1;
}

void TestSpecialT2UpdateSetOldProperty(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i% 65536;
    uint64_t f7Value = i;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value;
    uint16_t f10Value;
    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, fixedValue, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_BITFIELD8, &f13Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f14[BYTES_LEN] = {0};
    (void)snprintf((char *)f14, BYTES_LEN, "%s", bytesValue);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_BYTES, f14, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT2UpdateSetNewProperty(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3UpdateSetNewProperty(GmcNodeT *node, int64_t value, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", value);
    ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F22", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F23", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F24", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F25", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(stringTest);
}


void TestSpecialT3UpdateSetMidProperty(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3UpdateSetNewestProperty(GmcNodeT *node, int64_t value)
{
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", value);
    int32_t ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F22", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F23", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F24", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F25", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(stringTest);
}

void TestSpecialT3UpdateSetMidPropertyFailed(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    uint8_t f15[BYTES_LEN] = {0};
    (void)snprintf((char *)f15, BYTES_LEN, "%s", bytesValue);
    int32_t ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_BYTES, f15, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    uint8_t f16[STRING2_LEN] = {0};
    (void)snprintf((char *)f16, STRING2_LEN, "%s", stringValue);
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_STRING, f16, strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSpecialT3UpdateSetNewestPropertyFailed(GmcNodeT *node)
{
    char *stringTest = (char *)"test";
    int32_t ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_STRING, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F22", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F23", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F24", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, "F25", GMC_DATATYPE_BYTES, stringTest, strlen(stringTest));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}


void TestSpecialT3UpdateGetOldPropertyByName(GmcNodeT *node, int64_t i, char *bytesValue, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint64_t f1Value2 = i;
    int32_t f2Value2 = i;
    int16_t f4Value2 = i % 32768;
    uint16_t f5Value2 = i % 65536;
    uint64_t f7Value2 = i;

    uint8_t fixedValue[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9Value = 0;
    uint16_t f10Value = 0;
    uint8_t fixedValueR[SIMPLE_LABEL_FIXED_SIZE] = {0};
    uint8_t f9ValueR = 0;
    uint16_t f10ValueR = 0;
    uint8_t f13Value = i & 0xf;
    uint8_t f13ValueR = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    ASSERT_EQ(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    if (!isDefaultValue) {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = j;
        }
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        for (int j = 0; j < SIMPLE_LABEL_FIXED_SIZE; j++) {
            fixedValue[j] = 0xff;
        }
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", fixedValueR, SIMPLE_LABEL_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(fixedValue, fixedValueR, SIMPLE_LABEL_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f9Value, f9ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10ValueR, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10ValueR);
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13ValueR, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(f13Value, f13ValueR);
    ret = queryNodePropertyAndCompare(node, (char *)"F14", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestSpecialT3GetLpmProperty(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull = false;
    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = 0;
    uint32_t vrid2 = 0;
    uint32_t vrfIndex2 = 0;
    uint32_t destIpAddr2 = 0;
    uint8_t maskLen2 = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    if (i <= MAX_MASK_LEN_16) {
        destIpAddr = ((i + 2) << 16);
        maskLen = ((16) & 0xff);
    } else if (i > MAX_MASK_LEN_16 && i <= MAX_MASK_LEN_24) {
        destIpAddr = ((i + 2) << 8);
        maskLen = ((24) & 0xff);
    } else {
        destIpAddr = ((i + 2));
        maskLen = ((32) & 0xff);
    }
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &destIpAddr2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(destIpAddr, destIpAddr2);
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestSpecialT3MidVersionGetNewField(GmcNodeT *node, char *bytesValue, char *stringValue, bool fieldIsNull)
{
    if (!fieldIsNull) {
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, bytesValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F15", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F16", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSpecialT4GetNewField(GmcNodeT *node, int32_t v, bool fieldIsNull)
{
    char stringValue[265] = {0};
    (void)snprintf(stringValue, 265, "s%0255d", v);
    if (!fieldIsNull) {
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSpecialT4GetR1NodeField(GmcNodeT *node, int32_t v, bool boolValue, bool fieldIsNull)
{
    int32_t ret = 0;
    if (!fieldIsNull) {
        int64_t p14Value = v;
        ret = queryNodePropertyAndCompare(node, (char *)"P14", GMC_DATATYPE_INT64, &p14Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool p28 = v;
        ret = queryNodePropertyAndCompare(node, "P28", GMC_DATATYPE_BOOL, &boolValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        float p29 = v;
        ret = queryNodePropertyAndCompare(node, "P29", GMC_DATATYPE_FLOAT, &p29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        double p30 = v;
        ret = queryNodePropertyAndCompare(node, "P30", GMC_DATATYPE_DOUBLE, &p30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint8_t v3Bits[1] = {0x55};
        GmcBitMapT v3 = {0};
        v3.beginPos = 0;
        v3.endPos = 8 - 1;
        v3.bits = v3Bits;
        ret = queryNodePropertyAndCompare(node, (char *)"P31", GMC_DATATYPE_BITMAP, &v3Bits);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char stringValue[265] = {0};
        (void)snprintf(stringValue, 265, "b%0255d", v);
        ret = queryNodePropertyAndCompare(node, (char *)"P32", GMC_DATATYPE_BYTES, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        (void)snprintf(stringValue, 265, "s%0255d", v);
        ret = queryNodePropertyAndCompare(node, (char *)"P33", GMC_DATATYPE_STRING, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = queryNodePropertyAndCompare(node, (char *)"P14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, "P30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint8_t p31Bits[1] = {0x00};
        GmcBitMapT p31 = {0};
        p31.beginPos = 0;
        p31.endPos = 8 - 1;
        p31.bits = p31Bits;
        ret = queryNodePropertyAndCompare(node, (char *)"P31", GMC_DATATYPE_BITMAP, &p31Bits);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"P32", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"P33", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestSpecialT3MidVersionGetNewFieldFailed(GmcNodeT *node, char *bytesValue, char *stringValue)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char bytesV[BYTES_LEN] = {0};
    char stringV[BYTES_LEN] = {0};
    ret = GmcNodeGetPropertySizeByName(node, "F15", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F16", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F15", bytesV, strlen(bytesValue), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F16", stringV, strlen(stringValue), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestSpecialT3MostNewVersionGetNewFieldFailed(GmcNodeT *node, int64_t value)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }

    ret = GmcNodeGetPropertySizeByName(node, "F17", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F18", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F21", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F22", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F25", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F17", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F18", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F19", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F20", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F21", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F22", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F23", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F24", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F25", stringTest, STRING3_LEN, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    free(stringTest);
}

void TestSpecialT3MostNewVersionGetNewField(GmcNodeT *node, int64_t value, bool fieldIsNull)
{
    int64_t valueWrite = value;
    char *stringTest = (char *)malloc(STRING3_LEN);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }
    (void)snprintf((char *)stringTest, STRING3_LEN, "s%032766d", valueWrite);
    if (!fieldIsNull) {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F22", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F23", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F24", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F25", GMC_DATATYPE_BYTES, stringTest);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, (char *)"F17", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F18", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F19", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F20", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F21", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F22", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F23", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F24", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F25", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    free(stringTest);
}

void GtSpeciallabel3GeneralComparePropertyVector(GmcNodeT *node, int64_t value, char *stringValue)
{
    uint32_t v1Value = value;
    int32_t ret = queryNodePropertyAndCompare(node, (char *)"V1", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V2", GMC_DATATYPE_UINT32, &v1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, (char *)"V4", GMC_DATATYPE_STRING, stringValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t v3Bits[2] = {0x55, 0x55};
    GmcBitMapT v3 = {0};
    v3.beginPos = 0;
    v3.endPos = 8 - 1;
    v3.bits = v3Bits;
    ret = queryNodePropertyAndCompare(node, (char *)"V3", GMC_DATATYPE_BITMAP, &v3Bits);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GtSpeciallabel3GeneralGetVector(
    GmcNodeT *node, int64_t index, char *stringValue, uint16_t t1Count, uint16_t t2Count)
{
    int ret = 0, i = 0;
    GmcNodeT *t2V = NULL;
    for (i = 0; i < t1Count; ++i) {
        ret = GmcNodeGetElementByIndex(node, i, &node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GtSpeciallabel3GeneralComparePropertyVector(node, index, stringValue);
        ret = GmcNodeGetChild(node, "T2V", &t2V);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t k = 0; k < t2Count; k++) {
            ret = GmcNodeGetElementByIndex(t2V, k, &t2V);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GtSpeciallabel3GeneralComparePropertyVector(t2V, index, stringValue);
        }
    }
}

int TestSpecialTWholeRead(GmcStmtT *stmt, GtSpeciallabelCfgT vertexCfg, char *bytesValue, char *stringValue,
                          bool isDefaultValue)
{
    int ret = 0;
    bool isFinish = false;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t vertexCount = vertexCfg.count;
    int32_t coefficient = vertexCfg.coefficient;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    uint32_t fetchNum = 0;
    GmcNodeT *root, *t1V;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName2, schemaVersion, GMC_OPERATION_SCAN);
    AW_FUN_Log(LOG_INFO, "g_labelName2 = %s schemaVersion = %d", g_labelName2, schemaVersion);
    RETURN_IFERR(ret);
    ret = GmcExecute(stmt);
    RETURN_IFERR(ret);
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        fetchNum++;
        int64_t f0Value = 0;
        bool isNull = false;
        bool newFieldIsNull[2] = {true};
        GtSpeciallabel2GetNode(stmt, &root, &t1V);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        RETURN_IFERR(ret);
        if (f0Value >= 0 && f0Value < 10 || f0Value >= 30 && f0Value < 40) {
            newFieldIsNull[0] = true;
            newFieldIsNull[1] = true;
        } else if (f0Value >= 10 && f0Value < 20 || f0Value >= 40 && f0Value < 50) {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = true;
        } else {
            newFieldIsNull[0] = false;
            newFieldIsNull[1] = false;
        }
        TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, isDefaultValue);
        TestSpecialT3GetLpmProperty(root, f0Value);
        GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
        if (schemaVersion == 0) {
            TestSpecialT3MidVersionGetNewFieldFailed(root, bytesValue, stringValue);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
        } else if (schemaVersion == 1) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
        } else if (schemaVersion == 2) {
            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
            TestSpecialT3MostNewVersionGetNewField(root, f0Value, newFieldIsNull[1]);
        }
    }
    AW_MACRO_EXPECT_EQ_INT(expAffectRows, fetchNum);
    return GMERR_OK;
}

void SubSpecialTCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 30;
    int updateValue = 300;
    char keyName[128] = {0};
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    GmcNodeT *root = NULL, *t1V = NULL;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > 40) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, false);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, false);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcGetVertexPropertyByName(subStmt, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + updateValue, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, false);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            if (f0Value >= 0 && f0Value < count / 3) {
                                newFieldIsNull[0] = true;
                                newFieldIsNull[1] = true;
                            } else if (f0Value >= count / 3 && f0Value < count / 3 * 2) {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            } else {
                                newFieldIsNull[0] = false;
                                newFieldIsNull[1] = true;
                            }
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value < count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            bool newFieldIsNull[2] = {true};
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            if (f0Value > count) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
                            }
                            // 读new
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value + updateValue, bytesValue, false);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            // 读old
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtSpeciallabel2GetNode(subStmt, &root, &t1V);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "merge-update: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            newFieldIsNull[0] = false;
                            TestSpecialT3UpdateGetOldPropertyByName(root, f0Value, bytesValue, true);
                            TestSpecialT3GetLpmProperty(root, f0Value);
                            GtSpeciallabel3GeneralGetVector(t1V, f0Value, stringValue, 3, 3);
                            TestSpecialT3MidVersionGetNewField(root, bytesValue, stringValue, newFieldIsNull[0]);
                            TestSpecialT3MostNewVersionGetNewFieldFailed(root, f0Value);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void TestSpecialT4MostNewVersionGetNewFieldFailed(GmcNodeT *node)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    ret = GmcNodeGetPropertySizeByName(node, "F17", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

/*--------------------------------------一般复杂表---------------------------------*/
typedef struct TagGenerallabelCfg {
    int32_t startVal;       // 主键或其他非成员索引的起始值
    uint32_t count;         // 主键或其他非成员索引的数量
    int32_t expAffectRows;  // 预期的affectRows
    int32_t vectorT1Num;
    int32_t vectorT2Num;
    uint32_t schemaVersion;
    GmcOperationTypeE optType;
    bool fieldIsNull[8];    // 新增版本的字段是否空值
} GtGeneralLabelCfg;

char *g_labelName3 = (char *)"generalLabel";

void GtGeneralLabelGetNode(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **t1V)
{
    GmcNodeT *Root, *t1;
    int32_t ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1", &t1);
    if (ret == GMERR_NO_DATA) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
    } else {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    *root = Root;
    *t1V = t1;
}

// 设置索引start
void TestGeneralT1SetPk(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1PkIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    ret = GmcSetIndexKeyName(stmt, "primary_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 设置属性start
void TestGeneralSetCommonPropertyRoot(GmcNodeT *node, int64_t i, uint8_t *wrFixed, char *string, bool isDefault = true)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = i % 16;
    uint32_t vrfIndex = i % 1024;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i % 129;
    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!isDefault) {
        uint8_t f10Value = i % 31;
        uint16_t f11Value = i % 1023;

        ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD8, &f10Value, sizeof(uint8_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_BITFIELD16, &f11Value, sizeof(uint16_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_FIXED, wrFixed, 16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1SetAddBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F25", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F26", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F27", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F28", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F29", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F31", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F32", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F33", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F34", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F35", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F36", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F37", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F38", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F39", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F40", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F41", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F42", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F43", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F44", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F45", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F46", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F47", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F48", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F49", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F50", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F51", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F52", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F53", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F54", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F55", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F56", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F57", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F58", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F59", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F60", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F61", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F62", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F63", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F64", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F65", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F66", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F67", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F68", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F69", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F70", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F71", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F72", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F73", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F74", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F75", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F76", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F77", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F78", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F79", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F80", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F81", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F82", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F83", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F84", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F85", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F86", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F87", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F88", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F89", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F90", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F91", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F92", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F93", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F94", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F95", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F96", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F97", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F98", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F99", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F100", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f2Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_STRING, f2Value, strlen((char *)f2Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f3Value[10] = "fixed1234";
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_FIXED, f3Value, 9);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f4Value[10] = "bytes";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_BYTES, f4Value, strlen((char *)f4Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1ldVersionSetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0Value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f4Value[12] = "string";
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_STRING, f4Value, strlen((char *)f4Value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f5Value[8] = "fixed12";
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_FIXED, f5Value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1NewFieldSetFailed(GmcNodeT *node, char bigString)
{
    int32_t ret = 0;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigString, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';
    ret = GmcNodeSetPropertyByName(node, (char *)"F23", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F24", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F30", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F80", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F90", GMC_DATATYPE_FIXED, string13k, LABEL_BIG_OBJ_FIXED_SIZE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralUpdateSetCommonProperty_Root(GmcNodeT *node, int64_t i, char *string, bool isDefaultValue = true)
{
    int ret = 0;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint64_t f7Value = i;

    uint8_t f13Value = i & 0xf;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_TIME, &f7Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_STRING, string, strlen(string));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint8_t f9Value = i % 31;
    uint16_t f10Value = i % 1023;
    if (!isDefaultValue) {
        f9Value = i % 31;
        f10Value = i % 1023;
    } else {
        f9Value = 0x1f;
        f10Value = 0x3ff;
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_BITFIELD8, &f9Value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_BITFIELD16, &f10Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT1Write(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, uint8_t ipValue,
    char *string, uint32_t schemaVersion, GmcOperationTypeE operationType, int32_t affectRows = 1,
    bool isDefaultValue = true, char bigstring = 'a')
{
    int ret = 0;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    wrFixed[15] = ipValue;

    AW_FUN_Log(LOG_INFO, "startValue: %d, endValue: %d operationType: %d\n", startValue, endValue, operationType);
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (operationType == GMC_OPERATION_MERGE) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else {
            // 设置主键
            TestGeneralT1SetPk(root, i);
        }
        // 设置根节点公共属性
        TestGeneralSetCommonPropertyRoot(root, i, wrFixed, string, isDefaultValue);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1NewFieldSetFailed(root, bigstring);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T1, &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, i);
        // 插入vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeAppendElement(T2, &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, i);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, affectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1MergeUpdate(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    int32_t schemaVersion, GmcOperationTypeE operationType, bool isDefaultValue = true, int64_t updateValue = 0,
    char bigstring = 'a')
{
    int32_t affactRows = 1;
    if (operationType == GMC_OPERATION_MERGE) {
        affactRows = 2;
    }
    for (int i = startValue; i < endValue; i++) {
        int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, operationType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1PkIndexSet(stmt, i);
        GmcNodeT *root, *T1, *T2;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // MergeUpdate设置公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, string, isDefaultValue);

        // Merge设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT1NewFieldSetFailed(root, bigstring);
        } else if (schemaVersion == 3) {
            TestGeneralT1SetAddBigObject_Root(root, bigstring);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, affactRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT1GetUpdateBigObject_Root(GmcNodeT *node, char bigstring)
{
    int ret = 0;
    bool isNull;
    char string13k[1024 * 13] = {0};
    memset(string13k, bigstring, sizeof(string13k));
    string13k[1024 * 13 - 1] = '\0';

    char f23Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F23", f23Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f23Value2), 0);
    char f24Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F24", f24Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f24Value2), 0);
    char f25Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F25", f25Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f25Value2), 0);
    char f26Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F26", f26Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f26Value2), 0);
    char f27Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F27", f27Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f27Value2), 0);
    char f28Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F28", f28Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f28Value2), 0);
    char f29Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F29", f29Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f29Value2), 0);
    char f30Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F30", f30Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f30Value2), 0);
    char f31Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F31", f31Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f31Value2), 0);
    char f32Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F32", f32Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f32Value2), 0);
    char f33Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F33", f33Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f33Value2), 0);
    char f34Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F34", f34Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f34Value2), 0);
    char f35Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F35", f35Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f35Value2), 0);
    char f36Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F36", f36Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f36Value2), 0);
    char f37Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F37", f37Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f37Value2), 0);
    char f38Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F38", f38Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f38Value2), 0);
    char f39Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F39", f39Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f39Value2), 0);
    char f40Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F40", f40Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f41Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F41", f41Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f40Value2), 0);
    char f42Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F42", f42Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f42Value2), 0);
    char f43Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F43", f43Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f43Value2), 0);
    char f44Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F44", f44Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f44Value2), 0);
    char f45Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F45", f45Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f45Value2), 0);
    char f46Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F46", f46Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f46Value2), 0);
    char f47Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F47", f47Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f47Value2), 0);
    char f48Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F48", f48Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f48Value2), 0);
    char f49Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F49", f49Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f49Value2), 0);
    char f50Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F50", f50Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f50Value2), 0);
    char f51Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F51", f51Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f51Value2), 0);
    char f52Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F52", f52Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f52Value2), 0);
    char f53Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F53", f53Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f53Value2), 0);
    char f54Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F54", f54Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f54Value2), 0);
    char f55Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F55", f55Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f55Value2), 0);
    char f56Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F56", f56Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f56Value2), 0);
    char f57Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F57", f57Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f57Value2), 0);
    char f58Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F58", f58Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f58Value2), 0);
    char f59Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F59", f59Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f59Value2), 0);
    char f60Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F60", f60Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f60Value2), 0);
    char f61Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F61", f61Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f61Value2), 0);
    char f62Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F62", f62Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f62Value2), 0);
    char f63Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F63", f63Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f63Value2), 0);
    char f64Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F64", f64Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f64Value2), 0);
    char f65Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F65", f65Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f65Value2), 0);
    char f66Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F66", f66Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f66Value2), 0);
    char f67Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F67", f67Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f67Value2), 0);
    char f68Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F68", f68Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f68Value2), 0);
    char f69Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F69", f69Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f69Value2), 0);
    char f70Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F70", f70Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f70Value2), 0);
    char f71Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F71", f71Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f71Value2), 0);
    char f72Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F72", f72Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f72Value2), 0);
    char f73Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F73", f73Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f73Value2), 0);
    char f74Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F74", f74Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f74Value2), 0);
    char f75Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F75", f75Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f75Value2), 0);
    char f76Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F76", f76Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f76Value2), 0);
    char f77Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F77", f77Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f77Value2), 0);
    char f78Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F78", f78Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f78Value2), 0);
    char f79Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F79", f79Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f79Value2), 0);
    char f80Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F80", f80Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f80Value2), 0);
    char f81Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F81", f81Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f81Value2), 0);
    char f82Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F82", f82Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f82Value2), 0);
    char f83Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F83", f83Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f83Value2), 0);
    char f84Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F84", f84Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f84Value2), 0);
    char f85Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F85", f85Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f85Value2), 0);
    char f86Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F86", f86Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f86Value2), 0);
    char f87Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F87", f87Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f87Value2), 0);
    char f88Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F88", f88Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f88Value2), 0);
    char f89Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F89", f89Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f89Value2), 0);
    char f90Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F90", f90Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f90Value2), 0);
    char f91Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F91", f91Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f91Value2), 0);
    char f92Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F92", f92Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f92Value2), 0);
    char f93Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F93", f93Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f93Value2), 0);
    char f94Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F94", f94Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f94Value2), 0);
    char f95Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F95", f95Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f95Value2), 0);
    char f96Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F96", f96Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f96Value2), 0);
    char f97Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F97", f97Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f97Value2), 0);
    char f98Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F98", f98Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f98Value2), 0);
    char f99Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F99", f99Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f99Value2), 0);
    char f100Value2[13312] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F100", f100Value2, 13312, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string13k, f100Value2), 0);
}

void TestGeneralT1ldVersionGetCommonProperty_T1_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    char f2Value[100] = "string";
    char f2Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F2", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen((char *)f2Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", f2Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f2Value, f2Value2), 0);

    char f3Value[10] = "fixed1234";
    char f3Value2[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", f3Value2, 9, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f3Value, f3Value2), 0);

    char f4Value[10] = "bytes";
    char f4Value2[10] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", f4Value2, strlen(f4Value), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f4Value, f4Value2), 0);
}

void TestGeneralT1ldVersionGetCommonProperty_T2_V(GmcNodeT *node, int64_t i)
{
    int ret = 0;
    bool isNull;
    unsigned int propSize;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;

    int64_t f0Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F0", &f0Value2, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f0Value, f0Value2);

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f3Value, f3Value2);

    char f4Value[20] = "string";
    char f4Value2[100] = {0};
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F4", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen((char *)f4Value) + 1);
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", f4Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f4Value, f4Value2), 0);

    char f5Value[8] = "fixed12";
    char f5Value2[8] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", f5Value2, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(f5Value, f5Value2), 0);
}

void TestGeneralT1GetCommonPropertyRoot(GmcNodeT *node, int64_t i, char *string, bool isDefaultValue = true)
{
    int ret = 0;
    bool isNull;
    int64_t f0Value = i;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    uint32_t f3Value = i;
    int16_t f4Value = i % 32768;
    uint16_t f5Value = i % 65536;
    uint8_t f6Value = i;
    uint64_t f7Value = i;

    uint32_t vrid = 0;
    uint32_t vrfIndex = 0;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i % 256;
    uint8_t f13Value = i & 0xf;

    uint64_t f1Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f1Value, f1Value2);

    int32_t f2Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2Value2, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f2Value, f2Value2);

    uint32_t f3Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3Value2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f3Value, f3Value2);

    int16_t f4Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4Value2, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f4Value, f4Value2);

    uint16_t f5Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f5Value, f5Value2);

    uint64_t f7Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7Value2, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f7Value, f7Value2);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F9", &propSize);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(propSize, strlen(string) + 1);
    char f9Value2[20] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", f9Value2, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string, f9Value2), 0);
    uint8_t f10Value = 0;
    uint16_t f11Value = 0;
    if (!isDefaultValue) {
        f10Value = i % 31;
        f11Value = i % 1023;
    } else {
        f10Value = 0x1f;
        f11Value = 0x3ff;
    }
    uint8_t f10Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10Value2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f10Value, f10Value2);

    uint16_t f11Value2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11Value2, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(f11Value, f11Value2);
}

void TestGeneralT1GetLpm6Property(GmcNodeT *node, int64_t i, uint8_t ipValue)
{
    int ret = 0;
    bool isNull;
    uint32_t vrid = i % 16;
    uint32_t vrfIndex = i % 1024;
    uint32_t destIpAddr = 0;
    uint8_t maskLen = i % 256;

    uint32_t vrid2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &vrid2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(vrid, vrid2);

    uint32_t vrfIndex2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &vrfIndex2, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(vrfIndex, vrfIndex2);

    uint8_t wr_fixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    wr_fixed[15] = ipValue;

    ret = GmcNodeGetPropertyByName(node, (char *)"F8", wr_fixed, 16, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    uint8_t maskLen2;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &maskLen2, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(maskLen, maskLen2);
}

void TestGeneralHashclusterIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint64_t f1Value = i;
    int32_t f2Value = i;
    ret = GmcSetIndexKeyName(stmt, "hashcluster_unique_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralTNewVersionGetNewFieldFailed(GmcNodeT *node)
{
    int32_t ret = 0;
    bool isNull;
    uint32_t sizeValue = 0;
    char *stringTest = (char *)malloc(LABEL_BIG_OBJ_FIXED_SIZE + 1);
    if (stringTest == NULL) {
        AW_FUN_Log(LOG_ERROR, "stringTest is NULL\n");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }

    ret = GmcNodeGetPropertySizeByName(node, "F23", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F50", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertySizeByName(node, "F99", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F23", stringTest, LABEL_BIG_OBJ_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F30", stringTest, LABEL_BIG_OBJ_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F40", stringTest, LABEL_BIG_OBJ_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F60", stringTest, LABEL_BIG_OBJ_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F80", stringTest, LABEL_BIG_OBJ_FIXED_SIZE, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    free(stringTest);
}

void TestGeneralT1HashclusterScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    uint8_t ipValue, uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralHashclusterIndexSet(stmt, i + updateValue);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonPropertyRoot(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i, ipValue);

        // Hashcluster查询升级后新增字段
        if (schemaVersion == 0) {
            TestGeneralTNewVersionGetNewFieldFailed(root);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    }
}

// 插入查询start
void TestGeneralT1PkScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    uint8_t ipValue, uint32_t schemaVersion, bool isDefaultValue = true, int64_t updateValue = 0, char bigstring = 'a')
{
    bool isFinish = true;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonPropertyRoot(root, i + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i, ipValue);

        // Pk查询升级后新增字段
        if (schemaVersion == 0) {
            TestGeneralTNewVersionGetNewFieldFailed(root);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, i);
        // 查询vectoryT2
        ret = GmcNodeGetChild(T1, "T2", &T2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, i);
    }
}

void TestGeneralT1LocalIndexRangeSet(GmcStmtT *stmt, int64_t startValue, int64_t endValue)
{
    int ret = 0;
    unsigned int arrLen = 1;
    uint32_t lValue = 0;
    uint32_t rValue = endValue;

    GmcPropValueT *leftKeyProps = NULL;
    leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps != NULL) {
        leftKeyProps[0].type = GMC_DATATYPE_UINT32;
        leftKeyProps[0].value = &lValue;
        leftKeyProps[0].size = sizeof(uint32_t);
    }

    GmcPropValueT *rightKeyProps = NULL;
    rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps != NULL) {
        rightKeyProps[0].type = GMC_DATATYPE_UINT32;
        rightKeyProps[0].value = &rValue;
        rightKeyProps[0].size = sizeof(uint32_t);
    }

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps[0];
    items[0].rValue = &rightKeyProps[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    ret = GmcSetKeyRange(stmt, items, arrLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(leftKeyProps);
    free(rightKeyProps);
}

void TestGeneralT1LocalScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, char *string,
    uint8_t ipValue, int32_t schemaVersion, int32_t *fetchNum, bool isDefaultValue = true, int64_t updateValue = 0,
    char bigstring = 'a')
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestGeneralT1LocalIndexRangeSet(stmt, startValue + updateValue, endValue + updateValue);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        (*fetchNum)++;
        GmcNodeT *root, *T1, *T2;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonPropertyRoot(root, f0Value + updateValue, string, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, f0Value, ipValue);

        // Local查询升级后新增字段
        if (schemaVersion == 0) {
            TestGeneralTNewVersionGetNewFieldFailed(root);
        } else if (schemaVersion == 3) {
            TestGeneralT1GetUpdateBigObject_Root(root, bigstring);
        }

        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

// delete start
void TestGeneralT1PkDelete(
    GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue, uint32_t schemaVersion)
{
    int userDataIdx = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = startValue; i < endValue; i++) {
        TestGeneralT1PkIndexSet(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewR1NodeSet(GmcNodeT *node, int32_t value, char *bytesValue, char *stringValue)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f15 = value;
    ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f16 = value;
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f17 = value;
    ret = GmcNodeSetPropertyByName(node, "F17", GMC_DATATYPE_UINT32, &f17, sizeof(f17));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f18 = (value) & 0x7fff;
    ret = GmcNodeSetPropertyByName(node, "F18", GMC_DATATYPE_INT16, &f18, sizeof(f18));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f19 = (value) & 0xffff;
    ret = GmcNodeSetPropertyByName(node, "F19", GMC_DATATYPE_UINT16, &f19, sizeof(f19));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f20 = (value) & 0x7f;
    ret = GmcNodeSetPropertyByName(node, "F20", GMC_DATATYPE_INT8, &f20, sizeof(f20));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f21 = (value) & 0xff;
    ret = GmcNodeSetPropertyByName(node, "F21", GMC_DATATYPE_UINT8, &f21, sizeof(f21));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f22 = value;
    ret = GmcNodeSetPropertyByName(node, "F22", GMC_DATATYPE_TIME, &f22, sizeof(f22));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f23[LABEL_BIG_OBJ_FIXED_SIZE] = {0};
    memset(f23, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
    f23[0] = 65;
    f23[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = value % 256;
    ret = GmcNodeSetPropertyByName(node, "F23", GMC_DATATYPE_FIXED, f23, sizeof(f23));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f24 = (value) & 0x1f;
    ret = GmcNodeSetPropertyByName(node, "F24", GMC_DATATYPE_BITFIELD8, &f24, sizeof(f24));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f25 = (value) & 0x3ff;
    ret = GmcNodeSetPropertyByName(node, "F25", GMC_DATATYPE_BITFIELD16, &f25, sizeof(f25));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f26 = (value) & 0x1ffff;
    ret = GmcNodeSetPropertyByName(node, "F26", GMC_DATATYPE_BITFIELD32, &f26, sizeof(f26));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f27 = (value) & 0x1ffffffff;
    ret = GmcNodeSetPropertyByName(node, "F27", GMC_DATATYPE_BITFIELD64, &f27, sizeof(f27));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f28 = value;
    ret = GmcNodeSetPropertyByName(node, "F28", GMC_DATATYPE_BOOL, &f28, sizeof(f28));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f29 = value;
    ret = GmcNodeSetPropertyByName(node, "F29", GMC_DATATYPE_FLOAT, &f29, sizeof(f29));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f30 = value;
    ret = GmcNodeSetPropertyByName(node, "F30", GMC_DATATYPE_DOUBLE, &f30, sizeof(f30));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f31Bits[1] = {0xff};
    GmcBitMapT f31 = {0};
    f31.beginPos = 0;
    f31.endPos = 8 - 1;
    f31.bits = f31Bits;
    ret = GmcNodeSetPropertyByName(node, "F31", GMC_DATATYPE_BITMAP, &f31, sizeof(f31));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F32", GMC_DATATYPE_BYTES, bytesValue, strlen(bytesValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F33", GMC_DATATYPE_STRING, stringValue,  strlen(stringValue));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void TestGeneralT2NewRootNodeSet(GmcNodeT *node, int32_t value)
{
    int64_t f34 = value;
    int32_t ret = GmcNodeSetPropertyByName(node, "F34", GMC_DATATYPE_INT64, &f34, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT2NewRootNodeSetFailed(GmcNodeT *node, int32_t value)
{
    int64_t f34 = value;
    int32_t ret = GmcNodeSetPropertyByName(node, "F34", GMC_DATATYPE_INT64, &f34, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}
void TestGeneralT2NewR1NodeSetFailed(GmcNodeT *node, int32_t value)
{
    int ret = 0;
    int64_t f14 = value;
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_INT64, &f14, sizeof(f14));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    uint64_t f15 = value;
    ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_UINT64, &f15, sizeof(f15));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);

    int32_t f16 = value;
    ret = GmcNodeSetPropertyByName(node, "F16", GMC_DATATYPE_INT32, &f16, sizeof(f16));
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

int TestGeneralT2NewOldVersionWrite(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, uint8_t ipVal, char *bytesValue,
    char *stringValue, bool isDefault = true)
{
    int32_t ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    int32_t T1Num = vertexCfg.vectorT1Num;
    int32_t T2Num = vertexCfg.vectorT2Num;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    uint8_t wrFixed[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    wrFixed[15] = ipVal;
    for (int i = startPkVal; i < endValue; i++) {
        if (schemaVersion == 0xffffffff) {
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName3, optType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            RETURN_IFERR(ret);
        } else {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, schemaVersion, optType);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            RETURN_IFERR(ret);
        }
        GmcNodeT *root, *T1, *T2, *R1;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (optType == GMC_OPERATION_MERGE) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else {
            // 设置主键
            TestGeneralT1SetPk(root, i);
        }
        // 设置根节点公共属性
        TestGeneralSetCommonPropertyRoot(root, i, wrFixed, stringValue, isDefault);

        // InsertOrReplace设置新增字段属性
        if (schemaVersion == 0 || schemaVersion == 0xffffffff) {
            TestGeneralT2NewRootNodeSetFailed(root, i);
            ret = GmcNodeGetChild(root, "R1", &R1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
        } else if (schemaVersion == 2) {
            ret = GmcNodeGetChild(root, "R1", &R1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT2NewRootNodeSet(root, i);
            TestGeneralT2NewR1NodeSet(R1, i, bytesValue, stringValue);
        }
        // 插入vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int32_t k = 0; k < T1Num; k++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT1ldVersionSetCommonProperty_T1_V(T1, k);
            // 插入vectoryT2
            ret = GmcNodeGetChild(T1, "T2", &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int32_t m = 0; m < T2Num; m++) {
                ret = GmcNodeAppendElement(T2, &T2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGeneralT1ldVersionSetCommonProperty_T2_V(T2, m);
            }
        }
        ret = GmcExecute(stmt);
        // 内存满场景，线程报内存满错误
        if (ret != GMERR_OUT_OF_MEMORY) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            RETURN_IFERR(ret);
        }
    }

    return 0;
}

void TestGeneralLocalhashIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    int16_t f4Value = i;
    uint16_t f5Value = i;
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralLocalIndexSet(GmcStmtT *stmt, int64_t i)
{
    int ret = 0;
    uint32_t f3Value = i;
    ret = GmcSetIndexKeyName(stmt, "local_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3Value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGeneralT2NewOldVersionMergeUpdate(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, uint32_t keyId, char *byteValue,
    char *stringValue, bool isDefaultValue = true, int32_t updateValue = 0)
{
    int32_t ret = 0;
    int32_t startValue = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    GmcOperationTypeE optType = vertexCfg.optType;
    for (int i = startValue; i < endValue; i++) {
        ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, schemaVersion, optType);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (optType == GMC_OPERATION_MERGE) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else {
            if (keyId == 0) {
                TestGeneralT1PkIndexSet(stmt, i);
            } else if (keyId == 1) {
                TestGeneralHashclusterIndexSet(stmt, i);
            } else if (keyId == 2) {
                TestGeneralLocalhashIndexSet(stmt, i);
            } else {
                AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
                AW_MACRO_EXPECT_EQ_INT(1, 0);
                break;
            }
        }
        GmcNodeT *root, *T1, *T2, *R1;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // MergeUpdate设置公共部分属性
        TestGeneralUpdateSetCommonProperty_Root(root, i + updateValue, stringValue, isDefaultValue);

        // Merge设置新增字段属性
        if (schemaVersion == 0) {
            TestGeneralT2NewRootNodeSetFailed(root, i + updateValue);
            ret = GmcNodeGetChild(root, "R1", &R1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
        } else if (schemaVersion == 2) {
            ret = GmcNodeGetChild(root, "R1", &R1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT2NewRootNodeSet(root, i + updateValue);
            TestGeneralT2NewR1NodeSet(R1, i + updateValue, byteValue, stringValue);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestGetAffactRows(stmt, expAffectRows);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewR1NodeGet(
    GmcNodeT *node, int32_t value, char *bytesValue, char *stringValue, bool fieldIsNull)
{
    if (!fieldIsNull) {
        int64_t f14 = value;
        int32_t ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_INT64, &f14);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f15 = value;
        ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_UINT64, &f15);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int32_t f16 = value;
        ret = queryNodePropertyAndCompare(node, "F16", GMC_DATATYPE_INT32, &f16);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f17 = value;
        ret = queryNodePropertyAndCompare(node, "F17", GMC_DATATYPE_UINT32, &f17);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int16_t f18 = (value) & 0x7fff;
        ret = queryNodePropertyAndCompare(node, "F18", GMC_DATATYPE_INT16, &f18);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f19 = (value) & 0xffff;
        ret = queryNodePropertyAndCompare(node, "F19", GMC_DATATYPE_UINT16, &f19);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        int8_t f20 = (value) & 0x7f;
        ret = queryNodePropertyAndCompare(node, "F20", GMC_DATATYPE_INT8, &f20);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f21 = (value) & 0xff;
        ret = queryNodePropertyAndCompare(node, "F21", GMC_DATATYPE_UINT8, &f21);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f22 = value;
        ret = queryNodePropertyAndCompare(node, "F22", GMC_DATATYPE_TIME, &f22);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f23[LABEL_BIG_OBJ_FIXED_SIZE] = {0};
        memset(f23, 0, SIMPLE_LABEL_ADD_FIXED_SIZE);
        f23[0] = 65;
        f23[SIMPLE_LABEL_ADD_FIXED_SIZE - 1] = value % 256;
        ret = queryNodePropertyAndCompare(node, "F23", GMC_DATATYPE_FIXED, f23);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f24 = (value) & 0x1f;
        ret = queryNodePropertyAndCompare(node, "F24", GMC_DATATYPE_BITFIELD8, &f24);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint16_t f25 = (value) & 0x3ff;
        ret = queryNodePropertyAndCompare(node, "F25", GMC_DATATYPE_BITFIELD16, &f25);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t f26 = (value) & 0x1ffff;
        ret = queryNodePropertyAndCompare(node, "F26", GMC_DATATYPE_BITFIELD32, &f26);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint64_t f27 = (value) & 0x1ffffffff;
        ret = queryNodePropertyAndCompare(node, "F27", GMC_DATATYPE_BITFIELD64, &f27);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        bool f28 = value;
        ret = queryNodePropertyAndCompare(node, "F28", GMC_DATATYPE_BOOL, &f28);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        float f29 = value;
        ret = queryNodePropertyAndCompare(node, "F29", GMC_DATATYPE_FLOAT, &f29);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        double f30 = value;
        ret = queryNodePropertyAndCompare(node, "F30", GMC_DATATYPE_DOUBLE, &f30);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0xff};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryNodePropertyAndCompare(node, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F32", GMC_DATATYPE_BYTES, bytesValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F33", GMC_DATATYPE_STRING, stringValue);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_UINT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F16", GMC_DATATYPE_INT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F17", GMC_DATATYPE_UINT32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F18", GMC_DATATYPE_INT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F19", GMC_DATATYPE_UINT16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F20", GMC_DATATYPE_INT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F21", GMC_DATATYPE_UINT8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F22", GMC_DATATYPE_TIME, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F23", GMC_DATATYPE_FIXED, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F24", GMC_DATATYPE_BITFIELD8, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F25", GMC_DATATYPE_BITFIELD16, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F26", GMC_DATATYPE_BITFIELD32, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F27", GMC_DATATYPE_BITFIELD64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F28", GMC_DATATYPE_BOOL, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F29", GMC_DATATYPE_FLOAT, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = queryNodePropertyAndCompare(node, "F30", GMC_DATATYPE_DOUBLE, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint8_t f31Bits[1] = {0x00};
        GmcBitMapT f31 = {0};
        f31.beginPos = 0;
        f31.endPos = 8 - 1;
        f31.bits = f31Bits;
        ret = queryNodePropertyAndCompare(node, "F31", GMC_DATATYPE_BITMAP, f31Bits);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F32", GMC_DATATYPE_BYTES, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = queryNodePropertyAndCompare(node, (char *)"F33", GMC_DATATYPE_STRING, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewRootNodeGet(GmcNodeT *node, int32_t value, bool fieldIsNull)
{
    if (!fieldIsNull) {
        int64_t f34 = value;
        int32_t ret = queryNodePropertyAndCompare(node, "F34", GMC_DATATYPE_INT64, &f34);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    } else {
        int32_t ret = queryNodePropertyAndCompare(node, "F34", GMC_DATATYPE_INT64, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void TestGeneralT2NewRootNodeGetFailed(GmcNodeT *node)
{
    unsigned int sizeValue = 0;
    bool isNull = 0;
    int64_t pValue = 0;
    int32_t ret = GmcNodeGetPropertySizeByName(node, "F34", &sizeValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = GmcNodeGetPropertyByName(node, "F34", &pValue, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
}

void TestGeneralLpm6IndexSet(GmcStmtT *stmt, int64_t i, uint8_t *destIpAddr)
{
    int32_t ret = 0;
    uint8_t maskLen = i % 256;
    uint32_t vrid = i % 16;
    uint32_t vrfIndex = i % 1024;

    ret = GmcSetIndexKeyName(stmt, "lpm6_key");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrid, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, destIpAddr, 16);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

int TestGeneralT2NewOldVersionRead(GmcStmtT *stmt, GtGeneralLabelCfg vertexCfg, uint32_t keyId, uint8_t ipValue,
    char *bytesValue, char *stringValue, bool isDefaultValue = true, int32_t updateValue = 0, bool isUpdate = false)
{
    int ret = 0;
    int32_t startPkVal = vertexCfg.startVal;
    uint32_t endValue = vertexCfg.count;
    int32_t expAffectRows = vertexCfg.expAffectRows;
    uint32_t schemaVersion = vertexCfg.schemaVersion;
    int32_t t1Num = vertexCfg.vectorT1Num;
    int32_t t2Num = vertexCfg.vectorT2Num;
    bool fieldIsNull[8] = {0};
    uint32_t fetchNum = 0;
    for (int i = 0; i < 8; i++) {
        fieldIsNull[i] = vertexCfg.fieldIsNull[i];
    }
    bool isFinish = false;
    uint8_t destIpAddr[16] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x20};
    destIpAddr[15] = ipValue;

    for (int i = startPkVal; i < endValue; i++) {
        if (schemaVersion == 0xffffffff) {
            ret = testGmcPrepareStmtByLabelName(stmt, g_labelName3, GMC_OPERATION_SCAN);
            RETURN_IFERR(ret);
        } else {
            ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_labelName3, schemaVersion, GMC_OPERATION_SCAN);
            RETURN_IFERR(ret);
        }
        if (keyId == 0) {
            TestGeneralT1PkIndexSet(stmt, i);
        } else if (keyId == 1) {
            TestGeneralHashclusterIndexSet(stmt, i + updateValue);
        } else if (keyId == 2) {
            TestGeneralLocalhashIndexSet(stmt, i + updateValue);
        } else if (keyId == 3) {
            TestGeneralLocalIndexSet(stmt, i + updateValue);
        } else if (keyId == 4) {
            TestGeneralLpm6IndexSet(stmt, i, destIpAddr);
        } else {
            AW_FUN_Log(LOG_ERROR, "keyId error!! keyId = %d", keyId);
        }
        ret = GmcExecute(stmt);
        RETURN_IFERR(ret);
        ret = GmcFetch(stmt, &isFinish);
        RETURN_IFERR(ret);
        GmcNodeT *root, *T1, *T2, *R1;
        // 查询根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 查询根节点公共部分
        TestGeneralT1GetCommonPropertyRoot(root, i + updateValue, stringValue, isDefaultValue);
        // 查询根节点LMP6索引
        TestGeneralT1GetLpm6Property(root, i, ipValue);
        if (schemaVersion == 0 || schemaVersion == 0xffffffff) {
            TestGeneralT2NewRootNodeGetFailed(root);
            ret = GmcNodeGetChild(root, "R1", &R1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
        } else if (schemaVersion == 2) {
            if (isUpdate) {
                ret = GmcNodeGetChild(root, "R1", &R1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGeneralT2NewR1NodeGet(R1, i + updateValue, bytesValue, stringValue, fieldIsNull[1]);
            } else {
                ret = GmcNodeGetChild(root, "R1", &R1);
                if (ret == GMERR_NO_DATA) {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                } else {
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    TestGeneralT2NewR1NodeGet(R1, i, bytesValue, stringValue, fieldIsNull[1]);
                }
            }
        }
        // 查询vectoryT1
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int32_t j = 0; j < t1Num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
            // 查询vectoryT2
            ret = GmcNodeGetChild(T1, "T2", &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int32_t m = 0; m < t2Num; m++) {
                ret = GmcNodeGetElementByIndex(T2, m, &T2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
            }
        }
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
        return ret;
    }
    return 0;
}

void TestGeneralT2LocalScan(GmcStmtT *stmt, char *labelName, int64_t startValue, int64_t endValue,
    uint32_t schemaVersion, uint32_t *fetchNum, int64_t updateValue = 0)
{
    bool isFinish = true;
    int64_t f0Value;
    bool isNull = 0;
    (*fetchNum) = 0;
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, schemaVersion, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    TestGeneralT1LocalIndexRangeSet(stmt, startValue + updateValue, endValue + updateValue);
    ret = GmcExecute(stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        (*fetchNum)++;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

void SubGeneralTCallBackWithNewVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 20;
    int updateValue = 500;
    char keyName[128] = {0};
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GmcNodeT *root, *T1, *T2, *R1;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            bool fieldIsNull = true;
                            if (f0Value >= 0 && f0Value < count / 2) {
                                fieldIsNull = true;
                            } else {
                                fieldIsNull = false;
                            }
                            TestGeneralT2NewRootNodeGet(root, f0Value, fieldIsNull);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            if (ret == GMERR_NO_DATA) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            } else {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT2NewR1NodeGet(R1, f0Value, bytesValue, stringValue, fieldIsNull);
                            }
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            bool fieldIsNull = true;
                            if (f0Value >= 0 && f0Value < count / 2) {
                                fieldIsNull = true;
                            } else {
                                fieldIsNull = false;
                            }
                            TestGeneralT2NewRootNodeGet(root, f0Value, fieldIsNull);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            if (ret == GMERR_NO_DATA) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            } else {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT2NewR1NodeGet(R1, f0Value, bytesValue, stringValue, fieldIsNull);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            bool fieldIsNull = true;
                            if (f0Value >= 0 && f0Value < count / 2) {
                                fieldIsNull = true;
                            } else {
                                fieldIsNull = false;
                            }
                            TestGeneralT2NewRootNodeGet(root, f0Value, fieldIsNull);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            if (ret == GMERR_NO_DATA) {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            } else {
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT2NewR1NodeGet(R1, f0Value, bytesValue, stringValue, fieldIsNull);
                            }
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

void SubGeneralTCallBackWithOldVersion(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    char labelName[128] = {0};
    unsigned int labelNameLen = 0;
    uint32_t size;
    int count = 20;
    int updateValue = 500;
    char keyName[128] = {0};
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GmcNodeT *root, *T1, *T2, *R1;
    SnUserDataT *userData1 = (SnUserDataT *)userData;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (int i = 0; i < info->labelCount; i++) {
            (void)memset(labelName, 0, sizeof(labelName));
            labelNameLen = sizeof(labelName);
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            switch (info->msgType) {
                case 5: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_DELETE: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "delete: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            TestGeneralT2NewRootNodeGetFailed(root);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType:%d  %d\r\n", info->eventType,
                                __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 6: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_INSERT: {
                            // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "insert: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            TestGeneralT2NewRootNodeGetFailed(root);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                case 7: {
                    switch (info->eventType) {
                        case GMC_SUB_EVENT_UPDATE: {
                            break;
                        }
                        case GMC_SUB_EVENT_REPLACE: {
                           // 读key
                            bool isNull = false;
                            int64_t f0Value = 0;
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_KEY);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            // 获取查询到的数据
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            GtGeneralLabelGetNode(subStmt, &root, &T1);
                            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0Value, sizeof(int64_t), &isNull);
                            AW_FUN_Log(LOG_INFO, "replace: f0Value = %d, line = %d\n", f0Value, __LINE__);
                            // 查询根节点公共部分
                            TestGeneralT1GetCommonPropertyRoot(root, f0Value, stringValue, true);
                            // 查询根节点LMP6索引
                            TestGeneralT1GetLpm6Property(root, f0Value, 0);
                            // 查询vectoryT1
                            ret = GmcNodeGetChild(root, "T1", &T1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                            for (int32_t j = 0; j < 3; j++) {
                                ret = GmcNodeGetElementByIndex(T1, j, &T1);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                TestGeneralT1ldVersionGetCommonProperty_T1_V(T1, j);
                                // 查询vectoryT2
                                ret = GmcNodeGetChild(T1, "T2", &T2);
                                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                for (int32_t m = 0; m < 3; m++) {
                                    ret = GmcNodeGetElementByIndex(T2, m, &T2);
                                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                                    TestGeneralT1ldVersionGetCommonProperty_T2_V(T2, m);
                                }
                            }
                            TestGeneralT2NewRootNodeGetFailed(root);
                            ret = GmcNodeGetChild(root, "R1", &R1);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
                            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                            AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_INSERT: {
                            break;
                        }
                        case GMC_SUB_EVENT_MERGE_UPDATE: {
                            break;
                        }
                        default: {
                            AW_FUN_Log(LOG_ERROR, "invalid eventType %d  line:%d\r\n",
                                info->eventType, __LINE__);
                            break;
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_ERROR, "invalid eventMsgType: %d eventType:%d line:%d\r\n",
                        info->msgType, info->eventType, __LINE__);
                    break;
                }
            }
        }
        userData1->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                userData1->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                userData1->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                userData1->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                userData1->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                userData1->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                userData1->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                userData1->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                userData1->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                userData1->agedNum++;
                break;
            }
            default: {
                break;
            }
        }
    }
}

// 查询表的版本号
#define VERTEX_LABLE_VERSION_NUM  8 // 一张vertex表最大版本个数
int g_version[VERTEX_LABLE_VERSION_NUM] = {0};
void TestGmsysviewLabelVersion(char *labelName)
{
    char command[512] = {0};
    uint64_t length;
    char tmpBuff[512];
    char str[2][512];
    for (int id = 0; id < VERTEX_LABLE_VERSION_NUM; id++) {
        g_version[id] = 0;
    }
    int32_t ret = 0;
    int i;
    char const *viewName = "V\\$CATA_VERTEX_LABEL_INFO";
    (void)snprintf(command, 512, "%s/gmsysview -q %s -f VERTEX_LABEL_NAME=%s", g_toolPath, viewName, labelName);
    FILE *pf = popen(command, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_ERROR, "popen(%s) error.\n", command);
        AW_MACRO_ASSERT_NE_INT((void *)NULL, pf);
    }
    while (fgets(tmpBuff, sizeof(tmpBuff), pf) != NULL) {
        length = strlen(tmpBuff);
        while (length > 0 && (tmpBuff[length - 1] == '\n' || tmpBuff[length - 1] == '\r')) {
            tmpBuff[length - 1] = '\0';
            --length;
        }
        (void)sscanf(tmpBuff, "%s %s", str[0], str[1]);
        if (str[0][0] == ' ' || str[1][0] == '\0') {
            continue;
        }
        if (strcmp(str[0], "VERSION:") == 0) {
            if (str[1]) {
                i = atoi(str[1]);
                if (i >= 8) {
                    AW_FUN_Log(LOG_ERROR, "version No. is >= 8 %s %s\n", str[0], str[1]);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
                    break;
                }
                g_version[i]++;
            } else {
                AW_FUN_Log(LOG_ERROR, "version No. is NULL %s %s\n", str[0], str[1]);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
            }
        }
    }

    ret = pclose(pf);
    if (ret != 0) {
        AW_FUN_Log(LOG_ERROR, "pclose(%d) error.\n", ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

#endif
