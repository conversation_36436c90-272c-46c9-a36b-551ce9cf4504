[{"type": "record", "name": "testT55", "schema_version": 2, "fields": [{"name": "F0", "type": "uint8", "nullable": false}, {"name": "F1", "type": "float", "nullable": false}, {"name": "F2", "type": "double", "nullable": false}, {"name": "F3", "type": "string", "nullable": false, "size": 100}, {"name": "F4", "type": "string", "nullable": true, "size": 100}], "keys": [{"node": "testT55", "name": "T10_PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]