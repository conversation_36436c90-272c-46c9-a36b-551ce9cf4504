#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include <sys/ipc.h> // 共享内存需要的头文件
#include <sys/shm.h> // 共享内存需要的头文件
#include "gtest/gtest.h"
#include "GmddlFunUpg.h"

int TestDropAllBefore()
{
    // drop
    char *tmpTable1 = (char *)"specialLabel";
    int ret = GmcDropVertexLabel(g_stmt, tmpTable1);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    char *tmpTable2 = (char *)"generalLabel";
    ret = GmcDropVertexLabel(g_stmt, tmpTable2);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = GmcDropVertexLabel(g_stmt, tmpTable3);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    char *tmpTable4 = (char *)"specialLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable4);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    char *tmpTable5 = (char *)"generalLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable5);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    char *tmpTable6 = (char *)"simpleLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable6);
    EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);

    return GMERR_OK;
}

class GmddlFunUpg : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GmddlFunUpg::SetUpTestCase()
{
    int ret = 0;
    AW_FUN_Log(LOG_INFO, ">>>>>start.sh \n");
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=64\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxNormalTableNum=10000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=20,299,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableClusterHash=1\"");

    // 关闭流控，不然订阅消息不消费时候，1s一个
    system("sh $TEST_HOME/tools/modifyCfg.sh "
           "\"clientServerFlowControl=0;0;0;0\" ");

    // 缩容配置
    system("sh $TEST_HOME/tools/modifyCfg.sh \"memCompactEnable=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    sleep(1);

    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GmddlFunUpg::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
}

void GmddlFunUpg::SetUp()
{
    // 建连
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 提前删除环境中同名表
    ret = TestDropAllBefore();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void GmddlFunUpg::TearDown()
{
    int ret = 0;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/*
开启2个线程，对其中的5张表的最低版本循环操作，其中一个线程是覆盖写，一个线程是对多表进行全表扫描。（下面升降级的5张表包含在其中），覆盖写和扫描线程作为背景并发业务。
4.批量升级其中的5张表。建连接使用高版本表，查看是否所有表都成功升级，并做DML操作。
5.高版本导入包含新字段的数据，预期导入新数据成功。
6.降级其中的5张表。查看是否所有表都正确降级。
7.再次导入包含新字段的数据，预期导入新数据失败。*/

int TestDropAll()
{
    // drop
    char *tmpTable1 = (char *)"specialLabel";
    int ret = GmcDropVertexLabel(g_stmt, tmpTable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"generalLabel";
    ret = GmcDropVertexLabel(g_stmt, tmpTable2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable3 = (char *)"simpleLabel";
    ret = GmcDropVertexLabel(g_stmt, tmpTable3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable4 = (char *)"specialLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable5 = (char *)"generalLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable6 = (char *)"simpleLabelA";
    ret = GmcDropVertexLabel(g_stmt, tmpTable6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

int TestDropFullFieldSchema()
{
    // drop
    char *tmpTable1 = (char *)"fields_1022_schema";
    int ret = GmcDropVertexLabel(g_stmt, tmpTable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *tmpTable2 = (char *)"fields_1023_schema";
    ret = GmcDropVertexLabel(g_stmt, tmpTable2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return 0;
}


// 001.6 90%过载下进行表升降级，预期成功。同时不影响其他使用表的业务线程。
TEST_F(GmddlFunUpg, rel_upgDeg_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 服务端进程绑定CPU指定核心
    system("taskset -pc 0 `pidof gmserver`");

    // cpu过载90%
    pthread_t FLT, DB;
    pthread_create(&FLT, NULL, ThreadRcpuOverloadlValue90, NULL);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 等待20s，恢复正常
    pthread_join(FLT, NULL);
    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 002.7 100%过载下进行表升降级，预期成功。同时不影响其他使用表的业务线程。
TEST_F(GmddlFunUpg, rel_upgDeg_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 服务端进程绑定CPU指定核心
    system("taskset -pc 0 `pidof gmserver`");

    // cpu过载100%
    pthread_t FLT;
    pthread_create(&FLT, NULL, ThreadRcpuOverloadlValue100, NULL);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>> 1111 \n");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 等待20s，恢复正常
    pthread_join(FLT, NULL);
    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 003.8 100%过载下进行表升降级，规格约束校验正确。同时不影响其他使用表的业务线程。
TEST_F(GmddlFunUpg, rel_upgDeg_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 服务端进程绑定CPU指定核心
    system("taskset -pc 0 `pidof gmserver`");

    // cpu过载100%
    pthread_t FLT;
    pthread_create(&FLT, NULL, ThreadRcpuOverloadlValue100, NULL);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>> 1111 \n");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 -- 预期失败\n");
    char *expectValue1 = (char *)"Unsuccess upgrade vertexLabel";
    char *schemaUpFile = (char *)"./schemaFileUpError/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 检查升级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级 -- 预期失败\n");
    ret = TestDownGradeAllFailed();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 等待20s，恢复正常
    pthread_join(FLT, NULL);
    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 004.9 升降级表过程中一段时间100%过载，预期成功。同时不影响其他使用表的业务线程。
TEST_F(GmddlFunUpg, rel_upgDeg_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 服务端进程绑定CPU指定核心
    system("taskset -pc 0 `pidof gmserver`");

    // cpu过载100%
    pthread_t FLT;
    pthread_create(&FLT, NULL, ThreadRcpuOverloadlValue100With30s, NULL);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, 0, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, schemaVersion);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 等待20s，恢复正常
    pthread_join(FLT, NULL);
    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 内存申请大小限制
#if defined ENV_RTOSV2X
#define  MEGABYTE (10 * 1024)
#else
#define  MEGABYTE (1024 * 1024 * 1024)
#endif

int g_shmCount = 0;
int shmid[10000];
void ShmCreateFull()
{
    key_t key[10000];
    for (int i=0; i < 10000; i++) {
        shmid[i] = 0;
        key[i] = i + 12345;
    }
    int count = 0;
    int j=0;
    for (int i = 0; i < 10000; i++) {
        shmid[i] = shmget(key[i], MEGABYTE, IPC_CREAT | 0630);
        if (i % 200 == 0) {
            AW_FUN_Log(LOG_INFO, "shmid is %d, i is %d\n", shmid[i], i);
        }
        if (shmid[i] == -1) {
            j = i;
            break;
        } else {
            char *p;
            p = (char *)shmat(shmid[i], NULL, 0);
            // 3. 对共享内存实现读写
            memset(p, 1, MEGABYTE);
            // 4. 取消映射
            shmdt(p);
            count++;
        }
    }
    for (int i = j; i < 10000; i++) {
        shmid[i] = shmget(key[i], 20000, IPC_CREAT | 0630);
        if (i % 200 == 0) {
            AW_FUN_Log(LOG_INFO, "shmid is %d, i is %d\n", shmid[i], i);
        }
        if (shmid[i] == -1) {
            sleep(50);
            j = i;
            break;
        } else {
            char *p;
            p = (char *)shmat(shmid[i], NULL, 0);
            // 3. 对共享内存实现读写
            memset(p, 1, 20000);
            // 4. 取消映射
            shmdt(p);
            count++;
        }
    }

    g_shmCount = count;
    AW_FUN_Log(LOG_INFO, ">>>>>>>>shmcount  %d\n", g_shmCount);
}

void FreeShm()
{
    // 释放消耗掉的共享内存
    for (int i = 0; i < g_shmCount; i++) {
        if (i % 100 == 0) {
            AW_FUN_Log(LOG_INFO, "i is %d \n", i);
        }
        // 删除共享内存
        shmctl(shmid[i], IPC_RMID, NULL);
    }
}

// 005.10 共享内存不足时，进行表升降级 和欧拉环境相关，可能申请共享内存后，无法操作，重启环境后可通过；
TEST_F(GmddlFunUpg, rel_upgDeg_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 消耗共享内存，升级并不耗大内存，所以升级预期成功
    ShmCreateFull();

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 释放共享内存
    FreeShm();

    sleep(2);
    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 025.11 大页内存不足时，进行表升降级   -- 没有相关用例
// TEST_F(GmddlFunUpg, rel_upgDeg_025)


// 006 20 时钟向前跳变
TEST_F(GmddlFunUpg, rel_upgDeg_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 注入故障：时钟跳变
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, TimeDumpForwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1   gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    sleep(2);

    // 恢复正常
    pthread_join(FLT, NULL);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=+ and OFFSET=120\"");
    }

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 注入故障：时钟跳变
    pthread_t FLT1;
    ret = pthread_create(&FLT1, NULL, TimeDumpForwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复正常
    pthread_join(FLT1, NULL);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=+ and OFFSET=120\"");
    }

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 007 21 时钟向后跳变
TEST_F(GmddlFunUpg, rel_upgDeg_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 注入故障：时钟向后跳变
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, TimeDumpBackwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    sleep(2);

    // 恢复正常
    pthread_join(FLT, NULL);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=- and OFFSET=120\"");
    }

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 注入故障：时钟跳变
    pthread_t FLT1;
    ret = pthread_create(&FLT1, NULL, TimeDumpBackwardThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复正常
    pthread_join(FLT1, NULL);
    for (int i = 0; i < 5; i++) {
        system("/root/CFE_Tool/cfe/cfe \"clean rSysClockJump where DIRECTION=- and OFFSET=120\"");
    }

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 008 25 服务端异常退出
TEST_F(GmddlFunUpg, rel_upgDeg_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleWrite(g_stmt, g_labelNameA, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgWriteA = {schemaVersion, startValue, endValue, true, g_labelNameA};
    pthread_create(&thr_arr[2], NULL, ThreadSimpleWrite, (void *)&tArgWriteA);

    GtSimpleThreCfg tArgReadA = {schemaVersion, startValue, endValue, true, g_labelNameA};
    pthread_create(&thr_arr[3], NULL, ThreadSimpleRead, (void *)&tArgReadA);

    GtSimpleThreCfg tArgUpDown = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadUpgradeDown, (void *)&tArgUpDown);

    sleep(10);

    AW_FUN_Log(LOG_INFO, ">>> kill gmserver \n");
    KILLThread009();

    sleep(10);
    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 4; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }
}

// 009 25 进程异常退出，客户端
TEST_F(GmddlFunUpg, rel_upgDeg_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    // 拉起客户端进程
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>拉起客户端进程,升级并读写\n");
    system("./reliUpgDeg &");

    sleep(20);
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg");
    system(cmd);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    system("gmsysview count");

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 010 27 进程挂起/解挂  服务端
TEST_F(GmddlFunUpg, rel_upgDeg_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t endValueMid = endValue;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 注入故障：db挂起解挂
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>注入故障：db挂起解挂\n");
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, KILLThread019, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    // 升级版本 1 gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 恢复正常
    pthread_join(FLT, NULL);

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, schemaVersion);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 注入故障：db挂起解挂
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>注入故障：db挂起解挂\n");
    pthread_t FLT1;
    ret = pthread_create(&FLT1, NULL, KILLThread019, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复正常
    pthread_join(FLT1, NULL);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 011 28 进程挂起/解挂  客户端
TEST_F(GmddlFunUpg, rel_upgDeg_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 拉起客户端进程
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>拉起客户端进程,升级并读写\n");
    system("./reliUpgDeg1 |tee reliUpgDeg1.txt &");
    system("./reliUpgDeg2 |tee reliUpgDeg2.txt &");
    system("./reliUpgDeg3 |tee reliUpgDeg3.txt &");

    sleep(1);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>挂起客户端进程\n");
    system("kill -19 `pidof reliUpgDeg1`");
    system("kill -19 `pidof reliUpgDeg2`");
    system("kill -19 `pidof reliUpgDeg3`");

    sleep(10);
    system("kill -18 `pidof reliUpgDeg1`");
    system("kill -18 `pidof reliUpgDeg2`");
    system("kill -18 `pidof reliUpgDeg3`");

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}



// 012 30 进程重复启动 客户端反复异常退出
TEST_F(GmddlFunUpg, rel_upgDeg_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 拉起客户端进程
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>拉起客户端进程,升级并读写\n");
    system("./reliUpgDeg1 |tee reliUpgDeg1.txt &");
    system("./reliUpgDeg2 |tee reliUpgDeg2.txt &");
    system("./reliUpgDeg3 |tee reliUpgDeg3.txt &");

    sleep(1);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>kill客户端进程&重新拉起\n");
    for (int i = 0; i < 100; i++) {
        (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
        system(cmd);
        (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
        system(cmd);
        usleep(200);

        system("./reliUpgDeg1 |tee reliUpgDeg11.txt &");
        system("./reliUpgDeg3 |tee reliUpgDeg33.txt &");
        usleep(200);
    }

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 013 35 超大规格表
TEST_F(GmddlFunUpg, rel_upgDeg_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./shcemaFileFullField/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./shcemaFileFullFieldUp/batchError.txt";
    ret = TestUpdateVertexLabelAbnormal(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadshcemaFullFieldWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadshcemaFullFieldRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadFullFieldUpgradeDown, (void *)&tArgGenWrite);

    sleep(2);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 2; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    char *tmpTable1 = (char *)"fields_1022_schema";
    ret = GmcDropVertexLabel(g_stmt, tmpTable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 014 36 表的个数超系统限制
TEST_F(GmddlFunUpg, rel_upgDeg_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    int i = 0;
    char vertexLabelName[32] = "testT1";
    char vertexLabelJson[MAX_CONN_SIZE];
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%u", i);
        // 建表
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        i++;
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>cretate %d tables, ret = %d \n", i - 1, ret);

    // 升级版本 1  gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    // 欧拉上报错内存满，AC上报错1011000 超出限制
    if (ret == GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
        char *expectValue1 = (char *)"upgrade unsuccessfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表,释放空间
    i = 0;
    ret = 0;
    while (!ret) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        i++;
    }

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 拉起客户端进程
    char cmd[1024] = {0};
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>拉起客户端进程,升级并读写\n");
    system("./reliUpgDeg1 |tee reliUpgDeg1.txt &");
    system("./reliUpgDeg2 |tee reliUpgDeg2.txt &");
    system("./reliUpgDeg3 |tee reliUpgDeg3.txt &");

    sleep(5);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg1");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg2");
    system(cmd);
    (void)snprintf(cmd, 1024, "pkill -%d %s", SIGHUP, "reliUpgDeg3");
    system(cmd);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 015 37 表的个数超系统限制
TEST_F(GmddlFunUpg, rel_upgDeg_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("./shcemaFileFullField/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    int i = 0;
    char vertexLabelName[32] = "testT1";
    char vertexLabelJson[MAX_CONN_SIZE];
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%u", i);
        // 建表
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        i++;
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>cretate %d tables, ret = %d \n", i - 1, ret);

    // 升级版本 1  gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    // 欧拉上报错内存满，AC上报错1011000 超出限制
    if (ret == GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
        char *expectValue1 = (char *)"upgrade unsuccessfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表,释放空间
    i = 0;
    ret = 0;
    while (!ret) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        i++;
    }

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleWrite(g_stmt, g_labelNameA, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgWriteA = {schemaVersion, startValue, endValue, true, g_labelNameA};
    pthread_create(&thr_arr[2], NULL, ThreadSimpleWrite, (void *)&tArgWriteA);

    GtSimpleThreCfg tArgReadA = {schemaVersion, startValue, endValue, true, g_labelNameA};
    pthread_create(&thr_arr[3], NULL, ThreadSimpleRead, (void *)&tArgReadA);

    GtSimpleThreCfg tArgUpDown = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadUpgradeDownNormal, (void *)&tArgUpDown);

    GtSimpleThreCfg tArgUpDownAml = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadUpgradeDownAbnormal, (void *)&tArgUpDownAml);

    GtSimpleThreCfg tArgUpDownAml2 = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[6], NULL, ThreadUpgradeDownAbnormal, (void *)&tArgUpDownAml2);

    sleep(10);
    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 6; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestDropFullFieldSchema();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 016 38 超大规格数据写
TEST_F(GmddlFunUpg, rel_upgDeg_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmimportVertex("./shcemaFileFullField/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 建表
    int i = 0;
    char vertexLabelName[32] = "testT1";
    char vertexLabelJson[MAX_CONN_SIZE];
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        sprintf(vertexLabelName, "testT%u", i);
        // 建表
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, NULL);
        i++;
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>cretate %d tables, ret = %d \n", i - 1, ret);

    // 升级版本 1  gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    // 欧拉上报错内存满，AC上报错1011000 超出限制
    if (ret == GMERR_OUT_OF_MEMORY) {
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
        char *expectValue1 = (char *)"upgrade unsuccessfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表,释放空间
    i = 0;
    ret = 0;
    while (!ret) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        i++;
    }


    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleWrite(g_stmt, g_labelNameA, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadshcemaFullFieldWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadshcemaFullFieldRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadFullFieldUpgradeDown, (void *)&tArgGenWrite);
    sleep(10);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 2; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestDropFullFieldSchema();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


//017 39 连接资源耗尽
TEST_F(GmddlFunUpg, rel_upgDeg_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *labelName = "acl_hpp_stat";
    GmcConnT *connSub[MAX_CONN_SIZE];
    GmcStmtT *stmtSub[MAX_CONN_SIZE];
    GmcConnT *conn[MAX_CONN_SIZE];
    GmcStmtT *stmt[MAX_CONN_SIZE];
// AC设备自带5个预留连接，先把gmsysview导入
#if defined ENV_RTOSV2
    char schema_path2[128] = "./allow_list/allowlist_gmsysview.gmuser";
    ImportAllowList(schema_path2, "allowlist_gmsysview.gmuser successfully.");
#endif

    char schema_path1[128] = "./allow_list/allowlist_conn.gmuser";
    ImportAllowList(schema_path1, "allowlist_conn.gmuser successfully.");
    char policy_path[128] = "./allow_list/policy.gmpolicy";
    ImportPolicy(policy_path, "policy.gmpolicy successfully.");

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 ThreadUpgradeDownNormal\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);

#ifdef ENV_SUSE
    existConnNum = existConnNum + 1;
#endif
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>> create %u conn existConnNum: %u.", MAX_CONN_SIZE - existConnNum, existConnNum);

    //连接建满
    for (unsigned int i = 0; i < MAX_CONN_SIZE - existConnNum; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < 5; i++) {
        AW_FUN_Log(LOG_INFO, ">>> write delete replace %d\n", i);
        // 写 读 基础数据 - 简单表
        int32_t startValue = 0;
        uint32_t endValue = 10000;
        uint32_t schemaVersion = 0;
        bool isDefaultValue = false;
        GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_INSERT};
        TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
        TestSimpleDelete(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
        vertexCfg.optType = GMC_OPERATION_REPLACE;
        TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

        // 写版本1数据 - 简单表
        schemaVersion = 3;
        startValue = 0;
        endValue = 20000;
        GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
        TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
        TestSimpleWrite(g_stmt, g_labelNameA, vertexCfg1, schemaVersion, isDefaultValue);

        // 读版本1数据 - 简单表
        GtSimplelabelCfgRead readCfg;
        readCfg.startVal = startValue;
        readCfg.count = endValue;
        TestSimpleScan(g_stmt, g_labelName, readCfg, 0);
        TestSimpleScan(g_stmt, g_labelNameA, readCfg, 0);
   }

    for (int i = 0; i < MAX_CONN_SIZE - existConnNum; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


//018 40 异步消息队列写满
TEST_F(GmddlFunUpg, rel_upgDeg_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWriteAsync(g_stmtAsync, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


//019 41 订阅消息队列写满
TEST_F(GmddlFunUpg, rel_upgDeg_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>> 订阅\n" );
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schemaFile/version_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);

    // 不消费订阅消息
    gIsSnCallbackWait = true;

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
#if defined ENV_EULER
    uint32_t endValue = 10000;
#else
    // 设备开启流控，1S处理一个
    uint32_t endValue = 100;
#endif
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
        return;
    }

    int nSuccInsertCnt = 0;
    for (int i = startValue; i < endValue; i++) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, isDefaultValue);
     
        ret = GmcExecute(g_stmt);
        if (ret != GMERR_OK) {
            break;
        }
        nSuccInsertCnt++;
    }
    AW_FUN_Log(LOG_INFO, ">>> success insert %d records.\n", nSuccInsertCnt);

    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleDelete(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, (char *)"simpleLabel"};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1  
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
#if defined ENV_EULER
    endValue = 20000;
#else
    // 设备开启流控，1S处理一个
    endValue = 200;
#endif
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重新消费订阅消息
    gIsSnCallbackWait = false;
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, nSuccInsertCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>订阅消息检测成功\n");

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 020 42 事务锁资源耗尽
TEST_F(GmddlFunUpg, rel_upgDeg_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 建表
    int i = 0;
    int tableNum = 0;
    char vertexLabelJson[MAX_CONN_SIZE];
    char vertexLabelName[32] = "testT1";
    while (!ret) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            tableNum, tableNum);
        // create vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_testlabelConfig);
        tableNum++;
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %s.", (char *)vertexLabelJson);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %d tables.", tableNum - 1);

    // 先删三分之一的表，不然任何操作，查视图都报内存满错误
    for (i = (tableNum - 1)/3*2; i < tableNum; i++) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
    }

    // 拉起升降级线程，
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgUpDownAml = {0, 0, 0, true, NULL};
    pthread_create(&thr_arr[0], NULL, ThreadUpgradeDownTxAbnormal, (void *)&tArgUpDownAml);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    ASSERT_EQ(GMERR_OK, ret);
    int data_num = 10;
    for (i = 0; i < (tableNum - 1)/3*2; i++) {
        sprintf(vertexLabelName, "testT%u", i);
        for (int j = 0; j < data_num; j++) {
            ret = TestGmcSetVertexProperty_1024(g_stmt, j, vertexLabelName);
            if (ret == GMERR_OK) {
                int affectRows = 0;
                ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, affectRows);

            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                ret = GmcTransRollBack(g_conn);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                if ((i * j)%8000 == 0 && j > 0) {
                    AW_FUN_Log(LOG_INFO, "label_index 1: %d, j: %d, ret : %d\n", i, j, ret);
                }
            }

            ret = TestGmcDelVertexProperty_1024(g_stmt, j, vertexLabelName);
            if (ret == GMERR_OK) {
                int affectRows = 0;
                ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(1, affectRows);

            } else if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
                ret = testGmcGetLastError(NULL);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                ret = GmcTransRollBack(g_conn);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                if ((i * j)%8000 == 0 && j > 0) {
                    AW_FUN_Log(LOG_INFO, "label_index 2: %d, j: %d, ret : %d\n", i, j, ret);
                }
            }
        }
    }

    // MS事务commit
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGmcSetVertexProperty_1024(g_stmt, 11, "testT11");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < (tableNum - 1) / 3 * 2; i++) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    sleep(2);
    // 后台线程结束
    g_ThrFlag = false;
    pthread_join(thr_arr[0], &thr_ret[0]);

    // 删表,释放空间
    i = 0;
    ret = 0;
    while (!ret) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
        i++;
    }
}

// 021 43 资源池耗尽
TEST_F(GmddlFunUpg, rel_upgDeg_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *labelName = "bfd_sess_index";
    readJanssonFile("../../schema_file/r21_ndb/gmjson/enp_l3app/bfd_sess_index.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    (void)GmcDestroyResPool(g_stmt, g_resPoolTestName);
    ret = GmcCreateResPool(g_stmt, g_resPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt, g_resPoolTestName, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // 多线程并发,dml可能报内存满错误，结果预期返回类型较多
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgUpDownAml = {0, 0, 0, true, NULL};
    pthread_create(&thr_arr[0], NULL, ThreadUpgradeDownRes, (void *)&tArgUpDownAml);

    for (int i = 0; i < 100; i++) {
        ret = TestGmcInsertVertex_resource(g_stmtAsync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);

        ret = TestGmcDeleteVertex_resource(g_stmtAsync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    sleep(2);
    // 后台线程结束
    g_ThrFlag = false;
    pthread_join(thr_arr[0], &thr_ret[0]);

    ret = GmcUnbindResPoolFromLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 022 44 表空间满
TEST_F(GmddlFunUpg, rel_upgDeg_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>> 订阅\n" );
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schemaFile/version_subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_NULL, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, schemaVersion, GMC_OPERATION_INSERT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_CONNECTION_RESET_BY_PEER);
        return;
    }

    int32_t nSuccInsertCnt = 0;
    int i = 0;
    while (ret == GMERR_OK) {
        TestSimpleT1SetPk(g_stmt, i);
        TestSimpleT1OldVersionSetProperty(g_stmt, i, isDefaultValue);
     
        ret = GmcExecute(g_stmt);
        //  AC上可能报错订阅通道满
        if (ret == GMERR_SUB_PUSH_QUEUE_FULL) {
            usleep(800);
            ret = GMERR_OK;
            continue;
        }
        if (ret != GMERR_OK) {
            break;
        }
        i++;
        nSuccInsertCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_FUN_Log(LOG_INFO, ">>> success insert %d records ret: %d.\n", nSuccInsertCnt, ret);

    GtSimplelabelCfgT vertexCfgDel = {nSuccInsertCnt/2, (uint32_t)nSuccInsertCnt, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleDelete(g_stmt, g_labelName, vertexCfgDel, schemaVersion, isDefaultValue);

    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleDelete(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, (char *)"simpleLabel"};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 1  
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAll();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 023 46 文件超大 1.批量升级表，存在有超大的表文件，预期该表升级失败。重新升级其它表，升级成功。
TEST_F(GmddlFunUpg, rel_upgDeg_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 文件超过1M大小
    char *testSchema = NULL;
    int nFileSize = readJanssonFile("./schemaFileUp1M/generalLabelNode.gmjson", &testSchema);
    AW_FUN_Log(LOG_INFO, ">>> filesize:%d strlen:%d\n", nFileSize, strlen(testSchema));
    free(testSchema);

    // 升级版本 
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
    char *expectValue1 = (char *)"upgrade unsuccessfully";
    char *schemaUpFile = (char *)"./schemaFileUp1M/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 文件略小于1M
    char *testSchema2 = NULL;
    int nFileSize2 = readJanssonFile("./schemaFileUp1MOK/generalLabelNode.gmjson", &testSchema2);
    AW_FUN_Log(LOG_INFO, ">>> filesize:%d strlen:%d\n", nFileSize2, strlen(testSchema2));
    free(testSchema2);
    ASSERT_LT(nFileSize2, (1024 * 1024));

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期成功\n");
    char *expectValue2 = (char *)"upgrade successfully";
    char *schemaUpFile2 = (char *)"./schemaFileUp1MOK/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile2, expectValue2, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 024 47 文件丢失或被删除 文件丢失
TEST_F(GmddlFunUpg, rel_upgDeg_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 建表
    int i = 0;
    int tableNum = 101;
    char vertexLabelJson[MAX_CONN_SIZE];
    char vertexLabelName[32] = "testT1";
    for (i = 0; i < tableNum; i++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        // create vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_testlabelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %s.", (char *)vertexLabelJson);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %d tables.", tableNum);

    // 注入故障：文件丢失
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, MvFileThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
    char *expectValue1 = (char *)"upgrade unsuccessfully";
    char *schemaUpFile = (char *)"./schemaFileUp100/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待故障线程
    pthread_join(FLT, NULL);
    MvFileBack();

    // 升级版本 
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期成功\n");
    char *expectValue2 = (char *)"upgrade successfully";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue2, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    for (i = 0; i < tableNum; i++) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}


// 025 48 文件丢失或被删除 目录丢失
TEST_F(GmddlFunUpg, rel_upgDeg_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    // 建表
    int i = 0;
    int tableNum = 101;
    char vertexLabelJson[MAX_CONN_SIZE];
    char vertexLabelName[32] = "testT1";
    for (i = 0; i < tableNum; i++) {
        sprintf(vertexLabelJson, "[{\
        \"type\":\"record\",\
        \"name\":\"testT%u\",\
        \"fields\":\
            [{\"name\":\"F0\", \"type\":\"uint8\", \"nullable\":false},\
            {\"name\":\"F1\", \"type\":\"float\",  \"nullable\":false},\
            {\"name\":\"F2\", \"type\":\"double\", \"nullable\":false},\
            {\"name\":\"F3\", \"type\":\"string\", \"nullable\":false, \"size\":100}],\
        \"keys\":\
            [{\"node\":\"testT%u\",\
            \"name\":\"T10_PK\",\
            \"fields\":[\"F0\"],\
            \"index\":{\"type\":\"primary\"},\
            \"constraints\":{\"unique\":true}}]\
        }]",
            i, i);
        // create vertex
        ret = GmcCreateVertexLabel(g_stmt, vertexLabelJson, g_testlabelConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %s.", (char *)vertexLabelJson);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>> success create %d tables.", tableNum);

    // 注入故障：文件丢失
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, MvDirThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 升级版本 
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期失败\n");
    char *expectValue1 = (char *)"upgrade unsuccessfully";
    char *schemaUpFile = (char *)"./schemaFileUp100/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待故障线程
    pthread_join(FLT, NULL);
    MvDirBack();

    // 升级版本 
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 预期成功\n");
    char *expectValue2 = (char *)"upgrade successfully";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue2, NULL, (char *)"batch");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删表
    for (i = 0; i < tableNum; i++) {
        sprintf(vertexLabelName, "testT%u", i);
        ret = GmcDropVertexLabel(g_stmt, vertexLabelName);
    }
}


// 026 53 使用已关闭资源：连接、stmt
TEST_F(GmddlFunUpg, rel_upgDeg_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t endValueMid = endValue;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);
    uint32_t endMidValue = endValue;

    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 升级版本 1  gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(stmt1, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(stmt1, g_labelName, vertexCfg, 0, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg1 = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(stmt1, g_labelName, readCfg1, schemaVersion);

    GtSimplelabelCfgT vertexCfgDel1 = {(int32_t)endMidValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleDelete(stmt1, g_labelName, vertexCfgDel1, schemaVersion, isDefaultValue);
    TestSimpleWrite(stmt1, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(stmt1, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(stmt1, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(stmt1, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(stmt1, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt1, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt1, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 低版本读写
    TestSimpleWrite(stmt2, g_labelName, vertexCfg, 0, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(stmt2, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(stmt2, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    ret = GtSpeciallabelStructWrite(stmt2, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt2, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 升级版本 1
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    GmcConnT *conn3 = NULL;
    GmcStmtT *stmt3 = NULL;
    ret = testGmcConnect(&conn3, &stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写 读 版本1数据 - 简单表
    TestSimpleWrite(stmt3, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    TestSimpleDelete(stmt3, g_labelName, vertexCfgDel1, schemaVersion, isDefaultValue);
    TestSimpleWrite(stmt3, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);

    sleep(5);  //等待基础版本线程覆盖数据

    // 读版本1数据 - 简单表
    TestSimpleScan(stmt3, g_labelName, readCfg1, schemaVersion);

    // 写 读 版本1数据 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(stmt3, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(stmt3, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    ret = GtSpeciallabel2StructWrite(stmt3, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(stmt3, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn3, stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// 027 58 在线修改系统配置为异常值
TEST_F(GmddlFunUpg, rel_upgDeg_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int32_t ret = 0;

    ret = TestGimportGmjson();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建基础表 gmsysview -q V\$CATA_VERTEX_LABEL_INFO
     char *expectValue = (char *)"0 files failed";
    ret = TestGmimportVertex("./schemaFileOld/", expectValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    // 写 读 基础数据 - 简单表
    int32_t startValue = 0;
    uint32_t endValue = 10000;
    uint32_t endValueMid = endValue;
    uint32_t schemaVersion = 0;
    bool isDefaultValue = false;
    GtSimplelabelCfgT vertexCfg = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, schemaVersion, isDefaultValue);

    // 写 读 基础数据 - 一般复杂表
    uint8_t ipValue = 0;
    char *bytesValue = (char *)"123456";
    char *stringValue = (char *)"ABCDEF";
    GtGeneralLabelCfg vertexCfgGen = {startValue, endValue, 1, 3, 3, 0, GMC_OPERATION_REPLACE, {false, false}};
    vertexCfgGen.schemaVersion = 0;
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue);
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 基础数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS = {startValue, endValue, 0, 1, 0, 3, 3, 0, GMC_OPERATION_INSERT};
    ret = GtSpeciallabelStructWrite(g_stmt, vertexCfgS, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 多线程并发dml操作
    pthread_t thr_arr[10];
    void *thr_ret[10];

    g_ThrFlag = true;
    GtSimpleThreCfg tArgWrite = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[0], NULL, ThreadSimpleWrite, (void *)&tArgWrite);

    GtSimpleThreCfg tArgRead = {schemaVersion, startValue, endValue, true, g_labelName};
    pthread_create(&thr_arr[1], NULL, ThreadSimpleRead, (void *)&tArgRead);

    GtSimpleThreCfg tArgGenWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[2], NULL, ThreadGeneralWrite, (void *)&tArgGenWrite);

    GtSimpleThreCfg tArgGenRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[3], NULL, ThreadGeneralRead, (void *)&tArgGenRead);

    GtSimpleThreCfg tArgSpeWrite = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[4], NULL, ThreadSpecialWrite, (void *)&tArgSpeWrite);

    GtSimpleThreCfg tArgSpeRead = {schemaVersion, startValue, endValue, true, NULL};
    pthread_create(&thr_arr[5], NULL, ThreadSpecialRead, (void *)&tArgSpeRead);

    sleep(2);

    // 注入故障：修改配置项异常值
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>注入故障：修改配置项异常值\n");
    pthread_t FLT;
    ret = pthread_create(&FLT, NULL, OnlineModifyThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    // 升级版本 1  gmddl -c alter -f ./schemaFileUp1/batch1.txt -u batch -ns public
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级\n");
    char *expectValue1 = (char *)"upgrade successfully";
    char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
    ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成\n");

    // 恢复正常
    pthread_join(FLT, NULL);

    // 写 读 版本1数据 - 简单表
    schemaVersion = 3;
    startValue = 0;
    endValue = 20000;
    GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);
    vertexCfg.count = endValue;
    TestSimpleWrite(g_stmt, g_labelName, vertexCfg, 0, isDefaultValue);

    // 读版本1数据 - 简单表
    GtSimplelabelCfgRead readCfg = {startValue, endValue, 0, 0, {false, true, true}};
    TestSimpleScan(g_stmt, g_labelName, readCfg, schemaVersion);

    // 写 读 版本1数据 - 一般复杂表
    GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, true}};
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
    vertexCfgGen.count = endValue;   // 覆盖写一遍基础表 -- 方便查询 - 一般复杂表
    TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen, ipValue, bytesValue, stringValue, isDefaultValue); 
    TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

    // 写 读 版本1数据 - 特殊复杂表
    GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
    ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 注入故障：修改配置项异常值
    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>注入故障：修改配置项异常值\n");
    pthread_t FLT1;
    ret = pthread_create(&FLT1, NULL, OnlineModifyThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);

    AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
    ret = TestDownGradeAllAsync();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 恢复正常
    pthread_join(FLT1, NULL);

    // 检查降级
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    // 后台线程结束
    g_ThrFlag = false;
    for (int i = 0; i <= 5; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    // 删表
    ret = TestDropAll();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
