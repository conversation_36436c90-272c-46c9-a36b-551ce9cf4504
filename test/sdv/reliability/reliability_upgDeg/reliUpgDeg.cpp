/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :表结构升降级工具升级功能测试
 Author       : wuxiaochun wx753022
 Modification :
001.批量升级，一次升级多张表
002.升级一张简单表，含所有支持的定长字段
003.升级一张一般复杂表，含所有支持的字段和节点
004.升级一张特殊复杂表，含所有支持的字段和节点
005.升级一张表，指定表名，与schema文件一致 
006.兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
007.非兼容性升级一张表，指定表名，与schema文件不一致，与存在的表名一致
008.多个线程并发升级同一张表的同一个版本
009.多个线程并发批量升级同一个配置文件
010.多个线程并发升级不同表
011.写满catalog系统内存后再进行表升级操作
012.升级时同步进行truncate操作
013.写满存储区内存后进行表升级
 Date         : 2023/03/13
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>
#include "gtest/gtest.h"
#include "GmddlFunUpg.h"

// 客户端退出
int main()
{
    GmcConnT *g_conn;
    GmcStmtT *g_stmt;
    int ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 创建同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ReadGmjson((char *)"./schemaFileOld/SpecialTableSchema.gmjson");
    ReadGmjson((char *)"./schemaFileUp1/SpecialTableSchemaUpgrade.gmjson");

    for (int i = 0; i  < 20; i++) {
        // 升级版本 1
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级 in reliUpgDeg\n");
        char *expectValue1 = (char *)"upgrade successfully";
        char *schemaUpFile = (char *)"./schemaFileUp1/batch1.txt";
        ret = TestUpdateVertexLabel(schemaUpFile, expectValue1, NULL, (char *)"batch");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>升级完成 in reliUpgDeg\n");

        bool isDefaultValue = false;
        uint8_t ipValue = 0;
        char *bytesValue = (char *)"123456";
        char *stringValue = (char *)"ABCDEF";

        // 写 读 版本1数据 - 简单表
        uint32_t schemaVersion = 3;
        int32_t startValue = 0;
        uint32_t endValue = 20000;
        GtSimplelabelCfgT vertexCfg1 = {startValue, endValue, 0, 1, GMC_OPERATION_REPLACE};
        TestSimpleWrite(g_stmt, g_labelName, vertexCfg1, schemaVersion, isDefaultValue);

        // 读版本1数据 - 简单表
        GtSimplelabelCfgRead readCfg;
        readCfg.startVal = startValue;
        readCfg.count = endValue;
        TestSimpleScan(g_stmt, g_labelName, readCfg, 0);

        // 写 读 版本1数据 - 一般复杂表
        GtGeneralLabelCfg vertexCfgGen1 = {startValue, endValue, 1, 3, 3, 2, GMC_OPERATION_REPLACE, {false, false}};
        TestGeneralT2NewOldVersionWrite(g_stmt, vertexCfgGen1, ipValue, bytesValue, stringValue, isDefaultValue);
        TestGeneralT2NewOldVersionRead(g_stmt, vertexCfgGen1, 0, ipValue, bytesValue, stringValue, isDefaultValue);

        // 写 读 版本1数据 - 特殊复杂表
        GtSpeciallabelCfgT vertexCfgS2 = {(int32_t)startValue, endValue, 0, 1, 0, 3, 3, 1, GMC_OPERATION_REPLACE};
        ret = GtSpeciallabel2StructWrite(g_stmt, vertexCfgS2, bytesValue, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestSpecialT3NewOldVersionGeneralRead(g_stmt, vertexCfgS2, 0, bytesValue, stringValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>降级\n");
        ret = TestDownGradeAll();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 检查降级
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName, 3, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName3, 2, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        ret = GmcPrepareStmtByLabelNameWithVersion(g_stmt, g_labelName2, 1, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
        AW_FUN_Log(LOG_INFO, ">>>>>>>>>>sleep 1s in reliUpgDeg\n");

    }


    system("gmsysview count");

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}


