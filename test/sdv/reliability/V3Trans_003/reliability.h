extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sub = NULL;
void *g_label = NULL, *g_label_2 = NULL, *g_label_3 = NULL;
char *g_schema = NULL, *g_schema_2 = NULL, *g_schema_3 = NULL;
char *g_sub_info = NULL;
GmcConnT *g_subChan = NULL;
int affectRows;
unsigned int len;
int g_data_num = 50;      // 10000
int g_delete_num = 0;     // 10000
int g_try_write_num = 0;  // 10000
uint32_t g_total_write_num = 0;
int g_thread_num = 2;
int g_thread_wait = 0;
int g_confliction_num = 5000;
const char *g_label_config = "{\"max_record_num\" : 50000000,\"defragmentation\":true }";

typedef struct TagThreadDataT {
    int index;
    bool isUnique;
} ThreadDataT;

typedef struct TagFlowControlDataT {
    int insert_flowControl_num;
    int replace_flowControl_num;
    int delete_flowControl_num;
    int scan_flowControl_num;

    int insert_succ_num;
    int replace_succ_num;
    int delete_succ_num;
    int scan_succ_num;
} FlowControlDataT;
FlowControlDataT g_flowControl = {0}, g_flowControl2 = {0}, g_flowControl3 = {0}, g_flowControl_arr[10] = {0};

char g_label_name[] = "ip4forward";
char g_lable_PK[] = "ip4_key";
char g_lable_SK_non_unique[] = "ip4_group_id";
char g_lable_SK_unique[] = "ip4_app_id";

char g_label_name_arp[] = "arp";
char g_lable_PK_arp[] = "arp_key";
char g_lable_SK_non_unique_arp[] = "arp_hashcluster_key";
char g_lable_SK_unique_arp[] = "arp_hashcluster_uniq_key";

char kv_table_name_sync[] = "KV0";
#define MAX_CHAR_COUNT_26 26

char g_label_name_local[] = "longLocalKey";
char g_lable_SK_local[] = "long_local";

char g_label_name_hashcluster[] = "longHashcluster";
char g_lable_SK_hashcluster[] = "long_cluster";

char g_label_name_string_bytes[] = "long_string_bytes";
char g_lable_SK_string[] = "string_key";
char g_lable_SK_bytes[] = "bytes_key";

char g_label_name_for_ls[] = "ip4forward_for_ls";

char g_label_name_all_index[] = "all_index";
char g_label_name_all_index_multi_local[] = "all_index_multi_local";
char g_label_name_all_index_time[] = "all_index_time";
char g_lable_all_index_PK[] = "PK";
char g_lable_all_index_SK_localhash[] = "LocalHash";
char g_lable_all_index_SK_localhash_non_unique[] = "LocalHash_NonUniq";
char g_lable_all_index_SK_local[] = "Local";
char g_lable_all_index_SK_local_multifield[] = "LocalMultiField";
char g_lable_all_index_SK_hashcluster[] = "HashCluster";
char g_lable_all_index_SK_hashcluster_non_unique[] = "HashCluster_NonUniq";
char g_lable_all_index_SK_lpm4[] = "LPM4";

const char *label_config_json = "{\"max_record_num\":90000000,\"defragmentation\":true }";

const char *g_subName = "subVertexLabel";
const char *g_subConnName = "subConnName";

pthread_barrier_t barrier;
pthread_mutex_t g_threadLock;
#define CS_NEVER_TIMEOUT 0xFFFFFFFF  // client request never timeout

using namespace std;

#define TEST_INFO_print(info, recordId, mod, threadId)                                         \
    do {                                                                                       \
        if (recordId % mod == 0) {                                                             \
            fprintf(stdout, "[%s] record Id : %d, threadId : %d\n", info, recordId, threadId); \
        }                                                                                      \
    } while (0)

#define TEST_INFO(info, recordId, mod, threadId) \
    do {                                         \
        if (recordId % mod == 0) {               \
        }                                        \
    } while (0)

int sem1 = 0;
static void sig_usr(int sign)
{
    if (sign == SIGUSR1) {
        sem1++;
        printf("--------------child pro recvd SIGUSR1--------------\n");
    }
}

static void signal_register(int sig)
{
    int ret = 0;
    struct sigaction act;
    act.sa_handler = sig_usr;
    sigemptyset(&act.sa_mask);
    act.sa_flags = 0;
    ret = sigaction(sig, &act, NULL);
    if (ret != 0) {
        printf("signal register fail.ret=%d\n", ret);
        exit(1);
    }
    return;
}

void *thread_replace_all_index(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    for (i = g_data_num * threadData.index; i < g_data_num + g_data_num * threadData.index; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, replace ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_all_index(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    //主键
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        int32_t value_int32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_PK, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //唯一的localhash
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_localhash, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //非唯一的localhash
    int16_t value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_localhash_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    //唯一的hashcluster
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_hashcluster, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //非唯一的hashcluster
    value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_hashcluster_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    // lpm4
    uint32_t vrId = 1, vrfIndex = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_lpm4, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_localhash(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_localhash, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_localhash_non_unique(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    int16_t value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_localhash_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_hashcluster(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_hashcluster, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_hashcluster_non_uniq(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    int16_t value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_hashcluster_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_lpm4(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t vrId = 1, vrfIndex = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    do {
        ret = GmcGetVertexCount(stmt, g_label_name_all_index, g_lable_all_index_SK_lpm4, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_create_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = {0};
    sprintf(schema_path, "./multi_vertexlabel/all_index_%d.gmjson", threadData.index);
    readJanssonFile(schema_path, &schema_info);
    EXPECT_NE((void *)NULL, schema_info);
    sprintf(labelName, "all_index_%d", threadData.index);

    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_info, NULL);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_info);

    for (i = 0; i < g_data_num; i++) {
        // TEST_INFO("REPLACE", i, 1000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_get_vertex_count_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    //主键
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        int32_t value_int32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_PK, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //唯一的localhash
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_localhash, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //非唯一的localhash
    int16_t value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    do {
        ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_localhash_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    //唯一的hashcluster
    for (i = 0; i < g_data_num * g_thread_num; i++) {
        TEST_INFO("GetVertexCount", i, 10000, threadData.index);
        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        do {
            ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_hashcluster, &count);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, count);
    }

    //非唯一的hashcluster
    value_int16 = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    do {
        ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_hashcluster_non_unique, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    // lpm4
    uint32_t vrId = 1, vrfIndex = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    count = 0;
    do {
        ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_lpm4, &count);
    } while (ret == GMERR_LOCK_NOT_AVAILABLE);
    EXPECT_EQ(GMERR_OK, ret);
    // EXPECT_EQ(g_data_num * g_thread_num, count);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_replace_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index % g_thread_num);

    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index % g_thread_num);

    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16 = 0;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(g_data_num, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_all_index(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    for (i = 0; i < g_confliction_num /*g_data_num * g_thread_num*/; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16 = i;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        do {
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(g_data_num * g_thread_num / g_confliction_num, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_delete_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index % g_thread_num);

    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("DELETE", i, 10000, threadData.index);
        int16_t value_int16 = 0;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(g_data_num, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN", i, 1000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_delete_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index % g_thread_num);
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("LOCAL DELETE", i, 1000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_drop_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    // TEST_INFO("DROP", 0, 1000, threadData.index);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_create_dml_scan_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = {0};
    sprintf(schema_path, "./multi_vertexlabel/all_index_%d.gmjson", threadData.index);
    readJanssonFile(schema_path, &schema_info);
    EXPECT_NE((void *)NULL, schema_info);
    sprintf(labelName, "all_index_%d", threadData.index);

    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_info, NULL);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_info);

    // 写
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // localhash扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCALHASH", i, 10000, threadData.index);

        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    // localhash更新
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // 主键删
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("DELETE", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(0, cnt);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_create_dml_scan_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    // 写
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            int res = testGmcGetLastError();
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
    }

    // localhash扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCALHASH", i, 10000, threadData.index);

        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
    }

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    // localhash更新
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16 = 0;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
    }

    // 主键删
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("DELETE", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(0, cnt);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_create_max_record_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = {0};
    sprintf(schema_path, "./multi_vertexlabel/all_index_%d.gmjson", threadData.index);
    readJanssonFile(schema_path, &schema_info);
    EXPECT_NE((void *)NULL, schema_info);
    sprintf(labelName, "all_index_%d", threadData.index);

    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_info, g_label_config);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_info);

    // 写
    for (i = 0; i < g_data_num; i++) {
        // TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            pthread_mutex_lock(&g_threadLock);
            g_total_write_num += i;
            pthread_mutex_unlock(&g_threadLock);
            printf("thread : %d, i : %d, ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_replace_all_index_max_record_async(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    for (i = g_data_num * threadData.index; i < g_data_num + g_data_num * threadData.index; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // GmcAsyncRequestDoneContextT replaceRequestCtx;
        // replaceRequestCtx.replaceCb =  replace_vertex_callback_verify;
        // replaceRequestCtx.userData =  NULL;
        // ret = GmcExecuteAsync(stmt, &replaceRequestCtx);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_INSUFFICIENT_RESOURCES) {
            pthread_mutex_lock(&g_threadLock);
            g_total_write_num += i;
            pthread_mutex_unlock(&g_threadLock);
            printf("thread : %d, i : %d, replace ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        // EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_all_index_max_record(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    // 主键更新
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16 = 0;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, update ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_read_delete_read_write_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);
#if 0
    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < count ; i++) {
        //TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }
#endif
    // localhash删
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < count; i++) {
        // TEST_INFO("DELETE", i, 10000, threadData.index);

        int16_t value_int16 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("delete %d failed, ret : %d, thread id : %d\n", i, ret, threadData.index);
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    pthread_mutex_lock(&g_threadLock);
    g_thread_wait++;
    pthread_mutex_unlock(&g_threadLock);
    while (g_thread_wait < g_thread_num) {
    }
#if 0
    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < count ; i++) {
        //TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(0, cnt);
    }
#endif
    // 写
    for (i = 0; i < count; i++) {
        // TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if ((i < count - 1) && (ret == GMERR_OUT_OF_MEMORY)) {
            pthread_mutex_lock(&g_threadLock);
            g_total_write_num += i;
            pthread_mutex_unlock(&g_threadLock);
            printf("thread : %d, i : %d, ret : %d, count : %d\n", threadData.index, i, ret, count);
            break;
        }
        if (i == count - 1) {
            pthread_mutex_lock(&g_threadLock);
            g_total_write_num += i;
            pthread_mutex_unlock(&g_threadLock);
            if (ret == GMERR_OUT_OF_MEMORY) {
                printf("thread : %d, i : %d, ret : %d, count : %d\n", threadData.index, i, ret, count);
                break;
            }
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("update thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);

    // localhash更新
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        int16_t value_int16 = i;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < count; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_delete_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < count; i++) {
        TEST_INFO("DELETE LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_non_unique_localhash_delete_multi_label(void *args)
{
    uint32_t i, circle, j;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    // localhash删
    for (i = 0; i < g_confliction_num; i++) {
        //非唯一的localhash
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int16_t value_int16 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_localhash_non_unique, &count);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("thread : %d, i : %d, labelName : %s, count : %d\n", threadData.index, i, labelName, count);

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(count, cnt);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_create_non_unique_dml_scan_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char *schema_info = NULL;
    char schema_path[512] = {0};
    char labelName[128] = {0};
    sprintf(schema_path, "./multi_vertexlabel/all_index_%d.gmjson", threadData.index);
    readJanssonFile(schema_path, &schema_info);
    EXPECT_NE((void *)NULL, schema_info);
    sprintf(labelName, "all_index_%d", threadData.index);

    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_info, NULL);
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema_info);

    // 写
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // localhash扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCALHASH", i, 10000, threadData.index);

        uint64_t value_uint64 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    // localhash更新
    for (i = 0; i < g_confliction_num; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);

        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int16_t value_int16 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, labelName, g_lable_all_index_SK_localhash_non_unique, &count);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_localhash_non_unique);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(count, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
    }

    // 主键删
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("DELETE", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }

    // 主键读
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < g_data_num; i++) {
        TEST_INFO("PK READ", i, 10000, threadData.index);

        int32_t value_int32 = i;
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(0, cnt);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_full_label_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int arrLen = 1;

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = NULL;
    items_sc[0].rValue = NULL;
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(count, cnt);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_full_label_desc_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, labelName, count);

    // local扫描
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int arrLen = 1;

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = NULL;
    items_sc[0].rValue = NULL;
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_DESC;

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(count, cnt);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_full_label_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index, count);

    // local扫描
    unsigned int arrLen = 1;

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = NULL;
    items_sc[0].rValue = NULL;
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    // EXPECT_EQ(count, cnt);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_full_label_desc_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index, count);

    // local扫描
    unsigned int arrLen = 1;

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = NULL;
    items_sc[0].rValue = NULL;
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_DESC;

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetKeyRange(stmt, items_sc, arrLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true || ret != GMERR_OK) {
            break;
        }
        cnt++;
    }
    EXPECT_EQ(count, cnt);

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_condition_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index, count);

    // local扫描
    for (int j = 0; j < 1; j++) {
        printf("----------------------j : %d-----------------------\n", j);
        for (i = 0; i < count; i++) {
            TEST_INFO("SCAN LOCAL", i, 100000, threadData.index);

            uint64_t value = i;
            unsigned int arrLen = 1;
            GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
            leftKeyProps[0].type = GMC_DATATYPE_UINT64;
            leftKeyProps[0].value = &value;
            leftKeyProps[0].size = sizeof(uint64_t);

            GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
            rightKeyProps[0].type = GMC_DATATYPE_UINT64;
            rightKeyProps[0].value = &value;
            rightKeyProps[0].size = sizeof(uint64_t);

            GmcRangeItemT items_sc[arrLen];
            items_sc[0].lValue = &leftKeyProps[0];
            items_sc[0].rValue = &rightKeyProps[0];
            items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
            items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
            items_sc[0].order = GMC_ORDER_ASC;

            ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetKeyRange(stmt, items_sc, arrLen);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                EXPECT_EQ(GMERR_OK, ret);
                if (isFinish == true || ret != GMERR_OK) {
                    break;
                }
                cnt++;
            }
            EXPECT_EQ(1, cnt);
            free(leftKeyProps);
            free(rightKeyProps);
        }
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_condition_desc_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index, count);

    // local扫描
    for (i = 0; i < count; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_DESC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        // EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_condition_multi_field_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index_multi_local, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(
        "scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index_multi_local, count);

    // local扫描
    for (i = 0; i < count; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint8_t value_uint8 = 32;
        int16_t value_int16 = i;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i;
        uint32_t vr_id = 1;
        uint32_t vrf_index = 1;
        uint64_t value_uint64 = i;
        unsigned int arrLen = 8;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_INT32;
        leftKeyProps[0].value = &value_int32;
        leftKeyProps[0].size = sizeof(int32_t);
        leftKeyProps[1].type = GMC_DATATYPE_INT32;
        leftKeyProps[1].value = &value_int32;
        leftKeyProps[1].size = sizeof(int32_t);
        leftKeyProps[2].type = GMC_DATATYPE_UINT64;
        leftKeyProps[2].value = &value_uint64;
        leftKeyProps[2].size = sizeof(uint64_t);
        leftKeyProps[3].type = GMC_DATATYPE_INT16;
        leftKeyProps[3].value = &value_int16;
        leftKeyProps[3].size = sizeof(int16_t);
        leftKeyProps[4].type = GMC_DATATYPE_UINT32;
        leftKeyProps[4].value = &vr_id;
        leftKeyProps[4].size = sizeof(uint32_t);
        leftKeyProps[5].type = GMC_DATATYPE_UINT32;
        leftKeyProps[5].value = &vrf_index;
        leftKeyProps[5].size = sizeof(uint32_t);
        leftKeyProps[6].type = GMC_DATATYPE_UINT32;
        leftKeyProps[6].value = &value_uint32;
        leftKeyProps[6].size = sizeof(uint32_t);
        leftKeyProps[7].type = GMC_DATATYPE_UINT8;
        leftKeyProps[7].value = &value_uint8;
        leftKeyProps[7].size = sizeof(uint8_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_INT32;
        rightKeyProps[0].value = &value_int32;
        rightKeyProps[0].size = sizeof(int32_t);
        rightKeyProps[1].type = GMC_DATATYPE_INT32;
        rightKeyProps[1].value = &value_int32;
        rightKeyProps[1].size = sizeof(int32_t);
        rightKeyProps[2].type = GMC_DATATYPE_UINT64;
        rightKeyProps[2].value = &value_uint64;
        rightKeyProps[2].size = sizeof(uint64_t);
        rightKeyProps[3].type = GMC_DATATYPE_INT16;
        rightKeyProps[3].value = &value_int16;
        rightKeyProps[3].size = sizeof(int16_t);
        rightKeyProps[4].type = GMC_DATATYPE_UINT32;
        rightKeyProps[4].value = &vr_id;
        rightKeyProps[4].size = sizeof(uint32_t);
        rightKeyProps[5].type = GMC_DATATYPE_UINT32;
        rightKeyProps[5].value = &vrf_index;
        rightKeyProps[5].size = sizeof(uint32_t);
        rightKeyProps[6].type = GMC_DATATYPE_UINT32;
        rightKeyProps[6].value = &value_uint32;
        rightKeyProps[6].size = sizeof(uint32_t);
        rightKeyProps[7].type = GMC_DATATYPE_UINT8;
        rightKeyProps[7].value = &value_uint8;
        rightKeyProps[7].size = sizeof(uint8_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;
        items_sc[1].lValue = &leftKeyProps[1];
        items_sc[1].rValue = &rightKeyProps[1];
        items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].order = GMC_ORDER_ASC;
        items_sc[2].lValue = &leftKeyProps[2];
        items_sc[2].rValue = &rightKeyProps[2];
        items_sc[2].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[2].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[2].order = GMC_ORDER_ASC;
        items_sc[3].lValue = &leftKeyProps[3];
        items_sc[3].rValue = &rightKeyProps[3];
        items_sc[3].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[3].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[3].order = GMC_ORDER_ASC;
        items_sc[4].lValue = &leftKeyProps[4];
        items_sc[4].rValue = &rightKeyProps[4];
        items_sc[4].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[4].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[4].order = GMC_ORDER_ASC;
        items_sc[5].lValue = &leftKeyProps[5];
        items_sc[5].rValue = &rightKeyProps[5];
        items_sc[5].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[5].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[5].order = GMC_ORDER_ASC;
        items_sc[6].lValue = &leftKeyProps[6];
        items_sc[6].rValue = &rightKeyProps[6];
        items_sc[6].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[6].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[6].order = GMC_ORDER_ASC;
        items_sc[7].lValue = &leftKeyProps[7];
        items_sc[7].rValue = &rightKeyProps[7];
        items_sc[7].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[7].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[7].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_multi_local, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local_multifield);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_scan_condition_multi_field_desc_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index_multi_local, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf(
        "scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index_multi_local, count);

    // local扫描
    for (i = 0; i < count; i++) {
        TEST_INFO("SCAN LOCAL", i, 10000, threadData.index);

        uint8_t value_uint8 = 32;
        int16_t value_int16 = i;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i;
        uint32_t vr_id = 1;
        uint32_t vrf_index = 1;
        uint64_t value_uint64 = i;
        unsigned int arrLen = 8;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_INT32;
        leftKeyProps[0].value = &value_int32;
        leftKeyProps[0].size = sizeof(int32_t);
        leftKeyProps[1].type = GMC_DATATYPE_INT32;
        leftKeyProps[1].value = &value_int32;
        leftKeyProps[1].size = sizeof(int32_t);
        leftKeyProps[2].type = GMC_DATATYPE_UINT64;
        leftKeyProps[2].value = &value_uint64;
        leftKeyProps[2].size = sizeof(uint64_t);
        leftKeyProps[3].type = GMC_DATATYPE_INT16;
        leftKeyProps[3].value = &value_int16;
        leftKeyProps[3].size = sizeof(int16_t);
        leftKeyProps[4].type = GMC_DATATYPE_UINT32;
        leftKeyProps[4].value = &vr_id;
        leftKeyProps[4].size = sizeof(uint32_t);
        leftKeyProps[5].type = GMC_DATATYPE_UINT32;
        leftKeyProps[5].value = &vrf_index;
        leftKeyProps[5].size = sizeof(uint32_t);
        leftKeyProps[6].type = GMC_DATATYPE_UINT32;
        leftKeyProps[6].value = &value_uint32;
        leftKeyProps[6].size = sizeof(uint32_t);
        leftKeyProps[7].type = GMC_DATATYPE_UINT8;
        leftKeyProps[7].value = &value_uint8;
        leftKeyProps[7].size = sizeof(uint8_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_INT32;
        rightKeyProps[0].value = &value_int32;
        rightKeyProps[0].size = sizeof(int32_t);
        rightKeyProps[1].type = GMC_DATATYPE_INT32;
        rightKeyProps[1].value = &value_int32;
        rightKeyProps[1].size = sizeof(int32_t);
        rightKeyProps[2].type = GMC_DATATYPE_UINT64;
        rightKeyProps[2].value = &value_uint64;
        rightKeyProps[2].size = sizeof(uint64_t);
        rightKeyProps[3].type = GMC_DATATYPE_INT16;
        rightKeyProps[3].value = &value_int16;
        rightKeyProps[3].size = sizeof(int16_t);
        rightKeyProps[4].type = GMC_DATATYPE_UINT32;
        rightKeyProps[4].value = &vr_id;
        rightKeyProps[4].size = sizeof(uint32_t);
        rightKeyProps[5].type = GMC_DATATYPE_UINT32;
        rightKeyProps[5].value = &vrf_index;
        rightKeyProps[5].size = sizeof(uint32_t);
        rightKeyProps[6].type = GMC_DATATYPE_UINT32;
        rightKeyProps[6].value = &value_uint32;
        rightKeyProps[6].size = sizeof(uint32_t);
        rightKeyProps[7].type = GMC_DATATYPE_UINT8;
        rightKeyProps[7].value = &value_uint8;
        rightKeyProps[7].size = sizeof(uint8_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_DESC;
        items_sc[1].lValue = &leftKeyProps[1];
        items_sc[1].rValue = &rightKeyProps[1];
        items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[1].order = GMC_ORDER_DESC;
        items_sc[2].lValue = &leftKeyProps[2];
        items_sc[2].rValue = &rightKeyProps[2];
        items_sc[2].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[2].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[2].order = GMC_ORDER_DESC;
        items_sc[3].lValue = &leftKeyProps[3];
        items_sc[3].rValue = &rightKeyProps[3];
        items_sc[3].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[3].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[3].order = GMC_ORDER_DESC;
        items_sc[4].lValue = &leftKeyProps[4];
        items_sc[4].rValue = &rightKeyProps[4];
        items_sc[4].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[4].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[4].order = GMC_ORDER_DESC;
        items_sc[5].lValue = &leftKeyProps[5];
        items_sc[5].rValue = &rightKeyProps[5];
        items_sc[5].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[5].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[5].order = GMC_ORDER_DESC;
        items_sc[6].lValue = &leftKeyProps[6];
        items_sc[6].rValue = &rightKeyProps[6];
        items_sc[6].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[6].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[6].order = GMC_ORDER_DESC;
        items_sc[7].lValue = &leftKeyProps[7];
        items_sc[7].rValue = &rightKeyProps[7];
        items_sc[7].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[7].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[7].order = GMC_ORDER_DESC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_multi_local, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local_multifield);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true || ret != GMERR_OK) {
                break;
            }
            cnt++;
        }
        EXPECT_EQ(1, cnt);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_delete_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index, count);

    // local删除
    for (i = 0; i < count; i++) {
        TEST_INFO("DELETE LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_replace_all_index_multi_local(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    for (i = g_data_num * threadData.index; i < g_data_num + g_data_num * threadData.index; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_multi_local, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, replace ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_replace_all_index_time(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    for (i = g_data_num * threadData.index; i < g_data_num + g_data_num * threadData.index; i++) {
        TEST_INFO("REPLACE", i, 10000, threadData.index);
        time_t t = time(NULL);
        if (i % 10000 == 0) {
            printf("replace time is : %ld\n", t);
        }
        int16_t value_int16;
        if (threadData.isUnique) {
            value_int16 = i;
        } else {
            value_int16 = i % g_confliction_num;
        }
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_time, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_TIME, &t, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, replace ret : %d\n", threadData.index, i, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_all_index_time(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_time, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index_time, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index_time, count);

    for (i = 0; i < count; i++) {
        TEST_INFO("UPDATE", i, 10000, threadData.index);
        time_t t = time(NULL);
        if (i % 10000 == 0) {
            printf("update time is : %ld\n", t);
        }
        int16_t value_int16 = i;
        int32_t value_int32 = i;
        uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
        uint64_t value_uint64 = i;
        uint8_t maskLen = 32;
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_time, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_TIME, &t, sizeof(uint64_t));
        EXPECT_EQ(GMERR_OK, ret);
        do {
            ret = GmcExecute(stmt);
        } while (ret == GMERR_LOCK_NOT_AVAILABLE);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, update ret : %d\n", threadData.index, i, ret);
        }
        ret = (ret == GMERR_OUT_OF_MEMORY ? GMERR_OK : ret);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_local_delete_time_one_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_time, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, g_label_name_all_index_time, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    // printf("scan thread : %d, labelName : %s, count : %d\n", threadData.index, g_label_name_all_index_time, count);

    // local删除
    for (i = 0; i < count; i++) {
        TEST_INFO("DELETE LOCAL", i, 10000, threadData.index);

        uint64_t value = i;
        unsigned int arrLen = 1;
        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT64;
        leftKeyProps[0].value = &value;
        leftKeyProps[0].size = sizeof(uint64_t);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT64;
        rightKeyProps[0].value = &value;
        rightKeyProps[0].size = sizeof(uint64_t);

        GmcRangeItemT items_sc[arrLen];
        items_sc[0].lValue = &leftKeyProps[0];
        items_sc[0].rValue = &rightKeyProps[0];
        items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items_sc[0].order = GMC_ORDER_ASC;

        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name_all_index_time, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_all_index_SK_local);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            printf("thread : %d, i : %d, delete ret : %d\n", threadData.index, i, ret);
        }
        ret = (ret == GMERR_OUT_OF_MEMORY ? GMERR_OK : ret);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        // EXPECT_EQ(1, affectRows);
        free(leftKeyProps);
        free(rightKeyProps);
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}

void *thread_update_pk_all_index_multi_label(void *args)
{
    uint32_t i, circle;
    int ret, affectRows;
    ThreadDataT threadData = *((ThreadDataT *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[threadData.index % MAX_EP_ASYNC_NUM].userEpollFd;
    do {
        //创建连接，开启心跳
        ret = TestYangGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, &connOptions);
    } while (ret != GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier);

    char labelName[128] = {0};
    sprintf(labelName, "all_index_%d", threadData.index % g_thread_num);

    // 反复更新主键
    for (circle = 0; circle < 10000000; circle++) {
        TEST_INFO("UPDATE circle", circle, 10000, threadData.index);
        for (i = 0; i < g_data_num; i++) {
            int32_t pk_old_value = g_data_num * circle + i;
            int32_t pk_new_value = g_data_num * (circle + 1) + i;
            // printf("threadId : %d, pk_old_value is %d, pk_new_value is %d\n", threadData.index, pk_old_value,
            // pk_new_value);

            int16_t value_int16 = i;
            int32_t value_int32 = i;
            uint32_t value_uint32 = i, vrId = 1, vrfIndex = 1, destIpAddr = i;
            uint64_t value_uint64 = i;
            uint8_t maskLen = 32;
            ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetIndexKeyName(stmt, g_lable_all_index_PK);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pk_old_value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSetVertexProperty(stmt, "f0", GMC_DATATYPE_INT32, &pk_new_value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_INT32, &value_int32, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_UINT64, &value_uint64, sizeof(uint64_t));
            // EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_INT16, &value_int16, sizeof(int16_t));
            // EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "vrId", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
            // EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "vrfIndex", GMC_DATATYPE_UINT32, &vrfIndex, sizeof(uint32_t));
            // EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "maskLen", GMC_DATATYPE_UINT8, &maskLen, sizeof(uint8_t));
            // EXPECT_EQ(GMERR_OK, ret);
            // ret = GmcSetVertexProperty(stmt, "destIpAddr", GMC_DATATYPE_UINT32, &destIpAddr, sizeof(uint32_t));
            // EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, affectRows);
        }
    }

    testGmcDisconnect(conn, stmt);
    return NULL;
}
