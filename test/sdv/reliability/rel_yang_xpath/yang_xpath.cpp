/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: yang_xpath
 * Author: hanyang
 * Create: 2024-05-31
 */
#include "yang_xpath.h"
#ifdef FEATURE_YANG_VALIDATION

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[100] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[100] = {0};
GmcConnT *g_conn_asyncT = NULL;
GmcStmtT *g_stmt_asyncT = NULL;

class yang_xpath_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void yang_xpath_test::SetUpTestCase()
{
    int ret = 0;

    // 启动服务
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void yang_xpath_test::TearDownTestCase()
{
    int ret = 0;

    // 停止服务
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void yang_xpath_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    TestUseNamespace(g_stmt_async, g_namespace);

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit.");
    AW_ADD_TRUNCATION_WHITE_LIST(1, "Configuration limit exceeded.");

    AW_CHECK_LOG_BEGIN();
}

void yang_xpath_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    AddWhiteList(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_CHECK_LOG_END();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 007.连接资源耗尽，xPath校验
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    // 按照最大连接数创建表
    GmcConnT *conn[MAX_CONN_SIZE] = {NULL};
    GmcStmtT *stmt[MAX_CONN_SIZE] = {NULL};
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    AW_MACRO_ASSERT_EQ_INT(0, ret);
#if defined ENV_RTOSV2X
    uint32_t tableNum = MAX_CONN_SIZE - existConnNum - 2;
#else
    uint32_t tableNum = 128 - existConnNum;
#endif

    char syscmd[1024];
    (void)sprintf(syscmd, "sh create_multi_label_yang.sh %d", tableNum);
    system(syscmd);

    // 创建表
    TestCreateMultiLabel(g_stmt_async, tableNum);

    // 创建连接, 同一客户端异步连接最多128个
    for (uint32_t i = 0; i < tableNum; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        if (ret != GMERR_OK) {
            AW_FUN_Log(LOG_DEBUG, "i:%d ret:%d\n", i, ret);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }

        TestUseNamespace(stmt[i], g_namespace);
    }

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 每个连接都执行DML操作，并xpath校验
    for (uint32_t i = 0; i < tableNum; i++) {
        AW_FUN_Log(LOG_STEP, "conn[%d] DMLAndCheck start.", i);
        TestDMLAndCheck(conn[i], i);
    }

    // 删除多个连接
    for (uint32_t i = 0; i < tableNum; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 删除表
    TestClearNamespace(g_stmt_async, g_namespace);
    system("rm -rf multi_vertexlabel/");
}

/*****************************************************************************
 Description  : 008.异步消息队列写满，xPath校验
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 100;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelBig(g_stmt_async);

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 预先插入root
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 插入数据直到返回buffer满错误码
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, "sp0");

    // 设置child节点
    g_waitFlag = true;
    uint32_t listNum = 10000;
    uint32_t i = 0;
    for (i = 0; i < listNum; i++) {
        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[0], "list", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[0], &g_childNode[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, BatchExecuteCallbackWait, &data);
        if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            AW_FUN_Log(LOG_INFO, "GMERR_CONNECTION_SEND_BUFFER_FULL, Execute count = %d\n", i);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        GmcBatchDestroy(batch);
    }

    // 数据校验，预期报错
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = true};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_WHEN_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
#if defined ENV_RTOSV2X
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_CONNECTION_SEND_BUFFER_FULL, ret);
#endif

    // 恢复处理buffer
    if (g_envType != 2) {
        sleep(15);
        g_waitFlag = false;
        ret = testWaitAsyncRecv(&data, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "data.totalNum = %d\n", data.totalNum);
        AW_FUN_Log(LOG_INFO, "data.succNum = %d\n", data.succNum);
        sleep(5);
    } else {
        sleep(5);
        g_waitFlag = false;
        ret = testWaitAsyncRecv(&data, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, "data.totalNum = %d\n", data.totalNum);
        AW_FUN_Log(LOG_INFO, "data.succNum = %d\n", data.succNum);
        sleep(2);
    }

    // 数据校验，预期成功
    std::atomic_uint32_t step1{0};
    GmcValidateResT validateRes1 {.validateRes = true};
    ValidateParam param1 = {.step = &step1, .exceptStatus = GMERR_OK, .validateRes = validateRes1};
    GmcValidateConfigT cfg1 = {.type = GMC_YANG_VALIDATION_WHEN_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg1, AsyncValidateCb, &param1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param1.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestClearNamespace(g_stmt_async, g_namespace);
    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
}

/*****************************************************************************
 Description  : 009.表空间满，xPath校验
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 100;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建namespace 和 Tablespace
    TestCreateNamespaceTabelspace(stmt_async, g_namespace2, g_namespaceUserName, g_tablespace);
    TestUseNamespace(stmt_async, g_namespace2);

    // 创建表
    TestCreateLabelBig(stmt_async);

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(conn_async, stmt_async);

    // 预先插入root
    TestInsertRoot(conn_async, "root", fieldValue);

    uint32_t listNum = 10000;
    uint32_t i = 0;
    for (i = 0; i < listNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        CreateSavepoint(conn_async, "sp0");

        // 设置批处理batch参数
        ret = TestBatchPrepare(conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(stmt_root, "root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_root, &rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(stmt_child, "list", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt_root, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_child, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodeProperty(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyBigData(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_child);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        if (data.status != GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, data.status);
            GmcBatchDestroy(batch);
            memset(&data, 0, sizeof(AsyncUserDataT));
            AW_FUN_Log(LOG_INFO, "actul insert yang records is:%d\n", i);

            // 数据校验
            DataCheck(stmt_root, true, GMERR_TRANSACTION_ROLLBACK);

            // 回滚事务
            ret = TestTransRollBackAsync(conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
            AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
            GmcBatchDestroy(batch);
            memset(&data, 0, sizeof(AsyncUserDataT));

            // 数据校验
            DataCheck(stmt_root, true);

            // 提交事务
            ret = TestTransCommitAsync(conn_async);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 删除表
    TestClearNamespace(stmt_async, g_namespace2);
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);

    // 删除namespace 和 Tablespace
    TestDropNamespace(stmt_async, g_namespace2);
    TestDropTablespace(stmt_async, g_tablespace);
    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.xpath定义复杂
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 100;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelMix(g_stmt_async);

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 写入数据并校验
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, "sp0");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    TestYangSetNodePropertyMix(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2_1 ~ con_2_10
    ret = GmcYangEditChildNode(g_childNode[2], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[21], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[22], "con_2_3", GMC_OPERATION_INSERT, &g_childNode[23]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[23], "con_2_4", GMC_OPERATION_INSERT, &g_childNode[24]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[24], "con_2_5", GMC_OPERATION_INSERT, &g_childNode[25]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[25], "con_2_6", GMC_OPERATION_INSERT, &g_childNode[26]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[26], "con_2_7", GMC_OPERATION_INSERT, &g_childNode[27]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[27], "con_2_8", GMC_OPERATION_INSERT, &g_childNode[28]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[28], "con_2_9", GMC_OPERATION_INSERT, &g_childNode[29]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[29], "con_2_10", GMC_OPERATION_INSERT, &g_childNode[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[10], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[10], &g_childNode[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[30], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[30], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_childNode[30], "con_1", GMC_OPERATION_INSERT, &g_childNode[31]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[31], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[10], g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[32]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_childNode[32], "con_1", GMC_OPERATION_INSERT, &g_childNode[33]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[33], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[12], "list_1_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[11], g_stmt_list[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[12], &g_childNode[34]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[34], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[34], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[20], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[20], &g_childNode[40]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[40], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[40], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点choice_2,case_2
    ret = GmcYangEditChildNode(g_childNode[40], "choice_2", GMC_OPERATION_INSERT, &g_childNode[41]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[41], "case_2", GMC_OPERATION_INSERT, &g_childNode[42]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[42], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_2_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[21], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[20], g_stmt_list[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[21], &g_childNode[50]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[50], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[50], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点choice_2,case_2
    // 设置child节点con_2
    ret = GmcYangEditChildNode(g_childNode[50], "con_2", GMC_OPERATION_INSERT, &g_childNode[51]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[51], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2_1 ~ con_2_10
    ret = GmcYangEditChildNode(g_childNode[51], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[61]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[61], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[62]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[62], "con_2_3", GMC_OPERATION_INSERT, &g_childNode[63]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[63], "con_2_4", GMC_OPERATION_INSERT, &g_childNode[64]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[64], "con_2_5", GMC_OPERATION_INSERT, &g_childNode[65]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[65], "con_2_6", GMC_OPERATION_INSERT, &g_childNode[66]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[66], "con_2_7", GMC_OPERATION_INSERT, &g_childNode[67]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[67], "con_2_8", GMC_OPERATION_INSERT, &g_childNode[68]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[68], "con_2_9", GMC_OPERATION_INSERT, &g_childNode[69]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[69], "con_2_10", GMC_OPERATION_INSERT, &g_childNode[70]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_2_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[21], g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[54]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[54], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[54], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[30], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[30], &g_childNode[80]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[80], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[80], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_3
    ret = GmcYangEditChildNode(g_childNode[80], "con_3", GMC_OPERATION_INSERT, &g_childNode[81]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[81], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t idValue = 5;
    TestYangSetIDValue(g_childNode[81], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetIDValue(g_childNode[81], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(8, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(8, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验，预期成功
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = true};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_WHEN_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root", "rel_yang_xpath_010");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestClearNamespace(g_stmt_async, g_namespace);
}

/*****************************************************************************
 Description  : 011.xpath定义复杂，数据量大
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 100;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelMix(g_stmt_async);

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 写入数据并校验
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, "sp0");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    TestYangSetNodePropertyMix(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2_1 ~ con_2_10
    ret = GmcYangEditChildNode(g_childNode[2], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[21], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[22], "con_2_3", GMC_OPERATION_INSERT, &g_childNode[23]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[23], "con_2_4", GMC_OPERATION_INSERT, &g_childNode[24]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[24], "con_2_5", GMC_OPERATION_INSERT, &g_childNode[25]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[25], "con_2_6", GMC_OPERATION_INSERT, &g_childNode[26]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[26], "con_2_7", GMC_OPERATION_INSERT, &g_childNode[27]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[27], "con_2_8", GMC_OPERATION_INSERT, &g_childNode[28]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[28], "con_2_9", GMC_OPERATION_INSERT, &g_childNode[29]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[29], "con_2_10", GMC_OPERATION_INSERT, &g_childNode[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点con_3
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[10], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[10], &g_childNode[30]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[30], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[30], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_childNode[30], "con_1", GMC_OPERATION_INSERT, &g_childNode[31]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[31], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[10], g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[32]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[32], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_1
    ret = GmcYangEditChildNode(g_childNode[32], "con_1", GMC_OPERATION_INSERT, &g_childNode[33]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[33], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 300; i++) {
        // 设置child节点list_1_1_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[12], "list_1_1_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[11], g_stmt_list[12]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[12], &g_childNode[34]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[34], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyMix(g_childNode[34], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[12]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child节点list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[20], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[20], &g_childNode[40]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[40], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[40], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点choice_2,case_2
    ret = GmcYangEditChildNode(g_childNode[40], "choice_2", GMC_OPERATION_INSERT, &g_childNode[41]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[41], "case_2", GMC_OPERATION_INSERT, &g_childNode[42]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyMix(g_childNode[42], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[20]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_2_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[21], "list_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[20], g_stmt_list[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[21], &g_childNode[50]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[50], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodePropertyMix(g_childNode[50], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点choice_2,case_2
    // 设置child节点con_2
    ret = GmcYangEditChildNode(g_childNode[50], "con_2", GMC_OPERATION_INSERT, &g_childNode[51]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyMix(g_childNode[51], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点con_2_1 ~ con_2_10
    ret = GmcYangEditChildNode(g_childNode[51], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[61]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[61], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[62]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[62], "con_2_3", GMC_OPERATION_INSERT, &g_childNode[63]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[63], "con_2_4", GMC_OPERATION_INSERT, &g_childNode[64]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[64], "con_2_5", GMC_OPERATION_INSERT, &g_childNode[65]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[65], "con_2_6", GMC_OPERATION_INSERT, &g_childNode[66]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[66], "con_2_7", GMC_OPERATION_INSERT, &g_childNode[67]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[67], "con_2_8", GMC_OPERATION_INSERT, &g_childNode[68]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[68], "con_2_9", GMC_OPERATION_INSERT, &g_childNode[69]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[69], "con_2_10", GMC_OPERATION_INSERT, &g_childNode[70]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 300; i++) {
        // 设置child节点list_2_1_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_1_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[21], g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[54]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[54], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyMix(g_childNode[54], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 300; i++) {
        // 设置child节点list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[30], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[30]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[30], &g_childNode[80]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[80], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyMix(g_childNode[80], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置child节点con_3
        ret = GmcYangEditChildNode(g_childNode[80], "con_3", GMC_OPERATION_INSERT, &g_childNode[81]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyMix(g_childNode[81], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        int32_t idValue = 5;
        TestYangSetIDValue(g_childNode[81], "ID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetIDValue(g_childNode[81], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[30]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(905, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(905, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验，预期成功
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = true};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = validateRes};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_WHEN_FORCE, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestClearNamespace(g_stmt_async, g_namespace);
    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
}

/*****************************************************************************
 Description  : 012.表被删除，表被重建
 Author       : hanyang
*****************************************************************************/
TEST_F(yang_xpath_test, rel_yang_xpath_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t fieldValue = 100;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 创建表
    TestCreateLabelDel(g_stmt_async);

    // 初始模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // list2预置数据，符合list1的xpath要求
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, "sp0");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    TestYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    TestYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    DataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 另一连接删除list2,删除报错
    // 另一连接
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcStmtT *stmt_child = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUseNamespace(stmt_async, g_namespace);

    // 删除list2
    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, "sp0");

    // 设置批处理batch参数
    ret = TestBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_root, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点list_1
    ret = testGmcPrepareStmtByLabelName(stmt_child, "list_2", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_root, stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_child, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(stmt_child, fieldValue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_child);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = TestTransCommitAsync(conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除list2
    // 删除edge表
    ret = GmcDropEdgeLabelAsync(stmt_root, "root_to_list_2", drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, data.status);

    // 删除表
    TestClearNamespace(g_stmt_async, g_namespace);

    GmcFreeStmt(stmt_root);
    GmcFreeStmt(stmt_child);
    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
}
#endif // FEATURE_YANG_VALIDATION
