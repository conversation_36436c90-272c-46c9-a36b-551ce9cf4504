[{"type": "container", "name": "Con_List_root1", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 65536, "nullable": true}, {"name": "P1", "type": "uint32", "nullable": true}, {"name": "P2", "type": "uint32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "uint32", "nullable": true}, {"name": "P5", "type": "uint32", "nullable": true}], "keys": [{"node": "Con_List_root1", "name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "Con_List_Child1", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "string", "size": 65536, "nullable": true}, {"name": "F3", "type": "string", "size": 65536, "nullable": true}, {"name": "F4", "type": "string", "size": 65536, "nullable": true}, {"name": "F5", "type": "string", "size": 65536, "nullable": true}, {"name": "F6", "type": "string", "size": 65536, "nullable": true}, {"name": "F7", "type": "string", "size": 65536, "nullable": true}, {"name": "F8", "type": "string", "size": 65536, "nullable": true}], "keys": [{"node": "Con_List_Child1", "name": "PK", "fields": ["PID", "F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]