{":id": 1, "ds": [{":id": 1, ":pid": 1, "name": "intended"}, {":id": 2, ":pid": 1, "name": "running", "huawei-aaa": [{":id": 1, ":pid": 2, "huawei-aaa:aaa": {"lam": {"password-policy": {"@complexity-check": false, "complexity-check-three": false}}}}], "huawei-network-instance": [{":id": 1, ":pid": 2, "huawei-network-instance:network-instance": {"instances": {"instance": [{":id": 1, ":pid": 1, "name": "_public_", "huawei-l3vpn:afs": {"af": [{":id": 1, ":pid": 1, "type": "ipv4-unicast", "huawei-routing:routing": {"routing-manage": {"topologys": {"topology": [{":id": 1, ":pid": 1, "name": "base"}]}}}}]}}]}}}], "huawei-ifm": [{":id": 1, ":pid": 2, "huawei-ifm:ifm": {"interfaces": {"interface": [{":id": 1, ":pid": 1, "name": "Vlanif1", "class": "main-interface", "type": "<PERSON><PERSON><PERSON>", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-dhcp:interface-ip-pool": {"select-type": {"interface": {"select-interface": {"dns-list": {"ip-address": [{":id": 0, ":pid": 1, "ip-address": "*************"}]}, "alarm-ip-used": {"@alarm-resume-percentage": 50, "@alarm-percentage": 100}, "auto-recycle": {"@day": 0, "@hour": 0, "@minute": 0}}}}}, "huawei-ip:ipv4": {"address": {"common-address": {"addresses": {"address": [{":id": 1, ":pid": 1, "ip": "*************", "mask": "*************", "type": "main"}]}}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 2, ":pid": 1, "name": "GE0/0/1", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 3, ":pid": 1, "name": "GE0/0/2", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 4, ":pid": 1, "name": "GE0/0/3", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 5, ":pid": 1, "name": "GE0/0/4", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 6, ":pid": 1, "name": "GE0/0/5", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 7, ":pid": 1, "name": "GE0/0/6", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 8, ":pid": 1, "name": "GE0/0/7", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 9, ":pid": 1, "name": "GE0/0/8", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-ethernet:ethernet": {"main-interface": {"l2-attribute": {"link-type": "hybrid", "pvid": 1, "untag-vlans": "1", "huawei-mstp:mstp-attribute": {"@enable": true, "@no-agreement-check": false, "@root-protection": false, "@bpdu-filter": "default", "@edge-port": "default"}}}}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 10, ":pid": 1, "name": "GE0/0/9", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"nat-enable": true, "@nat64-enable": false}}, {":id": 11, ":pid": 1, "name": "GE0/0/10", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"nat-enable": true, "@nat64-enable": false}}, {":id": 12, ":pid": 1, "name": "GE0/0/11", "class": "main-interface", "type": "GigabitEthernet", "admin-status": "up", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-lldp:lldp": {"session": {"@admin-status": "tx-rx", "tlv-enable": {"@management-address": true, "@port-description": true, "@system-capability": true, "@system-description": true, "@system-name": true, "@mac-physic": true, "@link-aggregation": true, "@max-frame-size": true}}}, "huawei-nat:nat": {"nat-enable": true, "@nat64-enable": false}}, {":id": 13, ":pid": 1, "name": "NULL", "class": "main-interface", "type": "NULL", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}, {":id": 14, ":pid": 1, "name": "lo", "class": "main-interface", "type": "LoopBack", "link-protocol": "ethernet", "@vrf-name": "_public_", "@is-l2-switch": false, "mib-statistics": {"huawei-pic:eth-port-err-sts": {"@rx-crc": "0"}}, "common-statistics": {"@stati-interval": 300}, "huawei-arp:arp-entry": {"@expire-time": 1200, "@arp-learn-disable": false, "@route-proxy-enable": false, "@inner-proxy-enable": false, "@dest-mac-check": false, "@src-mac-check": false}, "huawei-nat:nat": {"@nat-enable": false, "@nat64-enable": false}}]}}}], "huawei-vlan": [{":id": 1, ":pid": 2, "huawei-vlan:vlan": {"vlans": {"vlan": [{":id": 1, ":pid": 1, "id": 1, "@type": "common", "huawei-arp:arp-security": {"@l2proxy-enable": false}}]}}}], "huawei-cfg": [{":id": 1, ":pid": 2, "huawei-cfg:cfg": {"autosave": {"@interval-time": 30, "@delay-time": 5}}}], "huawei-pki": [{":id": 1, ":pid": 2, "huawei-pki:pki": {"global": {"@strict-check-mode": false, "@synchronize-standby-device": true, "certificate-check": {"@validate-method": "crl-none", "@expiration-check-interval": 5, "@expiration-prewarning-day": 90}, "load-certificate": {"@default-ca": false, "@default-local": true}}, "domains": {"domain": [{":id": 1, ":pid": 1, "name": "default", "@digest-algorithm": "sha-256", "crl-update": {"@update-type": "ldap", "@auto-update": false, "@period": 8}, "scep": {"@enroll-interval": 1, "@enroll-times": 5}}]}}}], "huawei-dns": [{":id": 1, ":pid": 2, "huawei-dns:dns": {"dns-relay": {"enable": true, "@ipv6-enable": false, "local-ips": {"local-ip": [{":id": 1, ":pid": 1, "ip-address": "0.0.0.0", "vpn-name": "_public_"}]}}}}], "huawei-dhcp": [{":id": 1, ":pid": 2, "huawei-dhcp:dhcp": {"common": {"global": {"enable": true}}, "server": {"common": {"@ping-packet-nub": 2, "@ping-packet-timeout": 500, "@bootp-enable": false, "@bootp-auto-enable": false}}}}], "huawei-sshs": [{":id": 1, ":pid": 2, "huawei-sshs:sshs": {"server-enable": {"stelnet-ipv4-enable": "enable"}, "server-port": {"@ipv4-port-number": 22}, "ipv4-server-sources": {"ipv4-server-source": [{":id": 1, ":pid": 1, "src-interface": "Vlanif1"}]}}}], "huawei-ssl": [{":id": 1, ":pid": 2, "huawei-ssl:ssl": {"ssl-policys": {"ssl-policy": [{":id": 1, ":pid": 1, "policy-name": "default", "pki-realm": "default", "@mini-version": "tls1.2", "@exclude-rsa-kex": "enable", "@exclude-dhe-kex": "disable", "@exclude-hmac-sha1": "enable", "@exclude-ciphermode-cbc": "enable", "ecdh-group": {"@nist": true, "@curve": true, "@brainpool": true, "@ffdhe": false}}]}}}], "huawei-sshc": [{":id": 1, ":pid": 2, "huawei-sshc:sshc": {"client": {"first-time-enable": "enable"}}}], "huawei-lldp": [{":id": 1, ":pid": 2, "huawei-lldp:lldp": {"global-attribute": {"@message-transmission-interval": 30, "@message-transmission-delay": 2, "@message-transmission-hold-multiplier": 4, "@restart-delay": 2}}}], "huawei-smart-upgrade": [{":id": 1, ":pid": 2, "huawei-smart-upgrade:smart-upgrade": {"global": {"check-new-version-interval": 24, "transport": {"http": {"http-url": "https://houp.huawei.com", "http-port": 443, "@verify-server-disable": true}}}}}], "huawei-socket": [{":id": 1, ":pid": 2, "huawei-socket:socket": {"tcp-global": {"tcp-max-mss": 1200}}}], "huawei-easyweb-netmgmt": [{":id": 1, ":pid": 2, "huawei-easyweb-netmgmt:easyweb-netmgmt": {"network": {"working-mode": "initial", "@online-upgrade-enable": false, "networks": {"vlans": {"vlan": [{":id": 1, ":pid": 1, "id": 1, "description": "Vlanif1"}]}}}}}], "huawei-wlan-capwap": [{":id": 1, ":pid": 2, "huawei-wlan-capwap:wlan-capwap": {"@control-local-link-priority": 7, "@control-remote-link-priority": 7, "capwap-source": {"source-type": "ip-address", "@source-ip-version": "ipv4", "source-ips": {"ipv4-address": "*************"}}, "capwap-heartbeats": {"@interval": 25, "@timers": 6}, "capwap-dtls": {"@ctrl-dtls-switch": "auto", "@no-auth-switch": false, "@cert-mandatory-match-switch": true, "@version10-switch": false, "@cbc-switch": false, "@data-dtls-switch": false}}}], "huawei-web-manager": [{":id": 1, ":pid": 2, "huawei-web-manager:web-manager": {"web-server": {"@https-server-enable": true, "@https-port-number": 8443, "@http-forward-enable": true}, "source-interface": {"@all-interface-enable": false, "interfaces": {"interface": [{":id": 1, ":pid": 1, "interface-name": "Vlanif1"}]}}}}], "huawei-nat-policy": [{":id": 1, ":pid": 2, "huawei-nat-policy:nat-policy": {"rules": {"rule": [{":id": 1, ":pid": 1, "name": "default", "egress": {"interfaces": {"egress-interface": [{":id": 0, ":pid": 1, "egress-interface": "GE0/0/9"}, {":id": 0, ":pid": 1, "egress-interface": "GE0/0/10"}, {":id": 0, ":pid": 1, "egress-interface": "GE0/0/11"}]}}}]}}}]}]}