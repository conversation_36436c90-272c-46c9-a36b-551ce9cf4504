
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef RCA_COMMON_H
#define RCA_COMMON_H
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <list>
#include <atomic>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define TIMEZONEMAXCMD 512
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
char *g_vertexschema = NULL, *g_edgeschema = NULL;
GmcNodeT *g_vertexLabelT0Node = NULL;
GmcNodeT *g_vertexLabelT1Node = NULL;
GmcNodeT *g_vertexLabelT2Node = NULL;
GmcNodeT *g_vertexLabelT3Node = NULL;
GmcNodeT *SubT1choiceCase = NULL;

GmcNodeT *g_containerT2Node = NULL;
GmcNodeT *g_choiceNode = NULL;
GmcNodeT *g_choiceCaseNode = NULL;
GmcNodeT *g_caseContainerNode1 = NULL;
GmcNodeT *g_caseContainerNode2 = NULL;
GmcNodeT *g_containerT3Node = NULL;

GmcNodeT *g_listContainerNode1 = NULL;
GmcNodeT *g_listChoiceNode = NULL;
GmcNodeT *g_listChoiceCaseNode1 = NULL;
GmcNodeT *g_listContainerNode2 = NULL;

const char *g_vertexLabelT0 = "ContainerOne";
const char *g_vertexLabelT1 = "ListOne";
const char *g_vertexLabelT2 = "LeafList";
const char *g_vertexLabelT3 = "ListTwo";
const char *g_edgeLabeT0T1 = "ContainerList";
const char *g_edgeLabeT0T2 = "ContainerLeafList";
const char *g_edgeLabeT1T3 = "ListListTwo";

const char *g_containerNodeName2 = "ContainerTwo";
const char *g_choiceNodeName = "Choice";
const char *g_choiceCaseNodeName = "CaseOne";
const char *g_caseContainerNodeName1 = "CaseContainerOne";
const char *g_caseContainerNodeName2 = "CaseContainerTwo";
const char *g_containerNodeName3 = "ContainerThree";


const char *g_listContainerNodeName1 = "ListContainerOne";
const char *g_listChoiceNodeName = "Listchoice";
const char *g_listChoiceCaseNodeName = "ListchoiceCase";
const char *g_listContainerNodeName2 = "ListContainerTwo";
struct FetchRetCbParam01 {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};
AsyncUserDataT userData = {0};
// 32deep
const char *g_vertexLabelroot = "root";
const char *g_labelconfig = R"(
{
    "max_record_count":5000000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
const char *g_keyName = "PK";
// 申请各个表用的stmt
GmcStmtT *g_stmt_sync_T0 = NULL;
GmcStmtT *g_stmt_sync_T1List = NULL;
GmcStmtT *g_stmt_sync_T2List = NULL;
GmcStmtT *g_stmt_sync_T3List = NULL;
GmcStmtT *g_stmt_sync_LeafList = NULL;
// 申请32depp表用的stmt
GmcStmtT *g_stmt_sync_32deep = NULL;
int GetTimeZoneType()
{
    char timecmd[TIMEZONEMAXCMD] = {0};
    // 将date 定向到 time.ini中
    (void)snprintf(timecmd, TIMEZONEMAXCMD, " date > time.ini");
    system(timecmd);

    // 返回0为CST时区，1为UTC时区
    char key1[2048] = "CST";
    char key2[2048] = "Defaul";
    char key3[2048] = "UTC";
    FILE *fp;
    char buffer[2048];
    // 从 time.ini 获取 date时区
    (void)snprintf(timecmd, TIMEZONEMAXCMD, "cat  time.ini");
    printf("%s\n", timecmd);
    fp = popen(timecmd, "r");
    (void)fgets(buffer, sizeof(buffer), fp);
    printf("%s", buffer);
    // 如果获取的字符串中 包括 CST 则时区类型为 CST
    if (strstr(buffer, key1)) {
        printf("key1 is %s  buffer  is %s \n", key1, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 0;
    // 如果获取的字符串中 包括 Defaul 则时区类型为 Defaul
    } else if (strstr(buffer, key2)) {
        printf("key2 is %s  buffer  is %s \n", key2, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else if (strstr(buffer, key3)) {
        printf("key3 is %s  buffer  is %s \n", key3, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else {
        printf(" buffer  is %s \n", buffer);
        (void)pclose(fp);
        fp = NULL;
        return 0;
    }
}

void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T2List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T3List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_LeafList);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// yang set 字段
int testYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}


// yang set T0层f0
void testYangSetVertexProperty_F0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


// yang set T0层f0
void testYangSetVertexProperty_Fx(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype, const char *name)
{
    int ret = 0;
    uint32_t f0Value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t), name, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetNodeProperty(GmcNodeT *node, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(node, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


int TestYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}


void TestYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F5写入和默认值不同的值
    uint32_t valueF5 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF5, sizeof(uint32_t), "F5", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F6写入和默认值相同的值
    uint32_t valueF6 = 666;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF6, sizeof(uint32_t), "F6", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F7，F8，F9不写入值

    char valueF10[10] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF10, (strlen(valueF10)), "F10", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F11写入和默认值不同的值
    char valueF11[10] = "string";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF11, (strlen(valueF11)), "F11", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F12写入和默认值相同的值
    char valueF12[10] = "default12";
    ret = TestYangSetField(node, GMC_DATATYPE_STRING, valueF12, (strlen(valueF12)), "F12", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F13不写入值
}

// 只有list类型需要设置主键
void TestYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = i;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestYangSetNodePropertyBasic(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF0 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF2 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF3 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = TestYangSetField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void GetPath_callbackOnlyNum(void *userData, GmcLeafrefPathT leafrefPathes, int32_t status, const char *errMsg)
{
    int ret = 0;
    bool isEnd = false;
    uint32_t count = 0;
    AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
    EXPECT_EQ(userData1->expStatus, status) << errMsg;

    if (userData1->expStatus != GMERR_OK) {
        userData1->recvNum++;
        return;
    }

    // 比较预期和返回的path数量
    if (status == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(userData1->leafrefPathes.num, leafrefPathes.num);
    }
    userData1->recvNum++;
}


string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue)
                            : GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

// diff 回调
void FetchDiff_callback(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));

            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}

// 获取diff
void testFetchAndDeparseDiff(
    GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data, int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}


/******************************diff*******************************************/

int g_diffWriteTime = 1;
void TestCheckYangTreeBatch(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        printf(">> reply diff_time: %d \n", g_diffWriteTime);
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "replyDiff/%d.txt", g_diffWriteTime++);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }

        // cout << "actual diff：\n" << res;
        // cout << "expect：\n" << expectReply[i].c_str();
        // ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callbackBatch(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            uint32_t idx = userData1->lastExpectIdx;
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TestCheckYangTreeBatch(userData1->stmt, yangTree, count, *userData1->expectDiff);

            AW_FUN_Log(LOG_INFO, "diff count is %d, userData1->lastExpectIdx is %d.",
                count, userData1->lastExpectIdx);
            // 全部获取完以后 才释放FetchRet
            if (isEnd) {
                    userData1->recvNum++;
                    GmcYangFreeFetchRet(fetchRet);
                    return;
                }
            // 没有获取完diff 重新触发回调函数
            userData1->lastExpectIdx = idx + count;
            ASSERT_EQ(GMERR_OK,
                GmcYangFetchDiffExecuteAsync(userData1->stmt, fetchRet, FetchDiff_callbackBatch, userData1));
            return;
        } else if (status == GMERR_NO_DATA) {
            // 如果root contain下数据超过2M，分批时候，diff报错返回
            userData1->recvNum++;
        }
    }
}

void TestFetchAndDeparseDiffBatch(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    system("mkdir -p replyDiff;rm -rf replyDiff/*");

    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callbackBatch, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
    AW_FUN_Log(LOG_INFO, "diff data.lastExpectIdx is %d.", data.lastExpectIdx);
}

static vector<string> expectDiffNotCmp = {
    "huawei-ifm:ifm:update[(priKey(ID:1)),(priKey(ID:1))]\n"
};

int WriteFile(const char *fileName, int nValue)
{
    char tmpStr[128] = {0};
    (void)snprintf(tmpStr, 128, "%d", nValue);

    FILE *fp = fopen(fileName, "w");
    if (fp == NULL) {
        AW_FUN_Log(LOG_INFO, "fopen error\n");
        return -1;
    }
    int ret = fputs(tmpStr, fp);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "fputs error\n");
        return -1;
    }
    ret = fclose(fp);
    if (ret == -1) {
        AW_FUN_Log(LOG_INFO, "fclose error\n");
        return -1;
    }
    return 0;
}

int GetValueFromFile(const char *fileName)
{
    FILE *pf = fopen(fileName, "r");
    if (pf == NULL) {
        AW_FUN_Log(LOG_STEP, "fopen error.\n");
        return -1;
    }

    int ch;
    char nums[128] = {0};
    int i = 0;
    while ((ch = fgetc(pf)) != EOF) {
        if (ch >= 48 && ch <= 57) {
            nums[i++] = ch;
        }
    }
    return atoi(nums);
}


#endif
