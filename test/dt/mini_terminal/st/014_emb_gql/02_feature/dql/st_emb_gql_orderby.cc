/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_gql_hashagg.cc
 * Description:
 * Create: 2024-12-10
 */

#include <iostream>
#include <vector>
#include <string>
#include "st_emb_gql_com.h"
#include "gme_gql_api.h"

using namespace std;
using namespace testing::ext;

class StEmbGqlOrderBy : public StEmbGqlCommonWithMoiveData {
    void SetUp() override
    {
        StEmbGqlCommonWithMoiveData::SetUp();
    }

    void TearDown() override
    {
        StEmbGqlCommonWithMoiveData::TearDown();
    }

public:
    std::vector<std::string> personSetIdOrder = {
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"<PERSON><PERSON> Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"43\",\"properties\":{\"NAME\":\"Lilly Wachowski\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"44\",\"properties\":{\"NAME\":\"Lana Wachowski\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"45\",\"properties\":{\"NAME\":\"Joel Silver\",\"BORN\":1952}}",
        "{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"49\",\"properties\":{\"NAME\":\"Taylor Hackford\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"52\",\"properties\":{\"NAME\":\"Demi Moore\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"55\",\"properties\":{\"NAME\":\"Noah Wyle\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"57\",\"properties\":{\"NAME\":\"Kevin Pollak\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"59\",\"properties\":{\"NAME\":\"James Marshall\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"60\",\"properties\":{\"NAME\":\"Christopher Guest\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"61\",\"properties\":{\"NAME\":\"Rob Reiner\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"62\",\"properties\":{\"NAME\":\"Aaron Sorkin\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"63\",\"properties\":{\"NAME\":\"Kelly McGillis\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"64\",\"properties\":{\"NAME\":\"Val Kilmer\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"65\",\"properties\":{\"NAME\":\"Anthony Edwards\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"68\",\"properties\":{\"NAME\":\"Tony Scott\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"69\",\"properties\":{\"NAME\":\"Jim Cash\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"70\",\"properties\":{\"NAME\":\"Renee Zellweger\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"71\",\"properties\":{\"NAME\":\"Kelly Preston\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"73\",\"properties\":{\"NAME\":\"Jay Mohr\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"75\",\"properties\":{\"NAME\":\"Regina King\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
        "{\"label\":\"PERSON\",\"identity\":\"77\",\"properties\":{\"NAME\":\"Cameron Crowe\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"78\",\"properties\":{\"NAME\":\"River Phoenix\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"79\",\"properties\":{\"NAME\":\"Corey Feldman\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"80\",\"properties\":{\"NAME\":\"Wil Wheaton\",\"BORN\":1972}}",
        "{\"label\":\"PERSON\",\"identity\":\"81\",\"properties\":{\"NAME\":\"John Cusack\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"85\",\"properties\":{\"NAME\":\"James L. Brooks\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"86\",\"properties\":{\"NAME\":\"Annabella Sciorra\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"88\",\"properties\":{\"NAME\":\"Werner Herzog\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"90\",\"properties\":{\"NAME\":\"Vincent Ward\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"91\",\"properties\":{\"NAME\":\"Ethan Hawke\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"94\",\"properties\":{\"NAME\":\"Scott Hicks\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"95\",\"properties\":{\"NAME\":\"Parker Posey\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"96\",\"properties\":{\"NAME\":\"Dave Chappelle\",\"BORN\":1973}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"99\",\"properties\":{\"NAME\":\"Nora Ephron\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"100\",\"properties\":{\"NAME\":\"Rita Wilson\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"101\",\"properties\":{\"NAME\":\"Bill Pullman\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"102\",\"properties\":{\"NAME\":\"Victor Garber\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"104\",\"properties\":{\"NAME\":\"John Patrick Stanley\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"106\",\"properties\":{\"NAME\":\"Billy Crystal\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"107\",\"properties\":{\"NAME\":\"Carrie Fisher\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"108\",\"properties\":{\"NAME\":\"Bruno Kirby\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}",
        "{\"label\":\"PERSON\",\"identity\":\"110\",\"properties\":{\"NAME\":\"Brooke Langton\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"112\",\"properties\":{\"NAME\":\"Orlando Jones\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"113\",\"properties\":{\"NAME\":\"Howard Deutch\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"114\",\"properties\":{\"NAME\":\"Christian Bale\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"116\",\"properties\":{\"NAME\":\"Mike Nichols\",\"BORN\":1931}}",
        "{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"119\",\"properties\":{\"NAME\":\"Takeshi Kitano\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"120\",\"properties\":{\"NAME\":\"Dina Meyer\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"121\",\"properties\":{\"NAME\":\"Ice-T\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"122\",\"properties\":{\"NAME\":\"Robert Longo\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"123\",\"properties\":{\"NAME\":\"Halle Berry\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"124\",\"properties\":{\"NAME\":\"Jim Broadbent\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"125\",\"properties\":{\"NAME\":\"Tom Tykwer\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"126\",\"properties\":{\"NAME\":\"David Mitchell\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"127\",\"properties\":{\"NAME\":\"Stefan Arndt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
        "{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
        "{\"label\":\"PERSON\",\"identity\":\"130\",\"properties\":{\"NAME\":\"Paul Bettany\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"131\",\"properties\":{\"NAME\":\"Ron Howard\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
        "{\"label\":\"PERSON\",\"identity\":\"133\",\"properties\":{\"NAME\":\"Stephen Rea\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"134\",\"properties\":{\"NAME\":\"John Hurt\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
        "{\"label\":\"PERSON\",\"identity\":\"137\",\"properties\":{\"NAME\":\"John Goodman\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"138\",\"properties\":{\"NAME\":\"Susan Sarandon\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"139\",\"properties\":{\"NAME\":\"Matthew Fox\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"142\",\"properties\":{\"NAME\":\"Naomie Harris\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"143\",\"properties\":{\"NAME\":\"Michael Clarke Duncan\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"144\",\"properties\":{\"NAME\":\"David Morse\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"147\",\"properties\":{\"NAME\":\"Patricia Clarkson\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"148\",\"properties\":{\"NAME\":\"Frank Darabont\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
        "{\"label\":\"PERSON\",\"identity\":\"150\",\"properties\":{\"NAME\":\"Michael Sheen\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"153\",\"properties\":{\"NAME\":\"John C. Reilly\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"154\",\"properties\":{\"NAME\":\"Ed Harris\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"157\",\"properties\":{\"NAME\":\"Jan de Bont\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"158\",\"properties\":{\"NAME\":\"Robert Zemeckis\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"159\",\"properties\":{\"NAME\":\"Milos Forman\",\"BORN\":1932}}",
        "{\"label\":\"PERSON\",\"identity\":\"160\",\"properties\":{\"NAME\":\"Diane Keaton\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"161\",\"properties\":{\"NAME\":\"Nancy Meyers\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"162\",\"properties\":{\"NAME\":\"Chris Columbus\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"163\",\"properties\":{\"NAME\":\"Julia Roberts\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"164\",\"properties\":{\"NAME\":\"Madonna\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"165\",\"properties\":{\"NAME\":\"Geena Davis\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"166\",\"properties\":{\"NAME\":\"Lori Petty\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"167\",\"properties\":{\"NAME\":\"Penny Marshall\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"168\",\"properties\":{\"NAME\":\"Paul Blythe\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"169\",\"properties\":{\"NAME\":\"Angela Scope\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"170\",\"properties\":{\"NAME\":\"Jessica Thompson\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"171\",\"properties\":{\"NAME\":\"James Thompson\"}}"};

    std::vector<std::string> bornSetIdOrder = {"1964", "1967", "1961", "1960", "1967", "1965", "1952", "1978", "1975",
        "1940", "1944", "1962", "1937", "1962", "1958", "1966", "1971", "1968", "1957", "1943", "1967", "1948", "1947",
        "1961", "1957", "1959", "1962", "1933", "1961", "1944", "1941", "1969", "1962", "1974", "1970", "1961", "1971",
        "1996", "1957", "1970", "1971", "1972", "1966", "1942", "1963", "1963", "1940", "1960", "1929", "1942", "1951",
        "1956", "1970", "1971", "1940", "1953", "1968", "1973", "1967", "1956", "1941", "1956", "1953", "1949", "1962",
        "1950", "1956", "1948", "1956", "1949", "1977", "1970", "1930", "1968", "1950", "1974", "1954", "1931", "1930",
        "1930", "1947", "1968", "1958", "1953", "1966", "1949", "1965", "1969", "1961", "1939", "1976", "1971", "1954",
        "1981", "1946", "1940", "1967", "1985", "1960", "1946", "1966", "1980", "1982", "NULL", "1957", "1953", "1968",
        "1955", "1959", "1959", "1938", "1969", "1960", "1944", "1965", "1950", "1955", "1967", "1943", "1951", "1932",
        "1946", "1949", "1958", "1967", "1954", "1956", "1963", "1943", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::string> nameSetIdOrder = {"Keanu Reeves", "Carrie-Anne Moss", "Laurence Fishburne", "Hugo Weaving",
        "Lilly Wachowski", "Lana Wachowski", "Joel Silver", "Emil Eifrem", "Charlize Theron", "Al Pacino",
        "Taylor Hackford", "Tom Cruise", "Jack Nicholson", "Demi Moore", "Kevin Bacon", "Kiefer Sutherland",
        "Noah Wyle", "Cuba Gooding Jr.", "Kevin Pollak", "J.T. Walsh", "James Marshall", "Christopher Guest",
        "Rob Reiner", "Aaron Sorkin", "Kelly McGillis", "Val Kilmer", "Anthony Edwards", "Tom Skerritt", "Meg Ryan",
        "Tony Scott", "Jim Cash", "Renee Zellweger", "Kelly Preston", "Jerry O Connell", "Jay Mohr", "Bonnie Hunt",
        "Regina King", "Jonathan Lipnicki", "Cameron Crowe", "River Phoenix", "Corey Feldman", "Wil Wheaton",
        "John Cusack", "Marshall Bell", "Helen Hunt", "Greg Kinnear", "James L. Brooks", "Annabella Sciorra",
        "Max von Sydow", "Werner Herzog", "Robin Williams", "Vincent Ward", "Ethan Hawke", "Rick Yune",
        "James Cromwell", "Scott Hicks", "Parker Posey", "Dave Chappelle", "Steve Zahn", "Tom Hanks", "Nora Ephron",
        "Rita Wilson", "Bill Pullman", "Victor Garber", "Rosie O Donnell", "John Patrick Stanley", "Nathan Lane",
        "Billy Crystal", "Carrie Fisher", "Bruno Kirby", "Liv Tyler", "Brooke Langton", "Gene Hackman", "Orlando Jones",
        "Howard Deutch", "Christian Bale", "Zach Grenier", "Mike Nichols", "Richard Harris", "Clint Eastwood",
        "Takeshi Kitano", "Dina Meyer", "Ice-T", "Robert Longo", "Halle Berry", "Jim Broadbent", "Tom Tykwer",
        "David Mitchell", "Stefan Arndt", "Ian McKellen", "Audrey Tautou", "Paul Bettany", "Ron Howard",
        "Natalie Portman", "Stephen Rea", "John Hurt", "Ben Miles", "Emile Hirsch", "John Goodman", "Susan Sarandon",
        "Matthew Fox", "Christina Ricci", "Rain", "Naomie Harris", "Michael Clarke Duncan", "David Morse",
        "Sam Rockwell", "Gary Sinise", "Patricia Clarkson", "Frank Darabont", "Frank Langella", "Michael Sheen",
        "Oliver Platt", "Danny DeVito", "John C. Reilly", "Ed Harris", "Bill Paxton", "Philip Seymour Hoffman",
        "Jan de Bont", "Robert Zemeckis", "Milos Forman", "Diane Keaton", "Nancy Meyers", "Chris Columbus",
        "Julia Roberts", "Madonna", "Geena Davis", "Lori Petty", "Penny Marshall", "Paul Blythe", "Angela Scope",
        "Jessica Thompson", "James Thompson"};

    std::vector<std::string> bornSetShuffle = {"1943", "1963", "1956", "1954", "1967", "1958", "1949", "1946", "1932",
        "1951", "1943", "1967", "1955", "1950", "1965", "1944", "1960", "1969", "1938", "1959", "1959", "1955", "1968",
        "1953", "1957", "1982", "1980", "1964", "1946", "1960", "1985", "1967", "1940", "1946", "1981", "1954", "1971",
        "1976", "1939", "1961", "1969", "1965", "1949", "1966", "1953", "1958", "1968", "1947", "1930", "1930", "1931",
        "1954", "1974", "1950", "1968", "1930", "1970", "1977", "1949", "1956", "1948", "1956", "1950", "1962", "1966",
        "1967", "1961", "1960", "1967", "1965", "1952", "1978", "1975", "1940", "1944", "1962", "1937", "1949", "1958",
        "1966", "1971", "1968", "1957", "1943", "1967", "1948", "1947", "1961", "1957", "1959", "1962", "1933", "1961",
        "1944", "1941", "1969", "1962", "1953", "1956", "1941", "1956", "1967", "1973", "1968", "1953", "1940", "1971",
        "1970", "1956", "1951", "1942", "1929", "1960", "1962", "1940", "1963", "1963", "1942", "1966", "1972", "1971",
        "1970", "1957", "1974", "1996", "1971", "1961", "1970", "NULL", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::string> personSetBornOrder = {
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"116\",\"properties\":{\"NAME\":\"Mike Nichols\",\"BORN\":1931}}",
        "{\"label\":\"PERSON\",\"identity\":\"159\",\"properties\":{\"NAME\":\"Milos Forman\",\"BORN\":1932}}",
        "{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
        "{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
        "{\"label\":\"PERSON\",\"identity\":\"134\",\"properties\":{\"NAME\":\"John Hurt\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"85\",\"properties\":{\"NAME\":\"James L. Brooks\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"69\",\"properties\":{\"NAME\":\"Jim Cash\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"99\",\"properties\":{\"NAME\":\"Nora Ephron\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"88\",\"properties\":{\"NAME\":\"Werner Herzog\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"157\",\"properties\":{\"NAME\":\"Jan de Bont\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"167\",\"properties\":{\"NAME\":\"Penny Marshall\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"49\",\"properties\":{\"NAME\":\"Taylor Hackford\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"68\",\"properties\":{\"NAME\":\"Tony Scott\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"133\",\"properties\":{\"NAME\":\"Stephen Rea\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"160\",\"properties\":{\"NAME\":\"Diane Keaton\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"138\",\"properties\":{\"NAME\":\"Susan Sarandon\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"61\",\"properties\":{\"NAME\":\"Rob Reiner\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"119\",\"properties\":{\"NAME\":\"Takeshi Kitano\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"106\",\"properties\":{\"NAME\":\"Billy Crystal\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"60\",\"properties\":{\"NAME\":\"Christopher Guest\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"161\",\"properties\":{\"NAME\":\"Nancy Meyers\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"124\",\"properties\":{\"NAME\":\"Jim Broadbent\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"108\",\"properties\":{\"NAME\":\"Bruno Kirby\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"102\",\"properties\":{\"NAME\":\"Victor Garber\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"104\",\"properties\":{\"NAME\":\"John Patrick Stanley\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"154\",\"properties\":{\"NAME\":\"Ed Harris\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"113\",\"properties\":{\"NAME\":\"Howard Deutch\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"158\",\"properties\":{\"NAME\":\"Robert Zemeckis\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"45\",\"properties\":{\"NAME\":\"Joel Silver\",\"BORN\":1952}}",
        "{\"label\":\"PERSON\",\"identity\":\"94\",\"properties\":{\"NAME\":\"Scott Hicks\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"122\",\"properties\":{\"NAME\":\"Robert Longo\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"101\",\"properties\":{\"NAME\":\"Bill Pullman\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"144\",\"properties\":{\"NAME\":\"David Morse\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"164\",\"properties\":{\"NAME\":\"Madonna\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"131\",\"properties\":{\"NAME\":\"Ron Howard\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"107\",\"properties\":{\"NAME\":\"Carrie Fisher\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"100\",\"properties\":{\"NAME\":\"Rita Wilson\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"90\",\"properties\":{\"NAME\":\"Vincent Ward\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"165\",\"properties\":{\"NAME\":\"Geena Davis\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"63\",\"properties\":{\"NAME\":\"Kelly McGillis\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"57\",\"properties\":{\"NAME\":\"Kevin Pollak\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"77\",\"properties\":{\"NAME\":\"Cameron Crowe\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"143\",\"properties\":{\"NAME\":\"Michael Clarke Duncan\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"162\",\"properties\":{\"NAME\":\"Chris Columbus\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"121\",\"properties\":{\"NAME\":\"Ice-T\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"64\",\"properties\":{\"NAME\":\"Val Kilmer\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"148\",\"properties\":{\"NAME\":\"Frank Darabont\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"147\",\"properties\":{\"NAME\":\"Patricia Clarkson\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"137\",\"properties\":{\"NAME\":\"John Goodman\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"86\",\"properties\":{\"NAME\":\"Annabella Sciorra\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"127\",\"properties\":{\"NAME\":\"Stefan Arndt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"62\",\"properties\":{\"NAME\":\"Aaron Sorkin\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"52\",\"properties\":{\"NAME\":\"Demi Moore\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"71\",\"properties\":{\"NAME\":\"Kelly Preston\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"65\",\"properties\":{\"NAME\":\"Anthony Edwards\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"166\",\"properties\":{\"NAME\":\"Lori Petty\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"153\",\"properties\":{\"NAME\":\"John C. Reilly\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"125\",\"properties\":{\"NAME\":\"Tom Tykwer\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"44\",\"properties\":{\"NAME\":\"Lana Wachowski\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"81\",\"properties\":{\"NAME\":\"John Cusack\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"123\",\"properties\":{\"NAME\":\"Halle Berry\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"139\",\"properties\":{\"NAME\":\"Matthew Fox\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"59\",\"properties\":{\"NAME\":\"James Marshall\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"163\",\"properties\":{\"NAME\":\"Julia Roberts\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"43\",\"properties\":{\"NAME\":\"Lilly Wachowski\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"120\",\"properties\":{\"NAME\":\"Dina Meyer\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"95\",\"properties\":{\"NAME\":\"Parker Posey\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"112\",\"properties\":{\"NAME\":\"Orlando Jones\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"150\",\"properties\":{\"NAME\":\"Michael Sheen\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"70\",\"properties\":{\"NAME\":\"Renee Zellweger\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"126\",\"properties\":{\"NAME\":\"David Mitchell\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"73\",\"properties\":{\"NAME\":\"Jay Mohr\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"110\",\"properties\":{\"NAME\":\"Brooke Langton\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"78\",\"properties\":{\"NAME\":\"River Phoenix\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"91\",\"properties\":{\"NAME\":\"Ethan Hawke\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"55\",\"properties\":{\"NAME\":\"Noah Wyle\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"75\",\"properties\":{\"NAME\":\"Regina King\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"79\",\"properties\":{\"NAME\":\"Corey Feldman\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"130\",\"properties\":{\"NAME\":\"Paul Bettany\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"80\",\"properties\":{\"NAME\":\"Wil Wheaton\",\"BORN\":1972}}",
        "{\"label\":\"PERSON\",\"identity\":\"96\",\"properties\":{\"NAME\":\"Dave Chappelle\",\"BORN\":1973}}",
        "{\"label\":\"PERSON\",\"identity\":\"114\",\"properties\":{\"NAME\":\"Christian Bale\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
        "{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}",
        "{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}",
        "{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
        "{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
        "{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
        "{\"label\":\"PERSON\",\"identity\":\"171\",\"properties\":{\"NAME\":\"James Thompson\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"170\",\"properties\":{\"NAME\":\"Jessica Thompson\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"169\",\"properties\":{\"NAME\":\"Angela Scope\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"168\",\"properties\":{\"NAME\":\"Paul Blythe\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"142\",\"properties\":{\"NAME\":\"Naomie Harris\"}}"};

    std::vector<std::string> bornSetBornOrder = {"1929", "1930", "1930", "1930", "1931", "1932", "1933", "1937", "1938",
        "1939", "1940", "1940", "1940", "1940", "1941", "1941", "1942", "1942", "1943", "1943", "1943", "1944", "1944",
        "1944", "1946", "1946", "1946", "1947", "1947", "1948", "1948", "1949", "1949", "1949", "1949", "1950", "1950",
        "1950", "1951", "1951", "1952", "1953", "1953", "1953", "1953", "1954", "1954", "1954", "1955", "1955", "1956",
        "1956", "1956", "1956", "1956", "1956", "1957", "1957", "1957", "1957", "1958", "1958", "1958", "1959", "1959",
        "1959", "1960", "1960", "1960", "1960", "1961", "1961", "1961", "1961", "1961", "1962", "1962", "1962", "1962",
        "1962", "1963", "1963", "1963", "1964", "1965", "1965", "1965", "1966", "1966", "1966", "1966", "1967", "1967",
        "1967", "1967", "1967", "1967", "1967", "1968", "1968", "1968", "1968", "1968", "1969", "1969", "1969", "1970",
        "1970", "1970", "1970", "1971", "1971", "1971", "1971", "1971", "1972", "1973", "1974", "1974", "1975", "1976",
        "1977", "1978", "1980", "1981", "1982", "1985", "1996", "NULL", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::string> nameSetBornOrder = {"Max von Sydow", "Gene Hackman", "Clint Eastwood", "Richard Harris",
        "Mike Nichols", "Milos Forman", "Tom Skerritt", "Jack Nicholson", "Frank Langella", "Ian McKellen", "John Hurt",
        "Al Pacino", "James L. Brooks", "James Cromwell", "Jim Cash", "Nora Ephron", "Werner Herzog", "Marshall Bell",
        "J.T. Walsh", "Jan de Bont", "Penny Marshall", "Taylor Hackford", "Tony Scott", "Danny DeVito", "Stephen Rea",
        "Diane Keaton", "Susan Sarandon", "Rob Reiner", "Takeshi Kitano", "Billy Crystal", "Christopher Guest",
        "Nancy Meyers", "Jim Broadbent", "Bruno Kirby", "Victor Garber", "John Patrick Stanley", "Ed Harris",
        "Howard Deutch", "Robin Williams", "Robert Zemeckis", "Joel Silver", "Scott Hicks", "Robert Longo",
        "Bill Pullman", "David Morse", "Zach Grenier", "Madonna", "Ron Howard", "Gary Sinise", "Bill Paxton",
        "Carrie Fisher", "Tom Hanks", "Rita Wilson", "Vincent Ward", "Nathan Lane", "Geena Davis", "Kelly McGillis",
        "Kevin Pollak", "Cameron Crowe", "Michael Clarke Duncan", "Kevin Bacon", "Chris Columbus", "Ice-T",
        "Val Kilmer", "Frank Darabont", "Patricia Clarkson", "John Goodman", "Oliver Platt", "Annabella Sciorra",
        "Hugo Weaving", "Laurence Fishburne", "Stefan Arndt", "Meg Ryan", "Bonnie Hunt", "Aaron Sorkin",
        "Rosie O Donnell", "Tom Cruise", "Demi Moore", "Kelly Preston", "Anthony Edwards", "Helen Hunt", "Lori Petty",
        "Greg Kinnear", "Keanu Reeves", "John C. Reilly", "Tom Tykwer", "Lana Wachowski", "John Cusack", "Halle Berry",
        "Matthew Fox", "Kiefer Sutherland", "Steve Zahn", "Ben Miles", "James Marshall", "Philip Seymour Hoffman",
        "Carrie-Anne Moss", "Julia Roberts", "Lilly Wachowski", "Sam Rockwell", "Cuba Gooding Jr.", "Dina Meyer",
        "Parker Posey", "Orlando Jones", "Michael Sheen", "Renee Zellweger", "David Mitchell", "Jay Mohr",
        "Brooke Langton", "River Phoenix", "Ethan Hawke", "Noah Wyle", "Regina King", "Corey Feldman", "Paul Bettany",
        "Rick Yune", "Wil Wheaton", "Dave Chappelle", "Christian Bale", "Jerry O Connell", "Charlize Theron",
        "Audrey Tautou", "Liv Tyler", "Emil Eifrem", "Christina Ricci", "Natalie Portman", "Rain", "Emile Hirsch",
        "Jonathan Lipnicki", "James Thompson", "Jessica Thompson", "Angela Scope", "Paul Blythe", "Naomie Harris"};

    std::vector<std::string> nameSetNameOrder = {"Aaron Sorkin", "Al Pacino", "Angela Scope", "Annabella Sciorra",
        "Anthony Edwards", "Audrey Tautou", "Ben Miles", "Bill Paxton", "Bill Pullman", "Billy Crystal", "Bonnie Hunt",
        "Brooke Langton", "Bruno Kirby", "Cameron Crowe", "Carrie Fisher", "Carrie-Anne Moss", "Charlize Theron",
        "Chris Columbus", "Christian Bale", "Christina Ricci", "Christopher Guest", "Clint Eastwood", "Corey Feldman",
        "Cuba Gooding Jr.", "Danny DeVito", "Dave Chappelle", "David Mitchell", "David Morse", "Demi Moore",
        "Diane Keaton", "Dina Meyer", "Ed Harris", "Emil Eifrem", "Emile Hirsch", "Ethan Hawke", "Frank Darabont",
        "Frank Langella", "Gary Sinise", "Geena Davis", "Gene Hackman", "Greg Kinnear", "Halle Berry", "Helen Hunt",
        "Howard Deutch", "Hugo Weaving", "Ian McKellen", "Ice-T", "J.T. Walsh", "Jack Nicholson", "James Cromwell",
        "James L. Brooks", "James Marshall", "James Thompson", "Jan de Bont", "Jay Mohr", "Jerry O Connell",
        "Jessica Thompson", "Jim Broadbent", "Jim Cash", "Joel Silver", "John C. Reilly", "John Cusack", "John Goodman",
        "John Hurt", "John Patrick Stanley", "Jonathan Lipnicki", "Julia Roberts", "Keanu Reeves", "Kelly McGillis",
        "Kelly Preston", "Kevin Bacon", "Kevin Pollak", "Kiefer Sutherland", "Lana Wachowski", "Laurence Fishburne",
        "Lilly Wachowski", "Liv Tyler", "Lori Petty", "Madonna", "Marshall Bell", "Matthew Fox", "Max von Sydow",
        "Meg Ryan", "Michael Clarke Duncan", "Michael Sheen", "Mike Nichols", "Milos Forman", "Nancy Meyers",
        "Naomie Harris", "Natalie Portman", "Nathan Lane", "Noah Wyle", "Nora Ephron", "Oliver Platt", "Orlando Jones",
        "Parker Posey", "Patricia Clarkson", "Paul Bettany", "Paul Blythe", "Penny Marshall", "Philip Seymour Hoffman",
        "Rain", "Regina King", "Renee Zellweger", "Richard Harris", "Rick Yune", "Rita Wilson", "River Phoenix",
        "Rob Reiner", "Robert Longo", "Robert Zemeckis", "Robin Williams", "Ron Howard", "Rosie O Donnell",
        "Sam Rockwell", "Scott Hicks", "Stefan Arndt", "Stephen Rea", "Steve Zahn", "Susan Sarandon", "Takeshi Kitano",
        "Taylor Hackford", "Tom Cruise", "Tom Hanks", "Tom Skerritt", "Tom Tykwer", "Tony Scott", "Val Kilmer",
        "Victor Garber", "Vincent Ward", "Werner Herzog", "Wil Wheaton", "Zach Grenier"};

    std::vector<std::string> bornSetNameOrder = {"1961", "1940", "NULL", "1960", "1962", "1976", "1967", "1955", "1953",
        "1948", "1961", "1970", "1949", "1957", "1956", "1967", "1975", "1958", "1974", "1980", "1948", "1930", "1971",
        "1968", "1944", "1973", "1969", "1953", "1962", "1946", "1968", "1950", "1978", "1985", "1970", "1959", "1938",
        "1955", "1956", "1930", "1963", "1966", "1963", "1950", "1960", "1939", "1958", "1943", "1937", "1940", "1940",
        "1967", "NULL", "1943", "1970", "1974", "NULL", "1949", "1941", "1952", "1965", "1966", "1960", "1940", "1950",
        "1996", "1967", "1964", "1957", "1962", "1958", "1957", "1966", "1965", "1961", "1967", "1977", "1963", "1954",
        "1942", "1966", "1929", "1961", "1957", "1969", "1931", "1932", "1949", "NULL", "1981", "1956", "1971", "1941",
        "1960", "1968", "1968", "1959", "1971", "NULL", "1943", "1967", "1982", "1971", "1969", "1930", "1971", "1956",
        "1970", "1947", "1953", "1951", "1951", "1954", "1962", "1968", "1953", "1961", "1946", "1967", "1946", "1947",
        "1944", "1962", "1956", "1933", "1965", "1944", "1959", "1949", "1956", "1942", "1972", "1954"};

    std::vector<std::string> personSetShuffle = {
        "{\"label\":\"PERSON\",\"identity\":\"68\",\"properties\":{\"NAME\":\"Tony Scott\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"49\",\"properties\":{\"NAME\":\"Taylor Hackford\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"167\",\"properties\":{\"NAME\":\"Penny Marshall\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"125\",\"properties\":{\"NAME\":\"Tom Tykwer\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"124\",\"properties\":{\"NAME\":\"Jim Broadbent\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"121\",\"properties\":{\"NAME\":\"Ice-T\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"123\",\"properties\":{\"NAME\":\"Halle Berry\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"122\",\"properties\":{\"NAME\":\"Robert Longo\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"120\",\"properties\":{\"NAME\":\"Dina Meyer\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"126\",\"properties\":{\"NAME\":\"David Mitchell\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"127\",\"properties\":{\"NAME\":\"Stefan Arndt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
        "{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
        "{\"label\":\"PERSON\",\"identity\":\"130\",\"properties\":{\"NAME\":\"Paul Bettany\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"131\",\"properties\":{\"NAME\":\"Ron Howard\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
        "{\"label\":\"PERSON\",\"identity\":\"133\",\"properties\":{\"NAME\":\"Stephen Rea\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"134\",\"properties\":{\"NAME\":\"John Hurt\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"119\",\"properties\":{\"NAME\":\"Takeshi Kitano\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"116\",\"properties\":{\"NAME\":\"Mike Nichols\",\"BORN\":1931}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"114\",\"properties\":{\"NAME\":\"Christian Bale\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"113\",\"properties\":{\"NAME\":\"Howard Deutch\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"112\",\"properties\":{\"NAME\":\"Orlando Jones\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"110\",\"properties\":{\"NAME\":\"Brooke Langton\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}",
        "{\"label\":\"PERSON\",\"identity\":\"108\",\"properties\":{\"NAME\":\"Bruno Kirby\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"107\",\"properties\":{\"NAME\":\"Carrie Fisher\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"106\",\"properties\":{\"NAME\":\"Billy Crystal\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"104\",\"properties\":{\"NAME\":\"John Patrick Stanley\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
        "{\"label\":\"PERSON\",\"identity\":\"137\",\"properties\":{\"NAME\":\"John Goodman\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"138\",\"properties\":{\"NAME\":\"Susan Sarandon\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"143\",\"properties\":{\"NAME\":\"Michael Clarke Duncan\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"144\",\"properties\":{\"NAME\":\"David Morse\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"147\",\"properties\":{\"NAME\":\"Patricia Clarkson\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"148\",\"properties\":{\"NAME\":\"Frank Darabont\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
        "{\"label\":\"PERSON\",\"identity\":\"150\",\"properties\":{\"NAME\":\"Michael Sheen\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"153\",\"properties\":{\"NAME\":\"John C. Reilly\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"154\",\"properties\":{\"NAME\":\"Ed Harris\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"157\",\"properties\":{\"NAME\":\"Jan de Bont\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"158\",\"properties\":{\"NAME\":\"Robert Zemeckis\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"159\",\"properties\":{\"NAME\":\"Milos Forman\",\"BORN\":1932}}",
        "{\"label\":\"PERSON\",\"identity\":\"160\",\"properties\":{\"NAME\":\"Diane Keaton\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"161\",\"properties\":{\"NAME\":\"Nancy Meyers\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"162\",\"properties\":{\"NAME\":\"Chris Columbus\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"163\",\"properties\":{\"NAME\":\"Julia Roberts\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"164\",\"properties\":{\"NAME\":\"Madonna\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"165\",\"properties\":{\"NAME\":\"Geena Davis\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"166\",\"properties\":{\"NAME\":\"Lori Petty\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"139\",\"properties\":{\"NAME\":\"Matthew Fox\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"43\",\"properties\":{\"NAME\":\"Lilly Wachowski\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"44\",\"properties\":{\"NAME\":\"Lana Wachowski\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"45\",\"properties\":{\"NAME\":\"Joel Silver\",\"BORN\":1952}}",
        "{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"52\",\"properties\":{\"NAME\":\"Demi Moore\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"55\",\"properties\":{\"NAME\":\"Noah Wyle\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"57\",\"properties\":{\"NAME\":\"Kevin Pollak\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"59\",\"properties\":{\"NAME\":\"James Marshall\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"60\",\"properties\":{\"NAME\":\"Christopher Guest\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"61\",\"properties\":{\"NAME\":\"Rob Reiner\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"62\",\"properties\":{\"NAME\":\"Aaron Sorkin\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"63\",\"properties\":{\"NAME\":\"Kelly McGillis\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"64\",\"properties\":{\"NAME\":\"Val Kilmer\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"65\",\"properties\":{\"NAME\":\"Anthony Edwards\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"69\",\"properties\":{\"NAME\":\"Jim Cash\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"70\",\"properties\":{\"NAME\":\"Renee Zellweger\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"71\",\"properties\":{\"NAME\":\"Kelly Preston\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"102\",\"properties\":{\"NAME\":\"Victor Garber\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"101\",\"properties\":{\"NAME\":\"Bill Pullman\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"100\",\"properties\":{\"NAME\":\"Rita Wilson\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"99\",\"properties\":{\"NAME\":\"Nora Ephron\",\"BORN\":1941}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"96\",\"properties\":{\"NAME\":\"Dave Chappelle\",\"BORN\":1973}}",
        "{\"label\":\"PERSON\",\"identity\":\"95\",\"properties\":{\"NAME\":\"Parker Posey\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"94\",\"properties\":{\"NAME\":\"Scott Hicks\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"91\",\"properties\":{\"NAME\":\"Ethan Hawke\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"90\",\"properties\":{\"NAME\":\"Vincent Ward\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"88\",\"properties\":{\"NAME\":\"Werner Herzog\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"73\",\"properties\":{\"NAME\":\"Jay Mohr\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"75\",\"properties\":{\"NAME\":\"Regina King\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
        "{\"label\":\"PERSON\",\"identity\":\"77\",\"properties\":{\"NAME\":\"Cameron Crowe\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"78\",\"properties\":{\"NAME\":\"River Phoenix\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"79\",\"properties\":{\"NAME\":\"Corey Feldman\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"80\",\"properties\":{\"NAME\":\"Wil Wheaton\",\"BORN\":1972}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"86\",\"properties\":{\"NAME\":\"Annabella Sciorra\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"85\",\"properties\":{\"NAME\":\"James L. Brooks\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"81\",\"properties\":{\"NAME\":\"John Cusack\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"171\",\"properties\":{\"NAME\":\"James Thompson\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"170\",\"properties\":{\"NAME\":\"Jessica Thompson\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"169\",\"properties\":{\"NAME\":\"Angela Scope\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"168\",\"properties\":{\"NAME\":\"Paul Blythe\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"142\",\"properties\":{\"NAME\":\"Naomie Harris\"}}"};

    std::vector<std::string> personSetRolesOrder = {
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"164\",\"properties\":{\"NAME\":\"Madonna\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"110\",\"properties\":{\"NAME\":\"Brooke Langton\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"86\",\"properties\":{\"NAME\":\"Annabella Sciorra\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"71\",\"properties\":{\"NAME\":\"Kelly Preston\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"73\",\"properties\":{\"NAME\":\"Jay Mohr\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"144\",\"properties\":{\"NAME\":\"David Morse\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"63\",\"properties\":{\"NAME\":\"Kelly McGillis\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"78\",\"properties\":{\"NAME\":\"River Phoenix\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"112\",\"properties\":{\"NAME\":\"Orlando Jones\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"55\",\"properties\":{\"NAME\":\"Noah Wyle\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"150\",\"properties\":{\"NAME\":\"Michael Sheen\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"81\",\"properties\":{\"NAME\":\"John Cusack\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"114\",\"properties\":{\"NAME\":\"Christian Bale\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"70\",\"properties\":{\"NAME\":\"Renee Zellweger\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"165\",\"properties\":{\"NAME\":\"Geena Davis\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"60\",\"properties\":{\"NAME\":\"Christopher Guest\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}",
        "{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"133\",\"properties\":{\"NAME\":\"Stephen Rea\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"160\",\"properties\":{\"NAME\":\"Diane Keaton\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
        "{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"154\",\"properties\":{\"NAME\":\"Ed Harris\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"65\",\"properties\":{\"NAME\":\"Anthony Edwards\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"80\",\"properties\":{\"NAME\":\"Wil Wheaton\",\"BORN\":1972}}",
        "{\"label\":\"PERSON\",\"identity\":\"102\",\"properties\":{\"NAME\":\"Victor Garber\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"106\",\"properties\":{\"NAME\":\"Billy Crystal\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"134\",\"properties\":{\"NAME\":\"John Hurt\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"64\",\"properties\":{\"NAME\":\"Val Kilmer\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"91\",\"properties\":{\"NAME\":\"Ethan Hawke\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"121\",\"properties\":{\"NAME\":\"Ice-T\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"120\",\"properties\":{\"NAME\":\"Dina Meyer\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"108\",\"properties\":{\"NAME\":\"Bruno Kirby\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"163\",\"properties\":{\"NAME\":\"Julia Roberts\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"143\",\"properties\":{\"NAME\":\"Michael Clarke Duncan\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"96\",\"properties\":{\"NAME\":\"Dave Chappelle\",\"BORN\":1973}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"166\",\"properties\":{\"NAME\":\"Lori Petty\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"52\",\"properties\":{\"NAME\":\"Demi Moore\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"57\",\"properties\":{\"NAME\":\"Kevin Pollak\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"123\",\"properties\":{\"NAME\":\"Halle Berry\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"62\",\"properties\":{\"NAME\":\"Aaron Sorkin\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"75\",\"properties\":{\"NAME\":\"Regina King\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"107\",\"properties\":{\"NAME\":\"Carrie Fisher\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"147\",\"properties\":{\"NAME\":\"Patricia Clarkson\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"142\",\"properties\":{\"NAME\":\"Naomie Harris\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"138\",\"properties\":{\"NAME\":\"Susan Sarandon\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"95\",\"properties\":{\"NAME\":\"Parker Posey\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"153\",\"properties\":{\"NAME\":\"John C. Reilly\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"59\",\"properties\":{\"NAME\":\"James Marshall\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"137\",\"properties\":{\"NAME\":\"John Goodman\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"139\",\"properties\":{\"NAME\":\"Matthew Fox\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"130\",\"properties\":{\"NAME\":\"Paul Bettany\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
        "{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
        "{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"100\",\"properties\":{\"NAME\":\"Rita Wilson\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"119\",\"properties\":{\"NAME\":\"Takeshi Kitano\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"79\",\"properties\":{\"NAME\":\"Corey Feldman\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"88\",\"properties\":{\"NAME\":\"Werner Herzog\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}",
        "{\"label\":\"PERSON\",\"identity\":\"124\",\"properties\":{\"NAME\":\"Jim Broadbent\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"101\",\"properties\":{\"NAME\":\"Bill Pullman\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}"};

    std::vector<std::string> bornSetRolesOrder = {"1966", "1942", "1960", "1960", "1960", "1956", "1968", "1954",
        "1951", "1970", "1960", "1961", "1951", "1962", "1956", "1962", "1955", "1930", "1960", "1955", "1970", "1960",
        "1953", "1955", "1958", "1963", "1961", "1967", "1957", "1970", "1951", "1956", "1968", "1937", "1968", "1971",
        "1967", "1969", "1961", "1966", "1974", "1962", "1969", "1956", "1963", "1956", "1948", "1967", "1967", "1954",
        "1978", "1930", "1946", "1946", "1981", "1977", "1974", "1943", "1963", "1968", "1955", "1950", "1967", "1962",
        "1972", "1949", "1967", "1948", "1937", "1956", "1940", "1937", "1959", "1970", "1958", "1958", "1958", "1968",
        "1961", "1968", "1962", "1949", "1956", "1956", "1930", "1967", "1956", "1956", "1957", "1940", "1964", "1940",
        "1964", "1961", "1971", "1963", "1955", "1973", "1964", "1963", "1961", "1930", "1962", "1943", "1962", "1966",
        "1957", "1966", "1961", "1971", "1956", "1944", "1975", "1962", "1959", "1937", "NULL", "1946", "1961", "1961",
        "1961", "1942", "1956", "1929", "1964", "1964", "1964", "1968", "1956", "1965", "1967", "1960", "1966", "1982",
        "1937", "1996", "1956", "1938", "1944", "1968", "1960", "1967", "1961", "1956", "1930", "1964", "1971", "1963",
        "1939", "1976", "1985", "1954", "1956", "1982", "1947", "1971", "1971", "1942", "1929", "1975", "1967", "1967",
        "1967", "1980", "1960", "1974", "1933", "1949", "1953", "1940", "1968", "1956"};

    std::vector<std::string> bornSetBornOrder2 = {"1943", "1949", "1961", "1939", "1929", "1957", "1951", "1947",
        "1961", "1931", "1953", "1941", "1957", "1961", "1933", "1953", "1959", "1957", "1961", "1947", "1953", "1949",
        "1951", "1943", "1949", "1955", "1941", "1937", "1949", "1953", "1943", "1957", "1959", "1955", "1959", "1961",
        "1958", "1968", "1970", "1930", "1930", "1930", "1954", "1968", "1950", "1974", "1956", "1966", "1976", "1954",
        "1946", "1940", "1960", "1946", "1964", "1980", "1982", "1968", "1938", "1960", "1944", "1950", "1932", "1946",
        "1958", "1954", "1956", "1966", "1960", "1952", "1978", "1940", "1944", "1962", "1962", "1958", "1966", "1968",
        "1948", "1962", "1944", "1962", "1974", "1970", "1996", "1970", "1972", "1966", "1942", "1948", "1940", "1956",
        "1970", "1956", "1956", "1942", "1960", "1940", "1962", "1968", "1956", "1950", "1967", "1963", "1975", "1965",
        "1973", "1967", "1967", "1967", "1967", "1965", "1963", "1977", "1971", "1969", "1981", "1971", "1965", "1967",
        "1985", "1969", "1971", "1967", "1971", "1963", "1971", "1969", "NULL", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::string> nameSetShuffle = {"Jim Broadbent", "Keanu Reeves", "Tom Tykwer", "Laurence Fishburne",
        "David Mitchell", "Lilly Wachowski", "Stefan Arndt", "Joel Silver", "Ian McKellen", "Charlize Theron",
        "Audrey Tautou", "Taylor Hackford", "Paul Bettany", "Jack Nicholson", "Ron Howard", "Kevin Bacon",
        "Natalie Portman", "Noah Wyle", "Stephen Rea", "Kevin Pollak", "John Hurt", "James Marshall", "Ben Miles",
        "Rob Reiner", "Emile Hirsch", "Kelly McGillis", "John Goodman", "Anthony Edwards", "Susan Sarandon", "Meg Ryan",
        "Matthew Fox", "Jim Cash", "Christina Ricci", "Kelly Preston", "Rain", "Jay Mohr", "Naomie Harris",
        "Regina King", "Michael Clarke Duncan", "Cameron Crowe", "David Morse", "Corey Feldman", "Sam Rockwell",
        "John Cusack", "Gary Sinise", "Helen Hunt", "Patricia Clarkson", "James L. Brooks", "Frank Darabont",
        "Max von Sydow", "Frank Langella", "Robin Williams", "Michael Sheen", "Ethan Hawke", "Oliver Platt",
        "James Cromwell", "Danny DeVito", "Parker Posey", "John C. Reilly", "Steve Zahn", "Ed Harris", "Nora Ephron",
        "Bill Paxton", "Bill Pullman", "Philip Seymour Hoffman", "Rosie O Donnell", "Jan de Bont", "Nathan Lane",
        "Robert Zemeckis", "Carrie Fisher", "Milos Forman", "Liv Tyler", "Diane Keaton", "Gene Hackman", "Nancy Meyers",
        "Howard Deutch", "Chris Columbus", "Zach Grenier", "Julia Roberts", "Richard Harris", "Madonna",
        "Takeshi Kitano", "Geena Davis", "Ice-T", "Lori Petty", "Halle Berry", "Penny Marshall", "Hugo Weaving",
        "Paul Blythe", "Emil Eifrem", "Angela Scope", "Tom Cruise", "Jessica Thompson", "Kiefer Sutherland",
        "James Thompson", "J.T. Walsh", "NULL", "Aaron Sorkin", "NULL", "Tom Skerritt", "NULL", "Renee Zellweger",
        "NULL", "Bonnie Hunt", "NULL", "River Phoenix", "NULL", "Marshall Bell", "NULL", "Annabella Sciorra", "NULL",
        "Vincent Ward", "NULL", "Scott Hicks", "NULL", "Tom Hanks", "NULL", "Victor Garber", "NULL", "Billy Crystal",
        "NULL", "Brooke Langton", "NULL", "Christian Bale", "NULL", "Clint Eastwood", "NULL", "Robert Longo", "NULL",
        "Lana Wachowski", "NULL", "Demi Moore", "NULL", "Christopher Guest", "NULL", "Tony Scott", "NULL",
        "Jonathan Lipnicki", "NULL", "Greg Kinnear", "NULL", "Rick Yune", "NULL", "Rita Wilson", "NULL", "Bruno Kirby",
        "NULL", "Mike Nichols", "NULL", "Carrie-Anne Moss", "NULL", "Cuba Gooding Jr.", "NULL", "Jerry O Connell",
        "NULL", "Werner Herzog", "NULL", "John Patrick Stanley", "NULL", "Dina Meyer", "NULL", "Val Kilmer", "NULL",
        "Dave Chappelle", "Al Pacino", "NULL", "NULL", "NULL", "NULL", "Orlando Jones", "Wil Wheaton"};

    std::vector<std::string> personSetShuffle2 = {
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"40\",\"properties\":{\"NAME\":\"Carrie-Anne Moss\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"41\",\"properties\":{\"NAME\":\"Laurence Fishburne\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"42\",\"properties\":{\"NAME\":\"Hugo Weaving\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
        "{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
        "{\"label\":\"PERSON\",\"identity\":\"52\",\"properties\":{\"NAME\":\"Demi Moore\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"53\",\"properties\":{\"NAME\":\"Kevin Bacon\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"54\",\"properties\":{\"NAME\":\"Kiefer Sutherland\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"55\",\"properties\":{\"NAME\":\"Noah Wyle\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"56\",\"properties\":{\"NAME\":\"Cuba Gooding Jr.\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"57\",\"properties\":{\"NAME\":\"Kevin Pollak\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"58\",\"properties\":{\"NAME\":\"J.T. Walsh\",\"BORN\":1943}}",
        "{\"label\":\"PERSON\",\"identity\":\"59\",\"properties\":{\"NAME\":\"James Marshall\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"60\",\"properties\":{\"NAME\":\"Christopher Guest\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"62\",\"properties\":{\"NAME\":\"Aaron Sorkin\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"63\",\"properties\":{\"NAME\":\"Kelly McGillis\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"64\",\"properties\":{\"NAME\":\"Val Kilmer\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"65\",\"properties\":{\"NAME\":\"Anthony Edwards\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"67\",\"properties\":{\"NAME\":\"Meg Ryan\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"70\",\"properties\":{\"NAME\":\"Renee Zellweger\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"71\",\"properties\":{\"NAME\":\"Kelly Preston\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"73\",\"properties\":{\"NAME\":\"Jay Mohr\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"74\",\"properties\":{\"NAME\":\"Bonnie Hunt\",\"BORN\":1961}}",
        "{\"label\":\"PERSON\",\"identity\":\"75\",\"properties\":{\"NAME\":\"Regina King\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
        "{\"label\":\"PERSON\",\"identity\":\"78\",\"properties\":{\"NAME\":\"River Phoenix\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"79\",\"properties\":{\"NAME\":\"Corey Feldman\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"80\",\"properties\":{\"NAME\":\"Wil Wheaton\",\"BORN\":1972}}",
        "{\"label\":\"PERSON\",\"identity\":\"81\",\"properties\":{\"NAME\":\"John Cusack\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"82\",\"properties\":{\"NAME\":\"Marshall Bell\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"83\",\"properties\":{\"NAME\":\"Helen Hunt\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"84\",\"properties\":{\"NAME\":\"Greg Kinnear\",\"BORN\":1963}}",
        "{\"label\":\"PERSON\",\"identity\":\"86\",\"properties\":{\"NAME\":\"Annabella Sciorra\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
        "{\"label\":\"PERSON\",\"identity\":\"88\",\"properties\":{\"NAME\":\"Werner Herzog\",\"BORN\":1942}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"89\",\"properties\":{\"NAME\":\"Robin Williams\",\"BORN\":1951}}",
        "{\"label\":\"PERSON\",\"identity\":\"91\",\"properties\":{\"NAME\":\"Ethan Hawke\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"92\",\"properties\":{\"NAME\":\"Rick Yune\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"95\",\"properties\":{\"NAME\":\"Parker Posey\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"96\",\"properties\":{\"NAME\":\"Dave Chappelle\",\"BORN\":1973}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"97\",\"properties\":{\"NAME\":\"Steve Zahn\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"98\",\"properties\":{\"NAME\":\"Tom Hanks\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"100\",\"properties\":{\"NAME\":\"Rita Wilson\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"101\",\"properties\":{\"NAME\":\"Bill Pullman\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"102\",\"properties\":{\"NAME\":\"Victor Garber\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"103\",\"properties\":{\"NAME\":\"Rosie O Donnell\",\"BORN\":1962}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"105\",\"properties\":{\"NAME\":\"Nathan Lane\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"106\",\"properties\":{\"NAME\":\"Billy Crystal\",\"BORN\":1948}}",
        "{\"label\":\"PERSON\",\"identity\":\"107\",\"properties\":{\"NAME\":\"Carrie Fisher\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"108\",\"properties\":{\"NAME\":\"Bruno Kirby\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}",
        "{\"label\":\"PERSON\",\"identity\":\"110\",\"properties\":{\"NAME\":\"Brooke Langton\",\"BORN\":1970}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"112\",\"properties\":{\"NAME\":\"Orlando Jones\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"114\",\"properties\":{\"NAME\":\"Christian Bale\",\"BORN\":1974}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"115\",\"properties\":{\"NAME\":\"Zach Grenier\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
        "{\"label\":\"PERSON\",\"identity\":\"119\",\"properties\":{\"NAME\":\"Takeshi Kitano\",\"BORN\":1947}}",
        "{\"label\":\"PERSON\",\"identity\":\"120\",\"properties\":{\"NAME\":\"Dina Meyer\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"121\",\"properties\":{\"NAME\":\"Ice-T\",\"BORN\":1958}}",
        "{\"label\":\"PERSON\",\"identity\":\"123\",\"properties\":{\"NAME\":\"Halle Berry\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"124\",\"properties\":{\"NAME\":\"Jim Broadbent\",\"BORN\":1949}}",
        "{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
        "{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
        "{\"label\":\"PERSON\",\"identity\":\"130\",\"properties\":{\"NAME\":\"Paul Bettany\",\"BORN\":1971}}",
        "{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
        "{\"label\":\"PERSON\",\"identity\":\"133\",\"properties\":{\"NAME\":\"Stephen Rea\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"134\",\"properties\":{\"NAME\":\"John Hurt\",\"BORN\":1940}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"135\",\"properties\":{\"NAME\":\"Ben Miles\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
        "{\"label\":\"PERSON\",\"identity\":\"137\",\"properties\":{\"NAME\":\"John Goodman\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"138\",\"properties\":{\"NAME\":\"Susan Sarandon\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"139\",\"properties\":{\"NAME\":\"Matthew Fox\",\"BORN\":1966}}",
        "{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}",
        "{\"label\":\"PERSON\",\"identity\":\"142\",\"properties\":{\"NAME\":\"Naomie Harris\"}}",
        "{\"label\":\"PERSON\",\"identity\":\"143\",\"properties\":{\"NAME\":\"Michael Clarke Duncan\",\"BORN\":1957}}",
        "{\"label\":\"PERSON\",\"identity\":\"144\",\"properties\":{\"NAME\":\"David Morse\",\"BORN\":1953}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"145\",\"properties\":{\"NAME\":\"Sam Rockwell\",\"BORN\":1968}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"146\",\"properties\":{\"NAME\":\"Gary Sinise\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"147\",\"properties\":{\"NAME\":\"Patricia Clarkson\",\"BORN\":1959}}",
        "{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
        "{\"label\":\"PERSON\",\"identity\":\"150\",\"properties\":{\"NAME\":\"Michael Sheen\",\"BORN\":1969}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"151\",\"properties\":{\"NAME\":\"Oliver Platt\",\"BORN\":1960}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"152\",\"properties\":{\"NAME\":\"Danny DeVito\",\"BORN\":1944}}",
        "{\"label\":\"PERSON\",\"identity\":\"153\",\"properties\":{\"NAME\":\"John C. Reilly\",\"BORN\":1965}}",
        "{\"label\":\"PERSON\",\"identity\":\"154\",\"properties\":{\"NAME\":\"Ed Harris\",\"BORN\":1950}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"155\",\"properties\":{\"NAME\":\"Bill Paxton\",\"BORN\":1955}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"156\",\"properties\":{\"NAME\":\"Philip Seymour "
        "Hoffman\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"160\",\"properties\":{\"NAME\":\"Diane Keaton\",\"BORN\":1946}}",
        "{\"label\":\"PERSON\",\"identity\":\"163\",\"properties\":{\"NAME\":\"Julia Roberts\",\"BORN\":1967}}",
        "{\"label\":\"PERSON\",\"identity\":\"164\",\"properties\":{\"NAME\":\"Madonna\",\"BORN\":1954}}",
        "{\"label\":\"PERSON\",\"identity\":\"165\",\"properties\":{\"NAME\":\"Geena Davis\",\"BORN\":1956}}",
        "{\"label\":\"PERSON\",\"identity\":\"166\",\"properties\":{\"NAME\":\"Lori Petty\",\"BORN\":1963}}"};

    std::vector<std::string> nameSetNameLenOrder = {"Rain", "Ice-T", "Madonna", "Meg Ryan", "Jay Mohr", "Jim Cash",
        "Ed Harris", "Noah Wyle", "John Hurt", "Ben Miles", "Liv Tyler", "Tom Hanks", "Rick Yune", "Al Pacino",
        "Lori Petty", "Helen Hunt", "Tony Scott", "Val Kilmer", "Rob Reiner", "Dina Meyer", "Tom Tykwer", "Steve Zahn",
        "Tom Cruise", "Demi Moore", "J.T. Walsh", "Ron Howard", "Ethan Hawke", "Nora Ephron", "Rita Wilson",
        "Scott Hicks", "John Cusack", "Halle Berry", "Nathan Lane", "Bruno Kirby", "Paul Blythe", "Stephen Rea",
        "David Morse", "Gary Sinise", "Bill Paxton", "Jan de Bont", "Geena Davis", "Matthew Fox", "Wil Wheaton",
        "Bonnie Hunt", "Regina King", "Emil Eifrem", "Joel Silver", "Kevin Bacon", "Emile Hirsch", "Kevin Pollak",
        "Angela Scope", "John Goodman", "Bill Pullman", "Paul Bettany", "Tom Skerritt", "Ian McKellen", "Stefan Arndt",
        "Gene Hackman", "Robert Longo", "Aaron Sorkin", "Zach Grenier", "Mike Nichols", "Keanu Reeves", "Greg Kinnear",
        "Nancy Meyers", "Diane Keaton", "Milos Forman", "Vincent Ward", "Danny DeVito", "Hugo Weaving", "Oliver Platt",
        "Sam Rockwell", "Parker Posey", "Audrey Tautou", "Julia Roberts", "Naomie Harris", "Jim Broadbent",
        "Michael Sheen", "Cameron Crowe", "Marshall Bell", "Corey Feldman", "Max von Sydow", "Werner Herzog",
        "River Phoenix", "Kelly Preston", "Victor Garber", "Billy Crystal", "Orlando Jones", "Carrie Fisher",
        "Howard Deutch", "James Thompson", "Jack Nicholson", "Brooke Langton", "Lana Wachowski", "Christian Bale",
        "Robin Williams", "John C. Reilly", "Frank Darabont", "Frank Langella", "Dave Chappelle", "James Cromwell",
        "Takeshi Kitano", "Clint Eastwood", "David Mitchell", "Penny Marshall", "Kelly McGillis", "James Marshall",
        "Chris Columbus", "Richard Harris", "Susan Sarandon", "Lilly Wachowski", "James L. Brooks", "Robert Zemeckis",
        "Taylor Hackford", "Natalie Portman", "Renee Zellweger", "Rosie O Donnell", "Christina Ricci",
        "Jerry O Connell", "Anthony Edwards", "Charlize Theron", "Jessica Thompson", "Carrie-Anne Moss",
        "Cuba Gooding Jr.", "Christopher Guest", "Kiefer Sutherland", "Jonathan Lipnicki", "Patricia Clarkson",
        "Annabella Sciorra", "Laurence Fishburne", "John Patrick Stanley", "Michael Clarke Duncan",
        "Philip Seymour Hoffman"};

    std::vector<std::string> bornSetNullAtTail = {"1943", "1963", "1956", "1954", "1967", "1958", "1949", "1946",
        "1932", "1951", "1943", "1967", "1955", "1950", "1965", "1944", "1960", "1969", "1938", "1959", "1959", "1955",
        "1968", "1953", "1957", "1982", "1980", "1964", "1946", "1960", "1985", "1967", "1940", "1946", "1981", "1954",
        "1971", "1976", "1939", "1961", "1969", "1965", "1949", "1966", "1953", "1958", "1968", "1947", "1930", "1930",
        "1931", "1954", "1974", "1950", "1968", "1930", "1970", "1977", "1949", "1956", "1948", "1956", "1950", "1962",
        "1966", "1967", "1961", "1960", "1967", "1965", "1952", "1978", "1975", "1940", "1944", "1962", "1937", "1949",
        "1958", "1966", "1971", "1968", "1957", "1943", "1967", "1948", "1947", "1961", "1957", "1959", "1962", "1933",
        "1961", "1944", "1941", "1969", "1962", "1953", "1956", "1941", "1956", "1967", "1973", "1968", "1953", "1940",
        "1971", "1970", "1956", "1951", "1942", "1929", "1960", "1962", "1940", "1963", "1963", "1942", "1966", "1972",
        "1971", "1970", "1957", "1974", "1996", "1971", "1961", "1970", "NULL", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::string> bornSetBornOrderAsc = {"1929", "1930", "1930", "1930", "1931", "1932", "1933", "1937",
        "1938", "1939", "1940", "1940", "1940", "1940", "1941", "1941", "1942", "1942", "1943", "1943", "1943", "1944",
        "1944", "1944", "1946", "1946", "1946", "1947", "1947", "1948", "1948", "1949", "1949", "1949", "1949", "1950",
        "1950", "1950", "1951", "1951", "1952", "1953", "1953", "1953", "1953", "1954", "1954", "1954", "1955", "1955",
        "1956", "1956", "1956", "1956", "1956", "1956", "1957", "1957", "1957", "1957", "1958", "1958", "1958", "1959",
        "1959", "1959", "1960", "1960", "1960", "1960", "1961", "1961", "1961", "1961", "1961", "1962", "1962", "1962",
        "1962", "1962", "1963", "1963", "1963", "1964", "1965", "1965", "1965", "1966", "1966", "1966", "1966", "1967",
        "1967", "1967", "1967", "1967", "1967", "1967", "1968", "1968", "1968", "1968", "1968", "1969", "1969", "1969",
        "1970", "1970", "1970", "1970", "1971", "1971", "1971", "1971", "1971", "1972", "1973", "1974", "1974", "1975",
        "1976", "1977", "1978", "1980", "1981", "1982", "1985", "1996", "9999", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL"};

    std::vector<std::string> bornSetBornOrderDesc = {"9999", "1996", "1985", "1982", "1981", "1980", "1978", "1977",
        "1976", "1975", "1974", "1974", "1973", "1972", "1971", "1971", "1971", "1971", "1971", "1970", "1970", "1970",
        "1970", "1969", "1969", "1969", "1968", "1968", "1968", "1968", "1968", "1967", "1967", "1967", "1967", "1967",
        "1967", "1967", "1966", "1966", "1966", "1966", "1965", "1965", "1965", "1964", "1963", "1963", "1963", "1962",
        "1962", "1962", "1962", "1962", "1961", "1961", "1961", "1961", "1961", "1960", "1960", "1960", "1960", "1959",
        "1959", "1959", "1958", "1958", "1958", "1957", "1957", "1957", "1957", "1956", "1956", "1956", "1956", "1956",
        "1956", "1955", "1955", "1954", "1954", "1954", "1953", "1953", "1953", "1953", "1952", "1951", "1951", "1950",
        "1950", "1950", "1949", "1949", "1949", "1949", "1948", "1948", "1947", "1947", "1946", "1946", "1946", "1944",
        "1944", "1944", "1943", "1943", "1943", "1942", "1942", "1941", "1941", "1940", "1940", "1940", "1940", "1939",
        "1938", "1937", "1933", "1932", "1931", "1930", "1930", "1930", "1929", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL"};

    std::vector<std::string> bornSetBornOrderNullsFirst = {"NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "1929",
        "1930", "1930", "1930", "1931", "1932", "1933", "1937", "1938", "1939", "1940", "1940", "1940", "1940", "1941",
        "1941", "1942", "1942", "1943", "1943", "1943", "1944", "1944", "1944", "1946", "1946", "1946", "1947", "1947",
        "1948", "1948", "1949", "1949", "1949", "1949", "1950", "1950", "1950", "1951", "1951", "1952", "1953", "1953",
        "1953", "1953", "1954", "1954", "1954", "1955", "1955", "1956", "1956", "1956", "1956", "1956", "1956", "1957",
        "1957", "1957", "1957", "1958", "1958", "1958", "1959", "1959", "1959", "1960", "1960", "1960", "1960", "1961",
        "1961", "1961", "1961", "1961", "1962", "1962", "1962", "1962", "1962", "1963", "1963", "1963", "1964", "1965",
        "1965", "1965", "1966", "1966", "1966", "1966", "1967", "1967", "1967", "1967", "1967", "1967", "1967", "1968",
        "1968", "1968", "1968", "1968", "1969", "1969", "1969", "1970", "1970", "1970", "1970", "1971", "1971", "1971",
        "1971", "1971", "1972", "1973", "1974", "1974", "1975", "1976", "1977", "1978", "1980", "1981", "1982", "1985",
        "1996", "9999"};

    std::vector<std::string> nameSetNameOrderNullsFirst = {"NULL", " NULL TEST ", "Aaron Sorkin", "Al Pacino",
        "Angela Scope", "Annabella Sciorra", "Anthony Edwards", "Audrey Tautou", "Ben Miles", "Bill Paxton",
        "Bill Pullman", "Billy Crystal", "Bonnie Hunt", "Brooke Langton", "Bruno Kirby", "Cameron Crowe",
        "Carrie Fisher", "Carrie-Anne Moss", "Charlize Theron", "Chris Columbus", "Christian Bale", "Christina Ricci",
        "Christopher Guest", "Clint Eastwood", "Corey Feldman", "Cuba Gooding Jr.", "Danny DeVito", "Dave Chappelle",
        "David Mitchell", "David Morse", "Demi Moore", "Diane Keaton", "Dina Meyer", "Ed Harris", "Emil Eifrem",
        "Emile Hirsch", "Ethan Hawke", "Frank Darabont", "Frank Langella", "Gary Sinise", "Geena Davis", "Gene Hackman",
        "Greg Kinnear", "Halle Berry", "Helen Hunt", "Howard Deutch", "Hugo Weaving", "Ian McKellen", "Ice-T",
        "J.T. Walsh", "Jack Nicholson", "James Cromwell", "James L. Brooks", "James Marshall", "James Thompson",
        "Jan de Bont", "Jay Mohr", "Jerry O Connell", "Jessica Thompson", "Jim Broadbent", "Jim Cash", "Joel Silver",
        "John C. Reilly", "John Cusack", "John Goodman", "John Hurt", "John Patrick Stanley", "Jonathan Lipnicki",
        "Julia Roberts", "Keanu Reeves", "Kelly McGillis", "Kelly Preston", "Kevin Bacon", "Kevin Pollak",
        "Kiefer Sutherland", "Lana Wachowski", "Laurence Fishburne", "Lilly Wachowski", "Liv Tyler", "Lori Petty",
        "Madonna", "Marshall Bell", "Matthew Fox", "Max von Sydow", "Meg Ryan", "Michael Clarke Duncan",
        "Michael Sheen", "Mike Nichols", "Milos Forman", "Nancy Meyers", "Naomie Harris", "Natalie Portman",
        "Nathan Lane", "Noah Wyle", "Nora Ephron", "Oliver Platt", "Orlando Jones", "Parker Posey", "Patricia Clarkson",
        "Paul Bettany", "Paul Blythe", "Penny Marshall", "Philip Seymour Hoffman", "Rain", "Regina King",
        "Renee Zellweger", "Richard Harris", "Rick Yune", "Rita Wilson", "River Phoenix", "Rob Reiner", "Robert Longo",
        "Robert Zemeckis", "Robin Williams", "Ron Howard", "Rosie O Donnell", "Sam Rockwell", "Scott Hicks",
        "Stefan Arndt", "Stephen Rea", "Steve Zahn", "Susan Sarandon", "Takeshi Kitano", "Taylor Hackford",
        "Tom Cruise", "Tom Hanks", "Tom Skerritt", "Tom Tykwer", "Tony Scott", "Val Kilmer", "Victor Garber",
        "Vincent Ward", "Werner Herzog", "Wil Wheaton", "Zach Grenier"};

    std::vector<std::string> bornSetNameOrderNullsFirst = {"9999", "NULL", "1961", "1940", "NULL", "1960", "1962",
        "1976", "1967", "1955", "1953", "1948", "1961", "1970", "1949", "1957", "1956", "1967", "1975", "1958", "1974",
        "1980", "1948", "1930", "1971", "1968", "1944", "1973", "1969", "1953", "1962", "1946", "1968", "1950", "1978",
        "1985", "1970", "1959", "1938", "1955", "1956", "1930", "1963", "1966", "1963", "1950", "1960", "1939", "1958",
        "1943", "1937", "1940", "1940", "1967", "NULL", "1943", "1970", "1974", "NULL", "1949", "1941", "1952", "1965",
        "1966", "1960", "1940", "1950", "1996", "1967", "1964", "1957", "1962", "1958", "1957", "1966", "1965", "1961",
        "1967", "1977", "1963", "1954", "1942", "1966", "1929", "1961", "1957", "1969", "1931", "1932", "1949", "NULL",
        "1981", "1956", "1971", "1941", "1960", "1968", "1968", "1959", "1971", "NULL", "1943", "1967", "1982", "1971",
        "1969", "1930", "1971", "1956", "1970", "1947", "1953", "1951", "1951", "1954", "1962", "1968", "1953", "1961",
        "1946", "1967", "1946", "1947", "1944", "1962", "1956", "1933", "1965", "1944", "1959", "1949", "1956", "1942",
        "1972", "1954"};

    std::vector<std::string> nameSetNameOrderDesc = {"Zach Grenier", "Wil Wheaton", "Werner Herzog", "Vincent Ward",
        "Victor Garber", "Val Kilmer", "Tony Scott", "Tom Tykwer", "Tom Skerritt", "Tom Hanks", "Tom Cruise",
        "Taylor Hackford", "Takeshi Kitano", "Susan Sarandon", "Steve Zahn", "Stephen Rea", "Stefan Arndt",
        "Scott Hicks", "Sam Rockwell", "Rosie O Donnell", "Ron Howard", "Robin Williams", "Robert Zemeckis",
        "Robert Longo", "Rob Reiner", "River Phoenix", "Rita Wilson", "Rick Yune", "Richard Harris", "Renee Zellweger",
        "Regina King", "Rain", "Philip Seymour Hoffman", "Penny Marshall", "Paul Blythe", "Paul Bettany",
        "Patricia Clarkson", "Parker Posey", "Orlando Jones", "Oliver Platt", "Nora Ephron", "Noah Wyle", "Nathan Lane",
        "Natalie Portman", "Naomie Harris", "Nancy Meyers", "Milos Forman", "Mike Nichols", "Michael Sheen",
        "Michael Clarke Duncan", "Meg Ryan", "Max von Sydow", "Matthew Fox", "Marshall Bell", "Madonna", "Lori Petty",
        "Liv Tyler", "Lilly Wachowski", "Laurence Fishburne", "Lana Wachowski", "Kiefer Sutherland", "Kevin Pollak",
        "Kevin Bacon", "Kelly Preston", "Kelly McGillis", "Keanu Reeves", "Julia Roberts", "Jonathan Lipnicki",
        "John Patrick Stanley", "John Hurt", "John Goodman", "John Cusack", "John C. Reilly", "Joel Silver", "Jim Cash",
        "Jim Broadbent", "Jessica Thompson", "Jerry O Connell", "Jay Mohr", "Jan de Bont", "James Thompson",
        "James Marshall", "James L. Brooks", "James Cromwell", "Jack Nicholson", "J.T. Walsh", "Ice-T", "Ian McKellen",
        "Hugo Weaving", "Howard Deutch", "Helen Hunt", "Halle Berry", "Greg Kinnear", "Gene Hackman", "Geena Davis",
        "Gary Sinise", "Frank Langella", "Frank Darabont", "Ethan Hawke", "Emile Hirsch", "Emil Eifrem", "Ed Harris",
        "Dina Meyer", "Diane Keaton", "Demi Moore", "David Morse", "David Mitchell", "Dave Chappelle", "Danny DeVito",
        "Cuba Gooding Jr.", "Corey Feldman", "Clint Eastwood", "Christopher Guest", "Christina Ricci", "Christian Bale",
        "Chris Columbus", "Charlize Theron", "Carrie-Anne Moss", "Carrie Fisher", "Cameron Crowe", "Bruno Kirby",
        "Brooke Langton", "Bonnie Hunt", "Billy Crystal", "Bill Pullman", "Bill Paxton", "Ben Miles", "Audrey Tautou",
        "Anthony Edwards", "Annabella Sciorra", "Angela Scope", "Al Pacino", "Aaron Sorkin", " NULL TEST ", "NULL"};

    std::vector<std::string> nameSetIsVipOrder = {" NULL TEST ", "NULL", "Billy Crystal", "Carrie Fisher",
        "Bruno Kirby", "Lilly Wachowski", "Liv Tyler", "Joel Silver", "Brooke Langton", "Charlize Theron",
        "Gene Hackman", "Taylor Hackford", "Orlando Jones", "Jack Nicholson", "Howard Deutch", "Kevin Bacon",
        "Christian Bale", "Noah Wyle", "Zach Grenier", "Kevin Pollak", "Mike Nichols", "James Marshall",
        "Richard Harris", "Rob Reiner", "Clint Eastwood", "Kelly McGillis", "Takeshi Kitano", "Anthony Edwards",
        "Dina Meyer", "Meg Ryan", "Ice-T", "Jim Cash", "Robert Longo", "Kelly Preston", "Halle Berry", "Jay Mohr",
        "Jim Broadbent", "Regina King", "Tom Tykwer", "Cameron Crowe", "David Mitchell", "Corey Feldman",
        "Stefan Arndt", "John Cusack", "Ian McKellen", "Helen Hunt", "Audrey Tautou", "James L. Brooks", "Paul Bettany",
        "Max von Sydow", "Ron Howard", "Robin Williams", "Natalie Portman", "Ethan Hawke", "Stephen Rea",
        "James Cromwell", "John Hurt", "Parker Posey", "Ben Miles", "Steve Zahn", "Emile Hirsch", "Nora Ephron",
        "John Goodman", "Bill Pullman", "Susan Sarandon", "Rosie O Donnell", "Matthew Fox", "Nathan Lane",
        "Christina Ricci", "Hugo Weaving", "Rain", "Emil Eifrem", "Naomie Harris", "Tom Cruise",
        "Michael Clarke Duncan", "Kiefer Sutherland", "David Morse", "J.T. Walsh", "Sam Rockwell", "Aaron Sorkin",
        "Gary Sinise", "Tom Skerritt", "Patricia Clarkson", "Renee Zellweger", "Frank Darabont", "Bonnie Hunt",
        "Frank Langella", "River Phoenix", "Michael Sheen", "Marshall Bell", "Oliver Platt", "Annabella Sciorra",
        "Danny DeVito", "Vincent Ward", "John C. Reilly", "Scott Hicks", "Ed Harris", "Tom Hanks", "Bill Paxton",
        "Victor Garber", "Philip Seymour Hoffman", "Keanu Reeves", "Jan de Bont", "Al Pacino", "Robert Zemeckis",
        "Cuba Gooding Jr.", "Milos Forman", "Val Kilmer", "Diane Keaton", "Jerry O Connell", "Nancy Meyers",
        "Wil Wheaton", "Chris Columbus", "Werner Herzog", "Julia Roberts", "Dave Chappelle", "Madonna",
        "John Patrick Stanley", "Geena Davis", "Demi Moore", "Lori Petty", "Tony Scott", "Penny Marshall",
        "Greg Kinnear", "Paul Blythe", "Rita Wilson", "Angela Scope", "Christopher Guest", "Rick Yune",
        "Carrie-Anne Moss", "Laurence Fishburne", "James Thompson", "Jessica Thompson", "Jonathan Lipnicki",
        "Lana Wachowski"};

    std::vector<std::string> isVipSetIsVipOrder = {"0", "1", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL",
        "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL", "NULL"};

    std::vector<std::vector<std::string>> colValuesOrderByRoles = {{"1966"}, {"1942"}, {"1960"}, {"1960"}, {"1960"},
        {"1956"}, {"1968"}, {"1954"}, {"1951"}, {"1970"}, {"1960"}, {"1961"}, {"1951"}, {"1962"}, {"1956"}, {"1962"},
        {"1955"}, {"1930"}, {"1960"}, {"1955"}, {"1970"}, {"1960"}, {"1953"}, {"1955"}, {"1958"}, {"1963"}, {"1961"},
        {"1967"}, {"1957"}, {"1970"}, {"1951"}, {"1956"}, {"1968"}, {"1937"}, {"1968"}, {"1971"}, {"1967"}, {"1969"},
        {"1961"}, {"1966"}, {"1974"}, {"1962"}, {"1969"}, {"1956"}, {"1963"}, {"1956"}, {"1948"}, {"1967"}, {"1967"},
        {"1954"}, {"1978"}, {"1930"}, {"1946"}, {"1946"}, {"1981"}, {"1977"}, {"1974"}, {"1943"}, {"1963"}, {"1968"},
        {"1955"}, {"1950"}, {"1967"}, {"1962"}, {"1972"}, {"1949"}, {"1967"}, {"1948"}, {"1937"}, {"1956"}, {"1940"},
        {"1937"}, {"1959"}, {"1970"}, {"1958"}, {"1958"}, {"1958"}, {"1968"}, {"1961"}, {"1968"}, {"1962"}, {"1949"},
        {"1956"}, {"1956"}, {"1930"}, {"1967"}, {"1956"}, {"1956"}, {"1957"}, {"1940"}, {"1964"}, {"1940"}, {"1964"},
        {"1961"}, {"1971"}, {"1963"}, {"1955"}, {"1973"}, {"1964"}, {"1963"}, {"1961"}, {"1930"}, {"1962"}, {"1943"},
        {"1962"}, {"1966"}, {"1957"}, {"1966"}, {"1961"}, {"1971"}, {"1956"}, {"1944"}, {"1975"}, {"1962"}, {"1959"},
        {"1937"}, {"NULL"}, {"1946"}, {"1961"}, {"1961"}, {"1961"}, {"1942"}, {"1956"}, {"1929"}, {"1964"}, {"1964"},
        {"1964"}, {"1968"}, {"1956"}, {"1965"}, {"1967"}, {"1960"}, {"1966"}, {"1982"}, {"1937"}, {"1996"}, {"1956"},
        {"1938"}, {"1944"}, {"1968"}, {"1960"}, {"1967"}, {"1961"}, {"1956"}, {"1930"}, {"1964"}, {"1971"}, {"1963"},
        {"1939"}, {"1976"}, {"1985"}, {"1954"}, {"1956"}, {"1982"}, {"1947"}, {"1971"}, {"1971"}, {"1942"}, {"1929"},
        {"1975"}, {"1967"}, {"1967"}, {"1967"}, {"1980"}, {"1960"}, {"1974"}, {"1933"}, {"1949"}, {"1953"}, {"1940"},
        {"1968"}, {"1956"}};

    std::vector<std::vector<std::string>> colValuesBornOrderLimit10 = {
        {"{\"label\":\"PERSON\",\"identity\":\"87\",\"properties\":{\"NAME\":\"Max von Sydow\",\"BORN\":1929}}",
            "1929"},
        {"{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
            "1930"},
        {"{\"label\":\"PERSON\",\"identity\":\"111\",\"properties\":{\"NAME\":\"Gene Hackman\",\"BORN\":1930}}",
            "1930"},
        {"{\"label\":\"PERSON\",\"identity\":\"117\",\"properties\":{\"NAME\":\"Richard Harris\",\"BORN\":1930}}",
            "1930"},
        {"{\"label\":\"PERSON\",\"identity\":\"116\",\"properties\":{\"NAME\":\"Mike Nichols\",\"BORN\":1931}}",
            "1931"},
        {"{\"label\":\"PERSON\",\"identity\":\"159\",\"properties\":{\"NAME\":\"Milos Forman\",\"BORN\":1932}}",
            "1932"},
        {"{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}", "1933"},
        {"{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
            "1937"},
        {"{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
            "1938"},
        {"{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
            "1939"}};

    std::vector<std::vector<std::string>> colValuesBornOrderDescOffset1Limit10 = {
        {"{\"label\":\"PERSON\",\"identity\":\"76\",\"properties\":{\"NAME\":\"Jonathan Lipnicki\",\"BORN\":1996}}",
            "1996"},
        {"{\"label\":\"PERSON\",\"identity\":\"136\",\"properties\":{\"NAME\":\"Emile Hirsch\",\"BORN\":1985}}",
            "1985"},
        {"{\"label\":\"PERSON\",\"identity\":\"141\",\"properties\":{\"NAME\":\"Rain\",\"BORN\":1982}}", "1982"},
        {"{\"label\":\"PERSON\",\"identity\":\"132\",\"properties\":{\"NAME\":\"Natalie Portman\",\"BORN\":1981}}",
            "1981"},
        {"{\"label\":\"PERSON\",\"identity\":\"140\",\"properties\":{\"NAME\":\"Christina Ricci\",\"BORN\":1980}}",
            "1980"},
        {"{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}", "1978"},
        {"{\"label\":\"PERSON\",\"identity\":\"109\",\"properties\":{\"NAME\":\"Liv Tyler\",\"BORN\":1977}}", "1977"},
        {"{\"label\":\"PERSON\",\"identity\":\"129\",\"properties\":{\"NAME\":\"Audrey Tautou\",\"BORN\":1976}}",
            "1976"},
        {"{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
            "1975"},
        {"{\"label\":\"PERSON\",\"identity\":\"72\",\"properties\":{\"NAME\":\"Jerry O Connell\",\"BORN\":1974}}",
            "1974"}};

    std::vector<std::vector<std::string>> colValuesBornOrderOffset3Limit10 = {
        {"{\"label\":\"PERSON\",\"identity\":\"118\",\"properties\":{\"NAME\":\"Clint Eastwood\",\"BORN\":1930}}",
            "1930"},
        {"{\"label\":\"PERSON\",\"identity\":\"116\",\"properties\":{\"NAME\":\"Mike Nichols\",\"BORN\":1931}}",
            "1931"},
        {"{\"label\":\"PERSON\",\"identity\":\"159\",\"properties\":{\"NAME\":\"Milos Forman\",\"BORN\":1932}}",
            "1932"},
        {"{\"label\":\"PERSON\",\"identity\":\"66\",\"properties\":{\"NAME\":\"Tom Skerritt\",\"BORN\":1933}}", "1933"},
        {"{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
            "1937"},
        {"{\"label\":\"PERSON\",\"identity\":\"149\",\"properties\":{\"NAME\":\"Frank Langella\",\"BORN\":1938}}",
            "1938"},
        {"{\"label\":\"PERSON\",\"identity\":\"128\",\"properties\":{\"NAME\":\"Ian McKellen\",\"BORN\":1939}}",
            "1939"},
        {"{\"label\":\"PERSON\",\"identity\":\"85\",\"properties\":{\"NAME\":\"James L. Brooks\",\"BORN\":1940}}",
            "1940"},
        {"{\"label\":\"PERSON\",\"identity\":\"93\",\"properties\":{\"NAME\":\"James Cromwell\",\"BORN\":1940}}",
            "1940"},
        {"{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}", "1940"}};

    std::vector<std::vector<std::string>> colValuesShuffleLimit10 = {
        {"{\"label\":\"PERSON\",\"identity\":\"43\",\"properties\":{\"NAME\":\"Lilly Wachowski\",\"BORN\":1967}}",
            "1967"},
        {"{\"label\":\"PERSON\",\"identity\":\"44\",\"properties\":{\"NAME\":\"Lana Wachowski\",\"BORN\":1965}}",
            "1965"},
        {"{\"label\":\"PERSON\",\"identity\":\"45\",\"properties\":{\"NAME\":\"Joel Silver\",\"BORN\":1952}}", "1952"},
        {"{\"label\":\"PERSON\",\"identity\":\"46\",\"properties\":{\"NAME\":\"Emil Eifrem\",\"BORN\":1978}}", "1978"},
        {"{\"label\":\"PERSON\",\"identity\":\"47\",\"properties\":{\"NAME\":\"Charlize Theron\",\"BORN\":1975}}",
            "1975"},
        {"{\"label\":\"PERSON\",\"identity\":\"48\",\"properties\":{\"NAME\":\"Al Pacino\",\"BORN\":1940}}", "1940"},
        {"{\"label\":\"PERSON\",\"identity\":\"49\",\"properties\":{\"NAME\":\"Taylor Hackford\",\"BORN\":1944}}",
            "1944"},
        {"{\"label\":\"PERSON\",\"identity\":\"50\",\"properties\":{\"NAME\":\"Tom Cruise\",\"BORN\":1962}}", "1962"},
        {"{\"label\":\"PERSON\",\"identity\":\"51\",\"properties\":{\"NAME\":\"Jack Nicholson\",\"BORN\":1937}}",
            "1937"},
        {"{\"label\":\"PERSON\",\"identity\":\"39\",\"properties\":{\"NAME\":\"Keanu Reeves\",\"BORN\":1964}}",
            "1964"}};

    std::vector<std::vector<std::string>> colValuesNameAscBornAcs = {{" NULL TEST ", "NULL"}, {"Aaron Sorkin", "1961"},
        {"Al Pacino", "1940"}, {"Angela Scope", "NULL"}, {"Annabella Sciorra", "1960"}, {"Anthony Edwards", "1962"},
        {"Audrey Tautou", "1976"}, {"Ben Miles", "1967"}, {"Bill Paxton", "1955"}, {"Bill Pullman", "1953"},
        {"Billy Crystal", "1948"}, {"Bonnie Hunt", "1961"}, {"Brooke Langton", "1970"}, {"Bruno Kirby", "1949"},
        {"Cameron Crowe", "1957"}, {"Carrie Fisher", "1956"}, {"Carrie-Anne Moss", "1967"}, {"Charlize Theron", "1975"},
        {"Chris Columbus", "1958"}, {"Christian Bale", "1974"}, {"Christina Ricci", "1980"},
        {"Christopher Guest", "1948"}, {"Clint Eastwood", "1930"}, {"Corey Feldman", "1971"},
        {"Cuba Gooding Jr.", "1968"}, {"Danny DeVito", "1944"}, {"Dave Chappelle", "1973"}, {"David Mitchell", "1969"},
        {"David Morse", "1953"}, {"Demi Moore", "1962"}, {"Diane Keaton", "1946"}, {"Dina Meyer", "1968"},
        {"Ed Harris", "1950"}, {"Emil Eifrem", "1978"}, {"Emile Hirsch", "1985"}, {"Ethan Hawke", "1970"},
        {"Frank Darabont", "1959"}, {"Frank Langella", "1938"}, {"Gary Sinise", "1955"}, {"Geena Davis", "1956"},
        {"Gene Hackman", "1930"}, {"Greg Kinnear", "1963"}, {"Halle Berry", "1966"}, {"Helen Hunt", "1963"},
        {"Howard Deutch", "1950"}, {"Hugo Weaving", "1960"}, {"Ian McKellen", "1939"}, {"Ice-T", "1958"},
        {"J.T. Walsh", "1943"}, {"Jack Nicholson", "1937"}, {"James Cromwell", "1940"}, {"James L. Brooks", "1940"},
        {"James Marshall", "1967"}, {"James Thompson", "NULL"}, {"Jan de Bont", "1943"}, {"Jay Mohr", "1970"},
        {"Jerry O Connell", "1974"}, {"Jessica Thompson", "NULL"}, {"Jim Broadbent", "1949"}, {"Jim Cash", "1941"},
        {"Joel Silver", "1952"}, {"John C. Reilly", "1965"}, {"John Cusack", "1966"}, {"John Goodman", "1960"},
        {"John Hurt", "1940"}, {"John Patrick Stanley", "1950"}, {"Jonathan Lipnicki", "1996"},
        {"Julia Roberts", "1967"}, {"Keanu Reeves", "1964"}, {"Kelly McGillis", "1957"}, {"Kelly Preston", "1962"},
        {"Kevin Bacon", "1958"}, {"Kevin Pollak", "1957"}, {"Kiefer Sutherland", "1966"}, {"Lana Wachowski", "1965"},
        {"Laurence Fishburne", "1961"}, {"Lilly Wachowski", "1967"}, {"Liv Tyler", "1977"}, {"Lori Petty", "1963"},
        {"Madonna", "1954"}, {"Marshall Bell", "1942"}, {"Matthew Fox", "1966"}, {"Max von Sydow", "1929"},
        {"Meg Ryan", "1961"}, {"Michael Clarke Duncan", "1957"}, {"Michael Sheen", "1969"}, {"Mike Nichols", "1931"},
        {"Milos Forman", "1932"}, {"Nancy Meyers", "1949"}, {"Naomie Harris", "NULL"}, {"Natalie Portman", "1981"},
        {"Nathan Lane", "1956"}, {"Noah Wyle", "1971"}, {"Nora Ephron", "1941"}, {"Oliver Platt", "1960"},
        {"Orlando Jones", "1968"}, {"Parker Posey", "1968"}, {"Patricia Clarkson", "1959"}, {"Paul Bettany", "1971"},
        {"Paul Blythe", "NULL"}, {"Penny Marshall", "1943"}, {"Philip Seymour Hoffman", "1967"}, {"Rain", "1982"},
        {"Regina King", "1971"}, {"Renee Zellweger", "1969"}, {"Richard Harris", "1930"}, {"Rick Yune", "1971"},
        {"Rita Wilson", "1956"}, {"River Phoenix", "1970"}, {"Rob Reiner", "1947"}, {"Robert Longo", "1953"},
        {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Ron Howard", "1954"}, {"Rosie O Donnell", "1962"},
        {"Sam Rockwell", "1968"}, {"Scott Hicks", "1953"}, {"Stefan Arndt", "1961"}, {"Stephen Rea", "1946"},
        {"Steve Zahn", "1967"}, {"Susan Sarandon", "1946"}, {"Takeshi Kitano", "1947"}, {"Taylor Hackford", "1944"},
        {"Tom Cruise", "1962"}, {"Tom Hanks", "1956"}, {"Tom Skerritt", "1933"}, {"Tom Tykwer", "1965"},
        {"Tony Scott", "1944"}, {"Val Kilmer", "1959"}, {"Victor Garber", "1949"}, {"Vincent Ward", "1956"},
        {"Werner Herzog", "1942"}, {"Wil Wheaton", "1972"}, {"Zach Grenier", "1954"}, {"NULL", "9999"}};

    std::vector<std::vector<std::string>> colValuesNameDescBornAcs = {{"Zach Grenier", "1954"}, {"Wil Wheaton", "1972"},
        {"Werner Herzog", "1942"}, {"Vincent Ward", "1956"}, {"Victor Garber", "1949"}, {"Val Kilmer", "1959"},
        {"Tony Scott", "1944"}, {"Tom Tykwer", "1965"}, {"Tom Skerritt", "1933"}, {"Tom Hanks", "1956"},
        {"Tom Cruise", "1962"}, {"Taylor Hackford", "1944"}, {"Takeshi Kitano", "1947"}, {"Susan Sarandon", "1946"},
        {"Steve Zahn", "1967"}, {"Stephen Rea", "1946"}, {"Stefan Arndt", "1961"}, {"Scott Hicks", "1953"},
        {"Sam Rockwell", "1968"}, {"Rosie O Donnell", "1962"}, {"Ron Howard", "1954"}, {"Robin Williams", "1951"},
        {"Robert Zemeckis", "1951"}, {"Robert Longo", "1953"}, {"Rob Reiner", "1947"}, {"River Phoenix", "1970"},
        {"Rita Wilson", "1956"}, {"Rick Yune", "1971"}, {"Richard Harris", "1930"}, {"Renee Zellweger", "1969"},
        {"Regina King", "1971"}, {"Rain", "1982"}, {"Philip Seymour Hoffman", "1967"}, {"Penny Marshall", "1943"},
        {"Paul Blythe", "NULL"}, {"Paul Bettany", "1971"}, {"Patricia Clarkson", "1959"}, {"Parker Posey", "1968"},
        {"Orlando Jones", "1968"}, {"Oliver Platt", "1960"}, {"Nora Ephron", "1941"}, {"Noah Wyle", "1971"},
        {"Nathan Lane", "1956"}, {"Natalie Portman", "1981"}, {"Naomie Harris", "NULL"}, {"Nancy Meyers", "1949"},
        {"Milos Forman", "1932"}, {"Mike Nichols", "1931"}, {"Michael Sheen", "1969"},
        {"Michael Clarke Duncan", "1957"}, {"Meg Ryan", "1961"}, {"Max von Sydow", "1929"}, {"Matthew Fox", "1966"},
        {"Marshall Bell", "1942"}, {"Madonna", "1954"}, {"Lori Petty", "1963"}, {"Liv Tyler", "1977"},
        {"Lilly Wachowski", "1967"}, {"Laurence Fishburne", "1961"}, {"Lana Wachowski", "1965"},
        {"Kiefer Sutherland", "1966"}, {"Kevin Pollak", "1957"}, {"Kevin Bacon", "1958"}, {"Kelly Preston", "1962"},
        {"Kelly McGillis", "1957"}, {"Keanu Reeves", "1964"}, {"Julia Roberts", "1967"}, {"Jonathan Lipnicki", "1996"},
        {"John Patrick Stanley", "1950"}, {"John Hurt", "1940"}, {"John Goodman", "1960"}, {"John Cusack", "1966"},
        {"John C. Reilly", "1965"}, {"Joel Silver", "1952"}, {"Jim Cash", "1941"}, {"Jim Broadbent", "1949"},
        {"Jessica Thompson", "NULL"}, {"Jerry O Connell", "1974"}, {"Jay Mohr", "1970"}, {"Jan de Bont", "1943"},
        {"James Thompson", "NULL"}, {"James Marshall", "1967"}, {"James L. Brooks", "1940"}, {"James Cromwell", "1940"},
        {"Jack Nicholson", "1937"}, {"J.T. Walsh", "1943"}, {"Ice-T", "1958"}, {"Ian McKellen", "1939"},
        {"Hugo Weaving", "1960"}, {"Howard Deutch", "1950"}, {"Helen Hunt", "1963"}, {"Halle Berry", "1966"},
        {"Greg Kinnear", "1963"}, {"Gene Hackman", "1930"}, {"Geena Davis", "1956"}, {"Gary Sinise", "1955"},
        {"Frank Langella", "1938"}, {"Frank Darabont", "1959"}, {"Ethan Hawke", "1970"}, {"Emile Hirsch", "1985"},
        {"Emil Eifrem", "1978"}, {"Ed Harris", "1950"}, {"Dina Meyer", "1968"}, {"Diane Keaton", "1946"},
        {"Demi Moore", "1962"}, {"David Morse", "1953"}, {"David Mitchell", "1969"}, {"Dave Chappelle", "1973"},
        {"Danny DeVito", "1944"}, {"Cuba Gooding Jr.", "1968"}, {"Corey Feldman", "1971"}, {"Clint Eastwood", "1930"},
        {"Christopher Guest", "1948"}, {"Christina Ricci", "1980"}, {"Christian Bale", "1974"},
        {"Chris Columbus", "1958"}, {"Charlize Theron", "1975"}, {"Carrie-Anne Moss", "1967"},
        {"Carrie Fisher", "1956"}, {"Cameron Crowe", "1957"}, {"Bruno Kirby", "1949"}, {"Brooke Langton", "1970"},
        {"Bonnie Hunt", "1961"}, {"Billy Crystal", "1948"}, {"Bill Pullman", "1953"}, {"Bill Paxton", "1955"},
        {"Ben Miles", "1967"}, {"Audrey Tautou", "1976"}, {"Anthony Edwards", "1962"}, {"Annabella Sciorra", "1960"},
        {"Angela Scope", "NULL"}, {"Al Pacino", "1940"}, {"Aaron Sorkin", "1961"}, {" NULL TEST ", "NULL"},
        {"NULL", "9999"}};

    std::vector<std::vector<std::string>> colValuesBornDecs = {{"NULL", "9999"}, {"Jonathan Lipnicki", "1996"},
        {"Emile Hirsch", "1985"}, {"Rain", "1982"}, {"Natalie Portman", "1981"}, {"Christina Ricci", "1980"},
        {"Emil Eifrem", "1978"}, {"Liv Tyler", "1977"}, {"Audrey Tautou", "1976"}, {"Charlize Theron", "1975"},
        {"Jerry O Connell", "1974"}, {"Christian Bale", "1974"}, {"Dave Chappelle", "1973"}, {"Wil Wheaton", "1972"},
        {"Regina King", "1971"}, {"Noah Wyle", "1971"}, {"Corey Feldman", "1971"}, {"Rick Yune", "1971"},
        {"Paul Bettany", "1971"}, {"Jay Mohr", "1970"}, {"Ethan Hawke", "1970"}, {"River Phoenix", "1970"},
        {"Brooke Langton", "1970"}, {"Renee Zellweger", "1969"}, {"David Mitchell", "1969"}, {"Michael Sheen", "1969"},
        {"Sam Rockwell", "1968"}, {"Orlando Jones", "1968"}, {"Cuba Gooding Jr.", "1968"}, {"Parker Posey", "1968"},
        {"Dina Meyer", "1968"}, {"Philip Seymour Hoffman", "1967"}, {"Lilly Wachowski", "1967"},
        {"Carrie-Anne Moss", "1967"}, {"Julia Roberts", "1967"}, {"Ben Miles", "1967"}, {"James Marshall", "1967"},
        {"Steve Zahn", "1967"}, {"John Cusack", "1966"}, {"Matthew Fox", "1966"}, {"Halle Berry", "1966"},
        {"Kiefer Sutherland", "1966"}, {"Lana Wachowski", "1965"}, {"John C. Reilly", "1965"}, {"Tom Tykwer", "1965"},
        {"Keanu Reeves", "1964"}, {"Helen Hunt", "1963"}, {"Greg Kinnear", "1963"}, {"Lori Petty", "1963"},
        {"Demi Moore", "1962"}, {"Tom Cruise", "1962"}, {"Rosie O Donnell", "1962"}, {"Anthony Edwards", "1962"},
        {"Kelly Preston", "1962"}, {"Meg Ryan", "1961"}, {"Aaron Sorkin", "1961"}, {"Stefan Arndt", "1961"},
        {"Bonnie Hunt", "1961"}, {"Laurence Fishburne", "1961"}, {"Annabella Sciorra", "1960"},
        {"Oliver Platt", "1960"}, {"John Goodman", "1960"}, {"Hugo Weaving", "1960"}, {"Val Kilmer", "1959"},
        {"Frank Darabont", "1959"}, {"Patricia Clarkson", "1959"}, {"Kevin Bacon", "1958"}, {"Ice-T", "1958"},
        {"Chris Columbus", "1958"}, {"Kevin Pollak", "1957"}, {"Cameron Crowe", "1957"}, {"Kelly McGillis", "1957"},
        {"Michael Clarke Duncan", "1957"}, {"Carrie Fisher", "1956"}, {"Tom Hanks", "1956"}, {"Rita Wilson", "1956"},
        {"Geena Davis", "1956"}, {"Nathan Lane", "1956"}, {"Vincent Ward", "1956"}, {"Gary Sinise", "1955"},
        {"Bill Paxton", "1955"}, {"Zach Grenier", "1954"}, {"Madonna", "1954"}, {"Ron Howard", "1954"},
        {"David Morse", "1953"}, {"Robert Longo", "1953"}, {"Bill Pullman", "1953"}, {"Scott Hicks", "1953"},
        {"Joel Silver", "1952"}, {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Ed Harris", "1950"},
        {"Howard Deutch", "1950"}, {"John Patrick Stanley", "1950"}, {"Bruno Kirby", "1949"}, {"Nancy Meyers", "1949"},
        {"Victor Garber", "1949"}, {"Jim Broadbent", "1949"}, {"Billy Crystal", "1948"}, {"Christopher Guest", "1948"},
        {"Rob Reiner", "1947"}, {"Takeshi Kitano", "1947"}, {"Susan Sarandon", "1946"}, {"Stephen Rea", "1946"},
        {"Diane Keaton", "1946"}, {"Tony Scott", "1944"}, {"Danny DeVito", "1944"}, {"Taylor Hackford", "1944"},
        {"J.T. Walsh", "1943"}, {"Jan de Bont", "1943"}, {"Penny Marshall", "1943"}, {"Marshall Bell", "1942"},
        {"Werner Herzog", "1942"}, {"Jim Cash", "1941"}, {"Nora Ephron", "1941"}, {"Al Pacino", "1940"},
        {"James Cromwell", "1940"}, {"John Hurt", "1940"}, {"James L. Brooks", "1940"}, {"Ian McKellen", "1939"},
        {"Frank Langella", "1938"}, {"Jack Nicholson", "1937"}, {"Tom Skerritt", "1933"}, {"Milos Forman", "1932"},
        {"Mike Nichols", "1931"}, {"Clint Eastwood", "1930"}, {"Richard Harris", "1930"}, {"Gene Hackman", "1930"},
        {"Max von Sydow", "1929"}, {" NULL TEST ", "NULL"}, {"Paul Blythe", "NULL"}, {"Naomie Harris", "NULL"},
        {"James Thompson", "NULL"}, {"Jessica Thompson", "NULL"}, {"Angela Scope", "NULL"}};

    std::vector<std::vector<std::string>> colValuesNameAcs = {{" NULL TEST ", "NULL"}, {"Aaron Sorkin", "1961"},
        {"Al Pacino", "1940"}, {"Angela Scope", "NULL"}, {"Annabella Sciorra", "1960"}, {"Anthony Edwards", "1962"},
        {"Audrey Tautou", "1976"}, {"Ben Miles", "1967"}, {"Bill Paxton", "1955"}, {"Bill Pullman", "1953"},
        {"Billy Crystal", "1948"}, {"Bonnie Hunt", "1961"}, {"Brooke Langton", "1970"}, {"Bruno Kirby", "1949"},
        {"Cameron Crowe", "1957"}, {"Carrie Fisher", "1956"}, {"Carrie-Anne Moss", "1967"}, {"Charlize Theron", "1975"},
        {"Chris Columbus", "1958"}, {"Christian Bale", "1974"}, {"Christina Ricci", "1980"},
        {"Christopher Guest", "1948"}, {"Clint Eastwood", "1930"}, {"Corey Feldman", "1971"},
        {"Cuba Gooding Jr.", "1968"}, {"Danny DeVito", "1944"}, {"Dave Chappelle", "1973"}, {"David Mitchell", "1969"},
        {"David Morse", "1953"}, {"Demi Moore", "1962"}, {"Diane Keaton", "1946"}, {"Dina Meyer", "1968"},
        {"Ed Harris", "1950"}, {"Emil Eifrem", "1978"}, {"Emile Hirsch", "1985"}, {"Ethan Hawke", "1970"},
        {"Frank Darabont", "1959"}, {"Frank Langella", "1938"}, {"Gary Sinise", "1955"}, {"Geena Davis", "1956"},
        {"Gene Hackman", "1930"}, {"Greg Kinnear", "1963"}, {"Halle Berry", "1966"}, {"Helen Hunt", "1963"},
        {"Howard Deutch", "1950"}, {"Hugo Weaving", "1960"}, {"Ian McKellen", "1939"}, {"Ice-T", "1958"},
        {"J.T. Walsh", "1943"}, {"Jack Nicholson", "1937"}, {"James Cromwell", "1940"}, {"James L. Brooks", "1940"},
        {"James Marshall", "1967"}, {"James Thompson", "NULL"}, {"Jan de Bont", "1943"}, {"Jay Mohr", "1970"},
        {"Jerry O Connell", "1974"}, {"Jessica Thompson", "NULL"}, {"Jim Broadbent", "1949"}, {"Jim Cash", "1941"},
        {"Joel Silver", "1952"}, {"John C. Reilly", "1965"}, {"John Cusack", "1966"}, {"John Goodman", "1960"},
        {"John Hurt", "1940"}, {"John Patrick Stanley", "1950"}, {"Jonathan Lipnicki", "1996"},
        {"Julia Roberts", "1967"}, {"Keanu Reeves", "1964"}, {"Kelly McGillis", "1957"}, {"Kelly Preston", "1962"},
        {"Kevin Bacon", "1958"}, {"Kevin Pollak", "1957"}, {"Kiefer Sutherland", "1966"}, {"Lana Wachowski", "1965"},
        {"Laurence Fishburne", "1961"}, {"Lilly Wachowski", "1967"}, {"Liv Tyler", "1977"}, {"Lori Petty", "1963"},
        {"Madonna", "1954"}, {"Marshall Bell", "1942"}, {"Matthew Fox", "1966"}, {"Max von Sydow", "1929"},
        {"Meg Ryan", "1961"}, {"Michael Clarke Duncan", "1957"}, {"Michael Sheen", "1969"}, {"Mike Nichols", "1931"},
        {"Milos Forman", "1932"}, {"Nancy Meyers", "1949"}, {"Naomie Harris", "NULL"}, {"Natalie Portman", "1981"},
        {"Nathan Lane", "1956"}, {"Noah Wyle", "1971"}, {"Nora Ephron", "1941"}, {"Oliver Platt", "1960"},
        {"Orlando Jones", "1968"}, {"Parker Posey", "1968"}, {"Patricia Clarkson", "1959"}, {"Paul Bettany", "1971"},
        {"Paul Blythe", "NULL"}, {"Penny Marshall", "1943"}, {"Philip Seymour Hoffman", "1967"}, {"Rain", "1982"},
        {"Regina King", "1971"}, {"Renee Zellweger", "1969"}, {"Richard Harris", "1930"}, {"Rick Yune", "1971"},
        {"Rita Wilson", "1956"}, {"River Phoenix", "1970"}, {"Rob Reiner", "1947"}, {"Robert Longo", "1953"},
        {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Ron Howard", "1954"}, {"Rosie O Donnell", "1962"},
        {"Sam Rockwell", "1968"}, {"Scott Hicks", "1953"}, {"Stefan Arndt", "1961"}, {"Stephen Rea", "1946"},
        {"Steve Zahn", "1967"}, {"Susan Sarandon", "1946"}, {"Takeshi Kitano", "1947"}, {"Taylor Hackford", "1944"},
        {"Tom Cruise", "1962"}, {"Tom Hanks", "1956"}, {"Tom Skerritt", "1933"}, {"Tom Tykwer", "1965"},
        {"Tony Scott", "1944"}, {"Val Kilmer", "1959"}, {"Victor Garber", "1949"}, {"Vincent Ward", "1956"},
        {"Werner Herzog", "1942"}, {"Wil Wheaton", "1972"}, {"Zach Grenier", "1954"}, {"NULL", "9999"}};

    std::vector<std::vector<std::string>> colValuesBornDecsRolesAsc = {{"1996", "Ray Boyd"}, {"1985", "Speed Racer"},
        {"1982", "Raizo"}, {"1982", "Taejo Togokahn"}, {"1981", "Evey Hammond"}, {"1980", "Trixie"}, {"1978", "Emil"},
        {"1977", "Faye Dolan"}, {"1976", "Sophie Neveu"}, {"1975", "Mary Ann Lomax"}, {"1975", "Tina"},
        {"1974", "Dieter Dengler"}, {"1974", "Frank Cushman"}, {"1974", "Vern Tessio"}, {"1973", "Kevin Jackson"},
        {"1972", "Gordie Lachance"}, {"1971", "Cpl. Jeffrey Barnes"}, {"1971", "Kazuo Miyamoto"},
        {"1971", "Marcee Tidwell"}, {"1971", "Silas"}, {"1971", "Takeshi"}, {"1971", "Teddy Duchamp"},
        {"1970", "Annabelle Farrell"}, {"1970", "Bob Sugar"}, {"1970", "Chris Chambers"}, {"1970", "Ishmael Chambers"},
        {"1969", "David Frost"}, {"1969", "Dorothy Boyd"}, {"1968", "Albert Lewis"}, {"1968", "Clifford Franklin"},
        {"1968", "Cpl. Carl Hammaker"}, {"1968", "Frank Sachs"}, {"1968", "James Reston, Jr."}, {"1968", "Jane"},
        {"1968", "Patricia Eden"}, {"1968", "Rod Tidwell"}, {"1968", "Wild Bill Wharton"}, {"1967", "Cass Jones"},
        {"1967", "Dascomb"}, {"1967", "Duane"}, {"1967", "Dustin Dusty Davis"}, {"1967", "George Pappas"},
        {"1967", "Gust Avrakotos"}, {"1967", "Joanne Herring"}, {"1967", "Pfc. Louden Downey"}, {"1967", "Ryan Maslow"},
        {"1967", "Trinity"}, {"1967", "Trinity"}, {"1967", "Trinity"}, {"1966", "Ace Merrill"},
        {"1966", "Denny Lachance"}, {"1966", "Lt. Jonathan Kendrick"}, {"1966", "Luisa Rey"}, {"1966", "Racer X"},
        {"1965", "Peter Pete Connelly"}, {"1964", "Johnny Mnemonic"}, {"1964", "Julian Mercer"},
        {"1964", "Kevin Lomax"}, {"1964", "Neo"}, {"1964", "Neo"}, {"1964", "Neo"}, {"1964", "Shane Falco"},
        {"1963", "Carol Connelly"}, {"1963", "Dr. Jo Harding"}, {"1963", "Frank Navasky"}, {"1963", "Kelly Frears"},
        {"1963", "Kit Keller"}, {"1963", "Simon Bishop"}, {"1962", "Avery Bishop"}, {"1962", "Becky"},
        {"1962", "Doris Murphy"}, {"1962", "Goose"}, {"1962", "Jerry Maguire"}, {"1962", "Lt. Cdr. JoAnne Galloway"},
        {"1962", "Lt. Daniel Kaffee"}, {"1962", "Maverick"}, {"1961", "Annie Reed"}, {"1961", "Carole"},
        {"1961", "DeDe"}, {"1961", "Jan Edgecomb"}, {"1961", "Kathleen Kelly"}, {"1961", "Laurel Boyd"},
        {"1961", "Man in Bar"}, {"1961", "Morpheus"}, {"1961", "Morpheus"}, {"1961", "Morpheus"},
        {"1961", "Sally Albright"}, {"1960", "Agent Smith"}, {"1960", "Agent Smith"}, {"1960", "Agent Smith"},
        {"1960", "Annie Collins-Nielsen"}, {"1960", "Bill Smoke"}, {"1960", "Bob Zelnick"}, {"1960", "Pops"},
        {"1960", "Rupert Burns"}, {"1960", "V"}, {"1959", "Iceman"}, {"1959", "Melinda Moores"},
        {"1958", "Capt. Jack Ross"}, {"1958", "J-Bone"}, {"1958", "Jack Brennan"}, {"1958", "Jack Swigert"},
        {"1957", "Charlie"}, {"1957", "John Coffey"}, {"1957", "Lt. Sam Weinberg"}, {"1956", "Albert Goldman"},
        {"1956", "Baw"}, {"1956", "Chuck Noland"}, {"1956", "Dottie Hinson"}, {"1956", "Dr. Robert Langdon"},
        {"1956", "Hero Boy"}, {"1956", "Jim Lovell"}, {"1956", "Jimmy Dugan"}, {"1956", "Joe Banks"},
        {"1956", "Joe Fox"}, {"1956", "Marie"}, {"1956", "Mr. White"}, {"1956", "Paul Edgecomb"},
        {"1956", "Rep. Charlie Wilson"}, {"1956", "Sam Baldwin"}, {"1956", "Suzy"}, {"1956", "Zachry"},
        {"1955", "Bill Harding"}, {"1955", "Bob Hinson"}, {"1955", "Burt Hammersmith"}, {"1955", "Fred Haise"},
        {"1955", "Ken Mattingly"}, {"1954", "All the Way Mae Mordabito"}, {"1954", "Eddie"}, {"1954", "Squad Leader"},
        {"1953", "Brutus Brutal Howell"}, {"1953", "Walter"}, {"1951", "Andrew Marin"}, {"1951", "Armand Goldman"},
        {"1951", "Chris Nielsen"}, {"1950", "Gene Kranz"}, {"1949", "Greg"}, {"1949", "Jess"}, {"1949", "Vyvyan Ayrs"},
        {"1948", "Dr. Stone"}, {"1948", "Harry Burns"}, {"1947", "Takahashi"}, {"1946", "Eric Finch"},
        {"1946", "Erica Barry"}, {"1946", "Mom"}, {"1944", "Martini"}, {"1944", "Robert Bobby Ciaro"},
        {"1943", "Frank Fitzsimmons"}, {"1943", "Lt. Col. Matthew Andrew Markinson"}, {"1942", "Admiral"},
        {"1942", "Mr. Lachance"}, {"1942", "The Face"}, {"1940", "High Chancellor Adam Sutler"},
        {"1940", "John Milton"}, {"1940", "Judge Fielding"}, {"1940", "Warden Hal Moores"},
        {"1939", "Sir Leight Teabing"}, {"1938", "Richard Nixon"}, {"1937", "Col. Nathan R. Jessup"},
        {"1937", "Harry Sanborn"}, {"1937", "Hoffa"}, {"1937", "Melvin Udall"}, {"1937", "Randle McMurphy"},
        {"1933", "Viper"}, {"1930", "Bill Munny"}, {"1930", "English Bob"}, {"1930", "Jimmy McGinty"},
        {"1930", "Little Bill Daggett"}, {"1930", "Sen. Kevin Keeley"}, {"1929", "Nels Gudmundsson"},
        {"1929", "The Tracker"}, {"NULL", "Mika Coretti"}};

    std::vector<std::vector<std::string>> colValuesBornDecsNameAsc = {{"NULL", "9999"}, {"Jonathan Lipnicki", "1996"},
        {"Emile Hirsch", "1985"}, {"Rain", "1982"}, {"Natalie Portman", "1981"}, {"Christina Ricci", "1980"},
        {"Emil Eifrem", "1978"}, {"Liv Tyler", "1977"}, {"Audrey Tautou", "1976"}, {"Charlize Theron", "1975"},
        {"Christian Bale", "1974"}, {"Jerry O Connell", "1974"}, {"Dave Chappelle", "1973"}, {"Wil Wheaton", "1972"},
        {"Corey Feldman", "1971"}, {"Noah Wyle", "1971"}, {"Paul Bettany", "1971"}, {"Regina King", "1971"},
        {"Rick Yune", "1971"}, {"Brooke Langton", "1970"}, {"Ethan Hawke", "1970"}, {"Jay Mohr", "1970"},
        {"River Phoenix", "1970"}, {"David Mitchell", "1969"}, {"Michael Sheen", "1969"}, {"Renee Zellweger", "1969"},
        {"Cuba Gooding Jr.", "1968"}, {"Dina Meyer", "1968"}, {"Orlando Jones", "1968"}, {"Parker Posey", "1968"},
        {"Sam Rockwell", "1968"}, {"Ben Miles", "1967"}, {"Carrie-Anne Moss", "1967"}, {"James Marshall", "1967"},
        {"Julia Roberts", "1967"}, {"Lilly Wachowski", "1967"}, {"Philip Seymour Hoffman", "1967"},
        {"Steve Zahn", "1967"}, {"Halle Berry", "1966"}, {"John Cusack", "1966"}, {"Kiefer Sutherland", "1966"},
        {"Matthew Fox", "1966"}, {"John C. Reilly", "1965"}, {"Lana Wachowski", "1965"}, {"Tom Tykwer", "1965"},
        {"Keanu Reeves", "1964"}, {"Greg Kinnear", "1963"}, {"Helen Hunt", "1963"}, {"Lori Petty", "1963"},
        {"Anthony Edwards", "1962"}, {"Demi Moore", "1962"}, {"Kelly Preston", "1962"}, {"Rosie O Donnell", "1962"},
        {"Tom Cruise", "1962"}, {"Aaron Sorkin", "1961"}, {"Bonnie Hunt", "1961"}, {"Laurence Fishburne", "1961"},
        {"Meg Ryan", "1961"}, {"Stefan Arndt", "1961"}, {"Annabella Sciorra", "1960"}, {"Hugo Weaving", "1960"},
        {"John Goodman", "1960"}, {"Oliver Platt", "1960"}, {"Frank Darabont", "1959"}, {"Patricia Clarkson", "1959"},
        {"Val Kilmer", "1959"}, {"Chris Columbus", "1958"}, {"Ice-T", "1958"}, {"Kevin Bacon", "1958"},
        {"Cameron Crowe", "1957"}, {"Kelly McGillis", "1957"}, {"Kevin Pollak", "1957"},
        {"Michael Clarke Duncan", "1957"}, {"Carrie Fisher", "1956"}, {"Geena Davis", "1956"}, {"Nathan Lane", "1956"},
        {"Rita Wilson", "1956"}, {"Tom Hanks", "1956"}, {"Vincent Ward", "1956"}, {"Bill Paxton", "1955"},
        {"Gary Sinise", "1955"}, {"Madonna", "1954"}, {"Ron Howard", "1954"}, {"Zach Grenier", "1954"},
        {"Bill Pullman", "1953"}, {"David Morse", "1953"}, {"Robert Longo", "1953"}, {"Scott Hicks", "1953"},
        {"Joel Silver", "1952"}, {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Ed Harris", "1950"},
        {"Howard Deutch", "1950"}, {"John Patrick Stanley", "1950"}, {"Bruno Kirby", "1949"}, {"Jim Broadbent", "1949"},
        {"Nancy Meyers", "1949"}, {"Victor Garber", "1949"}, {"Billy Crystal", "1948"}, {"Christopher Guest", "1948"},
        {"Rob Reiner", "1947"}, {"Takeshi Kitano", "1947"}, {"Diane Keaton", "1946"}, {"Stephen Rea", "1946"},
        {"Susan Sarandon", "1946"}, {"Danny DeVito", "1944"}, {"Taylor Hackford", "1944"}, {"Tony Scott", "1944"},
        {"J.T. Walsh", "1943"}, {"Jan de Bont", "1943"}, {"Penny Marshall", "1943"}, {"Marshall Bell", "1942"},
        {"Werner Herzog", "1942"}, {"Jim Cash", "1941"}, {"Nora Ephron", "1941"}, {"Al Pacino", "1940"},
        {"James Cromwell", "1940"}, {"James L. Brooks", "1940"}, {"John Hurt", "1940"}, {"Ian McKellen", "1939"},
        {"Frank Langella", "1938"}, {"Jack Nicholson", "1937"}, {"Tom Skerritt", "1933"}, {"Milos Forman", "1932"},
        {"Mike Nichols", "1931"}, {"Clint Eastwood", "1930"}, {"Gene Hackman", "1930"}, {"Richard Harris", "1930"},
        {"Max von Sydow", "1929"}, {" NULL TEST ", "NULL"}, {"Angela Scope", "NULL"}, {"James Thompson", "NULL"},
        {"Jessica Thompson", "NULL"}, {"Naomie Harris", "NULL"}, {"Paul Blythe", "NULL"}};

    std::vector<std::vector<std::string>> colValuesBornDecs2 = {{"NULL", "9999"}, {"Jonathan Lipnicki", "1996"},
        {"Emile Hirsch", "1985"}, {"Rain", "1982"}, {"Natalie Portman", "1981"}, {"Christina Ricci", "1980"},
        {"Emil Eifrem", "1978"}, {"Liv Tyler", "1977"}, {"Audrey Tautou", "1976"}, {"Charlize Theron", "1975"},
        {"Jerry O Connell", "1974"}, {"Christian Bale", "1974"}, {"Dave Chappelle", "1973"}, {"Wil Wheaton", "1972"},
        {"Regina King", "1971"}, {"Noah Wyle", "1971"}, {"Corey Feldman", "1971"}, {"Rick Yune", "1971"},
        {"Paul Bettany", "1971"}, {"Jay Mohr", "1970"}, {"Ethan Hawke", "1970"}, {"River Phoenix", "1970"},
        {"Brooke Langton", "1970"}, {"Renee Zellweger", "1969"}, {"David Mitchell", "1969"}, {"Michael Sheen", "1969"},
        {"Sam Rockwell", "1968"}, {"Orlando Jones", "1968"}, {"Cuba Gooding Jr.", "1968"}, {"Parker Posey", "1968"},
        {"Dina Meyer", "1968"}, {"Philip Seymour Hoffman", "1967"}, {"Lilly Wachowski", "1967"},
        {"Carrie-Anne Moss", "1967"}, {"Julia Roberts", "1967"}, {"Ben Miles", "1967"}, {"James Marshall", "1967"},
        {"Steve Zahn", "1967"}, {"John Cusack", "1966"}, {"Matthew Fox", "1966"}, {"Halle Berry", "1966"},
        {"Kiefer Sutherland", "1966"}, {"Lana Wachowski", "1965"}, {"John C. Reilly", "1965"}, {"Tom Tykwer", "1965"},
        {"Keanu Reeves", "1964"}, {"Helen Hunt", "1963"}, {"Greg Kinnear", "1963"}, {"Lori Petty", "1963"},
        {"Demi Moore", "1962"}, {"Tom Cruise", "1962"}, {"Rosie O Donnell", "1962"}, {"Anthony Edwards", "1962"},
        {"Kelly Preston", "1962"}, {"Meg Ryan", "1961"}, {"Aaron Sorkin", "1961"}, {"Stefan Arndt", "1961"},
        {"Bonnie Hunt", "1961"}, {"Laurence Fishburne", "1961"}, {"Annabella Sciorra", "1960"},
        {"Oliver Platt", "1960"}, {"John Goodman", "1960"}, {"Hugo Weaving", "1960"}, {"Val Kilmer", "1959"},
        {"Frank Darabont", "1959"}, {"Patricia Clarkson", "1959"}, {"Kevin Bacon", "1958"}, {"Ice-T", "1958"},
        {"Chris Columbus", "1958"}, {"Kevin Pollak", "1957"}, {"Cameron Crowe", "1957"}, {"Kelly McGillis", "1957"},
        {"Michael Clarke Duncan", "1957"}, {"Carrie Fisher", "1956"}, {"Tom Hanks", "1956"}, {"Rita Wilson", "1956"},
        {"Geena Davis", "1956"}, {"Nathan Lane", "1956"}, {"Vincent Ward", "1956"}, {"Gary Sinise", "1955"},
        {"Bill Paxton", "1955"}, {"Zach Grenier", "1954"}, {"Madonna", "1954"}, {"Ron Howard", "1954"},
        {"David Morse", "1953"}, {"Robert Longo", "1953"}, {"Bill Pullman", "1953"}, {"Scott Hicks", "1953"},
        {"Joel Silver", "1952"}, {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Ed Harris", "1950"},
        {"Howard Deutch", "1950"}, {"John Patrick Stanley", "1950"}, {"Bruno Kirby", "1949"}, {"Nancy Meyers", "1949"},
        {"Victor Garber", "1949"}, {"Jim Broadbent", "1949"}, {"Billy Crystal", "1948"}, {"Christopher Guest", "1948"},
        {"Rob Reiner", "1947"}, {"Takeshi Kitano", "1947"}, {"Susan Sarandon", "1946"}, {"Stephen Rea", "1946"},
        {"Diane Keaton", "1946"}, {"Tony Scott", "1944"}, {"Danny DeVito", "1944"}, {"Taylor Hackford", "1944"},
        {"J.T. Walsh", "1943"}, {"Jan de Bont", "1943"}, {"Penny Marshall", "1943"}, {"Marshall Bell", "1942"},
        {"Werner Herzog", "1942"}, {"Jim Cash", "1941"}, {"Nora Ephron", "1941"}, {"Al Pacino", "1940"},
        {"James Cromwell", "1940"}, {"John Hurt", "1940"}, {"James L. Brooks", "1940"}, {"Ian McKellen", "1939"},
        {"Frank Langella", "1938"}, {"Jack Nicholson", "1937"}, {"Tom Skerritt", "1933"}, {"Milos Forman", "1932"},
        {"Mike Nichols", "1931"}, {"Clint Eastwood", "1930"}, {"Richard Harris", "1930"}, {"Gene Hackman", "1930"},
        {"Max von Sydow", "1929"}, {"Paul Blythe", "NULL"}, {"Angela Scope", "NULL"}, {"Jessica Thompson", "NULL"},
        {"James Thompson", "NULL"}, {"Naomie Harris", "NULL"}, {" NULL TEST ", "NULL"}};

    std::vector<std::vector<std::string>> colValuesBornDecs3 = {{"NULL", "9999"}, {"Jonathan Lipnicki", "1996"},
        {"Emile Hirsch", "1985"}, {"Rain", "1982"}, {"Natalie Portman", "1981"}, {"Christina Ricci", "1980"},
        {"Emil Eifrem", "1978"}, {"Liv Tyler", "1977"}, {"Audrey Tautou", "1976"}, {"Charlize Theron", "1975"},
        {"Jerry O Connell", "1974"}, {"Christian Bale", "1974"}, {"Dave Chappelle", "1973"}, {"Wil Wheaton", "1972"},
        {"Corey Feldman", "1971"}, {"Regina King", "1971"}, {"Rick Yune", "1971"}, {"Noah Wyle", "1971"},
        {"Paul Bettany", "1971"}, {"Ethan Hawke", "1970"}, {"River Phoenix", "1970"}, {"Brooke Langton", "1970"},
        {"Jay Mohr", "1970"}, {"Renee Zellweger", "1969"}, {"Michael Sheen", "1969"}, {"David Mitchell", "1969"},
        {"Orlando Jones", "1968"}, {"Dina Meyer", "1968"}, {"Parker Posey", "1968"}, {"Cuba Gooding Jr.", "1968"},
        {"Sam Rockwell", "1968"}, {"Lilly Wachowski", "1967"}, {"Julia Roberts", "1967"}, {"Ben Miles", "1967"},
        {"Carrie-Anne Moss", "1967"}, {"Steve Zahn", "1967"}, {"Philip Seymour Hoffman", "1967"},
        {"James Marshall", "1967"}, {"Matthew Fox", "1966"}, {"Halle Berry", "1966"}, {"John Cusack", "1966"},
        {"Kiefer Sutherland", "1966"}, {"John C. Reilly", "1965"}, {"Lana Wachowski", "1965"}, {"Tom Tykwer", "1965"},
        {"Keanu Reeves", "1964"}, {"Helen Hunt", "1963"}, {"Lori Petty", "1963"}, {"Greg Kinnear", "1963"},
        {"Kelly Preston", "1962"}, {"Anthony Edwards", "1962"}, {"Rosie O Donnell", "1962"}, {"Tom Cruise", "1962"},
        {"Demi Moore", "1962"}, {"Bonnie Hunt", "1961"}, {"Aaron Sorkin", "1961"}, {"Laurence Fishburne", "1961"},
        {"Meg Ryan", "1961"}, {"Stefan Arndt", "1961"}, {"Annabella Sciorra", "1960"}, {"Oliver Platt", "1960"},
        {"Hugo Weaving", "1960"}, {"John Goodman", "1960"}, {"Patricia Clarkson", "1959"}, {"Val Kilmer", "1959"},
        {"Frank Darabont", "1959"}, {"Ice-T", "1958"}, {"Kevin Bacon", "1958"}, {"Chris Columbus", "1958"},
        {"Cameron Crowe", "1957"}, {"Michael Clarke Duncan", "1957"}, {"Kelly McGillis", "1957"},
        {"Kevin Pollak", "1957"}, {"Tom Hanks", "1956"}, {"Rita Wilson", "1956"}, {"Geena Davis", "1956"},
        {"Carrie Fisher", "1956"}, {"Nathan Lane", "1956"}, {"Vincent Ward", "1956"}, {"Gary Sinise", "1955"},
        {"Bill Paxton", "1955"}, {"Madonna", "1954"}, {"Ron Howard", "1954"}, {"Zach Grenier", "1954"},
        {"Scott Hicks", "1953"}, {"David Morse", "1953"}, {"Robert Longo", "1953"}, {"Bill Pullman", "1953"},
        {"Joel Silver", "1952"}, {"Robert Zemeckis", "1951"}, {"Robin Williams", "1951"}, {"Howard Deutch", "1950"},
        {"Ed Harris", "1950"}, {"John Patrick Stanley", "1950"}, {"Bruno Kirby", "1949"}, {"Victor Garber", "1949"},
        {"Jim Broadbent", "1949"}, {"Nancy Meyers", "1949"}, {"Billy Crystal", "1948"}, {"Christopher Guest", "1948"},
        {"Rob Reiner", "1947"}, {"Takeshi Kitano", "1947"}, {"Stephen Rea", "1946"}, {"Susan Sarandon", "1946"},
        {"Diane Keaton", "1946"}, {"Taylor Hackford", "1944"}, {"Tony Scott", "1944"}, {"Danny DeVito", "1944"},
        {"Jan de Bont", "1943"}, {"J.T. Walsh", "1943"}, {"Penny Marshall", "1943"}, {"Werner Herzog", "1942"},
        {"Marshall Bell", "1942"}, {"Nora Ephron", "1941"}, {"Jim Cash", "1941"}, {"John Hurt", "1940"},
        {"Al Pacino", "1940"}, {"James L. Brooks", "1940"}, {"James Cromwell", "1940"}, {"Ian McKellen", "1939"},
        {"Frank Langella", "1938"}, {"Jack Nicholson", "1937"}, {"Tom Skerritt", "1933"}, {"Milos Forman", "1932"},
        {"Mike Nichols", "1931"}, {"Clint Eastwood", "1930"}, {"Gene Hackman", "1930"}, {"Richard Harris", "1930"},
        {"Max von Sydow", "1929"}, {" NULL TEST ", "NULL"}, {"Paul Blythe", "NULL"}, {"Naomie Harris", "NULL"},
        {"Angela Scope", "NULL"}, {"Jessica Thompson", "NULL"}, {"James Thompson", "NULL"}};

    std::vector<std::vector<std::string>> colValuesNameShuffle = {{"Regina King"}, {"David Mitchell"}, {"John Cusack"},
        {"Frank Langella"}, {"Takeshi Kitano"}, {"Rick Yune"}, {"Steve Zahn"}, {"Lilly Wachowski"}, {"Frank Darabont"},
        {"Robert Zemeckis"}, {"Brooke Langton"}, {"Jack Nicholson"}, {"Philip Seymour Hoffman"}, {"Nancy Meyers"},
        {"John Goodman"}, {"Julia Roberts"}, {"Joel Silver"}, {"Charlize Theron"}, {"Bill Paxton"}, {"Rob Reiner"},
        {"David Morse"}, {"Werner Herzog"}, {"Lana Wachowski"}, {"Kevin Bacon"}, {"Keanu Reeves"}, {"Dina Meyer"},
        {"Aaron Sorkin"}, {"Bruno Kirby"}, {"Oliver Platt"}, {"Richard Harris"}, {"Ed Harris"}, {"Howard Deutch"},
        {"Carrie-Anne Moss"}, {"Jerry O Connell"}, {"Greg Kinnear"}, {"Val Kilmer"}, {"Victor Garber"},
        {"Tom Skerritt"}, {"Geena Davis"}, {"NULL"}, {"Kelly Preston"}, {"Stephen Rea"}, {"Madonna"}, {"Jim Cash"},
        {"Tony Scott"}, {"Dave Chappelle"}, {"Marshall Bell"}, {"Ethan Hawke"}, {"Ben Miles"}, {"Christina Ricci"},
        {"Clint Eastwood"}, {"Billy Crystal"}, {"Halle Berry"}, {"Nathan Lane"}, {"Kevin Pollak"}, {"Gene Hackman"},
        {"Al Pacino"}, {"Jonathan Lipnicki"}, {"Max von Sydow"}, {"John C. Reilly"}, {"Diane Keaton"}, {"Emile Hirsch"},
        {"Michael Sheen"}, {"Scott Hicks"}, {"Helen Hunt"}, {"Jay Mohr"}, {"Demi Moore"}, {"Bonnie Hunt"},
        {"Milos Forman"}, {"Paul Blythe"}, {"James Marshall"}, {"Renee Zellweger"}, {"Michael Clarke Duncan"},
        {"Kelly McGillis"}, {"Matthew Fox"}, {"Taylor Hackford"}, {"Angela Scope"}, {"Ron Howard"}, {"Carrie Fisher"},
        {"Nora Ephron"}, {"Lori Petty"}, {"Tom Tykwer"}, {"Anthony Edwards"}, {"Ian McKellen"}, {"Susan Sarandon"},
        {"Patricia Clarkson"}, {"River Phoenix"}, {"Noah Wyle"}, {"Liv Tyler"}, {"Corey Feldman"}, {"James L. Brooks"},
        {"Natalie Portman"}, {"Bill Pullman"}, {"Robin Williams"}, {"Sam Rockwell"}, {"Rosie O Donnell"},
        {"Jim Broadbent"}, {"Annabella Sciorra"}, {"John Patrick Stanley"}, {"Cuba Gooding Jr."}, {"Jan de Bont"},
        {"Rita Wilson"}, {"Orlando Jones"}, {"Christian Bale"}, {"Robert Longo"}, {"Stefan Arndt"}, {"James Cromwell"},
        {" NULL TEST "}, {"Kiefer Sutherland"}, {"Tom Cruise"}, {"Meg Ryan"}, {"Christopher Guest"}, {"John Hurt"},
        {"Audrey Tautou"}, {"Wil Wheaton"}, {"Chris Columbus"}, {"Zach Grenier"}, {"Naomie Harris"}, {"James Thompson"},
        {"Penny Marshall"}, {"Ice-T"}, {"Mike Nichols"}, {"Emil Eifrem"}, {"J.T. Walsh"}, {"Danny DeVito"},
        {"Tom Hanks"}, {"Jessica Thompson"}, {"Vincent Ward"}, {"Hugo Weaving"}, {"Laurence Fishburne"}, {"Rain"},
        {"Cameron Crowe"}, {"Paul Bettany"}, {"Gary Sinise"}, {"Parker Posey"}};
};
INSTANTIATE_TEST_CASE_P(, StEmbGqlOrderBy, testing::Values(0));

HWTEST_P(StEmbGqlOrderBy, BaseCase, TestSize.Level0)
{
    std::vector<std::vector<std::string>> colValuesBornOrder;
    for (size_t i = 0; i < bornSetBornOrder.size(); ++i) {
        colValuesBornOrder.push_back({bornSetBornOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesIdOrder;
    for (size_t i = 0; i < personSetIdOrder.size(); ++i) {
        colValuesIdOrder.push_back({personSetIdOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesVarHopNameOrder = {{"Aaron Sorkin"}, {"Aaron Sorkin"}, {"Al Pacino"},
        {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"},
        {"Angela Scope"}, {"Annabella Sciorra"}, {"Anthony Edwards"}, {"Audrey Tautou"}, {"Ben Miles"}, {"Ben Miles"},
        {"Ben Miles"}, {"Bill Paxton"}, {"Bill Paxton"}, {"Bill Paxton"}, {"Bill Pullman"}, {"Billy Crystal"},
        {"Bonnie Hunt"}, {"Bonnie Hunt"}, {"Brooke Langton"}, {"Bruno Kirby"}, {"Cameron Crowe"}, {"Cameron Crowe"},
        {"Cameron Crowe"}, {"Carrie Fisher"}, {"Carrie-Anne Moss"}, {"Carrie-Anne Moss"}, {"Carrie-Anne Moss"},
        {"Charlize Theron"}, {"Charlize Theron"}, {"Chris Columbus"}, {"Christian Bale"}, {"Christina Ricci"},
        {"Christopher Guest"}, {"Clint Eastwood"}, {"Clint Eastwood"}, {"Corey Feldman"}, {"Cuba Gooding Jr."},
        {"Cuba Gooding Jr."}, {"Cuba Gooding Jr."}, {"Cuba Gooding Jr."}, {"Danny DeVito"}, {"Danny DeVito"},
        {"Danny DeVito"}, {"Dave Chappelle"}, {"David Mitchell"}, {"David Morse"}, {"Demi Moore"}, {"Diane Keaton"},
        {"Dina Meyer"}, {"Ed Harris"}, {"Emil Eifrem"}, {"Emile Hirsch"}, {"Ethan Hawke"}, {"Frank Darabont"},
        {"Frank Langella"}, {"Gary Sinise"}, {"Gary Sinise"}, {"Geena Davis"}, {"Gene Hackman"}, {"Gene Hackman"},
        {"Gene Hackman"}, {"Greg Kinnear"}, {"Greg Kinnear"}, {"Halle Berry"}, {"Helen Hunt"}, {"Helen Hunt"},
        {"Helen Hunt"}, {"Howard Deutch"}, {"Hugo Weaving"}, {"Hugo Weaving"}, {"Hugo Weaving"}, {"Hugo Weaving"},
        {"Hugo Weaving"}, {"Ian McKellen"}, {"Ice-T"}, {"J.T. Walsh"}, {"J.T. Walsh"}, {"Jack Nicholson"},
        {"Jack Nicholson"}, {"Jack Nicholson"}, {"Jack Nicholson"}, {"Jack Nicholson"}, {"James Cromwell"},
        {"James Cromwell"}, {"James L. Brooks"}, {"James Marshall"}, {"James Marshall"}, {"James Marshall"},
        {"James Thompson"}, {"James Thompson"}, {"James Thompson"}, {"James Thompson"}, {"James Thompson"},
        {"James Thompson"}, {"James Thompson"}, {"James Thompson"}, {"Jan de Bont"}, {"Jay Mohr"}, {"Jerry O Connell"},
        {"Jerry O Connell"}, {"Jessica Thompson"}, {"Jessica Thompson"}, {"Jessica Thompson"}, {"Jessica Thompson"},
        {"Jessica Thompson"}, {"Jessica Thompson"}, {"Jim Broadbent"}, {"Jim Cash"}, {"Joel Silver"}, {"Joel Silver"},
        {"Joel Silver"}, {"Joel Silver"}, {"Joel Silver"}, {"Joel Silver"}, {"John C. Reilly"}, {"John Cusack"},
        {"John Goodman"}, {"John Hurt"}, {"John Patrick Stanley"}, {"Jonathan Lipnicki"}, {"Julia Roberts"},
        {"Keanu Reeves"}, {"Keanu Reeves"}, {"Keanu Reeves"}, {"Keanu Reeves"}, {"Keanu Reeves"}, {"Keanu Reeves"},
        {"Keanu Reeves"}, {"Kelly McGillis"}, {"Kelly Preston"}, {"Kevin Bacon"}, {"Kevin Bacon"}, {"Kevin Bacon"},
        {"Kevin Pollak"}, {"Kiefer Sutherland"}, {"Kiefer Sutherland"}, {"Lana Wachowski"}, {"Lana Wachowski"},
        {"Lana Wachowski"}, {"Lana Wachowski"}, {"Lana Wachowski"}, {"Lana Wachowski"}, {"Lana Wachowski"},
        {"Lana Wachowski"}, {"Lana Wachowski"}, {"Laurence Fishburne"}, {"Laurence Fishburne"}, {"Laurence Fishburne"},
        {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Lilly Wachowski"},
        {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Lilly Wachowski"}, {"Liv Tyler"},
        {"Lori Petty"}, {"Madonna"}, {"Marshall Bell"}, {"Marshall Bell"}, {"Matthew Fox"}, {"Max von Sydow"},
        {"Max von Sydow"}, {"Meg Ryan"}, {"Meg Ryan"}, {"Meg Ryan"}, {"Meg Ryan"}, {"Meg Ryan"},
        {"Michael Clarke Duncan"}, {"Michael Sheen"}, {"Mike Nichols"}, {"Mike Nichols"}, {"Milos Forman"},
        {"Nancy Meyers"}, {"Nancy Meyers"}, {"Nancy Meyers"}, {"Naomie Harris"}, {"Natalie Portman"}, {"Nathan Lane"},
        {"Nathan Lane"}, {"Noah Wyle"}, {"Nora Ephron"}, {"Nora Ephron"}, {"Nora Ephron"}, {"Nora Ephron"},
        {"Oliver Platt"}, {"Oliver Platt"}, {"Orlando Jones"}, {"Parker Posey"}, {"Patricia Clarkson"},
        {"Paul Bettany"}, {"Paul Blythe"}, {"Penny Marshall"}, {"Philip Seymour Hoffman"}, {"Philip Seymour Hoffman"},
        {"Rain"}, {"Rain"}, {"Regina King"}, {"Renee Zellweger"}, {"Richard Harris"}, {"Rick Yune"}, {"Rick Yune"},
        {"Rita Wilson"}, {"River Phoenix"}, {"Rob Reiner"}, {"Rob Reiner"}, {"Rob Reiner"}, {"Rob Reiner"},
        {"Robert Longo"}, {"Robert Zemeckis"}, {"Robert Zemeckis"}, {"Robin Williams"}, {"Robin Williams"},
        {"Robin Williams"}, {"Ron Howard"}, {"Ron Howard"}, {"Ron Howard"}, {"Rosie O Donnell"}, {"Rosie O Donnell"},
        {"Sam Rockwell"}, {"Sam Rockwell"}, {"Scott Hicks"}, {"Stefan Arndt"}, {"Stephen Rea"}, {"Steve Zahn"},
        {"Steve Zahn"}, {"Susan Sarandon"}, {"Takeshi Kitano"}, {"Taylor Hackford"}, {"Tom Cruise"}, {"Tom Cruise"},
        {"Tom Cruise"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"},
        {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"}, {"Tom Hanks"},
        {"Tom Skerritt"}, {"Tom Tykwer"}, {"Tony Scott"}, {"Val Kilmer"}, {"Victor Garber"}, {"Vincent Ward"},
        {"Werner Herzog"}, {"Werner Herzog"}, {"Wil Wheaton"}, {"Zach Grenier"}, {"Zach Grenier"}};

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. MATCH查询点，orderby变量-点变量.属性
        {"MATCH (m:Person) RETURN m.born order by m.born;", GMERR_OK, {{"m.born"}, colValuesBornOrder}},
        // 2. MATCH查询边，定长1跳，orderby变量-边变量
        {"MATCH p=(m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born order by r.roles;", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        // 3. MATCH查询边，定长2跳，orderby表达式-属性值计算
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN r2.rating order by "
         "r2.rating + m.released",
            GMERR_OK,
            {{"r2.rating"}, {{"45"}, {"45"}, {"62"}, {"65"}, {"65"}, {"68"}, {"68"}, {"85"}, {"85"}, {"92"}, {"92"},
                                {"95"}, {"95"}}}},
        // 4. MATCH查询路径，变长1-2跳，orderby基础函数
        {"MATCH (p:Person)-[e]->{1,2}(m:Movie) RETURN p.name order by UPPER(p.name);", GMERR_OK,
            {{"p.name"}, colValuesVarHopNameOrder}},
        // 5. orderby不支持聚合函数
        {"MATCH (p:Person)-[e]->{1,2}(m:Movie) RETURN p.name, count(p.name) order by count(p.name);",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        // 6. MATCH查询点，orderby常量-字符串
        {"MATCH (m:Person) RETURN m order by '123';", GMERR_OK, {{"m"}, colValuesIdOrder}},
        // 7. ORDER BY 支持多个sort key
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN p.name ORDER BY r2.rating, "
         "p.name;",
            GMERR_OK,
            {{"p.name"}, {{"Angela Scope"}, {"James Thompson"}, {"Paul Blythe"}, {"Angela Scope"}, {"James Thompson"},
                             {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"}, {"James Thompson"},
                             {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"}, {"James Thompson"}}}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    };

    GmeConnT *conn = StEmbGqlGetConn();
    std::string gqlStr = "INSERT (Keanu:Person {born:9999, isVip: true});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    gqlStr = "INSERT (Keanu:Person {name:' NULL TEST ', isVip: false});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    std::vector<std::vector<std::string>> colValuesBornOrderAsc;
    for (size_t i = 0; i < bornSetBornOrderAsc.size(); ++i) {
        colValuesBornOrderAsc.push_back({bornSetBornOrderAsc[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornOrderDesc;
    for (size_t i = 0; i < bornSetBornOrderDesc.size(); ++i) {
        colValuesBornOrderDesc.push_back({bornSetBornOrderDesc[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornOrderNullsFirst;
    for (size_t i = 0; i < bornSetBornOrderNullsFirst.size(); ++i) {
        colValuesBornOrderNullsFirst.push_back({bornSetBornOrderNullsFirst[i]});
    }

    std::vector<std::vector<std::string>> colValuesNameOrder;
    for (size_t i = 0; i < nameSetNameOrder.size(); ++i) {
        colValuesNameOrder.push_back({nameSetNameOrder[i]});
    }
    colValuesNameOrder.push_back({"NULL"});
    colValuesNameOrder.insert(colValuesNameOrder.begin(), {" NULL TEST "});

    gqlQryCheckStmts = {
        // // 8. 查询排序升序
        {"MATCH (m:Person) RETURN m.born order by m.born ASC;", GMERR_OK, {{"m.born"}, colValuesBornOrderAsc}},
        // // 9. 查询排序降序
        {"MATCH (m:Person) RETURN m.born order by m.born DESC;", GMERR_OK, {{"m.born"}, colValuesBornOrderDesc}},
        // // 10. 查询排序包含null使用null first
        {"MATCH (m:Person) RETURN m.born order by m.born nulls first;", GMERR_OK,
            {{"m.born"}, colValuesBornOrderNullsFirst}},
        // // 11. 查询排序包含null使用null last
        {"MATCH (m:Person) RETURN m.born order by m.born nulls last;", GMERR_OK, {{"m.born"}, colValuesBornOrderAsc}},
        // // 12. ORDER BY 属性-group by
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.name;", GMERR_OK, {{"m.name"}, colValuesNameOrder}},
        // // 13. RETURN 投影列使用别名
        // {"MATCH p=(m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.name, m.born order by m.born desc, p asc;",
        //     GMERR_SEMANTIC_ERROR, {}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
};

HWTEST_P(StEmbGqlOrderBy, OrderByObject, TestSize.Level0)
{
    std::vector<std::vector<std::string>> colValuesIdOrder;
    for (size_t i = 0; i < personSetIdOrder.size(); ++i) {
        colValuesIdOrder.push_back({personSetIdOrder[i], bornSetIdOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesShuffle;
    for (size_t i = 0; i < bornSetShuffle.size(); ++i) {
        colValuesShuffle.push_back({bornSetShuffle[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornOrder;
    for (size_t i = 0; i < personSetBornOrder.size(); ++i) {
        colValuesBornOrder.push_back({personSetBornOrder[i], bornSetBornOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesNameOrder;
    for (size_t i = 0; i < nameSetNameOrder.size(); ++i) {
        colValuesNameOrder.push_back({nameSetNameOrder[i], bornSetNameOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesShuffle1;
    for (size_t i = 0; i < personSetShuffle.size(); ++i) {
        colValuesShuffle1.push_back({personSetShuffle[i]});
    }

    std::vector<std::vector<std::string>> colValuesRolesOrder;
    for (size_t i = 0; i < personSetRolesOrder.size(); ++i) {
        colValuesRolesOrder.push_back({personSetRolesOrder[i], bornSetRolesOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornOrder2;
    for (size_t i = 0; i < bornSetBornOrder2.size(); ++i) {
        colValuesBornOrder2.push_back({bornSetBornOrder2[i]});
    }

    std::vector<std::vector<std::string>> colValuesShuffle2;
    for (size_t i = 0; i < nameSetShuffle.size(); ++i) {
        colValuesShuffle2.push_back({nameSetShuffle[i]});
    }

    std::vector<std::vector<std::string>> colValuesShuffle3;
    for (size_t i = 0; i < personSetShuffle2.size(); ++i) {
        colValuesShuffle3.push_back({personSetShuffle2[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornOrder3;
    for (size_t i = 0; i < nameSetBornOrder.size(); ++i) {
        colValuesBornOrder3.push_back({nameSetBornOrder[i], bornSetBornOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesNameLenOrder;
    for (size_t i = 0; i < nameSetNameLenOrder.size(); ++i) {
        colValuesNameLenOrder.push_back({nameSetNameLenOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesIdOrder2;
    for (size_t i = 0; i < personSetIdOrder.size(); ++i) {
        colValuesIdOrder2.push_back({personSetIdOrder[i], nameSetIdOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesIdOrder3;
    for (size_t i = 0; i < nameSetIdOrder.size(); ++i) {
        colValuesIdOrder3.push_back({nameSetIdOrder[i], bornSetIdOrder[i]});
    }

    std::vector<std::vector<std::string>> colValuesBornNullAtTail;
    for (size_t i = 0; i < bornSetNullAtTail.size(); ++i) {
        colValuesBornNullAtTail.push_back({bornSetNullAtTail[i]});
    }

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. test expression
        {"MATCH (m:Person) RETURN m, m.born order by 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by -1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by +1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by true;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by false;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by '123';", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 1.245;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 + 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by NOT 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by NOT 1 = 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 LIKE 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 'aa' not like 'aa%';", GMERR_OK,
            {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 and 2;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 is null;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by true and false;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m.born order by m.born > 1 and m.born > 20;", GMERR_OK,
            {{"m.born"}, colValuesShuffle}},
        {"MATCH (m:Person) RETURN m.born order by m.born = 20;", GMERR_OK, {{"m.born"}, colValuesShuffle}},
        {"MATCH (m:Person) RETURN m, m.born order by m.born || '20';", GMERR_OK, {{"m", "m.born"}, colValuesBornOrder}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name || '20';", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m order by m.born not in (1924, 1944);", GMERR_OK, {{"m"}, colValuesShuffle1}},

        // 2. test attribute
        {"MATCH (m:Person) RETURN m, m.born order by m.born", GMERR_OK, {{"m", "m.born"}, colValuesBornOrder}},
        // attribute is not exist
        {"MATCH (m:Person) RETURN m, m.born order by m.notexist;", GMERR_UNDEFINE_COLUMN, {}},
        // If m.id is null, no verification will occur when m lacks a label.
        {"MATCH (m) RETURN m.name order by m.id", GMERR_OK, {{"m.name"}, colValuesShuffle2}},
        {"MATCH (m) RETURN m.name order by m.identity", GMERR_OK, {{"m.name"}, colValuesShuffle2}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m, m.born order by r.roles;", GMERR_OK,
            {{"m", "m.born"}, colValuesRolesOrder}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m, m.born order by r.notexist;", GMERR_UNDEFINE_COLUMN, {}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m, m.born order by r.roles || '123';", GMERR_OK,
            {{"m", "m.born"}, colValuesRolesOrder}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m order by r.roles not like 'ame%';", GMERR_OK,
            {{"m"}, colValuesShuffle3}},
        // expected output: odd nums < 1962, even nums, odd nums > 1962, nulls
        {"MATCH (m:Person) RETURN m.born order by (m.born - 1962) % 2;", GMERR_OK, {{"m.born"}, colValuesBornOrder2}},
        {"MATCH (m:Person) RETURN m, m.born order by (m.born - 1962.5);", GMERR_OK,
            {{"m", "m.born"}, colValuesBornOrder}},

        // 3. test functions
        {"MATCH (m:Person) RETURN m, m.born order by date('2024-01-01 10:22');", GMERR_OK,
            {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m, m.born order by ceil(m.born);", GMERR_OK, {{"m", "m.born"}, colValuesBornOrder}},
        {"MATCH (m:Person) RETURN m.name, m.born order by ceil(m.born - 1962.5);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornOrder3}},
        {"MATCH (m:Person) RETURN m, m.born order by floor(m.born);", GMERR_OK, {{"m", "m.born"}, colValuesBornOrder}},
        {"MATCH (m:Person) RETURN m.name, m.born order by floor(m.born - 1962.5);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornOrder3}},
        {"MATCH (m:Person) RETURN m.name, m.born order by floor(m.born - ((m.born) / 100.00));", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornOrder3}},
        {"MATCH (m:Person) RETURN m.born order by floor(m.born) < ceil(m.born);", GMERR_OK,
            {{"m.born"}, colValuesBornNullAtTail}},  // m.born null at the tail
        {"MATCH (m:Person) RETURN m.name order by char_length(m.name);", GMERR_OK, {{"m.name"}, colValuesNameLenOrder}},
        {"MATCH (m:Person) RETURN m.name order by character_length(m.name);", GMERR_OK,
            {{"m.name"}, colValuesNameLenOrder}},
        {"MATCH (m:Person) RETURN m, m.name order by upper('Test');", GMERR_OK, {{"m", "m.name"}, colValuesIdOrder2}},
        {"MATCH (m:Person) RETURN m.name, m.born order by upper(m.name);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name, m.born order by lower(m.name);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name, m.born order by cast(m.born as double);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornOrder3}},
        {"MATCH (m:Person) RETURN m.name, m.born order by cast(m.born as bool);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornOrder3}},
        {"MATCH (m:Person) RETURN m.name, m.born order by cast(1 - 1 as bool);", GMERR_OK,
            {{"m.name", "m.born"}, colValuesIdOrder3}},
        {"MATCH (m:Person) RETURN m.name, m.born order by random();", GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by abs((m.born - 1962) % 2);", GMERR_SYNTAX_ERROR, {}},
        // nested functions
        {"MATCH (m:Person) RETURN m.name, m.born order by abs(ceil(m.born - 1962.5));", GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by abs(char_length(m.name) - 3);", GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by abs(char_length(m.name) - 3) % 2;", GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by abs(char_length(m.name) - 3) % 2 > 1;", GMERR_SYNTAX_ERROR,
            {}},
        // aggregation functions
        {"MATCH (m:Person) RETURN m.name, m.born order by max(m.born);", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by min(m.born);", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by count(m.born);", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by sum(m.born);", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name, m.born order by avg(m.born);", GMERR_FEATURE_NOT_SUPPORTED, {}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }

    GmeConnT *conn = StEmbGqlGetConn();

    std::string gqlStr = "INSERT (Keanu:Person {born:9999});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    gqlStr = "INSERT (Keanu:Person {name:' NULL TEST '});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    colValuesIdOrder.push_back({"{\"label\":\"PERSON\",\"identity\":\"425\",\"properties\":{\"BORN\":9999}}", "9999"});
    colValuesIdOrder.push_back(
        {"{\"label\":\"PERSON\",\"identity\":\"426\",\"properties\":{\"NAME\":\" NULL TEST \"}}", "NULL"});

    int nullNamePos = 128;
    int nullBornPos = 129;
    std::vector<std::vector<std::string>> colValuesBornOrder4;
    for (size_t i = 0; i < bornSetBornOrder.size(); ++i) {
        colValuesBornOrder4.push_back({bornSetBornOrder[i]});
    }
    colValuesBornOrder4.insert(colValuesBornOrder4.begin() + nullNamePos, {"9999"});
    colValuesBornOrder4.insert(colValuesBornOrder4.begin() + nullBornPos, {"NULL"});

    colValuesNameOrder.push_back({"NULL", "9999"});
    colValuesNameOrder.insert(colValuesNameOrder.begin(), {" NULL TEST ", "NULL"});

    gqlQryCheckStmts = {
        // 4. test null value
        {"MATCH (m:Person) RETURN m, m.born order by 1;", GMERR_OK, {{"m", "m.born"}, colValuesIdOrder}},
        {"MATCH (m:Person) RETURN m.born order by m.born;", GMERR_OK, {{"m.born"}, colValuesBornOrder4}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrder}},
        // 5. test order by variable
        {"MATCH (m:Person) RETURN m order by m;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN r order by r;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m order by r;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH p=(m:Person)-[r:ACTED_IN]->(n:Movie) RETURN p order by p;", GMERR_SEMANTIC_ERROR, {}},
        {"MATCH p=(m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m order by p;", GMERR_SEMANTIC_ERROR, {}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}

HWTEST_P(StEmbGqlOrderBy, OrderByWay, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    std::string gqlStr = "INSERT (Keanu:Person {born:9999, isVip: true});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    gqlStr = "INSERT (Keanu:Person {name:' NULL TEST ', isVip: false});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    std::vector<std::vector<std::string>> colValuesBornOrderAsc;
    for (size_t i = 0; i < bornSetBornOrderAsc.size(); ++i) {
        colValuesBornOrderAsc.push_back({bornSetBornOrderAsc[i]});
    }

    std::vector<std::vector<std::string>> colValuesNameOrderNullsFirst;
    for (size_t i = 0; i < nameSetNameOrderNullsFirst.size(); ++i) {
        colValuesNameOrderNullsFirst.push_back({nameSetNameOrderNullsFirst[i], bornSetNameOrderNullsFirst[i]});
    }

    std::vector<std::vector<std::string>> colValuesNameOrderNullsLast = colValuesNameOrderNullsFirst;
    std::vector<std::string> firstElement = colValuesNameOrderNullsLast.front();
    colValuesNameOrderNullsLast.erase(colValuesNameOrderNullsLast.begin());
    colValuesNameOrderNullsLast.push_back(firstElement);

    std::vector<std::vector<std::string>> colValuesNameOrder;
    for (size_t i = 0; i < nameSetNameOrder.size(); ++i) {
        colValuesNameOrder.push_back({nameSetNameOrder[i], bornSetNameOrder[i]});
    }
    colValuesNameOrder.push_back({"NULL", "9999"});
    colValuesNameOrder.insert(colValuesNameOrder.begin(), {" NULL TEST ", "NULL"});

    std::vector<std::vector<std::string>> colValuesNameOrderDesc;
    for (size_t i = 0; i < nameSetNameOrderDesc.size(); ++i) {
        colValuesNameOrderDesc.push_back({nameSetNameOrderDesc[i]});
    }

    std::vector<std::vector<std::string>> colValueIsVipOrder;
    for (size_t i = 0; i < nameSetIsVipOrder.size(); ++i) {
        colValueIsVipOrder.push_back({nameSetIsVipOrder[i], isVipSetIsVipOrder[i]});
    }

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. test nulls first or nulls last
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name nulls first;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrderNullsFirst}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name nulls last;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameOrderNullsLast}},
        // 2. test sort algthrihm
        {"MATCH (m:Person) RETURN m, m.born order by m.born offset 0 limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesBornOrderLimit10}},
        {"MATCH (m:Person) RETURN m, m.born order by m.born desc offset 1 limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesBornOrderDescOffset1Limit10}},
        {"MATCH (m:Person) RETURN m, m.born order by m.born asc limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesBornOrderLimit10}},
        {"MATCH (m:Person) RETURN m, m.born order by m.born asc offset 3 limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesBornOrderOffset3Limit10}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 asc offset 3 limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesShuffleLimit10}},
        {"MATCH (m:Person) RETURN m, m.born order by 1 desc offset 3 limit 10;", GMERR_OK,
            {{"m", "m.born"}, colValuesShuffleLimit10}},
        // todo: external order
        // 3. test sort data type
        {"MATCH (m:Person) RETURN m.name order by m.name asc;", GMERR_OK, {{"m.name"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name order by m.name desc;", GMERR_OK, {{"m.name"}, colValuesNameOrderDesc}},
        {"MATCH (m:Person) RETURN m.born order by m.born / 100.00 asc;", GMERR_OK, {{"m.born"}, colValuesBornOrderAsc}},
        {"MATCH (m:Person) RETURN m.name, m.isVip order by m.isVip asc;", GMERR_OK,
            {{"m.name", "m.isVip"}, colValueIsVipOrder}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}

HWTEST_P(StEmbGqlOrderBy, OrderKeyNum, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    std::string gqlStr = "INSERT (Keanu:Person {born:9999, isVip: true});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    gqlStr = "INSERT (Keanu:Person {name:' NULL TEST ', isVip: false});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. test multiple key
        // all vertex attributes key
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name asc, m.born asc;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameAscBornAcs}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name desc, m.born asc;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameDescBornAcs}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.born desc, m.born asc;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornDecs}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name asc, m.name desc;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameAcs}},
        {"MATCH (m:Person) RETURN m.name, m.born order by m.name asc nulls last, m.name desc nulls first;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesNameAscBornAcs}},
        // vertex key with edge key
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born, r.roles order by m.born desc, r.roles asc;", GMERR_OK,
            {{"m.born", "r.roles"}, colValuesBornDecsRolesAsc}},
        // vertex key with expression key
        {"MATCH (m:Person) RETURN m.name, m.born order by (m.born + 100) desc, m.name || '2134' asc nulls last;",
            GMERR_OK, {{"m.name", "m.born"}, colValuesBornDecsNameAsc}},
        {"MATCH (m:Person) RETURN m.name, m.born order by (m.born + 100) desc, 1 + 1 asc nulls last;", GMERR_OK,
            {{"m.name", "m.born"}, colValuesBornDecs2}},
        // vertex key with functions key
        {"MATCH (m:Person) RETURN m.name, m.born order by (m.born + 100) desc, (char_length(m.name) - 3) % 2;",
            GMERR_OK, {{"m.name", "m.born"}, colValuesBornDecs3}},
        // partial key does not exist
        {"MATCH (m:Person) RETURN m.name, m.born order by m.born desc, m.kkone asc;", GMERR_UNDEFINE_COLUMN, {}},
        // partial key is not supported
        {"MATCH (m:Person) RETURN m.name, m.born order by m.born desc, m asc;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.name, m.born order by m.born desc, r asc;",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH p=(m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.name, m.born order by m.born desc, p asc;",
            GMERR_SEMANTIC_ERROR, {}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    };
}

HWTEST_P(StEmbGqlOrderBy, OrderByWithClause, TestSize.Level3)
{
    GmeConnT *conn = StEmbGqlGetConn();
    std::string gqlStr = "INSERT (Keanu:Person {born:9999, isVip: true});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    gqlStr = "INSERT (Keanu:Person {name:' NULL TEST ', isVip: false});";
    StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);

    std::vector<std::vector<std::string>> colValuesNameOrder;
    for (size_t i = 0; i < nameSetNameOrder.size(); ++i) {
        colValuesNameOrder.push_back({nameSetNameOrder[i]});
    }
    colValuesNameOrder.push_back({"NULL"});
    colValuesNameOrder.insert(colValuesNameOrder.begin(), {" NULL TEST "});

    std::vector<std::vector<std::string>> colValuesBornOrderAsc;
    for (size_t i = 0; i < bornSetBornOrderAsc.size(); ++i) {
        colValuesBornOrderAsc.push_back({bornSetBornOrderAsc[i]});
    }

    std::vector<std::vector<std::string>> colValuesOrderBy1 = {{"1964"}, {"1964"}, {"1964"}, {"1964"}, {"1964"},
        {"1964"}, {"1964"}, {"1967"}, {"1967"}, {"1967"}, {"1961"}, {"1961"}, {"1961"}, {"1960"}, {"1960"}, {"1960"},
        {"1960"}, {"1960"}, {"1978"}, {"1975"}, {"1975"}, {"1940"}, {"1962"}, {"1962"}, {"1962"}, {"1937"}, {"1937"},
        {"1937"}, {"1937"}, {"1937"}, {"1962"}, {"1958"}, {"1958"}, {"1958"}, {"1966"}, {"1966"}, {"1971"}, {"1968"},
        {"1968"}, {"1968"}, {"1968"}, {"1957"}, {"1943"}, {"1943"}, {"1967"}, {"1948"}, {"1961"}, {"1957"}, {"1959"},
        {"1962"}, {"1933"}, {"1961"}, {"1961"}, {"1961"}, {"1961"}, {"1961"}, {"1969"}, {"1962"}, {"1974"}, {"1974"},
        {"1970"}, {"1961"}, {"1961"}, {"1971"}, {"1996"}, {"1970"}, {"1971"}, {"1972"}, {"1966"}, {"1942"}, {"1942"},
        {"1963"}, {"1963"}, {"1963"}, {"1963"}, {"1963"}, {"1960"}, {"1929"}, {"1929"}, {"1942"}, {"1951"}, {"1951"},
        {"1951"}, {"1970"}, {"1971"}, {"1971"}, {"1940"}, {"1940"}, {"1968"}, {"1973"}, {"1967"}, {"1967"}, {"1956"},
        {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"},
        {"1956"}, {"1953"}, {"1949"}, {"1962"}, {"1962"}, {"1956"}, {"1956"}, {"1948"}, {"1956"}, {"1949"}, {"1977"},
        {"1970"}, {"1930"}, {"1930"}, {"1930"}, {"1968"}, {"1974"}, {"1954"}, {"1954"}, {"1930"}, {"1930"}, {"1947"},
        {"1968"}, {"1958"}, {"1966"}, {"1949"}, {"1939"}, {"1976"}, {"1971"}, {"1981"}, {"1946"}, {"1940"}, {"1967"},
        {"1967"}, {"1967"}, {"1985"}, {"1960"}, {"1946"}, {"1966"}, {"1980"}, {"1982"}, {"1982"}, {"NULL"}, {"1957"},
        {"1953"}, {"1968"}, {"1968"}, {"1955"}, {"1955"}, {"1959"}, {"1938"}, {"1969"}, {"1960"}, {"1960"}, {"1944"},
        {"1944"}, {"1965"}, {"1950"}, {"1955"}, {"1955"}, {"1955"}, {"1967"}, {"1967"}, {"1946"}, {"1967"}, {"1954"},
        {"1956"}, {"1963"}};

    std::vector<std::vector<std::string>> colValuesOrderByRolesDesc = {{"1956"}, {"1968"}, {"1940"}, {"1953"}, {"1949"},
        {"1933"}, {"1974"}, {"1960"}, {"1980"}, {"1967"}, {"1967"}, {"1967"}, {"1975"}, {"1929"}, {"1942"}, {"1971"},
        {"1971"}, {"1947"}, {"1982"}, {"1956"}, {"1954"}, {"1985"}, {"1976"}, {"1939"}, {"1963"}, {"1971"}, {"1964"},
        {"1930"}, {"1956"}, {"1961"}, {"1967"}, {"1960"}, {"1968"}, {"1944"}, {"1938"}, {"1956"}, {"1996"}, {"1937"},
        {"1982"}, {"1966"}, {"1960"}, {"1967"}, {"1965"}, {"1956"}, {"1968"}, {"1964"}, {"1964"}, {"1964"}, {"1929"},
        {"1956"}, {"1942"}, {"1961"}, {"1961"}, {"1961"}, {"1946"}, {"NULL"}, {"1937"}, {"1959"}, {"1962"}, {"1975"},
        {"1944"}, {"1956"}, {"1971"}, {"1961"}, {"1966"}, {"1957"}, {"1966"}, {"1962"}, {"1943"}, {"1962"}, {"1930"},
        {"1961"}, {"1963"}, {"1964"}, {"1973"}, {"1955"}, {"1963"}, {"1971"}, {"1961"}, {"1964"}, {"1940"}, {"1964"},
        {"1940"}, {"1957"}, {"1956"}, {"1956"}, {"1967"}, {"1930"}, {"1956"}, {"1956"}, {"1949"}, {"1962"}, {"1968"},
        {"1961"}, {"1968"}, {"1958"}, {"1958"}, {"1958"}, {"1970"}, {"1959"}, {"1937"}, {"1940"}, {"1956"}, {"1937"},
        {"1948"}, {"1967"}, {"1949"}, {"1972"}, {"1962"}, {"1967"}, {"1950"}, {"1955"}, {"1968"}, {"1963"}, {"1943"},
        {"1974"}, {"1977"}, {"1981"}, {"1946"}, {"1946"}, {"1930"}, {"1978"}, {"1954"}, {"1967"}, {"1967"}, {"1948"},
        {"1956"}, {"1963"}, {"1956"}, {"1969"}, {"1962"}, {"1974"}, {"1966"}, {"1961"}, {"1969"}, {"1967"}, {"1971"},
        {"1968"}, {"1937"}, {"1968"}, {"1956"}, {"1951"}, {"1970"}, {"1957"}, {"1967"}, {"1961"}, {"1963"}, {"1958"},
        {"1955"}, {"1953"}, {"1960"}, {"1970"}, {"1955"}, {"1960"}, {"1930"}, {"1955"}, {"1962"}, {"1956"}, {"1962"},
        {"1951"}, {"1961"}, {"1960"}, {"1970"}, {"1951"}, {"1954"}, {"1968"}, {"1956"}, {"1960"}, {"1960"}, {"1960"},
        {"1942"}, {"1966"}};

    std::vector<std::vector<std::string>> colValuesOrderByNull = {{"1971"}, {"1940"}, {"1964"}, {"1940"}, {"1964"},
        {"1968"}, {"1964"}, {"1973"}, {"1967"}, {"1967"}, {"1967"}, {"1967"}, {"1961"}, {"1956"}, {"1960"}, {"1956"},
        {"1960"}, {"1956"}, {"1960"}, {"1956"}, {"1975"}, {"1956"}, {"1940"}, {"1956"}, {"1962"}, {"1956"}, {"1937"},
        {"1956"}, {"1937"}, {"1956"}, {"1937"}, {"1956"}, {"1958"}, {"1956"}, {"1958"}, {"1956"}, {"1966"}, {"1956"},
        {"1968"}, {"1953"}, {"1968"}, {"1949"}, {"1957"}, {"1962"}, {"1943"}, {"1962"}, {"1948"}, {"1956"}, {"1957"},
        {"1956"}, {"1962"}, {"1948"}, {"1961"}, {"1956"}, {"1961"}, {"1949"}, {"1961"}, {"1977"}, {"1962"}, {"1970"},
        {"1974"}, {"1930"}, {"1961"}, {"1930"}, {"1971"}, {"1930"}, {"1970"}, {"1968"}, {"1972"}, {"1974"}, {"1942"},
        {"1954"}, {"1963"}, {"1954"}, {"1963"}, {"1930"}, {"1963"}, {"1930"}, {"1929"}, {"1947"}, {"1942"}, {"1968"},
        {"1951"}, {"1958"}, {"1970"}, {"1966"}, {"1964"}, {"1949"}, {"1964"}, {"1939"}, {"1967"}, {"1976"}, {"1961"},
        {"1971"}, {"1960"}, {"1981"}, {"1975"}, {"1946"}, {"1962"}, {"1940"}, {"1937"}, {"1967"}, {"1958"}, {"1967"},
        {"1971"}, {"1967"}, {"1968"}, {"1985"}, {"1967"}, {"1960"}, {"1959"}, {"1946"}, {"1961"}, {"1966"}, {"1969"},
        {"1980"}, {"1970"}, {"1982"}, {"1996"}, {"1982"}, {"1966"}, {"NULL"}, {"1963"}, {"1957"}, {"1960"}, {"1953"},
        {"1951"}, {"1968"}, {"1971"}, {"1968"}, {"1964"}, {"1955"}, {"1960"}, {"1955"}, {"1962"}, {"1959"}, {"1962"},
        {"1938"}, {"1968"}, {"1969"}, {"1961"}, {"1960"}, {"1961"}, {"1960"}, {"1961"}, {"1944"}, {"1942"}, {"1944"},
        {"1929"}, {"1965"}, {"1964"}, {"1950"}, {"1978"}, {"1955"}, {"1966"}, {"1955"}, {"1933"}, {"1955"}, {"1971"},
        {"1967"}, {"1951"}, {"1967"}, {"1937"}, {"1946"}, {"1974"}, {"1961"}, {"1963"}, {"1956"}, {"1954"}, {"1967"},
        {"1963"}, {"1943"}};

    std::vector<std::vector<std::string>> colValuesBornNameOrder = {{"Max von Sydow"}, {"Clint Eastwood"},
        {"Gene Hackman"}, {"Richard Harris"}, {"Mike Nichols"}, {"Milos Forman"}, {"Tom Skerritt"}, {"Jack Nicholson"},
        {"Frank Langella"}, {"Ian McKellen"}, {"Al Pacino"}, {"James Cromwell"}, {"James L. Brooks"}, {"John Hurt"},
        {"Jim Cash"}, {"Nora Ephron"}, {"Marshall Bell"}, {"Werner Herzog"}, {"J.T. Walsh"}, {"Jan de Bont"},
        {"Penny Marshall"}, {"Danny DeVito"}, {"Taylor Hackford"}, {"Tony Scott"}, {"Diane Keaton"}, {"Stephen Rea"},
        {"Susan Sarandon"}, {"Rob Reiner"}, {"Takeshi Kitano"}, {"Billy Crystal"}, {"Christopher Guest"},
        {"Bruno Kirby"}, {"Jim Broadbent"}, {"Nancy Meyers"}, {"Victor Garber"}, {"Ed Harris"}, {"Howard Deutch"},
        {"John Patrick Stanley"}, {"Robert Zemeckis"}, {"Robin Williams"}, {"Joel Silver"}, {"Bill Pullman"},
        {"David Morse"}, {"Robert Longo"}, {"Scott Hicks"}, {"Madonna"}, {"Ron Howard"}, {"Zach Grenier"},
        {"Bill Paxton"}, {"Gary Sinise"}, {"Carrie Fisher"}, {"Geena Davis"}, {"Nathan Lane"}, {"Rita Wilson"},
        {"Tom Hanks"}, {"Vincent Ward"}, {"Cameron Crowe"}, {"Kelly McGillis"}, {"Kevin Pollak"},
        {"Michael Clarke Duncan"}, {"Chris Columbus"}, {"Ice-T"}, {"Kevin Bacon"}, {"Frank Darabont"},
        {"Patricia Clarkson"}, {"Val Kilmer"}, {"Annabella Sciorra"}, {"Hugo Weaving"}, {"John Goodman"},
        {"Oliver Platt"}, {"Aaron Sorkin"}, {"Bonnie Hunt"}, {"Laurence Fishburne"}, {"Meg Ryan"}, {"Stefan Arndt"},
        {"Anthony Edwards"}, {"Demi Moore"}, {"Kelly Preston"}, {"Rosie O Donnell"}, {"Tom Cruise"}, {"Greg Kinnear"},
        {"Helen Hunt"}, {"Lori Petty"}, {"Keanu Reeves"}, {"John C. Reilly"}, {"Lana Wachowski"}, {"Tom Tykwer"},
        {"Halle Berry"}, {"John Cusack"}, {"Kiefer Sutherland"}, {"Matthew Fox"}, {"Ben Miles"}, {"Carrie-Anne Moss"},
        {"James Marshall"}, {"Julia Roberts"}, {"Lilly Wachowski"}, {"Philip Seymour Hoffman"}, {"Steve Zahn"},
        {"Cuba Gooding Jr."}, {"Dina Meyer"}, {"Orlando Jones"}, {"Parker Posey"}, {"Sam Rockwell"}, {"David Mitchell"},
        {"Michael Sheen"}, {"Renee Zellweger"}, {"Brooke Langton"}, {"Ethan Hawke"}, {"Jay Mohr"}, {"River Phoenix"},
        {"Corey Feldman"}, {"Noah Wyle"}, {"Paul Bettany"}, {"Regina King"}, {"Rick Yune"}, {"Wil Wheaton"},
        {"Dave Chappelle"}, {"Christian Bale"}, {"Jerry O Connell"}, {"Charlize Theron"}, {"Audrey Tautou"},
        {"Liv Tyler"}, {"Emil Eifrem"}, {"Christina Ricci"}, {"Natalie Portman"}, {"Rain"}, {"Emile Hirsch"},
        {"Jonathan Lipnicki"}, {"NULL"}, {" NULL TEST "}, {"Angela Scope"}, {"James Thompson"}, {"Jessica Thompson"},
        {"Naomie Harris"}, {"Paul Blythe"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}};

    std::vector<std::vector<std::string>> colValuesBornDescNullsFirstNameOrder = {{" NULL TEST "}, {"Angela Scope"},
        {"James Thompson"}, {"Jessica Thompson"}, {"Naomie Harris"}, {"Paul Blythe"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"}, {"NULL"},
        {"NULL"}, {"NULL"}, {"NULL"}, {"Jonathan Lipnicki"}, {"Emile Hirsch"}, {"Rain"}, {"Natalie Portman"},
        {"Christina Ricci"}, {"Emil Eifrem"}, {"Liv Tyler"}, {"Audrey Tautou"}, {"Charlize Theron"}, {"Christian Bale"},
        {"Jerry O Connell"}, {"Dave Chappelle"}, {"Wil Wheaton"}, {"Corey Feldman"}, {"Noah Wyle"}, {"Paul Bettany"},
        {"Regina King"}, {"Rick Yune"}, {"Brooke Langton"}, {"Ethan Hawke"}, {"Jay Mohr"}, {"River Phoenix"},
        {"David Mitchell"}, {"Michael Sheen"}, {"Renee Zellweger"}, {"Cuba Gooding Jr."}, {"Dina Meyer"},
        {"Orlando Jones"}, {"Parker Posey"}, {"Sam Rockwell"}, {"Ben Miles"}, {"Carrie-Anne Moss"}, {"James Marshall"},
        {"Julia Roberts"}, {"Lilly Wachowski"}, {"Philip Seymour Hoffman"}, {"Steve Zahn"}, {"Halle Berry"},
        {"John Cusack"}, {"Kiefer Sutherland"}, {"Matthew Fox"}, {"John C. Reilly"}, {"Lana Wachowski"}, {"Tom Tykwer"},
        {"Keanu Reeves"}, {"Greg Kinnear"}, {"Helen Hunt"}, {"Lori Petty"}, {"Anthony Edwards"}, {"Demi Moore"},
        {"Kelly Preston"}, {"Rosie O Donnell"}, {"Tom Cruise"}, {"Aaron Sorkin"}, {"Bonnie Hunt"},
        {"Laurence Fishburne"}, {"Meg Ryan"}, {"Stefan Arndt"}, {"Annabella Sciorra"}, {"Hugo Weaving"},
        {"John Goodman"}, {"Oliver Platt"}, {"Frank Darabont"}, {"Patricia Clarkson"}, {"Val Kilmer"},
        {"Chris Columbus"}, {"Ice-T"}, {"Kevin Bacon"}, {"Cameron Crowe"}, {"Kelly McGillis"}, {"Kevin Pollak"},
        {"Michael Clarke Duncan"}, {"Carrie Fisher"}, {"Geena Davis"}, {"Nathan Lane"}, {"Rita Wilson"}, {"Tom Hanks"},
        {"Vincent Ward"}, {"Bill Paxton"}, {"Gary Sinise"}, {"Madonna"}, {"Ron Howard"}, {"Zach Grenier"},
        {"Bill Pullman"}, {"David Morse"}, {"Robert Longo"}, {"Scott Hicks"}, {"Joel Silver"}, {"Robert Zemeckis"},
        {"Robin Williams"}, {"Ed Harris"}, {"Howard Deutch"}, {"John Patrick Stanley"}, {"Bruno Kirby"},
        {"Jim Broadbent"}, {"Nancy Meyers"}, {"Victor Garber"}, {"Billy Crystal"}, {"Christopher Guest"},
        {"Rob Reiner"}, {"Takeshi Kitano"}, {"Diane Keaton"}, {"Stephen Rea"}, {"Susan Sarandon"}, {"Danny DeVito"},
        {"Taylor Hackford"}, {"Tony Scott"}, {"J.T. Walsh"}, {"Jan de Bont"}, {"Penny Marshall"}, {"Marshall Bell"},
        {"Werner Herzog"}, {"Jim Cash"}, {"Nora Ephron"}, {"Al Pacino"}, {"James Cromwell"}, {"James L. Brooks"},
        {"John Hurt"}, {"Ian McKellen"}, {"Frank Langella"}, {"Jack Nicholson"}, {"Tom Skerritt"}, {"Milos Forman"},
        {"Mike Nichols"}, {"Clint Eastwood"}, {"Gene Hackman"}, {"Richard Harris"}, {"Max von Sydow"}};

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. where clause
        {"MATCH (m:Person) where m.born > 1970 RETURN m.name order by m.name asc, m.born asc", GMERR_OK,
            {{"m.name"},
                {{"Audrey Tautou"}, {"Charlize Theron"}, {"Christian Bale"}, {"Christina Ricci"}, {"Corey Feldman"},
                    {"Dave Chappelle"}, {"Emil Eifrem"}, {"Emile Hirsch"}, {"Jerry O Connell"}, {"Jonathan Lipnicki"},
                    {"Liv Tyler"}, {"Natalie Portman"}, {"Noah Wyle"}, {"Paul Bettany"}, {"Rain"}, {"Regina King"},
                    {"Rick Yune"}, {"Wil Wheaton"}, {"NULL"}}}},

        // 2. limit clause
        {"MATCH (m:Person) where m.born > 1970 RETURN m.name order by m.name asc, m.born asc limit 5;", GMERR_OK,
            {{"m.name"},
                {{"Audrey Tautou"}, {"Charlize Theron"}, {"Christian Bale"}, {"Christina Ricci"}, {"Corey Feldman"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.name order by m.name desc, m.born asc limit 5;", GMERR_OK,
            {{"m.name"}, {{"Wil Wheaton"}, {"Rick Yune"}, {"Regina King"}, {"Rain"}, {"Paul Bettany"}}}},

        // 3. return clause
        {"MATCH (m:Person) where m.born > 1970 RETURN m.born order by m.born asc;", GMERR_OK,
            {{"m.born"},
                {{"1971"}, {"1971"}, {"1971"}, {"1971"}, {"1971"}, {"1972"}, {"1973"}, {"1974"}, {"1974"}, {"1975"},
                    {"1976"}, {"1977"}, {"1978"}, {"1980"}, {"1981"}, {"1982"}, {"1985"}, {"1996"}, {"9999"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.born, m.born order by m.born + 1 asc;", GMERR_OK,
            {{"m.born", "m.born"},
                {{"1971", "1971"}, {"1971", "1971"}, {"1971", "1971"}, {"1971", "1971"}, {"1971", "1971"},
                    {"1972", "1972"}, {"1973", "1973"}, {"1974", "1974"}, {"1974", "1974"}, {"1975", "1975"},
                    {"1976", "1976"}, {"1977", "1977"}, {"1978", "1978"}, {"1980", "1980"}, {"1981", "1981"},
                    {"1982", "1982"}, {"1985", "1985"}, {"1996", "1996"}, {"9999", "9999"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN char_length(m.name) order by m.name asc;", GMERR_OK,
            {{"?column?"}, {{"13"}, {"15"}, {"14"}, {"15"}, {"13"}, {"14"}, {"11"}, {"12"}, {"15"}, {"17"}, {"9"},
                               {"15"}, {"9"}, {"12"}, {"4"}, {"11"}, {"9"}, {"11"}, {"NULL"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN abs(char_length(m.name)) order by m.name asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.born, (m.born + 1) order by m.born asc;", GMERR_OK,
            {{"m.born", "?column?"},
                {{"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"},
                    {"1972", "1973"}, {"1973", "1974"}, {"1974", "1975"}, {"1974", "1975"}, {"1975", "1976"},
                    {"1976", "1977"}, {"1977", "1978"}, {"1978", "1979"}, {"1980", "1981"}, {"1981", "1982"},
                    {"1982", "1983"}, {"1985", "1986"}, {"1996", "1997"}, {"9999", "10000"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.born, (m.born + 1) order by (m.born + 2) asc;", GMERR_OK,
            {{"m.born", "?column?"},
                {{"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"}, {"1971", "1972"},
                    {"1972", "1973"}, {"1973", "1974"}, {"1974", "1975"}, {"1974", "1975"}, {"1975", "1976"},
                    {"1976", "1977"}, {"1977", "1978"}, {"1978", "1979"}, {"1980", "1981"}, {"1981", "1982"},
                    {"1982", "1983"}, {"1985", "1986"}, {"1996", "1997"}, {"9999", "10000"}}}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) where m.born > 1970 RETURN m.name, (m.born + 1) order by m.born + "
         "n.released asc;",
            GMERR_OK,
            {{"m.name", "?column?"},
                {{"Corey Feldman", "1972"}, {"Wil Wheaton", "1973"}, {"Jerry O Connell", "1975"}, {"Noah Wyle", "1972"},
                    {"Rick Yune", "1972"}, {"Dave Chappelle", "1974"}, {"Regina King", "1972"},
                    {"Charlize Theron", "1976"}, {"Charlize Theron", "1976"}, {"Liv Tyler", "1978"},
                    {"Jerry O Connell", "1975"}, {"Emil Eifrem", "1979"}, {"Paul Bettany", "1972"},
                    {"Christian Bale", "1975"}, {"Rick Yune", "1972"}, {"Audrey Tautou", "1977"},
                    {"Natalie Portman", "1982"}, {"Christina Ricci", "1981"}, {"Rain", "1983"}, {"Rain", "1983"},
                    {"Emile Hirsch", "1986"}, {"Jonathan Lipnicki", "1997"}}}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) where m.born > 1970 RETURN m.name, (m.born + 1) order by abs(m.born "
         "+ n.released) asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.name, max(m.born) order by (m.born + 2) asc;",
            GMERR_FEATURE_NOT_SUPPORTED, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person) where m.born > 1970 RETURN m.name, max(m.born) order by (m.name || '123') asc;",
            GMERR_FEATURE_NOT_SUPPORTED, {}},

        // 4. group by clause
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.name asc;", GMERR_OK,
            {{"m.name"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.born asc;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by 1 asc;", GMERR_OK,
            {{"m.name"}, colValuesNameShuffle}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.name asc, m.name desc;", GMERR_OK,
            {{"m.name"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.name asc, m.born desc;",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by m.name asc, 1 desc;", GMERR_OK,
            {{"m.name"}, colValuesNameOrder}},
        {"MATCH (m:Person) RETURN m.name group by m.name order by char_length(m.name) asc, m.born desc;",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN count(m.name) order by 1;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN count(m.name) order by m.born;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN count(m.name) order by m.name;", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN count(m.name) order by count(m.name);", GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (m:Person) RETURN count(m.name) order by char_length(m.name) asc, m.born desc;",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN SUM(p.born), SUM(r2.rating) "
         "ORDER BY 1",
            GMERR_FEATURE_NOT_SUPPORTED, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN SUM(p.born), SUM(r2.rating) "
         "ORDER BY p.born",
            GMERR_FEATURE_NOT_SUPPORTED, {{"m.released"}, {{"NULL"}}}},
        // todo: 列名顺序反了
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN SUM(r2.rating), p.born group by "
         "p.born ORDER BY p.born",
            GMERR_OK, {{"p.born", "SUM(r2.rating)"}, {{"962", "NULL"}}}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN SUM(r2.rating) group by p.born "
         "ORDER BY p.name",
            GMERR_FEATURE_NOT_SUPPORTED, {}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN SUM(r2.rating) group by p.born "
         "ORDER BY p.name || '123'",
            GMERR_FEATURE_NOT_SUPPORTED, {}},

        // 5. match statement
        // fix hop query
        // one hop
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY 1;", GMERR_OK,
            {{"m.born"}, colValuesOrderBy1}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY 25 + 23;", GMERR_OK,
            {{"m.born"}, colValuesOrderBy1}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY p.name || '123';", GMERR_UNDEFINED_OBJECT,
            {}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || '123';", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || m.name;", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY NULL;", GMERR_OK,
            {{"m.born"}, colValuesOrderByNull}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name);", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) desc, "
         "lower(m.name);",
            GMERR_OK, {{"m.born"}, colValuesOrderByRolesDesc}},
        {"MATCH (m:Person)-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) desc nulls first, "
         "abs(m.born) nulls last;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person {name: 'Tom Hanks'})-[r:ACTED_IN]->(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) "
         "desc, lower(m.name);",
            GMERR_OK,
            {{"m.born"}, {{"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"},
                             {"1956"}, {"1956"}, {"1956"}}}},
        {"MATCH (m:Person {name: 'Tom Hanks'})-[r:ACTED_IN]->(n:Movie) WHERE m.name='Tom Hanks' RETURN m.born ORDER BY "
         "r.roles || upper(m.name) desc, lower(m.name);",
            GMERR_OK,
            {{"m.born"}, {{"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"},
                             {"1956"}, {"1956"}, {"1956"}}}},

        // two hop
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN p.name order by m.title;",
            GMERR_OK,
            {{"p.name"},
                {{"Angela Scope"}, {"James Thompson"}, {"James Thompson"}, {"Angela Scope"}, {"James Thompson"},
                    {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"}, {"Angela Scope"}, {"James Thompson"},
                    {"Paul Blythe"}, {"Angela Scope"}, {"James Thompson"}}}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN p.name order by 1;", GMERR_OK,
            {{"p.name"}, {{"Paul Blythe"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"},
                             {"Angela Scope"}, {"Angela Scope"}, {"James Thompson"}, {"James Thompson"},
                             {"James Thompson"}, {"James Thompson"}, {"James Thompson"}, {"James Thompson"}}}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN p.name order by mid.name;",
            GMERR_OK,
            {{"p.name"}, {{"Paul Blythe"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"}, {"Angela Scope"},
                             {"Angela Scope"}, {"Angela Scope"}, {"James Thompson"}, {"James Thompson"},
                             {"James Thompson"}, {"James Thompson"}, {"James Thompson"}, {"James Thompson"}}}},
        {"MATCH (p:Person)-[r1:FOLLOWS]->(mid:Person)-[r2:REVIEWED]->(m:Movie) RETURN p.name order by mid.name, "
         "r2.rating;",
            GMERR_OK,
            {{"p.name"}, {{"Paul Blythe"}, {"James Thompson"}, {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"},
                             {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"}, {"James Thompson"},
                             {"James Thompson"}, {"Angela Scope"}, {"James Thompson"}, {"Angela Scope"}}}},

        // three hop
        {"MATCH (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by m.title;",
            GMERR_OK,
            {{"r3.summary"},
                {{"An amazing journey"}, {"You had me at Jerry"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"A solid romp"}, {"Silly, but fun"}, {"Dark, but compelling"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by 1;",
            GMERR_OK,
            {{"r3.summary"},
                {{"You had me at Jerry"}, {"A solid romp"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"Dark, but compelling"}, {"Silly, but fun"}, {"An amazing journey"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by mid.name;",
            GMERR_OK,
            {{"r3.summary"},
                {{"You had me at Jerry"}, {"A solid romp"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"Dark, but compelling"}, {"Silly, but fun"}, {"An amazing journey"}}}},
        {"MATCH  (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by mid.name, r2.rating;",
            GMERR_UNDEFINE_COLUMN, {}},

        // any direction
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY 1;", GMERR_OK,
            {{"m.born"}, colValuesOrderBy1}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY 25 + 23;", GMERR_OK,
            {{"m.born"}, colValuesOrderBy1}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY p.name || '123';", GMERR_UNDEFINED_OBJECT, {}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || '123';", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || m.name;", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY NULL;", GMERR_OK,
            {{"m.born"}, colValuesOrderByNull}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name);", GMERR_OK,
            {{"m.born"}, colValuesOrderByRoles}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) desc, lower(m.name);",
            GMERR_OK, {{"m.born"}, colValuesOrderByRolesDesc}},
        {"MATCH (m:Person)-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) desc nulls first, "
         "abs(m.born) nulls last;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person {name: 'Tom Hanks'})-[r:ACTED_IN]-(n:Movie) RETURN m.born ORDER BY r.roles || upper(m.name) "
         "desc, lower(m.name);",
            GMERR_OK,
            {{"m.born"}, {{"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"},
                             {"1956"}, {"1956"}, {"1956"}}}},
        {"MATCH (m:Person {name: 'Tom Hanks'})-[r:ACTED_IN]-(n:Movie) WHERE m.name='Tom Hanks' RETURN m.born ORDER BY "
         "r.roles || upper(m.name) desc, lower(m.name);",
            GMERR_OK,
            {{"m.born"}, {{"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"}, {"1956"},
                             {"1956"}, {"1956"}, {"1956"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]-(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by m.title;",
            GMERR_OK,
            {{"r3.summary"},
                {{"An amazing journey"}, {"You had me at Jerry"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"A solid romp"}, {"Silly, but fun"}, {"Dark, but compelling"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]-(mid:Person)-[r2:FOLLOWS]->(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by 1;",
            GMERR_OK,
            {{"r3.summary"},
                {{"You had me at Jerry"}, {"A solid romp"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"Dark, but compelling"}, {"Silly, but fun"}, {"An amazing journey"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]-(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by mid.name;",
            GMERR_OK,
            {{"r3.summary"},
                {{"You had me at Jerry"}, {"A solid romp"},
                    {"Slapstick redeemed only by the Robin Williams and Gene Hackmans stellar performances"},
                    {"Dark, but compelling"}, {"Silly, but fun"}, {"An amazing journey"},
                    {"Fun, but a little far fetched"}, {"The coolest football movie ever"},
                    {"Pretty funny at times"}}}},
        {"MATCH (p:Person)-[r:FOLLOWS]->(mid:Person)-[r2:FOLLOWS]-(p2:Person)-[r3:REVIEWED]->(m:Movie) RETURN "
         "r3.summary order by mid.name, r2.rating;",
            GMERR_UNDEFINE_COLUMN, {}},

        // // variable hop query
        {"MATCH (p:Person)-[e]->{0,3}(m:Movie) RETURN p.name order by e.rating;", GMERR_SEMANTIC_ERROR, {}},
        {"MATCH (p:Person)-[e]->{0,3}(m:Movie) RETURN m.released, count(m.released) group by m.released order by "
         "m.released;",
            GMERR_OK,
            {{"m.released", "count(m.released)"},
                {{"1975", "3"}, {"1986", "16"}, {"1990", "4"}, {"1992", "34"}, {"1993", "7"}, {"1995", "11"},
                    {"1996", "17"}, {"1997", "9"}, {"1998", "21"}, {"1999", "25"}, {"2000", "31"}, {"2003", "20"},
                    {"2004", "2"}, {"2006", "26"}, {"2007", "4"}, {"2008", "18"}, {"2009", "8"}, {"2012", "13"}}}},
        {"MATCH (p:Person)-[e]->{0,3}(m:Movie) RETURN p.name order by p.born, m.released limit 5;", GMERR_OK,
            {{"p.name"},
                {{"Max von Sydow"}, {"Max von Sydow"}, {"Richard Harris"}, {"Gene Hackman"}, {"Clint Eastwood"}}}},
        {"MATCH (p:Person)-[e]-{0,3}(m:Movie) RETURN p.name order by e.rating;", GMERR_SEMANTIC_ERROR, {}},
        {"MATCH (p:Person)-[e]-{0,3}(m:Movie) RETURN m.released, count(m.released) group by m.released order by "
         "m.released;",
            GMERR_OK,
            {{"m.released", "count(m.released)"},
                {{"1975", "37"}, {"1986", "119"}, {"1990", "92"}, {"1992", "344"}, {"1993", "111"}, {"1995", "148"},
                    {"1996", "220"}, {"1997", "113"}, {"1998", "280"}, {"1999", "404"}, {"2000", "336"},
                    {"2003", "646"}, {"2004", "64"}, {"2006", "525"}, {"2007", "70"}, {"2008", "377"}, {"2009", "247"},
                    {"2012", "263"}}}},
        {"MATCH (p:Person)-[e]-{0,3}(m:Movie) RETURN p.name order by p.born, m.released limit 5;", GMERR_OK,
            {{"p.name"},
                {{"Max von Sydow"}, {"Max von Sydow"}, {"Max von Sydow"}, {"Max von Sydow"}, {"Max von Sydow"}}}},

        // // without label
        {"MATCH (p) RETURN p.name order by p.born, p.name;", GMERR_OK, {{"p.name"}, colValuesBornNameOrder}},
        {"MATCH (p) RETURN p.name order by p.born desc nulls first, p.name;", GMERR_OK,
            {{"p.name"}, colValuesBornDescNullsFirstNameOrder}},
        {"MATCH (p) RETURN p.name order by p.born asc, p.name;", GMERR_OK, {{"p.name"}, colValuesBornNameOrder}},
        {"MATCH (p) RETURN p.name order by p.born asc nulls last, p.name;", GMERR_OK,
            {{"p.name"}, colValuesBornNameOrder}},
        {"MATCH (p) RETURN p.name order by p.born, p.name asc nulls last limit 10;", GMERR_OK,
            {{"p.name"},
                {{"Max von Sydow"}, {"Clint Eastwood"}, {"Gene Hackman"}, {"Richard Harris"}, {"Mike Nichols"},
                    {"Milos Forman"}, {"Tom Skerritt"}, {"Jack Nicholson"}, {"Frank Langella"}, {"Ian McKellen"}}}},
        {"MATCH (p) RETURN p.name, p.released order by p.born asc nulls first, p.released limit 5;", GMERR_OK,
            {{"p.name", "p.released"},
                {{"NULL", "1975"}, {"NULL", "1986"}, {"NULL", "1986"}, {"NULL", "1990"}, {"NULL", "1992"}}}},
        {"MATCH (m)-[r]-(n) RETURN m.born, n.released ORDER BY m.released, n.born limit 5;", GMERR_OK,
            {{"m.born", "n.released"},
                {{"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}}}},
        {"MATCH (m)-[r]-(n) RETURN m.born, n.released ORDER BY m.released nulls last, n.born limit 5;", GMERR_OK,
            {{"m.born", "n.released"},
                {{"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}}}},
        {"MATCH (m)-[r:ACTED_IN]->(n) RETURN m.born, n.released ORDER BY m.released nulls last, n.born, r.roles desc "
         "limit 9;",
            GMERR_OK,
            {{"m.born", "n.released"},
                {{"1956", "2012"}, {"1968", "1999"}, {"1940", "1999"}, {"1953", "1993"}, {"1949", "2012"},
                    {"1933", "1986"}, {"1974", "1986"}, {"1960", "2006"}, {"1980", "2008"}}}},

        {"MATCH (m)-[r]-{0,3}(n) RETURN m.born, n.released ORDER BY m.released, n.born limit 5;", GMERR_OK,
            {{"m.born", "n.released"},
                {{"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}}}},
        {"MATCH (m)-[r]-{0,3}(n) RETURN m.born, n.released ORDER BY m.released nulls last, n.born limit 5;", GMERR_OK,
            {{"m.born", "n.released"},
                {{"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}, {"NULL", "NULL"}}}},
        {"MATCH (m)-[r]-{0,3}(n) RETURN m.born, n.released ORDER BY m.released nulls last, r.roles, n.born limit 10;",
            GMERR_SEMANTIC_ERROR, {{"m.released"}, {{"NULL"}}}},

        // // filter
        {"MATCH (p {born: 1961}) RETURN p.name order by p.name desc;", GMERR_OK,
            {{"p.name"}, {{"Stefan Arndt"}, {"Meg Ryan"}, {"Laurence Fishburne"}, {"Bonnie Hunt"}, {"Aaron Sorkin"}}}},
        {"MATCH (p {born: 1961}) RETURN p.name order by p desc;", GMERR_FEATURE_NOT_SUPPORTED, {}}

        // todo
        // 6. transaction
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }

    vector<std::string> indexGqls = {
        "CREATE INDEX node_single_vertex_index_name ON Person(name)",
        "CREATE INDEX node_single_vertex_index_name1 ON Person(born)",
        "CREATE INDEX node_single_vertex_index_name2 ON Person(isVip)",
        "CREATE INDEX node_single_vertex_index_name3 ON Person(score)",
        "CREATE INDEX node_single_edge_index_name ON ACTED_IN(roles)",
        "CREATE INDEX node_single_edge_index_name1 ON REVIEWED(rating)",
    };
    for (auto indexGql : indexGqls) {
        StEmbGqlExecCmdWithStatus(conn, indexGql.c_str(), GMERR_OK);
    }

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}

HWTEST_P(StEmbGqlOrderBy, OrderByKeyWord, TestSize.Level0)
{
    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. reserved key word
        {"MATCH (m:Person)-[r1:FOLLOWS]->(order:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(ORDER:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(asc:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(ASCENDING:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(desc:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(DESCENDING:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(NULLS:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(nulls:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_SYNTAX_ERROR, {}},
        // 2. unreserved key word
        {"MATCH (m:Person)-[r1:FOLLOWS]->(first:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_OK,
            {{"m.name", "m.born"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(last:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_OK,
            {{"m.name", "m.born"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(LAST:Person) RETURN m.name, m.born order by m.name asc, m.born asc;",
            GMERR_OK,
            {{"m.name", "m.born"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}

HWTEST_P(StEmbGqlOrderBy, Alias, TestSize.Level0)
{
    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. reserved key word
        {"MATCH (m:Person)-[r1:FOLLOWS]->(order:Person) RETURN m.name as order, m.born order by m.name asc, m.born "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(order:Person) RETURN m.name as t1, m.born as order order by m.name asc, "
         "m.born asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as t1, m.born as false order by t1 asc, false asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as t1, m.born as delete order by m.name asc, m.born "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(order:Person) RETURN m.name as t1, m.born as descending order by t1 asc, "
         "m.born asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        // 2. unreserved key word
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN 1 as name, m.born as first order by name asc, first desc;",
            GMERR_OK, {{"name", "first"}, {{"1", "NULL"}, {"1", "NULL"}, {"1", "NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN 1 as name, m.born as last order by name asc, last desc;",
            GMERR_OK, {{"name", "last"}, {{"1", "NULL"}, {"1", "NULL"}, {"1", "NULL"}}}},
        // 3. special character
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as %, m.born as kkone order by m.name asc, first "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as -, m.born as kkone order by m.name asc, first "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as %t, m.born as kkone order by m.name asc, first "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as r%t, m.born as kkone order by m.name asc, first "
         "asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        // 4. same with property
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as m.name, m.born as kkone order by m.name asc, "
         "kkone asc;",
            GMERR_SYNTAX_ERROR, {{"m.released"}, {{"NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone order by m.name asc, kkone "
         "asc;",
            GMERR_OK,
            {{"name", "kkone"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone order by name asc, kkone "
         "asc;",
            GMERR_OK,
            {{"name", "kkone"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        // 5. repeate alias
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as name order by name asc, m.born "
         "asc;",
            GMERR_INVALID_NAME, {{}, {}}},
        // 6. alias in where
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) where kkone > 1967 RETURN m.name as name, m.born as kkone order by "
         "name asc;",
            GMERR_UNDEFINED_OBJECT, {{}, {}}},
        // 7. alias in group by
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone group by kkone order by "
         "name asc;",
            GMERR_SYNTAX_ERROR, {{}, {}}},
        // 8. alias in order by
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone order by name asc, kkone "
         "desc;",
            GMERR_OK,
            {{"name", "kkone"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        // invlaid aliasnames
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone order by name.id asc, kkone "
         "desc;",
            GMERR_UNDEFINE_COLUMN, {{}, {}}},
        // Domain names are case insensitive
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as kkone order by NAME asc, KKONE "
         "desc;",
            GMERR_OK,
            {{"name", "kkone"}, {{"Angela Scope", "NULL"}, {"James Thompson", "NULL"}, {"Paul Blythe", "NULL"}}}},
        // duplicated name
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN m.name as name, m.born as NAME order by name asc, kkone "
         "desc;",
            GMERR_INVALID_NAME, {{}, {}}},
        {"MATCH (m:Person)-[r1:FOLLOWS]->(p:Person) RETURN 1 as name, m.born as kkone order by name asc, kkone desc;",
            GMERR_OK, {{"name", "kkone"}, {{"1", "NULL"}, {"1", "NULL"}, {"1", "NULL"}}}},
        {"MATCH (m:Person) RETURN COUNT(m.name) as name, m.born group by m.born order by name asc;", GMERR_OK,
            {{"m.BORN", "COUNT(m.NAME)"},
                {{"1", "1977"}, {"1", "1972"}, {"1", "1933"}, {"1", "1975"}, {"1", "1976"}, {"1", "1952"},
                    {"1", "1982"}, {"1", "1964"}, {"1", "1931"}, {"1", "1985"}, {"1", "1938"}, {"1", "1939"},
                    {"1", "1996"}, {"1", "1932"}, {"1", "1981"}, {"1", "1978"}, {"1", "1980"}, {"1", "1973"},
                    {"1", "1937"}, {"1", "1929"}, {"2", "1941"}, {"2", "1951"}, {"2", "1947"}, {"2", "1942"},
                    {"2", "1974"}, {"2", "1955"}, {"2", "1948"}, {"3", "1969"}, {"3", "1946"}, {"3", "1963"},
                    {"3", "1943"}, {"3", "1950"}, {"3", "1930"}, {"3", "1958"}, {"3", "1959"}, {"3", "1944"},
                    {"3", "1965"}, {"3", "1954"}, {"4", "1940"}, {"4", "1966"}, {"4", "1949"}, {"4", "1953"},
                    {"4", "1960"}, {"4", "1970"}, {"4", "1957"}, {"5", "1962"}, {"5", "1961"}, {"5", "NULL"},
                    {"5", "1971"}, {"5", "1968"}, {"6", "1956"}, {"7", "1967"}}}},
    };

    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}

// 执行一条查询（return子句包含聚合函数、order by、limit） 时，进程coredump
HWTEST_P(StEmbGqlOrderBy, orderbyLimitCore, TestSize.Level0)
{
    GmeConnT *conn = StEmbGqlGetConn();
    const char *gql = "DROP GRAPH IF EXISTS myGraph;";
    StEmbGqlExecCmdWithStatus(conn, gql, GMERR_OK);
    gql = "CREATE GRAPH graph3 {(node0:node0{id INT, vcol1 STRING}),(node0) -[:relation0{id INT}]-> (node0),(node0) "
          "-[:relation1{id INT, ecol1 INTEGER}]-> (node0)};";
    StEmbGqlExecCmdWithStatus(conn, gql, GMERR_OK);
    vector<std::string> indexGqls = {
        "INSERT (:node0 {id:13,vcol1:'string73'});",
        "INSERT (:node0 {id:60,vcol1:'string73'});",
        "INSERT (:node0 {id:64,vcol1:'string84'});",
        "INSERT (:node0 {id:79,vcol1:'string44'});",
        "INSERT (:node0 {id:61,vcol1:'string21'});",
        "INSERT (:node0 {id:30,vcol1:'string78'});",
        "INSERT (:node0 {id:87,vcol1:'string81'});",
        "INSERT (:node0 {id:69,vcol1:'string23'});",
        "INSERT (:node0 {id:28,vcol1:'string81'});",
        "INSERT (:node0 {id:76,vcol1:'string92'});",
        "INSERT (:node0 {id:37,vcol1:'string51'});",
        "INSERT (:node0 {id:93,vcol1:'string40'});",
        "MATCH (p0:node0 ),(p1:node0 ) INSERT (p0)-[e2:relation0{id:22}]->(p1);",
        "MATCH (p0:node0 where ((p0.vcol1 <= (p0.vcol1 || lower(p0.vcol1))))),(p1:node0 ) INSERT "
        "(p0)-[e2:relation0{id:56}]->(p1);",
    };
    for (auto gqlStr : indexGqls) {
        StEmbGqlExecCmdWithStatus(conn, gqlStr.c_str(), GMERR_OK);
    }

    std::vector<GqlQryCheckStmtT> gqlQryCheckStmts = {
        // 1. reserved key word
        {"MATCH (p0:node0 where (p0.id > 0))-[e1:relation0]->(p2:node0 ) return floor(0.283060), 99, (p0.vcol1 || "
         "p2.vcol1) order by p2.id,p0.vcol1,e1.id LIMIT 1;",
            GMERR_OK, {{"?column?", "?column?", "?column?"}, {{"0.00", "99", "string21string73"}}}},
    };
    for (auto gqlQryCheckStmt : gqlQryCheckStmts) {
        StGqlRstCheck(&gqlQryCheckStmt);
    }
}
