/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_instance_expr.cc
 * Description:
 * Create: 2024-05-22
 */

#include "st_emb_sql_util.h"
#include "gme_sql_inner.h"
#include "st_emb_sql_instance.h"

using namespace std;
using namespace testing::ext;

const uint32_t ST_SELECT_EXPR_ROW_COUNT = 1;
const uint32_t ST_SELECT_EXPR_COL_COUNT = 3;

class StEmbInsDQLExpr : public StEmbSqlInstance {};

static void *StEmbSqlInsExprHelper1(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table t1(a int , b text);",
        },
        {
            .sql = "insert into t1 values ( 1, 'aaa'),( 2, 'bbb'),( 3, 'ccc');",
        },
        {
            .sql = "select * from t1 where a = 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"2", "bbb"}}},
        },
        {
            .sql = "select * from t1 where b = 'bbb';",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"2", "bbb"}}},
        },
        {
            .sql = "drop table t1;",
            .expectRet = GMERR_OK,
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 表达式运算 查询
HWTEST_F(StEmbInsDQLExpr, TestMultiSelectWhereExpr1, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper1);
}

static void *StEmbSqlInsExprHelper2(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *sql = "create table t2(name text);";
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    data->ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(strcmp(unused, ""), 0);
    data->ret = GmeSqlStep(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);

    sql = "insert into t2 values('zhangsanwen');";
    stmt = NULL;
    data->ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(strcmp(unused, ""), 0);
    data->ret = GmeSqlStep(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);

    sql = "select  * from t2 where name like 'zhangsan%';";
    stmt = NULL;
    data->ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(strcmp(unused, ""), 0);
    data->ret = GmeSqlStep(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);

    sql = "select  * from t2 where name like 'zhangsanwen';";
    stmt = NULL;
    data->ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(strcmp(unused, ""), 0);
    data->ret = GmeSqlStep(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);

    sql = "select  * from t2 where name like 'zhangsanwe_';";
    stmt = NULL;
    data->ret = GmeSqlPrepare(conn, sql, strlen(sql) + 1, &stmt, &unused);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(strcmp(unused, ""), 0);
    data->ret = GmeSqlStep(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *dropTableSql = "drop table t2;";
    data->ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithLikeExprBasic, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper2);
}

static void *StEmbSqlInsExprHelper3(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createTableSql = "create table COMPANY(id int, name text, age text, address text);";
    data->ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql = "INSERT INTO COMPANY VALUES "
                            "(1, 'Paul', '22', 'Rich-Mond'),"
                            "(2, 'Allen', '35', 'Texas'),"
                            "(3, 'Markis', '45', 'California'),"
                            "(4, '%_*?!@#', '54', 'start!@#%[]*dsfgsdf\\gend');";
    data->ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql[] = {
        "select * from COMPANY where ADDRESS like '%-%';",
        "select * from COMPANY where AGE like '2%';",
        "select * from COMPANY where NAME like '____';",
        "select * from COMPANY where ADDRESS like '%DSF%end';",
        "select * from COMPANY where ADDRESS like '_tart%';",
    };

    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "name", "age", "address"}, {{"1", "Paul", "22", "Rich-Mond"}}};
    for (uint32_t i = 0; i < 3; i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult1);
        StEmbSqlClearActQryResultSet(data);
    }
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "age", "address"}, {{"4", "%_*?!@#", "54", "start!@#%[]*dsfgsdf\\gend"}}};
    for (uint32_t i = 3; i < 5; i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult2);
        StEmbSqlClearActQryResultSet(data);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE COMPANY;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithLikeExpr, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper3);
}

static void *StEmbSqlInsExprHelper4(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createTableSql = "create table COMPANY(id int, name text, age text, address text);";
    data->ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql = "INSERT INTO COMPANY VALUES "
                            "(1, 'Paul', '22', 'Rich-Mond'),"
                            "(2, 'Allen', '35', 'Texasa'),"
                            "(3, 'Marks', '35', 'California');";
    data->ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql[] = {
        "select * from COMPANY where AGE not like '3%';",
        "select * from COMPANY where NAME not like '_____';",
        "select * from COMPANY where ADDRESS not like '%a';",
    };

    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "name", "age", "address"}, {{"1", "Paul", "22", "Rich-Mond"}}};
    for (uint32_t i = 0; i < sizeof(whereSelectSql) / sizeof(const char *); i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult1);
        StEmbSqlClearActQryResultSet(data);
    }
    data->ret = GmeSqlExecute(conn, "DROP TABLE COMPANY;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithNotLikeExpr, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper4);
}

static void *StEmbSqlInsExprHelper5(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createTableSql = "create table COMPANY(id int, name text, age text, address text);";
    data->ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql = "INSERT INTO COMPANY VALUES "
                            "(1, 'Paul', '22', 'Rich-Mond'),"
                            "(2, 'Allen', '35', 'Texas'),"
                            "(3, 'Markis', '45', 'California'),"
                            "(4, '%_*?!@#', '54', 'start!@#%[]*dsfgsdf\\gend');";
    data->ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql[] = {
        "select * from COMPANY where ADDRESS glob '*-*';",
        "select * from COMPANY where AGE glob '2*';",
        "select * from COMPANY where NAME glob '\?\?\?\?';",
        "select * from COMPANY where ADDRESS glob '*dsf*end';",
        "select * from COMPANY where ADDRESS glob '\?tart*';",
    };

    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "name", "age", "address"}, {{"1", "Paul", "22", "Rich-Mond"}}};
    for (uint32_t i = 0; i < 3; i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult1);
        StEmbSqlClearActQryResultSet(data);
    }
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "age", "address"}, {{"4", "%_*?!@#", "54", "start!@#%[]*dsfgsdf\\gend"}}};
    for (uint32_t i = 3; i < 5; i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult2);
        StEmbSqlClearActQryResultSet(data);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE COMPANY;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithGlobExpr, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper4);
}

static void *StEmbSqlInsExprHelper6(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createTableSql = "create table COMPANY(id int, name text, age text, address text);";
    data->ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql = "INSERT INTO COMPANY VALUES "
                            "(1, 'Paul', '22', 'Rich-Mond'),"
                            "(2, 'Allen', '35', 'Texasa'),"
                            "(3, 'Marks', '35', 'California');";
    data->ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql[] = {
        "select * from COMPANY where AGE not glob '3*';",
        "select * from COMPANY where NAME not glob '\?\?\?\?\?';",
        "select * from COMPANY where ADDRESS not glob '*a';",
    };

    StEmbSqlQryResultSetExtendT exptQryResult1{{"id", "name", "age", "address"}, {{"1", "Paul", "22", "Rich-Mond"}}};
    for (uint32_t i = 0; i < sizeof(whereSelectSql) / sizeof(const char *); i++) {
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult1);
        StEmbSqlClearActQryResultSet(data);
    }
    data->ret = GmeSqlExecute(conn, "DROP TABLE COMPANY;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithNotGlobExpr, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper6);
}

static void *StEmbSqlInsExprHelper7(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createTableSql = "create table student(name text);";
    data->ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql = "INSERT INTO student VALUES ('Marks');";
    data->ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql = "select * from student where 'name' like 'name%';";

    StEmbSqlQryResultSetExtendT exptQryResult1{{"name"}, {{"Marks"}}};
    data->ret = GmeSqlExecute(conn, whereSelectSql, StEmbSqlGetActQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    StEmbSqlCheckActQryResultSet(data, exptQryResult1);
    StEmbSqlClearActQryResultSet(data);

    const char *selectSql = "select '1' like '1' from student;";
    StEmbSqlQryResultSetExtendT exptQryResult2{{"?column?"}, {{"1"}}};
    data->ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    StEmbSqlCheckActQryResultSet(data, exptQryResult2);
    StEmbSqlClearActQryResultSet(data);

    data->ret = GmeSqlExecute(conn, "DROP TABLE student;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSelectWithStringLikeString, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper7);
}

static void *StEmbSqlInsExprHelper8(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    StEmbSqlClearActQryResultSet(data);
    const char *stmts = "create table t1(id int, pid int, name text);"
                        "insert into t1(id,pid, name) values(1,1,'Kimi'),(2,1,'xx'),(4,1,'xx');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *selectSql1 = "select * from t1 where +id = +1 + 1 and +pid = +1 * 2 / 2 + 5 % 3 - 2;";
    data->ret = GmeSqlExecute(conn, selectSql1, StEmbSqlGetActQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "pid", "name"}, {{"2", "1", "xx"}}};
    StEmbSqlCheckActQryResultSet(data, exptQryResult);
    StEmbSqlClearActQryResultSet(data);

    selectSql1 = "select * from t1 where -id = -(1 + 1) and -pid = -(1 * 2 / 2 + 5 % 3 - 2);";
    data->ret = GmeSqlExecute(conn, selectSql1, StEmbSqlGetActQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    StEmbSqlCheckActQryResultSet(data, exptQryResult);
    StEmbSqlClearActQryResultSet(data);

    const char *dropSql = "drop table t1;";
    data->ret = GmeSqlExecute(conn, dropSql, nullptr, nullptr, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiArithmeticExprCheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper8);
}

static void *StEmbSqlInsExprHelper9(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);
    const char *stmts = "create table bitTable(id int, pid int, name text);"
                        "insert into bitTable(id, pid, name) values(1, 1, 'xx1'),(1, 2, 'xx2'),(1, 4, 'xx4');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "pid", "name"}, {{"1", "2", "xx2"}}};

    const char *selectSql[] = {
        "select * from bitTable where pid = (1 & 1) +1;",
        "select * from bitTable where pid = (1 | 1) +1;",
        "select * from bitTable where -pid = -((1 | 1) +1);",
        "select * from bitTable where pid = 1 << 1;",
        "select * from bitTable where pid = 4 >> 1;",
    };

    for (uint32_t i = 0; i < sizeof(selectSql) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult);
        StEmbSqlClearActQryResultSet(data);
    }
    data->ret = GmeSqlExecute(conn, "DROP TABLE bitTable;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiBitExprCheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper9);
}

static void *StEmbSqlInsExprHelper10(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);
    const char *stmts = "create table tt1(id int, pid int, name text);"
                        "insert into tt1(id,pid, name) values(1,1,'Kimi');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    // 1. int与double,int与int,double与double 做算术运算,返回正常结果
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "pid", "name"}, {{"1", "1", "Kimi"}}};
    const char *selectSql[] = {
        "select * from tt1 where 2 = 1 + 1;",
        "select * from tt1 where -2 = -(1 + 1);",
        "select * from tt1 where 2.0 = 1 + 1.0;",
        "select * from tt1 where 2.0 = 1.0 + 1;",
        "select * from tt1 where -2.0 = -(1.0 + 1);",
        "select * from tt1 where 2.0 = 1.0 + 1.0;",
        "select * from tt1 where 1 = 2 - 1;",
        "select * from tt1 where 1.0 = 2 - 1.0;",
        "select * from tt1 where 1.0 = 2.0 - 1;",
        "select * from tt1 where 1.0 = 2.0 - 1.0;",
        "select * from tt1 where 2 = 2 * 1;",
        "select * from tt1 where 2.0 = 2 * 1.0;",
        "select * from tt1 where 2.0 = 2.0 * 1;",
        "select * from tt1 where -2.0 = -2.0 * 1;",
        "select * from tt1 where 2.0 = 2.0 * 1.0;",
        "select * from tt1 where 2 = 2 / 1;",
        "select * from tt1 where 2.0 = 2 / 1.0;",
        "select * from tt1 where 2.0 = 2.0 / 1;",
        "select * from tt1 where 2.0 = 2.0 / 1.0;",
        "select * from tt1 where 1 = 5 % 2;",
        "select * from tt1 where -1 = -5 % 2;",
        // 模余运算: 支持定长数据的模余运算(都转为int64,再做模余运算,结果也为int64)
        "select * from tt1 where 1 = 5.0 % 2;",
        "select * from tt1 where -1 = -5.0 % 2;",
        "select * from tt1 where 1 = 5 % 2.0;",
    };

    for (uint32_t i = 0; i < sizeof(selectSql) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult);
        StEmbSqlClearActQryResultSet(data);
    }
    // 2. int与str,double与str做算术运算,不支持,返回错误码GMERR_INVALID_VALUE
    const char *selectSql1[] = {
        "select * from tt1 where 1 = 'a' + 1;",
        "select * from tt1 where 1 = 'a' + 1.0;",
        "select * from tt1 where 1 = 1.0 + 'a';",
        "select * from tt1 where 1 = 1 + 'a';",

        "select * from tt1 where 1 = 'a' - 1;",
        "select * from tt1 where 1 = 'a' - 1.0;",
        "select * from tt1 where 1 = 1.0 - 'a';",
        "select * from tt1 where 1 = 1 - 'a';",

        "select * from tt1 where 1 = 'a' * 1;",
        "select * from tt1 where 1 = 'a' * 1.0;",
        "select * from tt1 where 1 = 1.0 * 'a';",
        "select * from tt1 where 1 = 1 * 'a';",

        "select * from tt1 where 1 = 'a' / 1;",
        "select * from tt1 where 1 = 'a' / 1.0;",
        "select * from tt1 where 1 = 1.0 / 'a';",
        "select * from tt1 where 1 = 1 / 'a';",

        "select * from tt1 where 1 = 'a' % 1;",
        "select * from tt1 where 1 = 'a' % 1.0;",
        "select * from tt1 where 1 = 1.0 % 'a';",
        "select * from tt1 where 1 = 1 % 'a';",
    };
    for (uint32_t i = 0; i < sizeof(selectSql1) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_INVALID_VALUE);
        StEmbSqlClearActQryResultSet(data);
    }

    // 3. str与str 做算术运算,不支持,返回错误码GMERR_INVALID_VALUE
    const char *selectSql2[] = {
        "select * from tt1 where 1 = 'a' + 'a';",
        "select * from tt1 where 1 = 'a' - 'a';",
        "select * from tt1 where 1 = 'a' * 'a';",
        "select * from tt1 where 1 = 'a' / 'a';",
        "select * from tt1 where 1 = 'a' % 'a';",
    };
    for (uint32_t i = 0; i < sizeof(selectSql2) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql2[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_INVALID_VALUE);
        StEmbSqlClearActQryResultSet(data);
    }

    // 4. 算术运算符中出现NULL,返回空
    const char *selectSql4[] = {
        "select * from tt1 where 1 = 1 + NULL;",
        "select * from tt1 where 1 = -1 + NULL;",
        "select * from tt1 where 1 = NULL + 1;",
        "select * from tt1 where 1 = -NULL + 1;",
        "select * from tt1 where 1 = NULL + NULL;",
        "select * from tt1 where 1 = 1 - NULL;",
        "select * from tt1 where 1 = NULL - 1;",
        "select * from tt1 where 1 = NULL - NULL;",
        "select * from tt1 where 1 = 1 * NULL;",
        "select * from tt1 where 1 = -1 * NULL;",
        "select * from tt1 where 1 = NULL * 1;",
        "select * from tt1 where 1 = -NULL * 1;",
        "select * from tt1 where 1 = NULL * NULL;",
        "select * from tt1 where 1 = 1 / NULL;",
        "select * from tt1 where 1 = NULL / 1;",
        "select * from tt1 where 1 = -NULL / 1;",
        "select * from tt1 where 1 = NULL / NULL;",
        "select * from tt1 where 1 = 1 % NULL;",
        "select * from tt1 where 1 = -1 % NULL;",
        "select * from tt1 where 1 = NULL % 1;",
        "select * from tt1 where 1 = -NULL % 1;",
        "select * from tt1 where 1 = NULL % NULL;",
        "select * from tt1 where -1 = NULL % NULL;",
    };
    StEmbSqlQryResultSetExtendT nullQryResult{};
    for (uint32_t i = 0; i < sizeof(selectSql4) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql4[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, nullQryResult);
        StEmbSqlClearActQryResultSet(data);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE tt1;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiWhereExpr1CheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper10);
}

static void *StEmbSqlInsExprHelper11(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "create table tt2(id int, pid int, name text);"
                        "insert into tt2(id,pid, name) values(1,1,'Kimi');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    // 1. 位运算,当前只支持int类型
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "pid", "name"}, {{"1", "1", "Kimi"}}};
    const char *selectSql[] = {
        "select * from tt2 where 1 = 1 & 1;",
        "select * from tt2 where 1 = 1 | 1;",
        "select * from tt2 where 2 = 1 << 1;",
        "select * from tt2 where 1 = 2 >> 1;",
    };
    for (uint32_t i = 0; i < sizeof(selectSql) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult);
        StEmbSqlClearActQryResultSet(data);
    }
    // 2. int与str,double与str,str与str 做位运算,不支持,返回错误码GMERR_INVALID_VALUE
    const char *selectSql1[] = {
        "select * from tt2 where 1 = 'a' & 1;",
        "select * from tt2 where 1 = 'a' & 1.0;",
        "select * from tt2 where 1 = 1.0 & 'a';",
        "select * from tt2 where 1 = 1 & 'a';",
        "select * from tt2 where 1 = 'a' & 'a';",
        "select * from tt2 where 1 = 'a' | 1;",
        "select * from tt2 where 1 = 'a' | 1.0;",
        "select * from tt2 where 1 = 1.0 | 'a';",
        "select * from tt2 where 1 = 1 | 'a';",
        "select * from tt2 where 1 = 'a' | 'a';",
        "select * from tt2 where 1 = 'a' << 1;",
        "select * from tt2 where 1 = 'a' << 1.0;",
        "select * from tt2 where 1 = 1.0 << 'a';",
        "select * from tt2 where 1 = 1 << 'a';",
        "select * from tt2 where 1 = 'a' << 'a';",
        "select * from tt2 where 1 = 'a' >> 1;",
        "select * from tt2 where 1 = 'a' >> 1.0;",
        "select * from tt2 where 1 = 1.0 >> 'a';",
        "select * from tt2 where 1 = 1 >> 'a';",
        "select * from tt2 where 1 = 'a' >> 'a';",
    };
    for (uint32_t i = 0; i < sizeof(selectSql1) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_INVALID_VALUE);
        StEmbSqlClearActQryResultSet(data);
    }
    // 3. int 与 double 做位运算,暂不支持,返回错误码GMERR_INVALID_VALUE
    const char *selectSql2[] = {
        "select * from tt2 where 1 = 1 & 1.0;",
        "select * from tt2 where 1 = 1.0 & 1;",
        "select * from tt2 where 1 = 1.0 & 1.0;",
        "select * from tt2 where 1 = 1 | 1.0;",
        "select * from tt2 where 1 = 1.0 | 1;",
        "select * from tt2 where 1 = 1.0 | 1.0;",
        "select * from tt2 where 2 = 1 << 1.0;",
        "select * from tt2 where 2 = 1.0 << 1;",
        "select * from tt2 where 2 = 1.0 << 1.0;",
        "select * from tt2 where 1 = 2 >> 1.0;",
        "select * from tt2 where 1 = 2.0 >> 1;",
        "select * from tt2 where 1 = 2.0 >> 1.0;",
    };
    for (uint32_t i = 0; i < sizeof(selectSql2) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql2[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_INVALID_VALUE);
        StEmbSqlClearActQryResultSet(data);
    }
    // 4. 位运算符中出现NULL,返回空
    const char *selectSql3[] = {
        "select * from tt2 where 1 = 1 & NULL;",
        "select * from tt2 where 1 = NULL & 1;",
        "select * from tt2 where 1 = NULL & NULL;",
        "select * from tt2 where 1 = 1 | NULL;",
        "select * from tt2 where 1 = NULL | 1;",
        "select * from tt2 where 1 = NULL | NULL;",
        "select * from tt2 where 1 = 1 << NULL;",
        "select * from tt2 where 1 = NULL << 1;",
        "select * from tt2 where 1 = NULL << NULL;",
        "select * from tt2 where 1 = 1 >> NULL;",
        "select * from tt2 where 1 = NULL >> 1;",
        "select * from tt2 where 1 = NULL >> NULL;",
    };
    StEmbSqlQryResultSetExtendT nullQryResult{};
    for (uint32_t i = 0; i < sizeof(selectSql3) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql3[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, nullQryResult);
        StEmbSqlClearActQryResultSet(data);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE tt2;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiWhereExpr2CheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper11);
}

static void *StEmbSqlInsExprHelper12(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "create table tt3(id int, pid int, name text);"
                        "insert into tt3(id,pid, name) values(1,1,'Kimi');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    // 1. 比较运算符,支持int与int, int与double, double与double, str与str
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "pid", "name"}, {{"1", "1", "Kimi"}}};
    const char *selectSql[] = {
        "select * from tt3 where id = 1.0;",
        "select * from tt3 where 1 = 1;",
        "select * from tt3 where 1 = 1.0;",
        "select * from tt3 where 1.0 = 1;",
        "select * from tt3 where 1.0 = 1.0;",
        "select * from tt3 where 'a' = 'a';",
        "select * from tt3 where 1 <= 1;",
        "select * from tt3 where 1 <= 1.0;",
        "select * from tt3 where 1.0 <= 1;",
        "select * from tt3 where 1.0 <= 1.0;",
        "select * from tt3 where 'a' <= 'a';",
        "select * from tt3 where 1 >= 1;",
        "select * from tt3 where 1 >= 1.0;",
        "select * from tt3 where 1.0 >= 1;",
        "select * from tt3 where 1.0 >= 1.0;",
        "select * from tt3 where 'a' >= 'a';",
        "select * from tt3 where 1 < 2;",
        "select * from tt3 where 1 < 2.0;",
        "select * from tt3 where 1.0 < 2;",
        "select * from tt3 where 1.0 < 2.0;",
        "select * from tt3 where 'a' < 'ab';",
        "select * from tt3 where 2 > 1;",
        "select * from tt3 where 2 > 1.0;",
        "select * from tt3 where 2.0 > 1;",
        "select * from tt3 where 2.0 > 1.0;",
        "select * from tt3 where 'ab' > 'a';",
        "select * from tt3 where 1 <> 2;",
        "select * from tt3 where 1 <> 2.0;",
        "select * from tt3 where 1.0 <> 2;",
        "select * from tt3 where 1.0 <> 2.0;",
        "select * from tt3 where 'a' <> 'ab';",
        "select * from tt3 where 1 != 2;",
        "select * from tt3 where 1 != 2.0;",
        "select * from tt3 where 1.0 != 2;",
        "select * from tt3 where 1.0 != 2.0;",
        "select * from tt3 where 'a' != 'ab';",
        "select * from tt3 where 1 <> 2;",
        "select * from tt3 where 'a' <> 'ab';",
    };
    for (uint32_t i = 0; i < sizeof(selectSql) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, exptQryResult);
        StEmbSqlClearActQryResultSet(data);
    }
    // 2. 比较运算符,不支持str与int,str与double, 返回错误码GMERR_DATATYPE_MISMATCH
    const char *selectSql1[] = {
        "select * from tt3 where 'a' = 1;",
        "select * from tt3 where 'a' = 1.0;",
        "select * from tt3 where 1.0 = 'a';",
        "select * from tt3 where 1 = 'a';",
        "select * from tt3 where 'a' <= 1;",
        "select * from tt3 where 'a' <= 1.0;",
        "select * from tt3 where 1.0 <= 'a';",
        "select * from tt3 where 1 <= 'a';",
        "select * from tt3 where 'a' >= 1;",
        "select * from tt3 where 'a' >= 1.0;",
        "select * from tt3 where 1.0 >= 'a';",
        "select * from tt3 where 1 >= 'a';",
        "select * from tt3 where 'a' < 1;",
        "select * from tt3 where 'a' < 1.0;",
        "select * from tt3 where 1.0 < 'a';",
        "select * from tt3 where 1 < 'a';",
        "select * from tt3 where 'a' > 1;",
        "select * from tt3 where 'a' > 1.0;",
        "select * from tt3 where 1.0 > 'a';",
        "select * from tt3 where 1 > 'a';",
        "select * from tt3 where 'a' <> 1;",
        "select * from tt3 where 'a' <> 1.0;",
        "select * from tt3 where 1.0 <> 'a';",
        "select * from tt3 where 1 <> 'a';",
        "select * from tt3 where 'a' != 1;",
        "select * from tt3 where 'a' != 1.0;",
        "select * from tt3 where 1.0 != 'a';",
        "select * from tt3 where 1 != 'a';",
    };
    for (uint32_t i = 0; i < sizeof(selectSql1) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_DATATYPE_MISMATCH);
        StEmbSqlClearActQryResultSet(data);
    }
    // 3. 比较运算符中出现NULL,返回空
    const char *selectSql3[] = {
        "select * from tt3 where null = 1;   ",
        "select * from tt3 where null = 1.0;  ",
        "select * from tt3 where null = 'a';  ",
        "select * from tt3 where 1  = null; ",
        "select * from tt3 where 1.0 = null; ",
        "select * from tt3 where 'a' = null; ",
        "select * from tt3 where null = null; ",
        "select * from tt3 where null <= 1;  ",
        "select * from tt3 where null <= 1.0; ",
        "select * from tt3 where null <= 'a'; ",
        "select * from tt3 where 1  <= null; ",
        "select * from tt3 where 1.0 <= null; ",
        "select * from tt3 where 'a' <= null; ",
        "select * from tt3 where null <= null; ",
        "select * from tt3 where null >= 1;  ",
        "select * from tt3 where null >= 1.0; ",
        "select * from tt3 where null >= 'a'; ",
        "select * from tt3 where 1  >= null; ",
        "select * from tt3 where 1.0 >= null; ",
        "select * from tt3 where 'a' >= null; ",
        "select * from tt3 where null >= null; ",
        "select * from tt3 where null < 1;   ",
        "select * from tt3 where null < 1.0;  ",
        "select * from tt3 where null < 'a';  ",
        "select * from tt3 where 1  < null; ",
        "select * from tt3 where 1.0 < null; ",
        "select * from tt3 where 'a' < null; ",
        "select * from tt3 where null < null; ",
        "select * from tt3 where null > 1;   ",
        "select * from tt3 where null > 1.0;  ",
        "select * from tt3 where null > 'a';  ",
        "select * from tt3 where 1  > null; ",
        "select * from tt3 where 1.0 > null; ",
        "select * from tt3 where 'a' > null; ",
        "select * from tt3 where null > null; ",
        "select * from tt3 where null <> 1;  ",
        "select * from tt3 where null <> 1.0; ",
        "select * from tt3 where null <> 'a'; ",
        "select * from tt3 where 1  <> null; ",
        "select * from tt3 where 1.0 <> null; ",
        "select * from tt3 where 'a' <> null; ",
        "select * from tt3 where null <> null; ",
        "select * from tt3 where null != 1;  ",
        "select * from tt3 where null != 1.0; ",
        "select * from tt3 where null != 'a'; ",
        "select * from tt3 where 1  != null; ",
        "select * from tt3 where 1.0 != null; ",
        "select * from tt3 where 'a' != null; ",
        "select * from tt3 where null != null; ",
    };
    StEmbSqlQryResultSetExtendT nullQryResult{};
    for (uint32_t i = 0; i < sizeof(selectSql3) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, selectSql3[i], StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        StEmbSqlCheckActQryResultSet(data, nullQryResult);
        StEmbSqlClearActQryResultSet(data);
    }
    data->ret = GmeSqlExecute(conn, "DROP TABLE tt3;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiWhereExpr3CheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper12);
}

static void *StEmbSqlInsExprHelper13(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    StEmbSqlCleanQryResultSet(&data->result);
    const char *stmts = "create table stt(id int, cid int, name text);"
                        "insert into stt(id, cid, name) values(1, 1, 'xx1'),(2, 2, 'xx2'),(3, 4, 'xx4'),(4, 8, "
                        "null),(5, null, 'xx16');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeSqlExecute(conn, "select * from stt;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(true, data->result.rowCount == 5);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 1;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(true, data->result.rowCount == 5);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 'nice';", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where name;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 5 - 5;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(true, data->result.rowCount == 0);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 5 - 2;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(true, data->result.rowCount == 5);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 1 and 2;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    EXPECT_EQ(true, data->result.rowCount == 5);
    StEmbSqlCleanQryResultSet(&data->result);
    data->ret = GmeSqlExecute(conn, "select * from stt where 'tt' and 2;", StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    StEmbSqlCleanQryResultSet(&data->result);

    data->ret = GmeSqlExecute(conn, "DROP TABLE stt;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiSingleWhereExprCheck, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper13);
}

static void *StEmbSqlInsExprHelper14(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "create table tt4(id int, pid int, name text);"
                        "insert into tt4(id, pid, name) values(1, 1, 'xx1'),(1, 2, 'xx2'),(1, 4, 'xx4');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    // 1.between ... and ...
    StEmbSqlQryResultSetT expectedResult = {};
    const char *colNamesOne[ST_SELECT_EXPR_COL_COUNT] = {"id", "pid", "name"};
    const char *rowsOne[ST_SELECT_EXPR_ROW_COUNT][ST_SELECT_EXPR_COL_COUNT] = {{"1", "2", "xx2"}};
    StEmbSqlConstructQryResultSet(&expectedResult, ST_SELECT_EXPR_ROW_COUNT, ST_SELECT_EXPR_COL_COUNT, colNamesOne,
        reinterpret_cast<const char **>(rowsOne));

    const char *selectSql[] = {
        "select * from tt4 where pid between 2 and 3;",
        "select * from tt4 where pid between 2 and 2;",
        "select * from tt4 where (pid between 2 and 3) and (pid between 2 and 4);",
        "select * from tt4 where pid between 2 and 3 and pid between 2 and 4;",
    };

    for (uint32_t i = 0; i < sizeof(selectSql) / sizeof(const char *); ++i) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, selectSql[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        bool isEqual = StEmbSqlVerifyQryResult(&expectedResult, &data->result);
        EXPECT_EQ(true, isEqual);
    }

    // 2.between const1 and const2. 当 const1 > const2.
    StEmbSqlQryResultSetT expectedResult1 = {};
    StEmbSqlConstructQryResultSet(&expectedResult1, 0, 0, NULL, NULL);
    StEmbSqlCleanQryResultSet(&data->result);
    const char *selectSql2 = "select * from tt4 where pid between 4 and 2;";

    data->ret = GmeSqlExecute(conn, selectSql2, StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    bool isEqual3 = StEmbSqlVerifyQryResult(&expectedResult1, &data->result);
    EXPECT_EQ(true, isEqual3);

    data->ret = GmeSqlExecute(conn, "DROP TABLE tt4;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiBetweenExprCheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper14);
}

static void *StEmbSqlInsExprHelper15(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *createSql =
        "create table selcon(id int, age int, salary int, height int, name text, home text, job text);";
    data->ret = GmeSqlExecute(conn, createSql, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *insertSql[] = {
        "insert into selcon VALUES(2, 40, 30000, 175, 'TOM', 'jiangsu', 'BBB');",
        "insert into selcon VALUES(3, 30, 20000, 190, 'Alice', 'xinjiang', 'FBI');",
        "insert into selcon VALUES(4, 22, 16666, 180, 'Anna', 'guangdong', 'XXX');",
        "insert into selcon VALUES(5, 22, 20000, 175, 'Jimmy', 'guangdong', 'AAA');",
        "insert into selcon VALUES(1, 25, 10000, 25, 'TOM', 'henan', 'FBI');",
        "insert into selcon VALUES(1, 25, 20000, 25, 'TOM', 'henan', 'TOM');",
        "insert into selcon VALUES(11, 25, 10000, 25, 'TOM2', 'anhui', 'TOM');",
        "insert into selcon VALUES(11, 25, 10000, 25, 'TOM', 'jiangxi', 'TOM');",
    };

    for (uint32_t i = 0; i < sizeof(insertSql) / sizeof(const char *); ++i) {
        data->ret = GmeSqlExecute(conn, insertSql[i], NULL, NULL, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
    }

    const uint32_t ST_SELECT_EXPR_MAX_ROW_COUNT = 5;
    const uint32_t ST_SELECT_EXPR_MAX_COL_COUNT = 7;
    const char *schema[] = {"id", "age", "salary", "height", "name", "home", "job"};
    // 1. 串联字符串常量和text类型的ATTR
    const char *selectSql1[] = {"select * from selcon where name = 'Ali' || 'ce';",
        "select * from selcon where 'Ali' || 'ce' = name;", "select * from selcon where 'guang' || 'dong' = home;",
        "select * from selcon where home = 'guang' || 'dong';",
        "select * from selcon where 'jiangxiren' = home || 'ren';",
        "select * from selcon where 'laizijiangxi' = 'laizi' || home;",
        "select * from selcon where home || 'ren' = 'jiangxiren';",
        "select * from selcon where 'laizi' || home = 'laizijiangxi';",
        "select * from selcon where home || name = 'anhuiTOM2';"};
    uint32_t recCnt1[] = {1, 1, 2, 2, 1, 1, 1, 1, 1};
    const char *resultExp1[][ST_SELECT_EXPR_MAX_ROW_COUNT][ST_SELECT_EXPR_MAX_COL_COUNT] = {
        {{"3", "30", "20000", "190", "Alice", "xinjiang", "FBI"}},
        {{"3", "30", "20000", "190", "Alice", "xinjiang", "FBI"}},
        {{"4", "22", "16666", "180", "Anna", "guangdong", "XXX"},
            {"5", "22", "20000", "175", "Jimmy", "guangdong", "AAA"}},
        {{"4", "22", "16666", "180", "Anna", "guangdong", "XXX"},
            {"5", "22", "20000", "175", "Jimmy", "guangdong", "AAA"}},
        {{"11", "25", "10000", "25", "TOM", "jiangxi", "TOM"}},
        {{"11", "25", "10000", "25", "TOM", "jiangxi", "TOM"}},
        {{"11", "25", "10000", "25", "TOM", "jiangxi", "TOM"}},
        {{"11", "25", "10000", "25", "TOM", "jiangxi", "TOM"}},
        {{"11", "25", "10000", "25", "TOM2", "anhui", "TOM"}},
    };
    for (uint32_t i = 0; i < sizeof(selectSql1) / sizeof(const char *); i++) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, selectSql1[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        bool isEqual = StEmbSqlVerifyQryResult2(&data->result, reinterpret_cast<const char **>(resultExp1[i]), schema,
            recCnt1[i], ST_SELECT_EXPR_MAX_COL_COUNT);
        EXPECT_EQ(true, isEqual);
    }

    // 2. 多个串联运算符级联使用
    const char *selectSql2[] = {"select * from selcon where home || name || job = 'guangdongJimmyAAA';",
        "select * from selcon where home || name || job = 'guang' || 'dong' || 'Jimmy' || 'AAA';",
        "select * from selcon where id || age || name || 3.14 = '522Jimmy3.14';"};
    uint32_t recCnt2[] = {1, 1, 1};
    const char *resultExp2[][ST_SELECT_EXPR_MAX_ROW_COUNT][ST_SELECT_EXPR_MAX_COL_COUNT] = {
        {{"5", "22", "20000", "175", "Jimmy", "guangdong", "AAA"}},
        {{"5", "22", "20000", "175", "Jimmy", "guangdong", "AAA"}},
        {{"5", "22", "20000", "175", "Jimmy", "guangdong", "AAA"}}};
    for (uint32_t i = 0; i < sizeof(selectSql2) / sizeof(const char *); i++) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, selectSql2[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        bool isEqual = StEmbSqlVerifyQryResult2(&data->result, reinterpret_cast<const char **>(resultExp2[i]), schema,
            recCnt2[i], ST_SELECT_EXPR_MAX_COL_COUNT);
        EXPECT_EQ(true, isEqual);
    }

    // 3. 非text类型数据串联场景
    const char *selectSql3[] = {
        "select * from selcon where '4030000' = age || salary;",
        "select * from selcon where '400' = age || '0';",
        "select * from selcon where '140' = 1 || age;",
        "select * from selcon where age || '0' = 40 || 0;",
        "select * from selcon where '11.' || salary = '1' || 1.16666;",
    };
    uint32_t recCnt3[] = {1, 1, 1, 1, 1};
    const char *resultExp3[][ST_SELECT_EXPR_MAX_ROW_COUNT][ST_SELECT_EXPR_MAX_COL_COUNT] = {
        {{"2", "40", "30000", "175", "TOM", "jiangsu", "BBB"}}, {{"2", "40", "30000", "175", "TOM", "jiangsu", "BBB"}},
        {{"2", "40", "30000", "175", "TOM", "jiangsu", "BBB"}}, {{"2", "40", "30000", "175", "TOM", "jiangsu", "BBB"}},
        {{"4", "22", "16666", "180", "Anna", "guangdong", "XXX"}}};
    for (uint32_t i = 0; i < sizeof(selectSql3) / sizeof(const char *); i++) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, selectSql3[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        bool isEqual = StEmbSqlVerifyQryResult2(&data->result, reinterpret_cast<const char **>(resultExp3[i]), schema,
            recCnt3[i], ST_SELECT_EXPR_MAX_COL_COUNT);
        EXPECT_EQ(true, isEqual);
    }

    // 4. 含有NULL的串联
    const char *selectSql4[] = {"select * from selcon where name = 'TOM' || NULL;",
        "select * from selcon where name = NULL || 'TOM';", "select * from selcon where name = NULL || NULL || NULL;",
        "select * from selcon where name = 'TOM' || 'TOM' || NULL || NULL;"};
    uint32_t recCnt4[] = {0, 0, 0, 0};
    for (uint32_t i = 0; i < sizeof(selectSql4) / sizeof(const char *); i++) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, selectSql4[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
        bool isEqual = StEmbSqlVerifyQryResult2(&data->result, NULL, schema, recCnt4[i], 0);
        EXPECT_EQ(true, isEqual);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE selcon;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 用例描述: SELECT语句中，WHERE子句过滤时支持串联运算符（||）
// 输入数据: 带有串联运算符的WHERE过滤场景，
// 预期结果: 成功查询记录
HWTEST_F(StEmbInsDQLExpr, MultiSelectWhereWithConcat, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper15);
}

static void *StEmbSqlInsExprHelper16(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "CREATE TABLE example (id INTEGER PRIMARY KEY,num INTEGER,text TEXT);"
                        "INSERT INTO example VALUES (1, 10, 'hello');"
                        "INSERT INTO example VALUES (2, 11, 'yes');INSERT INTO example VALUES (3, 12, 'no');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    // 验证数据
    StEmbSqlQryResultSetT expectedResult = {0};
    const char *colNamesOne[ST_SELECT_EXPR_COL_COUNT] = {"id", "num", "text"};
    const char *rowsOne[ST_SELECT_EXPR_ROW_COUNT][ST_SELECT_EXPR_COL_COUNT] = {{"3", "12", "no"}};
    StEmbSqlConstructQryResultSet(&expectedResult, ST_SELECT_EXPR_ROW_COUNT, ST_SELECT_EXPR_COL_COUNT, colNamesOne,
        reinterpret_cast<const char **>(rowsOne));

    StEmbSqlCleanQryResultSet(&data->result);
    const char *selectSql = "SELECT * FROM example WHERE (num & 12) = 12;";
    data->ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetQryResultSet, data, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);
    bool isEqual = StEmbSqlVerifyQryResult(&expectedResult, &data->result);
    EXPECT_EQ(true, isEqual);
    StEmbSqlCleanQryResultSet(&data->result);

    data->ret = GmeSqlExecute(conn, "DROP TABLE example;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 补充测试二元运算的孩子孩子节点为column.
HWTEST_F(StEmbInsDQLExpr, MultiBitAndExprCheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper16);
}

static void *StEmbSqlInsExprHelper17(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "CREATE TABLE whereExpr (id INTEGER PRIMARY KEY,num INTEGER,text TEXT);"
                        "INSERT INTO whereExpr VALUES (1, 10, 'hello');"
                        "INSERT INTO whereExpr VALUES (2, 11, 'yes');INSERT INTO whereExpr VALUES (3, 12, 'no');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereSelectSql[] = {
        "select * from whereExpr where num = num -1;",
        "select * from whereExpr where id = (num - id);",
        "select * from whereExpr where id = num;",
        "select * from whereExpr where id = num - 1 ;",
        "select * from whereExpr where 1 = 2 -1 ;",

        // 表增加主键约束(默认会走索引扫描).增加const = const 场景不能走索引扫描.
        "select * from whereExpr where 1 = 1 ;",
        "select * from whereExpr where 1.0 = 1.0;",
        "select * from whereExpr where '1.1' = '1.1';",

        "select * from whereExpr where num = (num | 1) ;",
        "select * from whereExpr where num = (num | id) ;",

        "select * from whereExpr where num = (num | id) and id = num - id;",
    };
    for (uint32_t i = 0; i < sizeof(whereSelectSql) / sizeof(const char *); ++i) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, whereSelectSql[i], StEmbSqlGetQryResultSet, data, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
    }

    const char *whereDeleteSql[] = {
        "delete from whereExpr where num = num -1;",
        "delete from whereExpr where id = (num - id);",
        "delete from whereExpr where id = num;",
        "delete from whereExpr where id = num - 1 ;",
        "delete from whereExpr where 1 = 2 -1 ;",

        "delete from whereExpr where num = (num | 1) ;",
        "delete from whereExpr where num = (num | id) ;",

        "delete from whereExpr where num = (num | id) and id = num - id;",
    };
    for (uint32_t i = 0; i < sizeof(whereDeleteSql) / sizeof(const char *); ++i) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, whereDeleteSql[i], NULL, NULL, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE whereExpr;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 补充测试二元运算的孩子孩子节点为column.
HWTEST_F(StEmbInsDQLExpr, MultiWhereExprCheckStmt, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper17);
}

static void *StEmbSqlInsExprHelper18(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *stmts = "CREATE TABLE ExprUpdate (id INTEGER PRIMARY KEY, tcol1 INTEGER, tcol2 INTEGER, tcol3 TEXT);"
                        "INSERT INTO ExprUpdate VALUES (1, 1, 10, 'hello');"
                        "INSERT INTO ExprUpdate VALUES (2, 2, 11, 'yes');"
                        "INSERT INTO ExprUpdate VALUES (3, 3, 12,'no');";
    data->ret = GmeSqlExecute(conn, stmts, NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);

    const char *whereUpdateSql[] = {
        "update ExprUpdate set tcol3 = ' 100 ' where tcol1 = 1;",
        "update ExprUpdate set tcol3 = ' 105 ' where 1 = 2 -1 ;",

        "update ExprUpdate set tcol3 = ' 101 ' where tcol1 = tcol2 -9;",
        "update ExprUpdate set tcol3 = ' 102 ' where tcol1 = (tcol2 / tcol1);",

        "update ExprUpdate set tcol3 = ' 106 ' where tcol1 = (tcol1 | 1) ;",
        "update ExprUpdate set tcol3 = ' 107 ' where tcol1 = (tcol1 | tcol1) ;",

        "update ExprUpdate set tcol3 = ' 108 ' where tcol1 = (tcol1 | tcol1) and tcol1 = tcol1 - tcol1;",
    };
    for (uint32_t i = 0; i < sizeof(whereUpdateSql) / sizeof(const char *); ++i) {
        StEmbSqlCleanQryResultSet(&data->result);
        data->ret = GmeSqlExecute(conn, whereUpdateSql[i], NULL, NULL, NULL);
        EXPECT_EQ(data->ret, GMERR_OK);
    }

    data->ret = GmeSqlExecute(conn, "DROP TABLE ExprUpdate;", NULL, NULL, NULL);
    EXPECT_EQ(data->ret, GMERR_OK);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// update用例,需要创建表带主键约束,用于索引扫描.
// 目前索引不能支持表达式运算
// 所以主键字段不要用于where条件,.
HWTEST_F(StEmbInsDQLExpr, MultiWhereExprCheckStmt1, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper18);
}

static void *StEmbSqlInsExprHelper19(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tsnc(a int , b text);",
        },
        {
            .sql = "insert into tsnc values ( 1, 'aaa'),( 2, 'bbb'),( 3, 'ccc');",
        },
        {
            .sql = "select * from tsnc where a in (1, 2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "aaa"}, {"2", "bbb"}}},
        },
        {
            .sql = "select * from tsnc where a not in (1, 2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"3", "ccc"}}},
        },
        {
            .sql = "drop table tsnc;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// In表达式常规条件查询
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectInCondition, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper19);
}

static void *StEmbSqlInsExprHelper20(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tsnc(a int , b text);",
        },
        {
            .sql = "insert into tsnc values ( 1, 'aaa'),( 2, 'bbb'),( 3, 'ccc');",
        },
        {
            .sql = "select * from tsnc where a+1 in (2, 3);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "aaa"}, {"2", "bbb"}}},
        },
        {
            .sql = "select * from tsnc where a+1 in (1+1, 3);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"1", "aaa"}, {"2", "bbb"}}},
        },
        {
            .sql = "select * from tsnc where a not in (2-1, 2*1);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"a", "b"}, {{"3", "ccc"}}},
        },
        {
            .sql = "drop table tsnc;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// In表达式常规条件查询, in的子节点含表达式
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectInConditionWithExpr, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper20);
}

static void *StEmbSqlInsExprHelper21(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tsnc(a int , b text);",
        },
        {
            .sql = "insert into tsnc values ( 1, 'aaa'),( 2, 'bbb'),( 3, 'ccc');",
        },
        {
            .sql = "select * from tsnc where a in ((1, 2), (2,3));",
            .expectRet = GMERR_FEATURE_NOT_SUPPORTED,
        },
        {
            .sql = "select * from tsnc where (a, b) in (1, 2);",
            .expectRet = GMERR_FEATURE_NOT_SUPPORTED,
        },
        {
            .sql = "select * from tsnc where (a, b) in ((1, 2), (2,3));",
            .expectRet = GMERR_FEATURE_NOT_SUPPORTED,
        },
        {
            .sql = "drop table tsnc;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 不支持的In表达式常规条件查询
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectInConditionNotSuppored, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper21);
}

static void *StEmbSqlInsExprHelper22(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tsbc(id int, pid int, name text);",
        },
        {
            .sql = "insert into tsbc(id, pid, name) values(1, 1, 'xx1'),(1, 2, 'xx2'),(1, 4, 'xx4');",
        },
        {
            .sql = "select * from tsbc where pid between 2 and 3;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "2", "xx2"}}},
        },
        {
            .sql = "select * from tsbc where pid between 2 and 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "pid", "name"}, {{"1", "2", "xx2"}}},
        },
        {
            .sql = "select * from tsbc where pid between 4 and 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {},
        },
        {
            .sql = "drop table tsbc;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// Between表达式常规条件查询
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectBetweenCondition, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper22);
}

static void *StEmbSqlInsExprHelper23(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tbis(id int, cid int, name text);",
        },
        {
            .sql = "insert into tbis(id, cid, name) values(1, 1, 'xx1'),(2, 2, 'xx2'),(3, 4, 'xx4'),(4, 8, null),(5, "
                   "null, 'xx16');",
        },
        {
            .sql = "select * from tbis where cid is 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"2", "2", "xx2"}}},
        },
        {
            .sql = "select * from tbis where cid IS NOT distinct from 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"2", "2", "xx2"}}},
        },
        {
            .sql = "select * from tbis where cid is not 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"},
                {{"1", "1", "xx1"}, {"3", "4", "xx4"}, {"4", "8", "null"}, {"5", "null", "xx16"}}},
        },
        {
            .sql = "select * from tbis where cid is distinct from 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"},
                {{"1", "1", "xx1"}, {"3", "4", "xx4"}, {"4", "8", "null"}, {"5", "null", "xx16"}}},
        },
        {
            .sql = "drop table tbis;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// is表达式常规条件查询
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectIsCondition, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper23);
}

static void *StEmbSqlInsExprHelper24(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table t1(id int, cid int, name text);",
        },
        {
            .sql = "select * from t1 where id `< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id !< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id @< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id #< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id $< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id ^< 10;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "select * from t1 where id < 10!;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "drop table t1;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// 特殊关键字报语法错误
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectSpecialCharacters, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper24);
}

static void *StEmbSqlInsExprHelper25(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const EmbSqlWhereExprCaseT cases[] = {
        {
            .sql = "create table tbnull(id int, cid int, name text);",
        },
        {
            .sql = "insert into tbnull(id, cid, name) values(1, 1, 'xx1'),(2, 2, null),(3, 4, 'xx4'),(4, 8, null);",
        },
        {
            .sql = "select * from tbnull where name isnull;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"2", "2", "null"}, {"4", "8", "null"}}},
        },
        {
            .sql = "select * from tbnull where name notnull;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "cid", "name"}, {{"1", "1", "xx1"}, {"3", "4", "xx4"}}},
        },
        {
            .sql = "drop table tbnull;",
        },
    };
    for (const auto &testCase : cases) {
        data->ret = GmeSqlExecute(conn, testCase.sql,
            testCase.exptQryResult.colNames.empty() ? NULL : StEmbSqlGetActQryResultSet, data, NULL);
        EXPECT_EQ(testCase.expectRet, data->ret);
        if (!testCase.exptQryResult.colNames.empty()) {
            StEmbSqlCheckActQryResultSet(data, testCase.exptQryResult);
            StEmbSqlClearActQryResultSet(data);
        }
    }
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

// isnull notnull表达式常规条件查询
HWTEST_F(StEmbInsDQLExpr, MultiTestSelectNullCondition, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper25);
}

static void *StEmbSqlInsExprHelper26(void *args)
{
    StEmbSqlInsDataT *data = static_cast<StEmbSqlInsDataT *>(args);
    GmeConnT *conn = NULL;
    GmeOpen(data->cfgPath, GME_OPEN_CREATE, &conn);

    const char *sql = "drop table if exists t1; CREATE TABLE t1 (a int, b double);";
    data->ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);

    sql = "insert into t1 values(4, 5.1);";
    data->ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);

    StEmbSqlQryResultSetExtendT exptQryResult = {{"?column?", "?column?"}, {{"1", "2"}}};
    sql = "select a % 3.0, b % 3.0 from t1;";
    data->ret = GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, data, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);
    StEmbSqlCheckActQryResultSet(data, exptQryResult);
    StEmbSqlClearActQryResultSet(data);

    // 释放资源
    sql = "drop table t1;";
    data->ret = GmeSqlExecute(conn, sql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, data->ret);
    data->ret = GmeClose(conn);
    EXPECT_EQ(data->ret, GMERR_OK);
    return NULL;
}

HWTEST_F(StEmbInsDQLExpr, MultiTestSelectMod, TestSize.Level2)
{
    RunInMultiInstance(StEmbSqlInsExprHelper26);
}
