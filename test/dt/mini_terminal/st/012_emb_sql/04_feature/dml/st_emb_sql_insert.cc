/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_emb_sql_insert.cc
 * Description:
 * Create: 2024-02-05
 */

#include <string>
#include <vector>
#include <iostream>
#include "gme_sql_api.h"
#include "st_emb_sql_com.h"
#include "st_emb_sql_frame.h"

using namespace std;
using namespace testing::ext;

class StEmbSqlInsert : public StEmbSqlTestSuitExtend {};

typedef struct InsertStTest {
    const char *sql;
    Status expectRet = GMERR_OK;
    uint32_t expectChangeNum;
} InsertTestT;

// TestCase: No Index
HWTEST_F(StEmbSqlInsert, TestInsertData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table noIdxTable(id int, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase1: without columnList
    const char *insertSql = "insert into noIdxTable values(1, 'n1', 1),(2, 'n2', 2);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // TestCase2: with columnList
    insertSql = "insert into noIdxTable(id, name) values(3, 'n3'),(4, 'n4');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // Check
    const char *selectSql = "select * from noIdxTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "null"}, {"4", "n4", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table noIdxTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Primary Key
HWTEST_F(StEmbSqlInsert, TestInsertData_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase1: integer primary key
    const char *createTableSql = "create table pkTable(id int primary key, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *insertSql = "insert into pkTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // 插入主键冲突数据 预期返错 GMERR_PRIMARY_KEY_VIOLATION
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    const char *selectSql = "select * from pkTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table pkTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: text primary key
    createTableSql = "create table pkTable(id int, name text primary key, rank int);";
    ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertSql = "insert into pkTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // 插入主键冲突数据 预期返错 GMERR_PRIMARY_KEY_VIOLATION
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    selectSql = "select * from pkTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    dropTableSql = "drop table pkTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Unique
HWTEST_F(StEmbSqlInsert, TestInsertData_003, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase1: integer unique
    const char *createTableSql = "create table uniqueTable(id int unique, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *insertSql = "insert into uniqueTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // 插入唯一键冲突数据 预期返错 GMERR_UNIQUE_VIOLATION
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    const char *selectSql = "select * from uniqueTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table uniqueTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: text unique
    createTableSql = "create table uniqueTable(id int, name text unique, rank int);";
    ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertSql = "insert into uniqueTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入唯一键冲突数据 预期返错 GMERR_UNIQUE_VIOLATION
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    selectSql = "select * from uniqueTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Primary Key + Unique
HWTEST_F(StEmbSqlInsert, TestInsertData_004, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase1: integer primary key + text unique
    const char *createTableSql = "create table pkUniqueTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *insertSql = "insert into pkUniqueTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    const char *selectSql = "select * from pkUniqueTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table pkUniqueTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: text primary key + integer unique
    createTableSql = "create table uniquePkTable(id int unique, name text primary key, rank int);";
    ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertSql = "insert into uniquePkTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    selectSql = "select * from uniquePkTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    dropTableSql = "drop table uniquePkTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Unique Composite Index
HWTEST_F(StEmbSqlInsert, TestInsertData_005, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase:
    const char *createTableSql = "create table compositeTable(id int, name text, rank int, score int, descrp text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *createIdxSql = "create unique index idx on compositeTable(rank, name, id);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into compositeTable values(1, 'n1', 1, 100, 'good'),(2, 'n2', 2, 98, 'good');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // 插入唯一键冲突数据 预期返错 GMERR_UNIQUE_VIOLATION
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    const char *selectSql = "select * from compositeTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank", "score", "descrp"}, {{"1", "n1", "1", "100", "good"}, {"2", "n2", "2", "98", "good"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table compositeTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Primary Key + Composite Index
HWTEST_F(StEmbSqlInsert, TestInsertData_006, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase:
    const char *createTableSql = "create table compositeTable(id int primary key, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *createIdxSql = "create index idx on compositeTable(rank, name, id);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into compositeTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    const char *selectSql = "select * from compositeTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table compositeTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Unique + Composite Index
HWTEST_F(StEmbSqlInsert, TestInsertData_007, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase:
    const char *createTableSql = "create table compositeTable(id int unique, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *createIdxSql = "create index idx on compositeTable(rank, name, id);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into compositeTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    const char *selectSql = "select * from compositeTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table compositeTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Primary Key + Unique + Composite Index
HWTEST_F(StEmbSqlInsert, TestInsertData_008, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase:
    const char *createTableSql = "create table compositeTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *createIdxSql = "create index idx on compositeTable(rank, name, id);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into compositeTable values(1, 'n1', 1),(2, 'n2', 2),(3, 'n3', 3),(4, 'n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    const char *selectSql = "select * from compositeTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"}, {"4", "n4", "4"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table compositeTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Primary Key + Unique + Composite Index + NullData
HWTEST_F(StEmbSqlInsert, TestInsertData_009, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table compositeTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *createIdxSql = "create unique index idx on compositeTable(rank, name);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入数据 非null
    const char *insertSql = "insert into compositeTable values(0, 'n0', 0);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // 插入数据 null
    const char *insertNullSql1 = "insert into compositeTable values(null, 'n1', 1);";
    ret = GmeSqlExecute(conn, insertNullSql1, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    const char *insertNullSql2 = "insert into compositeTable values(2, null, 2);";
    ret = GmeSqlExecute(conn, insertNullSql2, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    const char *insertNullSql3 = "insert into compositeTable values(3, 'n3', null);";
    ret = GmeSqlExecute(conn, insertNullSql3, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // TestCase1: 再次插入 (null, 'n11', 11) 不违反主键约束 预期成功
    insertNullSql1 = "insert into compositeTable values(null, 'n11', 11);";
    ret = GmeSqlExecute(conn, insertNullSql1, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: 再次插入 (22, null, 22) 不违反唯一键约束 预期成功
    insertNullSql2 = "insert into compositeTable values(22, null, 22);";
    ret = GmeSqlExecute(conn, insertNullSql2, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase3: 再次插入 (33, 'n3', null) 不违反唯一索引约束 预期成功
    insertNullSql3 = "insert into compositeTable values(33, 'n33', null);";
    ret = GmeSqlExecute(conn, insertNullSql3, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql = "select * from compositeTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"0", "n0", "0"}, {"null", "n1", "1"}, {"2", "null", "2"}, {"3", "n3", "null"},
                                    {"null", "n11", "11"}, {"22", "null", "22"}, {"33", "n33", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table compositeTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Bool Primary Key
HWTEST_F(StEmbSqlInsert, TestInsertData_010, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table pkbTable (id bool primary key, name varchar, age bool, remark varchar);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    const char *insertSql = "insert into pkbTable values(true, 'Tom', false,'SZ'),(false, 'Jom', false,'SZ');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // 插入主键冲突数据 预期返错 GMERR_PRIMARY_KEY_VIOLATION
    insertSql = "insert into pkbTable values(true, 'Tomd', false,'SZD');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // 已经有true，插入1会主键冲突
    insertSql = "insert into pkbTable values(1, 'Tomd', false,'SZD');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // 但可以插入0，1以外的整数
    insertSql = "insert into pkbTable values(2, 'Tomd', false,'SZD'), (100, 'Tomd', false,'SZD');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // Check
    const char *selectSql = "select * from pkbTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "age", "remark"},
        {{"1", "Tom", "0", "SZ"}, {"0", "Jom", "0", "SZ"}, {"2", "Tomd", "0", "SZD"}, {"100", "Tomd", "0", "SZD"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    // 删除true
    const char *deleteSql = "delete from pkbTable where id = true;";
    ret = GmeSqlExecute(conn, deleteSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t deleteNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, deleteNum);

    // Check
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{{"id", "name", "age", "remark"},
        {{"0", "Jom", "0", "SZ"}, {"2", "Tomd", "0", "SZD"}, {"100", "Tomd", "0", "SZD"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table pkbTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Exec 超过 20 条数据的插入 查询看护
HWTEST_F(StEmbSqlInsert, TestInsertMultiData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table dataTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase: 插入 超过 100 条数据
    string insertSql = "insert into dataTable values";
    const uint32_t exptInsertNum = 100;
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        string idxStr = to_string(i);
        string nameStr = "'n" + idxStr + "'";
        string curTuple = "(" + idxStr + "," + nameStr + "," + idxStr + "),";
        insertSql += curTuple;
    }
    insertSql.back() = ';';
    ret = GmeSqlExecute(conn, static_cast<const char *>(insertSql.c_str()), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(exptInsertNum, insertNum);

    // Check
    const char *selectSql = "select * from dataTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}};
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        string idxStr = to_string(i);
        string nameStr = "n" + idxStr;
        vector<string> curTuple = {idxStr, nameStr, idxStr};
        exptQryResult.colValues.push_back(curTuple);
    }

    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table dataTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Prepare + Bind Handle
HWTEST_F(StEmbSqlInsert, TestInsertDataHandle_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table handleTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *insertSql = "insert into handleTable values(?, ?, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);

    // TestCase1: 缺省绑定 预期插入NULL
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1, insertNum);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: 绑定非空值
    string nameStr;
    const uint32_t exptInsertNum = 9;
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        ret = GmeSqlBindInt64(stmt, 1, i);
        ASSERT_EQ(GMERR_OK, ret);
        nameStr = "n";
        string idxStr = to_string(i);
        nameStr += idxStr;
        ret = GmeSqlBindText(stmt, 2, static_cast<const char *>(nameStr.c_str()), strlen(nameStr.c_str()), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindInt64(stmt, 3, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        insertNum = GmeSqlChanges(conn);
        ASSERT_EQ(1u, insertNum);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    // TestCase3: 缺省绑定上一次绑定的值 预期返错 GMERR_PRIMARY_KEY_VIOLATION
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase4: 缺省绑定上一次绑定的unique字段 预期返错 GMERR_PRIMARY_KEY_VIOLATION
    ret = GmeSqlBindInt64(stmt, 1, exptInsertNum + 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlBindInt64(stmt, 3, exptInsertNum + 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql = "select * from handleTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}, {{"null", "null", "null"}}};  // 首次缺省绑定
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        string idxStr = to_string(i);
        nameStr = "n";
        nameStr += idxStr;
        exptQryResult.colValues.push_back({idxStr, nameStr, idxStr});
    }
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table handleTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Prepare + Bind Handle + 超过 20 条数据的插入 查询看护
HWTEST_F(StEmbSqlInsert, TestInsertDataHandle_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table handleTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *insertSql = "insert into handleTable values(?, ?, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);

    // TestCase: step 超过20条数据
    string nameStr;
    const uint32_t exptInsertNum = 100;
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        ret = GmeSqlBindInt64(stmt, 1, i);
        ASSERT_EQ(GMERR_OK, ret);
        nameStr = "n";
        string idxStr = to_string(i);
        nameStr += idxStr;
        ret = GmeSqlBindText(stmt, 2, static_cast<const char *>(nameStr.c_str()), strlen(nameStr.c_str()), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindInt64(stmt, 3, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t insertNum = GmeSqlChanges(conn);
        ASSERT_EQ(1u, insertNum);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase1: Exec方式
    const char *selectSql = "select * from handleTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}};
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        string idxStr = to_string(i);
        nameStr = "n";
        nameStr += idxStr;
        exptQryResult.colValues.push_back({idxStr, nameStr, idxStr});
    }
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    // TestCase2: Prepare + Step 方式
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectSql, strlen(selectSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);

    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        int64_t actId = GmeSqlColumnInt64(stmt, 0);
        ASSERT_EQ(i, actId);
        string exptName = "n" + to_string(i);
        const char *actName = GmeSqlColumnText(stmt, 1);
        ASSERT_STREQ(exptName.c_str(), actName);
        int64_t actRank = GmeSqlColumnInt64(stmt, 2);
        ASSERT_EQ(i, actRank);
    }
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    const char *dropTableSql = "drop table handleTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Prepare + Bind blob Handle
HWTEST_F(StEmbSqlInsert, TestInsertBlobDataHandle_003, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql =
        "create table t1(id int unique, name text, weight real, home blob, repr floatvector(16));";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *insertSql = "INSERT INTO t1 VALUES(1, 'Bella', 3.0, ?, '[30,12,12,25,5,2,10,8,6,18,24,19,5,3,0,4]');";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);

    ret = GmeSqlBindBlob(stmt, 1, "?!2@$1.235454sdasd84\0", 21u, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    const char *selectTableSql = "select home FROM t1 WHERE id = 1;";
    stmt = NULL;
    ret = GmeSqlPrepare(conn, selectTableSql, strlen(selectTableSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    GmeDbDataTypeE type = GmeSqlColumnType(stmt, 0);
    ASSERT_EQ(type, GME_DB_DATATYPE_BLOB);
    GmeDbValueT val = GmeSqlColumnValue(stmt, 0);
    ASSERT_STREQ("?!2@$1.235454sdasd84\0", static_cast<const char *>(val.value.strAddr));

    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: insert blob data + execute
HWTEST_F(StEmbSqlInsert, TestInsertBlobData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table t1(id int unique, name text, weight real, home blob);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "INSERT INTO t1 VALUES(1, 'Bella', 3.0, '?!2@$1.235454sdasd84\\0');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // prepare + check
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *selectTableSql = "select home FROM t1 WHERE id = 1;";
    ret = GmeSqlPrepare(conn, selectTableSql, strlen(selectTableSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmeDbDataTypeE type = GmeSqlColumnType(stmt, 0);
    ASSERT_EQ(type, GME_DB_DATATYPE_BLOB);
    GmeDbValueT val = GmeSqlColumnValue(stmt, 0);
    ASSERT_STREQ("?!2@$1.235454sdasd84\\0", static_cast<const char *>(val.value.strAddr));
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // execute + check
    const char *selectSql = "select home FROM t1 WHERE id = 1;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"home"}, {{"?!2@$1.235454sdasd84\\0"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: insert blob data + fixed data
HWTEST_F(StEmbSqlInsert, TestInsertBlobData_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table t1(id int unique, name text, weight real, home blob);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "INSERT INTO t1 VALUES(1, 'Bella', 3.0, 100);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // prepare + check
    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *selectTableSql = "select home FROM t1 WHERE id = 1;";
    ret = GmeSqlPrepare(conn, selectTableSql, strlen(selectTableSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmeDbDataTypeE type = GmeSqlColumnType(stmt, 0);
    ASSERT_EQ(type, GME_DB_DATATYPE_BLOB);
    GmeDbValueT val = GmeSqlColumnValue(stmt, 0);
    ASSERT_STREQ("100", static_cast<const char *>(val.value.strAddr));
    ret = GmeSqlReset(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // execute + check
    const char *selectSql = "select home FROM t1 WHERE id = 1;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"home"}, {{"100"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: default value
HWTEST_F(StEmbSqlInsert, TestInsertDefaultData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table defaultTable(id int primary key default 1, name text unique default "
                                 "'nx', rank int default 1);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase1: default values
    const char *insertSql = "insert into defaultTable default values;";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // Check
    const char *selectSql = "select * from defaultTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}, {{"1", "nx", "1"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table defaultTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: one default value
    createTableSql = "create table defaultTable(id int primary key default 1, name text unique default 'nx', "
                     "rank int default 1);";
    ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    insertSql = "insert into defaultTable(name, id) values('n1', 1), ('n2', 2), ('n3', 3), ('n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    selectSql = "select * from defaultTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult2{
        {"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "1"}, {"3", "n3", "1"}, {"4", "n4", "1"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult2);
    StEmbSqlClearActQryResultSet(NULL);

    dropTableSql = "drop table defaultTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: default + Prepare + Bind Handle
HWTEST_F(StEmbSqlInsert, TestInsertDefaultDataHandle_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase:
    const char *createTableSql = "create table handleTable(id int, name text default 'nx', rank int default 1);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const uint32_t exptInsertNum = 9;

    // TestCase1: text default
    const char *insertSql = "insert into handleTable(id, rank) values(?, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        ret = GmeSqlBindInt64(stmt, 1, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlBindInt64(stmt, 2, i);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t insertNum = GmeSqlChanges(conn);
        ASSERT_EQ(1u, insertNum);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase2: integer default
    string nameStr;
    insertSql = "insert into handleTable(id, name) values(?, ?);";
    stmt = NULL;
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(strcmp(unused, ""), 0);
    for (uint32_t i = exptInsertNum + 1; i <= exptInsertNum + exptInsertNum; ++i) {
        ret = GmeSqlBindInt64(stmt, 1, i);
        ASSERT_EQ(GMERR_OK, ret);
        nameStr = "n";
        string idxStr = to_string(i);
        nameStr += idxStr;
        ret = GmeSqlBindText(stmt, 2, static_cast<const char *>(nameStr.c_str()), strlen(nameStr.c_str()), NULL);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmeSqlStep(stmt);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t insertNum = GmeSqlChanges(conn);
        ASSERT_EQ(1u, insertNum);
        ret = GmeSqlReset(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql = "select * from handleTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}, {}};
    // Construct1:
    for (uint32_t i = 1; i <= exptInsertNum; ++i) {
        string idxStr = to_string(i);
        nameStr = "nx";
        exptQryResult.colValues.push_back({idxStr, nameStr, idxStr});
    }
    // Construct2:
    for (uint32_t i = exptInsertNum + 1; i <= exptInsertNum + exptInsertNum; ++i) {
        string idxStr = to_string(i);
        nameStr = "n";
        nameStr += idxStr;
        string rankStr = "1";
        exptQryResult.colValues.push_back({idxStr, nameStr, rankStr});
    }
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table handleTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: default null value
HWTEST_F(StEmbSqlInsert, TestInsertNullData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table nullTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase: default null value
    const char *insertSql = "insert into nullTable(name, id) values('n1', 1), ('n2', 2), ('n3', 3), ('n4', 4);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(4u, insertNum);

    // Check
    const char *selectSql = "select * from nullTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"1", "n1", "null"}, {"2", "n2", "null"}, {"3", "n3", "null"}, {"4", "n4", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table nullTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: default null value + not null constaint
HWTEST_F(StEmbSqlInsert, TestInsertNullData_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table nullTable(id int primary key, name text unique, rank int not null);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into nullTable(name, id, rank) values('n1', 1, 1), ('n2', 2, 2);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // TestCase: default null value
    insertSql = "insert into nullTable(id, name) values(3, 'n3'), (4, 'n4');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // Check
    const char *selectSql = "select * from nullTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n2", "2"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table nullTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: primary or unique + null value, 对标sqlite: null = null 为假, null is null 为真
HWTEST_F(StEmbSqlInsert, TestInsertNullData_003, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table nullTable(id int primary key, name text unique, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into nullTable(name, id, rank) values('n1', 1, 1), ('n2', 2, 2);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // TestCase: null
    insertSql = "insert into nullTable values(null, null, null), (null, null, null);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // Check
    const char *selectSql = "select * from nullTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"},
        {{"1", "n1", "1"}, {"2", "n2", "2"}, {"null", "null", "null"}, {"null", "null", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table nullTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: auto inc
HWTEST_F(StEmbSqlInsert, TestInsertAutoIncData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table autoIncTable(id int primary key autoincrement, name text, rank int);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase:
    // 插入 自增列为0的数据
    const char *insertSql = "insert into autoIncTable(id, name, rank) values(0, 'n0', 0);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);
    // 重复插入 预期失败
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // 首先 插入第一条数据 自增列赋值为1
    insertSql = "insert into autoIncTable(id, name, rank) values(1, 'n1', 1);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // 然后 插入第二三条数据 不给自增列赋值
    insertSql = "insert into autoIncTable(name, rank) values('n2', 2),('n3', 3);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // 再 插入第四条数据 给自增列赋值为3 预期失败
    insertSql = "insert into autoIncTable values(3, 'n3', 3);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(0u, insertNum);

    // 再 插入第四条数据 给自增列赋值为5 预期成功
    insertSql = "insert into autoIncTable values(5, 'n5', 5);";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // 最后，插入第五条、第六条数据，不给自增长列赋值，那么自增长列的值应该为0、1、2、3、5、6、7
    insertSql = "insert into autoIncTable(name) values('n6'),('n7');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(2u, insertNum);

    // Check
    const char *selectSql = "select * from autoIncTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "rank"}, {{"0", "n0", "0"}, {"1", "n1", "1"}, {"2", "n2", "2"}, {"3", "n3", "3"},
                                    {"5", "n5", "5"}, {"6", "n6", "null"}, {"7", "n7", "null"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table autoIncTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: auto inc + default values
HWTEST_F(StEmbSqlInsert, TestInsertAutoIncData_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // TestCase: 主键自增列 设置默认值 但不影响自增
    const char *createTableSql =
        "create table autoIncTable(id int primary key autoincrement default 10, name text default "
        "'n1', rank int default 1);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // TestCase:
    // 首先 插入第一条数据 自增列为1
    const char *insertSql = "insert into autoIncTable default values;";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // 然后 插入第二条数据 自增列为2 (暂不支持 返错 GMERR_PRIMARY_KEY_VIOLATION)
    insertSql = "insert into autoIncTable default values;";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    // Check
    const char *selectSql = "select * from autoIncTable;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name", "rank"}, {{"1", "n1", "1"}, {"2", "n1", "1"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table autoIncTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// TestCase: Insert with index
HWTEST_F(StEmbSqlInsert, TestInsertDataWithIndex_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "CREATE TABLE dql_tb (id INT primary key, name TEXT, age INT, num INT, address TEXT);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *createIndexSql = "create index id_index on dql_tb(age, num);";
    ret = GmeSqlExecute(conn, createIndexSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into dql_tb(id, name, age, num, address) values(1,'Tina',3, 4,'Beijing');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    const char *updateSql = "update dql_tb set num=5000 where age = 3;";
    ret = GmeSqlExecute(conn, updateSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t updateNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, updateNum);

    // Check
    const char *selectSql = "select * from dql_tb;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "age", "num", "address"}, {{"1", "Tina", "3", "5000", "Beijing"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table dql_tb;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlInsert, TestMutiplePrepare4UnusedStmt, TestSize.Level0)
{
    const char *sql = "CREATE TABLE employee (id INT, name TEXT, age INT, remark TEXT);"
                      "CREATE TABLE employee_pk (id INT PRIMARY KEY, name TEXT, age INT, remark TEXT);"
                      "CREATE TABLE employee_default (id INT, name TEXT, age INT DEFAULT 18, remark TEXT DEFAULT "
                      "'default remark');"
                      "CREATE TABLE employee_not_null (id INT NOT NULL, name TEXT NOT NULL, age INT, remark TEXT);"
                      "CREATE TABLE employee_default_not_null (id INT, name TEXT, age INT DEFAULT 18, remark TEXT "
                      "DEFAULT 'default remark' NOT NULL);";
    GmeConnT *conn = StEmbSqlGetConn();
    StEmbSqlPrepareUnusedStmt(conn, sql);

    sql = "INSERT INTO employee (id, name, age, remark) VALUES(1, 'zhangsan001', 18, 'xxxx');"
          "INSERT INTO employee (name, age, remark) VALUES('zhangsan002', 28, 'yyyy');"
          "INSERT INTO employee (id, name, remark) VALUES(3, 'zhangsan003', 'zzzz');"
          "INSERT INTO employee (id) VALUES(2);"
          "INSERT INTO employee (id) VALUES(4);"
          "INSERT INTO employee (id, name, age, remark) VALUES(4, 'zhangsan005', 18, 'xxxx');"
          "INSERT INTO employee (id, name, age, remark) VALUES(6, 'zhangsan006', 18, 'xxxx');";
    StEmbSqlPrepareUnusedStmt(conn, sql);

    sql = "DROP TABLE employee;"
          "DROP TABLE employee_pk;"
          "DROP TABLE employee_default;"
          "DROP TABLE employee_not_null;";
    StEmbSqlPrepareUnusedStmt(conn, sql);
}

// 看护带 ? 的 sql 误用Exec执行
HWTEST_F(StEmbSqlInsert, TestInsertMisusedExec, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GmeSqlExecute(conn, "create table misusedTbl(a int, b text);", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmeSqlExecute(conn, "insert into misusedTbl values(?, ?);", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);

    ret = GmeSqlExecute(conn, "drop table misusedTbl;", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 测试GRD_SqlBindBlob接口
// 参数绑定的idx为0，报错GMERR_INVALID_PARAMETER_VALUE
// 参数绑定的idx为prepare中不存在的异常值，报错GMERR_INVALID_PARAMETER_VALUE
HWTEST_F(StEmbSqlInsert, TestInsertBlob, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建表
    Status ret = GmeSqlExecute(conn, "CREATE TABLE t1(id int unique, data blob);", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 构造插入数据
    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *insertSql = "INSERT INTO t1 VALUES(1, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定BLOB，但是index传入异常数字 0
    ret = GmeSqlBindBlob(stmt, 0, "?!2@$1.235454sdasd84\0", 21u, NULL);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 绑定BLOB，但是index传入异常数字 2
    ret = GmeSqlBindBlob(stmt, 2, "?!2@$1.235454sdasd84\0", 21u, NULL);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 绑定BLOB，但是index传入异常数字 100
    ret = GmeSqlBindBlob(stmt, 100, "?!2@$1.235454sdasd84\0", 21u, NULL);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 绑定BLOB，但是index传入异常数字 DB_MAX_UINT32
    ret = GmeSqlBindBlob(stmt, DB_MAX_UINT32, "?!2@$1.235454sdasd84\0", 21u, NULL);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除表
    ret = GmeSqlExecute(conn, "DROP TABLE t1;", NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlInsert, TestUpsertData_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table t_upsert_01(id int primary key, age int unique, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    InsertTestT inputSql[] = {
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 17), (2, 18), (3, 19), (4, 20), (5, 21);", GMERR_OK, 5},
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 54321) ON CONFLICT DO NOTHING;", GMERR_OK, 0},
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 50) ON CONFLICT DO "
         "UPDATE SET age = 100, name = 'primary';",
            GMERR_OK, 1},  // testcase for primary key conflict
        {"INSERT INTO t_upsert_01(id, age) VALUES(123, 18) ON CONFLICT DO "
         "UPDATE SET age = 101, name = 'unique';",
            GMERR_OK, 1},  // testcase for unique key conflict
        {"INSERT INTO t_upsert_01(id, age, name) VALUES(6, 105, 'success'), (3, 19, 'conflict') ON CONFLICT DO "
         "UPDATE SET age = 102, name = 'multiple value';",
            GMERR_OK, 2},  // testcase for one insert success one conflict
        {"INSERT INTO t_upsert_01(id, age) VALUES(4, 20), (5, 21) ON CONFLICT DO "
         "UPDATE SET name = 'update all';",
            GMERR_OK, 2},  // testcase for all insert value conflict
        {"INSERT INTO t_upsert_01(id, age) VALUES(6, 105) ON CONFLICT DO UPDATE SET age = 100;", GMERR_UNIQUE_VIOLATION,
            0},  // testcase for update conflict, return err, and rollback
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 54321) ON CONFLICT DO UPDATE SET age = length(1) + 1;", GMERR_OK,
            1},
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 54321) ON CONFLICT DO UPDATE SET age = excluded.age;",
            GMERR_FEATURE_NOT_SUPPORTED, 0},
        {"INSERT INTO t_upsert_01(id, age) VALUES(1, 54321) ON CONFLICT DO UPDATE SET age = errorTableName.age;",
            GMERR_FEATURE_NOT_SUPPORTED, 0},
    };

    for (uint32_t i = 0; i < sizeof(inputSql) / sizeof(InsertTestT); ++i) {
        ret = GmeSqlExecute(conn, inputSql[i].sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, inputSql[i].expectRet);
        uint32_t changeNum = GmeSqlChanges(conn);
        ASSERT_EQ(inputSql[i].expectChangeNum, changeNum);
    }

    // Check
    const char *selectSql = "select * from t_upsert_01;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "age", "name"}, {{"1", "2", "primary"}, {"2", "101", "unique"}, {"3", "102", "multiple value"},
                                   {"4", "20", "update all"}, {"5", "21", "update all"}, {"6", "105", "success"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table t_upsert_01;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlInsert, TestUpsertData_002, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table t_upsert_02(id int primary key, age int unique, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    InsertTestT inputSql[] = {
        {"INSERT INTO t_upsert_02(id, age) VALUES(11, 100), (12, 101), (13, 102), (14, 103), (15, 104), (16, 105),"
         "(17, 106), (18, 107), (19, 108), (20, 109), (21, 110), (22, 111), (23, 112), (24, 113), (25, 114),"
         "(26, 115), (27, 116), (28, 117), (29, 118), (30, 119), (31, 120);",
            GMERR_OK, 21},
        {"INSERT INTO t_upsert_02(id, age) VALUES(11, 100), (12, 101), (13, 102), (14, 103), (15, 104), (16, 105),"
         "(17, 106), (18, 107), (19, 108), (20, 109), (21, 110), (22, 111), (23, 112), (24, 113), (25, 114),"
         "(26, 115), (27, 116), (28, 117), (29, 118), (30, 119), (31, 120)"
         "ON CONFLICT DO UPDATE SET name = 'success';",
            GMERR_OK, 21},
    };

    for (uint32_t i = 0; i < sizeof(inputSql) / sizeof(InsertTestT); ++i) {
        ret = GmeSqlExecute(conn, inputSql[i].sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, inputSql[i].expectRet);
        uint32_t changeNum = GmeSqlChanges(conn);
        ASSERT_EQ(inputSql[i].expectChangeNum, changeNum);
    }

    // Check
    const char *selectSql = "select * from t_upsert_02 where id = 31;";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "age", "name"}, {{"31", "120", "success"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table t_upsert_02;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlInsert, TestUpsertData_003, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table t_upsert_03(id int primary key, age int unique, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // upsert暂不支持where子句
    const char *insertSql = "INSERT INTO t_upsert_03 VALUES(1, 18, 'none') ON CONFLICT (id)"
                            "DO UPDATE SET remark = 'new remark' WHERE id>0;";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    uint32_t changeNum = GmeSqlChanges(conn);
    ASSERT_EQ(0, changeNum);

    // 非法的语句，更新数据的子句缺少等号
    insertSql = "insert into t_upsert_03(id, name, age, remark) VALUES(1, 'zhangsan1', 18, 'none') "
                "ON CONFLICT (id) DO UPDATE SET remark 'new remark';";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_SYNTAX_ERROR, ret);
    changeNum = GmeSqlChanges(conn);
    ASSERT_EQ(0, changeNum);

    // 合法的语句
    const char *dropTableSql = "drop table t_upsert_03;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 多个索引，upsert语句可能因为部分索引未删除干净，导致upsert连续执行失败
HWTEST_F(StEmbSqlInsert, TestUpsertData_004, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table t_upsert_04(id int, age int, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *createIdxSql = "create index idx1 on t_upsert_04(id);create index idx2 on t_upsert_04(age);create "
                               "unique index idx3 on t_upsert_04(name);";
    ret = GmeSqlExecute(conn, createIdxSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    InsertTestT inputSql[] = {
        {"INSERT INTO t_upsert_04(id, age, name) VALUES(11, 100, 'tom0'), (12, 101, 'tom1'), (13, 102, 'tom2'), (14, "
         "103, 'tom3'), (15, 104, 'tom4'), (16, 105, 'tom5'), (17, 106, 'tom6'), (18, 107, 'tom7'), (19, 108, 'tom8'), "
         "(20, 109, 'tom9');",
            GMERR_OK, 10},
        {"INSERT INTO t_upsert_04(id, age, name) VALUES(11, 100, 'tom0'), (12, 101, 'tom1'), (13, 102, 'tom2'), (14, "
         "103, 'tom3'), (15, 104, 'tom4'), (16, 105, 'tom5'), (17, 106, 'tom6'), (18, 107, 'tom7'), (19, 108, 'tom8'), "
         "(20, 109, 'tom9') ON CONFLICT DO NOTHING;",
            GMERR_OK, 0},
    };

    for (uint32_t i = 0; i < sizeof(inputSql) / sizeof(InsertTestT); ++i) {
        ret = GmeSqlExecute(conn, inputSql[i].sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, inputSql[i].expectRet);
        uint32_t changeNum = GmeSqlChanges(conn);
        ASSERT_EQ(inputSql[i].expectChangeNum, changeNum);
    }

    const uint32_t execTime = 5;
    for (uint32_t i = 0; i < execTime; ++i) {
        ret = GmeSqlExecute(conn, inputSql[1].sql, NULL, NULL, NULL);
        EXPECT_EQ(ret, GMERR_OK);
    }

    const char *dropTableSql = "drop table t_upsert_04;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

HWTEST_F(StEmbSqlInsert, TestUpsertData_005, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const InsertTestT cases[] = {
        {.sql = "create table t_upsert_05(id int, age int, name text);"},
        {.sql = "create unique index idx1 on t_upsert_05(id);"},
        {.sql = "create unique index idx2 on t_upsert_05(id, age);"},
        {.sql = "create unique index idx3 on t_upsert_05(name);"},
        {.sql = "INSERT INTO t_upsert_05(id, age, name) VALUES(1, 100, 'tom0');"},
        // CONFLICT项没有唯一性约束, 报错.
        {
            .sql =
                "INSERT INTO t_upsert_05(id, age, name) VALUES(1, 100, 'tom0') ON CONFLICT (id, name) do update set id "
                "= 100;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql =
                "INSERT INTO t_upsert_05(id, age, name) VALUES(1, 100, 'tom0') ON CONFLICT (id, age) do update set id "
                "= 100;",
            .expectRet = GMERR_OK,
        },
        {
            .sql = "INSERT INTO t_upsert_05(id, age, name) VALUES(1, 100, 'tom0');",
            .expectRet = GMERR_UNIQUE_VIOLATION,
        },
        // name 列唯一性冲突, 报错.
        {
            .sql =
                "INSERT INTO t_upsert_05(id, age, name) VALUES(1, 100, 'tom0') ON CONFLICT (id, age) do update set id "
                "= 100;",
            .expectRet = GMERR_UNIQUE_VIOLATION,
        },
        {
            .sql = "select * from t_upsert_05;",
            .expectRet = GMERR_OK,
        },
        {.sql = "drop table t_upsert_05;"},
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql, nullptr, nullptr, nullptr);
        ASSERT_EQ(testCase.expectRet, ret);
    }
}

HWTEST_F(StEmbSqlInsert, TestUpsertData_006, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    Status ret = GMERR_OK;

    const InsertTestT cases[] = {
        {.sql = "create table t_upsert_06(id int, age int, name text);"},
        {.sql = "create unique index idx1 on t_upsert_06(id + age);"},
        {.sql = "INSERT INTO t_upsert_06(id, age, name) VALUES(1, 100, 'tom0');"},
        {
            .sql = "INSERT INTO t_upsert_06(id, age, name) VALUES(2, 99, 'tom0') ON CONFLICT (id - age) do update "
                   "set id = 100;",
            .expectRet = GMERR_SYNTAX_ERROR,
        },
        {
            .sql = "INSERT INTO t_upsert_06(id, age, name) VALUES(2, 99, 'tom0') ON CONFLICT (id + age) do update "
                   "set id = 100;",
            .expectRet = GMERR_OK,
        },
        {
            .sql = "INSERT INTO t_upsert_06(id, age, name) VALUES(2, 99, 'tom0') ON CONFLICT (id + age1) do update "
                   "set id = 100;",
            .expectRet = GMERR_UNDEFINE_COLUMN,
        },
        {
            .sql = "select * from t_upsert_06;",
            .expectRet = GMERR_OK,
        },
        {.sql = "drop table t_upsert_06;"},
    };
    for (const auto &testCase : cases) {
        ret = GmeSqlExecute(conn, testCase.sql, nullptr, nullptr, nullptr);
        ASSERT_EQ(testCase.expectRet, ret);
    }
}

HWTEST_F(StEmbSqlInsert, TestProcessDiffDataType, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table tb_temp (id integer primary key, name varchar);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "insert into tb_temp values (8.8888, 'zhangsan');";
    ret = GmeSqlExecute(conn, insertSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 匹配成功，预期会更新name = lisi
    const char *updateSql = "update tb_temp set name = 'lisi' where id < 8.8888;";
    ret = GmeSqlExecute(conn, updateSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 匹配失败，数据精度不匹配
    const char *deleteSql = "delete from tb_temp where id = 8.8888;";
    ret = GmeSqlExecute(conn, deleteSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // Check
    const char *selectSql = "select * from tb_temp where 'name' glob '*name';";
    ret = GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "name"}, {{"8", "lisi"}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);

    const char *dropTableSql = "drop table tb_temp;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
}

// 一次执行中包含的语句长度等于最大值1MB.
// 若运行 1MB长度sql耗时太久, 当前看护 1024b.
// 需要验证1MB的运行结果, 可将修改为 sqlLen = 1024 * 1024.
// 当前prepare 模式 执行1MB长度的insert语句耗时约为50s.
HWTEST_F(StEmbSqlInsert, TestInsertMaxStr_001, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table MaxStrTab1 (id integer, name text, age int, remark text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *tmpSql = "INSERT INTO MaxStrTab1 (id, name, age, remark) VALUES(1, 'zhangsan001', 18, 'xxxx');";
    uint32_t sqlLen = 1024;
    char *sql = static_cast<char *>(malloc(sqlLen));
    EXPECT_EQ((sql != nullptr), true);
    (void)memset_s(sql, sqlLen, 0, sqlLen);
    int32_t rowNum = 0;
    for (;;) {
        if (sqlLen - strlen(sql) <= strlen(tmpSql)) {
            break;
        }
        (void)strcat_s(sql, sqlLen, tmpSql);
        rowNum++;
    }

    GmeSqlStmtT *stmt = NULL;
    const char *unused = NULL;
    const char *curSqlStr = sql;
    uint64_t startTime = DbGetNsec();
    for (uint32_t i = 0; i < rowNum; i++) {
        uint32_t len = (uint32_t)(strlen(curSqlStr) + 1);
        stmt = NULL;
        ret = GmeSqlPrepare(conn, curSqlStr, len, &stmt, &unused);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlFinalize(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        curSqlStr = unused;
    }
    uint64_t finishTime = DbGetNsec();
    free(sql);

    GmeSqlStmtT *stmt1 = NULL;
    const char *unused1 = nullptr;
    const char *selectSql = "select * from MaxStrTab1;";
    uint32_t len1 = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, len1, &stmt1, &unused1);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t qryCount = 0;
    while (ret = GmeSqlStep(stmt1), ret == GMERR_OK) {
        // 校验列的个数
        uint32_t colCount = GmeSqlColumnCount(stmt1);
        EXPECT_EQ(colCount, 4u);
        // 校验第一列的名称
        const char *column1 = GmeSqlColumnName(stmt1, 0);
        EXPECT_EQ(strcmp(column1, "id"), 0);
        // 校验第一列的数据
        double aValue = GmeSqlColumnDouble(stmt1, 0);
        EXPECT_EQ(aValue, 1);
        qryCount++;
    }
    EXPECT_EQ(qryCount, rowNum);

    const char *dropTableSql = "drop table MaxStrTab1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 执行时间,单位 ms.
    double executeTime = (finishTime - startTime) / (double)NSECONDS_IN_MSECOND;
    double time = 1000.0;  // 拦截性能劣化的最大时间值, 单位毫秒
    EXPECT_LE(executeTime, time);
}

// 测试插入字段内容为单引号和双引号的场景，仅Prepare模式支持
HWTEST_F(StEmbSqlInsert, TestInsertDataWithSemicolon, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql = "create table t1 (id integer, name text, age int, remark text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *insertSql = "INSERT INTO t1 VALUES(?, ?, ?, ?);";
    uint32_t insertSqlLen = strlen(insertSql);
    GmeSqlStmtT *stmt = NULL;
    ret = GmeSqlPrepare(conn, insertSql, insertSqlLen, &stmt, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    const uint32_t rowNum = 2;
    for (uint32_t i = 0; i < rowNum; i++) {
        uint32_t bindId = 1;
        ret = GmeSqlBindInt64(stmt, bindId++, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindText(stmt, bindId++, "'", strlen("'"), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindInt64(stmt, bindId++, rowNum - i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlBindText(stmt, bindId++, "\"", strlen("\""), NULL);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlStep(stmt);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmeSqlReset(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    const char *selectSql = "select * from t1 where name = ?;";
    uint32_t selectSqlLen = (uint32_t)(strlen(selectSql) + 1);
    ret = GmeSqlPrepare(conn, selectSql, selectSqlLen, &stmt, NULL);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmeSqlBindText(stmt, 1, "'", strlen("'"), NULL);
    EXPECT_EQ(ret, GMERR_OK);
    StEmbSqlGetActQryResultSetEx(NULL, stmt);
    StEmbSqlQryResultSetExtendT exptQryResult{
        {"id", "name", "age", "remark"}, {{"0", "'", "2", "\""}, {"1", "'", "1", "\""}}};
    StEmbSqlCheckActQryResultSet(NULL, exptQryResult);
    StEmbSqlClearActQryResultSet(NULL);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

const int FIRST_PART = 119;
const int SECOND_PART = 63;
const int OVER_LIMIT_PART = 64;
const int CYCLE_CONSTRUCT_NUM = 8;
HWTEST_F(StEmbSqlInsert, checkVarPropeNumber, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *createTableSql =
        "create table t1 (id integer primary key, name1 text, name2 text, name3 text, name4 text, name5 "
        "text, name6 text, name7 text, name8 text, name9 text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *createIndexSql =
        "CREATE INDEX idx1 ON t1 (name1, name2, name3, name4, name5, name6, name7, name8, name9);";
    ret = GmeSqlExecute(conn, createIndexSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    string insertString1(FIRST_PART, 'a');
    string insertString2(SECOND_PART, 'b');
    string insertString3(OVER_LIMIT_PART, 'b');

    string insertSql1 = "INSERT INTO t1 VALUES(1, ";
    string insertSql2 = "INSERT INTO t1 VALUES(2, ";

    for (uint32_t i = 0; i < CYCLE_CONSTRUCT_NUM; i++) {
        insertSql1 += "'";
        insertSql1 += insertString1;
        insertSql1 += "', ";

        insertSql2 += "'";
        insertSql2 += insertString1;
        insertSql2 += "', ";
    }
    insertSql1 += "'";
    insertSql1 += insertString2;
    insertSql1 += "');";
    insertSql2 += "'";
    insertSql2 += insertString3;
    insertSql2 += "');";

    ret = GmeSqlExecute(conn, insertSql1.data(), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertNum = GmeSqlChanges(conn);
    ASSERT_EQ(1u, insertNum);

    ret = GmeSqlExecute(conn, insertSql1.data(), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = GmeSqlExecute(conn, insertSql2.data(), NULL, NULL, NULL);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    const char *dropTableSql = "drop table t1;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// TEXT数据类型上限校验.(TEXT最大64k, 包含结束符'\0')
HWTEST_F(StEmbSqlInsert, TestTextBindLimit, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table textTable(id int, name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *insertSql = "insert into textTable values(1, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);

    // 包含结束符 64k
    uint32_t len1 = 64 * 1024 * sizeof(char);
    char *textData1 = static_cast<char *>(malloc(len1));
    (void)memset_s(textData1, len1, 'a', len1);
    textData1[len1 - 1] = '\0';
    ret = GmeSqlBindText(stmt, 1, textData1, strlen(textData1), NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    free(textData1);

    // 包含结束符 64k + 1 , 超限.
    uint32_t len2 = 64 * 1024 * sizeof(char) + 1;
    char *textData2 = static_cast<char *>(malloc(len2));
    (void)memset_s(textData2, len2, 'a', len2);
    textData2[len2 - 1] = '\0';
    ret = GmeSqlBindText(stmt, 1, textData2, strlen(textData2), NULL);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    free(textData2);

    const char *dropTableSql = "drop table textTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// blob数据类型上限校验.(blob最大64k )
HWTEST_F(StEmbSqlInsert, TestBlobBindLimit, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table blobTable(id int, name blob);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    const char *unused = NULL;
    GmeSqlStmtT *stmt = NULL;
    const char *insertSql = "insert into blobTable values(1, ?);";
    ret = GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused);
    ASSERT_EQ(GMERR_OK, ret);

    // strlen(blobData1) = 64K -1
    uint32_t len1 = 64 * 1024 * sizeof(char);
    char *blobData1 = static_cast<char *>(malloc(len1));
    (void)memset_s(blobData1, len1, 'a', len1);
    blobData1[len1 - 1] = '\0';
    ret = GmeSqlBindBlob(stmt, 1, blobData1, strlen(blobData1), NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    free(blobData1);

    // strlen(blobData1) = 64K, 界限值
    uint32_t len2 = 64 * 1024 * sizeof(char) + 1;
    char *blobData2 = static_cast<char *>(malloc(len2));
    (void)memset_s(blobData2, len2, 'a', len2);
    blobData2[len2 - 1] = '\0';
    ret = GmeSqlBindBlob(stmt, 1, blobData2, strlen(blobData2), NULL);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlStep(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmeSqlReset(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    free(blobData2);

    // strlen(blobData1) = 64K + 1 ,超限报错
    uint32_t len3 = 64 * 1024 * sizeof(char) + 2;
    char *blobData3 = static_cast<char *>(malloc(len3));
    (void)memset_s(blobData3, len3, 'a', len3);
    blobData3[len3 - 1] = '\0';
    ret = GmeSqlBindBlob(stmt, 1, blobData3, strlen(blobData3), NULL);
    ASSERT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = GmeSqlFinalize(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    free(blobData3);

    const char *dropTableSql = "drop table blobTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// string数据类型上限校验
HWTEST_F(StEmbSqlInsert, TestStringLimit, TestSize.Level0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *createTableSql = "create table overLimitTable(name text);";
    Status ret = GmeSqlExecute(conn, createTableSql, NULL, NULL, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    string str1 = string(64 * 1024, 'a');
    string str2 = "INSERT INTO overLimitTable VALUES('" + str1 + "');";
    ret = GmeSqlExecute(conn, str2.c_str(), NULL, NULL, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    const char *dropTableSql = "drop table overLimitTable;";
    ret = GmeSqlExecute(conn, dropTableSql, NULL, NULL, NULL);
    EXPECT_EQ(GMERR_OK, ret);
}
