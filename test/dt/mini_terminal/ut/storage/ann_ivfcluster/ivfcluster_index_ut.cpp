/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Implementation of ivfcluster index ut
 * Author: lideshi
 * Create: 2025-03-25
 */

#include <algorithm>
#include <climits>
#include <random>
#include "gtest/gtest.h"

#include "common_init.h"
#include "dm_data_index.h"
#include "dm_meta_basic_in.h"
#include "db_dynmem_algo.h"
#include "db_mem_context.h"
#include "ivfcluster_index_ut_common.h"
#include "se_ann_utils.h"
#include "se_heap_base.h"
#include "se_index_inner.h"
#include "se_instance.h"
#include "se_ivfcluster_index.h"
#include "se_ivfcluster_build.h"
#include "se_ivfcluster_utils.h"
#include "storage_ann_common_ut.h"
#include "storage_common.h"
#include "stub.h"

using namespace testing::ext;

extern "C" {}

constexpr uint16_t DIM_TEST1 = 2;
constexpr uint16_t PROBE_TEST1 = 3;
constexpr uint16_t TOPN_TEST1 = 2;
constexpr uint16_t COUNT_TEST2 = 1000;
constexpr uint16_t DIM_TEST2 = 128;
constexpr uint16_t DIM_TEST3 = 7;
constexpr uint16_t PROBE_TEST2 = 10;
constexpr uint16_t TOPN_TEST2 = 5;
constexpr uint16_t PROBE_TEST3 = 20;
constexpr uint16_t TOPK_TEST = 10;
constexpr uint16_t SIFT_SIZE = 10000;
constexpr uint16_t DEFAULT_P_QUEUESIZE = 20;
constexpr float ALPHA_TEST = 1.2;
constexpr uint32_t KEY_LEN = 256 * sizeof(float) + 1;
constexpr uint32_t KEY_LEN_DIM_255 = 255 * sizeof(float) + 1;

static void TransferVector2Float(vector<float> &floatVector, const vector<vector<float>> &vectors)
{
    for (const auto &innerVec : vectors) {
        floatVector.insert(floatVector.end(), innerVec.begin(), innerVec.end());
    }
}

static void StartIvfClusterBuild(IndexCtxT *idxCtx, bool sample, uint16_t dim, uint32_t count, uint64_t *dataId,
    DmVecDistTypeE distType, float *vectors)
{
    VecBuildStateParaT buildStatePara = {
        .count = count, .distType = distType, .dataId = dataId, .vectors = vectors, .sample = sample, .dim = dim};
    EXPECT_EQ(GMERR_OK, IvfClusterIndexBuild(idxCtx, &buildStatePara));
}

class IvfClusterIndexUt : public StorageCommon, public testing::Test {
protected:
    virtual void SetUp()
    {
        ConstructSeInsAndSeRun(nullptr);
    }
    virtual void TearDown()
    {
        ClearAllStub();
        DestroySeIns();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase(){};

    static void TearDownTestCase(){};
};

HWTEST_F(IvfClusterIndexUt, UtIvfClusterCreateDrop001, TestSize.Level0)
{
    uint16_t dim = 0;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;
    ShmemPtrT idxShmAddr = {0};
    Status ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    dim = 1025;
    metaCfg.vectorDim = dim;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    dim = 1;
    metaCfg.vectorDim = dim;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    dim = 1024;
    metaCfg.vectorDim = dim;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    dim = 128;
    metaCfg.vectorDim = dim;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    dim = 256;
    metaCfg.vectorDim = dim;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxDrop(StorageCommon::seRunCtx, IVFCLUSTER_INDEX, idxShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    RedoLogBegin(StorageCommon::redoCtx);
    IdxCommitDrop(StorageCommon::seRunCtx, IVFCLUSTER_INDEX, idxShmAddr);
    RedoLogEnd(StorageCommon::redoCtx, true);

    dim = 256;
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_L2;
    indexCfg.extendParam = (void *)&metaCfg;
    ret = IdxCreate(StorageCommon::seRunCtx, indexCfg, &idxShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = IdxDrop(StorageCommon::seRunCtx, IVFCLUSTER_INDEX, idxShmAddr);
    EXPECT_EQ(GMERR_OK, ret);

    RedoLogBegin(StorageCommon::redoCtx);
    IdxCommitDrop(StorageCommon::seRunCtx, IVFCLUSTER_INDEX, idxShmAddr);
    RedoLogEnd(StorageCommon::redoCtx, true);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterOpenClose002, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);
    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterInsert003, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 256;
    const uint32_t numSamples = 100;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterInsertmultiNum004, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 256;
    const uint32_t numSamples = 1000;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterInsertWrongDim005, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 256;
    const uint32_t numSamples = 1;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN_DIM_255] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN_DIM_255 - 1);
        IndexKeyT idxKey = {
            .keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN_DIM_255, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterScan006, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 256;
    const uint32_t numSamples = 1000;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    // 校验扫描维度错误
    VecScanParaT vectorScanPara = {0};
    vectorScanPara.dim = 255;
    vectorScanPara.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara};
    IndexScanItrT iter = nullptr;
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, IdxBeginScan(idxCtx, cfg, &iter));

    // 校验扫描类型错误
    vectorScanPara.dim = 256;
    vectorScanPara.clusterType = (IndexClusterTypeE)DB_INVALID_UINT32;
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, IdxBeginScan(idxCtx, cfg, &iter));

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterScan007, TestSize.Level0)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 256;
    const uint32_t numSamples = 1024;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    // 查未聚类数量
    VecScanParaT vectorScanPara = {0};
    vectorScanPara.dim = 256;
    vectorScanPara.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara};
    IvfClusterScanAndCompareResult(idxCtx, &cfg, numSamples);

    // 没有聚类但查所有聚类数量
    vectorScanPara.clusterType = CLUSTER_TYPE;
    IvfClusterScanAndCompareResult(idxCtx, &cfg, numSamples);

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterBuild008, TestSize.Level2)
{
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    uint32_t numPerClass = 1024;
    const uint32_t numSamples = 1024;
    vector<vector<float>> vectors;
    uint64_t dataId[numSamples] = {0};
    IvfClusterCreateVectorsAndDataId(numPerClass, numSamples, dim, vectors, dataId);

    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = vectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = dataId[i];
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    vector<float> floatVector;
    TransferVector2Float(floatVector, vectors);
    VecBuildStateParaT buildStatePara = {.count = numSamples,
        .distType = DIST_TYPE_L2,
        .dataId = dataId,
        .vectors = floatVector.data(),
        .sample = false,
        .dim = dim};
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, AnnIdxBuild(idxCtx, &buildStatePara));
    IvfClusterStartBuild(idxCtx, dim, numSamples, dataId, DIST_TYPE_COSINE, floatVector.data());

    VecScanParaT vectorScanPara = {0};
    vectorScanPara.dim = 256;
    vectorScanPara.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara};
    IvfClusterScanAndCompareResult(idxCtx, &cfg, 0);

    // 聚类后查所有聚类数量
    vectorScanPara.clusterType = CLUSTER_TYPE;
    IvfClusterScanAndCompareResult(idxCtx, &cfg, numSamples);

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

static vector<vector<float>> g_clusterVectors = {
    {-0.3280029892921448, 0.04378657415509224, -0.05203954502940178, -0.10899379849433899, 0.126626655459404,
        -0.29381656646728516, -0.21500925719738007, 0.0622737780213356, 0.02298162691295147, 0.26088932156562805,
        0.10912088304758072, -0.23257005214691162, 0.09388387948274612, -0.08817031234502792, 0.02789944037795067,
        0.01297952700406313, -0.029891857877373695, 0.08884482830762863, 0.043335895985364914, 0.02856101468205452,
        0.01930924877524376, -0.015276550315320492, -0.11020248383283615, -0.11688821017742157, 0.0032172552309930325,
        0.050968218594789505, -0.06158887967467308, 0.056331049650907516, 0.1481785923242569, 0.02875208482146263,
        0.09829798340797424, 0.07852683216333389, 0.18810947239398956, 0.15130648016929626, 0.0076484703458845615,
        -0.08534704893827438, 0.17298433184623718, 0.010214789770543575, 0.023931866511702538, -0.04935707524418831,
        -0.002956137526780367, 0.09814895689487457, 0.0892195850610733, -0.07548753172159195, -0.17700058221817017,
        0.01667589694261551, -0.08909811079502106, -0.03879617154598236, 0.02738051861524582, -0.02424720674753189,
        -0.050637681037187576, -0.0034618861973285675, 0.017256448045372963, 0.11523961275815964, -0.027759816497564316,
        0.01497031468898058, -0.0358259491622448, 0.02793390117585659, -0.02204105630517006, -0.007922929711639881,
        0.05530712753534317, -0.07857482880353928, 0.027759741991758347, 0.04149212688207626, -0.13872963190078735,
        -0.04219328612089157, -0.02029433473944664, 0.04455927386879921, 0.0030146832577884197, 0.08953934162855148,
        -0.043989747762680054, -0.021359754726290703, -0.04668920114636421, 0.004202576354146004, 0.029828237369656563,
        0.07746116071939468, 0.09031279385089874, -0.007761345244944096, 0.00929616205394268, -0.02368292585015297,
        0.016854291781783104, 0.015321854501962662, 0.08151502162218094, 0.046630535274744034, 0.09869281947612762,
        -0.04986489191651344, 0.017128854990005493, 0.007982487790286541, -0.039073191583156586, -0.008178002201020718,
        0.06259291619062424, 0.022985827177762985, -0.003984061069786549, -0.0037199214566498995, -0.05741012468934059,
        0.022391213104128838, -0.04402102157473564, -0.002162871416658163, 0.05560309439897537, -0.05221039429306984,
        0.028285151347517967, 0.0701107457280159, -0.028469115495681763, -0.011684880591928959, 0.01617296412587166,
        0.046839676797389984, -0.1037207543849945, -0.02287573739886284, 0.022023523226380348, -0.07673375308513641,
        0.02680841274559498, 0.09330715239048004, -0.03660205751657486, 0.09161949902772903, 0.01771393045783043,
        0.05130961909890175, -0.1235385611653328, -0.07311510294675827, 0.0035499832592904568, 0.02192464843392372,
        0.02639847993850708, 0.015586833469569683, -0.03477298468351364, -0.022466281428933144, 0.01714305765926838,
        0.001090530538931489, -0.06887075304985046, 0.04234111309051514, 0.0044407472014427185, 0.006555940955877304,
        -0.0015502522001042962, -0.025276437401771545, 0.021983949467539787, -0.03782719746232033, -0.03810177370905876,
        0.09119347482919693, -0.01498572900891304, -0.014536045491695404, 0.003637082641944289, -0.014823264442384243,
        0.047367677092552185, 0.02479652315378189, 0.025151563808321953, 0.047719597816467285, -0.08314671367406845,
        -0.0063620638102293015, -0.0197548009455204, -0.0015276481863111258, 0.04732523486018181, 0.00893798004835844,
        0.010643083602190018, 0.06547558307647705, -0.05119845271110535, 0.009362315759062767, 0.04001453518867493,
        0.04095000401139259, 0.004086317028850317, 0.07256004214286804, 0.01645178161561489, 0.03671647608280182,
        -0.06402269750833511, -0.002665598876774311, 0.03525581583380699, 0.017168786376714706, -0.0010447512613609433,
        0.018704380840063095, 0.015553619712591171, 0.009175693616271019, -0.03099304623901844, 0.016471438109874725,
        -0.02598218433558941, 0.006673506926745176, -0.018385963514447212, -0.06451069563627243, -0.0036044421140104532,
        -0.004415329545736313, 0.019745416939258575, -0.00461671082302928, 0.03257465362548828, 0.03600171208381653,
        -0.0001732898090267554, 0.020819731056690216, 0.04116680473089218, 0.010721846483647823, 0.008972333744168282,
        0.015050549060106277, 0.04481714218854904, 0.04830627515912056, 0.013223105110228062, -0.050776660442352295,
        0.010851208120584488, 0.0012838842812925577, 0.019893798977136612, -0.021318471059203148, -0.058956943452358246,
        -0.025566212832927704, -0.02360931783914566, 0.0292639322578907, -0.018809078261256218, 0.0011258336016908288,
        -0.03516863286495209, -0.022364744916558266, -0.029729481786489487, 0.0026723803021013737, -0.0165926031768322,
        -0.025522300973534584, 0.009212569333612919, 0.027224060148000717, 0.01544133573770523, 0.02048693597316742,
        -0.08771789819002151, -0.0040705385617911816, -0.026439737528562546, -0.05525079369544983, 0.012528257444500923,
        -0.038832928985357285, -0.026584621518850327, 0.03711347654461861, -0.012796232476830482, -0.04252883791923523,
        -0.019373707473278046, 0.05655026435852051, -0.0024727724958211184, 0.006830994505435228, 0.01775241084396839,
        0.0028170342557132244, 0.008778981864452362, -0.034494902938604355, -0.020604563876986504, -0.03839634731411934,
        0.004039986524730921, -0.014317836612462997, -0.023928577080368996, -0.033406391739845276, 0.007265326101332903,
        0.003512580879032612, -0.02333998493850231, 0.013748164288699627, -0.004712382797151804, -0.021471401676535606,
        0.011271156370639801, 0.009784823283553123, -0.026734137907624245, 0.02861684374511242, -0.00892699509859085,
        -0.04794418066740036, 0.015718698501586914, -0.016641760244965553, 0.012401380576193333, 0.027921145781874657,
        0.008456799201667309, -0.04027281329035759, 0.028120117262005806, 0.004797107074409723, 0.008702132850885391,
        0.0053693875670433044},
    {-0.33018800616264343, 0.009235250763595104, 0.05976564437150955, -0.10035940259695053, 0.05202202498912811,
        -0.3288259506225586, -0.15503211319446564, 0.13893593847751617, -0.010633504949510098, 0.12473630905151367,
        0.03208613395690918, -0.2965734899044037, 0.17462953925132751, -0.07485950738191605, -0.08191180229187012,
        -0.0806308314204216, -0.04944682493805885, 0.07273442298173904, 0.10736934840679169, 0.004506973084062338,
        -0.1376284807920456, 0.01836022362112999, -0.0592765137553215, 0.11421529948711395, 0.004317301791161299,
        0.15370364487171173, -0.08857300877571106, 0.08163707703351974, 0.04436807706952095, 0.015493953600525856,
        -0.049000173807144165, 0.05715116485953331, 0.012098499573767185, -0.011362722143530846, 0.05744709074497223,
        -0.04940737783908844, -0.034944143146276474, -0.008015136234462261, -0.03244274482131004, -0.013566207140684128,
        0.07256223261356354, 0.14610759913921356, 0.06615222990512848, -0.0028341906145215034, -0.02706710621714592,
        0.12597225606441498, -0.008134119212627411, -0.024056587368249893, 0.058124516159296036, 0.0984039381146431,
        -0.0025999657809734344, 0.12084600329399109, 0.08702266216278076, 0.22068938612937927, -0.11033947020769119,
        0.037290241569280624, 0.05494832992553711, 0.011884675361216068, -0.11659476906061172, 0.019140593707561493,
        0.057910460978746414, 0.008199842646718025, 0.08706621825695038, 0.012048600241541862, -0.038787804543972015,
        0.054221011698246, 0.0537370927631855, 0.01805991120636463, 0.04679970443248749, 0.026923591271042824,
        0.01119141187518835, 0.03218372166156769, 0.02247256599366665, -0.02247876301407814, 0.00994220282882452,
        -0.0007205208530649543, 0.059176065027713776, -0.05607910826802254, -0.02597365900874138, -0.014075269922614098,
        0.07909342646598816, -0.022198332473635674, 0.07635409384965897, 0.017343761399388313, -0.010803782381117344,
        -0.00459686666727066, 0.06354156881570816, -0.05784923955798149, -0.09206821769475937, 0.04259466752409935,
        -0.05139241740107536, 0.04220243915915489, 0.03021113947033882, 0.025573523715138435, -0.007450728677213192,
        0.05961248278617859, 0.08546125888824463, -0.06630758196115494, 0.06863738596439362, 0.0072015514597296715,
        -0.001256398274563253, -0.019355639815330505, 0.008863572962582111, 0.022763295099139214, 0.000865614740177989,
        -0.007304833270609379, 0.09661281108856201, -0.05447233468294144, -0.08082705736160278, -0.0012702486710622907,
        0.03672619163990021, 0.10776195675134659, -0.0304250568151474, -0.005912955850362778, -0.012089092284440994,
        -0.08823317289352417, -0.006371643859893084, 0.08309713006019592, 0.023240970447659492, -0.04116027429699898,
        0.01136154867708683, 0.029227811843156815, 0.03178132697939873, 0.02567051537334919, -0.07063589990139008,
        -0.05337037891149521, -0.024499570950865746, 0.046447448432445526, 0.0034984329249709845, -0.03095073252916336,
        -0.025568943470716476, 0.012174053117632866, 0.03106544353067875, -0.012155253440141678, 0.014629779383540154,
        -0.0030633050482720137, 0.012306173332035542, -0.03648246452212334, -0.007447091396898031,
        -0.050821200013160706, -0.08570899814367294, 0.049707211554050446, 0.028757186606526375, 0.0011137055698782206,
        -0.07551827281713486, 0.013994512148201466, -0.034372735768556595, 0.014384374022483826, 0.08921404927968979,
        0.016616428270936012, -0.021485652774572372, 0.01120193675160408, 0.0009463403257541358, 0.08748101443052292,
        0.05169172212481499, -0.03268318995833397, 0.013612404465675354, -0.011043270118534565, -0.012209753505885601,
        -0.07256819307804108, -0.0020245795603841543, 0.056471966207027435, -0.0066251070238649845,
        0.014544076286256313, 0.10576822608709335, 0.017693933099508286, -0.03363364189863205, 0.004272103309631348,
        -0.00028591635054908693, 0.02192581444978714, -0.014817940071225166, 0.008400683291256428,
        -0.0023338592145591974, -0.004435607232153416, -0.03267031908035278, 0.021990511566400528, -0.03171413391828537,
        0.046255577355623245, 0.06582614034414291, -0.015591375529766083, -0.012772726826369762, -0.016421714797616005,
        -0.032081302255392075, -0.012600185349583626, 0.06391376256942749, -0.005286321975290775, -0.0123545927926898,
        0.06437505036592484, -0.007263238076120615, -0.0001788638037396595, 0.006507498677819967, -0.018717898055911064,
        0.04205566272139549, 0.007979601621627808, -0.020002273842692375, -0.028557751327753067, -0.056994181126356125,
        -0.03693509101867676, -0.017202267423272133, -0.017453821375966072, 0.01860525831580162, 0.04400085285305977,
        0.0008007966680452228, -0.04286860302090645, -0.010138381272554398, 0.026209743693470955, 0.007191424258053303,
        -0.06149232015013695, -0.043056558817625046, -0.030818741768598557, 0.0590287484228611, 0.05188388377428055,
        0.040777504444122314, -0.0585639663040638, -0.032412368804216385, -0.028824374079704285, 0.004469608888030052,
        0.02620411477982998, 0.05198505148291588, 0.00799303688108921, 0.012049409560859203, 0.03551698103547096,
        -0.02226332575082779, 0.03138013556599617, -0.009855332784354687, 0.01555590983480215, -0.043386612087488174,
        0.017867129296064377, 0.021181926131248474, -0.029741384088993073, 0.007104313932359219, 0.030000314116477966,
        0.032555293291807175, 0.056728657335042953, 0.030376514419913292, 0.022699857130646706, 0.007655886001884937,
        -0.020992666482925415, 0.012233310379087925, -0.008628453128039837, -0.000829228141810745, -0.01844286359846592,
        -0.00875283032655716, -0.000708996900357306, -0.0012624115915969014, -0.024626297876238823,
        -0.04502382501959801, -0.017725544050335884, -0.03165431320667267, 0.005341195967048407, 0.005938971880823374,
        0.006868084892630577, 0.015015962533652782, 0.02141035720705986, -0.021993866190314293, -0.012616090476512909},
    {-0.39937037229537964, 0.046779096126556396, -0.0012725215638056397, -0.041461169719696045, 0.14577524363994598,
        -0.30053040385246277, -0.1369628608226776, 0.280661016702652, 0.0689530000090599, 0.05612173676490784,
        0.007640505209565163, -0.12895043194293976, 0.07252486050128937, 0.019746137782931328, 0.011193484999239445,
        -0.04240778088569641, 0.0025463104248046875, 0.05607904493808746, 0.10851018875837326, 0.10479012876749039,
        -0.15307652950286865, 0.053715839982032776, -0.1140771135687828, 0.007117358036339283, 0.01563313603401184,
        0.21088653802871704, -0.0016343462048098445, 0.0667954832315445, 0.09567392617464066, -0.0329393707215786,
        0.008240916766226292, 0.030893599614501, 0.17818941175937653, 0.04823831096291542, 0.09917902201414108,
        -0.09383770823478699, 0.0034321718849241734, -0.07243310660123825, -0.09647882729768753, -0.057689063251018524,
        0.07034417241811752, 0.07693270593881607, -0.019997308030724525, -0.05449226498603821, -0.0621698796749115,
        0.015022017993032932, -0.07575686275959015, 0.0011587016051635146, 0.10510613024234772, -0.002998649375513196,
        -0.04343366622924805, 0.05115056410431862, -0.056936971843242645, 0.17699512839317322, -0.05983155593276024,
        0.017949646338820457, 0.03600435331463814, 0.009864376857876778, -0.005194675177335739, 0.03633294627070427,
        -0.049519576132297516, 0.06608758121728897, -0.009955248795449734, 0.02873806655406952, -0.02898184396326542,
        0.10484421253204346, -0.04290959984064102, 0.020412778481841087, -0.015187099575996399, -0.017363043501973152,
        0.07261500507593155, -0.05913873761892319, -0.025523457676172256, 0.0120138144120574, 0.02417861856520176,
        0.0047970605082809925, -0.060377638787031174, -0.006054858677089214, -0.005623145028948784, 0.0445869006216526,
        0.005661408416926861, 0.06026015430688858, 0.03176901862025261, 0.0246101226657629, 0.05788683891296387,
        0.07165748625993729, 0.028292911127209663, 0.012645605020225048, -0.006446615792810917, 0.020587125793099403,
        0.04488867148756981, -0.012126745656132698, -0.013502843677997589, 0.005487380083650351, -0.008508083410561085,
        0.08851002901792526, -0.00688053946942091, -0.036239005625247955, 0.12084408104419708, -0.014149664901196957,
        -0.014978501945734024, -0.12020454555749893, 0.01506676897406578, -0.015547000803053379, 0.07547860592603683,
        -0.03029678761959076, 0.025412114337086678, 0.005786105990409851, -0.05391334369778633, 0.05895312502980232,
        0.024954920634627342, -0.02164601720869541, 0.03557191789150238, 0.042925938963890076, 0.04207928851246834,
        -0.041445307433605194, -0.0580313503742218, 0.0003897516871802509, -0.0566859096288681, -0.06345254927873611,
        -0.003537525190040469, 0.028155626729130745, -0.014053278602659702, -0.062039438635110855, 0.05570830777287483,
        -0.033095650374889374, -0.04201461374759674, 0.13031086325645447, -0.007092820480465889, -0.008867738768458366,
        0.06957651674747467, -0.014535234309732914, 0.050874628126621246, 0.011874637566506863, -0.03202980384230614,
        0.011768887750804424, -0.024979056790471077, 0.028086233884096146, -0.010772794485092163, 0.007137604057788849,
        0.02290954440832138, -0.02604798972606659, 0.042771924287080765, -0.02328226901590824, 0.009373646229505539,
        -0.03197437524795532, 0.04621625319123268, -0.062033966183662415, 0.01837851107120514, -0.00344743556343019,
        -0.06439798325300217, 0.05046454817056656, -0.06373447924852371, -0.007362650707364082, 0.018428917974233627,
        -0.01780860126018524, 0.04720301553606987, -0.057496704161167145, 0.044797711074352264, -0.0027422155253589153,
        0.018450291827321053, 0.04923147335648537, -0.016320442780852318, -0.02248203195631504, -0.010720008052885532,
        -0.019428109750151634, 0.0041171833872795105, 0.053705450147390366, 0.08723694086074829, 0.036258477717638016,
        -0.052786458283662796, 0.058256953954696655, -0.013156993314623833, -0.029771678149700165, -0.04561522603034973,
        -0.009231368079781532, 0.011316608637571335, 0.006819224450737238, -0.03359285742044449, 0.038313060998916626,
        0.022474275901913643, -0.006453397683799267, -0.009761888533830643, -0.05099692940711975, -0.07600787281990051,
        0.06631483137607574, 0.025104856118559837, 0.008017662912607193, -0.024254338815808296, 0.05529274418950081,
        -0.015648789703845978, -0.0042570848017930984, 0.010930683463811874, 0.0035016643814742565,
        0.011107230558991432, -0.025698170065879822, 0.08814065158367157, 0.06998012214899063, -0.0389038510620594,
        0.024384982883930206, -0.0676112174987793, -0.01789971999824047, 0.06435993313789368, -0.08863399922847748,
        -6.732177280355245e-05, 0.03837215155363083, -0.010983921587467194, -0.07289184629917145, 0.0009914473630487919,
        -0.038096170872449875, -0.03839930146932602, 0.04714588075876236, -0.004004119895398617, -0.0480569526553154,
        -0.02862427569925785, -0.004209673963487148, 0.0315072275698185, -0.013485250063240528, 0.0005714273429475725,
        0.0017425615806132555, -0.01746894232928753, 0.03711014240980148, -0.011306854896247387, 0.035485170781612396,
        -0.003985805436968803, -0.045025162398815155, -0.024952104315161705, -0.012861078605055809,
        0.014429490081965923, 0.04637366160750389, -0.0016871955012902617, 0.012721081264317036, -0.0182940736413002,
        -0.02984393574297428, -0.034819699823856354, 0.055462583899497986, -0.014203917235136032, 0.03414839506149292,
        0.0360383465886116, -0.011569392867386341, -0.02619873732328415, 0.0033216404262930155, -0.06341051310300827,
        0.002536152722314, -0.01220027357339859, 0.03759247437119484, 0.007237307261675596, -0.020361319184303284,
        -0.019625287503004074, -0.009439670480787754, 0.0328899621963501, 0.011093728244304657, 0.03907237946987152,
        0.007255738601088524, -0.02833385393023491, -0.009498033672571182},
    {-0.33765292167663574, -0.033181872218847275, -0.11557231843471527, -0.10174019634723663, 0.1326136291027069,
        -0.2658998370170593, -0.18661145865917206, 0.14244741201400757, -0.091242715716362, 0.13077959418296814,
        0.07342977821826935, -0.28704598546028137, 0.07538655400276184, 0.0182714331895113, -0.06779599189758301,
        -0.14752621948719025, 0.04854216426610947, 0.05758925527334213, 0.18011830747127533, 0.07077527046203613,
        -0.1900940090417862, 0.045064736157655716, -0.16424834728240967, -0.09071193635463715, 0.0013710549101233482,
        0.21602670848369598, -0.0030333742033690214, 0.06479967385530472, 0.05950980260968208, 0.0015425956808030605,
        0.014741284772753716, 0.06466958671808243, 0.0649276077747345, 0.05808816850185394, 0.007441398221999407,
        -0.04174935072660446, 0.02614692784845829, 0.052316609770059586, -0.04989628866314888, 0.034262437373399734,
        0.013951970264315605, 0.12443367391824722, 0.004406553227454424, 0.07395532727241516, -0.010432442650198936,
        -0.07049968093633652, -0.08094463497400284, 0.04211217164993286, 0.03874630108475685, -0.002128026681020856,
        -0.08032890409231186, -0.10300189256668091, 0.025168156251311302, 0.1290690302848816, 0.024591542780399323,
        0.024378884583711624, 0.0278633926063776, -0.06043912097811699, -0.0851597785949707, 0.09467031061649323,
        -0.08850657194852829, -0.03372344747185707, 0.10838057100772858, 0.01787562109529972, -0.09989096224308014,
        0.05222995579242706, -0.020236846059560776, 0.06058275327086449, -0.015326282940804958, -0.06544289737939835,
        0.045974764972925186, -0.0692959651350975, 0.01960970088839531, -0.03445051237940788, -0.013430873863399029,
        0.07484005391597748, -0.07783441245555878, -0.03933895379304886, 0.07260777056217194, 0.011256084777414799,
        -0.0015758542576804757, -0.10111964493989944, 0.057245947420597076, -0.03053279221057892, 0.03393794596195221,
        -0.04337727651000023, -0.03436385840177536, -0.04168218746781349, 0.0879148468375206, 0.041205935180187225,
        -0.0924878939986229, 0.09960165619850159, 0.00912889838218689, 0.0029281156603246927, 0.04994053766131401,
        0.07355141639709473, 0.03722000867128372, 0.06493361294269562, 0.016297733411192894, -0.03552462160587311,
        0.08936652541160583, 0.006867928896099329, -0.03170432522892952, 0.01586945913732052, 0.06171305850148201,
        -0.04705384746193886, -0.06091522052884102, 0.009631582535803318, -0.06523746252059937, -0.004133997950702906,
        -0.00423261197283864, 0.0020919691305607557, -0.05026555061340332, 0.07015880942344666, -0.01806250959634781,
        -0.0001839933538576588, 0.034538935869932175, 0.014139591716229916, -0.026105500757694244, 0.01148893404752016,
        -0.03143459931015968, -0.012445607222616673, 0.10460910201072693, -0.0010654928628355265, 0.032227564603090286,
        -0.020618434995412827, -0.0064657400362193584, 0.05629497021436691, -0.05873757600784302, 0.03535976633429527,
        -0.04214201867580414, -0.043526541441679, -0.04229900985956192, 0.01730962097644806, -0.041893333196640015,
        -0.07617475092411041, 0.03877345472574234, -0.03220852464437485, 0.00748550146818161, 0.00795471016317606,
        0.01712544448673725, 0.01611873134970665, -0.04374125227332115, -0.0014870002632960677, 0.01008931826800108,
        -0.005166094750165939, -0.015405513346195221, 0.010155514813959599, -0.016500333324074745, -0.05623100697994232,
        0.019769975915551186, 0.029405994340777397, -0.03601234033703804, -0.02797807939350605, 0.004578226245939732,
        0.004535331390798092, 0.048917509615421295, 0.027692535892128944, 0.060890816152095795, 0.011891314759850502,
        0.040656812489032745, -0.034047216176986694, 0.014836985617876053, -0.023660095408558846, 0.0039038443937897682,
        -0.018228786066174507, 0.037004049867391586, -0.058042097836732864, 0.00393705815076828, 9.859818237600848e-05,
        0.009170289151370525, -0.037763811647892, -0.07288265228271484, 0.015940533950924873, 0.011284148320555687,
        0.009702667593955994, -0.055588498711586, -0.01033279113471508, -0.03274000436067581, 0.025581154972314835,
        -0.03836081176996231, -0.005059685092419386, -0.05374931916594505, -0.03818707540631294, 0.04944481700658798,
        0.018843555822968483, 0.05563298985362053, 0.03477681055665016, 0.023177536204457283, -0.012293091975152493,
        -0.07295236736536026, 0.0014126342721283436, 0.00900337379425764, -0.006511326879262924, -0.003421032102778554,
        -0.03344670683145523, 0.018442966043949127, -0.012447786517441273, 0.0032163173891603947, -0.004494339227676392,
        0.006583940237760544, 0.016164278611540794, 0.005108499899506569, 0.025945357978343964, 0.013831689953804016,
        0.02880200557410717, 0.00023732722911518067, 0.0005769786075688899, 0.04963957518339157, 0.0031852684915065765,
        -0.026261216029524803, -0.0015837537357583642, 0.005306906532496214, 0.027954647317528725,
        -0.0027811620384454727, 0.0007183526176959276, 0.016779892146587372, 0.008747064508497715,
        -0.003691922640427947, 0.027981270104646683, -0.021283186972141266, 0.023235732689499855, 0.05442146211862564,
        -0.03923545405268669, -0.02246522717177868, 0.009468107484281063, 0.0007572461036033928, 0.012500520795583725,
        0.022839393466711044, 0.017074069008231163, 0.02967916801571846, -0.005531701259315014, -0.023973971605300903,
        0.003926129546016455, -0.00541636161506176, -0.024181056767702103, 0.017343081533908844, -0.007765018381178379,
        0.00797899067401886, 0.0017767141107469797, 0.03461180999875069, 0.018181366845965385, 0.03394696116447449,
        0.016314031556248665, 0.023693768307566643, -0.052722033113241196, -0.0045343078672885895, 0.009244488552212715,
        -0.015191909857094288, 0.002776586450636387, -0.040608350187540054, 0.009971324354410172, -0.021217426285147667,
        0.0018404599977657199, 0.001436590333469212, 0.015579892322421074},
    {-0.34106406569480896, 0.006160170771181583, -0.005896878894418478, -0.12348280102014542, 0.10746525973081589,
        -0.3095061480998993, -0.18866515159606934, 0.13512073457241058, -0.07366184145212173, 0.12077847868204117,
        0.02920445427298546, -0.31412655115127563, 0.049704961478710175, -0.004141113720834255, -0.035480815917253494,
        -0.09287198632955551, 0.016214298084378242, 0.049396827816963196, 0.14777278900146484, 0.09841681271791458,
        -0.18337592482566833, 0.05302015691995621, -0.1771858185529709, -0.14265252649784088, 0.027141673490405083,
        0.19155776500701904, -0.03245734050869942, 0.0737251341342926, 0.06269197165966034, 0.035426560789346695,
        0.06460831314325333, 0.027941158041357994, 0.08720176666975021, 0.10517338663339615, 0.019277110695838928,
        -0.06164545193314552, 0.006969943642616272, 0.04003245383501053, -0.06782274693250656, -0.020068157464265823,
        0.03707687184214592, 0.17951227724552155, -0.0041473740711808205, 0.05039425566792488, -0.002386637730523944,
        -0.04084022343158722, -0.024370979517698288, 0.09703494608402252, 0.05973882973194122, 0.019786635413765907,
        -0.030582360923290253, -0.07570536434650421, 0.04801037907600403, 0.03459542617201805, 0.0065222242847085,
        0.06443021446466446, 0.030033480376005173, 0.0007120049558579922, -0.03801654651761055, 0.13278737664222717,
        -0.05238991975784302, -0.023904316127300262, 0.07576736062765121, 0.0813765674829483, -0.1313667744398117,
        0.06743530184030533, 0.02362486720085144, 0.016075678169727325, -0.029911158606410027, 0.029859857633709908,
        0.1190439835190773, -0.03946211561560631, 0.018686458468437195, -0.017165532335639, 0.03659325838088989,
        0.10809680074453354, -0.06150122359395027, -0.05624168738722801, 0.0599222406744957, 0.049702417105436325,
        -0.033766020089387894, -0.05834672972559929, 0.03641800209879875, -0.04296642169356346, -0.0112422751262784,
        0.029402993619441986, -0.041036538779735565, -0.025334550067782402, 0.09690795093774796, 0.01808745600283146,
        -0.018005836755037308, 0.09595073759555817, -0.0886007770895958, -0.02704150788486004, 0.0684681311249733,
        0.05507025867700577, 0.06266818195581436, 0.04250454157590866, -0.05815469101071358, -0.05777987837791443,
        0.06729757785797119, -0.021297847852110863, 0.06635741889476776, -0.027832694351673126, 0.03441550210118294,
        -0.043283611536026, -0.03878539055585861, -0.0012082618195563555, -0.030064450576901436, 0.024784572422504425,
        -0.021090401336550713, 0.02210601605474949, -0.031526897102594376, 0.039252690970897675, -0.0272524394094944,
        -0.044314153492450714, -0.017970401793718338, 0.04383672773838043, 0.02005230449140072, -0.0031569877173751593,
        -0.07270631939172745, 0.017737990245223045, 0.024455184116959572, 0.0848933681845665, 0.04507596045732498,
        -0.03849443420767784, -0.04768243432044983, -0.0077462587505578995, -0.036233387887477875,
        0.0022837058641016483, -0.03526768088340759, -0.005828617140650749, -0.02571967989206314, -0.013243635185062885,
        -0.002048950409516692, -0.05009596422314644, 0.05160157382488251, -0.03141074627637863, -0.014596697874367237,
        0.009935596957802773, 0.035311874002218246, -0.04606512933969498, -0.005401477217674255, 0.026642311364412308,
        -0.01441162545233965, 0.022633519023656845, -0.003783367807045579, -0.01395445317029953, -0.018029192462563515,
        -0.06836874783039093, -0.04052838683128357, 0.06132320314645767, 0.01585441827774048, -0.029228653758764267,
        0.0039989822544157505, 0.023669645190238953, 0.0542570985853672, 0.0421619638800621, 0.03691547363996506,
        0.016872262582182884, 0.10088914632797241, -0.02975938469171524, -0.006880791392177343, 0.012806881219148636,
        0.0032863053493201733, 0.008918837644159794, -0.0012539729941636324, -0.0550386868417263, 0.02074648253619671,
        0.01139115821570158, 0.028154410421848297, -0.013979911804199219, -0.058194682002067566, -0.015935886651277542,
        -0.012734823860228062, -0.035738248378038406, 0.007723707240074873, -0.03843492269515991, -0.038413289934396744,
        0.039966829121112823, -0.01655796729028225, -0.013537435792386532, 0.02112031914293766, 0.00783892534673214,
        0.0022983388043940067, -0.010645134374499321, 0.010033461265265942, 0.028533559292554855, -0.025935165584087372,
        -0.04452420026063919, -0.03313390910625458, 0.006634918972849846, -0.024162540212273598, -0.027439793571829796,
        -0.02252972312271595, -0.032851651310920715, -0.006448168773204088, 0.04427725821733475, 0.006668690126389265,
        -0.04296797141432762, 0.025647325441241264, 0.0170159712433815, 0.03635623678565025, 0.0022843594197183847,
        0.040481433272361755, -0.007808893918991089, 0.006075150799006224, 0.005789261311292648, 0.04870736971497536,
        0.016523806378245354, -0.011057228781282902, 0.020115181803703308, 0.04074589163064957, 0.014778821729123592,
        0.019624777138233185, 0.05287531390786171, 0.0026405686512589455, 0.018188655376434326, -0.015976576134562492,
        0.02778368629515171, -0.03472461551427841, 0.00848059169948101, 0.03875570371747017, -0.014771602116525173,
        0.007879146374762058, 0.014298728667199612, -0.002633957425132394, 0.012728394009172916, -0.022772669792175293,
        0.012112376280128956, 0.0452377125620842, -0.009696335531771183, 0.010064607486128807, -0.02302686870098114,
        -0.020227927714586258, -0.011148462072014809, -0.019414350390434265, 0.03859807178378105, -0.012312139384448528,
        0.005173403769731522, 0.013604786247015, 0.002058014739304781, -0.015161551535129547, -0.0088631147518754,
        0.011479197070002556, 0.008605094626545906, 0.012105515226721764, -0.013477030210196972, -0.02956073358654976,
        -0.01625729724764824, -0.007976224645972252, -0.006330995354801416, -0.013124958612024784, 0.009871655143797398,
        -0.0029904323164373636, 0.0013030701084062457},
    {-0.3561449646949768, 0.0038369307294487953, -0.0711759477853775, -0.07871484011411667, 0.14321908354759216,
        -0.281623512506485, -0.16476242244243622, 0.09042215347290039, -0.09856939315795898, 0.12679770588874817,
        0.10032810270786285, -0.33293575048446655, 0.04571393132209778, -0.03343328461050987, -0.07019589096307755,
        -0.14560779929161072, 0.06100926175713539, 0.04379595071077347, 0.1880316585302353, 0.051363054662942886,
        -0.14961497485637665, 0.042142897844314575, -0.15443819761276245, -0.09046993404626846, -0.004278919193893671,
        0.19899211823940277, -0.02730834111571312, 0.05093061923980713, 0.12399650365114212, 0.010989971458911896,
        0.019710863009095192, 0.07054194808006287, 0.09513945877552032, 0.07471021264791489, 0.009618663229048252,
        -0.06391763687133789, 0.036891572177410126, 0.009075120091438293, -0.02417731285095215, 0.03429488092660904,
        0.04830559715628624, 0.14220017194747925, 0.008518249727785587, 0.04181160777807236, -0.020948613062500954,
        -0.04110657051205635, -0.10450752824544907, 0.07107297331094742, 0.024984899908304214, 0.024056322872638702,
        -0.09211119264364243, -0.0943022221326828, 0.04244013503193855, 0.13178743422031403, 0.03223464637994766,
        0.009533265605568886, 0.00812896154820919, -0.06672851741313934, -0.06953778117895126, 0.08945013582706451,
        -0.08385192602872849, 0.007415722589939833, 0.12249982357025146, 0.03668799623847008, -0.08380458503961563,
        0.025982266291975975, -0.04309120774269104, 0.053552981466054916, 0.00031855475390329957,
        0.00030582628096453846, 0.02808241732418537, -0.06622470170259476, 0.035746537148952484, -0.01715024560689926,
        -0.005246714223176241, 0.060548607259988785, -0.07663257420063019, -0.024088235571980476, 0.06789735704660416,
        0.034045543521642685, -0.0001017653921735473, -0.08707211911678314, 0.0325825996696949, -0.03889257833361626,
        0.012288554571568966, -0.05936538428068161, -0.028975915163755417, -0.01778627559542656, 0.10915182530879974,
        0.017438052222132683, -0.07719714194536209, 0.07426697760820389, 0.011321580968797207, -0.0240369513630867,
        0.03075891174376011, 0.0977042093873024, 0.026627447456121445, 0.04172378033399582, 0.024726906791329384,
        -0.017693979665637016, 0.10046207904815674, 0.00632916484028101, -0.010261780582368374, 0.023394029587507248,
        0.0662640929222107, -0.056451357901096344, -0.05768139660358429, -0.012466534040868282, -0.07613518089056015,
        -0.006984218023717403, 0.024705352261662483, 0.023783722892403603, -0.0526532419025898, 0.07803910970687866,
        -0.026099028065800667, 0.005522788967937231, 0.027097484096884727, 0.01248115487396717, 0.013892237097024918,
        0.01219123974442482, -0.04912257567048073, -0.0007922735530883074, 0.060074515640735626, 0.020717216655611992,
        0.014523432590067387, -0.03690878301858902, -0.01688797026872635, 0.057233940809965134, -0.03506842255592346,
        0.04243665561079979, -0.06384866684675217, -0.03526170924305916, -0.016220636665821075, 0.006329234689474106,
        -0.02668789029121399, -0.0836651548743248, 0.027737217023968697, -0.010543933138251305, -0.01919601485133171,
        0.007914085872471333, 0.025266526266932487, -0.012510992586612701, -0.05046553909778595, -0.000678867450915277,
        0.005813611671328545, -0.0012363152345642447, 0.0005218234146013856, 0.029011910781264305, 0.01615438610315323,
        -0.0712188184261322, 0.009892227128148079, 0.005995734129101038, -0.01875617355108261, -0.015394335612654686,
        -0.015305701643228531, -0.000651148147881031, 0.04118188098073006, 0.01970830000936985, 0.055589351803064346,
        0.03604048490524292, 0.03897329419851303, -0.056175440549850464, 0.015982957556843758, -0.03571714833378792,
        0.003981240093708038, 0.007860389538109303, 0.02289908193051815, -0.07433250546455383, 0.011098244227468967,
        -0.01559825986623764, 0.030281081795692444, -0.04011179134249687, -0.04665444418787956, 0.01165723241865635,
        -0.015351779758930206, 0.0007135116611607373, -0.053496818989515305, -0.03617611154913902,
        -0.057864028960466385, 0.03897782415151596, -0.01185411773622036, -0.021624011918902397, -0.05195442959666252,
        -0.02077528089284897, 0.06536604464054108, -0.0004670176131185144, 0.045062948018312454, 0.027707472443580627,
        0.015983186662197113, -0.028549237176775932, -0.06406520307064056, 0.012329832650721073, 0.00018055523105431348,
        0.01177925430238247, 0.023813573643565178, -0.017617739737033844, -0.0203730259090662, -0.01946435682475567,
        0.007902259938418865, -0.013452350161969662, -0.0076031857170164585, 0.010155529715120792, 0.005481226835399866,
        0.008646714501082897, 0.0012481431476771832, 0.03440503403544426, 0.0007536518387496471, 0.00044059206265956163,
        0.04618430882692337, 0.014308026060461998, -0.024248439818620682, 0.015418182127177715, -0.001001131720840931,
        0.027318395674228668, -0.010126923210918903, 0.009000429883599281, -0.008376494981348515, 0.003640115028247237,
        -0.00023964753199834377, 0.027482880279421806, -0.012249959632754326, 0.005401962902396917, 0.04241948202252388,
        -0.03744872659444809, -0.017909705638885498, 0.014227679930627346, 0.006934490520507097, 0.03949575871229172,
        0.011741873808205128, 0.023451685905456543, 0.029393445700407028, 0.014536609873175621, -0.016169467940926552,
        0.01877879723906517, 0.0052554006688296795, -0.038331519812345505, -0.016240006312727928,
        0.00042243278585374355, 0.006625495385378599, 0.0032306762877851725, 0.02803410030901432, 0.01478761900216341,
        0.025488356128335, 0.0033666128292679787, 0.035996340215206146, -0.04931425675749779, -0.005103216040879488,
        0.014010073617100716, -0.007852485403418541, -0.0011255961144343019, -0.034637466073036194,
        0.010991410352289677, -0.02116834558546543, 0.0029576013330370188, -0.005372624844312668, 0.00693145114928484},
    {-0.33292368054389954, 0.16496743261814117, 0.013455718755722046, -0.07593999058008194, 0.025889554992318153,
        -0.2929989695549011, -0.21783961355686188, 0.15021009743213654, 0.020312169566750526, 0.0063538821414113045,
        -0.016636835411190987, -0.21474501490592957, 0.07499381899833679, -0.03659946843981743, -0.0609460286796093,
        -0.01170925609767437, 0.000925114203710109, 0.08025099337100983, 0.1123495101928711, 0.04224273934960365,
        -0.12618301808834076, 0.07578472048044205, -0.19171003997325897, 0.169272318482399, 0.02126893773674965,
        0.16360031068325043, -0.04914749413728714, 0.05156927928328514, 0.13794884085655212, -0.03506963327527046,
        0.0417838916182518, -0.03380144014954567, 0.09103404730558395, 0.13727591931819916, 0.14676299691200256,
        -0.05441221594810486, -0.001255929353646934, 0.036570727825164795, 0.03531520068645477, -0.01717429980635643,
        0.026972787454724312, 0.07821526378393173, -0.013019544072449207, 0.03671232983469963, -0.0405312180519104,
        0.036308106034994125, -0.136348158121109, 0.05400705337524414, 0.053033653646707535, -0.11327651888132095,
        -0.05595661699771881, -0.03451621159911156, 0.055064305663108826, 0.13266056776046753, -0.10892774909734726,
        -0.0006910011870786548, 0.01008482277393341, -0.0890078917145729, -0.0317729152739048, 0.10207930952310562,
        0.02621794492006302, -0.02462465688586235, 0.03859441354870796, 0.027427678927779198, -0.09728052467107773,
        -0.024326760321855545, 0.019526492804288864, 0.08206509798765182, 0.05996023491024971, 0.05969903618097305,
        -0.007229472044855356, -0.008148946799337864, 0.0007205229485407472, 0.001880844822153449, 0.12186231464147568,
        0.035365279763936996, 0.04109086096286774, 0.021446101367473602, 0.022963453084230423, -0.04260105639696121,
        0.03960590437054634, 0.028156666085124016, 0.01979026198387146, 0.07919126003980637, 0.02091173641383648,
        0.027145305648446083, 0.015248149633407593, 0.01571500487625599, -0.003217568388208747, -0.009214234538376331,
        0.028860269114375114, -0.07643244415521622, -0.0883241817355156, -0.02213244140148163, 0.04656972363591194,
        0.047133538872003555, 0.052441831678152084, 0.010300892405211926, 0.06347228586673737, 0.012403953820466995,
        -0.026292383670806885, 0.018101152032613754, -0.04799969121813774, 0.063237763941288, -0.02914377674460411,
        -0.07208116352558136, 0.002541938563808799, -0.009573046118021011, -0.06143439933657646, -0.024793876335024834,
        0.043888334184885025, 0.03156508877873421, -0.02290351875126362, -0.010580076836049557, 0.000508866214659065,
        -0.002639026613906026, 0.04468175396323204, 0.09032738208770752, 0.02533496730029583, 0.00013844620843883604,
        0.042994312942028046, 0.014851856976747513, 0.03563753888010979, -0.013523799367249012, -0.030217112973332405,
        -0.026327181607484818, -0.052394501864910126, 0.0940062552690506, -0.028929544612765312, -0.022304534912109375,
        -0.05026875436306, 0.01727239228785038, 0.034481436014175415, 0.014729062095284462, 0.11027918010950089,
        0.05939647555351257, -0.05712522193789482, -0.07545727491378784, -0.004275760613381863, 0.011225301772356033,
        -0.002597624668851495, 0.05221729353070259, 0.011465420015156269, -0.068231962621212, -0.02924441359937191,
        0.005339380353689194, -0.022025495767593384, 0.00871146097779274, -0.0013516389299184084, 0.015597701072692871,
        -0.00919384602457285, 0.04259186610579491, 0.015268789604306221, -0.02092280238866806, 0.04762352630496025,
        0.053621429949998856, -0.0027200081385672092, 0.04251972213387489, 0.027184555307030678, -0.11075253784656525,
        -0.030892975628376007, -0.017759239301085472, 0.03803408518433571, -0.07746483385562897, 0.05948793888092041,
        0.06348132342100143, 0.007785212714225054, 0.018538454547524452, 0.0496034100651741, 0.07448410242795944,
        0.01352983620017767, -0.04009021446108818, -0.010170784778892994, -0.041792646050453186, -0.06988788396120071,
        -0.1063951775431633, 0.013766145333647728, -0.04273240640759468, 0.028707940131425858, -0.010903967544436455,
        0.037150219082832336, -0.022521261125802994, 0.013484842143952847, 0.01856069825589657, -0.016581164672970772,
        -0.0922975018620491, 0.08101563155651093, -0.02562679350376129, 0.015129497274756432, -0.016916770488023758,
        0.06654741615056992, 0.032870981842279434, 0.021113669499754906, -0.010311227291822433, 0.05414431542158127,
        -0.037288848310709, 0.017603809013962746, 0.012559480965137482, -0.06311717629432678, 0.061721473932266235,
        0.001919962465763092, 0.007864858023822308, 0.059220168739557266, 0.01590850204229355, 0.017214152961969376,
        0.03497663885354996, -0.051974281668663025, 0.03385322540998459, -0.03226019814610481, 0.04264325648546219,
        -0.027404533699154854, -0.0076818023808300495, 0.0069345394149422646, -0.007128614000976086,
        -0.02243993617594242, 0.005630862899124622, 0.0022699059918522835, 0.04951808601617813, 0.033807821571826935,
        -0.028823405504226685, 0.013354907743632793, -0.0001516535849077627, 0.027240032330155373, 0.03576334938406944,
        -0.035570185631513596, 0.03521392121911049, -0.051039911806583405, -0.01574028842151165, 0.000548961223103106,
        0.021162278950214386, 0.027995482087135315, -0.006785545032471418, 0.015284174121916294, 0.007160759996622801,
        -0.002057629870250821, 0.010996062308549881, 0.012659885920584202, -0.011335021816194057, 0.00487522641196847,
        -0.012937473133206367, -0.01828492432832718, -0.0049298424273729324, 0.0014238052535802126,
        -0.007056019734591246, -0.010426228865981102, -0.0054637533612549305, -0.0011555077508091927,
        0.04457923024892807, -0.0032390495762228966, 0.0021464882884174585, 0.01563750021159649, 0.02791743166744709,
        0.026249932125210762, 0.06324594467878342, 0.021936027333140373, 0.0302919689565897},
    {-0.32243841886520386, 0.06503517180681229, -0.03388873115181923, -0.06817688792943954, 0.15805280208587646,
        -0.2439982146024704, -0.2437780797481537, 0.17227645218372345, -0.11443445831537247, 0.10512962937355042,
        -0.04329252988100052, -0.21610800921916962, 0.049680355936288834, -0.0320979543030262, -0.09631989151239395,
        -0.08085450530052185, 0.06906990706920624, 0.042417727410793304, 0.07761245220899582, 0.06649885326623917,
        -0.11432237923145294, 0.05754033848643303, -0.12547895312309265, -0.05707795172929764, 0.014392834156751633,
        0.11179720610380173, -0.06472749263048172, 0.014817196875810623, 0.02142360620200634, -0.014675155282020569,
        0.055212657898664474, 0.07782704383134842, 0.1646241694688797, 0.06784312427043915, -0.02848339080810547,
        -0.09108305722475052, -0.03275410458445549, 0.058386314660310745, -0.0887957215309143, -0.0831606388092041,
        0.061150532215833664, 0.156670942902565, 0.11090905964374542, -0.10739486664533615, -0.07731709629297256,
        0.060763224959373474, -0.04042396694421768, -0.010282066650688648, -0.02227780409157276, 0.05575770512223244,
        -0.10386437922716141, 0.040481891483068466, -0.05885593220591545, 0.07496907562017441, -0.12015201151371002,
        -0.08230861276388168, 0.00927529577165842, -0.01662682555615902, -0.03635401278734207, 0.025714639574289322,
        0.018315047025680542, 0.07188540697097778, 0.04007023200392723, 0.08435522764921188, -0.08695252984762192,
        0.06881970167160034, -0.05502260476350784, -0.023787077516317368, 0.07953354716300964, 0.07491771131753922,
        0.008768793195486069, 0.025589311495423317, -0.026273349300026894, 0.00376791856251657, 0.023014113306999207,
        0.04559210315346718, -0.10026287287473679, 0.009204096160829067, -0.09709332883358002, -0.049680568277835846,
        -0.02312549203634262, 0.029893340542912483, 0.0008424107800237834, 0.1110190823674202, 0.03533518686890602,
        -0.04954146221280098, 0.046091560274362564, -0.026568366214632988, 0.03294241055846214, -0.07542821019887924,
        -0.1665361374616623, -0.04886507987976074, 0.052364833652973175, 0.04249461740255356, 0.0415712334215641,
        -0.003947794903069735, -0.024333786219358444, 0.07697892934083939, -0.04293595254421234, -0.02590152621269226,
        0.011808475479483604, 0.0067674461752176285, 0.015145709738135338, 0.006053781136870384, 0.05098186060786247,
        0.012353274039924145, 0.007495752070099115, -0.06267043203115463, 0.031897805631160736, -0.04761189967393875,
        0.03319573029875755, 0.0033815698698163033, -0.023024344816803932, 0.04125543683767319, 0.02687772922217846,
        -0.02436310425400734, 0.0397135429084301, -0.05293130874633789, -0.0028964830562472343, -0.060907501727342606,
        0.04616980999708176, -0.04247147962450981, -0.003626099321991205, 0.03984858840703964, 0.04641812667250633,
        0.013795953243970871, 0.010878006927669048, 0.13037154078483582, 0.04910039156675339, 0.024052519351243973,
        0.028426680713891983, -0.06573314219713211, 0.09259825199842453, -0.026087045669555664, -0.03741271048784256,
        -0.08147348463535309, 0.023643800988793373, -0.05101289227604866, -0.04550085961818695, -0.02648649364709854,
        0.07000020891427994, 0.03835602104663849, -0.007176722399890423, 0.04976469278335571, 0.08121483772993088,
        0.0011364890960976481, 0.02256436087191105, 0.03457007557153702, -0.013958756811916828, -0.020942794159054756,
        0.013352558948099613, 0.04355365037918091, 0.03898025304079056, 0.03877072036266327, -0.037274327129125595,
        0.030386604368686676, -0.028124989941716194, -0.1006012037396431, 0.04517263174057007, 0.07238850742578506,
        0.01820477843284607, -0.032223328948020935, 0.02391681633889675, -0.007913557812571526, 0.013330669142305851,
        -0.010170100256800652, -0.020204395055770874, 0.038441918790340424, -0.05993557721376419, -0.03906434774398804,
        -0.0432521216571331, -0.022592825815081596, -0.015675760805606842, -0.010595750994980335, -0.06771815568208694,
        0.08419705182313919, 0.001336977118626237, -0.07440211623907089, -0.010980297811329365, 0.07518596947193146,
        0.05418891832232475, -0.03624502569437027, 0.0025932304561138153, -0.02923339605331421, -0.04710902273654938,
        0.04770638421177864, 0.041258543729782104, -0.013167401775717735, 0.014464553445577621, -0.014988427981734276,
        -0.026525700464844704, 0.018382037058472633, -0.013692461885511875, 0.01093026902526617, 0.018407370895147324,
        0.001030046259984374, 0.031009119004011154, 0.006854122970253229, 0.052101779729127884, 0.0025991431903094053,
        -0.04805142432451248, -0.023834075778722763, -0.01364744920283556, 0.0029137651436030865,
        -0.0071287439204752445, 0.006774136796593666, 0.03593036159873009, -0.06288384646177292, -0.018057866021990776,
        -0.05890734866261482, 0.022638468071818352, 0.03779199346899986, 0.024623064324259758, 0.0208035446703434,
        0.009259546175599098, -0.007791082374751568, -0.004099440295249224, 0.015174781903624535, 0.01982029154896736,
        0.026148084551095963, -0.003783249529078603, 0.01559017226099968, 0.017470384016633034, -0.021806543692946434,
        -0.04069480299949646, -0.02457944117486477, 0.01790059357881546, 0.01100952085107565, 0.018178550526499748,
        0.007405417971313, -0.011177332140505314, -0.0029185120947659016, 0.016135483980178833, 0.014247587881982327,
        0.04941389709711075, -0.04349081218242645, -0.05872710421681404, 0.042974408715963364, -0.027599036693572998,
        -0.014265523292124271, -0.01130467839539051, -0.01546489167958498, 0.04889893904328346, -0.020453153178095818,
        0.03405647724866867, 0.04033079370856285, -0.005956411361694336, 0.0568084642291069, 0.05014834553003311,
        0.056057777255773544, -0.022467847913503647, 0.061440832912921906, 0.01428884919732809, 0.027748383581638336,
        0.025491653010249138, -0.0003959099994972348},
    {-0.23306921124458313, 0.09526726603507996, -0.07830197364091873, -0.12847815454006195, 0.04407012090086937,
        -0.28247198462486267, -0.12795893847942352, 0.1664678305387497, -0.07502281665802002, 0.09094556421041489,
        0.05167004466056824, -0.3049142062664032, 0.036423876881599426, -0.05074155703186989, 0.09086661785840988,
        -0.06308332830667496, 0.0576363131403923, 0.008877897635102272, 0.09331697970628738, 0.08351977169513702,
        -0.13252857327461243, -0.023468367755413055, -0.15582703053951263, -0.08955491334199905, -0.050252124667167664,
        0.1790728121995926, 0.03600599989295006, 0.024154357612133026, 0.12063935399055481, -0.0069684311747550964,
        0.11192408949136734, 0.1491107940673828, 0.1165517047047615, 0.12062563002109528, 0.002843736205250025,
        0.051321905106306076, 0.014149440452456474, 0.03733734413981438, -0.05129580572247505, -0.2032792568206787,
        -0.014479265548288822, 0.009438594803214073, -0.023182189092040062, -0.08507928997278214, -0.11996761709451675,
        0.050999417901039124, -0.062400903552770615, 0.10563592612743378, 0.05247509852051735, 0.054547328501939774,
        -0.05482960119843483, 0.026225121691823006, -0.04035647213459015, -0.012404119595885277, 0.010036015883088112,
        -0.015856409445405006, 0.05805788189172745, -0.000766415090765804, -0.014922858215868473, 0.052620332688093185,
        0.04750298336148262, 0.0015460599679499865, 0.10214415192604065, 0.034410275518894196, -0.0855550691485405,
        -0.1158519759774208, -0.12072745710611343, 0.03741057217121124, -0.0007082220399752259, -0.03596961870789528,
        0.01740051619708538, -0.01676131971180439, -0.01026944164186716, -0.02766057848930359, -0.0065614329650998116,
        0.0006536070141009986, 0.004994065035134554, -0.025374585762619972, -0.03360956162214279, -0.0714818462729454,
        -0.015807878226041794, 0.057438235729932785, 0.03624061867594719, 0.013581672683358192, 0.022203613072633743,
        -0.03904504328966141, -0.013657256960868835, 0.04566509276628494, 0.05369142070412636, 0.0029769274406135082,
        0.06692127138376236, 0.022947072982788086, 0.01346239261329174, -0.06253784894943237, -0.013161740265786648,
        0.1633397787809372, 0.0233384408056736, 0.005216032266616821, 0.018087968230247498, 0.0047484636306762695,
        0.006331270094960928, -0.0023064063861966133, 0.0700526237487793, -0.00232699210755527, -0.018034033477306366,
        -0.06819351017475128, -0.08925110846757889, -0.09654161334037781, 0.07012734562158585, -0.011959182098507881,
        0.04379205033183098, 0.007724225986748934, -0.04594558849930763, 0.053786877542734146, -0.03510747849941254,
        0.00032080558594316244, -0.06725768744945526, -0.11373676359653473, 0.011675887741148472, 0.03635304421186447,
        -0.038398534059524536, 0.03874051943421364, 0.045734308660030365, -0.08857923001050949, 0.03208493813872337,
        0.010042098350822926, -0.03483465686440468, -0.01981395110487938, -0.031576089560985565, 0.020324725657701492,
        -0.0027972671668976545, -0.002230273559689522, 0.01708069071173668, -0.0518856979906559, -0.020152904093265533,
        -0.00671487208455801, 0.06748296320438385, 0.024136072024703026, -0.012564173899590969, 0.05411083623766899,
        0.04109589383006096, -0.017778180539608, -0.06326368451118469, -0.0350521020591259, -0.0016604504780843854,
        0.010128898546099663, 0.044569745659828186, -0.07065436244010925, -0.089369036257267, 0.02772563137114048,
        0.0711633563041687, -0.017474841326475143, -0.03412826731801033, -0.026953283697366714, -0.030943045392632484,
        0.08576619625091553, -0.04800133407115936, -0.09912628680467606, 0.09202926605939865, 0.02224983461201191,
        0.03455357998609543, 0.03420102596282959, -0.02235778421163559, -0.0015386573504656553, -0.0004711351648438722,
        0.013868491165339947, -0.033098578453063965, 0.046148259192705154, -0.0036120335571467876, 0.02924663946032524,
        -0.014751013368368149, 0.008768116123974323, 0.09195820242166519, -0.019033094868063927, 0.0227478239685297,
        0.0026759719476103783, -0.020063120871782303, -0.09783103317022324, -0.08842628449201584, 0.023973576724529266,
        0.01846235617995262, 0.006657670717686415, 0.02530636452138424, -0.004643816500902176, -0.023216020315885544,
        0.010760331526398659, -0.008490276522934437, -0.010377534665167332, 0.029936593025922775, -0.02365023083984852,
        0.013018133118748665, -0.01940586231648922, -0.03606460243463516, -0.01803823746740818, -0.023706480860710144,
        0.07215657085180283, 0.054793793708086014, 0.08508255332708359, 0.022343875840306282, -0.005405303090810776,
        0.05020616203546524, 0.018927203491330147, -0.0025312970392405987, -0.02594323456287384, -0.0582394078373909,
        0.004060847219079733, 0.006161681842058897, -0.029631344601511955, 0.023296257480978966, 0.015071156434714794,
        0.011220191605389118, 0.03070448525249958, 0.002146362094208598, 0.011862261220812798, -0.004820043686777353,
        -0.010570310987532139, 0.018812699243426323, 0.03353762999176979, 0.031149007380008698, 0.0011052580084651709,
        -0.005567708984017372, -0.04018496349453926, 0.014047623611986637, 0.0001657709217397496, -0.07124771922826767,
        0.050281353294849396, 0.013493482954800129, -0.036040887236595154, 0.02322206273674965, 0.05339059233665466,
        0.04991280287504196, 0.046147704124450684, -0.008218850940465927, -0.024758869782090187, 0.029581917449831963,
        -0.029591746628284454, -0.021421490237116814, -0.00801649410277605, 0.04746389761567116, -0.0500941202044487,
        0.021984152495861053, -0.056174810975790024, -0.02346985600888729, -0.004630844108760357, -0.04785681143403053,
        0.02166779711842537, -0.041735630482435226, 0.013368903659284115, 0.04866459220647812, 0.0003932075051125139,
        0.021060071885585785, -0.053972404450178146, -0.02227686159312725, -0.03680720925331116, 0.006330668926239014,
        -0.05163618177175522},
    {-0.34180155396461487, 0.02533678710460663, 0.03666078299283981, -0.13513201475143433, 0.053876858204603195,
        -0.2244197279214859, -0.21320997178554535, 0.09128864854574203, -0.030306870117783546, 0.155968576669693,
        0.03600025922060013, -0.09295092523097992, 0.05536457896232605, 0.036881785839796066, 0.02209142968058586,
        0.011999589391052723, -0.11353394389152527, 0.008549457415938377, 0.11883518099784851, 0.12295173108577728,
        -0.09323130548000336, -0.031220421195030212, -0.1434752643108368, 0.10186360031366348, 0.07477258890867233,
        0.13929614424705505, 0.01028964202851057, 0.09315548837184906, 0.04545753821730614, 0.04045918211340904,
        0.11290851980447769, 0.01390479039400816, 0.004287223797291517, 0.10177318751811981, 0.12688931822776794,
        -0.014181755483150482, -0.0783468708395958, -0.06170133501291275, -0.016497699543833733, 0.0015452841762453318,
        -0.01037389226257801, 0.1462375372648239, 0.010187444277107716, -0.08159423619508743, -0.010255356319248676,
        0.08761610090732574, -0.10598445683717728, -0.021477922797203064, -0.014499669894576073, -0.08649253845214844,
        -0.0015228779520839453, 0.018030667677521706, 0.08773729205131531, 0.11556008458137512, -0.023209238424897194,
        -0.06638476252555847, -0.014643911272287369, 0.10529317706823349, -0.08322092145681381, 0.04551783949136734,
        0.04740461707115173, -0.050281595438718796, 0.0312550887465477, 0.09918283671140671, -0.16187459230422974,
        0.07100517302751541, -0.06847618520259857, -0.01908370479941368, -0.015123019926249981, -0.030475223436951637,
        0.05846014618873596, -0.054082006216049194, 0.045662716031074524, -0.0106683149933815, 0.06629786640405655,
        0.02264820970594883, -0.07370530068874359, -0.1488797962665558, 0.03917776420712471, 0.041738059371709824,
        -0.12639743089675903, -0.08281537890434265, 0.013313372619450092, -0.07874125242233276, 0.019374489784240723,
        0.009686420671641827, 0.017139924690127373, -0.035212744027376175, 0.04118041694164276, 0.013501234352588654,
        -0.12742078304290771, 0.09125406295061111, 0.036180682480335236, 0.102608822286129, 0.022037846967577934,
        0.0611438974738121, -0.029152171686291695, 0.10089130699634552, 0.01868494413793087, -0.10158465057611465,
        -0.07335067540407181, 0.02832052670419216, -0.026689773425459862, -0.06580916047096252, 0.05999723821878433,
        -0.0482318215072155, -0.009764857590198517, -0.05300958827137947, -0.04270520061254501, -0.024440359324216843,
        0.06219879537820816, -0.006353850942105055, 0.015335110016167164, -0.04117415472865105, 0.15152017772197723,
        -0.029569515958428383, -0.013864526525139809, 0.050955429673194885, -0.001969598699361086, -0.13314726948738098,
        -0.06881025433540344, 0.03362768143415451, -0.005036220885813236, 0.04012630507349968, -0.033981744199991226,
        -0.059663478285074234, 0.012620573863387108, -0.02956375665962696, 0.03744988888502121, 0.07401768863201141,
        -0.05638943240046501, 0.021524891257286072, -0.05448508635163307, -0.014223958365619183, -0.02243431657552719,
        0.06538696587085724, 0.002014980185776949, 0.045656751841306686, 0.002139123622328043, 0.010739930905401707,
        -0.059088949114084244, 0.06374244391918182, 0.026888683438301086, -0.01765270158648491, 0.004428585525602102,
        0.020052989944815636, 0.011125540360808372, 0.033213794231414795, -0.010192048735916615, 0.026890911161899567,
        -0.020937027409672737, -0.014132356271147728, 0.04699888452887535, -0.05239040404558182, 0.034831702709198,
        0.015222802758216858, 0.025597218424081802, 0.027860933914780617, 0.0024443785659968853, -0.001070209895260632,
        0.00621852558106184, 0.03320416063070297, 0.022723114117980003, -0.02977401576936245, -0.010796991176903248,
        0.03548059239983559, 0.008749893866479397, -0.005581078119575977, -0.013285022228956223, -0.029790714383125305,
        -0.016454702243208885, 0.05531647801399231, -0.012831178493797779, 0.019935309886932373, -0.012905663810670376,
        -0.0484672449529171, -0.02491004392504692, 0.026297487318515778, -0.037268638610839844, -0.007291227579116821,
        -0.020448844879865646, -0.07167531549930573, 0.0011399021605029702, -0.019347909837961197, 0.006052926182746887,
        0.02782253921031952, -0.014877322129905224, 0.03032509982585907, -0.03726591169834137, 0.07371212542057037,
        0.03458453714847565, -0.019147291779518127, 0.05363599210977554, -0.0014157480327412486, 0.07196155935525894,
        -0.04098165035247803, -0.06595553457736969, 0.06528495252132416, -0.013133696280419827, 0.016524262726306915,
        0.041814956814050674, 0.09919631481170654, 0.037188638001680374, 0.03557370975613594, -0.011408843100070953,
        -0.028820812702178955, 0.025679156184196472, -0.005659835413098335, 0.08212077617645264,
        -0.00024746867711655796, -0.05054054036736488, -0.028533853590488434, -0.030582258477807045,
        0.012585430406033993, 0.05306782200932503, -0.015229948796331882, 0.057368431240320206, 0.028291456401348114,
        0.0020716660656034946, 0.019219566136598587, 0.07606654614210129, 0.036319550126791, 0.02346498891711235,
        0.03061891533434391, 0.016472460702061653, -0.008959690108895302, 0.05565788969397545, 0.01297779195010662,
        -0.09661725163459778, -0.02110612578690052, 0.007994133047759533, 0.015893345698714256, 0.043171439319849014,
        0.01680651493370533, 0.030289821326732635, -0.035235095769166946, 0.01185435988008976, 0.03990205377340317,
        0.010598193854093552, -0.017494741827249527, 0.049947887659072876, 0.007651945110410452, -0.048362866044044495,
        0.010857087559998035, -0.051590923219919205, -0.0021993485279381275, 0.02204151265323162, -0.031229494139552116,
        -0.03350365161895752, 0.0033836616203188896, 0.015694674104452133, -0.005662263371050358, 0.010961303487420082,
        -0.0005942907300777733, -0.0038282093591988087, -0.0336047038435936},
    {0.15433314442634583, 0.054032936692237854, 0.05342928320169449, 0.18295875191688538, 0.03948220610618591,
        0.22112953662872314, 0.06622807681560516, 0.12311480194330215, -0.10067740827798843, 0.24403619766235352,
        -0.11163999885320663, 0.052330050617456436, 0.07827538996934891, -0.06696511059999466, 0.09967666119337082,
        0.11941978335380554, 0.07549983263015747, 0.18075686693191528, -0.0957430899143219, -0.00897067878395319,
        -0.11004021018743515, -0.09181967377662659, -0.008004521951079369, 0.07596593350172043, -0.062335651367902756,
        0.10781494528055191, -0.015264257788658142, 0.06973592936992645, 0.20719397068023682, -0.016324356198310852,
        0.1768888533115387, 0.05044867470860481, 0.18554535508155823, 0.04865948110818863, 0.07552389800548553,
        0.028709720820188522, -0.0705999881029129, 0.002246459247544408, 0.08270815759897232, -0.014937632717192173,
        -0.08058758080005646, 0.0557357557117939, 0.04991172254085541, -0.04071158170700073, 0.12752927839756012,
        0.10501597076654434, 0.12968777120113373, -0.021706465631723404, -0.04264327138662338, -0.06112031638622284,
        0.10274544358253479, 0.010780120268464088, 0.08551555871963501, 0.0374089740216732, 0.06004846468567848,
        0.0771898552775383, 0.1029907763004303, 0.05908099561929703, -0.05489940196275711, 0.0618363581597805,
        0.12077993154525757, -0.00011368899140506983, -0.004189876839518547, 0.17355193197727203, -0.009792201220989227,
        -0.04456096515059471, -0.052590370178222656, -0.03177936375141144, -0.1381489336490631, 0.012396690435707569,
        -0.09798353165388107, 0.039322834461927414, 0.09018521755933762, 0.0942828580737114, -0.01451829168945551,
        0.0623503215610981, -0.10316672921180725, 0.019175224006175995, 0.10844212025403976, 0.05545589327812195,
        0.0009936365531757474, -0.005759602412581444, 0.012915847823023796, 0.09503762423992157, -0.07639719545841217,
        0.007441316731274128, 0.10128756612539291, -0.013097507879137993, 0.10828669369220734, -0.03134286031126976,
        -0.014767676591873169, 0.07893439382314682, 0.10239342600107193, -0.039282359182834625, 0.13355408608913422,
        0.03445596992969513, -0.06173250079154968, -0.05731720104813576, -0.045328665524721146, 0.17732788622379303,
        -0.0057854303158819675, 0.01793302781879902, -0.05927915871143341, 0.039251670241355896, 0.005819707177579403,
        -0.03998840972781181, 0.1042865514755249, 0.04115544632077217, 0.04967886954545975, -0.008435174822807312,
        0.03820057213306427, -0.018103234469890594, 0.07391580939292908, 0.009429561905562878, -0.026704300194978714,
        -0.010387004353106022, -0.005116105079650879, -0.04259682819247246, -0.03353462740778923,
        -0.0050627076998353004, 0.03157196566462517, -0.04028097540140152, 0.017615709453821182, 0.0023170390632003546,
        0.004754075780510902, -0.03513580188155174, 0.00707838824018836, -0.04769309610128403, -0.003880416275933385,
        -0.005189509596675634, 0.03448808565735817, -0.004740430042147636, -0.013114361092448235,
        -0.0007961358060128987, -0.007205401547253132, -0.0028563118539750576, -0.02248665690422058,
        -0.025085147470235825, -0.0795157253742218, -0.0019571343436837196, -0.009661297313869, 0.05599658936262131,
        0.04408097267150879, -0.06791433691978455, 0.06677486002445221, 0.05766028165817261, -0.001414168276824057,
        0.0023792930878698826, -0.0332941934466362, -0.0010880372719839215, -0.09514862298965454, 0.008585544303059578,
        0.037309713661670685, 0.036315158009529114, -0.0202076006680727, 0.051380984485149384, 0.05859878286719322,
        0.01712116412818432, -0.009357762522995472, 0.029282551258802414, 0.03593525290489197, 0.0015879131387919188,
        -0.007023144979029894, 0.037992533296346664, 0.017192676663398743, 0.005138549953699112, -0.05678017809987068,
        0.025553330779075623, 0.05606899410486221, -0.007957251742482185, 0.0003748558519873768, -0.029160039499402046,
        0.02264704927802086, 0.043211910873651505, 0.04377852380275726, 0.016944672912359238, -0.03410213440656662,
        -0.004106986336410046, -0.03549211099743843, -0.004769656807184219, 0.004179608076810837, -0.020045286044478416,
        0.02186979167163372, -0.01940617896616459, 0.01056763157248497, 0.023064492270350456, -0.07047932595014572,
        0.04248915612697601, 0.028301287442445755, 0.016692332923412323, -0.011009524576365948, -0.0279121994972229,
        -0.024198710918426514, -0.04231858626008034, -0.004893096163868904, 0.04222963750362396, 0.011364261619746685,
        -0.03291577845811844, -0.010198797099292278, 0.007706050761044025, -0.043085791170597076, -0.04399697110056877,
        -0.007751392666250467, 0.013324620202183723, -0.0071948375552892685, -0.033415403217077255,
        0.026766009628772736, 0.04326672479510307, -0.018080255016684532, -0.033673956990242004, 0.000598935061134398,
        0.0010166603606194258, -0.0034815878607332706, -0.03000551275908947, -0.017780926078557968,
        -0.012357347644865513, 0.05792362987995148, -0.0035247327759861946, -0.03178674727678299, -0.018492572009563446,
        0.0017248360672965646, 0.05020378902554512, -0.02048276923596859, 0.0077034407295286655, 0.039670974016189575,
        -0.021927490830421448, -0.022507622838020325, 0.01924610696732998, 0.03543926775455475, 0.012073997408151627,
        0.010666519403457642, -0.028007937595248222, -0.026832664385437965, 0.027306590229272842, -0.03918386623263359,
        -0.023099912330508232, -0.007076159585267305, -0.04582725092768669, -0.016414141282439232,
        -0.019597426056861877, -0.011014189571142197, -0.05931577831506729, 0.016082167625427246, -0.029945041984319687,
        -0.012334608472883701, -0.052573319524526596, -0.030065147206187248, -0.013084043748676777,
        -0.01456295233219862, 0.02004406228661537, -0.026521217077970505, -0.020244043320417404, 0.01618902198970318,
        0.0002963162260130048, 0.0023189045023173094, 0.011202109977602959},
    {0.09021931141614914, 0.07016117125749588, -0.0010901216883212328, 0.13442304730415344, -0.024345485493540764,
        0.2896796464920044, -0.059495192021131516, 0.11495377868413925, -0.0038813934661448, 0.31692853569984436,
        -0.07328909635543823, 0.007006822619587183, 0.10744226723909378, -0.09754575788974762, 0.006917884107679129,
        0.13970836997032166, 0.008295265957713127, 0.08180001378059387, -0.06330828368663788, -0.007850121706724167,
        -0.04648716002702713, -0.02667389251291752, -0.0639600083231926, 0.03018946386873722, -0.0262310728430748,
        0.03629599139094353, -0.0031164956744760275, 0.06235441938042641, 0.15765376389026642, -0.008891614153981209,
        0.2019534558057785, 0.03292324021458626, 0.1589742749929428, -0.013525133952498436, -0.10173320025205612,
        0.0229911208152771, -0.07008981704711914, -0.014253410510718822, 0.041102830320596695, -0.03343123197555542,
        -0.08062750846147537, 0.12572863698005676, 0.015000586397945881, -0.053494520485401154, 0.06106523051857948,
        -0.0035271397791802883, -0.07744819670915604, -0.017640400677919388, -0.01796841435134411, -0.10866880416870117,
        0.04304586723446846, -0.08709879219532013, 0.01171020232141018, 0.03452286496758461, 0.047189369797706604,
        0.025720663368701935, 0.11225428432226181, -0.007236547768115997, 0.09290864318609238, 0.05200301110744476,
        0.057194024324417114, -0.08752529323101044, -0.016276340931653976, 0.24292074143886566, 0.0858362689614296,
        0.020871082320809364, -0.054783377796411514, -0.06400663405656815, -0.05940570682287216, 0.023382745683193207,
        0.0019329942297190428, 0.0849551185965538, 0.14652101695537567, -0.09776438027620316, 0.05874442309141159,
        -0.016378354281187057, -0.11952629685401917, 0.005321010947227478, 0.003615583060309291, 0.013267767615616322,
        -0.07903995364904404, 0.0034063910134136677, 0.055941179394721985, 0.10307147353887558, 0.02231755293905735,
        -0.044774774461984634, 0.07059527188539505, -0.003011701861396432, 0.1007838025689125, 0.05646316707134247,
        -0.09039917588233948, 0.07764909416437149, 0.04439360275864601, 0.026369662955403328, 0.0651133731007576,
        0.002235173247754574, -0.04866126552224159, -0.0617138110101223, 0.06716679781675339, 0.058693818747997284,
        -0.010741027072072029, -0.011632080189883709, -0.07721058279275894, 0.08376244455575943, 0.0333944633603096,
        0.053243741393089294, 0.05269549414515495, -0.0030393321067094803, 0.04438440874218941, -0.0031889001838862896,
        0.03416978567838669, 0.03121434710919857, -0.03487594053149223, -0.052705708891153336, 0.00452985055744648,
        0.10179086029529572, 0.03485307842493057, 0.09078691154718399, -0.012620684690773487, 0.006316705606877804,
        0.0457141250371933, 0.09293971955776215, 0.04673336073756218, -0.02763863280415535, 0.02103482559323311,
        -0.0075634499080479145, -0.009520028717815876, -0.06981003284454346, 0.011698384769260883, 0.02674861066043377,
        0.11242856085300446, 0.04413197562098503, 0.014917371794581413, 0.033953603357076645, 0.01799178123474121,
        0.09853741526603699, 0.051563262939453125, -0.009368672966957092, -0.02170637622475624, -0.032125744968652725,
        0.054268285632133484, 0.01954837702214718, 0.0667152851819992, 0.06587234884500504, 0.06545985490083694,
        -0.022939525544643402, 0.09407893568277359, -0.020160779356956482, -0.0817580372095108, 0.013188358396291733,
        -0.05618254840373993, 0.06722839921712875, -0.018594298511743546, 0.09965387731790543, -0.033317629247903824,
        0.0494559071958065, -0.04462136700749397, 0.016268393024802208, 0.0035487946588546038, -0.026441115885972977,
        -0.013695440255105495, -0.03485829010605812, -0.023755326867103577, -0.055243149399757385,
        0.0024068206548690796, 0.05479288846254349, -0.021997136995196342, -0.010416464880108833, 0.006585696712136269,
        -0.005429042503237724, 0.09464841336011887, -0.003524580504745245, 0.009963816963136196, 0.0689268633723259,
        -0.02616758830845356, -0.07606415450572968, 0.04137379676103592, 0.038003794848918915, -0.002887116512283683,
        0.007104061543941498, 0.030483530834317207, -0.040320612490177155, 0.04607358202338219, 0.020755717530846596,
        0.04311928153038025, 0.00855250284075737, 0.058060478419065475, 0.07576357573270798, -0.03155878558754921,
        -0.016486819833517075, -0.08583077043294907, 0.011046230792999268, 0.04247983172535896, -0.04187515750527382,
        0.013044383376836777, -0.009341882541775703, 0.01656428538262844, 0.002917467150837183, -0.007622893899679184,
        -0.005219192244112492, 0.008413796313107014, -0.07505299150943756, -0.011029740795493126, -0.015466121956706047,
        -0.01847101002931595, -0.004528270568698645, -0.0070590064860880375, -0.0056611099280416965, 0.0231806468218565,
        0.021137740463018417, 0.020113766193389893, 0.05790907144546509, -0.045077595859766006, -0.011901297606527805,
        0.048590321093797684, 0.06039527803659439, 0.06596759706735611, 0.04780726507306099, -0.011883756145834923,
        0.03469827026128769, -0.003956938162446022, -0.013747570104897022, 0.02026248350739479, 0.040406301617622375,
        0.04946912080049515, 0.014623232185840607, -0.043708328157663345, -0.013930237852036953, -0.0016824633348733187,
        0.01826196163892746, 0.01775713637471199, -0.021030832082033157, 0.018392527475953102, 0.012077538296580315,
        -0.008260715752840042, 0.037941306829452515, 0.024844728410243988, 0.004287404473870993, -0.01459487248212099,
        -0.023020131513476372, 0.026326972991228104, 0.04695694521069527, -0.014952152036130428, -0.07610002905130386,
        -0.05695825815200806, 0.003147609531879425, -0.014454366639256477, 0.06699289381504059, -0.01988336071372032,
        -0.03546140342950821, -0.029868867248296738, -0.019986949861049652, 0.015115078538656235, 0.009530194103717804,
        0.005041111260652542, -0.04736171290278435},
    {0.16314218938350677, 0.05615178123116493, -0.010532560758292675, 0.16748426854610443, -0.015101027674973011,
        0.17892715334892273, 0.0073465583845973015, 0.11318451166152954, 0.040786419063806534, 0.24237823486328125,
        -0.10793037712574005, 0.06191352382302284, 0.127462700009346, -0.017736569046974182, 0.017707472667098045,
        0.13199396431446075, 0.07841818034648895, 0.12522050738334656, -0.08957020938396454, 0.14591561257839203,
        -0.05022333562374115, -0.1143253892660141, -0.017503349110484123, 0.08137829601764679, 0.004563444759696722,
        0.039303719997406006, 0.0605909526348114, 0.03918279707431793, 0.18399979174137115, 0.022635485976934433,
        0.13643597066402435, 0.047024089843034744, 0.17289265990257263, -0.026417264714837074, 0.017559083178639412,
        0.12318264693021774, -0.13484081625938416, -0.0115977106615901, 0.10539772361516953, 0.02371501550078392,
        -0.08033906668424606, 0.11739088594913483, -0.03264264762401581, 0.013826289214193821, 0.12985765933990479,
        -0.004794710781425238, 0.054756611585617065, -0.01670481450855732, -0.021108217537403107, -0.020905902609229088,
        0.029386049136519432, -0.014505471102893353, 0.0758223831653595, 0.0511074922978878, 0.10300794243812561,
        0.07735810428857803, 0.13310053944587708, 0.04821064695715904, -0.018455607816576958, 0.05718744173645973,
        0.15615785121917725, -0.0931672602891922, -0.05807837098836899, 0.1880522221326828, 0.03881456330418587,
        -0.1121210977435112, -0.07179174572229385, -0.05854616314172745, -0.21501895785331726, -0.0014925000723451376,
        0.02964692935347557, 0.0926436185836792, 0.13677047193050385, -0.0013957505580037832, 0.05507953092455864,
        -0.08244320005178452, -0.07490532845258713, -0.03705575317144394, 0.005215792916715145, 0.041653797030448914,
        -0.017728587612509727, -0.03918757289648056, 0.011436285451054573, 0.0745072141289711, -0.012024583294987679,
        -0.03178338333964348, 0.04554372653365135, -0.02429201453924179, 0.05728887766599655, -0.03362821042537689,
        -0.02980990894138813, 0.05985426902770996, 0.028651872649788857, -0.02575630322098732, 0.03815619274973869,
        0.10661986470222473, -0.05997146666049957, -0.0926743894815445, 0.017269110307097435, 0.0823960080742836,
        0.03391492739319801, -0.007102483883500099, 0.016146449372172356, -0.02625918574631214, 0.004419155418872833,
        -0.023372260853648186, -0.01347494125366211, 0.017847178503870964, 0.02238423004746437, -0.006818997208029032,
        -0.010807955637574196, 0.003601407166570425, -0.015043686144053936, 0.023035673424601555, -0.04026871174573898,
        0.0016899650217965245, 0.04089730232954025, 0.029656922444701195, 0.12029261142015457, 0.0388636514544487,
        0.08798716962337494, 0.11213438957929611, 0.06799213588237762, 0.011466770432889462, 0.014838157221674919,
        -0.020201895385980606, -0.014182626269757748, -0.0462062805891037, 0.003256786847487092, -0.05837886407971382,
        0.08287185430526733, -0.005769363138824701, 0.00019564677495509386, -0.0423000194132328, -0.008119593374431133,
        0.029585100710392, 0.020614497363567352, -0.09522125869989395, -0.06855719536542892, -0.028684383258223534,
        0.025235384702682495, -0.07118884474039078, 0.037841882556676865, 0.008711881935596466, 0.02666362375020981,
        0.00820996705442667, -0.01823263242840767, -0.018983792513608932, -0.10183415561914444, 0.004125982988625765,
        -0.012124324217438698, 0.04355071112513542, -0.02148980088531971, 0.04342525452375412, -0.0753679871559143,
        0.05674904212355614, -0.006988960783928633, 0.0432707704603672, 0.013515115715563297, -0.04959102347493172,
        -0.002960151294246316, -0.029514042660593987, -0.03865368664264679, -0.041379284113645554, -0.05182411149144173,
        0.08122466504573822, -0.0206978190690279, 0.008876374922692776, -0.046009406447410583, -0.034108966588974,
        0.012975981459021568, 0.021608253940939903, 0.029914846643805504, 0.060095787048339844, 0.08068282902240753,
        0.006795130670070648, 0.018548347055912018, -0.05169818550348282, 0.05963628366589546, -0.018067888915538788,
        0.03107716701924801, -0.04780270904302597, -0.009180296212434769, 0.023746637627482414, -0.02277030609548092,
        0.06076255440711975, -0.022381143644452095, 0.07660075277090073, 0.027881184592843056, -0.005856708623468876,
        -0.020493673160672188, 0.004840684123337269, -0.017343509942293167, -0.02103942446410656, -0.006152646616101265,
        0.046746086329221725, -0.061509255319833755, -0.0037205738481134176, -0.005057184956967831,
        0.0017094783252105117, 0.0014028068399056792, -0.06636199355125427, 0.026805102825164795, -0.10263268649578094,
        0.030833527445793152, 0.03357371687889099, -0.061936598271131516, 0.015529251657426357, 0.013374406844377518,
        -0.04943963140249252, 0.021813293918967247, -0.010913628153502941, -0.035212401300668716, 0.019296515733003616,
        -0.018651505932211876, -0.01524303387850523, 0.0488630011677742, -0.017665034160017967, -0.03707475960254669,
        0.016525404527783394, -0.0028650425374507904, -0.014503546059131622, -0.0145350256934762, 0.07897070795297623,
        0.018642734736204147, 0.009186946786940098, -0.031031571328639984, 0.048151493072509766, 0.0427398644387722,
        -0.0091561796143651, -0.0010126614943146706, 0.00043324497528374195, -0.05938667804002762, 0.04627208411693573,
        0.0037369581405073404, 0.03699687123298645, -0.0013126259436830878, 0.006404017563909292,
        -0.0069097066298127174, -0.011400368995964527, -0.022944077849388123, -0.01987585611641407,
        -0.03395337238907814, 0.01567836105823517, -0.017816059291362762, -0.03934546932578087, -0.035058073699474335,
        0.0010009834077209234, 0.017050620168447495, -0.06671023368835449, -0.03499315679073334, -0.04521815478801727,
        0.0281609408557415, 0.004620449151843786, 0.007994680665433407, -0.00712593924254179},
    {0.138726145029068, 0.0865996703505516, 0.022818056866526604, 0.10920017957687378, -0.024274010211229324,
        0.158567413687706, 0.08842840045690536, 0.11469797790050507, -0.12412703782320023, 0.2987560033798218,
        -0.04979976639151573, 0.08194214105606079, 0.18815116584300995, -0.0834268257021904, 0.0649072602391243,
        0.12835903465747833, 0.10418090969324112, 0.08070370554924011, -0.08068583160638809, 0.11696811765432358,
        -0.03314678370952606, -0.10350920259952545, -0.0021249516867101192, 0.11110801994800568, -0.020347418263554573,
        0.012836056761443615, -0.0147421108558774, 0.04850191995501518, 0.28887951374053955, 0.03710705786943436,
        0.15309202671051025, 0.05815737694501877, 0.1486026495695114, -0.10356075316667557, -0.025628680363297462,
        -0.042705751955509186, -0.13407784700393677, 0.038305725902318954, 0.011045108549296856, -0.016850629821419716,
        -0.029060902073979378, 0.08857680857181549, 0.08666670322418213, 0.10060235857963562, 0.15459546446800232,
        0.059409502893686295, 0.039329059422016144, -0.055964790284633636, -0.0393063984811306, 0.018291227519512177,
        0.04025673121213913, -0.01974448189139366, 0.00022248085588216782, 0.04520319402217865, 0.125096395611763,
        0.0873364508152008, 0.09587427228689194, -0.04730328917503357, -0.030483804643154144, 0.04758092015981674,
        0.2100020796060562, 0.026933174580335617, 0.0482303686439991, 0.15056510269641876, 0.035126861184835434,
        -0.03632441163063049, -0.06718684732913971, -0.10626809298992157, -0.04692118242383003, -0.00033721557701937854,
        0.048758555203676224, 0.08914171904325485, 0.0771753117442131, -0.04522727057337761, -0.02861839532852173,
        -0.021755024790763855, -0.011670163832604885, -0.0035954101476818323, 0.10652622580528259, 0.05801974609494209,
        -0.09695744514465332, 0.0007768938085064292, 0.08405522257089615, 0.049486786127090454, -0.0014669881202280521,
        -0.05508897081017494, 0.12299232929944992, -0.0747041180729866, 0.1147742047905922, 0.0037290106993168592,
        0.018210196867585182, 0.06979767978191376, 0.038802266120910645, -0.0783943384885788, 0.0615210235118866,
        0.10446664690971375, -0.058791130781173706, 0.0015328162116929889, -0.0445830374956131, 0.03925525024533272,
        0.059612881392240524, 0.052937619388103485, 0.008696042001247406, -0.01617085374891758, 0.08777380734682083,
        0.029535658657550812, -0.02396349236369133, 0.014840633608400822, -0.02793315052986145, -0.03604250028729439,
        0.004000761546194553, -0.03616968169808388, -0.002419113414362073, 0.033083945512771606, -0.029023371636867523,
        -0.04769432544708252, 0.0017429889412596822, -0.06892836093902588, 0.04430681839585304, 0.052991099655628204,
        0.03372406214475632, 0.019905952736735344, 0.04342947527766228, 0.03552340343594551, -0.054672375321388245,
        -0.05030686780810356, 0.029112691059708595, 0.023248257115483284, -0.019074518233537674, 0.0030444650910794735,
        0.040810540318489075, -0.027641551569104195, -0.0210200697183609, 0.0020585726015269756, -0.04079742357134819,
        0.11820816248655319, -0.05499528720974922, -0.045343510806560516, -0.010945316404104233, -0.040487900376319885,
        -0.008059108629822731, 0.05102485045790672, -0.0455796979367733, 0.0190082099288702, 0.015639590099453926,
        0.018135376274585724, -0.007257305085659027, 0.033511027693748474, -0.03699062019586563, 0.010323247872292995,
        -0.014340193010866642, 0.08024248480796814, -0.06658519804477692, 0.0334254689514637, -0.03157343342900276,
        0.06298686563968658, 0.010147452354431152, -0.04308617487549782, -0.06025736406445503, 0.01576130837202072,
        0.028919173404574394, 0.02112697809934616, 0.016009289771318436, -0.04103802889585495, 0.0052783736027777195,
        -0.023729657754302025, -0.0217489805072546, -0.014845442958176136, -0.012792936526238918, -0.010212269611656666,
        -0.028813326731324196, 0.016652127727866173, -0.013542004860937595, 0.08241952955722809, -0.02232193574309349,
        0.017829928547143936, 0.03220869600772858, 0.04894747957587242, -0.0034851704258471727, -0.018884308636188507,
        0.018927790224552155, -0.09442877024412155, -0.011323639191687107, 0.05381431803107262, -0.03803348168730736,
        -0.04221956804394722, -0.001475048833526671, -0.001373356208205223, -0.009165686555206776, 0.002658452372997999,
        -0.04001506417989731, -0.04226957634091377, 0.02799442782998085, -0.07417106628417969, 0.024072518572211266,
        -0.009169148281216621, -0.016195127740502357, 0.00528370076790452, -0.009986587800085545, -0.017760546877980232,
        -0.026201864704489708, 0.016902070492506027, 0.010969938710331917, -0.010721592232584953, -0.023999832570552826,
        0.021571151912212372, -0.04904699698090553, 0.06282468140125275, -0.013850634917616844, 0.014793398790061474,
        0.0328550823032856, 0.02579113282263279, -0.017683271318674088, -0.037672773003578186, -0.03900304064154625,
        -0.018468379974365234, 0.0373123399913311, -0.014817186631262302, -0.03305235877633095, -0.004162858240306377,
        -0.007770541589707136, 0.0024523555766791105, -0.012238201685249805, 0.01275879517197609, -0.014817462302744389,
        0.04972965642809868, 0.0046787019819021225, 0.01758650690317154, 0.027681931853294373, -0.016380464658141136,
        -0.003965819254517555, 0.004028699826449156, -0.09060055017471313, 0.010999524034559727, -0.03961264714598656,
        0.023542718961834908, -0.020802995190024376, 0.009288058616220951, -0.025454046204686165, 0.0055731069296598434,
        0.021279018372297287, -0.0178329199552536, -0.04883981868624687, 0.024782754480838776, -0.006199068855494261,
        -0.021427804604172707, -0.003020988777279854, 0.016504092141985893, 0.00878588855266571, -0.04163431003689766,
        0.004832691978663206, 0.004208934959024191, -0.027218056842684746, -0.017651014029979706, 0.035928305238485336,
        0.009402988478541374},
    {0.16573821008205414, 0.06877116858959198, -0.024512464180588722, 0.15308527648448944, -0.023779647424817085,
        0.26529091596603394, -0.031995486468076706, 0.10056058317422867, -0.012774057686328888, 0.30632269382476807,
        -0.1403466761112213, 0.11047274619340897, 0.11546027660369873, -0.0622270368039608, 0.10047215968370438,
        0.1680879443883896, 0.03920263797044754, 0.06268013268709183, -0.12171677500009537, -0.016705667600035667,
        -0.03297998756170273, -0.09409506618976593, 0.042085133492946625, 0.09536496549844742, -0.035465408116579056,
        0.04707224667072296, 0.07685402780771255, 0.058447662740945816, 0.20209155976772308, -0.02827707678079605,
        0.18473516404628754, 0.06645021587610245, 0.1693553775548935, 0.10574671626091003, 0.013800021260976791,
        0.125718355178833, -0.06364039331674576, -0.00817776471376419, 0.06596829742193222, 0.11982575803995132,
        -0.08347677439451218, 0.09191977977752686, 0.04103207215666771, -0.020269988104701042, 0.13432569801807404,
        -0.01906207576394081, -0.004396469797939062, 0.06121758371591568, -0.03316330909729004, 0.011184667237102985,
        0.12280193716287613, -0.096310555934906, 0.04949205741286278, 0.06471583992242813, 0.1126190721988678,
        0.052007194608449936, 0.10223572701215744, 0.017634501680731773, 0.005003730766475201, 0.09068486839532852,
        0.1420845091342926, 0.07963120192289352, 0.05427907034754753, 0.14566396176815033, 0.048095278441905975,
        -0.10113558173179626, -0.04779857397079468, -0.13092181086540222, -0.13556534051895142, -0.019328199326992035,
        0.04540524259209633, 0.09227664768695831, 0.09725982695817947, 0.08552409708499908, 0.01652597449719906,
        -0.012946153990924358, -0.10200174152851105, 0.02588493376970291, 0.09776066988706589, 0.08080273121595383,
        0.003473440883681178, -0.05299924686551094, 0.12064594030380249, 0.10045724362134933, -0.010512575507164001,
        -0.0953846126794815, 0.042677897959947586, 0.04969288036227226, 0.042885564267635345, -0.041186463087797165,
        -0.029028596356511116, 0.09079665690660477, 0.052351124584674835, 0.03817687928676605, 0.0011494782520458102,
        0.05820201709866524, -0.04585632309317589, -0.07414479553699493, -0.06331772357225418, -0.008673792704939842,
        0.029603563249111176, 0.05852308124303818, -0.046252284198999405, 0.0182326827198267, 0.008793910034000874,
        -0.02297850325703621, 0.03294270113110542, 0.0554802305996418, -0.020867271348834038, 0.001364343916065991,
        -0.02424606867134571, 0.030780619010329247, 0.006045373156666756, -0.01815066672861576, -0.10526468604803085,
        0.02596515603363514, -0.004818713758140802, -0.015579486265778542, -0.041573408991098404, 0.022128093987703323,
        0.0018219631165266037, -0.023540902882814407, 0.04487321525812149, -0.006598731502890587, -0.020360806956887245,
        -0.039003580808639526, -0.02073141187429428, 0.030836954712867737, -0.02203141525387764, -0.020360397174954414,
        0.04227365180850029, -0.09080272912979126, -0.016302285715937614, 0.04718843474984169, -0.003496046643704176,
        0.005227161105722189, 0.06461627036333084, -0.029716074466705322, -0.022237757220864296, -0.010900870896875858,
        0.030576437711715698, -0.021349843591451645, -0.05679883062839508, -0.008885147050023079, 0.038865022361278534,
        -0.0016160864615812898, -0.018496638163924217, 0.02876075729727745, -0.010924043133854866, -0.04999614134430885,
        0.003006929764524102, 0.062060944736003876, -0.005317754112184048, 0.0008302743663080037, 0.01139583345502615,
        0.020666195079684258, -0.029009083285927773, 0.06253843009471893, 0.06665422022342682, 0.010779563337564468,
        0.0017573946388438344, -0.008950427174568176, 0.024610424414277077, -0.022239575162529945,
        0.0022280062548816204, 0.021885881200432777, -0.020455414429306984, -0.01739884354174137,
        -0.0012576713925227523, -0.05201170966029167, 0.0075926450081169605, 0.03164948895573616, -0.015043898485600948,
        0.03420811891555786, 0.03150838613510132, 0.02996780350804329, -0.017286276444792747, -0.051445845514535904,
        0.0548296682536602, -0.02350318804383278, -0.03956610709428787, -0.024588575586676598, 0.012904912233352661,
        -0.003711721859872341, 0.023487024009227753, 0.0039887335151433945, 0.023562168702483177, 0.014770257286727428,
        -0.03420187160372734, -0.03679952397942543, -0.01678931899368763, -0.006430871784687042, -0.011849450878798962,
        -0.0025710912887007, -0.01908976398408413, 0.0015973960980772972, -0.023250989615917206, -0.012793155387043953,
        0.00038909801514819264, -0.007981525734066963, 0.009408976882696152, -0.030921729281544685,
        -0.035538509488105774, -0.036859072744846344, 0.021486053243279457, 0.02425730973482132, -0.054610639810562134,
        -0.020391985774040222, -0.0038614757359027863, 0.028902573511004448, -0.008801809512078762,
        -0.017483321949839592, -0.050125595182180405, -0.030481571331620216, 0.0043569947592914104,
        0.020709708333015442, 0.03717876598238945, 0.0017607174813747406, -0.022800449281930923, 0.014083506539463997,
        0.017900701612234116, -0.01417913194745779, 0.030019771307706833, 0.022980740293860435, 0.011206435970962048,
        -0.006238542962819338, 0.01787872239947319, 0.017733432352542877, 0.025324622169137, 0.025381721556186676,
        -0.018892815336585045, -0.006264131050556898, -0.02928796224296093, 0.02348346635699272, 0.013650174252688885,
        0.012503263540565968, 0.005425323266535997, -0.014109357260167599, 0.010115341283380985, 0.011944466270506382,
        0.0006775215151719749, 0.003474863013252616, -0.02121458575129509, -0.04375713691115379, -0.04049747809767723,
        0.0003338698297739029, -0.009228946641087532, 0.011622109450399876, 0.03633980453014374, 0.012697761878371239,
        0.04267511144280434, -0.008645952679216862, 0.02357933297753334, 0.026194235309958458, 0.009634980000555515,
        -0.0041539352387189865},
    {0.14368446171283722, 0.06948216259479523, -0.026190388947725296, 0.03615943714976311, -0.051077701151371,
        0.15629617869853973, 0.06919106096029282, 0.1537628471851349, 0.025866296142339706, 0.21140940487384796,
        -0.12395473569631577, 0.097295843064785, 0.08388106524944305, 0.0897907018661499, 0.1496247500181198,
        0.18553432822227478, 0.004762984346598387, 0.10357257723808289, -0.0412759929895401, 0.059669721871614456,
        -0.023762457072734833, -0.0707278773188591, 0.025435397401452065, 0.06356333196163177, 0.09258712828159332,
        -0.00923105888068676, 0.010873276740312576, 0.05792077258229256, 0.2048366516828537, 0.008702452294528484,
        0.12087637931108475, 0.09371370077133179, 0.11266572773456573, 0.009960931725800037, -0.015673082321882248,
        0.011743536219000816, -0.12011586129665375, 0.09554535150527954, 0.06911837309598923, 0.07110601663589478,
        0.015479948371648788, 0.041764892637729645, 0.07104486972093582, -0.019426865503191948, 0.1312023550271988,
        -0.011612584814429283, 0.04609192907810211, 0.017356442287564278, -0.029955828562378883, -0.0023414073511958122,
        0.05461146682500839, -0.1313098520040512, 0.038623273372650146, 0.09032487124204636, 0.0994390994310379,
        0.08251897245645523, 0.0604962557554245, 0.00924333743751049, -0.07111847400665283, 0.10675983130931854,
        0.20239733159542084, 0.0534067377448082, -0.06461793184280396, 0.14665964245796204, 0.07144829630851746,
        -0.16738532483577728, -0.04211602732539177, 0.023273488506674767, -0.11310956627130508, 0.048904068768024445,
        -0.03433304652571678, 0.11062560975551605, 0.113495372235775, 0.06891326606273651, 0.029847370460629463,
        -0.02308540605008602, -0.02686723880469799, -0.13309107720851898, 0.08427061140537262, 0.08338772505521774,
        -0.16258229315280914, -0.03240540251135826, 0.03836004063487053, 0.09148707240819931, -0.010973053984344006,
        -0.10148205608129501, 0.09548812359571457, 0.02848677523434162, 0.049758486449718475, -0.011150872334837914,
        -0.10557813197374344, 0.027593186125159264, 0.049085285514593124, 0.047223400324583054, 0.0749441459774971,
        -0.04651368409395218, -0.0180650744587183, -0.021804459393024445, -0.08418126404285431, 0.09470492601394653,
        0.013150532729923725, 0.09405636042356491, -0.06032254174351692, 0.02259901352226734, -0.10296870768070221,
        -0.010797887109220028, 0.05277122184634209, 0.036586228758096695, -0.007089326158165932, -0.037477947771549225,
        0.022384438663721085, -0.0760861188173294, 0.02114877663552761, 0.040313705801963806, -0.030639201402664185,
        -0.03438195586204529, -0.02107934281229973, -0.08715282380580902, 0.02054762654006481, 0.07267158478498459,
        0.05242683365941048, 0.054158441722393036, 0.07408519089221954, 0.07330887019634247, 0.06033245101571083,
        -0.006902290508151054, 0.018413595855236053, -0.05565398931503296, -0.03168618679046631, -0.05918528512120247,
        0.06952328234910965, -0.0758298859000206, 0.03195492923259735, 0.037397369742393494, -0.011757836677134037,
        0.04817675054073334, -0.0038897169288247824, -0.022228511050343513, -0.024174954742193222, 0.02580563724040985,
        0.04985729604959488, 0.035787809640169144, -0.005445550661534071, -0.02108205109834671, 0.04395484924316406,
        -0.007354694418609142, -0.025189487263560295, 0.015093890950083733, 0.00098244973924011, -0.03402477502822876,
        -0.00891425646841526, 0.014553737826645374, 0.009309804067015648, 0.04203730821609497, -0.029643014073371887,
        0.031264450401067734, 0.04808272048830986, -0.07020660489797592, -0.0018192961579188704, 0.015412948094308376,
        0.0015562312910333276, 0.012361323460936546, -0.001289301086217165, -0.0033175263088196516,
        -0.05577946826815605, -0.05594826862215996, -0.06348562985658646, 0.0275405365973711, -0.03041599504649639,
        -0.016222035512328148, 0.03342806547880173, 0.0637185201048851, -0.056483134627342224, -0.01834157668054104,
        0.023765230551362038, -0.03323042020201683, 0.032751500606536865, -0.004230400547385216, 0.019029363989830017,
        -0.029484722763299942, -0.019648125395178795, 0.011945236474275589, 0.023303991183638573, 0.028129111975431442,
        0.043509308248758316, 0.002220400143414736, -0.04409756138920784, -0.014029843732714653, 0.015930110588669777,
        -0.003976671490818262, -0.0006284796982072294, -0.03849920257925987, 0.04095269367098808, -0.07962260395288467,
        0.03784417733550072, 0.010294600389897823, -0.010047431103885174, -0.05292801931500435, 0.007472587749361992,
        -0.008137143217027187, 0.051644060760736465, -0.03800482302904129, -0.03771480917930603, -0.018460728228092194,
        -0.03283620625734329, -0.013703803531825542, -0.06010884791612625, -0.002967404667288065, -0.030612671747803688,
        0.005111435893923044, 0.029439551755785942, -0.10528036206960678, 0.016915272921323776, -0.004662751220166683,
        -0.06622511893510818, 0.030052436515688896, 0.038656219840049744, -0.03604273498058319, -0.005053170491009951,
        -0.09115864336490631, -0.03278254345059395, -0.06075287610292435, -0.009579077363014221, 0.06196507066488266,
        -0.004882157780230045, -0.03406611457467079, -0.02301047556102276, 0.0075153931975364685, 0.004894239362329245,
        -0.03402533009648323, 0.033856350928545, -0.010859259404242039, -0.009318085387349129, 0.008205587044358253,
        -0.06358614563941956, 0.03872948884963989, -0.052955131977796555, -0.006433952134102583, 0.017010359093546867,
        0.003466369118541479, 0.006478655152022839, 0.028184061869978905, 0.014948106370866299, -0.0834944024682045,
        -0.049326129257678986, -0.00047415363951586187, -0.02127397432923317, 0.02172071486711502, 0.01173438224941492,
        0.044113848358392715, 0.0025961450301110744, -0.018071558326482773, 0.01749461703002453, 0.009146315976977348,
        -0.050742242485284805, -0.009916797280311584},
    {0.1298094540834427, 0.07956395298242569, 0.011595455929636955, 0.05173901841044426, -0.0026993080973625183,
        0.1398932933807373, 0.007261330261826515, 0.14378978312015533, 0.005862004589289427, 0.30864158272743225,
        -0.06623237580060959, -0.02745087631046772, 0.10048869252204895, -0.003095644759014249, 0.06684884428977966,
        0.14202894270420074, 0.10575990378856659, 0.06697306782007217, -0.06835158914327621, 0.047652363777160645,
        -0.08775102347135544, -0.19320280849933624, -0.0810924842953682, 0.0863097533583641, -0.008638949133455753,
        0.10925482213497162, 0.0912817046046257, 0.06264014542102814, 0.1405610889196396, -0.009903536178171635,
        0.19692391157150269, 0.0322306789457798, 0.0489131361246109, -0.06110483407974243, 0.027134373784065247,
        0.14630025625228882, -0.12023231387138367, 0.03643612191081047, 0.06867370754480362, 0.041103705763816833,
        -0.013434196822345257, 0.038411762565374374, 0.06460864096879959, 0.0011621641460806131, 0.10536094754934311,
        0.035964030772447586, 0.09262564033269882, -0.0463472381234169, 0.03088979423046112, 0.033354636281728745,
        0.014125559478998184, -0.05294058471918106, 0.0665811076760292, 0.09723594039678574, 0.16164301335811615,
        0.03783421218395233, 0.06289596110582352, -0.0032532066106796265, -0.08667179197072983, 0.11004587262868881,
        0.1668568253517151, 0.02073133923113346, -0.034232161939144135, 0.13266056776046753, 0.03476358577609062,
        -0.11493151634931564, 0.009378255344927311, -0.13094522058963776, -0.1504994034767151, -0.02236087992787361,
        0.07255621999502182, 0.1075742319226265, 0.09134586900472641, 0.1178646832704544, 0.019714290276169777,
        -0.09368806332349777, -0.032916102558374405, 0.0032272282987833023, 0.10861700028181076, 0.02332194522023201,
        -0.09700699895620346, 0.031802207231521606, 0.06564552336931229, 0.12444636225700378, -0.047588616609573364,
        -0.0793488472700119, -0.03701995685696602, -0.04713016748428345, 0.09496312588453293, -0.008399663493037224,
        -0.03795255348086357, 0.07809993624687195, 0.09577620774507523, -0.01986328326165676, -0.0248054638504982,
        0.09023463726043701, -0.023221805691719055, -0.095000721514225, 0.04210957884788513, 0.05116396024823189,
        -0.03961413726210594, 0.07796886563301086, -0.05958428606390953, -0.05694335326552391, 0.029512574896216393,
        -0.11374101042747498, 0.013214350678026676, 0.010385592468082905, 0.02359738200902939, 0.03948306292295456,
        0.008701007813215256, 0.00914869923144579, -0.058086708188056946, 0.02744937129318714, 0.019191039726138115,
        -0.0038657947443425655, 0.02147808112204075, -0.06546277552843094, -0.02129518799483776, 0.04599538818001747,
        0.0633472353219986, 0.021402457728981972, 0.11968886107206345, 0.019291305914521217, 0.014645976945757866,
        -0.0038151126354932785, 0.03244589641690254, 0.0077338567934930325, -0.0193830206990242, -0.08920548111200333,
        0.026052776724100113, 0.022521434351801872, -0.03190502151846886, -0.006023460067808628, -0.03746147081255913,
        0.00994367990642786, 0.002372304443269968, 0.014208085834980011, -0.009187325835227966, -0.005037861876189709,
        -0.01056573074311018, 0.024651123210787773, -0.09848218411207199, 0.029170067980885506, 0.011423573829233646,
        0.05379287526011467, -0.010841011069715023, 0.06599254906177521, -0.11576248705387115, 0.006072878371924162,
        -0.01195357833057642, 0.012140254490077496, 0.043122537434101105, 0.10814473778009415, -0.04306792840361595,
        0.056208204478025436, 0.044181134551763535, -0.03813200443983078, 0.02211672067642212, 0.06814716011285782,
        0.03137572482228279, 0.004365295637398958, -0.0012298321817070246, -0.021101495251059532, -0.05622885376214981,
        0.000689742446411401, -0.016101233661174774, 0.014620468951761723, 0.004936481360346079, -0.01814979873597622,
        -0.060829129070043564, 0.0408453568816185, -0.019473835825920105, -0.002014316152781248, 0.014507985673844814,
        -0.04729626327753067, 0.017780225723981857, 0.011719867587089539, 0.06183263286948204, -0.019255394116044044,
        -0.01424036268144846, -0.002407493069767952, -0.02486160583794117, 0.0639580711722374, 0.09962186962366104,
        -0.013868022710084915, 0.010158668272197247, -0.037167128175497055, -0.007581181824207306, 0.003940933849662542,
        -0.062450479716062546, -0.011907647363841534, 0.03531165048480034, -0.024724828079342842, 0.06109343096613884,
        -0.00931581575423479, -0.04465491324663162, -0.008584672585129738, -0.03149290010333061, -0.039894863963127136,
        -0.017516344785690308, 0.03949592635035515, -0.007019133307039738, -0.02485121227800846, 0.023058416321873665,
        0.008031283505260944, -0.06819325685501099, -0.0048722461797297, 0.006044066045433283, -0.006802393589168787,
        -0.010290014557540417, 0.04890282079577446, 0.01372552290558815, -0.030173713341355324, -0.013341008685529232,
        -0.012696412391960621, 0.014977535232901573, -0.030099431052803993, -0.015420781448483467, -0.03197165206074715,
        -0.03323986753821373, 0.010421168059110641, -0.020408466458320618, 0.047373682260513306, 0.025890370830893517,
        -0.011681689880788326, 0.005333658307790756, 0.03178089112043381, -0.016186542809009552, -0.023091981187462807,
        -0.032561395317316055, 0.028230896219611168, -0.014914678409695625, 0.06662428379058838, -0.03111431933939457,
        0.04677021503448486, 0.026071276515722275, 0.006538995075970888, 0.009324355982244015, -0.03537622466683388,
        -0.039675187319517136, -0.020104076713323593, -0.03392006456851959, -0.0023066920693963766,
        -0.004931311123073101, 0.022450296208262444, -0.017517713829874992, 0.01549981627613306, 0.015300735831260681,
        -0.010254641063511372, 0.03508707135915756, -0.022805145010352135, 0.030572401359677315, -0.05302604287862778,
        -0.040586378425359726, 0.007583439815789461},
    {0.12740114331245422, 0.1273922473192215, 0.009936089627444744, 0.17630654573440552, -0.0769321545958519,
        0.15731516480445862, 0.03623779118061066, 0.10348186641931534, -0.06139952316880226, 0.23953551054000854,
        -0.11030569672584534, 0.05810077115893364, 0.18287357687950134, -0.05052262172102928, 0.056586265563964844,
        0.11677800118923187, 0.10182050615549088, 0.12484810501337051, -0.10782851278781891, 0.054709188640117645,
        -0.028715675696730614, -0.05377231538295746, 0.04755378141999245, 0.05146459490060806, -0.025566337630152702,
        0.07651972025632858, 0.012282981537282467, 0.049429796636104584, 0.20615050196647644, 0.017910003662109375,
        0.1658921241760254, 0.08581136167049408, 0.22571992874145508, 0.002874471480026841, 0.00896228663623333,
        0.02694244310259819, -0.05893450602889061, 0.04492209851741791, 0.0231558196246624, 0.0268765389919281,
        -0.09310991317033768, 0.1353059709072113, 0.07836111634969711, -0.057519275695085526, 0.1269223988056183,
        0.011779279448091984, 0.04124825820326805, -0.0507066547870636, 0.006022619549185038, -0.014863910153508186,
        0.09202530980110168, -0.03167761489748955, 0.0013625520514324307, 0.051645755767822266, 0.19339612126350403,
        0.08063642680644989, 0.1008002758026123, -0.010763931088149548, -0.015392748638987541, 0.08463464677333832,
        0.11940872669219971, 0.11049623787403107, 0.050635892897844315, 0.1476403921842575, 0.015187559649348259,
        -0.10819131880998611, -0.07016446441411972, -0.10087509453296661, -0.1623772829771042, 0.01784525252878666,
        -0.06655105203390121, 0.05424471199512482, 0.09122960269451141, 0.1112440675497055, 0.03752152621746063,
        -0.05536246299743652, -0.1081160381436348, -0.016599856317043304, 0.10041684657335281, 0.09452889114618301,
        0.014189586974680424, -0.07245821505784988, 0.06878732144832611, 0.1489635407924652, -0.02194492518901825,
        -0.08265187591314316, 0.023657971993088722, 0.015837742015719414, 0.030060436576604843, -0.018981466069817543,
        -0.07623091340065002, 0.05920648202300072, 0.0674322247505188, 0.04545885697007179, 0.11477801948785782,
        0.017581161111593246, -0.04469013959169388, -0.10292450338602066, -0.06233558431267738, 0.08537016063928604,
        0.04266709089279175, 0.04140445217490196, -0.01515106949955225, -0.01703096739947796, -0.003191347699612379,
        0.007607475854456425, 0.028800547122955322, 0.070640429854393, -0.01378757506608963, 0.0346578024327755,
        -0.057145826518535614, 0.03735838830471039, 0.033875562250614166, -0.00748714804649353, -0.1200416162610054,
        0.04692595824599266, -0.023116037249565125, -0.06086714565753937, 0.03560516610741615, -0.015055079944431782,
        -0.008384439162909985, 0.03248700872063637, 0.04296603426337242, -0.017748596146702766, -0.03074122965335846,
        -0.033691469579935074, 0.04984567314386368, -0.018100420013070107, -0.03403513506054878, 0.013135842978954315,
        0.055465660989284515, -0.035334181040525436, 0.06760204583406448, 0.030097326263785362, 0.005354261491447687,
        0.027059093117713928, 0.03840222582221031, -0.014136835001409054, -0.027466023340821266, -0.035996899008750916,
        0.06141789257526398, -0.017350642010569572, -0.026215840131044388, 0.006595324724912643, 0.06335202604532242,
        0.013552337884902954, -0.008136328309774399, -0.003998287487775087, -0.09047531336545944, 0.01608068123459816,
        -0.015745628625154495, 0.04941151291131973, -0.0016093556769192219, 0.03389827162027359, 0.02157353237271309,
        0.005632220301777124, 0.004061601590365171, 0.029931357130408287, 0.044956762343645096, 0.0540030337870121,
        0.07729370146989822, -0.011925708502531052, 0.04526693746447563, 0.013562336564064026, -0.02786754071712494,
        0.013038616627454758, 0.014032412320375443, 0.07201045006513596, -0.008770010434091091, -0.04691615700721741,
        0.028278864920139313, -0.030074171721935272, -0.026347806677222252, 0.0003442266315687448, 0.040204260498285294,
        -0.028852667659521103, 0.012484551407396793, -0.05964231118559837, 0.031035901978611946, 0.018949639052152634,
        -0.004367036744952202, -0.02111014537513256, 0.03480032831430435, -0.02896050736308098, 0.028775949031114578,
        -0.00030449649784713984, -0.01266536582261324, 0.037970591336488724, -0.02612179145216942, -0.00215985719114542,
        -0.02583489380776882, -0.005561229307204485, -0.012549764476716518, -0.005031691864132881, -0.04316446930170059,
        0.027934052050113678, 0.00463896244764328, -0.020720308646559715, -0.0028501227498054504, -0.05716131627559662,
        0.024184614419937134, -0.014255760237574577, -0.08220003545284271, -0.04406094551086426, 0.039041679352521896,
        0.004401725251227617, -0.05916266515851021, -0.0012476484989747405, 0.047692492604255676, 0.020208561792969704,
        0.015891259536147118, -0.05036023259162903, -0.030658017843961716, 0.028376709669828415, -0.013124017044901848,
        0.01735207997262478, 0.02943398430943489, -0.024487249553203583, -0.02527395822107792, -0.025628112256526947,
        -0.047899652272462845, -0.01820230670273304, -0.01935476064682007, 0.00590931810438633, 0.010939844883978367,
        -0.01599792204797268, 0.046346697956323624, 0.01151324063539505, 0.019352274015545845, -0.010700143873691559,
        0.0038689167704433203, -0.018857019022107124, -0.028995579108595848, -0.0163434986025095, 0.0013707855250686407,
        0.0232568196952343, 0.021273599937558174, -0.03880032151937485, -0.03289246931672096, 0.010729465633630753,
        -0.03172808513045311, -0.01590624824166298, 0.010110197588801384, -0.03720299154520035, 0.0002702003694139421,
        -0.0139417489990592, -0.022265128791332245, 0.009135633707046509, 0.0353502593934536, 0.03262854367494583,
        0.03797363117337227, 0.009021328762173653, 0.00035578812821768224, -0.026752673089504242, 0.0019878093153238297,
        0.022113746032118797},
    {0.18126952648162842, 0.09843814373016357, -0.014482078142464161, 0.12706464529037476, -0.011566732078790665,
        0.18668028712272644, 0.027760762721300125, 0.1743394285440445, -0.04407200962305069, 0.2467699646949768,
        0.028129324316978455, 0.06302167475223541, 0.16308608651161194, -0.028038790449500084, -0.05003329738974571,
        0.0936996340751648, 0.02682122215628624, 0.054196253418922424, -0.06724245846271515, 0.07260048389434814,
        0.006108445581048727, -0.07092894613742828, 0.02021924965083599, 0.058561913669109344, -0.10320692509412766,
        0.038504961878061295, 0.03730148822069168, 0.041100356727838516, 0.1897352784872055, -0.030574973672628403,
        0.10897056013345718, 0.07807745784521103, 0.12757514417171478, 0.04786728322505951, -0.07491166144609451,
        -0.00669637368991971, -0.04197238013148308, 0.028752366080880165, 0.0029507551807910204, 0.08408704400062561,
        -0.04311573505401611, 0.07691234350204468, -0.0017077900702133775, -0.02795485220849514, 0.09796786308288574,
        -0.03850138187408447, 0.04462425783276558, -0.03758743777871132, 0.02610558085143566, -0.09541875869035721,
        0.1490732878446579, -0.09868159145116806, -0.044676851481199265, 0.04117865487933159, 0.026955880224704742,
        0.12206264585256577, 0.16684666275978088, 0.03560858964920044, -0.1260332614183426, 0.007405610755085945,
        0.07695866376161575, 0.07456585764884949, -0.056381020694971085, 0.1987050473690033, 0.14309096336364746,
        0.0020726951770484447, -0.07259634137153625, -0.11659141629934311, -0.0967220738530159, -0.047073669731616974,
        -0.027292858809232712, 0.012120647355914116, 0.07121268659830093, -0.013359210453927517, 0.03186596557497978,
        -0.03584538772702217, -0.017329460009932518, -0.038518525660037994, 0.12385234236717224, 0.09446096420288086,
        -0.07403455674648285, -0.059254005551338196, 0.11004025489091873, 0.016874849796295166, -0.04639364033937454,
        -0.0037386822514235973, 0.029917916283011436, -0.03300080820918083, 0.07629015296697617, -0.004923991858959198,
        -0.07297030836343765, 0.059723254293203354, 0.05838855728507042, -0.010928809642791748, 0.08385012298822403,
        0.009674987755715847, -0.05040748789906502, 0.04591751471161842, -0.04551415517926216, 0.04955190047621727,
        -0.06086205318570137, -0.057296231389045715, -0.05053636431694031, 0.07507575303316116, -0.0026429130230098963,
        -0.0040832990780472755, 0.005266418680548668, -0.05859208106994629, -0.06438886374235153, 0.022360756993293762,
        0.06844935566186905, 0.04109979793429375, -0.019841670989990234, 0.01814534328877926, -0.01047356054186821,
        0.030499018728733063, 0.019684987142682076, 0.0058572250418365, 0.04101948440074921, -0.010393212549388409,
        0.005193929187953472, 0.012228933162987232, 0.044872552156448364, -0.004935734905302525, -0.01931914873421192,
        -0.07014323770999908, 0.06645064800977707, -0.025406453758478165, -0.07081859558820724, 0.04208182170987129,
        0.049847561866045, 0.02237541973590851, 0.05590426176786423, 0.09619680792093277, 0.007574786432087421,
        0.093117855489254, -0.015726853162050247, -0.011723819188773632, -0.057954300194978714, -0.059522129595279694,
        -0.001830444554798305, -0.005901770666241646, 0.006717261858284473, 0.00726583506911993, 0.11977498978376389,
        -0.11113932728767395, 0.018175948411226273, -0.044952426105737686, -0.023256247863173485, -0.044884465634822845,
        0.0825466439127922, -0.05427474528551102, -0.043906211853027344, 0.08139745146036148, -0.06169634312391281,
        0.07100401818752289, -0.0519162192940712, 0.01671724021434784, 0.04290531203150749, 0.06544984877109528,
        0.02638377621769905, 0.0662999227643013, -0.0008719173492863774, -0.02515091374516487, -0.027159633114933968,
        -0.05360753834247589, -0.07880259305238724, 0.0524144247174263, 0.03229866549372673, -0.06844530254602432,
        0.09613486379384995, 0.08300118893384933, 0.019810156896710396, -0.027218718081712723, -0.01839837245643139,
        0.021234946325421333, -0.025218738242983818, 0.03158285468816757, 0.039321109652519226, -0.010982026346027851,
        -0.0336274690926075, -0.03475160896778107, 0.016546916216611862, -0.017688794061541557, 0.009855277836322784,
        -0.042039379477500916, 0.07834239304065704, 0.07633820921182632, 0.009097042493522167, -0.03718072548508644,
        -0.08069916069507599, 0.010633031837642193, 0.07575085759162903, 0.007721922360360622, 0.060780271887779236,
        0.06474385410547256, 0.011392367072403431, 0.04399571940302849, 0.023703524842858315, -0.008465036749839783,
        0.06681788712739944, -0.001882304553873837, -0.011136140674352646, -0.03259025514125824, -0.01854725554585457,
        0.015281833708286285, -0.06897128373384476, 0.03104953095316887, -0.020303402096033096, -0.021792776882648468,
        0.027349509298801422, 0.048215292394161224, -0.01891140826046467, -0.025742104277014732, -0.015063943341374397,
        -0.012124057859182358, 0.020813651382923126, -0.028656115755438805, 0.016599463298916817, 0.011102334596216679,
        0.003911234904080629, -0.03288177400827408, -0.02409420721232891, -0.004774106666445732, 0.10328233987092972,
        -0.027209443971514702, -0.021236170083284378, 0.02679787017405033, -0.08161761611700058, 0.05725504457950592,
        0.006228995975106955, -0.04669442027807236, 0.04790551960468292, 0.034122493118047714, 0.0269272830337286,
        0.0734018087387085, -0.07457426190376282, -0.031575653702020645, 0.019433898851275444, 0.03734714910387993,
        0.003162967972457409, 0.045176196843385696, -0.04203765466809273, -0.025982702150940895, -0.005814241711050272,
        0.005350235849618912, -0.05571023374795914, -0.011227753013372421, 0.020951468497514725, -0.007333244662731886,
        0.016718275845050812, -0.035513974726200104, -5.759009582106955e-06, -0.02261967957019806,
        -0.014431697316467762, -0.026057688519358635},
    {0.09475471824407578, 0.15588951110839844, 0.015662696212530136, 0.16547556221485138, -0.03080790676176548,
        0.1834028959274292, -0.002319897059351206, 0.0925418809056282, -0.02483947202563286, 0.296332985162735,
        -0.026433458551764488, 0.05307121202349663, 0.17281277477741241, -0.05736009031534195, 0.01597863994538784,
        0.09209569543600082, -0.009474632330238819, 0.12397828698158264, -0.06096882000565529, 0.03721846640110016,
        0.0033347688149660826, -0.06409712880849838, -0.0029991858173161745, 0.006964747793972492, -0.07313664257526398,
        0.12692980468273163, -0.01070441771298647, 0.08579252660274506, 0.22732873260974884, 0.06729128956794739,
        0.1844906359910965, 0.10501547902822495, 0.15483659505844116, -0.021683035418391228, 0.04033437371253967,
        0.02914767898619175, -0.07122910022735596, 0.03601798787713051, 0.10204067826271057, 0.059369493275880814,
        -0.0433800108730793, 0.05559929832816124, 0.048623815178871155, -0.06220104172825813, 0.07587472349405289,
        -0.0026645974721759558, 0.055621251463890076, -0.07881064713001251, -0.073374904692173, 0.0334017314016819,
        -0.010075629688799381, -0.13505919277668, 0.004417911171913147, 0.11178967356681824, -0.04359317943453789,
        0.07910922169685364, 0.02632497064769268, 0.063067227602005, -0.11786549538373947, 0.07865411043167114,
        0.10303299129009247, -0.03756512701511383, -0.08569804579019547, 0.205509215593338, 0.025302188470959663,
        -0.026712797582149506, -0.07242437452077866, -0.059868890792131424, -0.08079342544078827, 0.003542038844898343,
        -0.06113724783062935, 0.13306587934494019, 0.1324070245027542, -0.03133475035429001, -0.015012922696769238,
        0.01614522747695446, -0.06519012153148651, 0.02618272416293621, 0.06798259913921356, 0.009585283696651459,
        -0.009562715888023376, -0.007479995954781771, -0.022628437727689743, 0.09084860235452652, -0.029109809547662735,
        -0.08569478988647461, -0.0030390231404453516, -0.02748902328312397, 0.12275201082229614, -0.05480119585990906,
        -0.06134738773107529, 0.021426692605018616, 0.07704412192106247, -0.059738099575042725, 0.08003034442663193,
        0.0371473990380764, -0.017506198957562447, -0.13434912264347076, 0.0035550198517739773, 0.10530141741037369,
        -0.0036535696126520634, 0.043678928166627884, -0.007817263714969158, -0.025933153927326202, 0.01098928228020668,
        0.042325325310230255, 0.04078815504908562, 0.02766658179461956, 0.03597788140177727, -0.03491166979074478,
        0.021993814036250114, -0.00243674055673182, 0.020425552502274513, 0.06535174697637558, 0.022117748856544495,
        0.0009012571536004543, -0.0904015451669693, -0.015619499608874321, 0.03967006877064705, 0.027322115376591682,
        0.017941929399967194, 0.033061433583498, 0.02453727461397648, -0.025199180468916893, -0.044317275285720825,
        0.031049653887748718, -0.009863476268947124, -0.07057607918977737, -0.012661966495215893, -0.07505126297473907,
        0.04559946432709694, -0.027903040871024132, 1.8054427073366242e-06, 0.07459333539009094, 0.04868635535240173,
        0.05560454726219177, 0.04341264069080353, -0.043597348034381866, -0.06339066475629807, -0.027640976011753082,
        -0.017171401530504227, 0.05474718287587166, -0.0004928361740894616, 0.014282278716564178, 0.0813976526260376,
        0.015334982424974442, -0.06410157680511475, 0.00956018827855587, -0.06218790262937546, 0.03662586584687233,
        -0.13463269174098969, -0.009352841414511204, 0.014633022248744965, 0.16154247522354126, -0.008939751423895359,
        0.05405595898628235, 0.057610563933849335, 0.008349587209522724, -0.04071970283985138, 0.003445118200033903,
        0.051664069294929504, -0.017592374235391617, -0.02341548539698124, -0.05095955356955528, -0.04071341082453728,
        0.03616984933614731, -0.006784840486943722, 0.013303072191774845, -0.0057746791280806065, 0.026121696457266808,
        0.030512485653162003, 0.02895992062985897, 0.009122409857809544, 0.06592559069395065, -0.016004104167222977,
        -0.0043402728624641895, 0.013234077952802181, -0.024203116074204445, -0.011850995011627674,
        -0.04899873584508896, 0.037261441349983215, -0.0342601053416729, -0.010922937653958797, 0.03333955630660057,
        -0.00648750364780426, 0.05140304937958717, 0.004050979856401682, 0.032495006918907166, 0.026041461154818535,
        0.03058934025466442, -0.04211359843611717, 0.022111935541033745, 0.06230124831199646, -0.07035376131534576,
        0.04819800704717636, 0.025623878464102745, -0.010283526964485645, 0.0016115346224978566, 0.05151030048727989,
        -0.016705617308616638, 0.03519647568464279, -0.09122834354639053, -0.017093606293201447, 0.062128886580467224,
        0.047887276858091354, 0.018271179869771004, -0.03537868708372116, 0.06945628672838211, -0.04351482540369034,
        0.018891526386141777, 0.011087610386312008, 0.006870775483548641, 0.024791836738586426, 0.035621363669633865,
        -0.008346603251993656, 0.004112000577151775, 0.08062322437763214, 0.0018274873727932572, -0.03874054178595543,
        0.038671813905239105, -0.01071444433182478, -0.024038229137659073, -0.0002517199900466949,
        -0.022507254034280777, -0.003131852252408862, -0.02942778915166855, -0.049020908772945404,
        0.0020562889985740185, 0.021119097247719765, -0.0059622484259307384, 0.04157816991209984, -0.014190244488418102,
        -0.056172385811805725, 0.00979262962937355, -0.016939710825681686, -0.02662396989762783, -0.00432629743590951,
        0.014588937163352966, -0.028014887124300003, 0.035949137061834335, -0.029991546645760536, -0.011080030351877213,
        -0.013287756592035294, -0.008899875916540623, 0.04485347121953964, -0.02941095270216465, 0.0021283088717609644,
        0.04723123461008072, -0.012289376929402351, 0.010277645662426949, -0.04601232334971428, 0.010047649033367634,
        0.01871577650308609, -0.02125951088964939, 0.044899892061948776, -0.05799537152051926}};

HWTEST_F(IvfClusterIndexUt, UtIvfClusterBuild009, TestSize.Level0)
{
    vector<uint64_t> ids = {};  // 用于存储id
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    const uint32_t numSamples = g_clusterVectors.size();
    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = g_clusterVectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = i;
        ids.push_back(insertDataId);
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    vector<float> floatVector;
    TransferVector2Float(floatVector, g_clusterVectors);
    IvfClusterStartBuild(idxCtx, dim, numSamples, ids.data(), DIST_TYPE_COSINE, floatVector.data());

    VecScanParaT vectorScanPara = {0};
    vectorScanPara.dim = 256;
    vectorScanPara.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara};
    IvfClusterScanAndCompareResult(idxCtx, &cfg, 1);

    vectorScanPara.clusterType = CLUSTER_TYPE;
    IvfClusterScanAndCompareResult(idxCtx, &cfg, numSamples);

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}

HWTEST_F(IvfClusterIndexUt, UtIvfClusterBuild010, TestSize.Level0)
{
    vector<uint64_t> ids = {};  // 用于存储id
    ShmemPtrT idxShmAddr = {0};
    uint16_t dim = 256;
    AnnMetaCfgT metaCfg = {0};
    metaCfg.vectorDim = dim;
    metaCfg.distType = DIST_TYPE_COSINE;
    IndexMetaCfgT indexCfg = GetIvfClusterMetaCfg();
    indexCfg.extendParam = (void *)&metaCfg;

    IndexCtxT *idxCtx = nullptr;
    IvfClusterCreateAndOpen(indexCfg, &metaCfg, &idxShmAddr, &idxCtx);

    static vector<vector<float>> clusterVectors(10);
    std::copy(g_clusterVectors.begin(), g_clusterVectors.begin() + 10, clusterVectors.begin());

    const uint32_t numSamples = clusterVectors.size();
    for (uint32_t i = 0; i < numSamples; i++) {
        vector<float> insertVector = clusterVectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = i;
        ids.push_back(insertDataId);
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    vector<float> floatVector;
    TransferVector2Float(floatVector, clusterVectors);
    IvfClusterStartBuild(idxCtx, dim, numSamples, ids.data(), DIST_TYPE_COSINE, floatVector.data());

    VecScanParaT vectorScanPara = {0};
    vectorScanPara.dim = 256;
    vectorScanPara.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara};
    IvfClusterScanAndCompareResult(idxCtx, &cfg, 1);

    vectorScanPara.clusterType = CLUSTER_TYPE;
    IvfClusterScanAndCompareResult(idxCtx, &cfg, numSamples);

    vector<uint64_t> ids2 = {};
    static vector<vector<float>> secondClusterVectors(10);
    std::copy(g_clusterVectors.begin() + 10, g_clusterVectors.begin() + 20, secondClusterVectors.begin());

    const uint32_t numSamples2 = secondClusterVectors.size();
    for (uint32_t i = 0; i < numSamples2; i++) {
        vector<float> insertVector = secondClusterVectors[i];
        uint8_t keyData[KEY_LEN] = {0};
        IvfClusterTransferVector(keyData, reinterpret_cast<uint8_t *>(insertVector.data()), KEY_LEN - 1);
        IndexKeyT idxKey = {.keyData = reinterpret_cast<uint8_t *>(keyData), .keyLen = KEY_LEN, .prefixPropeNum = 0};
        uint64_t insertDataId = i + numSamples;
        ids2.push_back(insertDataId);
        ASSERT_EQ(GMERR_OK, IdxInsert(idxCtx, idxKey, insertDataId));  //  首次插入会创建根节点
    }

    vector<float> floatVector2;
    TransferVector2Float(floatVector2, secondClusterVectors);
    IvfClusterStartBuild(idxCtx, dim, numSamples2, ids2.data(), DIST_TYPE_COSINE, floatVector2.data());

    VecScanParaT vectorScanPara2 = {0};
    vectorScanPara2.dim = 256;
    vectorScanPara2.clusterType = UNCLUSTER_TYPE;
    IndexScanCfgT cfg2 = {.scanType = INDEX_RANGE_CLOSED,
        .scanDirect = INDEX_SCAN_ASCEND,
        .leftKey = nullptr,
        .rightKey = nullptr,
        .scanMode = INDEX_SCAN_ASCEND,
        .scanPara = (void *)&vectorScanPara2};
    IvfClusterScanAndCompareResult(idxCtx, &cfg2, 1);

    vectorScanPara2.clusterType = CLUSTER_TYPE;
    IvfClusterScanAndCompareResult(idxCtx, &cfg2, numSamples + numSamples2);

    IvfflatCloseAndDrop(idxShmAddr, idxCtx);
}
