/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: executor mock test for inverted index
 * Author: GMDBv5 EE Team
 * Create: 2025-01-16
 */

#include <cstdint>
#include <cstdlib>
#include <iostream>
#include <queue>

#include "tokenizer.h"
#include "ee_concurrency_control.h"
#include "ee_plan_state_router.h"
#include "ee_access_method.h"
#include "ee_cmd_state_fusion.h"
#include "ee_cmd_router_fusion.h"
#include "cpl_public_sql_parser_common.h"
#include "ee_plan_node_ddl.h"
#include "ee_access_method_router.h"
#include "ut_emb_sql_executor_common.h"
using namespace testing::ext;

// global variables for mock functions
static const uint64_t START_TUPLE_ADDR = 0x12345678;
uint32_t g_propId;
DmValueT g_val;
DocEntryT g_doc;
TupleAddr g_addr;
InvertedScanItrT g_iter;
bool g_ignoreIdxType;

struct BoolNodeT {
    ExprOpTypeE type;
    int kidNum;
    std::string term;
};

class UtEmbSqlExecutorInverted : public UtEmbSqlExecutorCommon {
protected:
    void SetUp() override
    {
        UtEmbSqlExecutorCommon::SetUp();
        SqlUtExecuteStmt(memCtx, session, "CREATE TABLE InvertedUtDQL(a int primary key, b text, c text);");
        SqlUtExecuteStmt(memCtx, session, "CREATE INDEX btree_index on InvertedUtDQL(b);");
        g_ignoreIdxType = false;
    }
    void TearDown() override
    {
        QryReleaseAllLabelLatch(session);
        SqlUtExecuteStmt(memCtx, session, "DROP INDEX InvertedUtDQL.btree_index;");
        SqlUtExecuteStmt(memCtx, session, "DROP TABLE InvertedUtDQL;");
        // 清空示例元组
        UtEmbSqlExecutorCommon::TearDown();
        ClearAllStub();
    }

    void CreateInvertedScanPlanCommon(
        DmVertexLabelT *vertexLabel, uint32_t indexId, ExprT *rightExpr, InvertedScanT **out);
    // 创建单词项倒排扫描算子执行计划
    void CreateInvertedScanPlanTerm(DmVertexLabelT *vertexLabel, uint32_t indexId, std::string matchTerm,
        InvertedScanT **out, std::vector<uint32_t> vertexProps = {1});
    // 创建一个boolean扫描执行计划
    void CreateInvertedScanPlanBoolean(DmVertexLabelT *vertexLabel, uint32_t indexId, ExprOpTypeE type,
        const std::vector<std::string> &terms, InvertedScanT **out, std::vector<uint32_t> vertexProps = {1});
    // 创建一个多级boolean扫描执行计划
    void CreateInvertedScanPlanBooleanComplex(DmVertexLabelT *vertexLabel, uint32_t indexId,
        const std::vector<BoolNodeT> &BFExpr, InvertedScanT **out, std::vector<uint32_t> vertexProps = {1});

    // 插入一个元组，并将元组地址存储在g_addr中
    Status InsertTupleWithTupleAddr(DmVertexLabelT *vertexLabel, int32_t prob1, std::string tmpStr);
    // 创建一个新索引，并获取创建索引的NodeT
    void CreateInvertedIndexCmd(std::string tabName, std::string indexName, const std::vector<std::string> &propNames,
        CreateIndexStmtT **cmd, CStateT *cstate);

    void TestInvertedScan(InvertedScanT *invertedScan, std::vector<const char *> expResult);

    void TestInvertedScanBM25(InvertedScanT *invertedScan, std::vector<const char *> expResult);

    void CreateInvertedIndex(const std::vector<std::string> &propNames = {"b"});
};

void UtEmbSqlExecutorInverted::CreateInvertedIndexCmd(std::string tabName, std::string indexName,
    const std::vector<std::string> &propNames, CreateIndexStmtT **cmd, CStateT *cstate)
{
    std::string sql = "CREATE INDEX " + indexName + " ON " + tabName + "(";
    for (uint32_t i = 0; i < propNames.size(); i++) {
        sql += (propNames[i] + (i < propNames.size() - 1 ? std::string(", ") : std::string(");")));
    }
    SqlParsedListT parsedList = {0};
    Status ret = SqlParse(memCtx, sql.c_str(), &parsedList);
    ASSERT_EQ(ret, GMERR_OK);
    SqlIrStmtT irStmt = {0};
    NodeT *sqlStmt = *static_cast<NodeT **>(static_cast<void *>(DbListItem(&parsedList.cmdList, 0)));
    ret = SqlInitLabelList4irStmt(memCtx, &irStmt);
    ASSERT_EQ(ret, GMERR_OK);
    ret = SqlAnalyze(session, memCtx, sqlStmt, &irStmt);
    ASSERT_EQ(ret, GMERR_OK);
    ASSERT_NE(irStmt.utilityStmt, nullptr);
    *cmd = (CreateIndexStmtT *)irStmt.utilityStmt;
    (*cmd)->indexLabel->indexType = INVERTED_INDEX;
    cstate->seInstance = session->seInstance;
    cstate->memCtx = memCtx;
    cstate->dtlWorkerId = 0;
    cstate->session = session;
}

void UtEmbSqlExecutorInverted::CreateInvertedScanPlanCommon(
    DmVertexLabelT *vertexLabel, uint32_t indexId, ExprT *rightExpr, InvertedScanT **out)
{
    InvertedScanT *invertedScan = static_cast<InvertedScanT *>(DbDynMemCtxAlloc(memCtx, sizeof(InvertedScanT)));

    invertedScan->scan.plan.subPlanList = NULL;
    invertedScan->scan.plan.leftTree = NULL;
    invertedScan->scan.plan.rightTree = NULL;
    invertedScan->scan.plan.tagType = T_INVERTED_INDEX_SCAN;
    invertedScan->scan.plan.aaSlotDesc.propNum = 2;  // 表中默认2个属性
    invertedScan->scan.plan.aaSlotDesc.propSchema = static_cast<DmPropertySchemaT **>(
        DbDynMemCtxAlloc(memCtx, invertedScan->scan.plan.aaSlotDesc.propNum * sizeof(DmPropertySchemaT *)));
    EXPECT_NE(invertedScan->scan.plan.aaSlotDesc.propSchema, nullptr);

    invertedScan->scan.plan.aaSlotDesc.propSchema[0] =
        static_cast<DmPropertySchemaT *>(DbDynMemCtxAlloc(memCtx, sizeof(DmPropertySchemaT)));
    EXPECT_NE(invertedScan->scan.plan.aaSlotDesc.propSchema[0], nullptr);
    for (uint32_t i = 0; i < invertedScan->scan.plan.aaSlotDesc.propNum; i++) {
        invertedScan->scan.plan.aaSlotDesc.propSchema[i] = &vertexLabel->metaVertexLabel->schema->properties[i];
    }
    invertedScan->indexId = indexId;
    invertedScan->scan.label = vertexLabel;
    invertedScan->scan.labelId = vertexLabel->metaCommon.metaId;
    invertedScan->scan.projectionExpr = NULL;
    invertedScan->scan.qualExpr = NULL;
    invertedScan->scan.setFieldExpr = NULL;
    invertedScan->needMatchScore = true;
    ExprVarT *matchLeft = (ExprVarT *)ExprMakeLVar(memCtx, vertexLabel->metaVertexLabel->schema->properties[1].propeId);
    invertedScan->matchExpr = ExprMakeBinary(memCtx, (ExprT *)matchLeft, rightExpr, EXPR_OP_INVERTED_MATCH);

    *out = invertedScan;
    ASSERT_NE(invertedScan, nullptr);
}

void UtEmbSqlExecutorInverted::CreateInvertedScanPlanTerm(DmVertexLabelT *vertexLabel, uint32_t indexId,
    std::string matchTerm, InvertedScanT **out, std::vector<uint32_t> vertexProps)
{
    InvertedPropeInfoT propes[DM_MAX_KEY_PROPE_NUM];
    uint32_t propNum = vertexProps.size();
    for (uint32_t i = 0; i < propNum; i++) {
        propes[i].propId = vertexLabel->metaVertexLabel->schema->properties[vertexProps[i]].propeId;
        propes[i].propWgt = 1.0;
    }
    ExprTermT *matchRight = (ExprTermT *)ExprMakeTerm(memCtx, matchTerm.data(), propes, propNum, 1.0);
    CreateInvertedScanPlanCommon(vertexLabel, indexId, (ExprT *)matchRight, out);
}

void UtEmbSqlExecutorInverted::CreateInvertedScanPlanBoolean(DmVertexLabelT *vertexLabel, uint32_t indexId,
    ExprOpTypeE type, const std::vector<std::string> &terms, InvertedScanT **out, std::vector<uint32_t> vertexProps)
{
    if (type == EXPR_OP_INVERTED_NOT) {
        ASSERT_EQ(terms.size(), 2);  // not孩子不能超过2个
    }

    InvertedPropeInfoT propes[DM_MAX_KEY_PROPE_NUM];
    uint32_t propNum = vertexProps.size();
    for (uint32_t i = 0; i < propNum; i++) {
        propes[i].propId = vertexLabel->metaVertexLabel->schema->properties[vertexProps[i]].propeId;
        propes[i].propWgt = 1.0;
    }

    ExprNonTermT *matchRight = (ExprNonTermT *)ExprMakeNonTerm(memCtx, type, terms.size(), false, 0);
    for (uint32_t i = 0; i < terms.size(); i++) {
        matchRight->array.expr[i] = ExprMakeTerm(memCtx, terms[i].data(), propes, propNum, 1.0);
    }
    CreateInvertedScanPlanCommon(vertexLabel, indexId, (ExprT *)matchRight, out);
}

void UtEmbSqlExecutorInverted::CreateInvertedScanPlanBooleanComplex(DmVertexLabelT *vertexLabel, uint32_t indexId,
    const std::vector<BoolNodeT> &BFExpr, InvertedScanT **out, std::vector<uint32_t> vertexProps)
{
    ASSERT_LE(1, BFExpr.size());
    InvertedPropeInfoT propes[DM_MAX_KEY_PROPE_NUM];
    uint32_t propNum = vertexProps.size();
    for (uint32_t i = 0; i < propNum; i++) {
        propes[i].propId = vertexLabel->metaVertexLabel->schema->properties[vertexProps[i]].propeId;
        propes[i].propWgt = 1.0;
    }

    ExprNonTermT *matchRight = (ExprNonTermT *)ExprMakeNonTerm(memCtx, BFExpr[0].type, BFExpr[0].kidNum, false, 0);
    std::queue<ExprT *> curQueue;
    curQueue.push((ExprT *)matchRight);
    int curNodeId = 1;
    while (!curQueue.empty()) {
        ExprT *top = curQueue.front();
        curQueue.pop();
        if (top->opType == EXPR_OP_INVERTED_TERM) {
            continue;
        }
        ExprNonTermT *topFunc = CastToNonTerm(top);
        ASSERT_LE(2, topFunc->array.num);  //  AND OR 孩子数目不少于2个
        for (uint32_t i = 0; i < topFunc->array.num; i++) {
            if (BFExpr[curNodeId].type == EXPR_OP_INVERTED_TERM) {
                topFunc->array.expr[i] =
                    (ExprT *)ExprMakeTerm(memCtx, BFExpr[curNodeId].term.data(), propes, propNum, 1.0);
            } else {
                topFunc->array.expr[i] =
                    (ExprT *)ExprMakeNonTerm(memCtx, BFExpr[curNodeId].type, BFExpr[curNodeId].kidNum, false, 0);
                curQueue.push(topFunc->array.expr[i]);
            }
            curNodeId++;
        }
    }
    CreateInvertedScanPlanCommon(vertexLabel, indexId, (ExprT *)matchRight, out);
}

Status UtEmbSqlExecutorInverted::InsertTupleWithTupleAddr(DmVertexLabelT *vertexLabel, int32_t prop1, std::string prob2)
{
    LabelBeginCfgT beginCfg = {{vertexLabel}, session->seInstance, memCtx, memCtx, session};
    DmVertexT *vertex = NULL;
    Status ret = DmCreateEmptyVertexWithMemCtx(memCtx, vertexLabel, &vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    HpRunHdlT heapRunHdl;
    ret = HeapLabelOpenWithCCExecuteType(&heapRunHdl, beginCfg, HEAP_OPTYPE_INSERT, CC_EXECUTE_DML);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmValueT val;
    val.type = DB_DATATYPE_INT32;
    val.value.intValue = prop1;
    (void)DmVertexSetPropeByName("a", val, vertex);
    val.type = DB_DATATYPE_STRING;
    val.value.strAddr = (void *)prob2.data();
    val.value.length = prob2.size() + 1;
    (void)DmVertexSetPropeByName("b", val, vertex);
    HeapTupleBufT hpTupleBuf = (HeapTupleBufT){0};
    ret = DmSerializeVertex(vertex, &hpTupleBuf.buf, &hpTupleBuf.bufSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 元组插入地址记录在g_addr中
    ret = HeapLabelInsertHpTupleBuffer(heapRunHdl, &hpTupleBuf, &g_addr);
    if (ret != GMERR_OK) {
        return ret;
    }
    HeapLabelCloseAndResetCtx(heapRunHdl);
    DmDestroyVertex(vertex);
    return GMERR_OK;
}

void UtEmbSqlExecutorInverted::TestInvertedScan(InvertedScanT *invertedScan, std::vector<const char *> expResult)
{
    // 需要在事务语境下执行
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    PlanStateT *invertedScanState;
    EXPECT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    AAT *aa = NULL;
    EXPECT_EQ(GMERR_OK, NewAA(memCtx, &aa));
    // 倒排扫描会返回插入元组的地址
    uint32_t i = 0;
    uint32_t leftSize = expResult.size();
    do {
        Status ret = ExecProcNode(invertedScanState, aa);
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            ASSERT_EQ(ret, GMERR_OK);
        }
        ASSERT_GE(leftSize, aa->prop.writePos);
        for (uint32_t j = 0; j < aa->prop.writePos; j++) {
            AASlotVertexT resultVertex = aa->prop.slots[j]->dmVertex;
            DmValueT resultVal;
            ASSERT_EQ(DmVertexGetPropeByName(resultVertex, "a", &resultVal), GMERR_OK);
            ASSERT_EQ(atoi(expResult[i * aa->prop.size + j]), resultVal.value.intValue);
        }
        i++;
        leftSize -= aa->prop.writePos;
    } while (AAPropSlotsIsFull(aa));
    ExecEndNode(invertedScanState);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    DeleteAA(aa);
    DbDynMemCtxFree(memCtx, invertedScan);
}

void UtEmbSqlExecutorInverted::TestInvertedScanBM25(InvertedScanT *invertedScan, std::vector<const char *> expResult)
{
    static const double INVERTED_EPSILON = 0.0000001;
    // 需要在事务语境下执行
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    PlanStateT *invertedScanState;
    EXPECT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    AAT *aa = NULL;
    EXPECT_EQ(GMERR_OK, NewAA(memCtx, &aa));
    // 倒排扫描会返回插入元组的地址
    uint32_t i = 0;
    uint32_t leftSize = expResult.size();
    do {
        Status ret = ExecProcNode(invertedScanState, aa);
        if (ret != GMERR_OK && ret != GMERR_NO_DATA) {
            ASSERT_EQ(ret, GMERR_OK);
        }
        ASSERT_GE(leftSize, aa->prop.writePos);
        for (uint32_t j = 0; j < aa->prop.writePos; j++) {
            DmValueT resultVal = aa->prop.slots[j]->matchScore;
            ASSERT_NEAR(atof(expResult[i * aa->prop.size + j]), resultVal.value.doubleValue, INVERTED_EPSILON);
        }
        i++;
        leftSize -= aa->prop.writePos;
    } while (AAPropSlotsIsFull(aa));
    ExecEndNode(invertedScanState);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    DeleteAA(aa);
    DbDynMemCtxFree(memCtx, invertedScan);
}

static void SetAllIdxStub();

Status IdxCreateMock(SeRunCtxHdT seRunCtx, IndexMetaCfgT idxMetaCfg, ShmemPtrT *idxShmAddr)
{
    if (DmIndexLabelIsInvertedIndex(idxMetaCfg.idxType)) {
        idxMetaCfg.idxType = BTREE_INDEX;
    }
    ClearAllStub();
    Status ret = IdxCreate(seRunCtx, idxMetaCfg, idxShmAddr);
    SetAllIdxStub();
    return ret;
}

Status IdxAllocMock(SeRunCtxHdT seRunCtxPtr, DmIndexTypeE indexType, IndexCtxT **idxCtx)
{
    bool isInverted = false;
    if (DmIndexLabelIsInvertedIndex(indexType)) {
        isInverted = true;
        indexType = BTREE_INDEX;
    }
    ClearAllStub();
    Status ret = IdxAlloc(seRunCtxPtr, indexType, idxCtx);
    SetAllIdxStub();
    if (isInverted) {
        (*idxCtx)->idxMetaCfg.idxType = INVERTED_INDEX;
    }
    return ret;
}

Status IdxOpenMock(ShmemPtrT idxShmAddr, const IndexOpenCfgT *idxCfg, IndexCtxT *idxCtx)
{
    bool isInverted = false;
    if (DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        isInverted = true;
        idxCtx->idxMetaCfg.idxType = BTREE_INDEX;
    }
    ClearAllStub();
    Status ret = IdxOpen(idxShmAddr, idxCfg, idxCtx);
    SetAllIdxStub();
    if (isInverted) {
        idxCtx->idxMetaCfg.idxType = INVERTED_INDEX;
    }
    return ret;
}

Status IdxBatchInsertMock(IndexCtxT *idxCtx, IndexKeyT idxKey[], HpBatchOutT addr[], uint32_t batchNum)
{
    if (!DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        ClearAllStub();
        Status ret = IdxBatchInsert(idxCtx, idxKey, addr, batchNum);
        SetAllIdxStub();
        return ret;
    }
    static const int WORD_NUM = 100;
    char word[WORD_NUM];
    for (uint32_t i = 0; i < batchNum; i++) {
        (void)memcpy_s(word, WORD_NUM, idxKey[i].keyData, idxKey[i].keyLen);
        word[idxKey[i].keyLen] = 0;
        printf("[Len=%d]:%s\n", idxKey[i].keyLen, word);
        DynDocListT *curList = addr[i].docListOut;
        DB_ASSERT(curList != NULL);
        DB_ASSERT(curList->posLists != NULL);
        printf("[dCount: %d] (Doc List:", curList->dCount);
        for (uint32_t j = 0; j < curList->dCount; j++) {
            printf("%lu:%d ", curList->docList[j].addr, curList->docList[j].occurrences);
        }
        printf("){Position List: ");
        for (uint32_t j = 0; j < curList->pCount; j++) {
            printf("%d ", curList->posLists[j]);
        }
        printf("}\n");
    }
    return GMERR_OK;
}

Status IdxDeleteMock(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara)
{
    if (!DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        ClearAllStub();
        Status ret = IdxDelete(idxCtx, idxKey, addr, removePara);
        SetAllIdxStub();
        return ret;
    }
    return GMERR_OK;
}

Status IdxBeginScanMock(IndexCtxT *idxCtx, IndexScanCfgT scanCfg, IndexScanItrT *iter)
{
    if (!g_ignoreIdxType && !DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        ClearAllStub();
        Status ret = IdxBeginScan(idxCtx, scanCfg, iter);
        SetAllIdxStub();
        return ret;
    }
    *iter = (IndexScanItrT)&g_iter;
    return GMERR_OK;
}

Status IdxScanMock(IndexCtxT *idxCtx, IndexScanItrT iter, HpTupleAddr *addr, bool *isFound)
{
    if (!g_ignoreIdxType && !DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        ClearAllStub();
        Status ret = IdxScan(idxCtx, iter, addr, isFound);
        SetAllIdxStub();
        return ret;
    }
    g_doc.docId = 0;
    g_doc.occurrences = 1;
    g_doc.addr = g_addr;  // 返回InsertTupleWithTupleAddr插入元组时记录下的addr
    // 模拟生成倒排索引返回结果，存储在Iter中
    InvertedScanItrT *invertedIter = (InvertedScanItrT *)iter;
    invertedIter->totalNum = 1;
    invertedIter->docList = &g_doc;
    invertedIter->posLists = NULL;
    // 提示发现有效tuple数据
    *isFound = true;
    return GMERR_OK;
}

void IdxEndScanMock(IndexCtxT *idxCtx, IndexScanItrT iter)
{
    if (!g_ignoreIdxType && !DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        ClearAllStub();
        IdxEndScan(idxCtx, iter);
        SetAllIdxStub();
    }
}

void IdxCloseMock(IndexCtxT *idxCtx)
{
    bool isInverted = false;
    if (DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        isInverted = true;
        idxCtx->idxMetaCfg.idxType = BTREE_INDEX;
    }
    ClearAllStub();
    IdxClose(idxCtx);
    SetAllIdxStub();
    if (isInverted) {
        idxCtx->idxMetaCfg.idxType = INVERTED_INDEX;
    }
}

void IdxReleaseMock(IndexCtxT *idxCtx)
{
    if (DmIndexLabelIsInvertedIndex(idxCtx->idxMetaCfg.idxType)) {
        idxCtx->idxMetaCfg.idxType = BTREE_INDEX;
    }
    ClearAllStub();
    IdxRelease(idxCtx);
    SetAllIdxStub();
    idxCtx = NULL;
}

Status IdxUpdateStatMock(IndexCtxT *idxCtx, void *idxStat)
{
    return GMERR_OK;
}

Status IdxStatViewMock(DmIndexTypeE indexType, ShmemPtrT idxShmAddr, uint16_t instanceId, IndexStatisticsT *idxStat)
{
    return GMERR_OK;
}

static void SetAllIdxStub()
{
    SetStubC((void *)IdxCreate, (void *)IdxCreateMock);
    SetStubC((void *)IdxAlloc, (void *)IdxAllocMock);
    SetStubC((void *)IdxOpen, (void *)IdxOpenMock);
    SetStubC((void *)IdxBatchInsert, (void *)IdxBatchInsertMock);
    SetStubC((void *)IdxDelete, (void *)IdxDeleteMock);
    SetStubC((void *)IdxBeginScan, (void *)IdxBeginScanMock);
    SetStubC((void *)IdxScan, (void *)IdxScanMock);
    SetStubC((void *)IdxEndScan, (void *)IdxEndScanMock);
    SetStubC((void *)IdxClose, (void *)IdxCloseMock);
    SetStubC((void *)IdxRelease, (void *)IdxReleaseMock);
    SetStubC((void *)IdxUpdateStat, (void *)IdxUpdateStatMock);
    SetStubC((void *)IdxStatView, (void *)IdxStatViewMock);
}

/**
 * @tc.name: UtExecutorInvertedTest001
 * @tc.desc: test ExecInitNode with single term (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest001, TestSize.Level0)
{
    SetAllIdxStub();

    // 使用btree_index的indexlabel和indexCtx来模拟倒排索引的行为
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "btree_index");
    g_ignoreIdxType = true;

    InvertedScanT *invertedScan = NULL;
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "凯莱", &invertedScan);
    // 需要在事务语境下执行
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    PlanStateT *invertedScanState;
    ASSERT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    DbDynMemCtxFree(memCtx, invertedScan);
}

/**
 * @tc.name: UtExecutorInvertedTest002
 * @tc.desc: test ExecProcNode with single term (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest002, TestSize.Level0)
{
    SetAllIdxStub();

    // 使用btree_index的indexlabel和indexCtx来模拟倒排索引的行为
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "btree_index");
    g_ignoreIdxType = true;

    std::string tmpStr = "1_hello_world";
    // 启动ExecProcNode测试
    InvertedScanT *invertedScan = NULL;
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "凯莱", &invertedScan);
    // 需要在事务语境下执行
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(InsertTupleWithTupleAddr(vertexLabel, 1, tmpStr), GMERR_OK);
    PlanStateT *invertedScanState;
    ASSERT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    AAT *aa = NULL;
    EXPECT_EQ(GMERR_OK, NewAA(memCtx, &aa));
    // 倒排扫描会返回插入元组的地址
    ASSERT_EQ(ExecProcNode((PlanStateT *)invertedScanState, aa), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    // 检查读出结果是否正确
    ASSERT_EQ(aa->prop.slots[0]->slotType, AA_SLOT_DM_VERTEX);
    AASlotVertexT resultVertex = aa->prop.slots[0]->dmVertex;
    DmValueT resultVal;
    resultVal.value.length = tmpStr.size() + 1;
    resultVal.value.strAddr = (uint8_t *)DbDynMemCtxAlloc(memCtx, tmpStr.size() + 1);
    ASSERT_EQ(DmVertexGetPropeByName(resultVertex, "b", &resultVal), GMERR_OK);
    ASSERT_EQ(resultVal.value.length, tmpStr.size() + 1);
    ASSERT_EQ(memcmp(resultVal.value.strAddr, tmpStr.c_str(), tmpStr.size()), 0);
    // 释放内存空间
    DbDynMemCtxFree(memCtx, invertedScan);
    DbDynMemCtxFree(memCtx, resultVal.value.strAddr);
}

/**
 * @tc.name: UtExecutorInvertedTest003
 * @tc.desc: test ExecExplainNode with single term (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest003, TestSize.Level0)
{
    SetAllIdxStub();

    // 使用btree_index的indexlabel和indexCtx来模拟倒排索引的行为
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "btree_index");
    g_ignoreIdxType = true;

    InvertedScanT *invertedScan = NULL;
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "凯莱", &invertedScan);
    ExecExplainCtxT explainCtx = {.memCtx = memCtx, .sb = &sb};
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    DbDynMemCtxFree(memCtx, invertedScan);
    std::string str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);
}

/**
 * @tc.name: UtExecutorInvertedTest004
 * @tc.desc: test ExecEndNode after ExecInit and after ExecProc (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest004, TestSize.Level0)
{
    SetAllIdxStub();

    // 使用btree_index的indexlabel和indexCtx来模拟倒排索引的行为
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "btree_index");
    g_ignoreIdxType = true;

    InvertedScanT *invertedScan = NULL;
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "凯莱", &invertedScan);

    std::string tmpStr = "2_GuassPD";
    // 需要在事务语境下执行
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(InsertTupleWithTupleAddr(vertexLabel, 2, tmpStr), GMERR_OK);  // 2 is prob
    // init但不执行，测试end
    PlanStateT *invertedScanState;
    ASSERT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    ExecEndNode((PlanStateT *)invertedScanState);
    // init且执行，测试end
    ASSERT_EQ(ExecInitNode((PlanT *)invertedScan, estate, &invertedScanState), GMERR_OK);
    AA *aa = NULL;
    EXPECT_EQ(GMERR_OK, NewAA(memCtx, &aa));
    // 倒排扫描会返回插入元组的地址
    ASSERT_EQ(ExecProcNode((PlanStateT *)invertedScanState, aa), GMERR_OK);
    ExecEndNode((PlanStateT *)invertedScanState);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);

    DbDynMemCtxFree(memCtx, invertedScan);
}

/**
 * @tc.name: UtExecutorInvertedTest005
 * @tc.desc: test CmdCreateIndex to create Inverted Index (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest005, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this is a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'this is also a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'this this this test test test');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'test test test test test test test test test test');");
    SetAllIdxStub();

    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", {"b"}, &cmd, &cstate);
    // 需要在事务语境下执行：从已有的数据库表建立倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
}

/**
 * @tc.name: UtExecutorInvertedTest006
 * @tc.desc: test tuple insert on Inverted Index (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest006, TestSize.Level0)
{
    SetAllIdxStub();

    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", {"b"}, &cmd, &cstate);
    // 需要在事务语境下执行: 创建空倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    // 执行一条插入语句
    printf("=============执行插入=============\n");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this this this test test test'),(2, 'this test test');");
}

/**
 * @tc.name: UtExecutorInvertedTest007
 * @tc.desc: test tuple delete on Inverted Index (with mock), not support now
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest007, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this this this test test test');");
    SetAllIdxStub();

    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", {"b"}, &cmd, &cstate);
    // 需要在事务语境下执行: 创建空倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    // 执行一条删除语句
    printf("=============执行删除=============\n");
    SqlUtExecuteStmt(memCtx, session, "DELETE FROM InvertedUtDQL where a = 1;");
}

/**
 * @tc.name: UtExecutorInvertedTest008
 * @tc.desc: test tuple update on Inverted Index (with mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest008, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this is an apple');");
    SetAllIdxStub();

    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", {"b"}, &cmd, &cstate);
    // 需要在事务语境下执行: 创建空倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
    printf("=============执行更新=============\n");
    // 执行一条更新语句
    SqlUtExecuteStmt(memCtx, session, "UPDATE InvertedUtDQL SET b = 'this is two apple apple' WHERE a = 1;");
}

/**
 * @tc.name: UtExecutorInvertedTest009
 * @tc.desc: test create index with no mock
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest009, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this is a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'this is also a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'this this this test test test');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'test test test test test test test test test test');");

    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", {"b"}, &cmd, &cstate);
    // 需要在事务语境下执行：从已有的数据库表建立倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);

    QryReleaseAllLabelLatch(session);
}

void UtEmbSqlExecutorInverted::CreateInvertedIndex(const std::vector<std::string> &propNames)
{
    // 创建倒排索引
    CreateIndexStmtT *cmd;
    CStateT cstate;
    NodeT *cmdResult = NULL;
    CreateInvertedIndexCmd("InvertedUtDQL", "inv_index", propNames, &cmd, &cstate);
    // 需要在事务语境下执行：从已有的数据库表建立倒排索引
    EXPECT_EQ(SeTransBegin(session->seInstance, nullptr), GMERR_OK);
    ASSERT_EQ(CmdCreateIndex(&cstate, (NodeT *)cmd, &cmdResult), GMERR_OK);
    EXPECT_EQ(SeTransCommit(session->seInstance), GMERR_OK);
}

/**
 * @tc.name: UtExecutorInvertedTest010
 * @tc.desc: test inverted Scan with no mock
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest010, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'this is a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'this is also a test');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'this this this test test test');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'test test test test test test test test test test');");
    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 0: no match
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "hello", &invertedScan);
    TestInvertedScan(invertedScan, {});

    // Test Case 1: one match
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "also", &invertedScan);
    TestInvertedScan(invertedScan, {"2"});

    // Test Case 2: two match
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "is", &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2"});

    // Test Case 3: three match
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "this", &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3"});

    // // Test Case 4: all match
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "test", &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3", "4"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest011
 * @tc.desc: test inverted Scan (boolean and)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest011, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'sqlite has inverted index');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'sqlite has B-tree index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'inverted index helps to improve text search performance');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'full text search is important for sqlite');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(5, 'design a good index for better performance');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(6, 'do you want to learn good index?');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(7, 'sqlite supports full text search by index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(8, 'sqlite contains inverted index and B-tree index');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 0: no match
    CreateInvertedScanPlanBoolean(
        vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"inverted", "B-tree", "performance"}, &invertedScan);
    TestInvertedScan(invertedScan, {});

    // Test Case 1: one match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"inverted", "B-tree"}, &invertedScan);
    TestInvertedScan(invertedScan, {"8"});

    // Test Case 2: two match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"performance", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {"3", "5"});

    // Test Case 3: three match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"inverted", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "3", "8"});

    // Test Case 4: four match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"sqlite", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "7", "8"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest012
 * @tc.desc: test inverted Scan (boolean or)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest012, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'sqlite has inverted index');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'sqlite has B-tree index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'inverted index helps to improve text search performance');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'full text search is important for sqlite');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(5, 'design a good index for better performance');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(6, 'do you want to learn good index?');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(7, 'sqlite supports full text search by index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(8, 'sqlite contains inverted index and B-tree index');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 0: no match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_AND, {"dash", "hash"}, &invertedScan);
    TestInvertedScan(invertedScan, {});

    // Test Case 1: two match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_OR, {"hash", "B-tree"}, &invertedScan);
    TestInvertedScan(invertedScan, {"2", "8"});

    // Test Case 2: four match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_OR, {"search", "performance"}, &invertedScan);
    TestInvertedScan(invertedScan, {"3", "4", "5", "7"});

    // Test Case 3: six match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_OR, {"sqlite", "inverted"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3", "4", "7", "8"});

    // Test Case 4: seven match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_OR, {"B-tree", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3", "5", "6", "7", "8"});

    // Test Case 5: all match
    CreateInvertedScanPlanBoolean(
        vertexLabel, indexId, EXPR_OP_INVERTED_OR, {"sqlite", "index", "search"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3", "4", "5", "6", "7", "8"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest013
 * @tc.desc: test inverted Scan (boolean not)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest013, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'sqlite has inverted index');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'sqlite has B-tree index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'inverted index helps to improve text search performance');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'full text search is important for sqlite');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(5, 'design a good index for better performance');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(6, 'do you want to learn good index?');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(7, 'sqlite supports full text search by index');");
    SqlUtExecuteStmt(memCtx, session,
        "INSERT INTO InvertedUtDQL(a, b) VALUES(8, 'sqlite contains inverted index and B-tree index');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 0: no match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_NOT, {"inverted", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {});

    // Test Case 1: one match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_NOT, {"sqlite", "index"}, &invertedScan);
    TestInvertedScan(invertedScan, {"4"});

    // Test Case 2: five match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_NOT, {"sqlite", "full"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "8"});

    // Test Case 3: five match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_NOT, {"index", "search"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "5", "6", "8"});

    // Test Case 4: four match
    CreateInvertedScanPlanBoolean(vertexLabel, indexId, EXPR_OP_INVERTED_NOT, {"index", "postgres"}, &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "3", "5", "6", "7", "8"});
}

/**
 * @tc.name: UtExecutorInvertedTest014
 * @tc.desc: test inverted Scan 复杂and or not
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */

void PrepareData(DbMemCtxT *memCtx, SessionT *session)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, 'inverted index sqlite');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, 'inverted index postgres');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, 'postgres sqlite sqlserver');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'fulltext index sqlite');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(5, 'fulltext index postgres');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(6, 'B-tree index sqlite');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(7, 'B-tree index postgres');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(8, 'postgres sqlite mysql');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(9, 'inverted B-tree search performance');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(10, 'inverted B-tree write performance');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(11, 'inverted index sqlite search and write');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(12, 'B-tree inverted sqlite postgres');");
}

HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest014, TestSize.Level0)
{
    PrepareData(memCtx, session);
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 0: '(sqlite AND (guasspd OR cayleydb)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "guasspd"}, {EXPR_OP_INVERTED_TERM, 0, "cayleydb"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {});

    // Test Case 1: 'inverted AND (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "11", "12"});

    // Test Case 2: '(inverted OR B-tree) AND (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2", "6", "7", "11", "12"});

    // Test Case 3: '(inverted OR B-tree) NOT (sqlite)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {"2", "7", "9", "10"});

    // Test Case 4: '(inverted OR B-tree) NOT (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {"9", "10"});

    // Test Case 5: '(inverted AND B-tree AND performance) OR (sqlite AND postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 3, ""}, {EXPR_OP_INVERTED_AND, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "performance"}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"},
            {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    TestInvertedScan(invertedScan, {"3", "8", "9", "10", "12"});
}

/**
 * @tc.name: UtExecutorInvertedTest015_1
 * @tc.desc: test Explain of boolean
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest015_1, TestSize.Level0)
{
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    ExecExplainCtxT explainCtx = {.memCtx = memCtx, .sb = &sb};
    std::string str;

    // Test Case 0: '(sqlite AND (guasspd OR cayleydb)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "guasspd"}, {EXPR_OP_INVERTED_TERM, 0, "cayleydb"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);

    // Test Case 1: 'inverted AND (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);

    // Test Case 2: '(inverted OR B-tree) AND (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);
}

/**
 * @tc.name: UtExecutorInvertedTest015_2
 * @tc.desc: test Explain of boolean
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest015_2, TestSize.Level0)
{
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    ExecExplainCtxT explainCtx = {.memCtx = memCtx, .sb = &sb};
    std::string str;

    // Test Case 3: '(inverted OR B-tree) NOT (sqlite)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);

    // Test Case 4: '(inverted OR B-tree) NOT (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_OR, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "sqlite"}, {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);

    // Test Case 5: '(inverted AND B-tree AND performance) OR (sqlite AND postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 3, ""}, {EXPR_OP_INVERTED_AND, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "inverted"}, {EXPR_OP_INVERTED_TERM, 0, "B-tree"},
            {EXPR_OP_INVERTED_TERM, 0, "performance"}, {EXPR_OP_INVERTED_TERM, 0, "sqlite"},
            {EXPR_OP_INVERTED_TERM, 0, "postgres"}},
        &invertedScan);
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);
}

/**
 * @tc.name: UtExecutorInvertedTest016
 * @tc.desc: 测试单个词项可分词 (without mock)
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest016, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文分词能力强，倒排索引更强？');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文分词全文检索？');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '中文gpt4, gpt-4 and deepseek');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // Test Case 1: 中文连续出现
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "中文分词", &invertedScan);
    TestInvertedScan(invertedScan, {"1", "2"});

    // test case 2: 中文不连续出现
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "中文索引", &invertedScan);
    TestInvertedScan(invertedScan, {"1"});

    // test case 3: 3个单词中文不连续出现
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "倒排中文索引", &invertedScan);
    TestInvertedScan(invertedScan, {"1"});

    // test case 4: 2个英文单词
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "gpt4_deepseek", &invertedScan);
    TestInvertedScan(invertedScan, {"3"});

    // test case 5: 3个英文单词
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "gpt-4_gpt4_deepseek", &invertedScan);
    TestInvertedScan(invertedScan, {"3"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest017
 * @tc.desc: 测试单个文档单个词项的BM25分数
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest017, TestSize.Level0)
{
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文中文英文日文日文日文中文中文中文');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "中文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.527417132828265"});

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "英文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.28768207245178085"});

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "日文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.4520718281385128"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest018
 * @tc.desc: 测试多个文档单个词项的BM25分数
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest018, TestSize.Level0)
{
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文中文中文中文中文日文日文日文');");
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文中文中文中文中文中文');");  // 更短
    SqlUtExecuteStmt(
        memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '中文英文日文');");  // 都出现一次，但idf不同

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "中文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.23077399914301783", "-0.24480755314495808", "-0.16670943803122915"});

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "日文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.6688513185420085", "-0.5867836720951364"});

    CreateInvertedScanPlanTerm(vertexLabel, indexId, "英文", &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.224532226919548"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest019
 * @tc.desc: 测试多个文档多个词项的BM25分数
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest019, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文中文英文英文英文日文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文分词中文分词全文索引');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '支持中文全文全文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'Btree索引和全文索引');");

    // 创建倒排索引
    CreateInvertedIndex();

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    // 测试AND的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "分词"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-2.0751470059343395"});
    // 测试OR的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "全文"}, {EXPR_OP_INVERTED_TERM, 0, "索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.000224228853071", "-0.5263130270315441", "-1.3897771478863732"});
    // 测试NOT的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "全文"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-0.5263130270315441"});
    // 测试复杂表达式的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_NOT, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "日文"}, {EXPR_OP_INVERTED_TERM, 0, "全文"},
            {EXPR_OP_INVERTED_TERM, 0, "索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.5974947132332962", "-0.5263130270315441"});
    // 测试可分词词项的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文分词"}, {EXPR_OP_INVERTED_TERM, 0, "全文索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-3.0753712347874105", "-1.3897771478863732"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest020
 * @tc.desc: 测试文档插入后的结果
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest020, TestSize.Level0)
{
    // 创建倒排索引
    CreateInvertedIndex();

    // 插入数据
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文中文英文英文英文日文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文分词中文分词全文索引');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '支持中文全文全文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'Btree索引和全文索引');");

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    // 测试可分词词项的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_NOT, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "日文"}, {EXPR_OP_INVERTED_TERM, 0, "全文"},
            {EXPR_OP_INVERTED_TERM, 0, "索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.5974947132332962", "-0.5263130270315441"});

    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文分词"}, {EXPR_OP_INVERTED_TERM, 0, "全文索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-3.0753712347874105", "-1.3897771478863732"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest021
 * @tc.desc: 测试文档删除后的结果
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest021, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文中文英文英文英文日文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文分词中文分词全文索引');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '支持中文全文全文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'Btree索引和全文索引');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(5, '日文日文日文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(6, '全文索引和中文分词和日文');");
    // 创建倒排索引
    CreateInvertedIndex();
    // 删除数据
    SqlUtExecuteStmt(memCtx, session, "DELETE from InvertedUtDQL where a >= 5;");

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    // 测试可分词词项的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_NOT, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "日文"}, {EXPR_OP_INVERTED_TERM, 0, "全文"},
            {EXPR_OP_INVERTED_TERM, 0, "索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.5974947132332962", "-0.5263130270315441"});

    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文分词"}, {EXPR_OP_INVERTED_TERM, 0, "全文索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-3.0753712347874105", "-1.3897771478863732"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest022
 * @tc.desc: 测试文档更新后的结果
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest022, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(1, '中文中文原始的文本不含janpanese');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(2, '中文分词全文索引');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(3, '支持中文全文全文');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL(a, b) VALUES(4, 'Btree索引和全文索引');");
    // 创建倒排索引
    CreateInvertedIndex();
    // 修改数据
    SqlUtExecuteStmt(memCtx, session, "update InvertedUtDQL set b = '中文中文中文英文英文英文日文' where a = 1;");
    SqlUtExecuteStmt(memCtx, session, "update InvertedUtDQL set b = '中文分词中文分词全文索引' where a = 2;");

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    // 测试可分词词项的算分
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_NOT, 2, ""},
            {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "日文"}, {EXPR_OP_INVERTED_TERM, 0, "全文"},
            {EXPR_OP_INVERTED_TERM, 0, "索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-1.5974947132332962", "-0.5263130270315441"});

    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_OR, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "中文分词"}, {EXPR_OP_INVERTED_TERM, 0, "全文索引"}},
        &invertedScan);
    TestInvertedScanBM25(invertedScan, {"-3.0753712347874105", "-1.3897771478863732"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest023
 * @tc.desc: 测试多列创建索引和查询
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest023, TestSize.Level0)
{
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(1, '中文日文英文', '中文分词能力sql');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(2, '中文中文sql', '日文日文gql');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(3, '支持中文英文分词', 'sql和gql能力');");
    // 创建倒排索引
    CreateInvertedIndex({"b", "c"});
    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // 仅查询第二列
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "中文", &invertedScan, {2});  // 查询第2列
    TestInvertedScan(invertedScan, {"1", "3"});

    // 查询两列
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "sql", &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"1", "2", "3"});

    // 布尔查询
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sql"}, {EXPR_OP_INVERTED_TERM, 0, "gql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"2", "3"});

    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_NOT, 2, ""}, {EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "日文"},
            {EXPR_OP_INVERTED_TERM, 0, "中文"}, {EXPR_OP_INVERTED_TERM, 0, "sql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"3"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest023
 * @tc.desc: 测试数据增量更新和删除后的多列查询
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest024, TestSize.Level0)
{
    // 创建倒排索引
    CreateInvertedIndex({"b", "c"});

    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(1, '中文日文英文', '中文分词能力sql');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(2, '中文中文sql', '日文日文gql');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(3, '支持中文英文分词', 'sql和gql能力');");
    SqlUtExecuteStmt(memCtx, session, "INSERT INTO InvertedUtDQL VALUES(4, '中文SQL', '无关字体');");

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;

    // insert后查询多列
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sql"}, {EXPR_OP_INVERTED_TERM, 0, "gql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"2", "3"});

    // 更新列
    SqlUtExecuteStmt(memCtx, session, "UPDATE InvertedUtDQL set b = '中文日文GQL' where a = 1;");
    SqlUtExecuteStmt(memCtx, session, "UPDATE InvertedUtDQL set b = '中文sql sql', c = '日文日文gql' where a = 2;");
    // 更新后查询多列
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sql"}, {EXPR_OP_INVERTED_TERM, 0, "gql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"1", "2", "3"});

    // 删除列
    SqlUtExecuteStmt(memCtx, session, "delete from InvertedUtDQL where a >= 3;");
    // 删除后查询多列
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sql"}, {EXPR_OP_INVERTED_TERM, 0, "gql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    TestInvertedScan(invertedScan, {"1", "2"});

    QryReleaseAllLabelLatch(session);
}

/**
 * @tc.name: UtExecutorInvertedTest015_1
 * @tc.desc: 测试多列查询的explain
 * @tc.type: FUNC
 * @tc.require: None
 * @tc.author: luoyongping
 */
HWTEST_F(UtEmbSqlExecutorInverted, UtExecutorInvertedTest025, TestSize.Level0)
{
    // 创建倒排索引
    CreateInvertedIndex({"b", "c"});

    DmVertexLabelT *vertexLabel = SqlUtGetVertexLabel("InvertedUtDQL", session);
    ASSERT_NE(vertexLabel, nullptr);
    uint32_t indexId = UtGetIndexIdByName(vertexLabel, "inv_index");
    InvertedScanT *invertedScan = NULL;
    ExecExplainCtxT explainCtx = {.memCtx = memCtx, .sb = &sb};
    std::string str;

    // Test Case 0: '(sqlite AND (guasspd OR cayleydb)'
    CreateInvertedScanPlanTerm(vertexLabel, indexId, "sql", &invertedScan, {1, 2});  // 查询第1, 2列
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);

    // Test Case 1: 'inverted AND (sqlite OR postgres)'
    CreateInvertedScanPlanBooleanComplex(vertexLabel, indexId,
        {{EXPR_OP_INVERTED_AND, 2, ""}, {EXPR_OP_INVERTED_TERM, 0, "sql"}, {EXPR_OP_INVERTED_TERM, 0, "gql"}},
        &invertedScan, {1, 2});  // 查询第1, 2列
    ASSERT_EQ(ExecExplainNode(&explainCtx, (PlanT *)invertedScan, 0), GMERR_OK);
    str = DmSbDump(explainCtx.sb);
    std::cout << str << std::endl;
    DmSbReset(explainCtx.sb);
}
