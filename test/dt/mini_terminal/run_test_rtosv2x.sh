#!/bin/bash
# Copyright (c) Huawei Technologies Co., Ltd. 2018-2019. All rights reserved.
# version V1.0
#
# used for run dt in hpe
#

set -e

CUR_DIR=$(pwd)

function run_st_record_result() {
    export PATH=$PATH:/usr/local/bin
    cd ${CUR_DIR}/st_result/${curTime}/
    if [ ! -d "st_$1" ]; then
        mkdir st_$1
    fi
    if [ ! -d "${CUR_DIR}/$1" ]; then
        echo "[ERROR] ${CUR_DIR}/$1: No such file or directory" 2>&1 | tee -a ${CUR_DIR}/st_result/${curTime}/st_$1/$1_record
        return
    fi
    echo '-----------------------------------------------------' 2>&1 | tee -a ${CUR_DIR}/st_result/${curTime}/st_$1/$1_record
    echo "[ st_$1 ] begin" 2>&1 | tee -a ${CUR_DIR}/st_result/${curTime}/st_$1/$1_record
    cd ${CUR_DIR}/$1
    ./st_$1 $2 2>&1 | tee -a ${CUR_DIR}/st_result/${curTime}/st_$1/$1_record 2>&1
    echo "Finish st_$1"
    if [ `ps | grep 'vm.elf' | grep -v 'grep' | wc -l` != 0 ]; then
        kill -9 $(ps | grep 'vm.elf' | grep -v 'grep' | awk '{print $1}')
    fi
    ipcrm -a
}

function run_ut_record_result() {
    export PATH=$PATH:/usr/local/bin
    cd ${CUR_DIR}/ut_result/${curTime}/
    if [ ! -d "ut_$1" ]; then
        mkdir ut_$1
    fi
    if [ ! -d "${CUR_DIR}/$1" ]; then
        echo "[ERROR] ${CUR_DIR}/$1: No such file or directory" 2>&1 | tee -a ${CUR_DIR}/ut_result/${curTime}/ut_$1/$1_record
        return
    fi
    echo '-----------------------------------------------------' 2>&1 | tee -a ${CUR_DIR}/ut_result/${curTime}/ut_$1/$1_record
    echo "[ ut_$1 ] begin" 2>&1 | tee -a ${CUR_DIR}/ut_result/${curTime}/ut_$1/$1_record
    cd ${CUR_DIR}/$1
    ./ut_$1 $2 2>&1 | tee -a ${CUR_DIR}/ut_result/${curTime}/ut_$1/$1_record
    echo "Finish ut_$1"
}

function run_st_split() {
    if [ ! -d "st_result" ]; then
        mkdir st_result
    fi
    mkdir st_result/${curTime}
    export PATH=$PATH:/usr/local/bin
    if [ `ps | grep 'vm.elf' | grep -v 'grep' | wc -l` != 0 ]; then
        kill -9 $(ps | grep 'vm.elf' | grep -v 'grep' | awk '{print $1}')
    fi
    ipcrm -a
    ipcs
    sleep 1s
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xclient" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_client
        cd ${CUR_DIR}/client
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result client --gtest_filter=StClientAbnormal.*
            run_st_record_result client --gtest_filter=StClientAlarm.*
            run_st_record_result client --gtest_filter=StClientAsync.*
            run_st_record_result client --gtest_filter=StClientAutoInc.*
            run_st_record_result client --gtest_filter=StClientBatch.*
            run_st_record_result client --gtest_filter=StClientCatalog.*
            run_st_record_result client --gtest_filter=StClientConfig.*
            run_st_record_result client --gtest_filter=StClientCypher.*
            run_st_record_result client --gtest_filter=StClientDeltaStore.*
            run_st_record_result client --gtest_filter=StClientEdge.*
            run_st_record_result client --gtest_filter=StClientHeartbeat.*
            run_st_record_result client --gtest_filter=StClientKv.*
            run_st_record_result client --gtest_filter=StClientList.*
            run_st_record_result client --gtest_filter=StClientCoreDump.*
            run_st_record_result client --gtest_filter=StClientNamespace.*
            run_st_record_result client --gtest_filter=StClientStruct.*
            run_st_record_result client --gtest_filter=StClientSubNotice.*
            run_st_record_result client --gtest_filter=StClientSub.*
            run_st_record_result client --gtest_filter=StClientSuperField.*
            run_st_record_result client --gtest_filter=StClientAsyncNoTimoutEpoll.*
            run_st_record_result client --gtest_filter=StClientAsyncNoResponseEpoll.*
            run_st_record_result client --gtest_filter=StClientAsyncSameEpoll.*
            run_st_record_result client --gtest_filter=StClientAsync.*
            run_st_record_result client --gtest_filter=StClientTx.*
            run_st_record_result client --gtest_filter=StClientTree.*
            run_st_record_result client --gtest_filter=StClientVertexQuery.*
            run_st_record_result client --gtest_filter=StClientVertex.*
            run_st_record_result client --gtest_filter=StClientYang.*
        else
            run_st_record_result client ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_client
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdatamodel" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_datamodel
        cd ${CUR_DIR}/datamodel
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result datamodel --gtest_filter=BitMapSt.*
            run_st_record_result datamodel --gtest_filter=StDataModel.*;
        else
            run_st_record_result datamodel ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_datamodel
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdeltastore" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_deltastore
        cd ${CUR_DIR}/deltastore
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result deltastore --gtest_filter=DeltaStorePriv.*
            run_st_record_result deltastore --gtest_filter=DeltaStoreSt.*;
        else
            run_st_record_result deltastore $1
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_deltastore
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xquery" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_query
        cd ${CUR_DIR}/query
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result query --gtest_filter=StQueryCheck.*
            run_st_record_result query --gtest_filter=StQueryCmd.*
            run_st_record_result query --gtest_filter=StQueryCheckPriv.*
            run_st_record_result query --gtest_filter=StQueryLabelDDL.*
            run_st_record_result query --gtest_filter=StQueryRoleDDL.*
            run_st_record_result query --gtest_filter=StQueryDqlVertex.*
            run_st_record_result query --gtest_filter=StQueryDMLKV.*
            run_st_record_result query --gtest_filter=StQueryDMLSplit.*
            run_st_record_result query --gtest_filter=StQueryDML.*
            run_st_record_result query --gtest_filter=StQueryDqlVertex.*
            run_st_record_result query --gtest_filter=StQueryFlowCtr.*
            run_st_record_result query --gtest_filter=StQueryCheckObjPrivTolerant.*
            run_st_record_result query --gtest_filter=StQueryCheckObjPriv.*
            run_st_record_result query --gtest_filter=StQueryPrivilegel.*
            run_st_record_result query --gtest_filter=StQueryTree.*
            run_st_record_result query --gtest_filter=StQueryUser.*
            run_st_record_result query --gtest_filter=StQueryStorageIndexView.*
            run_st_record_result query --gtest_filter=StQueryCataView.*;
        else
            run_st_record_result query ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_query
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "query_subs" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_query_subs
        cd ${CUR_DIR}/query_subs
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result query_subs --gtest_filter=StQuerySubsDDL.*
            run_st_record_result query_subs --gtest_filter=StQuerySubs.*
            run_st_record_result query_subs --gtest_filter=StQueryFullSync.*;
        else
            run_st_record_result query_subs ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_query_subs
            core_num=`find . -name 'core*' | wc -l`
        done
        cd -
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "X001_connect" ]; then    
        mkdir ${CUR_DIR}/st_result/${curTime}/st_001_connect
        cd ${CUR_DIR}/001_connect
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result 001_connect --gtest_filter=StRuntimeRecvMode.*
            run_st_record_result 001_connect --gtest_filter=St001ConnectBase.*
            run_st_record_result 001_connect --gtest_filter=StPortal.*
            run_st_record_result 001_connect --gtest_filter=StRuntime.*
        else
            run_st_record_result 001_connect ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_001_connect
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xstorage" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_storage
        cd ${CUR_DIR}/storage
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result storage --gtest_filter=StAbnormalExit.*
            run_st_record_result storage --gtest_filter=StCHHashIdx.*
            run_st_record_result storage --gtest_filter=StStorage_Defragment.*
            run_st_record_result storage --gtest_filter=StVertexSimple.*
            run_st_record_result storage --gtest_filter=StStorage.*
            run_st_record_result storage --gtest_filter=StStorageHc.*
            run_st_record_result storage --gtest_filter=StStorage_LargeObject.*
            run_st_record_result storage --gtest_filter=StHashClusterIdx.*
            run_st_record_result storage --gtest_filter=StStoragePerf.*
            run_st_record_result storage --gtest_filter=StStorage_LiteTrx.*
            run_st_record_result storage --gtest_filter=StStorage_Redo.*
            run_st_record_result storage --gtest_filter=StSortedIdx.*
            run_st_record_result storage --gtest_filter=StStorageEdgeTopo.*;
        else
            run_st_record_result storage ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_storage
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xtools" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_tools
        cd ${CUR_DIR}/tools
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_st_record_result tools --gtest_filter=StGmAdmin.*
            run_st_record_result tools --gtest_filter=StGmeximport.*
            run_st_record_result tools --gtest_filter=StGmeximportV3.*
            run_st_record_result tools --gtest_filter=StGmlog.*
            run_st_record_result tools --gtest_filter=StGmrule.*
            run_st_record_result tools --gtest_filter=StGmSysview.*
            run_st_record_result tools --gtest_filter=StRecord.*
            run_st_record_result tools --gtest_filter=StUpdateSchema.*;
        else
            run_st_record_result tools ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_tools
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
}

function run_st() {
    if [ ! -d "st_result" ]; then
        mkdir st_result
    fi
    mkdir st_result/${curTime}
    export PATH=$PATH:/usr/local/bin
    if [ `ps | grep 'vm.elf' | grep -v 'grep' | wc -l` != 0 ]; then
        kill -9 $(ps | grep 'vm.elf' | grep -v 'grep' | awk '{print $1}')
    fi
    ipcrm -a
    ipcs
    sleep 1s
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xclient" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_client
        cd ${CUR_DIR}/client
        rm -rf core*
        run_st_record_result clien
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_client
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdatamodel" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_datamodel
        cd ${CUR_DIR}/datamodel
        rm -rf core*
        run_st_record_result datamodel
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_datamodel
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdeltastore" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_deltastore
        cd ${CUR_DIR}/deltastore
        rm -rf core*
        run_st_record_result deltastore
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_deltastore
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xquery" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_query
        cd ${CUR_DIR}/query
        rm -rf core*
        run_st_record_result query
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_query
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "query_subs" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_query_subs
        cd ${CUR_DIR}/query_subs
        rm -rf core*
        run_st_record_result query_subs
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_query_subs
            core_num=`find . -name 'core*' | wc -l`
        done
        cd -
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xruntime" ]; then    
        mkdir ${CUR_DIR}/st_result/${curTime}/st_runtime
        cd ${CUR_DIR}/runtime
        rm -rf core*
        run_st_record_result runtim
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_runtime
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xstorage" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_storage
        cd ${CUR_DIR}/storage
        rm -rf core*
        run_st_record_result storage
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_storage
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xtools" ]; then
        mkdir ${CUR_DIR}/st_result/${curTime}/st_tools
        cd ${CUR_DIR}/tools
        rm -rf core*
        run_st_record_result tools
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../st_result/${curTime}/st_tools
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
}

function run_ut_split() {
    if [ ! -d "ut_result" ]; then
        mkdir ut_result
    fi
    mkdir ut_result/${curTime}
    export PATH=$PATH:/usr/local/bin
    sleep 1s
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xclient" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_client
        cd ${CUR_DIR}/client
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_ut_record_result client --gtest_filter=UtClientInterfaceConnCheck.*
            run_ut_record_result client --gtest_filter=UtClientInterfaceCheck.*
            run_ut_record_result client --gtest_filter=UtClientInterfaceGraphCheck.*
            run_ut_record_result client --gtest_filter=UtClientInterfaceKVCheck.*
            run_ut_record_result client --gtest_filter=UtClientInterfacePrivilegeCheck.*
            run_ut_record_result client --gtest_filter=UtClientInterfaceSubCheck.*
            run_ut_record_result client --gtest_filter=UtClientBatchOp.*
            run_ut_record_result client --gtest_filter=UtClientConn.*
            run_ut_record_result client --gtest_filter=UtClientFlowControl.*
            run_ut_record_result client --gtest_filter=UtClientGraphEdge.*
            run_ut_record_result client --gtest_filter=UtClientKv.*
            run_ut_record_result client --gtest_filter=UtClientCataLog.*
            run_ut_record_result client --gtest_filter=UtClientMsgCheck.*
            run_ut_record_result client --gtest_filter=UtPrivilege.*
            run_ut_record_result client --gtest_filter=UtClientRegFunc.*
            run_ut_record_result client --gtest_filter=UtClientVertex.*;
        else
            run_ut_record_result client ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_client
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xcommon" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_common
        cd ${CUR_DIR}/common
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_ut_record_result common --gtest_filter=UtCommon.*
            run_ut_record_result common --gtest_filter=UtAdptLock.*
            run_ut_record_result common --gtest_filter=UtBlockShmemCtx.*
            run_ut_record_result common --gtest_filter=UtConfigFile.*
            run_ut_record_result common --gtest_filter=UtCommonCpuStat.*
            run_ut_record_result common --gtest_filter=UtDbUtils.*
            run_ut_record_result common --gtest_filter=UtDyMemCtx.*
            run_ut_record_result common --gtest_filter=UtDbError.*
            run_ut_record_result common --gtest_filter=utGaList.*
            run_ut_record_result common --gtest_filter=UtHashMethodCompare.*
            run_ut_record_result common --gtest_filter=UtHashmap.*
            run_ut_record_result common --gtest_filter=UtLabelLatch.*
            run_ut_record_result common --gtest_filter=utList.*
            run_ut_record_result common --gtest_filter=UtLog.*
            run_ut_record_result common --gtest_filter=UtMemAdpt.*
            run_ut_record_result common --gtest_filter=UtMemBlk.*
            run_ut_record_result common --gtest_filter=UtMemCheck.*
            run_ut_record_result common --gtest_filter=UtPagePool.*
            run_ut_record_result common --gtest_filter=UtMemSegment.*
            run_ut_record_result common --gtest_filter=UTMultiWayTree.*
            run_ut_record_result common --gtest_filter=utPriorityQueue.*
            run_ut_record_result common --gtest_filter=UtRpcMsg.*
            run_ut_record_result common --gtest_filter=UtRWLatch.*
            run_ut_record_result common --gtest_filter=UtRWSpinLock.*
            run_ut_record_result common --gtest_filter=UtShmArrray.*
            run_ut_record_result common --gtest_filter=UtSignal.*
            run_ut_record_result common --gtest_filter=UtSocket.*
            run_ut_record_result common --gtest_filter=UtSpinLock.*
            run_ut_record_result common --gtest_filter=UtStatus.*
            run_ut_record_result common --gtest_filter=utString.*
            run_ut_record_result common --gtest_filter=UtTcp.*
            run_ut_record_result common --gtest_filter=UtThread.*
            run_ut_record_result common --gtest_filter=UtDbTime.*
            run_ut_record_result common --gtest_filter=utRtosTimer.*;
        else
            run_ut_record_result common ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_common
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdatamodel" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_datamodel
        cd ${CUR_DIR}/datamodel
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_ut_record_result datamodel --gtest_filter=DatamodelClient.*
            run_ut_record_result datamodel --gtest_filter=UtDmBaseLabel.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_basic.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_bitfield.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_edge.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_edgeTopo.*
            run_ut_record_result datamodel --gtest_filter=UtDmEdgeLabel.*
            run_ut_record_result datamodel --gtest_filter=UtFormatPrint.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_indexkey.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_kv.*
            run_ut_record_result datamodel --gtest_filter=UtDmKvTable.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_list_property.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_math.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_node.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_print.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_record.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_respool.*
            run_ut_record_result datamodel --gtest_filter=UtDmSchema.*
            run_ut_record_result datamodel --gtest_filter=UtDmSubscription.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_vertex_delta_update.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_vertex_nest.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_vertex_superfield.*
            run_ut_record_result datamodel --gtest_filter=ut_dm_vertex.*
            run_ut_record_result datamodel --gtest_filter=UtDmVertexLabel.*;
        else
            run_ut_record_result datamodel ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_datamodel
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdeltastore" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_deltastore
        cd ${CUR_DIR}/deltastore
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_ut_record_result deltastore --gtest_filter=DeltaStoreDirectReadTest.*
            run_ut_record_result deltastore --gtest_filter=DeltaStoreTest.*
            run_ut_record_result deltastore --gtest_filter=DsFlowCtrl.*
            run_ut_record_result deltastore --gtest_filter=DeltaStoreHashTest.*
            run_ut_record_result deltastore --gtest_filter=DsLgcRedoUt.*
            run_ut_record_result deltastore --gtest_filter=DstoreMergeViewTest.*
            run_ut_record_result deltastore --gtest_filter=DstoreLgcRedoUt.*
            run_ut_record_result deltastore --gtest_filter=UtStartMerge.*
            run_ut_record_result deltastore --gtest_filter=DstoreLogicRedo.*;
        else
            run_ut_record_result deltastore ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_deltastore
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xtools" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_tools
        cd ${CUR_DIR}/tools
        rm -rf core*
        if [ "X${2}" = "X" ]; then
            run_ut_record_result tools --gtest_filter=UtGmerror.*
            run_ut_record_result tools --gtest_filter=UtGmserver.*
            run_ut_record_result tools --gtest_filter=UtGmutils.*
            run_ut_record_result tools --gtest_filter=UtTools.*;
        else
            run_ut_record_result tools ${2}
        fi
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_tools
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
}

function run_ut() {
    if [ ! -d "ut_result" ]; then
        mkdir ut_result
    fi
    mkdir ut_result/${curTime}
    export PATH=$PATH:/usr/local/bin
    sleep 1s
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xclient" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_client
        cd ${CUR_DIR}/client
        rm -rf core*
        run_ut_record_result client
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_client
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xcommon" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_common
        cd ${CUR_DIR}/common
        rm -rf core*
        run_ut_record_result common
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_common
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdatamodel" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_datamodel
        cd ${CUR_DIR}/datamodel
        rm -rf core*
        run_ut_record_result datamodel
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_datamodel
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xdeltastore" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_deltastore
        cd ${CUR_DIR}/deltastore
        rm -rf core*
        run_ut_record_result deltastore
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_deltastore
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
    if [ "X$1" = "X" ] || [ "X$1" = "Xall" ] || [ "X$1" = "Xtools" ]; then
        mkdir ${CUR_DIR}/ut_result/${curTime}/ut_tools
        cd ${CUR_DIR}/tools
        rm -rf core*
        run_ut_record_result tools
        core_num=`find . -name 'core*' | wc -l`
        while [ $core_num -ne 0 ]
        do
            core_file=`ls -tr | grep "core*" | tail -1`
            mv $core_file ../ut_result/${curTime}/ut_tools
            core_num=`find . -name 'core*' | wc -l`
        done
        cd ..
    fi
}

function unzip_all() {
    if [ -f ${CUR_DIR}/server_strip.tar.gz ]; then
        tar zxvf ${CUR_DIR}/server_strip.tar.gz
        cp -arf ${CUR_DIR}/server_strip/* /hpe/
    fi
    if [ -f ${CUR_DIR}/client.tar.gz ]; then
        tar zxvf ${CUR_DIR}/client.tar.gz
        cp -arf ${CUR_DIR}/client/lib/* /lib/
        cp -arf ${CUR_DIR}/client/bin/* /bin/
    fi
    if [ -f ${CUR_DIR}/server.tar.gz ]; then
        tar zxvf ${CUR_DIR}/server.tar.gz
    fi
}

function run_st_get_static() {
    stAllSucCnt=0
    stAllFailCnt=0
    stAllCoreCnt=0
    cd ${CUR_DIR}/st_result/${1}/
    for stFolder in `ls ${CUR_DIR}/st_result/${1}/`
    do
        stModuleSucCnt=0
        stModuleFailCnt=0
        stModuleCoreCnt=0
        for file in `ls ${stFolder}`
        do
            if [ "${file##*_}"x = "record"x ]; then
                echo "${file}"
                stModuleSucCnt=`grep '      OK ]' ${stFolder}/${file} | wc -l`
                stModuleFailCnt=`grep ' FAILED  ]' ${stFolder}/${file} | wc -l`                
                stModuleCoreCnt=`find ${stFolder} -name 'core*.task' | wc -l`
                echo "${stFolder}'s [PASSED]:${stModuleSucCnt}, [FAILED]:${stModuleFailCnt}, [CORE DUMPED]:${stModuleCoreCnt}" | tee -a ${stFolder}/${file}
                break
            fi
        done
        stAllSucCnt=$((stAllSucCnt+stModuleSucCnt))
        stAllFailCnt=$((stAllFailCnt+stModuleFailCnt))
        stAllCoreCnt=$((stAllCoreCnt+stModuleCoreCnt))
    done
    echo "TOTAL [PASSED]:${stAllSucCnt}, [FAILED]:${stAllFailCnt}, [CORE DUMPED]:${stAllCoreCnt}"
}

function run_ut_get_static() {
    utAllSucCnt=0
    utAllFailCnt=0
    utAllCoreCnt=0
    cd ${CUR_DIR}/ut_result/${1}/
    for utFolder in `ls ${CUR_DIR}/ut_result/${1}/`
    do
        utModuleSucCnt=0
        utModuleFailCnt=0
        utModuleCoreCnt=0
        for file in `ls ${utFolder}`
        do
            if [ "${file##*_}"x = "record"x ]; then
                echo "${file}"
                utModuleSucCnt=`grep '      OK ]' ${utFolder}/${file} | wc -l`
                utModuleFailCnt=`grep ' FAILED  ]' ${utFolder}/${file} | wc -l`
                utModuleCoreCnt=`find ${utFolder} -name 'core*.task' | wc -l`
                echo "${utFolder}'s [PASSED]:${utModuleSucCnt}, [FAILED]:${utModuleFailCnt}, [CORE DUMPED]:${utModuleCoreCnt}" | tee -a ${utFolder}/${file}
                break
            fi
        done
        utAllSucCnt=$((utAllSucCnt+utModuleSucCnt))
        utAllFailCnt=$((utAllFailCnt+utModuleFailCnt))
        utAllCoreCnt=$((utAllCoreCnt+utModuleCoreCnt))
    done
    echo "TOTAL [PASSED]:${utAllSucCnt}, [FAILED]:${utAllFailCnt}, [CORE DUMPED]:${utAllCoreCnt}"
}

function stop_server() {
    kill -9 $(ps | grep 'vm.elf' | grep -v 'grep' | awk '{print $1}') 
    ipcrm -a
}

function enable_env() {
    configFile=${CUR_DIR}/conf/gmserver.ini
    sed -i 's/^subsChannelGlobalShareMemSizeMax.*/subsChannelGlobalShareMemSizeMax = 4/g' ${configFile}
    sed -i 's/^subsChannelGlobalDynamicMemSizeMax.*/subsChannelGlobalDynamicMemSizeMax = 4/g' ${configFile}
    sed -i 's/^maxSeMem.*/maxSeMem = 25/g' ${configFile}
    sed -i 's/^maxTotalShmSize.*/maxTotalShmSize = 64/g' ${configFile}
    sed -i 's/^maxConnNum.*/maxConnNum = 64/g' ${configFile}
    sed -i 's/^deviceSize.*/deviceSize = 4/g' ${configFile}
    sed -i 's/^userPolicyMode.*/userPolicyMode = 0/g' ${configFile}
    cp ${configFile} /file/

    if [ ! -d "/usr/local/hpe" ]; then
        ln -s /hpe /usr/local/hpe
    fi
    echo 1 > /hpe/conf/app.cfg
    echo /usr/local/hpe/gmserver >> /hpe/conf/app.cfg
    if [ ! -d "/dev/mpool" ]; then
        touch /dev/mpool
    fi
    CUR_DIR=$(pwd)
    export GMDB_HOME=$CUR_DIR
}

function modify_cfg()
{
    fileName=$1
    val=`grep -w "maxSeMem =" "${fileName}" | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    if [ ${val} -gt 25 ]; then
        sed -i "/^maxSeMem/cmaxSeMem = 25" "${fileName}"
    fi
    
    val=`grep -w "maxTotalShmSize =" "${fileName}" | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    if [ ${val} -gt 64 ]; then
        sed -i "/^maxTotalShmSize/cmaxTotalShmSize = 64" "${fileName}"
    fi
    
    val=`grep -w "workerHungThreshold =" "${fileName}" | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    if [ '${val}' \> '3,4,5' ]; then
        sed -i "/^workerHungThreshold/cworkerHungThreshold = 3,4,5" "${fileName}"
    fi
    
    val=`grep -w "userPolicyMode =" "${fileName}" | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    if [ ${val} -gt 0 ]; then
        sed -i "/^userPolicyMode/cuserPolicyMode = 0" "${fileName}"
    fi
    
    val=`grep -w "maxConnNum =" "${fileName}" | awk -F"=" '{print $2}' | tr -d ' ' | tr -d '\r'`
    maxConn=64
    if [ ${val} -gt ${maxConn} ]
    then
        sed -i "/^maxConnNum/cmaxConnNum = 64" "${fileName}"
    fi
}

function main() {
    curTime=$(date "+%Y-%m-%d_%H-%M-%S")
    if [ "$1" = "unzip" ]; then
        unzip_all
    elif [ "$1" = "run_st_split" ]; then
        startTime_s=`date +%s`
        run_st_split $2 $3
        endTime_s=`date +%s`
        usedTime=$((endTime_s-startTime_s))
        echo "usedTime: ${usedTime}s"
        run_st_get_static ${curTime}
    elif [ "$1" = "run_st" ]; then
        startTime_s=`date +%s`
        run_st $2 $3
        endTime_s=`date +%s`
        usedTime=$((endTime_s-startTime_s))
        echo "usedTime: ${usedTime}s"
        run_st_get_static ${curTime}
    elif [ "$1" = "run_ut_split" ]; then
        startTime_s=`date +%s`
        run_ut_split $2 $3
        endTime_s=`date +%s`
        usedTime=$((endTime_s-startTime_s))
        echo "usedTime: ${usedTime}s"
        run_ut_get_static ${curTime}
    elif [ "$1" = "run_ut" ]; then
        startTime_s=`date +%s`
        run_ut $2 $3
        endTime_s=`date +%s`
        usedTime=$((endTime_s-startTime_s))
        echo "usedTime: ${usedTime}s"
        run_ut_get_static ${curTime}
    elif [ "$1" = "stop" ]; then
        stop_server
    elif [ "$1" = "env" ]; then
        enable_env
    elif [ "$1" = "cfg" ]; then
        if [ ! -f ${2} ]; then
            echo "${2} does not exist, please check"
            exit 1
        fi
        modify_cfg $2
    elif [ "$1" = "stat_st" ]; then
        run_st_get_static $2
    elif [ "$1" = "stat_ut" ]; then
        run_ut_get_static $2
    else
        echo "unknown opt [${1}], only support [run_st, run_ut, unzip, env]"
    fi
}

main $@

