/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2020. All rights reserved.
 */

#include <cstdio>
#include <iostream>
#include <vector>
#include "gtest/gtest.h"
#include "db_table_space.h"
#include "stub.h"
#include "dm_meta_prop_label.h"
#include "db_common_init.h"
#include "db_log.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#include "se_trx_inner.h"
#include "se_trx_mgr.h"
#include "se_define.h"
#include "se_page_mgr.h"
#include "se_heap_inner.h"
#include "common_init.h"
#include "storage_session.h"
#include "se_label_readview.h"
#include "storage_ut_common.h"
#include "dm_meta_vertex_label.h"

using namespace std;

#define TRX_ID 123
#define FAKE_LABEL_ID 256

#ifdef __cplusplus
extern "C" {
#endif
extern void TrxCntrLruCacheRemoveNode(TrxContainerCtxListT *cntrCtxList);
extern void TrxReleaseContainerLruCache(TrxT *trx);
#ifdef __cplusplus
}
#endif

static uint64_t g_namespaceTrxIdArray[100] = {0};

static Status UtGetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t *trxId, uint64_t *trxIdLastModify, uint64_t *trxCommitTime, DbInstanceHdT dbInstance)
{
    if (trxId == NULL) {
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    *trxId = g_namespaceTrxIdArray[labelId];
    *trxIdLastModify = g_namespaceTrxIdArray[labelId];
    return GMERR_OK;
}

static Status UtSetLabelLastTrxIdAndTrxCommitTimeById(
    uint32_t labelId, uint64_t trxId, uint64_t trxCommitTime, bool trxIsModify, DbInstanceHdT dbInstance)
{
    g_namespaceTrxIdArray[labelId] = trxId;
    return GMERR_OK;
}

static Status UtGetLabelNameByEdgeLabelId(uint32_t elId, char *labelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(labelName);
    return GMERR_DATA_EXCEPTION;
}
DbMemCtxT *trxmgrtopShmMemCtx = NULL;
class UtStorageTrxMgr : public testing::Test {
public:
    static SeInstanceT *seIns;
    static SeRunCtxHdT g_seRunCtx;
    static void *topDynamicMemCtx;
    static DmVertexLabelT stubVtxLabel;

protected:
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        if (ret != DB_SUCCESS) {
            printf("ret = %d\n", ret);
            ASSERT_EQ(0, 1);
        }

        DbMemCtxT *topShmMemCtx = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
        ASSERT_TRUE(topShmMemCtx != nullptr);
        trxmgrtopShmMemCtx = topShmMemCtx;

        DbMemCtxArgsT dyargs = {0};
        dyargs.ctxSize = sizeof(DbDynamicMemCtxT);
        dyargs.memType = DB_DYNAMIC_MEMORY;
        dyargs.init = DynamicAlgoInit;
        topDynamicMemCtx =
            (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "Se Dynamic MemCtx", &dyargs);
        // set up default dynamic memctx
        DbMemCtxSwitchTo((DbMemCtxT *)topDynamicMemCtx);

        SeConfigT config = {0};
        config.deviceSize = SE_DEFAULT_DEV_SIZE;
        config.pageSize = SE_DEFAULT_PAGE_SIZE;
        config.maxSeMem = SE_DEFAULT_MAX_MEM / 4;
        config.instanceId = GET_INSTANCE_ID;
        config.maxTrxNum = MAX_TRX_NUM;
        seIns = NULL;
        ret = SeCreateInstance(NULL, topShmMemCtx, &config, (SeInstanceHdT *)&seIns);
        ASSERT_EQ(GMERR_OK, ret);
        OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelLastTrxIdAndTrxCommitTimeById, UtGetLabelLastTrxIdAndTrxCommitTimeById,
            UtGetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[TRX_CHECK_READVIEW_NUM] = {
            UtSetLabelLastTrxIdAndTrxCommitTimeById, UtSetLabelLastTrxIdAndTrxCommitTimeById,
            UtSetLabelLastTrxIdAndTrxCommitTimeById};
        OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM] = {
            UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId, UtGetLabelNameByEdgeLabelId};
        SeInitTrxMgrCheckFunc(GET_INSTANCE_ID, setFunc, getFunc, getLabelName);
    };

    static void TearDownTestCase()
    {
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

SeInstanceT *UtStorageTrxMgr::seIns{nullptr};
SeRunCtxHdT UtStorageTrxMgr::g_seRunCtx{nullptr};
void *UtStorageTrxMgr::topDynamicMemCtx{nullptr};
DmVertexLabelT UtStorageTrxMgr::stubVtxLabel{0};

TEST_F(UtStorageTrxMgr, SeOpenAndAllocCtx)
{
    int32_t status = SeOpenWithNewSession(
        GET_INSTANCE_ID, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);

    status = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);
}

TEST_F(UtStorageTrxMgr, RunTrx)
{
    int32_t status = SeOpenWithNewSession(
        GET_INSTANCE_ID, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    int32_t ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);

    TrxCfgT cfg;
    SeTransGetCfg(UtStorageTrxMgr::g_seRunCtx, &cfg);
    IsolationLevelE isolationLevel = cfg.isolationLevel;
    EXPECT_EQ(READ_COMMITTED, isolationLevel);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    TrxMgrT *trxMgr = (TrxMgrT *)(UtStorageTrxMgr::g_seRunCtx)->trxMgr;
    EXPECT_EQ((TrxIdT)1, trx->base.trxId);
    EXPECT_EQ(TRX_STATE_ACTIVE, trx->base.state);
    TrxIdListT *rwTrxIds = (TrxIdListT *)((uint8_t *)trxMgr + trxMgr->rwTrxIdsOffset);
    TrxIdListT *roTrxIds = (TrxIdListT *)((uint8_t *)trxMgr + trxMgr->roTrxIdsOffset);
    uint16_t idListLen = rwTrxIds->listLen;
    EXPECT_EQ(0, idListLen);
    idListLen = roTrxIds->listLen;
    EXPECT_EQ(1, idListLen);

    ret = SeTransRollback(UtStorageTrxMgr::g_seRunCtx, false);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trx->base.state);
    idListLen = rwTrxIds->listLen;
    EXPECT_EQ(0, idListLen);
    idListLen = roTrxIds->listLen;
    EXPECT_EQ(0, idListLen);

    trxCfg.readOnly = false;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    idListLen = rwTrxIds->listLen;
    EXPECT_EQ(1, idListLen);
    idListLen = roTrxIds->listLen;
    EXPECT_EQ(0, idListLen);

    TrxT *tmpTrx = TrxMgrGetActiveTrxByTrxId(trxMgr, trx->base.trxId, trx->base.trxSlot, true);
    EXPECT_NE((TrxT *)NULL, tmpTrx);
    tmpTrx = TrxMgrGetActiveTrxByTrxId(trxMgr, trx->base.trxId + 1, trx->base.trxSlot + 1, true);
    EXPECT_EQ((TrxT *)NULL, tmpTrx);

    ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId + 1);
    EXPECT_EQ(ret, NO_DATA_INTER);

    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId - 1);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SeRunCtxT *seRunCtxPtr = (UtStorageTrxMgr::g_seRunCtx);
    ReadViewClose((TrxT *)seRunCtxPtr->trx);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    status = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);
}

TEST_F(UtStorageTrxMgr, SeCLoseAndFreeTrx)
{
    int32_t status = SeOpenWithNewSession(
        GET_INSTANCE_ID, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);

    TrxMgrT *trxMgr = (TrxMgrT *)(UtStorageTrxMgr::g_seRunCtx)->trxMgr;
    status = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(status, 0);
    TrxPoolT *trxPool = (TrxPoolT *)((uint8_t *)trxMgr + trxMgr->trxPoolOffset);
    EXPECT_EQ(0, trxPool->freeHead);
    EXPECT_EQ(0, trxPool->usedCnt);
    EXPECT_EQ(0, trxMgr->trxCnt);
}

TEST_F(UtStorageTrxMgr, TrxStateCheck)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransRollback(UtStorageTrxMgr::g_seRunCtx, false);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    ret = SeTransRollback(UtStorageTrxMgr::g_seRunCtx, false);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);
    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    TrxAbort(trx);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ABORT, trxState);

    ret = SeTransRollback(UtStorageTrxMgr::g_seRunCtx, false);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);
    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, ReadViewIsVisibleIn)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(localseRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransAssignReadView(localseRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    TrxT *trxLocal = (TrxT *)(localseRunCtx)->trx;

    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trxLocal->base.trxId);
    EXPECT_EQ(ret, NO_DATA_INTER);

    ret = ReadViewIsTrxVisible(&trxLocal->trx.base.readView, trxLocal->base.trxId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = ReadViewIsTrxVisible(&trxLocal->trx.base.readView, trx->base.trxId);
    EXPECT_EQ(ret, NO_DATA_INTER);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransCommit(localseRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    ret = SeClose(localseRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, ReadViewIsVisibleOut)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(localseRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransAssignReadView(localseRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    TrxT *trxLocal = (TrxT *)(localseRunCtx)->trx;

    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId - 1);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId + 1);
    EXPECT_EQ(ret, NO_DATA_INTER);

    ret = ReadViewIsTrxVisible(&trxLocal->trx.base.readView, trxLocal->base.trxId);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = ReadViewIsTrxVisible(&trxLocal->trx.base.readView, trxLocal->base.trxId - 1);
    EXPECT_EQ(ret, NO_DATA_INTER);
    ret = ReadViewIsTrxVisible(&trx->trx.base.readView, trx->base.trxId + 1);
    EXPECT_EQ(ret, NO_DATA_INTER);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransCommit(localseRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    ret = SeClose(localseRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, TrxResourceList)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    uint32_t resId = 1;
    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    bool isNewCtx = false;
    SeTrxContainerCtxT *trxCntrCtx = NULL;

    ret = TrxStoreContainerCtx(trx, resId, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);

    uint32_t resId2 = 2;
    ret = TrxStoreContainerCtx(trx, resId2, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);

    uint32_t resId3 = 3;
    ret = TrxStoreContainerCtx(trx, resId3, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);

    EXPECT_EQ(3, trx->cntrCtxList.itemCnt);

    uint32_t resId4 = 4;
    ret = TrxStoreContainerCtx(trx, resId4, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);

    uint32_t resId5 = 5;
    ret = TrxStoreContainerCtx(trx, resId5, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(4, trx->cntrCtxList.itemCnt);
    TrxSetContainerIsUsed(trxCntrCtx);

    SeTrxContainerCtxT *item = TrxGetContainerCtxById(trx, resId);
    EXPECT_NE((SeTrxContainerCtxT *)NULL, item);

    item = TrxGetContainerCtxById(trx, resId2);
    EXPECT_NE((SeTrxContainerCtxT *)NULL, item);

    item = TrxGetContainerCtxById(trx, resId3);
    EXPECT_NE((SeTrxContainerCtxT *)NULL, item);

    item = TrxGetContainerCtxById(trx, resId4);
    EXPECT_NE((SeTrxContainerCtxT *)NULL, item);

    item = TrxGetContainerCtxById(trx, resId5);
    EXPECT_NE((SeTrxContainerCtxT *)NULL, item);

    item = TrxGetContainerCtxById(trx, resId5 + 1);
    EXPECT_EQ((SeTrxContainerCtxT *)NULL, item);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, TrxMgrIsRwTrxActive)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    TrxMgrT *trxMgr = (TrxMgrT *)(UtStorageTrxMgr::g_seRunCtx)->trxMgr;
    TrxIdListT *rwTrxIds = (TrxIdListT *)((uint8_t *)trxMgr + trxMgr->rwTrxIdsOffset);
    EXPECT_EQ(0, rwTrxIds->listLen);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(localseRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    TrxT *trxLocal = (TrxT *)(localseRunCtx)->trx;

    bool isActive = TrxMgrIsRwTrxActive(trxMgr, trx->base.trxId);
    EXPECT_EQ(true, isActive);
    isActive = TrxMgrIsRwTrxActive(trxMgr, trxLocal->base.trxId);
    EXPECT_EQ(true, isActive);

    isActive = TrxMgrIsRwTrxActive(trxMgr, trx->base.trxId - 1);
    EXPECT_EQ(false, isActive);
    isActive = TrxMgrIsRwTrxActive(trxMgr, trxLocal->base.trxId + 1);
    EXPECT_EQ(false, isActive);
    EXPECT_EQ(2, rwTrxIds->listLen);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransCommit(localseRunCtx);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(0, rwTrxIds->listLen);

    ret = SeClose(localseRunCtx);
    ASSERT_EQ(0, ret);
    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, labelReadView_reuse)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    for (uint32_t i = 0; i < 2; ++i) {  // 测试事务提交后能够再次复用
        TrxCfgT trxCfg = GetOptimisticTrxCfg();
        ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
        ASSERT_EQ(ret, 0);

        ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
        ASSERT_EQ(ret, 0);

        SeTransSetLabelModifiedActive(UtStorageTrxMgr::g_seRunCtx);
        TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
        for (uint32_t labelId = 0; labelId < 10; labelId++) {
            ret = OptiTrxSetLabelReadview(trx, labelId, TRX_VERTEXLABEL_HEAP, true);
            ASSERT_EQ(ret, 0);
        }

        for (uint32_t labelId = 10; labelId < 20; labelId++) {
            ret = OptiTrxSetLabelReadview(trx, labelId, TRX_KVLABEL_HEAP, true);
            ASSERT_EQ(ret, 0);
        }

        for (uint32_t labelId = 20; labelId < 30; labelId++) {
            ret = OptiTrxSetLabelReadview(trx, labelId, TRX_EDGELABEL_HEAP, true);
            ASSERT_EQ(ret, 0);
        }

        ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
        ASSERT_EQ(ret, 0);
    }

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
}

TEST_F(UtStorageTrxMgr, LabelReadView_validate)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    TrxCfgT trxCfg = GetOptimisticTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(UtStorageTrxMgr::g_seRunCtx);
    // 开启第二个事务
    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransAssignReadView(localseRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(localseRunCtx);
    TrxT *trx2 = (TrxT *)localseRunCtx->trx;
    uint32_t labelId2 = 9;
    ret = OptiTrxSetLabelReadview(trx2, labelId2, TRX_VERTEXLABEL_HEAP, true);

    ret = SeTransCommit(localseRunCtx);
    ASSERT_EQ(ret, 0);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    for (uint32_t labelId = 0; labelId < 10; labelId++) {
        ret = OptiTrxSetLabelReadview(trx, labelId, TRX_VERTEXLABEL_HEAP, true);
        ASSERT_EQ(ret, 0);
    }

    ret = SeOptimisticTrxConflictCheck(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_RESTRICT_VIOLATION);  // 测试乐观事务的冲突检测接口，预期有冲突，报错

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_RESTRICT_VIOLATION);  // 预期有冲突，报错
    ret = SeTransRollback(UtStorageTrxMgr::g_seRunCtx, false);
    ASSERT_EQ(ret, GMERR_OK);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    ret = SeClose(localseRunCtx);
    ASSERT_EQ(ret, 0);
}

TEST_F(UtStorageTrxMgr, LabelReadView_not_validate)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    TrxCfgT trxCfg = GetOptimisticTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    ASSERT_EQ(ret, 0);
    ret = SeTransAssignReadView(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    SeTransSetLabelModifiedActive(UtStorageTrxMgr::g_seRunCtx);
    // 开启第二个事务, 假设未做过dml操作（不调用SeTransSetLabelModifiedActive）
    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    ASSERT_EQ(ret, 0);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    ret = SeTransAssignReadView(localseRunCtx);
    ASSERT_EQ(ret, 0);

    TrxT *trx2 = (TrxT *)localseRunCtx->trx;
    uint32_t labelId2 = 9;
    ret = OptiTrxSetLabelReadview(trx2, labelId2, TRX_VERTEXLABEL_HEAP, true);

    ret = SeTransCommit(localseRunCtx);
    ASSERT_EQ(ret, 0);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    for (uint32_t labelId = 0; labelId < 10; labelId++) {
        ret = OptiTrxSetLabelReadview(trx, labelId, TRX_VERTEXLABEL_HEAP, true);
        ASSERT_EQ(ret, 0);
    }

    ret = SeOptimisticTrxConflictCheck(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);  // 测试乐观事务的冲突检测接口，预期没有有冲突

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);  // 预期没有冲突

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);

    ret = SeClose(localseRunCtx);
    ASSERT_EQ(ret, 0);
}

TEST_F(UtStorageTrxMgr, GetConnIdByTrxIdSuccess)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(ret, GMERR_OK);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(trxState, TRX_STATE_ACTIVE);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;

    TrxBaseInfoT trxBaseInfo = {0};
    ret = SeTransGetTrxBaseInfoByTrxId(UtStorageTrxMgr::g_seRunCtx, trx->base.trxId, &trxBaseInfo);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(trxBaseInfo.connId, 8888);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtStorageTrxMgr, GetConnIdByTrxIdMultiTrxSuccess)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(ret, GMERR_OK);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(trxState, TRX_STATE_ACTIVE);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;

    // 开启第二个事务
    trxCfg.connId = 6666;
    SeRunCtxHdT localseRunCtx = NULL;
    ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &localseRunCtx);
    EXPECT_EQ(ret, GMERR_OK);
    ret = SeTransBegin(localseRunCtx, &trxCfg);
    EXPECT_EQ(ret, GMERR_OK);

    TrxT *trx2 = (TrxT *)localseRunCtx->trx;

    TrxBaseInfoT trxBaseInfo = {0};
    ret = SeTransGetTrxBaseInfoByTrxId(UtStorageTrxMgr::g_seRunCtx, trx2->base.trxId, &trxBaseInfo);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(trxBaseInfo.connId, 6666);
    ret = SeTransGetTrxBaseInfoByTrxId(UtStorageTrxMgr::g_seRunCtx, trx->base.trxId, &trxBaseInfo);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(trxBaseInfo.connId, 8888);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    ret = SeTransCommit(localseRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
    ret = SeClose(localseRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtStorageTrxMgr, GetConnIdByTrxIdErrWhenTrxIdNotExist)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    TrxBaseInfoT trxBaseInfo = {0};
    ret = SeTransGetTrxBaseInfoByTrxId(UtStorageTrxMgr::g_seRunCtx, 1234, &trxBaseInfo);
    EXPECT_EQ(ret, GMERR_NO_ACTIVE_TRANSACTION);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtStorageTrxMgr, GetConnIdByTrxIdErrWhenTrxHasBeenCommitted)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    ASSERT_EQ(ret, GMERR_OK);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(ret, GMERR_OK);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(trxState, TRX_STATE_ACTIVE);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    TrxBaseInfoT trxBaseInfo = {0};
    ret = SeTransGetTrxBaseInfoByTrxId(UtStorageTrxMgr::g_seRunCtx, trx->base.trxId, &trxBaseInfo);
    EXPECT_EQ(ret, GMERR_NO_ACTIVE_TRANSACTION);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);
}

TEST_F(UtStorageTrxMgr, LiteTrxTestTrxMemCtx1)
{
    // 持久化当前不支持轻量化事务
    PERSISTENCE_NOT_SUPPORT;
    // 轻量化事务有一段缓存buf，通过TrxLiteAllocBuf申请，看护此处流程在事务逻辑无泄漏
    Status ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    ShmemPtrT heapShmAddr;
    HeapAccessCfgT heapCfg = {.pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = false,
        .isLabelLockSerializable = true,
        .isUseRsm = false,
#ifdef FEATURE_GQL
        .skipRowLockPessimisticRR = false,
#endif
        .ccType = CONCURRENCY_CONTROL_READ_UNCOMMIT,
        .trxType = PESSIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 0,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = DB_DEFAULT_TABLE_SPACE_ID,
        .tableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .fsmTableSpaceIndex = DB_DEFAULT_TABLE_SPACE_INDEX,
        .maxItemNum = DB_MAX_UINT64};
    ret = HeapLabelCreate(NULL, &heapCfg, &heapShmAddr);
    ASSERT_EQ(GMERR_OK, ret);
    DmVertexLabelT stubVtxLabel = {0};
    MetaVertexLabelT metaVL = {0};
    stubVtxLabel.metaVertexLabel = &metaVL;
    MetaShmAlloc((DbMemCtxT *)trxmgrtopShmMemCtx, (void **)&stubVtxLabel.commonInfo, &stubVtxLabel.commonInfoShmPtr,
        sizeof(VertexLabelCommonInfoT));
    stubVtxLabel.commonInfo->heapInfo.heapShmAddr = heapShmAddr;

    ASSERT_EQ(ret, GMERR_OK);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.isolationLevel = READ_UNCOMMITTED;
    trxCfg.isLiteTrx = true;

    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(ret, GMERR_OK);

    HpRunHdlT heapHdl;
    HeapRunCtxAllocCfgT heapRunCtxAllocCfg = {.containerType = CONTAINER_HEAP,
        .heapShmAddr = heapShmAddr,
        .seRunCtx = UtStorageTrxMgr::g_seRunCtx,
        .dmInfo = &stubVtxLabel,
        .isBackGround = false};
    ret = HeapLabelAllocAndInitRunctx(&heapRunCtxAllocCfg, &heapHdl);
    EXPECT_EQ(0, ret);
    ret = HeapLabelOpen(heapHdl, HEAP_OPTYPE_INSERT, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx);
    EXPECT_EQ(0, ret);
    HeapTupleBufT heapTupleBuf = {0};
    char tupleBuf[50] = "";
    const int num = 10000;
    HpTupleAddr heapTupleShmAddr;
    vector<HpTupleAddr> heapTupleShmAddrList;
    for (uint32_t i = 0; i < num; ++i) {
        ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
        EXPECT_EQ(ret, GMERR_OK);
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i);
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        ret = HeapLabelInsertHpTupleBuffer(heapHdl, &heapTupleBuf, &heapTupleShmAddr);
        heapTupleShmAddrList.push_back(heapTupleShmAddr);
        ASSERT_EQ(ret, GMERR_OK);
        ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
        ASSERT_EQ(ret, GMERR_OK);
    }
    TrxT *trx = (TrxT *)UtStorageTrxMgr::g_seRunCtx->trx;
    uint32_t bufLength = 0;
    AdvancedPtrT cacheBuf = APtrNULL();

    for (uint32_t i = 0; i < num; ++i) {
        ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
        EXPECT_EQ(ret, GMERR_OK);
        sprintf_s(tupleBuf, sizeof(tupleBuf), "[tuple (Thread%u) %u]", pthread_self(), i + num);
        heapTupleBuf.buf = (uint8_t *)tupleBuf;
        heapTupleBuf.bufSize = strlen(tupleBuf) + 1;
        bool isUndoBypass = false;
        HeapUpdOutPara out = {.isUndoBypass = &isUndoBypass};
        ret = HeapLabelUpdateWithHpTupleBuf(heapHdl, &heapTupleBuf, heapTupleShmAddrList[i], &out);
        ASSERT_EQ(ret, GMERR_OK);
        ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
        ASSERT_EQ(ret, GMERR_OK);
        if (bufLength < trx->liteTrx.bufLength) {
            cacheBuf = trx->liteTrx.cacheBuf;
        } else {
            // cacheBuf在事务结构释放时清除，buf大小不变不应该申请新的内存。
            EXPECT_TRUE(APtrIsEqual(cacheBuf, trx->liteTrx.cacheBuf));
        }
    }

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, GMERR_OK);

    HeapCntrAcsInfoT heapCntrAcsInfo = {
        .heapShmAddr = heapShmAddr, .isPersistent = false, .isUseRsm = false, .instanceId = DbGetProcGlobalId()};
    HeapLabelDrop(NULL, &heapCntrAcsInfo, NULL);
}

extern "C" StatusInter TrxStoreHashHandleContainerCtx(
    TrxT *trx, uint32_t containerId, SeTrxContainerCtxT **trxCntrCtx, bool *isNewContainerOpening);

extern "C" SeTrxContainerCtxT *TrxGetHashHandleContainerCtxById(TrxT *trx, uint32_t containerId);

TEST_F(UtStorageTrxMgr, TrxCntrLruCacheTestSingleElementList)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    uint32_t resId = 1;
    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    trx->cntrCtxList.enableLruCache = true;

    bool isNewCtx = false;
    SeTrxContainerCtxT *trxCntrCtx = NULL;

    ret = TrxStoreHashHandleContainerCtx(trx, resId, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 1);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->next, (TrxContainerLruCacheNodeT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);

    SeTrxContainerCtxT *item = TrxGetHashHandleContainerCtxById(trx, resId);
    EXPECT_NE(item, (SeTrxContainerCtxT *)NULL);
    item = TrxGetContainerCtxById(trx, resId + 1);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);

    TrxCntrLruCacheRemoveNode(&trx->cntrCtxList);
    item = TrxGetContainerCtxById(trx, resId);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 0);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, TrxCntrLruCacheTestMultiElementList)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    uint32_t resId = 1;
    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    trx->cntrCtxList.enableLruCache = true;

    bool isNewCtx = false;
    SeTrxContainerCtxT *trxCntrCtx = NULL;

    ret = TrxStoreHashHandleContainerCtx(trx, resId, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);
    uint32_t resId2 = 2;
    ret = TrxStoreHashHandleContainerCtx(trx, resId2, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->next->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);

    // 经过访问，链表排序会发生变化
    SeTrxContainerCtxT *item = TrxGetHashHandleContainerCtxById(trx, resId);
    EXPECT_NE(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->next->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);
    item = TrxGetHashHandleContainerCtxById(trx, 2 + 1);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->next->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);
    item = TrxGetHashHandleContainerCtxById(trx, resId2);
    EXPECT_NE(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->next->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);

    // 优先删除头节点
    TrxCntrLruCacheRemoveNode(&trx->cntrCtxList);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 1);
    item = TrxGetHashHandleContainerCtxById(trx, resId2);
    EXPECT_NE(item, (SeTrxContainerCtxT *)NULL);
    item = TrxGetHashHandleContainerCtxById(trx, resId);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);
    TrxCntrLruCacheRemoveNode(&trx->cntrCtxList);
    item = TrxGetHashHandleContainerCtxById(trx, resId2);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 0);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

TEST_F(UtStorageTrxMgr, TrxCntrLruCacheTestFullList)
{
    int32_t ret = SeOpenWithNewSession(1, (DbMemCtxT *)UtStorageTrxMgr::topDynamicMemCtx, &UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(ret, 0);
    TrxCfgT trxCfg = GetDefaultTrxCfg();
    trxCfg.readOnly = true;
    ret = SeTransBegin(UtStorageTrxMgr::g_seRunCtx, &trxCfg);
    EXPECT_EQ(GMERR_OK, ret);
    TrxStateE trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_ACTIVE, trxState);

    TrxT *trx = (TrxT *)(UtStorageTrxMgr::g_seRunCtx)->trx;
    trx->cntrCtxList.enableLruCache = true;
    bool isNewCtx = false;
    SeTrxContainerCtxT *trxCntrCtx = NULL;

    // 验证正确限制链表最大长度
    uint32_t resId = 1;
    for (uint32_t i = resId; i <= SE_TRX_CNTR_LRU_CACHE_SIZE; i++) {
        ret = TrxStoreHashHandleContainerCtx(trx, i, &trxCntrCtx, &isNewCtx);
        EXPECT_EQ(GMERR_OK, ret);
        TrxSetContainerIsUsed(trxCntrCtx);
        EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId);
        EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, i);
        EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, i);
        EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);
    }
    uint32_t resId2 = SE_TRX_CNTR_LRU_CACHE_SIZE + 1;
    ret = TrxStoreHashHandleContainerCtx(trx, resId2, &trxCntrCtx, &isNewCtx);
    EXPECT_EQ(GMERR_OK, ret);
    TrxSetContainerIsUsed(trxCntrCtx);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId + 1);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, SE_TRX_CNTR_LRU_CACHE_SIZE);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);

    // 经过访问，链表排序会发生变化
    for (uint32_t i = resId + 1; i <= resId2; i++) {
        SeTrxContainerCtxT *item = TrxGetHashHandleContainerCtxById(trx, i);
        EXPECT_NE(item, (SeTrxContainerCtxT *)NULL);
        EXPECT_EQ(item->ctxHead.id, i);
        uint32_t expectHeadId = (i != resId2) ? (i + 1) : (resId + 1);
        EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, expectHeadId);
        EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, i);
        EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, SE_TRX_CNTR_LRU_CACHE_SIZE);
        EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);
    }
    SeTrxContainerCtxT *item = TrxGetHashHandleContainerCtxById(trx, SE_TRX_CNTR_LRU_CACHE_SIZE + 2);
    EXPECT_EQ(item, (SeTrxContainerCtxT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead->container->ctxHead.id, resId + 1);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->container->ctxHead.id, resId2);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, SE_TRX_CNTR_LRU_CACHE_SIZE);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail->next, (TrxContainerLruCacheNodeT *)NULL);

    // 验证链表清空
    TrxReleaseContainerLruCache(trx);
    EXPECT_EQ(trx->cntrCtxList.lruCacheHead, (TrxContainerLruCacheNodeT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheTail, (TrxContainerLruCacheNodeT *)NULL);
    EXPECT_EQ(trx->cntrCtxList.lruCacheItemCnt, 0);

    ret = SeTransCommit(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(GMERR_OK, ret);
    trxState = SeTransGetState(UtStorageTrxMgr::g_seRunCtx);
    EXPECT_EQ(TRX_STATE_NOT_STARTED, trxState);

    ret = SeClose(UtStorageTrxMgr::g_seRunCtx);
    ASSERT_EQ(0, ret);
}

static int32_t TrxIdCmpWrong(const void *left, const void *right)
{
    return (int32_t)((*(const TrxIdT *)left) - (*(const TrxIdT *)right));
}

TEST_F(UtStorageTrxMgr, BsearchWithTrxIdCmp)
{
    TrxIdT a1 = 45791565;
    TrxIdT a2 = 4340758861;
    TrxIdT a3 = 4340758861;

    int32_t res = TrxIdCmp(&a1, &a2);
    EXPECT_EQ(res, -1);
    res = TrxIdCmp(&a2, &a1);
    EXPECT_EQ(res, 1);
    res = TrxIdCmp(&a2, &a3);
    EXPECT_EQ(res, 0);
    // 错误的cmp实现方式，返回会被截断
    res = TrxIdCmpWrong(&a1, &a2);
    EXPECT_EQ(res, 0);
}

void *WriteCommitState(void *args)
{
    DbSetServerThreadFlag();
    Trx *trx = (Trx *)args;
    for (uint32_t i = 0; i < 10000; i++) {
        trx->base.state = TRX_STATE_NOT_STARTED;
        DbUsleep(1);
    }
    return 0;
}

void *WriteNotStartState(void *args)
{
    DbSetServerThreadFlag();
    Trx *trx = (Trx *)args;
    for (uint32_t i = 0; i < 10000; i++) {
        trx->base.state = TRX_STATE_COMMITTED;
        DbUsleep(1);
    }
    return 0;
}

void *ReadAndAssert(void *args)
{
    DbSetServerThreadFlag();
    Trx *trx = (Trx *)args;
    for (uint32_t i = 0; i < 10000; i++) {
        TrxStateE state = trx->base.state;
        // DTS2023122701604 此处需要使用局部变量获取state，不能直接断言一个可能被修改状态
        // DB_ASSERT不是原子操作，每次判断时条件都可能被修改，最后判断COMMITTED状态的时候正好被修改为了NOT_STARTED，那么就会断言失败
        DB_ASSERT(state == TRX_STATE_NOT_STARTED || state == TRX_STATE_ROLLBACK || state == TRX_STATE_ACTIVE ||
                  state == TRX_STATE_ABORT || state == TRX_STATE_COMMITTED);
        DbUsleep(1);
    }
    return 0;
}

TEST_F(UtStorageTrxMgr, AssertTrxStateWhileReadAndWrite)
{
    Trx trx;
    trx.base.state = TRX_STATE_NOT_STARTED;

    const int32_t writeCommitStateNum = 20;
    const int32_t writeNotStartStateNum = 20;
    const int32_t readNum = 10;
    pthread_t writeCommitThread[writeCommitStateNum] = {0};
    pthread_t writeNotStartThread[writeNotStartStateNum] = {0};
    pthread_t readThread[readNum] = {0};

    for (int32_t i = 0; i < writeCommitStateNum; i++) {
        pthread_create(&writeCommitThread[i], NULL, WriteCommitState, &trx);
    }
    for (int32_t i = 0; i < writeNotStartStateNum; i++) {
        pthread_create(&writeNotStartThread[i], NULL, WriteNotStartState, &trx);
    }
    for (int32_t i = 0; i < readNum; i++) {
        pthread_create(&readThread[i], NULL, ReadAndAssert, &trx);
    }
    int32_t ret = 0;
    for (int32_t i = 0; i < writeCommitStateNum; i++) {
        ret = pthread_join(writeCommitThread[i], NULL);
        EXPECT_EQ(0, ret);
    }
    for (int32_t i = 0; i < writeNotStartStateNum; i++) {
        ret = pthread_join(writeNotStartThread[i], NULL);
        EXPECT_EQ(0, ret);
    }
    for (int32_t i = 0; i < readNum; i++) {
        ret = pthread_join(readThread[i], NULL);
        EXPECT_EQ(0, ret);
    }
}
