/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: storage_lpm_index_ut.cc
 * Description: Implementation of lpm index ut
 * Author: liangtingting
 * Create: 2021/4/12
 */

#include "adpt_types.h"
#include "adpt_spinlock.h"
#include "se_define.h"
#include "se_instance.h"
#include "db_table_space.h"
#include "se_memdata.h"
#include "gmc_errno.h"
#include "stub.h"
#include "db_shm_array.h"
#include "db_mem_context.h"
#include "adpt_mem_segment_euler.h"
#include "db_dynmem_algo.h"
#include "db_config.h"
#include "se_art.h"
#include "se_heap_utils.h"
#include "se_lpm_index.h"
#include "se_index.h"
#include "se_index_inner.h"
#include "db_common_init.h"
#include "common_init.h"
#include "storage_session.h"
#include "storage_ut_common.h"
#include "se_page_mgr.h"

#ifdef __cplusplus
extern "C" {
#endif

static const uint32_t VR_ID_MAX = 16;
static const uint32_t VRF_ID_MAX = 128;
static const uint32_t VR_ID_MAX_2 = 4096;
static const uint32_t VRF_ID_MAX_2 = 16384;
static const uint32_t MULTI_NUM_3 = 10;
static const uint32_t VRID_VRFID_LEN = 8;
static const uint32_t BATCH_IP_NUM = 110;
static const uint32_t VR_ID_MAX_BATCH = 11;
static const uint32_t VRF_ID_MAX_BATCH = 11;
static const uint32_t BATCH_NUM = VR_ID_MAX_BATCH * VRF_ID_MAX_BATCH * BATCH_IP_NUM;
static const uint32_t LPM_IPV4_INSERT_NUM = 100000;
static const uint32_t LPM_IPV6_INSERT_NUM = 1000;
static const uint32_t IPV6_ADDR_LEN = 16;

static const uint32_t IP6FORWARD_MAX_MASK_LEN[] = {5000, 20000, 45000, 95000, 150000, 400000, 1000000, DB_MAX_UINT32};
static const uint32_t IP6FORWARD_MASK_LEN[] = {28, 64, 40, 128, 96, 48, 32, 128, DB_MAX_UINT32};
static const uint32_t IP6FORWARD_LEFT_SHIFT[] = {44, 8, 32, 8, 8, 24, 40, 8, DB_MAX_UINT32};

#define TEST_CHECK_MEMDATA_SPACE_ID 1

typedef struct TagUtShmCommT {
    volatile uint32_t writeCnt;
    volatile uint32_t readCnt;
    volatile uint32_t readOkCnt;
} UtShmCommT;

extern StatusInter HeapFetch(HeapRunCtxT *ctx, HpItemPointerT itemPtr, HeapPageReadRowProc func, void *userData);
extern Status LpmInitExtCfg(const SeInstanceT *seInstance, LpmIndexT *newIndex, DmIndexTypeE idxType);
extern void HeapPerfOpTimeStat(
    HeapRunCtxT *ctx, HpPerfOpTypeE type, StatusInter result, uint64_t rowNum, uint64_t cyclesSum);
extern Status LpmInitArtForVrfIdShmPtr(DbMemCtxT *lpmMemCtx, LpmIndexT *lpmIndex, ShmemPtrT *vrfIdShmPtr);

extern IsIncludeParaT g_gmdbIdxIsIncludePara[SE_INDEX_INCLUDED_PAR_NUM];
#ifdef __cplusplus
}
#endif

static DbMemCtxT *g_topShmMemCtxForLpm = nullptr;
static SeRunCtxHdT g_seRunCtxForLpm;

static IndexMetaCfgT g_lpm4MetaCfg = {
    .indexId = 0,
    .idxType = LPM4_INDEX,
    .realIdxType = LPM4_INDEX,
    .idxConstraint = UNIQUE,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

static IndexMetaCfgT g_lpm6MetaCfg = {
    .indexId = 0,
    .idxType = LPM6_INDEX,
    .realIdxType = LPM6_INDEX,
    .idxConstraint = UNIQUE,
    .indexMultiVersionType = INDEX_ONE_VERSION_TYPE,
    .indexCap = 0,
    .isLabelLatchMode = false,
    .tableSpaceId = 0,
    .tableSpaceIndex = 0,
    .nullInfoBytes = 1,
    .isUseClusteredHash = false,
    .hasVarchar = false,
    .isHcGlobalLatch = false,
    .isMemFirst = false,
    .isVertexUseRsm = false,
    .hcLatch = NULL,
    .keyDataType = 0,
    .extendParam = NULL,
};

void CheckMemdataIsRuningProperly(SeInstanceT *seIns, uint32_t deviceId = 0, uint32_t blockId = 1)
{
#ifndef FEATURE_PERSISTENCE
    // alloc和getpage两种方式获取虚拟地址是一样的
    uint8_t *memPageAddr1 = nullptr;
    uint8_t *memPageAddr2 = nullptr;
    PageMgrT *pageMgr = (PageMgrT *)seIns->mdMgr;

    PageIdT pageAddr;
    uint32_t spaceId = TEST_CHECK_MEMDATA_SPACE_ID;
    AllocPageParamT allocPageParam = {.spaceId = spaceId,
        .trmId = 0,
        .labelId = RSM_INVALID_LABEL_ID,
        .dbInstance = NULL,
        .labelRsmUndo = NULL};  // index不使用rsmUndo
    StatusInter ret = SeAllocPage(pageMgr, &allocPageParam, &pageAddr);
    ASSERT_EQ(STATUS_OK_INTER, ret);
    PageIdT pageAddrExpect = {.deviceId = deviceId, .blockId = blockId};
    ASSERT_TRUE(DbIsPageIdEqual(pageAddr, pageAddrExpect));

    ret = SeGetPage(pageMgr, pageAddr, (uint8_t **)&memPageAddr1, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    ret = SeGetPage(pageMgr, pageAddrExpect, (uint8_t **)&memPageAddr2, ENTER_PAGE_NORMAL, false);
    ASSERT_EQ(STATUS_OK_INTER, ret);

    EXPECT_EQ(memPageAddr1, memPageAddr2);
    // 预期pageAddr和pageAddrExpect是一样的，只需要释放一次即可
    FreePageParamT freePageParam = {.spaceId = spaceId, .addr = pageAddr, .dbInstance = NULL, .labelRsmUndo = NULL};
    SeInitCachePagePara(&freePageParam.cachePagePara);
    ret = SeFreePage(pageMgr, &freePageParam);
    ASSERT_EQ(STATUS_OK_INTER, ret);
#endif
}

int32_t LpmCreateAndOpenStorageEngine()
{
    g_topShmMemCtxForLpm = DbGetShmemCtxById(DB_TOP_SHMEMCTX_ID, GET_INSTANCE_ID);
    DB_POINTER(g_topShmMemCtxForLpm);

    SeConfigT lpmConfig = {0};
    lpmConfig.deviceSize = 4 * DB_KIBI;
    lpmConfig.pageSize = SE_DEFAULT_PAGE_SIZE;
    lpmConfig.instanceId = 1;
    lpmConfig.maxSeMem = 1024 * DB_KIBI;
    lpmConfig.maxTrxNum = MAX_TRX_NUM;
    lpmConfig.heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_32;

    SeInstanceT *lpmPtr = nullptr;
    Status ret = SeCreateInstance(NULL, g_topShmMemCtxForLpm, &lpmConfig, (SeInstanceHdT *)&lpmPtr);
    EXPECT_EQ(GMERR_OK, ret);
    SeInstanceT *cmpPtr = (SeInstanceT *)SeGetInstance(GET_INSTANCE_ID);
    EXPECT_EQ(lpmPtr, cmpPtr);

    CheckMemdataIsRuningProperly(lpmPtr);

    DbMemCtxArgsT lpmArgs = {0};
    lpmArgs.ctxSize = sizeof(DbDynamicMemCtxT);
    lpmArgs.memType = DB_DYNAMIC_MEMORY;
    lpmArgs.init = DynamicAlgoInit;

    void *seTopDynamicCtxTop =
        (void *)DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), false, "se dynamic", &lpmArgs);
    return SeOpenWithNewSession(1, (DbMemCtxT *)seTopDynamicCtxTop, &g_seRunCtxForLpm);
}

void HeapPerfOpTimeStatStub(
    HeapRunCtxT *ctx, HpPerfOpTypeE type, StatusInter result, uint64_t rowNum, uint64_t cyclesSum)
{
    return;
}

StatusInter HeapFetchStub(HeapRunCtxT *ctx, HpItemPointerT itemPtr, HeapPageReadRowProc func, void *userData)
{
    DB_UNUSED(ctx);
    DB_UNUSED(itemPtr);
    DB_UNUSED(func);
    DB_UNUSED(userData);
    return STATUS_OK_INTER;
}

Status LpmInitExtCfgStub(const SeInstanceT *seInstance, LpmIndexT *newIndex, DmIndexTypeE idxType)
{
    newIndex->ipVersion = (idxType == LPM4_INDEX) ? IP_VERSION_IPV4 : IP_VERSION_IPV6;
    newIndex->vrIdMax = VR_ID_MAX;
    newIndex->vrfIdMax = VRF_ID_MAX;
    return GMERR_OK;
}

StatusInter HeapFetchStubNoExist(HeapRunCtxT *ctx, HpItemPointerT itemPtr, HeapPageReadRowProc func, void *userData)
{
    DB_UNUSED(ctx);
    DB_UNUSED(itemPtr);
    DB_UNUSED(func);
    DB_UNUSED(userData);
    return NO_DATA_HEAP_ITEM_NOT_EXIST;
}

void LpmBatchFreeKeyAndAddr(IndexKeyT *keys, HpBatchOutT *addrs, uint32_t batchNum)
{
    for (uint32_t i = 0; i < batchNum; ++i) {
        IndexKeyT *key = keys + i;
        free(key->keyData);
    }
    free(keys);
    free(addrs);
}

Status LpmCheckAddrStub(const Handle heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    return HeapFetchAndCheckExist(heapRunHdl, addr, isExist);
}

Status LpmCheckAddrAndFetchStub(IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    DB_POINTER2(idxCtx, isExist);
    *isExist = true;
    return HeapFetchAndCheckExist(heapRunHdl, addr, isExist);
}

Status LpmCheckAddrAndFetchStubNotExist(IndexCtxT *idxCtx, const HpRunHdlT heapRunHdl, HpTupleAddr addr, bool *isExist)
{
    DB_POINTER2(idxCtx, isExist);
    *isExist = false;
    return GMERR_OK;
}

void LpmCtxInitCallbackFuncation(IndexOpenCfgT *lpmCtx)
{
    lpmCtx->callbackFunc.addrCheck = LpmCheckAddrStub;
    lpmCtx->callbackFunc.addrCheckAndFetch = LpmCheckAddrAndFetchStub;
}

class UtLpmIndex : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
    static void SetUpTestCase()
    {
        int32_t ret = CommonInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = LpmCreateAndOpenStorageEngine();
        EXPECT_EQ(GMERR_OK, ret);
        setStubC((void *)HeapFetch, (void *)HeapFetchStub);
        setStubC((void *)LpmInitExtCfg, (void *)LpmInitExtCfgStub);
        setStubC((void *)HeapPerfOpTimeStat, (void *)HeapPerfOpTimeStatStub);
    };

    static void TearDownTestCase()
    {
        clearAllStub();
        SeReleasePageMgr((SeInstanceT *)SeGetInstance(GET_INSTANCE_ID));
        (void)SeLockResourceDestroy(GET_INSTANCE_ID);
        DbDestroyTopShmemCtx(GET_INSTANCE_ID);
        CommonRelease();
    };
};

TEST_F(UtLpmIndex, storage_lpm4_index_create_and_drop_001)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    Status ret = LpmIndexCreate(g_seRunCtxForLpm, lpm4Cfg, &lpm4ShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    LpmIndexDrop(g_seRunCtxForLpm, lpm4ShmAddr);
}

static Status UtLpmIndexOpen(IndexOpenCfgT cfg, ShmemPtrT idxShmAddr, IndexCtxT *idxCtx)
{
    idxCtx->idxShmAddr = idxShmAddr;
    idxCtx->idxOpenCfg = cfg;
    SeRunCtxT *lpmRunCtxPtr = cfg.seRunCtx;
    idxCtx->idxHandle = (IdxBaseT *)DbShmPtrToAddr(idxShmAddr);
    if (SECUREC_UNLIKELY(lpmRunCtxPtr == nullptr || idxCtx->idxHandle == nullptr)) {
        return GMERR_DATA_EXCEPTION;
    }
    DbSessionCtxT *ctx = &lpmRunCtxPtr->resSessionCtx;

    idxCtx->idxMetaCfg = idxCtx->idxHandle->indexCfg;
    if (ctx->isDirectRead && !IdxIsConstructed(idxCtx->idxHandle)) {
        return GMERR_OK;
    }
    idxCtx->constructedWhenOpen = true;
    idxCtx->idxOpenCfg.callbackFunc.keyCmp = ArtKeyCmpStub;
    return LpmIndexOpen(idxCtx);
}

static void UtLpmIndexInit(IndexMetaCfgT lpmCfg, ShmemPtrT *lpmShmAddr, IndexCtxT **lpmHandle)
{
    DB_POINTER2(lpmShmAddr, lpmHandle);
    Status ret = LpmIndexCreate(g_seRunCtxForLpm, lpmCfg, lpmShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT lpmCtx = {0};
    lpmCtx.seRunCtx = g_seRunCtxForLpm;
    LpmCtxInitCallbackFuncation(&lpmCtx);
    ret = IdxAlloc(g_seRunCtxForLpm, lpmCfg.idxType, lpmHandle);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtLpmIndexOpen(lpmCtx, *lpmShmAddr, *lpmHandle);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpmIndexUnInit(ShmemPtrT lpmShmAddr, IndexCtxT *lpmHandle)
{
    DB_POINTER(lpmHandle);
    LpmIndexClose(lpmHandle);
    IdxRelease(lpmHandle);
    LpmIndexDrop(g_seRunCtxForLpm, lpmShmAddr);
}

TEST_F(UtLpmIndex, storage_lpm4_index_open_and_close_002)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);
    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

Status LpmInitExtCfgStub2(const SeInstanceT *seInstance, LpmIndexT *newIndex, DmIndexTypeE idxType)
{
    newIndex->ipVersion = (idxType == LPM4_INDEX) ? IP_VERSION_IPV4 : IP_VERSION_IPV6;
    newIndex->vrIdMax = VR_ID_MAX_2;
    newIndex->vrfIdMax = VRF_ID_MAX_2;
    return GMERR_OK;
}

static inline IndexKeyT GenerateIPV46IndexKey(void *keyBuf, uint8_t *keyBufTmp, uint8_t totalLength, uint8_t keyLength)
{
    DB_POINTER2(keyBuf, keyBufTmp);
    errno_t ret =
        memcpy_s(keyBufTmp + LPM_KEY_BITSLEN, totalLength - LPM_KEY_BITSLEN, keyBuf, totalLength - LPM_KEY_BITSLEN);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT indexKey = {.keyData = keyBufTmp, .keyLen = keyLength};
    return indexKey;
}

static void UtLpm4IndexInsert(
    IndexCtxT *lpmHandle, uint32_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    Status ret = LpmIndexInsert(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(result, ret);
}

static void UtLpm6IndexInsert(
    IndexCtxT *lpmHandle, uint8_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV6_KEY_LEN, keyLength);
    Status ret = LpmIndexInsert(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(result, ret);
}

static void UtLpm4IndexUndoInsert(IndexCtxT *lpmHandle, uint32_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    Status ret = LpmIndexUndoInsert(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpm6IndexUndoInsert(IndexCtxT *lpmHandle, uint8_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV6_KEY_LEN, keyLength);
    Status ret = LpmIndexUndoInsert(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpm4IndexLookup(
    IndexCtxT *lpmHandle, uint32_t *keyBuf, HpTupleAddr addrWrite, bool canBeFound = true, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, IPV4_KEY_LEN);
    bool isFound = false;
    HpTupleAddr addrRead = 0;
    Status ret = LpmIndexLookup(lpmHandle, indexKey, &addrRead, &isFound);
    EXPECT_EQ(result, ret);
    EXPECT_EQ(canBeFound, isFound);
    if (canBeFound) {
        EXPECT_EQ(addrWrite, addrRead);
    }
}

static void UtLpm4IndexBeginScan(
    IndexCtxT *lpmHandle, uint32_t *keyBuf, IndexScanItrT *iter, uint8_t keyLength = IPV4_KEY_LEN)
{
    DB_POINTER3(lpmHandle, keyBuf, iter);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    IndexScanCfgT scanCfg;
    scanCfg.leftKey = &indexKey;
    scanCfg.rightKey = NULL;
    Status ret = LpmIndexBeginScan(lpmHandle, scanCfg, iter);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpm6IndexBeginScan(IndexCtxT *lpmHandle, uint8_t *keyBufLeft, uint8_t *keyBufRight, IndexScanItrT *iter,
    uint8_t keyLength = IPV6_KEY_LEN, IndexRangeTypeE rangeType = INDEX_RANGE_BUTT)
{
    DB_POINTER3(lpmHandle, keyBufLeft, iter);
    uint8_t leftKeyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexLeftKey = GenerateIPV46IndexKey(keyBufLeft, leftKeyBufTmp, IPV6_KEY_LEN, keyLength);
    uint8_t rightKeyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexRightKey = {0};
    IndexScanCfgT scanCfg;
    scanCfg.scanDirect = INDEX_SCAN_ASCEND;
    scanCfg.leftKey = &indexLeftKey;
    scanCfg.rightKey = NULL;
    if (keyBufRight != NULL) {
        indexRightKey = GenerateIPV46IndexKey(keyBufRight, rightKeyBufTmp, IPV6_KEY_LEN, keyLength);
        scanCfg.rightKey = &indexRightKey;
    }
    scanCfg.scanType = rangeType;
    Status ret = LpmIndexBeginScan(lpmHandle, scanCfg, iter);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpm6IndexBeginScanFailed(IndexCtxT *lpmHandle, uint8_t keyLength)
{
    DB_POINTER(lpmHandle);
    IndexScanItrT iter;
    uint8_t leftKeyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexLeftKey = {.keyData = leftKeyBufTmp, .keyLen = keyLength};
    IndexKeyT indexRightKey = {.keyData = leftKeyBufTmp, .keyLen = keyLength};
    IndexScanCfgT scanCfg;
    scanCfg.leftKey = &indexLeftKey;
    scanCfg.rightKey = &indexRightKey;
    Status ret = LpmIndexBeginScan(lpmHandle, scanCfg, &iter);
    EXPECT_EQ(GMERR_SYNTAX_ERROR, ret);
    IdxEndScan(lpmHandle, iter);
}

static void UtLpmIndexScan(IndexCtxT *lpmHandle, IndexScanItrT iter, HpTupleAddr addrWrite = DB_INVALID_UINT64,
    bool canBeFound = true, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, iter);
    HpTupleAddr addrRead = 0;
    bool isFound = false;
    Status ret = LpmIndexScan(lpmHandle, iter, &addrRead, &isFound);
    EXPECT_EQ(result, ret);
    EXPECT_EQ(canBeFound, isFound);
    if (canBeFound && addrWrite != DB_INVALID_UINT64) {
        EXPECT_EQ(addrWrite, addrRead);
    }
}

static void UtLpmIndexRangeScan(IndexCtxT *lpmHandle, IndexScanItrT iter, uint32_t expectedCnt,
    HpTupleAddr addrWrite = DB_INVALID_UINT64, bool canBeFound = true, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, iter);
    HpTupleAddr addrRead = 0;
    bool isFound = true;
    uint32_t foundCnt = 0;
    uint32_t addr = addrWrite;
    while (isFound) {
        Status ret = LpmIndexScan(lpmHandle, iter, &addrRead, &isFound);
        if (!isFound) {
            break;
        }
        EXPECT_EQ(result, ret);
        EXPECT_EQ(canBeFound, isFound);
        if (canBeFound && addrWrite != DB_INVALID_UINT64) {
            EXPECT_EQ(addr++, addrRead);
            if (isFound) {
                foundCnt++;
            }
        }
    }
    EXPECT_EQ(foundCnt, expectedCnt);
}

static void UtLpm4IndexScan(IndexCtxT *lpmHandle, uint32_t *keyBuf, HpTupleAddr addrWrite = DB_INVALID_UINT64,
    bool canBeFound = true, uint8_t keyLength = IPV4_KEY_LEN, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    IndexScanItrT iter;
    UtLpm4IndexBeginScan(lpmHandle, keyBuf, &iter, keyLength);
    UtLpmIndexScan(lpmHandle, iter, addrWrite, canBeFound, result);
    IdxEndScan(lpmHandle, iter);
}

static void UtLpm6IndexScan(IndexCtxT *lpmHandle, uint8_t *keyBufLeft, uint8_t *keyBufRight,
    HpTupleAddr addrWrite = DB_INVALID_UINT64, bool canBeFound = true, uint8_t keyLength = IPV6_KEY_LEN,
    Status result = GMERR_OK, IndexRangeTypeE rangeType = INDEX_RANGE_BUTT, uint32_t expectedCnt = 0)
{
    DB_POINTER2(lpmHandle, keyBufLeft);
    IndexScanItrT iter;
    UtLpm6IndexBeginScan(lpmHandle, keyBufLeft, keyBufRight, &iter, keyLength, rangeType);
    if (keyBufRight == NULL) {
        UtLpmIndexScan(lpmHandle, iter, addrWrite, canBeFound, result);
    } else {
        UtLpmIndexRangeScan(lpmHandle, iter, expectedCnt, addrWrite, canBeFound, result);
    }
    IdxEndScan(lpmHandle, iter);
}

static void LpmCheckIsExisted(IndexCtxT *lpmHandle, IndexKeyT *keys, uint32_t startBatch, uint32_t endBatch,
    HpTupleAddr addrWrite = DB_INVALID_UINT64, bool canBeFound = true)
{
    DB_POINTER2(lpmHandle, keys);
    IndexScanCfgT scanCfg;
    scanCfg.rightKey = NULL;
    IndexScanItrT iter;
    for (uint32_t i = startBatch; i < endBatch; ++i) {
        scanCfg.leftKey = keys + i;
        Status ret = LpmIndexBeginScan(lpmHandle, scanCfg, &iter);
        EXPECT_EQ(GMERR_OK, ret);
        UtLpmIndexScan(lpmHandle, iter, addrWrite, canBeFound);
        IdxEndScan(lpmHandle, iter);
    }
}

static void UtLpm4IndexGetKeyCount(IndexCtxT *lpmHandle, uint32_t *keyBuf, uint8_t keyLength, uint64_t expected)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    uint64_t count;
    Status ret = LpmIndexGetKeyCount(lpmHandle, indexKey, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, count);
}

static void UtLpm6IndexGetKeyCount(IndexCtxT *lpmHandle, uint8_t *keyBuf, uint8_t keyLength, uint64_t expected)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV6_KEY_LEN, keyLength);
    uint64_t count;
    Status ret = LpmIndexGetKeyCount(lpmHandle, indexKey, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected, count);
}

static void UtLpm4IndexDelete(IndexCtxT *lpmHandle, uint32_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite,
    IndexRemoveParaT removePara, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    Status ret = LpmIndexDelete(lpmHandle, indexKey, addrWrite, removePara);
    EXPECT_EQ(result, ret);
}

static void UtLpm6IndexDelete(IndexCtxT *lpmHandle, uint8_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite,
    IndexRemoveParaT removePara, Status result = GMERR_OK)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV6_KEY_LEN, keyLength);
    Status ret = LpmIndexDelete(lpmHandle, indexKey, addrWrite, removePara);
    EXPECT_EQ(result, ret);
}

static void UtLpm4IndexUndoRemove(IndexCtxT *lpmHandle, uint32_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV4_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV4_KEY_LEN, keyLength);
    Status ret = LpmIndexUndoRemove(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(GMERR_OK, ret);
}

static void UtLpm6IndexUndoRemove(IndexCtxT *lpmHandle, uint8_t *keyBuf, uint8_t keyLength, HpTupleAddr addrWrite)
{
    DB_POINTER2(lpmHandle, keyBuf);
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    IndexKeyT indexKey = GenerateIPV46IndexKey(keyBuf, keyBufTmp, IPV6_KEY_LEN, keyLength);
    Status ret = LpmIndexUndoRemove(lpmHandle, indexKey, addrWrite);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_max_vr_id_003)
{
    int32_t stubIdx = setStubC((void *)LpmInitExtCfg, (void *)LpmInitExtCfgStub2);
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[4] = {4095, 0, 0, 0};
    for (uint32_t vrf = 0; vrf < VRF_ID_MAX_2; ++vrf) {
        keyBuf[1] = vrf;
        UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, vrf + 1);
    }
    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
    clearStub(stubIdx);
}

static void UtLpmIndexLookupOrScan(uint32_t keyBuf[][4], uint32_t num, bool scanFlag)
{
    DB_POINTER(keyBuf);
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    for (uint32_t i = 0; i < num - 1; ++i) {
        UtLpm4IndexInsert(lpm4Handle, keyBuf[i], IPV4_KEY_LEN, i + 1);
    }

    if (scanFlag) {
        IndexScanItrT iter;
        UtLpm4IndexBeginScan(lpm4Handle, keyBuf[num - 1], &iter);
        UtLpmIndexScan(lpm4Handle, iter, num - 1);
        UtLpmIndexScan(lpm4Handle, iter, 0, false);
        IdxEndScan(lpm4Handle, iter);
    } else {
        UtLpm4IndexLookup(lpm4Handle, keyBuf[num - 1], num - 1);
    }

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_lookup_004)
{
    uint32_t keyBuf[][4] = {{0, 0, 0xa077cef0, 24}, {0, 0, 0xa077cef0, 20}};
    UtLpmIndexLookupOrScan(keyBuf, 2, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_fetch_005)
{
    uint32_t keyBuf[][4] = {{0, 0, 0xfffffff0, 24}, {0, 0, 0xfffffff0, 20}};
    UtLpmIndexLookupOrScan(keyBuf, 2, true);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_multi_and_lookup_one_006)
{
    uint32_t keyBuf[][4] = {
        {0, 0, 0xc0ac0000, 16}, {1, 9, 0xc0ac0000, 24}, {7, 22, 0xc0ac0000, 32}, {7, 22, 0xc0ac0000, 20}};
    UtLpmIndexLookupOrScan(keyBuf, 4, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_multi_and_fetch_one_007)
{
    uint32_t keyBuf[][4] = {
        {0, 0, 0xc0ac0000, 16}, {1, 9, 0xc0ac0000, 24}, {7, 22, 0xc0ac0000, 32}, {7, 22, 0xc0ac0000, 20}};
    UtLpmIndexLookupOrScan(keyBuf, 4, true);
}

#ifndef HPE
TEST_F(UtLpmIndex, storage_lpm4_index_insert_continuous_lookup_and_delete_008)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    uint32_t keyBuf[4] = {0, 0, 0xffffff00, 32};
    HpTupleAddr addrWrite = 0;
    for (uint32_t vr = 0; vr < VR_ID_MAX; vr++) {
        keyBuf[0] = vr;
        for (uint32_t vrf = 0; vrf < VRF_ID_MAX; vrf++) {
            keyBuf[1] = vrf;
            for (uint32_t i = 0x00000001; i <= 0x000000ff; i++) {
                keyBuf[2] = 0xffffff00 + i;
                addrWrite++;
                UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, addrWrite);
                UtLpm4IndexLookup(lpm4Handle, keyBuf, addrWrite);
                UtLpm4IndexDelete(lpm4Handle, keyBuf, IPV4_KEY_LEN, addrWrite, removePara);
            }
        }
    }

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_fetch_next_009)
{
    uint32_t keyBuf[][4] = {{0, 0, 0xfffffff0, 25}, {0, 0, 0xfffffff0, 20}};
    UtLpmIndexLookupOrScan(keyBuf, 2, true);
}

TEST_F(UtLpmIndex, storage_lpm4_index_fetch_for_mask_range_010)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfabcde20, 1};
    uint32_t keyBuf2[4] = {4, 16, 0xccadde27, 0};
    uint32_t insertNum = 32u;
    for (uint32_t i = 1; i <= insertNum; i++) {
        keyBuf1[3] = i;
        keyBuf2[3] = i;
        UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i);
        UtLpm4IndexInsert(lpm4Handle, keyBuf2, IPV4_KEY_LEN, 50u + i);
    }

    uint32_t keyBuf3[4] = {4, 16, 0xccadde00, 20};
    UtLpm4IndexScan(lpm4Handle, keyBuf3, 76);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_fetch_for_continuous_ipv4_and_mask_range_011)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 26};
    uint32_t keyBuf2[4] = {0, 0, 0xfffffff0, 20};
    for (uint32_t j = 1; j < 4; j++) {
        keyBuf1[2] += j;
        keyBuf2[2] += j;
        for (uint32_t i = 1; i <= 32; i++) {
            if (j > 1 && i < 31) {
                continue;
            }
            keyBuf1[3] = i;
            UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i);
        }
        UtLpm4IndexScan(lpm4Handle, keyBuf2, 32);
    }

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_scan_invalid_ipv4_012)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[4] = {0, 0, 0xfffffff0, 24};
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 1);

    uint32_t keyBuf2[5] = {0, 0, 0xfffffff0, 1, 24};
    UtLpm4IndexScan(lpm4Handle, keyBuf2, 0, false, IPV4_KEY_LEN + 4, GMERR_SYNTAX_ERROR);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}
#endif

static void UtLpmIndexInsertInvalidiPv4(uint32_t *keyBuf, uint32_t keyLength = IPV4_KEY_LEN)
{
    DB_POINTER(keyBuf);
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);
    UtLpm4IndexInsert(lpm4Handle, keyBuf, keyLength, 1, GMERR_SYNTAX_ERROR);
    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_invalid_ipv4_013)
{
    uint32_t keyBuf[5] = {0, 0, 0xfffffff0, 1, 24};
    UtLpmIndexInsertInvalidiPv4(keyBuf, IPV4_KEY_LEN + 4);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_invalid_prefix_014)
{
    uint32_t keyBuf[4] = {0, 0, 0xfffffff0, 33};
    UtLpmIndexInsertInvalidiPv4(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_invalid_vrid_015)
{
    uint32_t keyBuf[5] = {16, 0, 0xfffffff0, 24};
    UtLpmIndexInsertInvalidiPv4(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_invalid_vrfid_016)
{
    uint32_t keyBuf[5] = {15, 1024, 0xfffffff0, 24};
    UtLpmIndexInsertInvalidiPv4(keyBuf);
}

static void UtLpmIndexInsertSameIpv4(uint32_t *keyBuf)
{
    DB_POINTER(keyBuf);
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 1);
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 1, GMERR_UNIQUE_VIOLATION);
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 2, GMERR_UNIQUE_VIOLATION);
    UtLpm4IndexUndoInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 2);
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 3, GMERR_UNIQUE_VIOLATION);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

#ifndef HPE
TEST_F(UtLpmIndex, storage_lpm4_index_insert_same_ip_017)
{
    uint32_t keyBuf[4] = {0, 0, 0xffffffff, 20};
    UtLpmIndexInsertSameIpv4(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_same_ip_and_rollback_018)
{
    uint32_t keyBuf[13] = {0, 0, 0xffffffff, 20};
    UtLpmIndexInsertSameIpv4(keyBuf);
}

static void UtLpmIndexTruncate(IndexCtxT *lpmHandle, ShmemPtrT lpmShmAddr)
{
    DB_POINTER(lpmHandle);
    LpmIndexClose(lpmHandle);
    IdxRelease(lpmHandle);
    LpmIndexTruncate(g_seRunCtxForLpm, lpmShmAddr);
}

TEST_F(UtLpmIndex, storage_lpm4_index_truncate_019)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 25};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);

    uint32_t keyBuf2[4] = {0, 0, 0xffffffff, 30};
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    UtLpmIndexTruncate(lpm4Handle, lpm4ShmAddr);
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);
    UtLpm4IndexScan(lpm4Handle, keyBuf2, 0, false);

    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

static void UtLpmIndexRemoveTwice(bool isErase, bool isGc, uint32_t times = 2)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 23};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);

    uint32_t keyBuf2[4] = {0, 0, 0xffffffff, 20};
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    UtLpm4IndexDelete(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite, removePara);
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    if (times == 2) {
        removePara = {.isErase = isErase, .isGc = isGc};
        UtLpm4IndexDelete(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite, removePara);
        if (isErase || isGc) {
            UtLpm4IndexScan(lpm4Handle, keyBuf2, 0, false);
        } else {
            UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);
        }
    }

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_remove_020)
{
    UtLpmIndexRemoveTwice(false, false, 1);
}

TEST_F(UtLpmIndex, storage_lpm4_index_remove_twice_021)
{
    UtLpmIndexRemoveTwice(false, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_remove_is_gc_022)
{
    UtLpmIndexRemoveTwice(false, true);
}

TEST_F(UtLpmIndex, storage_lpm4_index_remove_twice_is_gc_023)
{
    UtLpmIndexRemoveTwice(true, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_remove_twice_is_erase_and_gc_024)
{
    UtLpmIndexRemoveTwice(true, true);
}

static void UtLpm4IndexOpNoexisetedKey(bool deleteFlag, bool scanFlag, bool undoFlag)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xffff0000, 24};
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, 1);

    uint32_t keyBuf2[4] = {2, 0, 1, 20};
    if (deleteFlag) {
        IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
        UtLpm4IndexDelete(lpm4Handle, keyBuf2, IPV4_KEY_LEN, 1, removePara);
    } else if (scanFlag) {
        UtLpm4IndexScan(lpm4Handle, keyBuf2, 0, false);
    } else {
        UtLpm4IndexUndoInsert(lpm4Handle, keyBuf2, IPV4_KEY_LEN, 1);
    }

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_delete_nonexisted_key_025)
{
    UtLpm4IndexOpNoexisetedKey(true, false, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_fetch_nonexisted_key_026)
{
    UtLpm4IndexOpNoexisetedKey(false, true, false);
}

TEST_F(UtLpmIndex, storage_lpm4_index_undoinsert_nonexisted_key_027)
{
    UtLpm4IndexOpNoexisetedKey(false, false, true);
}

TEST_F(UtLpmIndex, storage_lpm4_index_drop_and_recreate_028)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 21};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);

    uint32_t keyBuf2[4] = {0, 0, 0xffffffff, 20};
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);
    UtLpm4IndexScan(lpm4Handle, keyBuf1, addrWrite);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_undo_insert_and_delete_029)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 24};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);

    uint32_t keyBuf2[4] = {0, 0, 0xffffff10, 28};
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    UtLpm4IndexUndoInsert(lpm4Handle, keyBuf2, IPV4_KEY_LEN, addrWrite);
    UtLpm4IndexScan(lpm4Handle, keyBuf2, 0, false);

    UtLpm4IndexUndoRemove(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);
    UtLpm4IndexScan(lpm4Handle, keyBuf2, addrWrite);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_get_count_030)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 7};
    for (uint32_t i = 1; i <= 32; i++) {
        keyBuf1[3] = i;
        UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i);
    }
    uint32_t keyBuf2[4] = {0, 0, 0, 0};
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf2, IPV4_KEY_LEN, 32);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}
#endif

static void UtLpm4IndexMultiInsert(IndexCtxT *lpm4Handle, uint32_t keyBuf[][4], uint32_t num)
{
    DB_POINTER2(lpm4Handle, keyBuf);
    for (uint32_t i = 0; i < num; ++i) {
        UtLpm4IndexInsert(lpm4Handle, keyBuf[i], IPV4_KEY_LEN, i + 1);
    }
}

static void UtLpm4IndexMultiScan(IndexCtxT *lpm4Handle, uint32_t keyBuf[][4], uint32_t num, bool canBeFound = true)
{
    DB_POINTER2(lpm4Handle, keyBuf);
    for (uint32_t i = 0; i < num; ++i) {
        UtLpm4IndexScan(lpm4Handle, keyBuf[i], i + 1, canBeFound);
    }
}

static void UtLpm4IndexMultiDelete(
    IndexCtxT *lpm4Handle, uint32_t keyBuf[][4], uint32_t num, IndexRemoveParaT removePara)
{
    DB_POINTER2(lpm4Handle, keyBuf);
    for (uint32_t i = 0; i < num; ++i) {
        UtLpm4IndexDelete(lpm4Handle, keyBuf[i], IPV4_KEY_LEN, i + 1, removePara);
    }
}

TEST_F(UtLpmIndex, storage_lpm4_index_trx_for_zero_mask_len_031)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf0[][4] = {{0, 0, 0xc0ac0000, 0}, {0, 0, 0xc0ac1111, 24}, {0, 0, 0xc0ac0000, 32}};
    UtLpm4IndexMultiInsert(lpm4Handle, keyBuf0, 3);
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf0[0], IPV4_KEY_LEN, 3);

    uint32_t keyBuf[][4] = {{0, 0, 0xc0ac1000, 0}, {0, 0, 0xc0ac1100, 0}, {0, 0, 0xc0ac0000, 0}};
    UtLpm4IndexMultiScan(lpm4Handle, keyBuf, 3);

    int32_t stubIdx = setStubC((void *)HeapFetch, (void *)HeapFetchStubNoExist);
    UtLpm4IndexMultiScan(lpm4Handle, keyBuf, 3, false);
    clearStub(stubIdx);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf0, 3, removePara);
    removePara = {.isErase = false, .isGc = true};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf0, 3, removePara);

    UtLpm4IndexMultiScan(lpm4Handle, keyBuf, 3, false);
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf0[0], IPV4_KEY_LEN, 0);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_zero_mask_len_and_lookup_032)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[4] = {0, 0, 0x231455ac, 0};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, addrWrite);

    keyBuf[3] = 33;
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, addrWrite, GMERR_SYNTAX_ERROR);

    keyBuf[2] = 0x11111111;
    keyBuf[3] = 32;
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 2);

    keyBuf[2] = 0x22222222;
    UtLpm4IndexLookup(lpm4Handle, keyBuf, addrWrite);

    keyBuf[3] = 33;
    UtLpm4IndexLookup(lpm4Handle, keyBuf, 1, true, GMERR_OK);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_zero_mask_len_twice_033)
{
    uint32_t keyBuf[4] = {0, 0, 0xffffffff, 0};
    UtLpmIndexInsertSameIpv4(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm4_index_batch_insert_and_delete_034)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    IndexKeyT *keys = (IndexKeyT *)malloc(BATCH_NUM * sizeof(IndexKeyT));
    DB_POINTER(keys);
    HpBatchOutT *addrs = (HpBatchOutT *)malloc(BATCH_NUM * sizeof(HpBatchOutT));
    DB_POINTER(addrs);
    uint32_t inc = 0;
    for (uint32_t vr = 0; vr < VR_ID_MAX_BATCH; vr++) {
        for (uint32_t vrf = 0; vrf < VRF_ID_MAX_BATCH; vrf++) {
            for (uint32_t i = 0; i < BATCH_IP_NUM; i++) {
                HpTupleAddr addrConstructed = 0xabcd + inc;
                uint8_t *keyDataPtr = (uint8_t *)malloc(IPV4_KEY_LEN);
                DB_POINTER(keyDataPtr);
                uint32_t keyBuf[4] = {vr, vrf, i, 32};
                keys[inc] = GenerateIPV46IndexKey(keyBuf, keyDataPtr, IPV4_KEY_LEN, IPV4_KEY_LEN);
                addrs[inc].addrOut = addrConstructed;
                inc++;
            }
        }
    }

    Status ret = LpmIndexBatchInsert(lpm4Handle, keys, addrs, BATCH_NUM);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpm4Handle, keys, 0, BATCH_NUM);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = LpmIndexBatchDelete(lpm4Handle, keys, addrs, BATCH_NUM, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    removePara = {.isErase = true, .isGc = false};
    ret = LpmIndexBatchDelete(lpm4Handle, keys, addrs, BATCH_NUM, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpm4Handle, keys, 0, BATCH_NUM, 0, false);

    LpmBatchFreeKeyAndAddr(keys, addrs, BATCH_NUM);
    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_create_and_drop_035)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    Status ret = LpmIndexCreate(g_seRunCtxForLpm, lpm6Cfg, &lpm6ShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    LpmIndexDrop(g_seRunCtxForLpm, lpm6ShmAddr);
}

TEST_F(UtLpmIndex, storage_lpm6_index_open_and_close_036)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);
    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_delete_and_scan_longest_key_037)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 128};
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, 1);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 120};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, true);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    UtLpm6IndexDelete(lpm6Handle, keyBuf1, IPV6_KEY_LEN, 1, removePara);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, true);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

static void UtLpm6IndexOpNoexisetedKey(bool deleteFlag, bool scanFlag, bool undoFlag)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 37};
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, 1);

    uint8_t keyBuf2[25] = {1, 0, 0, 0, 0, 0, 0, 0, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};

    if (deleteFlag) {
        IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
        UtLpm6IndexDelete(lpm6Handle, keyBuf2, IPV6_KEY_LEN, 1, removePara);
    } else if (scanFlag) {
        UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 0, false);
    } else {
        UtLpm6IndexUndoInsert(lpm6Handle, keyBuf2, IPV6_KEY_LEN, 1);
    }

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_delete_nonexisted_key_038)
{
    UtLpm6IndexOpNoexisetedKey(true, false, false);
}

TEST_F(UtLpmIndex, storage_lpm6_index_fetch_nonexisted_key_039)
{
    UtLpm6IndexOpNoexisetedKey(false, true, false);
}

TEST_F(UtLpmIndex, storage_lpm6_index_undoinsert_nonexisted_key_040)
{
    UtLpm6IndexOpNoexisetedKey(false, false, true);
}

TEST_F(UtLpmIndex, storage_lpm6_index_fetch_next_041)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, 1);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    IndexScanItrT iter;
    UtLpm6IndexBeginScan(lpm6Handle, keyBuf2, NULL, &iter);
    UtLpmIndexScan(lpm6Handle, iter, 1);
    UtLpmIndexScan(lpm6Handle, iter, 0, false);
    IdxEndScan(lpm6Handle, iter);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_mask_range_and_fetch_042)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    for (uint32_t i = 1; i <= 128; i++) {
        keyBuf1[24] = i;
        UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i);
    }

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 128);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_lookup_for_continuous_ipv6_and_mask_range_043)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    for (uint32_t j = 1; j < 4; j++) {
        keyBuf1[23] += j;
        keyBuf2[23] += j;
        for (uint32_t i = 1; i <= 128; i++) {
            if (j > 1 && i < 127) {
                continue;
            }
            keyBuf1[24] = i;
            UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i);
        }
        UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 128);
    }

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

static void UtLpmIndexInsertInvalidiPv6(uint8_t *keyBuf, uint32_t keyLength = IPV6_KEY_LEN)
{
    DB_POINTER(keyBuf);
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);
    UtLpm6IndexInsert(lpm6Handle, keyBuf, keyLength, 1, GMERR_SYNTAX_ERROR);
    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_invalid_ipv6_044)
{
    uint8_t keyBuf[26] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 1, 129};
    UtLpmIndexInsertInvalidiPv6(keyBuf, IPV6_KEY_LEN + 1);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_invalid_ipv6_045)
{
    uint8_t keyBuf[26] = {0, 0, 0, 0, 0, 0, 0, 0, 1, 16, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xf0, 1, 112};
    UtLpmIndexInsertInvalidiPv6(keyBuf, IPV6_KEY_LEN + 1);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_invalid_prefix_046)
{
    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 129};
    UtLpmIndexInsertInvalidiPv6(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_invalid_prefix_047)
{
    uint8_t keyBuf[25] = {0xff, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 186};
    UtLpmIndexInsertInvalidiPv6(keyBuf);
}

static void UtLpmIndexInsertSameIpv6(uint8_t *keyBuf)
{
    DB_POINTER(keyBuf);
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    UtLpm6IndexInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, 1);
    UtLpm6IndexInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, 2, GMERR_UNIQUE_VIOLATION);
    UtLpm6IndexUndoInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, 2);
    UtLpm6IndexInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, 3, GMERR_UNIQUE_VIOLATION);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_same_ip_048)
{
    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    UtLpmIndexInsertSameIpv6(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_same_ip_and_rollback_049)
{
    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    UtLpmIndexInsertSameIpv6(keyBuf);
}

TEST_F(UtLpmIndex, storage_lpm6_index_truncate_050)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    HpTupleAddr addrWrite = 1;
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpmIndexTruncate(lpm6Handle, lpm6ShmAddr);
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 0, false);

    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

static void UtLpm6IndexRemoveTwice(bool isErase, bool isGc, uint32_t times = 2)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    HpTupleAddr addrWrite = 2;
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    UtLpm6IndexDelete(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite, removePara);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    if (times == 2) {
        removePara = {.isErase = isErase, .isGc = isGc};
        UtLpm6IndexDelete(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite, removePara);
        if (isErase || isGc) {
            UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 0, false);
        } else {
            UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);
        }
    }

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_remove_051)
{
    UtLpm6IndexRemoveTwice(false, false, 1);
}

TEST_F(UtLpmIndex, storage_lpm6_index_remove_twice_052)
{
    UtLpm6IndexRemoveTwice(false, false);
}

TEST_F(UtLpmIndex, storage_lpm6_index_remove_is_gc_053)
{
    UtLpm6IndexRemoveTwice(false, true);
}

TEST_F(UtLpmIndex, storage_lpm6_index_remove_twice_is_gc_054)
{
    UtLpm6IndexRemoveTwice(true, false);
}

TEST_F(UtLpmIndex, storage_lpm6_index_remove_twice_is_erase_and_gc_055)
{
    UtLpm6IndexRemoveTwice(true, true);
}

TEST_F(UtLpmIndex, storage_lpm6_index_drop_and_recreate_056)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    HpTupleAddr addrWrite = 3;
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_undo_insert_and_delete_057)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    HpTupleAddr addrWrite = 4;
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 100};
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpm6IndexUndoInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, 0, false);

    UtLpm6IndexUndoRemove(lpm6Handle, keyBuf1, IPV6_KEY_LEN, addrWrite);
    UtLpm6IndexScan(lpm6Handle, keyBuf2, NULL, addrWrite);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_get_count_058)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xf0, 112};
    for (uint32_t i = 1; i <= 128; i++) {
        keyBuf1[24] = i;
        UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i);
    }

    uint8_t keyBuf2[8] = {0, 0, 0, 0, 0, 0, 0, 0};
    UtLpm6IndexGetKeyCount(lpm6Handle, keyBuf2, IPV4_KEY_LEN, 128);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

IndexKeyT Lpm6UtConstructKey(
    uint8_t *keydataPtr, uint32_t vrId, uint32_t vrfId, uint32_t ip, uint8_t maskLen, bool isFullKey = false)
{
    uint8_t keyBufTmp[IPV6_KEY_LEN];
    errno_t ret = memcpy_s(keyBufTmp + LPM_KEY_BITSLEN, 4, &vrId, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = memcpy_s(keyBufTmp + LPM_VRFID_POS_IN_KEY, 4, &vrfId, 4);
    EXPECT_EQ(GMERR_OK, ret);
    if (isFullKey) {
        uint32_t singleBufLen = sizeof(uint32_t);
        uint32_t partialNum = IPV6_ADDR_LEN / singleBufLen;
        for (uint32_t i = 0; i < partialNum; i++) {
            ret = memcpy_s(keyBufTmp + LPM_IP_POS_IN_KEY + i * singleBufLen, singleBufLen, &ip, singleBufLen);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else {
        ret = memcpy_s(keyBufTmp + LPM_IP_POS_IN_KEY, IPV6_ADDR_LEN, &ip, IPV6_ADDR_LEN);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = memcpy_s(keyBufTmp + LPM6_MASKLEN_POS_IN_KEY, 1, &maskLen, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = memcpy_s(keydataPtr, IPV6_KEY_LEN, keyBufTmp, IPV6_KEY_LEN);
    EXPECT_EQ(GMERR_OK, ret);
    IndexKeyT key = {.keyData = keydataPtr, .keyLen = IPV6_KEY_LEN};
    return key;
}

void Lpm6UtBatchConstructKeyAndAddr(IndexKeyT **keys, HpBatchOutT **addrs, uint32_t batchNum)
{
    *keys = (IndexKeyT *)malloc(batchNum * sizeof(IndexKeyT));
    DB_POINTER(*keys);
    *addrs = (HpBatchOutT *)malloc(batchNum * sizeof(HpBatchOutT));
    DB_POINTER(*addrs);
    uint32_t inc = 0;
    for (uint32_t vr = 0; vr < VR_ID_MAX_BATCH; vr++) {
        for (uint32_t vrf = 0; vrf < VRF_ID_MAX_BATCH; vrf++) {
            for (uint32_t i = 0; i < BATCH_IP_NUM; i++) {
                HpTupleAddr addrConstructed = 0xabcd + inc;
                uint8_t *keyDataPtr = (uint8_t *)malloc(IPV6_KEY_LEN);
                DB_POINTER(keyDataPtr);
                IndexKeyT *key = *keys + inc;
                *key = Lpm6UtConstructKey(keyDataPtr, vr, vrf, i, 128);
                HpBatchOutT *addr = *addrs + inc;
                addr->addrOut = addrConstructed;
                inc++;
            }
        }
    }
}

TEST_F(UtLpmIndex, storage_lpm6_index_batch_insert_and_delete_059)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpmShmAddr;
    IndexCtxT *lpmHandle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpmShmAddr, &lpmHandle);

    IndexKeyT *keys = nullptr;
    HpBatchOutT *addrs = nullptr;
    Lpm6UtBatchConstructKeyAndAddr(&keys, &addrs, BATCH_NUM);

    Status ret = LpmIndexBatchInsert(lpmHandle, keys, addrs, BATCH_NUM);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpmHandle, keys, 0, BATCH_NUM);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, BATCH_NUM, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    removePara = {.isErase = true, .isGc = false};
    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, BATCH_NUM, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpmHandle, keys, 0, BATCH_NUM, 0, false);

    LpmBatchFreeKeyAndAddr(keys, addrs, BATCH_NUM);
    UtLpmIndexUnInit(lpmShmAddr, lpmHandle);
}

void Lpm6UtBatchConstructKeyAndAddrInOneArt(IndexKeyT **keys, HpBatchOutT **addrs, uint32_t batchIpNum,
    uint32_t startIp = 0, bool isMaskChange = false, bool isFullKey = false)
{
    // 只在单个ART上进行操作
    *keys = (IndexKeyT *)malloc(batchIpNum * sizeof(IndexKeyT));
    DB_POINTER(*keys);
    *addrs = (HpBatchOutT *)malloc(batchIpNum * sizeof(HpBatchOutT));
    DB_POINTER(*addrs);
    uint32_t inc = 0u;
    uint32_t maskLen = 128u;
    for (uint32_t i = startIp; i < startIp + batchIpNum; i++) {
        HpTupleAddr addrConstructed = 0xabcd + inc;
        uint8_t *keyDataPtr = (uint8_t *)malloc(IPV6_KEY_LEN);
        DB_POINTER(keyDataPtr);
        IndexKeyT *key = *keys + inc;
        if (isMaskChange) {
            maskLen = i % 77;  // maskLen 的变化可能会构造出相同的key，需要谨慎修改
        }
        *key = Lpm6UtConstructKey(keyDataPtr, 0, 0, i, maskLen, isFullKey);
        HpBatchOutT *addr = *addrs + inc;
        addr->addrOut = addrConstructed;
        inc++;
    }
}

static void UtLpmIndexBatchDeleteHalf(uint32_t startIp)
{
    // 批量删除前一半，然后重置cursor,继续全量删除，预期前面一半报错，后面一半删除成功
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpmShmAddr;
    IndexCtxT *lpmHandle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpmShmAddr, &lpmHandle);

    IndexKeyT *keys = nullptr;
    HpBatchOutT *addrs = nullptr;
    uint32_t batchNum = 10000;
    Lpm6UtBatchConstructKeyAndAddrInOneArt(&keys, &addrs, batchNum);

    Status ret = LpmIndexBatchInsert(lpmHandle, keys, addrs, batchNum);
    EXPECT_EQ(GMERR_OK, ret);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t halfBatchNum = batchNum / 2;
    removePara = {.isErase = true, .isGc = false};
    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, halfBatchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpmHandle, keys, 0, halfBatchNum, DB_INVALID_UINT64, false);        // 前面一半应该不存在
    LpmCheckIsExisted(lpmHandle, keys, halfBatchNum, batchNum, DB_INVALID_UINT64, true);  // 后面一半应该存在

    // 重新删除，删除前面一半虽然数据已经不在了 但是依旧要返回成功
    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, halfBatchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpmHandle, keys, 0, halfBatchNum, DB_INVALID_UINT64, false);        // 前面一半应该不存在
    LpmCheckIsExisted(lpmHandle, keys, halfBatchNum, batchNum, DB_INVALID_UINT64, true);  // 后面一半应该存在

    ret = LpmIndexBatchDelete(lpmHandle, keys, addrs, batchNum, removePara);
    EXPECT_EQ(GMERR_OK, ret);
    LpmCheckIsExisted(lpmHandle, keys, 0, batchNum, DB_INVALID_UINT64, false);  // 最后全量删除，所有数据都不在

    LpmBatchFreeKeyAndAddr(keys, addrs, batchNum);
    UtLpmIndexUnInit(lpmShmAddr, lpmHandle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_batch_delete_half_and_retry_same_masklen_060)
{
    UtLpmIndexBatchDeleteHalf(0);
}

TEST_F(UtLpmIndex, storage_lpm6_index_batch_delete_half_and_retry_diff_masklen_061)
{
    UtLpmIndexBatchDeleteHalf(12344);
}

static void InitIpv6Value(uint8_t *ipv6Addr, uint32_t length, uint64_t index, uint8_t *maskLen)
{
    uint32_t i = 0;
    while (index + 1 > IP6FORWARD_MAX_MASK_LEN[i]) {
        ++i;
    }
    *maskLen = IP6FORWARD_MASK_LEN[i];
    uint64_t vrIndex = (index + 1) << IP6FORWARD_LEFT_SHIFT[i];
    uint8_t temp[8] = {0};
    for (i = 0; i < 8; i++) {  // together 64 bit, because of uint64_t
        temp[i] = (uint8_t)((vrIndex >> (i * 8)) & 0xFF);
        uint32_t ipv6Idx = ((*maskLen <= 64) ? (7 - i) : i);
        ipv6Addr[ipv6Idx] = temp[i];  // together 128 bit
    }
}

static void GenerateIpv6SpecialKeyBef(uint8_t *keyBuf, uint32_t i)
{
    DB_POINTER(keyBuf);
    uint8_t maskLen = 1;
    uint8_t ipv6Addr[IPV6_ADDR_LEN] = {0};
    InitIpv6Value(ipv6Addr, IPV6_ADDR_LEN, i, &maskLen);
    for (uint8_t j = 0; j < 8; j++) {
        keyBuf[8 + j] = ipv6Addr[j];
    }
    keyBuf[24] = maskLen;
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_multi_ip_addr_062)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27};
    for (uint32_t i = 1; i < 500; i++) {
        GenerateIpv6SpecialKeyBef(keyBuf1, i);
        UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i);
    }

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

StatusInter HeapFetchStub1(HeapRunCtxT *ctx, HpItemPointerT itemPtr, HeapPageReadRowProc func, void *userData)
{
    DB_UNUSED(ctx);
    DB_UNUSED(itemPtr);
    DB_UNUSED(func);
    DB_UNUSED(userData);
    return NO_DATA_HEAP_ITEM_NOT_EXIST;
}

int32_t LpmIndexDeleteStub(IndexCtxT *idxCtx, IndexKeyT idxKey, HpTupleAddr addr, IndexRemoveParaT removePara)
{
    return GMERR_NO_DATA;
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_delete_lookup_063)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28};
    uint32_t insertNum = 20000;
    for (uint32_t i = 1; i < insertNum; i++) {
        GenerateIpv6SpecialKeyBef(keyBuf1, i);
        UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i);
        UtLpm6IndexScan(lpm6Handle, keyBuf1, NULL, i);
    }

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    int32_t stubIdx = setStubC((void *)HeapFetch, (void *)HeapFetchStub1);
    for (uint32_t i = 1; i < insertNum; i++) {
        GenerateIpv6SpecialKeyBef(keyBuf1, i);
        UtLpm6IndexDelete(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i, removePara);
        UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i + insertNum);
    }
    clearStub(stubIdx);

    stubIdx = setStubC((void *)LpmIndexDelete, (void *)LpmIndexDeleteStub);
    for (uint32_t i = 1; i < insertNum; i++) {
        GenerateIpv6SpecialKeyBef(keyBuf1, i);
        UtLpm6IndexDelete(lpm6Handle, keyBuf1, IPV6_KEY_LEN, i, removePara, GMERR_NO_DATA);
    }
    clearStub(stubIdx);
    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_delete_merge_one_node_064)
{
    // 看护DTS: DTS202106190GIONQP0I00
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[][4] = {
        {0, 0, 0xc0a80001, 32}, {0, 0, 0xc0a80002, 32}, {0, 0, 0xc0a80103, 32}, {0, 0, 0xc0a80104, 32}};
    UtLpm4IndexMultiInsert(lpm4Handle, keyBuf, 4);
    UtLpm4IndexMultiScan(lpm4Handle, keyBuf, 4);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf, 4, removePara);
    UtLpm4IndexMultiScan(lpm4Handle, keyBuf, 4, false);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_delete_single_children_065)
{
    /*  Art Lpm 因为支持变长字段，所以在删除节点时，如果碰到只有一个孩子节点时，需要额外判断其前缀槽位是否有效，即
    需要确定该节点是否存有上一层节点的rowid */
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[][4] = {{0, 0, 0x02000000, 8}, {0, 0, 0x01020304, 8}, {0, 0, 0x01020304, 16},
        {0, 0, 0x01020304, 24}, {0, 0, 0x01020304, 32}};
    UtLpm4IndexMultiInsert(lpm4Handle, keyBuf, 5);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf, 5, removePara);
    removePara = {.isErase = false, .isGc = false};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf, 5, removePara);
    removePara = {.isErase = false, .isGc = true};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf, 5, removePara);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_delete_one_node_variant_masklen_066)
{
    /*
    看护DTS： DTS2021062906HOS2P1H00
    变长key，maskLen发生变化但是依旧是同一个key，整棵树仅有一个节点
    1. 根节点是node4类型，有prefixSlot, numchildren=1
    2. 槽位都是 lpm addr 类型，先删除key槽位的值，当全部删除结束后，因为numchildren=0，
        但因为有prefixSlot, 所以应该降级成叶子节点
    key 1,0,0,0/9,10,17,18
    */
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[][4] = {
        {0, 0, 0x01000000, 9}, {0, 0, 0x01000000, 10}, {0, 0, 0x01000000, 17}, {0, 0, 0x01000000, 18}};
    UtLpm4IndexMultiInsert(lpm4Handle, keyBuf, 4);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    UtLpm4IndexMultiDelete(lpm4Handle, keyBuf, 4, removePara);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

static void UtLpmIndexInsertDeleteSameKey(uint32_t keyNum, uint32_t multi)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xc0a8dd10, 32};
    for (uint8_t i = 1; i <= keyNum; i++) {
        keyBuf1[2] = 0xc0a8dd10 + multi * i;
        keyBuf1[3] = i;
        UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i);
    }
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf1, IPV4_KEY_LEN, keyNum);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    for (uint8_t i = 1; i <= keyNum; i++) {
        keyBuf1[2] = 0xc0a8dd10 + multi * i;
        keyBuf1[3] = i;
        UtLpm4IndexDelete(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i, removePara);
    }
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf1, IPV4_KEY_LEN, keyNum);

    removePara = {.isErase = false, .isGc = true};
    for (uint8_t i = 1; i <= keyNum; i++) {
        keyBuf1[2] = 0xc0a8dd10 + multi * i;
        keyBuf1[3] = i;
        UtLpm4IndexDelete(lpm4Handle, keyBuf1, IPV4_KEY_LEN, i, removePara);
    }
    UtLpm4IndexGetKeyCount(lpm4Handle, keyBuf1, IPV4_KEY_LEN, 0);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_get_key_count_067)
{
    // 看护DTS：DTS2021062604H4TZP0F00
    UtLpmIndexInsertDeleteSameKey(MULTI_NUM_3, 1);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_delete_same_key_and_count_068)
{
    // 看护DTS：DTS202106290GPZWTP0H00
    UtLpmIndexInsertDeleteSameKey(32, 0);
}

TEST_F(UtLpmIndex, storage_lpm4_index_get_estimate_mem_size_069)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    uint64_t size = 0;
    Status ret = LpmIndexGetEstimateMemSize(DbGetProcGlobalId(), lpm4Cfg, 10000, 8, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(150000u, size);
}

TEST_F(UtLpmIndex, storage_lpm6_index_get_estimate_mem_size_070)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    uint64_t size = 0;
    Status ret = LpmIndexGetEstimateMemSize(DbGetProcGlobalId(), lpm6Cfg, 10000, 8, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(300000u, size);
}

static ShmemPtrT *GetVrfRoot(IndexCtxT *lpmHandle, uint32_t index = 0)
{
    EXPECT_NE(nullptr, lpmHandle);
    LpmIndexT *lpmIndex = (LpmIndexT *)lpmHandle->idxHandle;
    ShmemPtrT *vrArr = (ShmemPtrT *)DbShmPtrToAddr(lpmIndex->vrArrRoot);
    EXPECT_NE(nullptr, vrArr);
    ShmemPtrT *vrfArr = (ShmemPtrT *)DbShmPtrToAddr(vrArr[index]);
    EXPECT_NE(nullptr, vrfArr);
    return vrfArr;
}

static IndexStatisticsT GetArtIndexStatistics(ShmemPtrT *vrfArr, uint32_t vrfId = 0)
{
    EXPECT_NE(nullptr, vrfArr);
    IndexStatisticsT idxStat = {0};
    EXPECT_EQ(true, DbIsShmPtrValid(vrfArr[vrfId]));
    ArtTreeT *art = (ArtTreeT *)DbShmPtrToAddr(vrfArr[vrfId]);
    EXPECT_NE(nullptr, art);
    Status ret = ArtIndexStatistics(art, &idxStat);
    EXPECT_EQ(GMERR_OK, ret);

    return idxStat;
}

static void UtLpmIndexScaleIn(IndexCtxT *lpmHandle)
{
    DB_POINTER(lpmHandle);
    IndexScaleInCfgT idxScaleCfg = {.splitTime = 100, .startTime = DbRdtsc(), .isOverTime = false};
    uint32_t scaleInTimes = 0;
    do {
        idxScaleCfg.startTime = DbRdtsc();
        idxScaleCfg.isOverTime = false;
        Status ret = LpmIndexScaleIn(lpmHandle, &idxScaleCfg);
        EXPECT_EQ(GMERR_OK, ret);
        scaleInTimes++;
    } while (idxScaleCfg.isOverTime);
}

TEST_F(UtLpmIndex, storage_lpm4_index_scale_in_single_art_071)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf[4] = {0, 0, 0, 32};
    UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, 0);

    ShmemPtrT *vrfArr = GetVrfRoot(lpm4Handle);
    IndexStatisticsT idxStat = GetArtIndexStatistics(vrfArr);

    for (uint32_t i = 0; i < LPM_IPV4_INSERT_NUM; i++) {
        keyBuf[2]++;
        UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, i + 1);
    }
    IndexStatisticsT idxInsertStat = GetArtIndexStatistics(vrfArr);
    EXPECT_GE(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    keyBuf[2] = 0;
    for (uint32_t i = 0; i < LPM_IPV4_INSERT_NUM; i++) {
        keyBuf[2]++;
        UtLpm4IndexDelete(lpm4Handle, keyBuf, IPV4_KEY_LEN, i + 1, removePara);
    }
    idxStat = GetArtIndexStatistics(vrfArr);
    EXPECT_EQ(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    UtLpmIndexScaleIn(lpm4Handle);
    idxStat = GetArtIndexStatistics(vrfArr);
    EXPECT_GE(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_scale_in_single_art_072)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128};
    UtLpm6IndexInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, 0);
    ShmemPtrT *vrfArr = GetVrfRoot(lpm6Handle);
    IndexStatisticsT idxStat = GetArtIndexStatistics(vrfArr);

    for (uint32_t i = 0; i < LPM_IPV6_INSERT_NUM; i++) {
        (*(uint64_t *)&keyBuf[8])++;
        UtLpm6IndexInsert(lpm6Handle, keyBuf, IPV6_KEY_LEN, i + 1);
    }
    IndexStatisticsT idxInsertStat = GetArtIndexStatistics(vrfArr);
    EXPECT_GE(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    *(uint64_t *)&keyBuf[8] = 0;
    for (uint32_t i = 0; i < LPM_IPV6_INSERT_NUM; i++) {
        (*(uint64_t *)&keyBuf[8])++;
        UtLpm6IndexDelete(lpm6Handle, keyBuf, IPV6_KEY_LEN, i + 1, removePara);
    }
    idxStat = GetArtIndexStatistics(vrfArr);
    EXPECT_EQ(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    UtLpmIndexScaleIn(lpm6Handle);
    idxStat = GetArtIndexStatistics(vrfArr);
    EXPECT_GE(idxInsertStat.artIndex.artMemPerfStat.pageCount, idxStat.artIndex.artMemPerfStat.pageCount);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

void LpmUtBatchInsert(IndexCtxT *idxCtx, uint32_t vrfId, uint32_t vrId)
{
    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128};
    *(uint32_t *)&keyBuf[0] = vrId;
    *(uint32_t *)&keyBuf[4] = vrfId;
    for (uint32_t i = 0; i < LPM_IPV6_INSERT_NUM; i++) {
        (*(uint64_t *)&keyBuf[8])++;
        UtLpm6IndexInsert(idxCtx, keyBuf, IPV6_KEY_LEN, i + 1);
    }
}

void LpmUtBatchDelete(IndexCtxT *idxCtx, uint32_t vrfId, uint32_t vrId)
{
    IndexRemoveParaT removePara = {.isErase = true, .isGc = false};
    uint8_t keyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 128};
    *(uint32_t *)&keyBuf[0] = vrId;
    *(uint32_t *)&keyBuf[4] = vrfId;
    for (uint32_t i = 0; i < LPM_IPV6_INSERT_NUM; i++) {
        (*(uint64_t *)&keyBuf[8])++;
        UtLpm6IndexDelete(idxCtx, keyBuf, IPV6_KEY_LEN, i + 1, removePara);
    }
}

TEST_F(UtLpmIndex, storage_lpm6_index_scale_in_multi_art_073)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    LpmIndexT *lpmIndex = (LpmIndexT *)lpm6Handle->idxHandle;
    ShmemPtrT *vrArr = (ShmemPtrT *)DbShmPtrToAddr(lpmIndex->vrArrRoot);
    EXPECT_NE(nullptr, vrArr);

    uint32_t pageNum[VRF_ID_MAX][VR_ID_MAX] = {0};
    for (uint32_t vrId = 0; vrId < VR_ID_MAX; vrId++) {
        for (uint32_t vrfId = 0; vrfId < VRF_ID_MAX; vrfId++) {
            LpmUtBatchInsert(lpm6Handle, vrfId, vrId);
            EXPECT_EQ(true, DbIsShmPtrValid(vrArr[vrId]));
            ShmemPtrT *vrfArr = (ShmemPtrT *)DbShmPtrToAddr(vrArr[vrId]);
            EXPECT_NE(nullptr, vrfArr);
            IndexStatisticsT idxStat = GetArtIndexStatistics(vrfArr, vrfId);
            pageNum[vrId][vrfId] = idxStat.artIndex.artMemPerfStat.pageCount;
            LpmUtBatchDelete(lpm6Handle, vrfId, vrId);
        }
    }

    UtLpmIndexScaleIn(lpm6Handle);
    ArtRunningCtxT *artCtx = &((LpmCtxT *)lpm6Handle->idxRunCtx)->artCtx;
    EXPECT_GE(artCtx->memRunCtx.artMemMgr->scaleInCount, 2u);

    // 该处希望scaleInTimes至少缩容2次，保证LpmIndexScaleIn内部逻辑是连续的，可以适当调小idxScaleCfg.splitTime，让它更容易触发超时重试
    for (uint32_t vrId = 0; vrId < VR_ID_MAX; vrId++) {
        ShmemPtrT *vrfArr = (ShmemPtrT *)DbShmPtrToAddr(vrArr[vrId]);
        for (uint32_t vrfId = 0; vrfId < VRF_ID_MAX; vrfId++) {
            // 由于插入时有内存上限，，并非所有ART数都有数据，此处只有page大于2的ART才可能触发缩容
            if (pageNum[vrId][vrfId] > 2u) {
                IndexStatisticsT idxStat = GetArtIndexStatistics(vrfArr, vrfId);
                EXPECT_GT(pageNum[vrId][vrfId], idxStat.artIndex.artMemPerfStat.pageCount);
            }
        }
    }

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

static IndexCtxT *LpmUtBatchOpBeforeScale(IndexOpenCfgT lpmCtx, ShmemPtrT lpmShmAddr)
{
    IndexCtxT *lpmHandle = {0};
    Status ret = IdxAlloc(g_seRunCtxForLpm, LPM6_INDEX, &lpmHandle);
    EXPECT_EQ(GMERR_OK, ret);
    ret = UtLpmIndexOpen(lpmCtx, lpmShmAddr, lpmHandle);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t vrId = 0; vrId < VR_ID_MAX; vrId++) {
        for (uint32_t vrfId = 0; vrfId < VRF_ID_MAX; vrfId++) {
            LpmUtBatchInsert(lpmHandle, vrfId, vrId);
            LpmUtBatchDelete(lpmHandle, vrfId, vrId);
        }
    }
    return lpmHandle;
}

TEST_F(UtLpmIndex, storage_lpm6_index_scale_and_check_invalid_cache_074)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpmShmAddr;
    Status ret = LpmIndexCreate(g_seRunCtxForLpm, lpm6Cfg, &lpmShmAddr);
    EXPECT_EQ(GMERR_OK, ret);
    IndexOpenCfgT lpmCtx = {0};
    lpmCtx.seRunCtx = g_seRunCtxForLpm;
    LpmCtxInitCallbackFuncation(&lpmCtx);

    IndexCtxT *idxCtxScale = LpmUtBatchOpBeforeScale(lpmCtx, lpmShmAddr);
    IndexCtxT *idxCtxDml = LpmUtBatchOpBeforeScale(lpmCtx, lpmShmAddr);
    ArtRunningCtxT *artCtxScale = &((LpmCtxT *)idxCtxScale->idxRunCtx)->artCtx;
    ArtRunningCtxT *artCtxDml = &((LpmCtxT *)idxCtxDml->idxRunCtx)->artCtx;

    uint64_t scalePageCacheVersion = artCtxScale->memRunCtx.pageCache.version;
    uint64_t scaleMemMgrVersion = artCtxScale->art->artMemMgr.version;
    uint64_t dmlPageCacheVersion = artCtxDml->memRunCtx.pageCache.version;
    uint64_t dmlMemMgrVersion = artCtxDml->art->artMemMgr.version;

    UtLpmIndexScaleIn(idxCtxScale);
    EXPECT_GE(artCtxScale->memRunCtx.artMemMgr->scaleInCount, 1u);
    EXPECT_GE(artCtxDml->memRunCtx.artMemMgr->scaleInCount, 1u);

    uint64_t scaleNewPageCacheVersion = artCtxScale->memRunCtx.pageCache.version;
    uint64_t scaleNewMemMgrVersion = artCtxScale->art->artMemMgr.version;
    uint64_t dmlNewPageCacheVersion = artCtxDml->memRunCtx.pageCache.version;
    uint64_t dmlNewMemMgrVersion = artCtxDml->art->artMemMgr.version;

    EXPECT_EQ(scalePageCacheVersion, scaleMemMgrVersion);
    EXPECT_EQ(scalePageCacheVersion, dmlPageCacheVersion);
    EXPECT_EQ(scalePageCacheVersion, dmlMemMgrVersion);

    EXPECT_NE(scalePageCacheVersion, scaleNewPageCacheVersion);
    EXPECT_EQ(scalePageCacheVersion, dmlNewPageCacheVersion);

    EXPECT_NE(scalePageCacheVersion, scaleNewMemMgrVersion);
    EXPECT_EQ(scaleNewMemMgrVersion, dmlNewMemMgrVersion);

    LpmIndexClose(idxCtxScale);
    IdxRelease(idxCtxScale);
    LpmIndexClose(idxCtxDml);
    IdxRelease(idxCtxDml);
    LpmIndexDrop(g_seRunCtxForLpm, lpmShmAddr);
}

TEST_F(UtLpmIndex, storage_lpm6_index_insert_and_range_scan_key_075)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);

    uint8_t keyBuf1[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 125};
    UtLpm6IndexInsert(lpm6Handle, keyBuf1, IPV6_KEY_LEN, 1);

    uint8_t keyBuf2[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 126};
    UtLpm6IndexInsert(lpm6Handle, keyBuf2, IPV6_KEY_LEN, 2);

    uint8_t keyBuf3[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 127};
    UtLpm6IndexInsert(lpm6Handle, keyBuf3, IPV6_KEY_LEN, 3);

    uint8_t minKeyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1};
    uint8_t leftKeyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 125};
    uint8_t rightKeyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 127};
    uint8_t maxKeyBuf[25] = {0, 0, 0, 0, 0, 0, 0, 0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
        0xff, 0xff, 0xff, 0xff, 0xff, 128};

    UtLpm6IndexScan(lpm6Handle, minKeyBuf, leftKeyBuf, 1, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_CLOSED, 1);
    UtLpm6IndexScan(lpm6Handle, minKeyBuf, leftKeyBuf, 1, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_OPENRIGHT, 0);
    UtLpm6IndexScan(lpm6Handle, rightKeyBuf, maxKeyBuf, 3, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_CLOSED, 1);
    UtLpm6IndexScan(lpm6Handle, rightKeyBuf, maxKeyBuf, 3, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_OPENLEFT, 0);

    UtLpm6IndexScan(lpm6Handle, leftKeyBuf, rightKeyBuf, 1, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_CLOSED, 3);
    UtLpm6IndexScan(lpm6Handle, leftKeyBuf, rightKeyBuf, 2, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_OPENLEFT, 2);
    UtLpm6IndexScan(lpm6Handle, leftKeyBuf, rightKeyBuf, 1, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_OPENRIGHT, 2);
    UtLpm6IndexScan(lpm6Handle, leftKeyBuf, rightKeyBuf, 2, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_OPENBOTH, 1);
    UtLpm6IndexScan(lpm6Handle, leftKeyBuf, rightKeyBuf, 1, true, IPV6_KEY_LEN, GMERR_OK, INDEX_RANGE_BUTT, 3);

    UtLpmIndexUnInit(lpm6ShmAddr, lpm6Handle);
}

TEST_F(UtLpmIndex, storage_lpm6_index_wrong_key_and_range_scan_key_076)
{
    IndexMetaCfgT lpm6Cfg = g_lpm6MetaCfg;
    ShmemPtrT lpm6ShmAddr;
    IndexCtxT *lpm6Handle = {0};
    UtLpmIndexInit(lpm6Cfg, &lpm6ShmAddr, &lpm6Handle);
    UtLpm6IndexBeginScanFailed(lpm6Handle, IPV6_KEY_LEN - 1);
    UtLpm6IndexBeginScanFailed(lpm6Handle, IPV6_KEY_LEN + 1);
}

TEST_F(UtLpmIndex, storage_lpm4_index_insert_update_078)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    uint32_t keyBuf1[4] = {0, 0, 0xffffff00, 32};
    uint32_t keyBuf2[4] = {0, 0, 0xffffff01, 32};
    HpTupleAddr addrWrite = 1;
    HpTupleAddr addrWrite2 = 2;

    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);
    UtLpm4IndexLookup(lpm4Handle, keyBuf1, addrWrite);

    uint8_t delKeyBuf[IPV4_KEY_LEN];
    IndexKeyT delIndexKey = GenerateIPV46IndexKey(keyBuf1, delKeyBuf, IPV4_KEY_LEN, IPV4_KEY_LEN);
    uint8_t insertKeyBuf[IPV4_KEY_LEN];
    IndexKeyT insertIndexKey = GenerateIPV46IndexKey(keyBuf2, insertKeyBuf, IPV4_KEY_LEN, IPV4_KEY_LEN);
    removePara.isErase = true;
    IndexUpdateInfoT updateInfo = {
        .oldIdxKey = delIndexKey, .newIdxKey = insertIndexKey, .oldAddr = addrWrite, .newAddr = addrWrite2};
    Status ret = LpmIndexUpdate(lpm4Handle, updateInfo, removePara);
    EXPECT_EQ(GMERR_OK, ret);

    UtLpm4IndexDelete(lpm4Handle, keyBuf2, IPV4_KEY_LEN, addrWrite, removePara);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm4_index_truncate_079)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    LpmIndexT *lpmIndex = (LpmIndexT *)DbShmPtrToAddr(lpm4ShmAddr);
    LpmAddrT *addr = (LpmAddrT *)DbShmPtrToAddr(lpmIndex->addrArrayHead);
    bool isValid = DbIsShmPtrValid(addr->next);
    EXPECT_EQ(false, isValid);

    uint32_t keyBuf[4] = {0, 0, 0xc0ac1000, 25};
    for (uint64_t i = 0; i < 10000u; i++) {
        keyBuf[0] = i % 8;
        keyBuf[1] = i % 64;
        keyBuf[2] += 0x1001 * (i % 100);
        keyBuf[3] = 0x10000 * (i % 32) + 20;
        HpTupleAddr addrWrite = i * 1024;
        UtLpm4IndexInsert(lpm4Handle, keyBuf, IPV4_KEY_LEN, addrWrite);
    }

    UtLpmIndexTruncate(lpm4Handle, lpm4ShmAddr);
    addr = (LpmAddrT *)DbShmPtrToAddr(lpmIndex->addrArrayHead);
    isValid = DbIsShmPtrValid(addr->next);
    EXPECT_EQ(false, isValid);

    LpmIndexDrop(g_seRunCtxForLpm, lpm4ShmAddr);
}

static int g_gmdbArtInitCount = 0;

Status ArtInitStub(ArtCfgT artCfg, ArtTreeT *artTree)
{
    DB_UNUSED(artCfg);
    DB_UNUSED(artTree);
    if (g_gmdbArtInitCount < 10) {
        return GMERR_OK;
    } else {
        return GMERR_INTERNAL_ERROR;
    }
}

TEST_F(UtLpmIndex, storage_lpm4_index_double_free_080)
{
    int index = setStubC((void *)ArtInit, (void *)ArtInitStub);

    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    for (g_gmdbArtInitCount++; g_gmdbArtInitCount <= 10; g_gmdbArtInitCount++) {
        DbMemCtxT *lpmMemCtx = ((LpmCtxT *)lpm4Handle->idxRunCtx)->memCtx;
        LpmIndexT *lpmIndex = LpmCastIdxAsLpmIdx(lpm4Handle->idxHandle);
        ShmemPtrT vrfArrShmPtr = DB_INVALID_SHMPTR;
        uint32_t maxLen = ((uint32_t)sizeof(ShmemPtrT)) * lpmIndex->vrfIdMax;
        (void)SeShmAlloc(lpmMemCtx, maxLen, &vrfArrShmPtr);
        Status ret = LpmInitArtForVrfIdShmPtr(lpmMemCtx, lpmIndex, &vrfArrShmPtr);
        if (g_gmdbArtInitCount < 10) {
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(
                true, vrfArrShmPtr.segId != DB_INVALID_SHMPTR.segId && vrfArrShmPtr.offset != DB_INVALID_SHMPTR.offset);
        } else {
            EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);
            EXPECT_EQ(
                true, vrfArrShmPtr.segId == DB_INVALID_SHMPTR.segId && vrfArrShmPtr.offset == DB_INVALID_SHMPTR.offset);
        }
        DbShmemCtxFree(lpmMemCtx, vrfArrShmPtr);
    }

    LpmIndexDrop(g_seRunCtxForLpm, lpm4ShmAddr);
    clearStub(index);
}

TEST_F(UtLpmIndex, storage_lpm4_index_undo_insert_same_record_and_delete_081)
{
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    lpm4Cfg.indexMultiVersionType = INDEX_PCC_RR_TYPE;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);

    uint32_t keyBuf1[4] = {0, 0, 0xfffffff0, 24};
    HpTupleAddr addrWrite = 1;
    UtLpm4IndexInsert(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite);

    IndexRemoveParaT removePara = {.isErase = false, .isGc = false};
    UtLpm4IndexDelete(lpm4Handle, keyBuf1, IPV4_KEY_LEN, addrWrite, removePara);

    uint32_t keyBuf2[4] = {0, 0, 0xfffffff0, 24};
    HpTupleAddr addrWrite2 = 2;
    lpm4Handle->idxOpenCfg.callbackFunc.addrCheckAndFetch = LpmCheckAddrAndFetchStubNotExist;
    UtLpm4IndexInsert(lpm4Handle, keyBuf2, IPV4_KEY_LEN, addrWrite2);
    // undo删除成功，剩一条标记删除的数据
    UtLpm4IndexUndoInsert(lpm4Handle, keyBuf2, IPV4_KEY_LEN, addrWrite2);

    IndexStatisticsT idxStat = {0};
    LpmIndexStatView(lpm4ShmAddr, 0, &idxStat);
    EXPECT_EQ(idxStat.artIndex.insertDataCount, 1u);

    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}

TEST_F(UtLpmIndex, storage_lpm_index_view_get_page_size_082)
{
    uint16_t instanceId = 0;
    uint64_t idxPageSize = 0;
    IndexMetaCfgT lpm4Cfg = g_lpm4MetaCfg;
    ShmemPtrT lpm4ShmAddr;
    IndexCtxT *lpm4Handle = {0};
    UtLpmIndexInit(lpm4Cfg, &lpm4ShmAddr, &lpm4Handle);
    Status ret = LpmIndexViewGetPageSize(lpm4ShmAddr, instanceId, &idxPageSize);
    EXPECT_EQ(GMERR_OK, ret);
    UtLpmIndexUnInit(lpm4ShmAddr, lpm4Handle);
}
