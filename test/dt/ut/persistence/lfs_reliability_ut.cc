/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: persistence reliability ut test for fsm
 * Author:zhouwenkang
 * Create: 2023-12-27
 */
#include "ut_reliability.h"
#include "persistence_common.h"
#include "se_heap.h"
#include "se_heap_base.h"
#include "se_heap_utils.h"
#include "se_page.h"
#include "se_space.h"
#include "dm_data_basic.h"
#include "se_lfsmgr_inner.h"
#include "se_lfsmgr.h"
#include "se_lfsmgr_redo.h"
#include "db_mem_context.h"
#include "se_ckpt_inner.h"
#include "se_database.h"
#include "ut_space_util.h"

const static uint32_t UT_REQUIRESIZE = 1024;
const static uint32_t UT_PAGERAWSIZE = 3800;
static int g_stub = 0;
static uint32_t g_stubTimes = 0;
static uint32_t g_callTimes = 0;

extern "C" {
StatusInter LfsSearchAvailBlock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t requireSize, PageAddrT *addr, bool *isNewBlock, DbInstanceHdT dbInstance);
StatusInter LfsGetAllocListId(const LfsMgrT *mgr, uint32_t requireSize, uint32_t *listId);
StatusInter LfsGetOwnerListId(const LfsMgrT *mgr, uint32_t availSize, int32_t *listId);
StatusInter LfsGetFsmPageByCache(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, FsmPageHeadT **page, bool isWrite);
StatusInter LfsGetSlotByFsmPageIdxAndSlotId(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotInfoT fsmSlotInfo, FsmListNodeT **slot, bool isWrite);
StatusInter LfsGetNewUpperPage(PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *pageAddr, DbInstanceHdT dbInstance);
StatusInter LfsAllocNewFsmPage(PageMgrT *pageMgr, LfsMgrT *mgr, FsmPageHeadT **newFsmPage, DbInstanceHdT dbInstance);
StatusInter LfsGetNewFsmPage(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t blockId, FsmPageHeadT **page, DbInstanceHdT dbInstance);
StatusInter LfsUpdateBlockFreeSpace(PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotParaT *slotPara,
    FsmBlockUpdateParaT updatePara, bool relink, uint32_t listId);
StatusInter LfsFreeAllFsmPage(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset);
StatusInter LfsFreeFsmPage(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx);
StatusInter SeFreePage(PageMgrT *pageMgr, FreePageParamT *freePageParam);
StatusInter LfsGetSpecificSlotByFsmPageIdxAndSlotId(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT **slot);
void LfsInitListRange(LfsMgrT *mgr);
StatusInter LfsRedoForSetBlockFreeSpaceImpl(PageMgrT *pageMgr, LfsMgrT *mgr, FsmUpdatedSlotInfoT *targetSlot,
    uint32_t newListId, bool relink, FsmListT *oldLinkFsmList);
bool LfsIsGetFragmentedSlotId(PageMgrT *pageMgr, LfsMgrT *mgr, int32_t fragListId, uint32_t lastFsmListIdx,
    uint32_t *fsmPageIdx, uint32_t *slotId);
StatusInter LfsReleaseUpperPageWithRedo(
    PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx, uint32_t slotId);
void LfsRedoForMgrInExtentFsmSlot(LfsMgrT *mgr, bool allocNewFsmPage);
void LfsRedoForSlotImpl(PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT *slot);
StatusInter LfsAllocLv2FsmPageTable(PageMgrT *pageMgr, LfsMgrT *mgr, DbInstanceHdT dbInstance);
StatusInter LfsAllocLv1FsmPageTable(PageMgrT *pageMgr, LfsMgrT *mgr, DbInstanceHdT dbInstance);
}

class LfsReliabilityUt : public UtReliability, public PersistenceUtTest {
public:
    void InitBeforeSetUp() override
    {
        system("ipcrm -a");
        system("rm -rf /data/gmdb");
        DbSetServerThreadFlag();
        ConstructSeInsAndSeRun(SE_CONFIG_INI_PATH);
        UtReplySetUp(seIns);
    }
    void DestroyAfterTearDown() override
    {
        UtReplyTearDown(seIns);
        clearAllStub();
        DestroySeIns();
    }
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
};

static PageIdT UtHeapCreateLabel(SeInstanceT *seIns)
{
    HeapAccessCfgT heapCfg = {
        .pageType = HEAP_VAR_LEN_ROW_PAGE,
        .tupleType = HEAP_TUPLE_TYPE_VERTEX,
        .fixRowSize = 0,
        .slotExtendSize = 0,
        .seInstanceId = GET_INSTANCE_ID,
        .isYangBigStore = false,
        .isStatusMergeSubs = false,
        .isPersistent = true,
        .isLabelLockSerializable = false,
        .isUseRsm = false,
        .ccType = CONCURRENCY_CONTROL_NORMAL,
        .trxType = OPTIMISTIC_TRX,
        .isolation = REPEATABLE_READ,
        .labelId = 1,
        .heapFileId = 0,
        .heapFsmFileId = 0,
        .tableSpaceId = 0,
        .tableSpaceIndex = 0,
        .fsmTableSpaceIndex = 0,
        .maxItemNum = DB_MAX_UINT64,
    };
    SeGetSpaceIdByType(SPACE_TYPE_USER_DEFAULT, DbGetProcGlobalId(), &heapCfg.tableSpaceIndex);
    PageIdT pageAddr = SE_INVALID_PAGE_ADDR;
    HeapLabelCreate(PersistenceUtTest::seRunCtx, &heapCfg, (ShmemPtrT *)&pageAddr);
    return pageAddr;
}

static StatusInter UtGetLfsMgr(LfsMgrT **fsmMgr)
{
    PageIdT heapPageId = UtHeapCreateLabel(PersistenceUtTest::seIns);
    bool result = DbIsPageIdValid(heapPageId);
    DB_ASSERT(result);
    uint8_t *pageHead;
    StatusInter ret = SeGetPage(
        (PageMgrT *)PersistenceUtTest::seRunCtx->pageMgr, heapPageId, (uint8_t **)&pageHead, ENTER_PAGE_NORMAL, false);
    DB_ASSERT(ret == STATUS_OK_INTER && pageHead != NULL);
    HeapT *heap = (HeapT *)(void *)(pageHead + sizeof(PageHeadT));
    (*fsmMgr) = &heap->fsm;
    return STATUS_OK_INTER;
}

static inline void Ut_InitFsmRunCtxCfg(FsmRunCtxCfgT *fsmRunCtxCfg, PageIdT pageId, uint32_t offset)
{
    fsmRunCtxCfg->pageMgr = (PageMgrT *)PersistenceUtTest::seRunCtx->pageMgr;
    fsmRunCtxCfg->fsmAddr = pageId;
    fsmRunCtxCfg->offset = offset;
    fsmRunCtxCfg->directAccess = 0;
    fsmRunCtxCfg->enableLatch = 0;
    fsmRunCtxCfg->enableCache = 0;
    fsmRunCtxCfg->enableRedo = 1;
}

static void UtLeaveLfsMgr(PageMgrT *pageMgr, PageIdT pageAddr, bool isChanged)
{
    SeLeavePage(pageMgr, pageAddr, isChanged);
}

/**
 * @tc.name: InitFsmMgr_001
 * @tc.desc:test lfs mgr init, lfs mgr in the heapT page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, InitFsmMgr_001)
{
    PageIdT heapPageId = UtHeapCreateLabel(seIns);
    EXPECT_EQ(true, DbIsPageIdValid(heapPageId));
}

/**
 * @tc.name: InitFsmMgr_002
 * @tc.desc:test lfs mgr init, lfs mgr in the heapT page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, InitFsmMgr_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    LfsMgrT mgr = *fsmMgr;
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    mgr.cfgInfo.reserveSize = mgr.cfgInfo.availSize;
    LfsInitListRange(&mgr);
}

/**
GetAvailBlock成功的场景主要有
1. 使用新的slot
1.1 mgr中的slot ———— GetAvailBlock_1_001 (GetAvailBlock_1_00x 主要为该场景的异常场景)
1.2 fsm页中的slot ———— GetAvailBlock_2_001 (GetAvailBlock_2_00x 主要为该场景的异常场景)
2. 使用旧的slot
2.1 mgr中的slot ———— GetAvailBlock_3_001 (GetAvailBlock_3_00x 主要为该场景的异常场景)
2.2 fsm页中的slot ———— GetAvailBlock_4_001 (GetAvailBlock_4_00x 主要为该场景的异常场景)
2.3 该slot位于最高级链表且对应的页被freed ———— GetAvailBlock_5_00x (GetAvailBlock_5_00x 主要为该场景的异常场景)
2.4 该slot在链上有后续节点 ———— GetAvailBlock_6_001 (GetAvailBlock_6_00x 主要为该场景的异常场景)
3. 使用fsm扩展的slot，无需测试已挂链场景的申请，与fsm扩展场景无关
3.1 测试上述场景的正常场景 GetAvailBlock_7_001 (GetAvailBlock_7_00x 主要为该场景的正常场景)
3.2 测试上述场景的异常场景 GetAvailBlock_8_001 (GetAvailBlock_8_00x 主要为该场景的异常场景)
 */
/**
 * @tc.name: GetAvailBlock_1_001
 * @tc.desc: success to get avail block by a new slot in mgr
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_1_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);   // no fsm page because the new slot in mgr page

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_1_002
 * @tc.desc: success to get avail block and return it to the fsm
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_1_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);   // no fsm page because the new slot in mgr page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_1_003
 * @tc.desc: unsuccess to get avail block because of requireSize
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_1_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    // novalid availSize
    uint32_t availSize = fsmMgr->cfgInfo.availSize + 1;
    ret = LfsGetAvailBlock(&fsmRunCtx, availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_REQ_SIZE);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

StatusInter LfsSearchAvailBlockMock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t requireSize, PageAddrT *addr, bool *isNewBlock, DbInstanceHdT dbInstance)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: GetAvailBlock_1_004
 * @tc.desc: unsuccess to get avail block because LfsSearchAvailBlock mock to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_1_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    setStubC((void *)LfsSearchAvailBlock, (void *)LfsSearchAvailBlockMock);
    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetNewUpperPageMock(PageMgrT *pageMgr, LfsMgrT *mgr, PageAddrT *pageAddr)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: GetAvailBlock_1_007
 * @tc.desc: unsuccess to get avail block
 * 1. when alloc a new upper page unsucc because of LfsGetNewUpperPage is mocked to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_1_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    // get a new block while unsucc to alloc upper page
    setStubC((void *)LfsGetNewUpperPage, (void *)LfsGetNewUpperPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_2_001
 * @tc.desc: success to get avail block by a new slot in new fsm page
 * 1. get FSM_SLOT_NUM_IN_MGR new block
 * 2. the next block uses a new slot in new fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_2_002
 * @tc.desc: success to get avail block by a new slot in new fsm page
 * 1. get FSM_SLOT_NUM_IN_MGR new block
 * 2. the next block uses a new slot in new fsm page
 * 3. return the last block the fsm
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

StatusInter LfsAllocNewFsmPageMock(PageMgrT *pageMgr, LfsMgrT *mgr, FsmPageHeadT **newFsmPage, DbInstanceHdT dbInstance)
{
    clearStub(g_stub);
    uint32_t fsmPageCnt = mgr->fsmPageCnt;
    mgr->fsmPageCnt = mgr->allFsmPageNum;
    StatusInter ret = LfsAllocNewFsmPage(pageMgr, mgr, newFsmPage, dbInstance);
    mgr->fsmPageCnt = fsmPageCnt;
    return ret;
}
/**
 * @tc.name: GetAvailBlock_2_003
 * @tc.desc: unsuccess to get avail block
 * 1. when using a new slot in fsm page
 * 2. fsmPageCnt is not enough
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    g_stub = setStubC((void *)LfsAllocNewFsmPage, (void *)LfsAllocNewFsmPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INT_ERR_LFS_TOO_MANY_FSM_PAGE);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetNewFsmPageMock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t blockId, FsmPageHeadT **page, DbInstanceHdT dbInstance)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: GetAvailBlock_2_004
 * @tc.desc: unsuccess to get avail block
 * 1. when using a new slot in fsm page
 * 2. alloc new fsm page while unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    setStubC((void *)LfsGetNewFsmPage, (void *)LfsGetNewFsmPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

static StatusInter SeAllocPageMock(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *pageAddr)
{
    return INTERNAL_ERROR_INTER;
}

static StatusInter SeAllocPageTimesMock(PageMgrT *pageMgr, AllocPageParamT *allocPageParam, PageIdT *pageAddr)
{
    g_callTimes++;
    if (g_callTimes < g_stubTimes) {
        g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageTimesMock);
        // 执行默认的框架打桩函数
        return SeAllocPageRelyMock(pageMgr, allocPageParam, pageAddr);
    } else {
        g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageRelyMock);  // 使用框架函数重新打桩
        return SeAllocPageMock(pageMgr, allocPageParam, pageAddr);
    }
}

/**
 * @tc.name: GetAvailBlock_2_005
 * @tc.desc: unsuccess to get avail block
 * 1. when using a new slot in fsm page
 * 2. alloc new fsm page while unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    setStubC((void *)SeAllocPage, (void *)SeAllocPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_2_006
 * @tc.desc: unsuccess to get avail block
 * 1. success alloc new fsm page
 * 2. unsuccess alloc new data page
 * 3. check rollback
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    g_callTimes = 0;
    g_stubTimes = 2;
    g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageTimesMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_2_007
 * @tc.desc: unsuccess to get avail block
 * 1. when using a new slot in fsm page
 * 2. alloc new data page while unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_2_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in an old fsm page
    setStubC((void *)SeAllocPage, (void *)SeAllocPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_3_001
 * @tc.desc: success to get avail block by an old slot in mgr page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_3_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);   // no fsm page because the new slot in mgr page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(false, isNewBlock);  // old slot

    // leave the data page manually
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_4_001
 * @tc.desc: success to get avail block by an old slot in fsm page
 * 1. get FSM_SLOT_NUM_IN_MGR new block and return to fsm with freeSize is 0
 * 2. get a new block and return
 * 3. get a block using the last slot
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_4_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    blockInfo.freeSize = 31 * UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a block by old slot in fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(false, isNewBlock);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetFsmPageByCacheMock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, FsmPageHeadT **page, bool isWrite)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: GetAvailBlock_4_002
 * @tc.desc: unsuccess to get avail block because LfsGetFsmPageByCache mock to return INTERNAL_ERROR_INTER
 * in the fun LfsGetFsmSlotById
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_4_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    blockInfo.freeSize = 31 * UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a block by old slot in fsm page
    setStubC((void *)LfsGetFsmPageByCache, (void *)LfsGetFsmPageByCacheMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_4_003
 * @tc.desc: unsuccess to get avail block
 * 1. when using a new slot in fsm page
 * 2. get an old fsm page unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_4_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    blockInfo.freeSize = 0;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);

    // get a new block using a new slot in old fsm page while get the old fsm page unsucc
    setStubC((void *)LfsGetFsmPageByCache, (void *)LfsGetFsmPageByCacheMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetSlotByFsmPageIdxAndSlotIdMock(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotInfoT fsmSlotInfo, FsmListNodeT **slot, bool isWrite)
{
    clearStub(g_stub);
    FsmSlotInfoT fsmSlotInfoTemp = {.fsmPageIdx = fsmSlotInfo.fsmPageIdx, .slotId = 0XFFFFFFFF};
    return LfsGetSlotByFsmPageIdxAndSlotId(pageMgr, mgr, fsmSlotInfoTemp, slot, isWrite);
}

/**
 * @tc.name: GetAvailBlock_4_004
 * @tc.desc: unsuccess to get avail block because slotId is too larger in LfsGetSlotByFsmPageIdxAndSlotId
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_4_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // in order to get the fsmpage when alloc block, we should alloc a new fsm page and make one of its slot used
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    blockInfo.freeSize = 31 * UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a block by old slot in fsm page
    g_stub = setStubC((void *)LfsGetSlotByFsmPageIdxAndSlotId, (void *)LfsGetSlotByFsmPageIdxAndSlotIdMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_4_005
 * @tc.desc: success to get avail block by an old slot in fsm page, and the next slot is also in fsm page
 * 1. get FSM_SLOT_NUM_IN_MGR new block and return to fsm with freeSize is 0
 * 2. get two new blocks in fsm page and return
 * 3. get a block using the last slot
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_4_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    for (uint32_t i = 0; i < 2; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
        blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a block by old slot in fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(false, isNewBlock);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_5_001
 * @tc.desc: success to get avail block, the slot is in the free list
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_5_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    BlockInfoT blockInfo = {0};
    blockInfo.freeSize = fsmMgr->cfgInfo.availSize;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true, blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a new block by a freed slot
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_5_002
 * @tc.desc: unsuccess to get avail block in free list because LfsGetNewUpperPage is mock to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_5_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    BlockInfoT blockInfo = {0};
    blockInfo.freeSize = fsmMgr->cfgInfo.availSize;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true, blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a new block by a freed slot
    setStubC((void *)LfsGetNewUpperPage, (void *)LfsGetNewUpperPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ(true, isNewBlock);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_6_001
 * @tc.desc: success to get avail block with an old slot which has next slot
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_6_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize / 2, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(false, isNewBlock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

static void UtUpdateInfoForFsmExtension(FsmRunCtxT *fsmRunCtx)
{
    LfsMgrT *fsmMgr = NULL;
    (void)FsmRunCtxGetMgr(fsmRunCtx, true, &fsmMgr);
    DB_ASSERT(fsmMgr != NULL);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;
    fsmMgr->fsmPageCnt = fsmPageIdNumsInMgr;
    fsmMgr->lastFsmPageId = fsmPageIdNumsInMgr;
    fsmMgr->maxBlockCnt = fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR;
    fsmMgr->usedPageCnt = UT_REQUIRESIZE;
    fsmMgr->releasedPageCnt = UT_REQUIRESIZE;
    fsmMgr->freePageCnt =
        fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR - fsmMgr->usedPageCnt - fsmMgr->releasedPageCnt;
    LfsRedoForMgrInExtentFsmSlot(fsmMgr, true);
    LfsRedoForMgrPageCnt(fsmMgr);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_001
 * @tc.desc: success to get avail block by a new slot in mgr. this slot is from the fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_001)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_002
 * @tc.desc: success to get avail block and return it to the fsm. this slot is from old lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_002)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 2, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

static void UtUpdateInfoForFullLv1FsmPage(FsmRunCtxT *fsmRunCtx)
{
    LfsMgrT *fsmMgr = NULL;
    (void)FsmRunCtxGetMgr(fsmRunCtx, true, &fsmMgr);
    DB_ASSERT(fsmMgr != NULL);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;
    fsmMgr->fsmPageCnt = fsmPageIdNumsInMgr + fsmMgr->maxSlotPerFsmTablePage;
    fsmMgr->lastFsmPageId = fsmPageIdNumsInMgr + fsmMgr->maxSlotPerFsmTablePage;
    fsmMgr->maxBlockCnt =
        (fsmPageIdNumsInMgr + fsmMgr->maxSlotPerFsmTablePage) * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR;
    fsmMgr->usedPageCnt = UT_REQUIRESIZE + 1;
    fsmMgr->releasedPageCnt = UT_REQUIRESIZE;
    fsmMgr->freePageCnt = (fsmPageIdNumsInMgr + fsmMgr->maxSlotPerFsmTablePage) * maxSlotPerFsmPage +
                          FSM_SLOT_NUM_IN_MGR - fsmMgr->usedPageCnt - fsmMgr->releasedPageCnt;
    LfsRedoForMgrInExtentFsmSlot(fsmMgr, true);
    LfsRedoForMgrPageCnt(fsmMgr);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_003
 * @tc.desc: success to get avail block and return it to the fsm. this slot is from new lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_003)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t maxSlotPerFsmTablePage = fsmMgr->maxSlotPerFsmTablePage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set the first lv1 fsm page full
    UtUpdateInfoForFullLv1FsmPage(&fsmRunCtx);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)(fsmPageIdNumsInMgr + maxSlotPerFsmTablePage) * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1,
        fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + maxSlotPerFsmTablePage + 1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)2, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_004
 * @tc.desc: success to get avail block by a new slot in mgr, release and then reuse.
             this slot is from the fsm extension.
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_004)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // reuse the released slot
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(false, isNewBlock);  // old slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // leave the data page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_005
 * @tc.desc: success to get avail block and return it to the fsm, release and then reuse.
             this slot is from old lv1 fsm page.
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_005)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 2, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // reuse the released slot
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(false, isNewBlock);  // old slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 2, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_006
 * @tc.desc: success to get avail block and return it to the fsm, release and then reuse.
             this slot is from new lv1 fsm page.
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_006)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t maxSlotPerFsmTablePage = fsmMgr->maxSlotPerFsmTablePage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set the first lv1 fsm page full
    UtUpdateInfoForFullLv1FsmPage(&fsmRunCtx);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)(fsmPageIdNumsInMgr + maxSlotPerFsmTablePage) * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1,
        fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + maxSlotPerFsmTablePage + 1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)2, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // reuse the released slot
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(false, isNewBlock);  // old slot
    EXPECT_EQ((uint32_t)(fsmPageIdNumsInMgr + maxSlotPerFsmTablePage) * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1,
        fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + maxSlotPerFsmTablePage + 1,
        fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)2, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

const uint32_t OLD_INVALID_PAGE_IDX = 0xFFFF;
static void UtAllocPageTableForOldInvalidPageIdx(PageMgrT *pageMgr, LfsMgrT *fsmMgr)
{
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;
    fsmMgr->fsmPageCnt = fsmPageIdNumsInMgr;
    LfsRedoForMgrPageCnt(fsmMgr);
    (void)LfsAllocLv2FsmPageTable(pageMgr, fsmMgr, NULL);

    fsmMgr->fsmPageCnt = OLD_INVALID_PAGE_IDX;
    LfsRedoForMgrPageCnt(fsmMgr);
    fsmMgr->lv1TableCnt = (OLD_INVALID_PAGE_IDX - fsmPageIdNumsInMgr) / fsmMgr->maxSlotPerFsmTablePage - 1;
    (void)LfsAllocLv1FsmPageTable(pageMgr, fsmMgr, NULL);
}

static void UtUpdateInfoForOldInvalidPageIdx(FsmRunCtxT *fsmRunCtx)
{
    LfsMgrT *fsmMgr = NULL;
    (void)FsmRunCtxGetMgr(fsmRunCtx, true, &fsmMgr);
    DB_ASSERT(fsmMgr != NULL);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    UtAllocPageTableForOldInvalidPageIdx(fsmRunCtx->pageMgr, fsmMgr);
    fsmMgr->lastFsmPageId = OLD_INVALID_PAGE_IDX;
    fsmMgr->maxBlockCnt = OLD_INVALID_PAGE_IDX * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR;
    fsmMgr->usedPageCnt = UT_REQUIRESIZE;
    fsmMgr->releasedPageCnt = UT_REQUIRESIZE;
    fsmMgr->freePageCnt =
        OLD_INVALID_PAGE_IDX * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR - fsmMgr->usedPageCnt - fsmMgr->releasedPageCnt;
    LfsRedoForMgrInExtentFsmSlot(fsmMgr, true);
    LfsRedoForMgrPageCnt(fsmMgr);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
}

/**
 * @tc.name: GetAvailBlock_7_007
 * @tc.desc: success to get avail block and return it to the fsm. the new fsm page is in 0xFFFF slot
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_7_007)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForOldInvalidPageIdx(&fsmRunCtx);
    uint32_t lv1TableCnt = fsmMgr->lv1TableCnt;

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)OLD_INVALID_PAGE_IDX * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)OLD_INVALID_PAGE_IDX + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)lv1TableCnt, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_8_001
 * @tc.desc: unsucc to get avail block by a new slot in mgr. this slot is from the fsm extension.
             fail in allocating lv2 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_8_001)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 3;
    g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageTimesMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)0, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)0, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

StatusInter SeGetPageMock(PageMgrT *pageMgr, PageIdT pageAddr, uint8_t **page, PageOptionE option, bool isWrite)
{
    return INTERNAL_ERROR_INTER;
}

StatusInter SeGetPageMockTimes(PageMgrT *pageMgr, PageIdT pageAddr, uint8_t **page, PageOptionE option, bool isWrite)
{
    g_callTimes++;
    if (g_callTimes < g_stubTimes) {
        g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
        return SeGetPageRelyMock(pageMgr, (PageIdCompare)pageAddr, page, option, isWrite);  // 执行默认的框架打桩函数
    } else {
        g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);  // 使用框架函数重新打桩
        return SeGetPageMock(pageMgr, pageAddr, page, option, isWrite);
    }
}
/**
 * @tc.name: GetAvailBlock_8_002
 * @tc.desc: unsucc to get avail block by a new slot in mgr. this slot is from the fsm extension.
             fail in getting lv2 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_8_002)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 13;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)0, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_8_003
 * @tc.desc: unsucc to get avail block by a new slot in mgr. this slot is from the fsm extension.
             fail in allocating lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_8_003)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 4;
    g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageTimesMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)0, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_8_004
 * @tc.desc: unsucc to get avail block by a new slot in mgr. this slot is from the fsm extension.
             fail in getting lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_8_004)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 17;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: GetAvailBlock_8_005
 * @tc.desc: unsucc to get avail block and return it to the fsm. this slot is from old lv1 fsm page.
             fail in getting fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, GetAvailBlock_8_005)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    g_callTimes = 0;
    g_stubTimes = 4;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
SetBlockFreeSpace成功的场景主要有
1. 还一个不在链上的slot
1.1 该slot属于mgr ———— SetBlockFreeSpace_1_001 (SetBlockFreeSpace_1_00x 主要为该场景的异常场景)
1.2 该slot属于fsm页 ———— SetBlockFreeSpace_2_001 (SetBlockFreeSpace_2_00x 主要为该场景的异常场景)
1.3 该slot归还到最高层且被free ———— SetBlockFreeSpace_3_001 (SetBlockFreeSpace_3_00x 主要为该场景的异常场景)
2. 还一个在链上的slot
2.1 该slot对应的链上没有前后节点 ———— SetBlockFreeSpace_4_001 (SetBlockFreeSpace_4_00x 主要为该场景的异常场景)
2.2 该slot对应的链上只有后节点 ———— SetBlockFreeSpace_5_001 (SetBlockFreeSpace_5_00x 主要为该场景的异常场景)
2.3 该slot对应的链上只有前节点 ———— SetBlockFreeSpace_6_001 (SetBlockFreeSpace_6_00x 主要为该场景的异常场景)
2.4 该slot对应的链上有前后节点 ———— SetBlockFreeSpace_7_001 (SetBlockFreeSpace_7_00x 主要为该场景的异常场景)
2.5 slot所属新链为空 ———— SetBlockFreeSpace_8_001 (SetBlockFreeSpace_8_00x 主要为该场景的异常场景)
2.6 slot所属新链非空 ———— SetBlockFreeSpace_9_001 (SetBlockFreeSpace_9_00x 主要为该场景的异常场景)
2.7 slot所属新链恰好为旧链，即无需更换链表等级 ———— SetBlockFreeSpace_10_001 (SetBlockFreeSpace_10_00x
主要为该场景的异常场景)
3. 释放fsm扩展的slot，无需测试已挂链场景的释放，与fsm扩展场景无关
3.1 测试上述场景的正常场景 GetAvailBlock_11_001 (GetAvailBlock_11_00x 主要为该场景的正常场景)
无需测试异常场景，因为setBlockFree逻辑无修改
 */
/**
 * @tc.name: SetBlockFreeSpace_1_001
 * @tc.desc: success to set a slot in mgr
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_1_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

StatusInter LfsRedoForSetBlockFreeSpaceImplMock(PageMgrT *pageMgr, LfsMgrT *mgr, FsmUpdatedSlotInfoT *targetSlot,
    uint32_t newListId, bool relink, FsmListT *oldLinkFsmList)
{
    clearStub(g_stub);
    StatusInter ret = LfsRedoForSetBlockFreeSpaceImpl(pageMgr, mgr, targetSlot, newListId, relink, oldLinkFsmList);
    ret = INTERNAL_ERROR_INTER;
    return ret;
}

/**
 * @tc.name: SetBlockFreeSpace_1_002
 * @tc.desc: unsuccess to set a slot in mgr because LfsRedoForSetBlockFreeSpace return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_1_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    g_stub = setStubC((void *)LfsRedoForSetBlockFreeSpaceImpl, (void *)LfsRedoForSetBlockFreeSpaceImplMock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_2_001
 * @tc.desc: success to set a slot in fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_2_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true;
        blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ(true, isNewBlock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_2_002
 * @tc.desc: unsuccess to set a slot in fsm page while LfsGetFsmPageByCache is mocked to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_2_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ(true, isNewBlock);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    setStubC((void *)LfsGetFsmPageByCache, (void *)LfsGetFsmPageByCacheMock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_2_003
 * @tc.desc: unsuccess to set a slot in fsm page while slot id is too large
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_2_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ(true, isNewBlock);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    g_stub = setStubC((void *)LfsGetSlotByFsmPageIdxAndSlotId, (void *)LfsGetSlotByFsmPageIdxAndSlotIdMock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_2_004
 * @tc.desc: unsuccess to set a slot in fsm page while LfsGetOwnerListId is mock to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_2_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ(true, isNewBlock);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsUpdateBlockFreeSpaceMock(PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotParaT *slotPara,
    FsmBlockUpdateParaT updatePara, bool relink, uint32_t listId)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: SetBlockFreeSpace_2_005
 * @tc.desc: unsuccess to set a slot in fsm page while LfsUpdateBlockFreeSpace is mock to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_2_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    setStubC((void *)LfsUpdateBlockFreeSpace, (void *)LfsUpdateBlockFreeSpaceMock);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_3_001
 * @tc.desc: success to set a slot in mgr to free list
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_3_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_4_001
 * @tc.desc: success to set a slot in list and not pre not next
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_4_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    PageIdT pageAddr = addrInfo.pageHead->addr;

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    blockInfo.relink = false;
    blockInfo.freeSize = 0;
    blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_5_001
 * @tc.desc: success to set a slot in list, not pre but has next
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_5_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    // get and return the first
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get and return the second
    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    PageIdT pageAddr = addrInfo.pageHead->addr;
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    // return the second again, no pre one next
    blockInfo.relink = false;
    blockInfo.freeSize = 0;
    blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_6_001
 * @tc.desc: success to set a slot in list, one pre but no next
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_6_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    // get and return the first
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    PageIdT firstDataPageAddr = addrInfo.pageHead->addr;  // keep the first data page head
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get and return the second
    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, firstDataPageAddr, &page, ENTER_PAGE_NORMAL, false);
    // return the first again, one pre no next
    blockInfo.relink = false;
    blockInfo.freeSize = 0;
    blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, page, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_7_001
 * @tc.desc: success to set a slot in list, one pre and one next
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_7_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    // get and return the first
    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get and return the second
    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    PageIdT pageAddr = addrInfo.pageHead->addr;  // keep the second data page head
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get and return the third
    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)3, fsmMgr->maxBlockCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the second again, one pre one next
    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    blockInfo.relink = false;
    blockInfo.freeSize = 0;
    blockInfo.leavePage = true;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, page, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_8_001
 * @tc.desc: success to set a slot in list to a new list which is null
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_8_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    PageIdT pageAddr = addrInfo.pageHead->addr;

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the slot to a new list which is null
    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    blockInfo.leavePage = true;
    blockInfo.relink = false;
    blockInfo.freeSize = 0;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_9_001
 * @tc.desc: success to set a slot in list to a new list which is not null
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_9_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    PageIdT pageAddr = addrInfo.pageHead->addr;

    blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    blockInfo.relink = false;
    blockInfo.leavePage = true;
    blockInfo.freeSize = 0;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_10_001
 * @tc.desc: success to set a slot in list which new list Id is the old list Id
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_10_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};

    ret = LfsGetAvailBlock(&fsmRunCtx, 30 * UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);
    PageIdT pageAddr = addrInfo.pageHead->addr;

    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the slot to the same list again
    uint8_t *page = NULL;
    SeGetPage(fsmRunCtx.pageMgr, pageAddr, &page, ENTER_PAGE_NORMAL, false);
    blockInfo.relink = false;
    blockInfo.leavePage = true;
    blockInfo.freeSize = 0;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_11_001
 * @tc.desc: success to get avail block by a new slot in mgr, and then release. this slot is from the fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_11_001)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // free the data page
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_11_002
 * @tc.desc: success to get avail block and return it to the fsm, and then release. this slot is from old lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_11_002)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 2, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // release the data page
    blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: SetBlockFreeSpace_11_003
 * @tc.desc: success to get avail block and return it to the fsm, and then release. this slot is from new lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, SetBlockFreeSpace_11_003)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t maxSlotPerFsmTablePage = fsmMgr->maxSlotPerFsmTablePage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_PAGERAWSIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // link the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set the first lv1 fsm page full
    UtUpdateInfoForFullLv1FsmPage(&fsmRunCtx);

    // get a data page by a new slot in mgr, use old lv1 fsm page for the new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)(fsmPageIdNumsInMgr + maxSlotPerFsmTablePage) * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1,
        fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + maxSlotPerFsmTablePage + 1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)2, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // release data page
    blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsMgrReset_001
 * @tc.desc: success to reset the lfs mgr no fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_002
 * @tc.desc: success to reset the lfs mgr with fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // used FSM_SLOT_NUM_IN_MGR + 1 slot so that a fsm page in fsm mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);  // leave the data page by fsm
    RedoLogEnd(redoCtx, true);

    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter LfsMgrResetMock(PageMgrT *pageMgr, PageIdT fsmMgrPageId, uint32_t offset)
{
    DB_POINTER(pageMgr);

    StatusInter ret = LfsFreeAllFsmPage(pageMgr, fsmMgrPageId, offset);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "[SE-LFS] free all fsm page unsucc, fsm mgr = (%" PRIu32 ", %" PRIu32 ")", fsmMgrPageId.deviceId,
            fsmMgrPageId.blockId);
        return ret;
    }

    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    FsmRedoLogBegin(redoCtx, pageMgr->type != SE_MEMDATA);
    LfsMgrT *fsmMgr = NULL;
    ret = FsmGetMgr(pageMgr, fsmMgrPageId, offset, true, &fsmMgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "[SE-LFS] get fsmMgr unsuccess");
        return ret;
    }
    FsmLeaveMgr(pageMgr, fsmMgrPageId, false);
    fsmMgr = NULL;
    if (fsmMgr == NULL) {
        (void)RedoLogEnd(redoCtx, true);
        return INVALID_PARAMETER_VALUE_INTER;
    }
    return INVALID_PARAMETER_VALUE_INTER;
}
/**
 * @tc.name: LfsMgrReset_003
 * @tc.desc: unsuccess to reset the lfs mgr without fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // the 2th call FsmGetMgr in LfsMgrReset and return NULL
    setStubC((void *)LfsMgrReset, (void *)LfsMgrResetMock);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INVALID_PARAMETER_VALUE_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter LfsFreeLv0FsmPageMock(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx)
{
    FsmRedoLogBegin(PersistenceUtTest::redoCtx, (pageMgr->type != SE_MEMDATA));
    LfsMgrT *mgr = NULL;
    StatusInter ret = FsmGetMgr(pageMgr, fsmAddr, offset, true, &mgr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "[SE-LFS] get fsmMgr unsuccess");
        return ret;
    }
    mgr->fsmPageIds[0] = SE_INVALID_PAGE_ADDR;
    FsmLeaveMgr(pageMgr, fsmAddr, true);
    RedoLogEnd(PersistenceUtTest::redoCtx, true);
    clearStub(g_stub);
    return LfsFreeFsmPage(pageMgr, fsmAddr, offset, fsmPageIdx);
}
/**
 * @tc.name: LfsMgrReset_004
 * @tc.desc: unsuccess to reset the lfs mgr with fsm page
 * make the pageId of first fsm page (0xffff, 0xffff)
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // used FSM_SLOT_NUM_IN_MGR + 1 slot so that one fsm page in fsm mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);  // leave the data page by fsm
    RedoLogEnd(redoCtx, true);

    // simulate that the fsm page is freed
    g_stub = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeLv0FsmPageMock);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

static StatusInter SeFreePageMock(PageMgrT *pageMgr, FreePageParamT *freePageParam)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: LfsMgrReset_005
 * @tc.desc: unsuccess to reset the lfs mgr with fsm page
 * SeFreePageMock return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // used FSM_SLOT_NUM_IN_MGR + 1 slot so that one fsm page in fsm mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo;
    blockInfo.freeSize = 0;
    blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
    blockInfo.relink = true;
    blockInfo.leavePage = true;
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);  // leave the data page by fsm
    RedoLogEnd(redoCtx, true);

    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMock);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter LfsFreeFsmPageStub(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx)
{
    return STATUS_OK_INTER;
}

static int g_callTimesForFreeFsmPage = 0;
static int g_stubTimesForFreeFsmPage = 0;
static int g_stubForFreeFsmPage = 0;
StatusInter LfsFreeFsmPageMockForTimes(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx)
{
    g_callTimesForFreeFsmPage++;
    if (g_callTimesForFreeFsmPage < g_stubTimesForFreeFsmPage) {
        return LfsFreeFsmPageStub(pageMgr, fsmAddr, offset, fsmPageIdx);
    } else {
        clearStub(g_stubForFreeFsmPage);
        return LfsFreeFsmPage(pageMgr, fsmAddr, offset, fsmPageIdx);
    }
}
/**
 * @tc.name: LfsMgrReset_006
 * @tc.desc: success to reset the lfs mgr after alloc and free
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_007
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in getting lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageStub);
    g_callTimes = 0;
    g_stubTimes = 3;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    clearStub(g_stubForFreeFsmPage);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_008
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in freeing lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_008)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageStub);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMock);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    clearStub(g_stubForFreeFsmPage);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_009
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in getting lv2 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_009)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    g_callTimes = 0;
    g_stubTimes = 5;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_010
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in getting lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_010)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    g_callTimes = 0;
    g_stubTimes = 6;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter SeFreePageMockTimes(PageMgrT *pageMgr, FreePageParamT *freePageParam)
{
    g_callTimes++;
    if (g_callTimes < g_stubTimes) {
        g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMockTimes);
        return SeFreePageRelyMock(pageMgr, freePageParam);  // 执行默认的框架打桩函数
    } else {
        g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);  // 使用框架函数重新打桩
        return SeFreePageMock(pageMgr, freePageParam);
    }
}
/**
 * @tc.name: LfsMgrReset_011
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in freeing lv2 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_011)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageStub);
    g_callTimes = 0;
    g_stubTimes = 2;
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    clearStub(g_stubForFreeFsmPage);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrReset_012
 * @tc.desc: unsucc to reset the lfs mgr after alloc and free. fail in freeing fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsMgrReset_012)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMock);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrDestroy_001
 * @tc.desc: success to destroy the lfs mgr
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrDestroy_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsMgrDestroy(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter LfsMgrResetMockReturnError(PageMgrT *pageMgr, PageIdT fsmMgrPageId, uint32_t offset)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: LfsMgrDestroy_002
 * @tc.desc: unsuccess to destroy if LfsMgrReset return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrDestroy_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    setStubC((void *)LfsMgrReset, (void *)LfsMgrResetMockReturnError);
    ret = LfsMgrDestroy(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsGetMaxFreeSizeBlock_001
 * @tc.desc: succ to get a max free size block with a new slot
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetMaxFreeSizeBlock_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock = false;
    PageAddrInfoT addrInfo;
    ret = LfsGetMaxFreeSizeBlock(&fsmRunCtx, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(isNewBlock, true);
    SeLeavePage(fsmRunCtx.pageMgr, addrInfo.pageHead->addr, false);  // leave the data page
    RedoLogEnd(redoCtx, true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsGetMaxFreeSizeBlock_002
 * @tc.desc: succ to get a max free size block with an old slot
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetMaxFreeSizeBlock_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    isNewBlock = true;
    ret = LfsGetMaxFreeSizeBlock(&fsmRunCtx, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(isNewBlock, false);
    SeLeavePage(fsmRunCtx.pageMgr, addrInfo.pageHead->addr, false);  // leave the data page
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsGetMaxFreeSizeBlock_003
 * @tc.desc: unsucc to get a max free size block
 * LfsGetSlotByFsmPageIdxAndSlotId return
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetMaxFreeSizeBlock_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    isNewBlock = true;
    ret = LfsGetMaxFreeSizeBlock(&fsmRunCtx, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(isNewBlock, false);
    SeLeavePage(fsmRunCtx.pageMgr, addrInfo.pageHead->addr, false);  // leave the data page
    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetSlotByFsmPageIdxAndSlotIdMock2(
    PageMgrT *pageMgr, LfsMgrT *mgr, FsmSlotInfoT fsmSlotInfo, FsmListNodeT **slot, bool isWrite)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: LfsGetMaxFreeSizeBlock_004
 * @tc.desc: unsucc to get a max free size block
 * LfsGetSlotByFsmPageIdxAndSlotId return INT_ERR_LFS_INVALID_BLOCK_ID
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetMaxFreeSizeBlock_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    g_stub = setStubC((void *)LfsGetSlotByFsmPageIdxAndSlotId, (void *)LfsGetSlotByFsmPageIdxAndSlotIdMock2);
    ret = LfsGetMaxFreeSizeBlock(&fsmRunCtx, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsGetMaxFreeSizeBlock_005
 * @tc.desc: succ to get a max free size block with a new slot from fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsGetMaxFreeSizeBlock_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    bool isNewBlock = false;
    PageAddrInfoT addrInfo;
    ret = LfsGetMaxFreeSizeBlock(&fsmRunCtx, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(isNewBlock, true);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, addrInfo.pageHead->addr, false);  // leave the data page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsTryGetSpecificBlock_001
 * @tc.desc: succ try to get a slot in mgr page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 0, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(addrInfo.pageAddr.pageId, addr.pageId);
    EXPECT_EQ(addrInfo.pageAddr.blockId, addr.blockId);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsTryGetSpecificBlock_002
 * @tc.desc: try to get a slot which is freed
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 0, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_BLOCK_HAS_RELEASED);  // the slot is freed

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsTryGetSpecificBlock_003
 * @tc.desc: succ try to get a slot in fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    blockInfo.freeSize = 30 * UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, FSM_SLOT_NUM_IN_MGR, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(addrInfo.pageAddr.blockId, addr.blockId);
    EXPECT_EQ(addrInfo.pageAddr.pageId, addr.pageId);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsTryGetSpecificBlock_004
 * @tc.desc: try to get a slot which slot id is not valid
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 1, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);

    RedoLogEnd(redoCtx, true);
}

StatusInter LfsGetSpecificSlotByFsmPageIdxAndSlotIdMock(
    PageMgrT *pageMgr, LfsMgrT *mgr, uint32_t fsmPageIdx, uint32_t slotId, FsmListNodeT **slot)
{
    return INTERNAL_ERROR_INTER;
}
/**
 * @tc.name: LfsTryGetSpecificBlock_005
 * @tc.desc: unsucc to try to get a slot because LfsGetSpecificSlotByFsmPageIdxAndSlotId is mocked
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    PageAddrT addr = {0};
    setStubC((void *)LfsGetSpecificSlotByFsmPageIdxAndSlotId, (void *)LfsGetSpecificSlotByFsmPageIdxAndSlotIdMock);
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 0, &addr);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsTryGetSpecificBlock_006
 * @tc.desc: succ try to get a slot in fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsTryGetSpecificBlock_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // return the page to the fsm
    BlockInfoT blockInfo = {
        .freeSize = UT_REQUIRESIZE * 30,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(addrInfo.pageAddr.pageId, addr.pageId);
    EXPECT_EQ(addrInfo.pageAddr.blockId, addr.blockId);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsReleaseAllFreeBlock_001
 * @tc.desc: succ to release all free block
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllFreeBlock_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo[FSM_SLOT_NUM_IN_MGR + 1] = {0};  // 保存所有mgr页中slot对应的数据页 + 1个fsm页中slot
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo[i], NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
    }

    // 所有的slot都挂载最高级链表
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo[i].pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    ret = LfsReleaseAllFreeBlock(&fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsReleaseAllFreeBlock_002
 * @tc.desc: unsucc to release all free block because LfsGetSlotByFsmPageIdxAndSlotId is mocked
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllFreeBlock_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo[FSM_SLOT_NUM_IN_MGR + 1] = {0};  // 保存所有mgr页中slot对应的数据页 + 1个fsm页中slot
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo[i], NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
    }

    // 所有的slot都挂在最高级链表
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo[i].pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    RedoLogBegin(redoCtx);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    setStubC((void *)LfsGetSlotByFsmPageIdxAndSlotId, (void *)LfsGetSlotByFsmPageIdxAndSlotIdMock2);
    ret = LfsReleaseAllFreeBlock(&fsmRunCtx);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);
}

static void UtSetFreeForFsmExtension(FsmRunCtxT *fsmRunCtx)
{
    LfsMgrT *fsmMgr = NULL;
    (void)FsmRunCtxGetMgr(fsmRunCtx, true, &fsmMgr);
    DB_ASSERT(fsmMgr != NULL);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;
    fsmMgr->maxBlockCnt = fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR;
    fsmMgr->fsmPageCnt = fsmPageIdNumsInMgr;
    fsmMgr->usedPageCnt = UT_REQUIRESIZE;
    fsmMgr->releasedPageCnt = fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR - fsmMgr->usedPageCnt;
    fsmMgr->freePageCnt = 0;
    LfsRedoForMgrPageCnt(fsmMgr);
    FsmRunCtxLeaveMgr(fsmRunCtx, true);
}

/**
 * @tc.name: LfsReleaseAllFreeBlock_003
 * @tc.desc: ssucc to release all free block for fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllFreeBlock_003)
{
    // create and get lfs mgr

    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtSetFreeForFsmExtension(&fsmRunCtx);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    // release the slot
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // ReleaseAllFreeBlock
    ret = LfsReleaseAllFreeBlock(&fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_001
 * @tc.desc: succ to release all block
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_002
 * @tc.desc: succ to release all block if slot is freed
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo[FSM_SLOT_NUM_IN_MGR + 1] = {0};
    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo[i], NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
    }
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo[i].pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_003
 * @tc.desc: unsucc to release all block because LfsGetSlotByFsmPageIdxAndSlotId return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    setStubC((void *)LfsGetSlotByFsmPageIdxAndSlotId, (void *)LfsGetSlotByFsmPageIdxAndSlotIdMock2);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_004
 * @tc.desc: unsucc to release all block because SeFreePage return INTERNAL_ERROR_INTER
 * LfsReleaseAllBlock -> LfsReleaseUpperPageWithRedo -> LfsReleaseUpperPage -> SeFreePage
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    setStubC((void *)SeFreePage, (void *)SeFreePageMock);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_005
 * @tc.desc: unsucc because FsmGetMgr is mocked to return INTERNAL_ERROR_INTER in fun LfsReleaseAllBlock
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    g_callTimes = 0;
    g_stubTimes = 1;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_006
 * @tc.desc: unsucc because FsmGetMgr return INTERNAL_ERROR_INTER in LfsReleaseUpperPageWithRedo
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    g_callTimes = 0;
    g_stubTimes = 2;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsReleaseAllBlock_007
 * @tc.desc: unsucc because LfsGetFsmPageByCache return INTERNAL_ERROR_INTER in LfsReleaseAllBlockInFsmPage
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    g_stub = setStubC((void *)LfsGetFsmPageByCache, (void *)LfsGetFsmPageByCacheMock);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

StatusInter LfsReleaseUpperPageWithRedoMockTimes(
    PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx, uint32_t slotId)
{
    g_callTimes++;
    clearStub(g_stub);
    StatusInter ret = LfsReleaseUpperPageWithRedo(pageMgr, fsmAddr, offset, fsmPageIdx, slotId);
    g_stub = setStubC((void *)LfsReleaseUpperPageWithRedo, (void *)LfsReleaseUpperPageWithRedoMockTimes);
    if (g_callTimes == g_stubTimes) {
        ret = INTERNAL_ERROR_INTER;
    }
    return ret;
}

/**
 * @tc.name: LfsReleaseAllBlock_008
 * @tc.desc: unsucc because LfsReleaseUpperPageWithRedo return INTERNAL_ERROR_INTER in LfsReleaseAllBlockInFsmPage
 * ps: LfsReleaseUpperPageWithRedoMock call LfsReleaseUpperPageWithRedo succ, but set the ret == INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsReleaseAllBlock_008)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo;
    BlockInfoT blockInfo = {
        .freeSize = 0,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR + 1; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    RedoLogEnd(redoCtx, true);

    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    g_callTimes = 0;
    g_stubTimes = FSM_SLOT_NUM_IN_MGR + 1;  // mgr页中的slot日志正常记录
    g_stub = setStubC((void *)LfsReleaseUpperPageWithRedo, (void *)LfsReleaseUpperPageWithRedoMockTimes);
    ret = LfsReleaseAllBlock(fsmRunCtx.pageMgr, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrInitMemVars_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrInitMemVars_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    LfsMgrInitMemVars(fsmMgr);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrResetMemVars_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrResetMemVars_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    LfsMgrMemFieldT memData = {0};
    LfsMgrResetMemVars(fsmMgr, &memData);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsMgrRestoreMemVars_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsMgrRestoreMemVars_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    LfsMgrMemFieldT memData = {0};
    LfsMgrRestoreMemVars(fsmMgr, &memData);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsGetFsmStat_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFsmStat_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    FsmStat fsmStat = {0};
    LfsGetFsmStat(fsmMgr, &fsmStat);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsGetFragmentedBlock_001
 * 由于链表上没有slot导致没有找到可以去碎片化的页
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_NO_FRAG_BLOCK);
}

/**
 * @tc.name: LfsGetFragmentedBlock_002
 * 由于链表上只有一个slot而导致没有找到可以去碎片化的页
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
}

/**
 * @tc.name: LfsGetFragmentedBlock_003
 * success to get a fragmented block
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    blockInfo.freeSize = UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
}

/**
 * @tc.name: LfsGetFragmentedBlock_004
 * unsuccess to get a fragmented block by an old slot in fsm page, and the next slot is also in fsm page
 * because LfsGetFsmPageByCache is mock to return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    for (uint32_t i = 0; i < 2; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
        blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);

    setStubC((void *)LfsGetFsmPageByCache, (void *)LfsGetFsmPageByCacheMock);
    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
}

bool LfsIsGetFragmentedSlotIdMock(PageMgrT *pageMgr, LfsMgrT *mgr, int32_t fragListId, uint32_t lastFsmListIdx,
    uint32_t *fsmPageIdx, uint32_t *slotId)
{
    return false;
}

/**
 * @tc.name: LfsGetFragmentedBlock_005
 * LfsIsGetFragmentedSlotId return false
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = fsmMgr->cfgInfo.availSize / 2;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);

    setStubC((void *)LfsIsGetFragmentedSlotId, (void *)LfsIsGetFragmentedSlotIdMock);
    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
}

/**
 * @tc.name: LfsGetFragmentedBlock_006
 * LfsIsGetFragmentedSlotId 中 for 循环 覆盖率
 * 场景构造：链上有三个slot，高级链表两个，其中第一个不可搬迁，第二个可搬迁，低级链表一个
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = 0,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);

    blockInfo.maxRowSize = fsmMgr->cfgInfo.availSize / 8;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)3, fsmMgr->maxBlockCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    blockInfo.freeSize = UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
}

/**
 * @tc.name: LfsGetFragmentedBlock_007
 * mgr->fragmentedInfo.lastFragmentedPageId == slot->dataPageId in fun LfsCalcFragBlockForMove
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = 0,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);

    blockInfo.maxRowSize = fsmMgr->cfgInfo.availSize / 8;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)3, fsmMgr->maxBlockCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    blockInfo.freeSize = UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_NO_FRAG_BLOCK);  // 再次寻找可搬迁页时跳过上次可搬迁页
}

/**
 * @tc.name: LfsGetFragmentedBlock_008
 * unsuccess to get a fragmented block because LfsGetAllocListId return err in fun LfsCalcFragBlockForMove
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_008)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = FSM_INVALID_MAX_ROW_SIZE,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    ret = LfsGetAvailBlock(&fsmRunCtx, fsmMgr->cfgInfo.availSize, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)2, fsmMgr->maxBlockCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    blockInfo.freeSize = UT_REQUIRESIZE;
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
}

/**
 * @tc.name: LfsGetFragmentedBlock_009
 * 当某个页要搬迁到更高级链表上，该场景为出错场景
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetFragmentedBlock_009)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);

    BlockInfoT blockInfo = {
        .freeSize = fsmMgr->cfgInfo.availSize / 2,
        .maxRowSize = fsmMgr->cfgInfo.availSize / 2,
        .relink = true,
        .leavePage = true,
    };
    ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);

    PageAddrT addr = {0};
    ret = LfsGetFragmentedBlock(&fsmRunCtx, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_NO_FRAG_BLOCK);
}

/**
 * @tc.name: LfsGetBlockCnt_001
 * success to get a fragmented block
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetBlockCnt_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    uint32_t blockCnt = 0;
    ret = LfsGetBlockCnt(&fsmRunCtx, &blockCnt);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(blockCnt, (uint32_t)0);
}

/**
 * @tc.name: LfsGetRealBlockCnt_001
 * success to get a fragmented block
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetRealBlockCnt_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    uint32_t realBlockCnt = 0u;
    ret = LfsGetRealBlockCnt(&fsmRunCtx, &realBlockCnt);
    EXPECT_EQ(STATUS_OK_INTER, ret);
    EXPECT_EQ(realBlockCnt, (uint32_t)0);
}

/**
 * @tc.name: FsmLatchMember_001
 * FsmLatchMember is static fun in se_lfsmgr.c, hence, we call this fun by LfsGetAvailBlock and the 'write' para is true
 * fsmRunCtx->enableLatch == true, write == true
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmLatchMember_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableLatch = true;

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: FsmLatchMember_002
 * FsmLatchMember is static fun in se_lfsmgr.c, hence, we call this fun by LfsTryGetSpecificBlock
 * and the 'write' para is false
 * fsmRunCtx->enableLatch == true, write == false
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmLatchMember_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableLatch = true;

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    PageAddrT addr = {0};
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 0, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);
}

void DbRWSpinWLockWithSessionMock(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    return;
}
void DbRWSpinWUnlockWithSessionMock(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
    return;
}
/**
 * @tc.name: FsmLatchMember_003
 * FsmLatchMember is static fun in se_lfsmgr.c, hence, we call this fun by LfsGetAvailBlock and the 'write' para is true
 * fsmRunCtx->enableLatch == true, fsmRunCtx->directAccess == true, write == true
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmLatchMember_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableLatch = true;
    fsmRunCtxCfg.directAccess = true;

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    setStubC((void *)DbRWSpinWLockWithSession, (void *)DbRWSpinWLockWithSessionMock);
    setStubC((void *)DbRWSpinWUnlockWithSession, (void *)DbRWSpinWUnlockWithSessionMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);
}

void DbRWSpinRLockWithSessionMock(
    DbSessionCtxT *sessionCtx, DbLatchT *latch, const ShmemPtrT *latchShmAddr, LatchAddrTypeE latchType)
{
    return;
}
void DbRWSpinRUnlockWithSessionMock(DbSessionCtxT *sessionCtx, DbLatchT *latch)
{
    return;
}
/**
 * @tc.name: FsmLatchMember_004
 * FsmLatchMember is static fun in se_lfsmgr.c, hence, we call this fun by LfsTryGetSpecificBlock
 * and the 'write' para is false
 * fsmRunCtx->enableLatch == true, fsmRunCtx->directAccess == true, write == false
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmLatchMember_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableLatch = true;
    fsmRunCtxCfg.directAccess = true;

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    PageAddrT addr = {0};
    setStubC((void *)DbRWSpinRLockWithSession, (void *)DbRWSpinRLockWithSessionMock);
    setStubC((void *)DbRWSpinRUnlockWithSession, (void *)DbRWSpinRUnlockWithSessionMock);
    ret = LfsTryGetSpecificBlock(&fsmRunCtx, 0, &addr);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);
}

/**
 * @tc.name: LfsGetSpecificSlotByFsmPageIdxAndSlotId_001
 * unsucc if LfsGetFsmPage return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetSpecificSlotByFsmPageIdxAndSlotId_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableLatch = true;
    fsmRunCtxCfg.directAccess = true;

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    g_callTimes = 0;
    g_stubTimes = 1;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);

    FsmListNodeT *slot;
    ret = LfsGetSpecificSlotByFsmPageIdxAndSlotId(fsmRunCtx.pageMgr, fsmMgr, 0, 0, &slot);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_FSM_PAGE_ID);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: LfsGetSpecificSlotByFsmPageIdxAndSlotId_002
 * unsucc if LfsGetFsmSlot return INTERNAL_ERROR_INTER
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, LfsGetSpecificSlotByFsmPageIdxAndSlotId_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);

    FsmListNodeT *slot;
    ret = LfsGetSpecificSlotByFsmPageIdxAndSlotId(fsmRunCtx.pageMgr, fsmMgr, 0, 1, &slot);
    EXPECT_EQ(ret, INT_ERR_LFS_INVALID_BLOCK_ID);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: FsmAllocRunCtx_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmAllocRunCtx_001)
{
    FsmRunCtxT *fsmRunCtx = NULL;
    StatusInter ret = FsmAllocRunCtx(seRunCtx, &fsmRunCtx);
    EXPECT_EQ(ret, 0);
    FsmFreeRunCtx(&fsmRunCtx);
}

static void *DbDynMemCtxAllocMock(void *ctx, size_t size)
{
    return NULL;
}

/**
 * @tc.name: FsmAllocRunCtx_002
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmAllocRunCtx_002)
{
    FsmRunCtxT *fsmRunCtx = NULL;
    g_stub = setStubC((void *)DbDynMemCtxAlloc, (void *)DbDynMemCtxAllocMock);
    StatusInter ret = FsmAllocRunCtx(seRunCtx, &fsmRunCtx);
    EXPECT_EQ(ret, OUT_OF_MEMORY_INTER);
    clearStub(g_stub);
    FsmFreeRunCtx(&fsmRunCtx);
}

/**
 * @tc.name: FsmInitRunCtx_001
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmInitRunCtx_001)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableCache = true;
    UtLeaveLfsMgr(fsmRunCtxCfg.pageMgr, fsmRunCtxCfg.fsmAddr, true);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
}

/**
 * @tc.name: FsmInitRunCtx_002
 * unsucc FsmGetMgr in FsmInitRunCtx
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, FsmInitRunCtx_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);
    fsmRunCtxCfg.enableCache = true;
    UtLeaveLfsMgr(fsmRunCtxCfg.pageMgr, fsmRunCtxCfg.fsmAddr, true);

    // get lfs run ctx
    g_callTimes = 0;
    g_stubTimes = 1;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
}

/**
 * @tc.name: pageLeak_001
 * @tc.desc: check page leak for creating fsm mgr
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_001)
{
    uint32_t spaceIdx;
    SeGetSpaceIdByType(SPACE_TYPE_USER_DEFAULT, DbGetProcGlobalId(), &spaceIdx);
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, spaceIdx);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtxCfg.pageMgr, fsmRunCtxCfg.fsmAddr, true);

    uint32_t allocedPageNum = 1;  // 创建流程会使用一个页
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_002
 * @tc.desc: check page leak for alloc data page succ
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_002)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    EXPECT_EQ(true, isNewBlock);                  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->maxBlockCnt);  // only one slot
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);   // no fsm page because the new slot in mgr page

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    // leave the data page
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    RedoLogEnd(redoCtx, true);

    uint32_t allocedPageNum = 1;  // 新申请一个页
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_003
 * @tc.desc: check page leak for alloc data page succ while get page unsucc, the page need to free
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_003)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    g_callTimes = 0;
    g_stubTimes = 5;  // 为了 alloc data page 后的get page unsucc
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    // get a data page by a new slot in mgr
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ((uint32_t)0, fsmMgr->maxBlockCnt);

    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    RedoLogEnd(redoCtx, true);

    uint32_t allockedPageNum = 1;  // 新申请的页个数
    uint32_t freedPageNum = 1;     // free的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_004
 * @tc.desc: check page leak for alloc fsm page and data page succ
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_004)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)1, fsmMgr->fsmPageCnt);
    EXPECT_EQ((uint32_t)FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    uint32_t allockedPageNum = 2;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_005
 * @tc.desc: check page leak for alloc fsm page succ while get fsm page unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_005)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    g_callTimes = 0;
    g_stubTimes = 5;  // get fsm page unsucc
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);

    uint32_t allockedPageNum = 1;  // 新申请的页个数
    uint32_t freePageNum = 1;      // 归还的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_006
 * @tc.desc: check page leak for alloc fsm page succ while alloc data page unsucc
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_006)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get new blocks by all the slots in mgr, the num is FSM_SLOT_NUM_IN_MGR
    RedoLogBegin(redoCtx);
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    for (uint32_t i = 0; i < FSM_SLOT_NUM_IN_MGR; i++) {
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
    }

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    setStubC((void *)LfsGetNewUpperPage, (void *)LfsGetNewUpperPageMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ((uint32_t)0, fsmMgr->fsmPageCnt);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogEnd(redoCtx, true);

    uint32_t allockedPageNum = 1;  // 新申请的页个数
    uint32_t freePageNum = 1;      // 归还的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_007
 * @tc.desc: alloc data page until using two fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_007)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    uint32_t dataPageNum = FSM_SLOT_NUM_IN_MGR + fsmMgr->maxSlotPerFsmPage + 1;  // 需要满足申请3个fsm page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    for (uint32_t i = 0; i < dataPageNum; i++) {
        RedoLogBegin(redoCtx);
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEnd(redoCtx, true);
    }

    uint32_t fsmPageNum = 2;
    uint32_t allocedPageNum = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space) - beforeAllocedPageHwm;
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(allocedPageNum, dataPageNum + fsmPageNum);  // 上述流程申请了allocedPageNum个页
    EXPECT_EQ(afterFreePageNum, beforeFreePageNum);
}

static StatusInter UtDropFsm(PageMgrT *pageMgr, PageIdT pageId)
{
    if (!DbIsPageIdValid(pageId)) {
        return STATUS_OK_INTER;
    }

    HeapT *heap = NULL;
    StatusInter ret = SeGetHeap(pageMgr, pageId, false, &heap);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "head addr novalid when drop, [deviceId: %" PRIu32 " blockId: %" PRIu32 "]", pageId.deviceId,
            pageId.blockId);
        return ret;
    }
    uint32_t offset = heap->fsm.cfgInfo.mgrOffset;
    SeLeaveHeap(pageMgr, pageId, false);

    ret = LfsReleaseAllBlock(pageMgr, pageId, offset);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "free all page unsucc, lfs mgr = (%" PRIu32 ", %" PRIu32 ")", pageId.deviceId, pageId.blockId);
        return ret;
    }

    ret = LfsMgrDestroy(pageMgr, pageId, offset);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Destroy lfs mgr = (%" PRIu32 ", %" PRIu32 ")", pageId.deviceId, pageId.blockId);
        return ret;
    }

    return STATUS_OK_INTER;
}

/**
 * @tc.name: pageLeak_008
 * @tc.desc: create and drop all data page and fsm page
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_008)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    uint32_t dataPageNum = FSM_SLOT_NUM_IN_MGR + fsmMgr->maxSlotPerFsmPage + 1;  // 需要满足申请2个fsm page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    for (uint32_t i = 0; i < dataPageNum; i++) {
        RedoLogBegin(redoCtx);
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEnd(redoCtx, true);
    }

    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    uint32_t fsmPageNum = 2;
    uint32_t allocedPageNum = dataPageNum + fsmPageNum;
    uint32_t freePageNum = dataPageNum + fsmPageNum;

    // 上述流程先申请allocedPageNum个页，后归还freePageNum个页
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_009
 * @tc.desc:
 * 1. alloc data page until using two fsm page
 * 2. destroy unsucc because free slot in mgr unsucc
 * 3. destroy again succ
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_009)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    uint32_t dataPageNum = FSM_SLOT_NUM_IN_MGR + fsmMgr->maxSlotPerFsmPage + 1;  // 需要满足申请2个fsm page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    for (uint32_t i = 0; i < dataPageNum; i++) {
        RedoLogBegin(redoCtx);
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEnd(redoCtx, true);
    }

    // 释放mgr中的slot对应的数据页，中间失败
    g_callTimes = 0;
    g_stubTimes = FSM_SLOT_NUM_IN_MGR / 2;
    g_stub = setStubC((void *)LfsReleaseUpperPageWithRedo, (void *)LfsReleaseUpperPageWithRedoMockTimes);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    // 再次drop成功
    clearStub(g_stub);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    uint32_t fsmPageNum = 2;
    uint32_t allocedPageNum = dataPageNum + fsmPageNum;
    uint32_t freePageNum = dataPageNum + fsmPageNum;

    // 上述流程先申请allocedPageNum个页，后归还freePageNum个页
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_010
 * @tc.desc:
 * 1. alloc data page until using two fsm page
 * 2. destroy unsucc because free slot in fsm page unsucc
 * 3. destroy again succ
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_010)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t dataPageNum = FSM_SLOT_NUM_IN_MGR + maxSlotPerFsmPage + 1;  // 需要满足申请2个fsm page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    for (uint32_t i = 0; i < dataPageNum; i++) {
        RedoLogBegin(redoCtx);
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEnd(redoCtx, true);
    }

    // 释放fsm page中的slot对应的数据页，中间失败
    g_callTimes = 0;
    g_stubTimes = FSM_SLOT_NUM_IN_MGR + maxSlotPerFsmPage / 2;
    g_stub = setStubC((void *)LfsReleaseUpperPageWithRedo, (void *)LfsReleaseUpperPageWithRedoMockTimes);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    // 再次drop成功
    clearStub(g_stub);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    uint32_t fsmPageNum = 2;
    uint32_t allocedPageNum = dataPageNum + fsmPageNum;
    uint32_t freePageNum = dataPageNum + fsmPageNum;

    // 上述流程先申请allocedPageNum个页，后归还freePageNum个页
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

StatusInter LfsFreeFsmPageMockErrForTimes(PageMgrT *pageMgr, PageIdT fsmAddr, uint32_t offset, uint32_t fsmPageIdx)
{
    g_callTimes++;
    if (g_callTimes < g_stubTimes) {
        clearStub(g_stub);
        StatusInter ret = LfsFreeFsmPage(pageMgr, fsmAddr, offset, fsmPageIdx);
        g_stub = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockErrForTimes);
        return ret;
    } else {
        return INTERNAL_ERROR_INTER;
    }
}
/**
 * @tc.name: pageLeak_011
 * @tc.desc:
 * 1. alloc data page until using two fsm page
 * 2. destroy unsucc because free the second fsm page unsucc
 * 3. destroy again succ
 * @tc.author: zhouwenkang
 */
TEST_F(LfsReliabilityUt, pageLeak_011)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    BlockInfoT blockInfo = {0};
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t dataPageNum = FSM_SLOT_NUM_IN_MGR + maxSlotPerFsmPage + 1;  // 需要满足申请2个fsm page
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);
    for (uint32_t i = 0; i < dataPageNum; i++) {
        RedoLogBegin(redoCtx);
        ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        EXPECT_EQ(true, isNewBlock);
        blockInfo.freeSize = 0;
        blockInfo.maxRowSize = FSM_INVALID_MAX_ROW_SIZE;
        blockInfo.relink = true, blockInfo.leavePage = true;
        ret = LfsSetBlockFreeSpace(&fsmRunCtx, (uint8_t *)addrInfo.pageHead, blockInfo);
        EXPECT_EQ(ret, STATUS_OK_INTER);
        RedoLogEnd(redoCtx, true);
    }

    // 释放fsm page 中间失败
    g_callTimes = 0;
    g_stubTimes = 2;
    g_stub = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockErrForTimes);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);

    // 再次drop成功
    clearStub(g_stub);
    ret = UtDropFsm(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    uint32_t fsmPageNum = 2;
    uint32_t allocedPageNum = dataPageNum + fsmPageNum;
    uint32_t freePageNum = dataPageNum + fsmPageNum;

    // 上述流程先申请allocedPageNum个页，后归还freePageNum个页
    EXPECT_EQ(beforeAllocedPageHwm + allocedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freePageNum, afterFreePageNum);
}

/**
 * @tc.name: pageLeak_012
 * @tc.desc: check page leak for alloc fsm page and data page succ in fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_012)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_013
 * @tc.desc: check page leak for alloc fsm page and data page unsucc in fsm extension.
             fail in allocating lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_013)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 4;
    g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageTimesMock);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeAllocPage, (void *)SeAllocPageRelyMock);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)0, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);

    uint32_t allockedPageNum = 3;  // 新申请的页个数
    uint32_t freedPageNum = 2;     // 释放的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    allockedPageNum = 4;  // 新申请的页个数
    freedPageNum = 0;     // 释放的页个数
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_014
 * @tc.desc: check page leak for alloc fsm page and data page unsucc in fsm extension.
             fail in getting lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_014)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a data page by a new slot in mgr, create lv2 and lv1 fsm page for the new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    g_callTimes = 0;
    g_stubTimes = 17;
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageMockTimes);
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeGetPage, (void *)SeGetPageRelyMock);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t freedPageNum = 2;     // 释放的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);

    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);  // new slot
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    allockedPageNum = 4;  // 新申请的页个数
    freedPageNum = 0;     // 释放的页个数
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_015
 * @tc.desc: check page leak for free all fsm page and data page succ in fsm extension
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_015)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;        // 新申请的页个数
    uint32_t freedPageNum = 3;  // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_016
 * @tc.desc: check page leak for free all fsm page and data page unsucc in fsm extension.
             fail in freeing lv1 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_016)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    g_callTimes = 0;
    g_stubTimes = 2;
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;        // 新申请的页个数
    uint32_t freedPageNum = 1;  // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;  // 新申请的页个数
    freedPageNum = 3;     // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_017
 * @tc.desc: check page leak for free all fsm page and data page unsucc in fsm extension.
             fail in freeing lv2 fsm page
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_017)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    g_callTimes = 0;
    g_stubTimes = 3;
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageMockTimes);
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, INTERNAL_ERROR_INTER);
    clearStub(g_stub);
    g_stub = setStubC((void *)SeFreePage, (void *)SeFreePageRelyMock);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;        // 新申请的页个数
    uint32_t freedPageNum = 2;  // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;  // 新申请的页个数
    freedPageNum = 3;     // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);
}

/**
 * @tc.name: pageLeak_018
 * @tc.desc: check page leak for free all fsm page and data page succ in fsm extension. reset twice
 * @tc.author: chentingrong
 */
TEST_F(LfsReliabilityUt, pageLeak_018)
{
    // create and get lfs mgr
    LfsMgrT *fsmMgr;
    StatusInter ret = UtGetLfsMgr(&fsmMgr);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    uint32_t maxSlotPerFsmPage = fsmMgr->maxSlotPerFsmPage;
    uint32_t fsmPageIdNumsInMgr = fsmMgr->fsmPageIdNumsInMgr;

    // get lfs run ctx cfg
    FsmRunCtxCfgT fsmRunCtxCfg;
    Ut_InitFsmRunCtxCfg(&fsmRunCtxCfg, fsmMgr->cfgInfo.mgrPageId, fsmMgr->cfgInfo.mgrOffset);

    // get lfs run ctx
    FsmRunCtxT fsmRunCtx = {0};
    ret = FsmInitRunCtx(&fsmRunCtxCfg, &fsmRunCtx);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    // set lv0 pages full
    RedoLogBegin(redoCtx);
    UtUpdateInfoForFsmExtension(&fsmRunCtx);

    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, fsmMgr->cfgInfo.tableSpaceIndex);
    uint32_t beforeAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t beforeFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);

    // get a new block by a new slot in a new fsm page
    bool isNewBlock;
    PageAddrInfoT addrInfo = {0};
    ret = LfsGetAvailBlock(&fsmRunCtx, UT_REQUIRESIZE, &isNewBlock, &addrInfo, NULL);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    EXPECT_EQ(true, isNewBlock);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr * maxSlotPerFsmPage + FSM_SLOT_NUM_IN_MGR + 1, fsmMgr->maxBlockCnt);
    EXPECT_EQ((uint32_t)fsmPageIdNumsInMgr + 1, fsmMgr->fsmPageCnt);  // no fsm page because the new slot in mgr page
    EXPECT_EQ((uint32_t)1, fsmMgr->lv1TableCnt);
    EXPECT_EQ((uint32_t)1, fsmMgr->lv2TableCnt);
    SeLeavePage(fsmRunCtx.pageMgr, DeserializePageId(fsmRunCtx.pageMgr, addrInfo.pageAddr.pageId), true);

    uint32_t allockedPageNum = 4;  // 新申请的页个数
    uint32_t afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    uint32_t afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum, afterFreePageNum);

    RedoLogEnd(redoCtx, true);

    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    g_stubForFreeFsmPage = setStubC((void *)LfsFreeFsmPage, (void *)LfsFreeFsmPageMockForTimes);
    ret = LfsFreeAllFsmPage(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;        // 新申请的页个数
    uint32_t freedPageNum = 3;  // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);

    // reenter reset
    g_callTimesForFreeFsmPage = 0;
    g_stubTimesForFreeFsmPage = fsmPageIdNumsInMgr + 1;
    ret = LfsMgrReset(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, fsmMgr->cfgInfo.mgrOffset);
    EXPECT_EQ(ret, STATUS_OK_INTER);
    UtLeaveLfsMgr(fsmRunCtx.pageMgr, fsmRunCtx.fsmMgrAddr, true);

    RedoLogBegin(redoCtx);
    allockedPageNum = 4;  // 新申请的页个数
    freedPageNum = 3;     // 释放的页个数，这里只reset，没有释放数据页
    afterAllocedPageHwm = UtSpaceUtil::SpaceGetAllocedPageHwm(seIns, space);
    afterFreePageNum = UtSpaceUtil::SpaceGetFreePageNum(PersistenceUtTest::seIns, space);
    EXPECT_EQ(beforeAllocedPageHwm + allockedPageNum, afterAllocedPageHwm);
    EXPECT_EQ(beforeFreePageNum + freedPageNum, afterFreePageNum);
    RedoLogEnd(redoCtx, true);
}
