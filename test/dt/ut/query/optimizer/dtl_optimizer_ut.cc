/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: ut file for optimizer
 * Author: tangjiaxin
 * Create: 2022-08-18
 */

#include "gtest/gtest.h"
#include "cpl_optimizer.h"
#include "cpl_optimizer_cascades.h"
#include "cpl_ir_explain.h"
#include "query_ut_base.h"
#include "ut_ir_common.h"
#include "ut_catalog_base.h"
#include "cpl_ir_logical_op.h"
#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif

#ifdef __cplusplus
}
#endif

using namespace std;

DbMemCtxT *g_optimizerMemCtx = NULL;
static DbMemCtxT *oldMemCtx = NULL;

class DtlOptimizerUt : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        BaseInit();
        DbMemCtxArgsT args = {0};
        g_optimizerMemCtx =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        oldMemCtx = DbMemCtxSwitchTo((DbMemCtxT *)g_optimizerMemCtx);

        // 因为所有的元数据创建和删除都需要维护所属namespace的labelCount,同时ut中元数据的namespaceId设置都为0，
        // 因此在测试环境的初始化中创建了id为0的namespace
        DmNamespaceT *namespaceInfo = CreateNspWithId(0, g_optimizerMemCtx);
        EXPECT_EQ(GMERR_OK, CataCreateNamespace(namespaceInfo, NULL));
    }
    static void TearDownTestCase()
    {
        DbMemCtxSwitchBack(oldMemCtx, g_optimizerMemCtx);
        DbDeleteDynMemCtx(g_optimizerMemCtx);
        BaseUninit();
    }
    virtual void SetUp()
    {
        g_tableCnt = 0;
        g_udfCnt = 0;
    }
    virtual void TearDown()
    {
        for (uint32_t i = 0; i < g_tableCnt; i++) {
            Status ret = CataRemoveVertexLabelById(NULL, i);
            DB_ASSERT(ret == GMERR_OK);
        }
        for (uint32_t i = 0; i < g_udfCnt; i++) {
            Status ret = CataRemoveUdfById(NULL, i);
            DB_ASSERT(ret == GMERR_OK);
        }
    }
};

/*
       merge
*/
TEST_F(DtlOptimizerUt, IRPlanMerge)
{
    TableParamsT tableParams = (TableParamsT){0, DM_DTL_OUTPUT_LABEL, propNum, dataTypes, g_fields};
    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogMergeParam(tableParams));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyMergeParam(tableParams));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
       modify
        /
    workLabelScan
*/
TEST_F(DtlOptimizerUt, IRPlanWorkLabelScan)
{
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, propNum, dataTypes, g_fields};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};
    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))->add(logOpParams, LogWorkLabelScanParam(tableParams1));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))->add(phyOpParams, PhyWorkLabelScanParam(tableParams1));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("expect physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("real physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
        modify
         /
      project
        /
    workLabelScan
*/
TEST_F(DtlOptimizerUt, IRPlanProjectWorkLabelScan)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t colIndexPhy[] = {0, 1, 3};
    DbDataTypeE dts[] = {dataTypes[1], dataTypes[3]};
    char *flds[] = {g_field1, g_field3};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 2, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndexPhy) / sizeof(colIndexPhy[0]), colIndexPhy))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("expect physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("real physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

TEST_F(DtlOptimizerUt, IRPlan2SameProjects)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t colIndex2[] = {0, 1, 2};
    uint32_t colIndexPhy[] = {0, 1, 3};
    DbDataTypeE dts[] = {dataTypes[1], dataTypes[3]};
    char *flds[] = {g_field1, g_field3};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 2, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex2) / sizeof(colIndex2[0]), colIndex2))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndexPhy) / sizeof(colIndexPhy[0]), colIndexPhy))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("expect physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("real physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
             modify
               /
            project(count,1,3)
              /
         nestLoopJoin(count,1,2,3,4,5,8,9)
         /         \
   workLabelScan(count,1,2,3,4,5)  seqScan(count,7,2,3,8,9)
*/
TEST_F(DtlOptimizerUt, IRPlanNaturalJoin)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t colIndexPhy[] = {0, 1, 3};
    uint32_t joinKeyNum = 3;
    uint32_t leftKeyIds[] = {0, 2, 3};
    uint32_t leftKeyIdsPhy[] = {0, 2, 3};
    uint32_t rightKeyIds[] = {0, 2, 3};
    uint32_t rightKeyIdsPhy[] = {0, 2, 3};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    char *fields1[] = {g_count, g_field7, g_field2, g_field3, g_field8, g_field9};
    TableParamsT tableParams = (TableParamsT){2, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};
    TableParamsT tableParams2 = (TableParamsT){1, DM_DTL_INPUT_LABEL, propNum, dataTypes, fields1};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, LogScanParam(tableParams2));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndexPhy) / sizeof(colIndexPhy[0]), colIndexPhy))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIdsPhy, rightKeyIdsPhy, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, SeqScanParam(tableParams2));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
}

/*
             modify(count,1,2)
               /
            project(count,1,2)
              /
         nestLoopJoin(count,1,2,3,4,5,7,8,9)
         /                           \
   workLabelScan(count,1,2,3,4,5)  indexScan(count,1,2,7,8,9)
*/
TEST_F(DtlOptimizerUt, IRPlanNestLoopJoinIndexScan)
{
    uint32_t colIndex[] = {0, 1, 2};
    uint32_t joinKeyNum = 3;
    uint32_t leftKeyIds[] = {0, 1, 2};
    uint32_t rightKeyIds[] = {0, 1, 2};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[2]};
    char *flds[] = {g_count, g_field1, g_field2};
    char *fields1[] = {g_count, g_field1, g_field2, g_field7, g_field8, g_field9};
    TableParamsT tableParams = (TableParamsT){2, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};
    TableParamsT tableParams2 = (TableParamsT){1, DM_DTL_INPUT_LABEL, propNum, dataTypes, fields1};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(ELEMENT_COUNT(colIndex), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, LogScanParam(tableParams2));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(ELEMENT_COUNT(colIndex), colIndex))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, IndexScanParam(1, tableParams2));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
    printf("optimize plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
}

// Antijoin不参与join order优化
TEST_F(DtlOptimizerUt, IRPlanAntiJoinOptimize)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t joinKeyNum1 = 6;
    uint32_t leftKeyIds1[] = {0, 1, 2, 3, 4, 5};
    uint32_t rightKeyIds1[] = {0, 1, 2, 3, 4, 5};
    uint32_t joinKeyNum2 = 3;
    uint32_t leftKeyIds2[] = {0, 1, 3};
    uint32_t rightKeyIds2[] = {0, 1, 3};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    char *fields1[] = {g_count, g_field1, g_field7, g_field3, g_field8, g_field9};
    char *fileds2[] = {g_count, g_field1, g_field2, g_field3, g_field4, g_field5};

    TableParamsT tableParams = (TableParamsT){3, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};
    TableParamsT tableParams2 = (TableParamsT){1, DM_DTL_INPUT_LABEL, propNum, dataTypes, fields1};
    TableParamsT tableParams3 = (TableParamsT){2, DM_DTL_INPUT_LABEL, propNum, dataTypes, fileds2};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum1, leftKeyIds1, rightKeyIds1, IR_JOIN_ANTI))
        ->add(logOpParams, JoinParam(joinKeyNum2, leftKeyIds2, rightKeyIds2, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, LogScanParam(tableParams2))
        ->add(logOpParams, LogScanParam(tableParams3));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *opParams = CreateOpParams(g_optimizerMemCtx);
    opParams->add(opParams, PhyModifyParam(tableParams))
        ->add(opParams, ProjectParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(opParams, NestLoopJoinParam(joinKeyNum1, leftKeyIds1, rightKeyIds1, IR_JOIN_ANTI))
        ->add(opParams, NestLoopJoinParam(joinKeyNum2, leftKeyIds2, rightKeyIds2, IR_JOIN_INNER))
        ->add(opParams, PhyWorkLabelScanParam(tableParams1))
        ->add(opParams, IndexScanParam(0, tableParams2))
        ->add(opParams, IndexScanParam(1, tableParams3));
    IRPlanT *phyPlan = CreateIRPlan(opParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
       modify
        /
    extendProject
       /
    workLabelScan
*/
TEST_F(DtlOptimizerUt, IRPlanExtendProjectWorLabelScan)
{
    IRExtractIndexT colIndex[4];
    colIndex[0].type = IR_EXTRACT_INDEX;
    colIndex[0].value.index = 0;
    colIndex[1].type = IR_EXTRACT_INDEX;
    colIndex[1].value.index = 1;
    colIndex[2].type = IR_EXTRACT_INDEX;
    colIndex[2].value.index = 2;
    colIndex[3].type = IR_EXTRACT_CONST;
    colIndex[3].value.constValue.type = DB_DATATYPE_STRING;
    colIndex[3].value.constValue.value.strAddr = (void *)"CONST_VALUE";
    colIndex[3].value.constValue.value.length = strlen("CONST_VALUE") + 1;

    DbDataTypeE dts1[] = {dataTypes[0], dataTypes[2], DB_DATATYPE_INT32, dataTypes[5]};
    char *flds1[] = {g_count, g_field1, g_field3, g_field4};
    DbDataTypeE dts2[] = {dataTypes[0], dataTypes[2], DB_DATATYPE_INT32, dataTypes[5]};
    char *flds2[] = {g_count, g_field1, g_field3, g_field4};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 4, dts1, flds1};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, 4, dts2, flds2};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, LogExtendExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, PhyExtendProjectParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
       modify
        /
    extendProject
       /
     project
      /
    workLabelScan
*/
TEST_F(DtlOptimizerUt, IRPlanProjectExtendProjectWorLabelScan)
{
    uint32_t colIndex1[] = {0, 2, 1};
    IRExtractIndexT colIndex[4];
    colIndex[0].type = IR_EXTRACT_INDEX;
    colIndex[0].value.index = 0;
    colIndex[1].type = IR_EXTRACT_INDEX;
    colIndex[1].value.index = 1;
    colIndex[2].type = IR_EXTRACT_INDEX;
    colIndex[2].value.index = 2;
    colIndex[3].type = IR_EXTRACT_CONST;
    colIndex[3].value.constValue.type = DB_DATATYPE_STRING;
    colIndex[3].value.constValue.value.strAddr = (void *)"CONST_VALUE";
    colIndex[3].value.constValue.value.length = strlen("CONST_VALUE") + 1;

    IRExtractIndexT colIndex2[4];
    colIndex2[0].type = IR_EXTRACT_INDEX;
    colIndex2[0].value.index = 0;
    colIndex2[1].type = IR_EXTRACT_INDEX;
    colIndex2[1].value.index = 2;
    colIndex2[2].type = IR_EXTRACT_INDEX;
    colIndex2[2].value.index = 1;
    colIndex2[3].type = IR_EXTRACT_CONST;
    colIndex2[3].value.constValue.type = DB_DATATYPE_STRING;
    colIndex2[3].value.constValue.value.strAddr = (void *)"CONST_VALUE";
    colIndex2[3].value.constValue.value.length = strlen("CONST_VALUE") + 1;

    DbDataTypeE dts1[] = {dataTypes[0], dataTypes[1], dataTypes[2], DB_DATATYPE_INT32};
    char *flds1[] = {g_count, g_field1, g_field3, g_field4};
    DbDataTypeE dts2[] = {dataTypes[0], dataTypes[2], dataTypes[1], DB_DATATYPE_INT32};
    char *flds2[] = {g_count, g_field3, g_field1, g_field4};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 4, dts1, flds1};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, 4, dts2, flds2};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, LogExtendExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, ExtractParam(sizeof(colIndex1) / sizeof(colIndex1[0]), colIndex1))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, PhyExtendProjectParam(sizeof(colIndex2) / sizeof(colIndex2[0]), colIndex2))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
             modify
               /
            project
              /
         nestLoopJoin
         /          \
      scan    udf(combine type)
*/
TEST_F(DtlOptimizerUt, IRPlanNestLoopJoinFuncUdf1)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t joinKeyNum = 5;
    uint32_t leftKeyIds[] = {0, 1, 2, 3, 5};
    uint32_t rightKeyIds[] = {0, 1, 2, 3, 4};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};

    char funcName[] = "funcUdf2";
    uint32_t inputPropNum = 3;
    DmPropertySchemaT inputProps[] = {{0, dataTypes[1]}, {1, dataTypes[2]}, {2, dataTypes[3]}};
    inputProps[0].name = g_field1;
    inputProps[1].name = g_field2;
    inputProps[2].name = g_field3;
    inputProps[0].nameLen = strlen(g_field1) + 1;
    inputProps[1].nameLen = strlen(g_field2) + 1;
    inputProps[2].nameLen = strlen(g_field3) + 1;
    uint32_t outputPropNum = 1;
    DmPropertySchemaT outputProps[] = {{3, dataTypes[5]}};
    outputProps[0].name = g_field5;
    outputProps[0].nameLen = strlen(g_field5) + 1;

    uint32_t inputNum = 3;
    IRUdfInputParamT inputParams[inputNum];
    inputParams[0].type = IR_UDF_INDEX;
    inputParams[0].value.index = 0;
    inputParams[1].type = IR_UDF_INDEX;
    inputParams[1].value.index = 1;
    inputParams[2].type = IR_UDF_CONST;
    inputParams[2].value.constValue.type = DB_DATATYPE_BOOL;
    inputParams[2].value.constValue.value.boolValue = true;

    uint32_t tableNum = 2;
    TableParamsT tableParams2[] = {
        {0, DM_DTL_OUTPUT_LABEL, 3, dts, flds}, {1, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields}};
    UdfParamsT udfParams = {0, funcName, inputPropNum, inputProps, outputPropNum, outputProps, tableNum, tableParams2,
        inputNum, inputParams};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, FuncUdfParam(udfParams));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, FuncUdfParam(udfParams));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
             modify
               /
            project
              /
         nestLoopJoin
         /          \
      scan    udf(index type)
*/
TEST_F(DtlOptimizerUt, IRPlanNestLoopJoinFuncUdf2)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t joinKeyNum = 5;
    uint32_t leftKeyIds[] = {0, 1, 2, 3, 5};
    uint32_t rightKeyIds[] = {0, 1, 2, 3, 4};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};

    char funcName[] = "funcUdf2";
    uint32_t inputPropNum = 3;
    DmPropertySchemaT inputProps[] = {{0, dataTypes[1]}, {1, dataTypes[2]}, {2, dataTypes[3]}};
    inputProps[0].name = g_field1;
    inputProps[1].name = g_field2;
    inputProps[2].name = g_field3;
    inputProps[0].nameLen = strlen(g_field1) + 1;
    inputProps[1].nameLen = strlen(g_field2) + 1;
    inputProps[2].nameLen = strlen(g_field3) + 1;
    uint32_t outputPropNum = 1;
    DmPropertySchemaT outputProps[] = {{3, dataTypes[5]}};
    outputProps[0].name = g_field5;
    outputProps[0].nameLen = strlen(g_field5) + 1;

    uint32_t inputNum = 3;
    IRUdfInputParamT inputParams[inputNum];
    inputParams[0].type = IR_UDF_INDEX;
    inputParams[0].value.index = 0;
    inputParams[1].type = IR_UDF_INDEX;
    inputParams[1].value.index = 1;
    inputParams[2].type = IR_UDF_INDEX;
    inputParams[2].value.index = 2;

    uint32_t tableNum = 2;
    TableParamsT tableParams2[] = {
        {1, DM_DTL_OUTPUT_LABEL, 2, dts, flds}, {2, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields}};
    UdfParamsT udfParams = {0, funcName, inputPropNum, inputProps, outputPropNum, outputProps, tableNum, tableParams2,
        inputNum, inputParams};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, FuncUdfParam(udfParams));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, FuncUdfParam(udfParams));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
             modify
               /
            project
              /
         nestLoopJoin
         /          \
      scan    udf(const value type)
*/
TEST_F(DtlOptimizerUt, IRPlanNestLoopJoinFuncUdf3)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t joinKeyNum = 5;
    uint32_t leftKeyIds[] = {0, 1, 2, 3, 5};
    uint32_t rightKeyIds[] = {0, 1, 2, 3, 4};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    TableParamsT tableParams = (TableParamsT){1, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};

    char funcName[] = "funcUdf2";
    uint32_t inputPropNum = 3;
    DmPropertySchemaT inputProps[] = {{0, dataTypes[1]}, {1, dataTypes[2]}, {2, dataTypes[3]}};
    inputProps[0].name = g_field1;
    inputProps[1].name = g_field2;
    inputProps[2].name = g_field3;
    inputProps[0].nameLen = strlen(g_field1) + 1;
    inputProps[1].nameLen = strlen(g_field2) + 1;
    inputProps[2].nameLen = strlen(g_field3) + 1;
    uint32_t outputPropNum = 1;
    DmPropertySchemaT outputProps[] = {{3, dataTypes[5]}};
    outputProps[0].name = g_field5;
    outputProps[0].nameLen = strlen(g_field5) + 1;

    uint32_t inputNum = 3;
    IRUdfInputParamT inputParams[inputNum];
    inputParams[0].type = IR_UDF_CONST;
    inputParams[0].value.constValue.type = DB_DATATYPE_UINT64;
    inputParams[0].value.constValue.value.ulongValue = 1;

    inputParams[1].type = IR_UDF_CONST;
    inputParams[1].value.constValue.type = DB_DATATYPE_UINT32;
    inputParams[1].value.constValue.value.uintValue = 2;

    inputParams[2].type = IR_UDF_CONST;
    inputParams[2].value.constValue.type = DB_DATATYPE_BOOL;
    inputParams[2].value.constValue.value.boolValue = true;

    uint32_t tableNum = 2;
    TableParamsT tableParams2[] = {
        {1, DM_DTL_OUTPUT_LABEL, 2, dts, flds}, {2, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields}};
    UdfParamsT udfParams = {0, funcName, inputPropNum, inputProps, outputPropNum, outputProps, tableNum, tableParams2,
        inputNum, inputParams};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, FuncUdfParam(udfParams));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(sizeof(colIndex) / sizeof(colIndex[0]), colIndex))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, FuncUdfParam(udfParams));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);
    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}

/*
       agg
        \
        null
*/
TEST_F(DtlOptimizerUt, IRPlanAgg)
{
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};

    char funcName[] = "dtl_agg_func_agg1";
    DmPropertySchemaT inputProps[] = {{0, dataTypes[0]}, {1, dataTypes[1]}, {2, dataTypes[2]}};
    inputProps[0].name = g_field1;
    inputProps[1].name = g_field2;
    inputProps[2].name = g_field3;
    inputProps[0].nameLen = strlen(g_field1) + 1;
    inputProps[1].nameLen = strlen(g_field2) + 1;
    inputProps[2].nameLen = strlen(g_field3) + 1;
    DmPropertySchemaT outputProps[] = {{3, dataTypes[2]}};
    outputProps[0].name = g_field5;
    outputProps[0].nameLen = strlen(g_field5) + 1;
    IRUdfInputParamT inputParams[3];
    inputParams[0].type = IR_UDF_INDEX;
    inputParams[0].value.index = 0;
    inputParams[1].type = IR_UDF_INDEX;
    inputParams[1].value.index = 1;
    inputParams[2].type = IR_UDF_CONST;
    inputParams[2].value.constValue.type = DB_DATATYPE_STRING;
    inputParams[2].value.constValue.value.strAddr = (void *)"CONST_VALUE";
    inputParams[2].value.constValue.value.length = strlen("CONST_VALUE") + 1;
    TableParamsT tableParams2[] = {
        {0, DM_DTL_OUTPUT_LABEL, 2, dts, flds}, {1, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields}};
    UdfParamsT udfParams = {0, funcName, 3, inputProps, 1, outputProps, 2, tableParams2, 3, inputParams};
    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    logOpParams->add(logOpParams, AggUdfParam(udfParams))->add(logOpParams, ItemNullParam());
    IRPlanT *logPlan = CreateIRPlan(logOpParams);
    DmVertexLabelT *outLabel = IRUtCreateVertexLabel(g_optimizerMemCtx, &udfParams.tableParams[0]);
    ((OpUdfAggFuncT *)logPlan->root->op)->outLabel = outLabel;
    DmVertexLabelT *inLabel = IRUtCreateVertexLabel(g_optimizerMemCtx, &udfParams.tableParams[1]);
    ((OpUdfAggFuncT *)logPlan->root->op)->inLabel = inLabel;

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    IRPlanT *optimizedPlan = (IRPlanT *)(void *)DbListItem(&irPlans, 0);
    OpUdfAggFuncT *aggFunc = (OpUdfAggFuncT *)(void *)optimizedPlan->root->op;
    EXPECT_EQ(aggFunc->inLabelIndex, inLabel->metaVertexLabel->pkIndex);
    EXPECT_EQ(aggFunc->outLabelIndex, outLabel->metaVertexLabel->pkIndex);

    printf("logic plan:\n");
    IRPrintPlan(g_optimizerMemCtx, optimizedPlan);
}

/*
             modify(count,1,3)
               /
            project(count,1,3)
              /
         nestLoopJoin(count,1,2,3,4,5,7,8,9)
         /         \
   workLabelScan(count,1,2,3,4,5)                    filter(count,1,3,7,8,9)
                                                          /      \
                              indexScan(count,1,3,7,8,9)             itemBinary(==)
                                                                      /         \
                                                        itemAttr(propId:0)   itemConst(0)
*/
TEST_F(DtlOptimizerUt, IRPlanFilterIndexScan)
{
    uint32_t colIndex[] = {0, 1, 3};
    uint32_t joinKeyNum = 3;
    uint32_t leftKeyIds[] = {0, 1, 3};
    uint32_t rightKeyIds[] = {0, 1, 2};
    DbDataTypeE dts[] = {dataTypes[0], dataTypes[1], dataTypes[3]};
    char *flds[] = {g_count, g_field1, g_field3};
    char *fields1[] = {g_count, g_field1, g_field3, g_field7, g_field8, g_field9};
    DbDataTypeE dataTypes1[] = {DB_DATATYPE_UINT64, DB_DATATYPE_UINT32, DB_DATATYPE_FLOAT, DB_DATATYPE_FLOAT,
        DB_DATATYPE_STRING, DB_DATATYPE_STRING};
    TableParamsT tableParams = (TableParamsT){2, DM_DTL_OUTPUT_LABEL, 3, dts, flds};
    TableParamsT tableParams1 = (TableParamsT){0, DM_DTL_INPUT_LABEL, propNum, dataTypes, g_fields};
    TableParamsT tableParams2 = (TableParamsT){1, DM_DTL_INPUT_LABEL, propNum, dataTypes1, fields1};

    OpParamsT *logOpParams = CreateOpParams(g_optimizerMemCtx);
    DbValueT longValue = {.type = DB_DATATYPE_UINT64};
    longValue.value.ulongValue = 0;
    logOpParams->add(logOpParams, LogModifyParam(tableParams))
        ->add(logOpParams, ExtractParam(ELEMENT_COUNT(colIndex), colIndex))
        ->add(logOpParams, JoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(logOpParams, LogWorkLabelScanParam(tableParams1))
        ->add(logOpParams, SelectParam())
        ->add(logOpParams, LogScanParam(tableParams2))
        ->add(logOpParams, ItemBinaryParam(COMP_OP_EQ))
        ->add(logOpParams, ItemAttrParam(colIndex[1]))
        ->add(logOpParams, ItemConstParam(longValue));
    IRPlanT *logPlan = CreateIRPlan(logOpParams);

    printf("logical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)logPlan);
    printf("\n");

    OpParamsT *phyOpParams = CreateOpParams(g_optimizerMemCtx);
    phyOpParams->add(phyOpParams, PhyModifyParam(tableParams))
        ->add(phyOpParams, ProjectParam(ELEMENT_COUNT(colIndex), colIndex))
        ->add(phyOpParams, NestLoopJoinParam(joinKeyNum, leftKeyIds, rightKeyIds, IR_JOIN_INNER))
        ->add(phyOpParams, PhyWorkLabelScanParam(tableParams1))
        ->add(phyOpParams, IndexFilterScanParam(tableParams2))
        ->add(phyOpParams, ItemBinaryParam(COMP_OP_EQ))
        ->add(phyOpParams, ItemAttrParam(colIndex[1]))
        ->add(phyOpParams, ItemConstParam(longValue));
    IRPlanT *phyPlan = CreateIRPlan(phyOpParams);

    printf("physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)phyPlan);
    printf("\n");

    DbListT irPlans = {0};
    DbCreateList(&irPlans, sizeof(IRPlanT), g_optimizerMemCtx);
    EXPECT_EQ(GMERR_OK, DbAppendListItem(&irPlans, logPlan));
    EXPECT_EQ(GMERR_OK, DtlQryOptimize(g_optimizerMemCtx, &irPlans));

    printf("after optimize physical plan:\n");
    IRPrintPlan(g_optimizerMemCtx, (IRPlanT *)DbListItem(&irPlans, 0));
    printf("\n");
    EXPECT_TRUE(IRIsPlanEqual(phyPlan, (IRPlanT *)DbListItem(&irPlans, 0)));
}
