#include "query_ut_base.h"
#include <thread>
#include <vector>
#include "qry_sysview.h"
#include "qry_view.h"
#include "srv_data_prepare.h"
#include "db_msg_buffer.h"
#include "dm_meta_basic.h"
#include "sysview_instance.h"
#include "db_rpc_conn_msg.h"
#include "srv_data_public.h"
#include "srv_data_service.h"
#include "srv_data_public.h"
#include "db_server_dfgmt_task.h"
#include "ee_access_method.h"
#include "common_init.h"

class UtSysview : public testing::Test {
protected:
    DbMemCtxT *old;
    FixBufferT req_start = {0};
    FixBufferT req_scan = {0};
    FixBufferT req_next = {0};
    FixBufferT req_close = {0};
    FixBufferT req = {0};
    DbMemCtxT *dyAlgoCtxVertex;
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        printf("sysview ut stub cleared.\n");
        dyAlgoCtxVertex = QryGetDyAlgoCtxVertexBase();
        FixBufCreate(&req_start, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        FixBufCreate(&req_scan, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        FixBufCreate(&req_next, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        FixBufCreate(&req_close, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        clearAllStub();
        FixBufRelease(&req_start);
        FixBufRelease(&req_scan);
        FixBufRelease(&req_next);
        FixBufRelease(&req_close);
        FixBufRelease(&req);
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

DbOamapIteratorT g_iter = 0;

static char g_labelNameIp4[] = "ip4forward";
static char g_labelJsonIp4[] = R"([{
        "type":"record",
        "name":"ip4forward",
        "fields":[
            {"name":"nhp_group_id1", "type":"int32"},
            {"name":"nhp_group_name1", "type":"int32"},
            {"name":"subs_string", "type":"string", "size":8},
            {"name":"subs_char", "type":"char"},
            {"name":"subs_int32", "type":"int32"}
        ],
        "keys":
        [
            {"node":"ip4forward", "name":"ip4forward_K0", "fields":["nhp_group_id1"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"ip4forward", "name":"nhp_group_name1", "fields":["nhp_group_name1"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";
static char *g_cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

TEST_F(UtSysview, QRY_DDL_CreateVertexLabel)
{
    uint32_t ret = QryTestCreateVertexLabel(g_cfgJson, g_labelJsonIp4, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(g_labelNameIp4, true, NULL));
}

TEST_F(UtSysview, QRY_DML_CreateEdgeLabel1)
{
    uint32_t ret = QryTestCreateBaseVertexLabelFirst(g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateBaseVertexLabelSecond(g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateBaseEdgeLabel();
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, QryTestDropBaseVertexLabelFirst());
}

static char g_subsName[] = "sub_xxx";
static char g_subsJson[] = R"({
            "label_name":"ip4forward",
            "persist":false,
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";

static void QryDDLCreateSubs(FixBufferT *req, DbMemCtxT *dyAlgoCtxVertex)
{
    uint32_t ret = QryTestCreateVertexLabel(g_cfgJson, g_labelJsonIp4, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    FixBufCreate(req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char *subsConn = (char *)QryGetBaseSubsChanneName();

    TextT putText;
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = g_subsName;
    putText.len = sizeof(g_subsName);
    ret = FixBufPutText(req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = g_subsJson;
    putText.len = sizeof(g_subsJson);
    ret = FixBufPutText(req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = subsConn;
    putText.len = strlen(subsConn) + 1;
    ret = FixBufPutText(req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = (char *)"TEST";
    putText.len = strlen(putText.str) + 1;
    ret = FixBufPutText(req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(req), MSG_OP_RPC_CREATE_SUBSCRIPTION,
        req->pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    AddSubsUserCbFuncNameStub();
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(g_labelNameIp4, true, NULL));
    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}
TEST_F(UtSysview, QRY_DDL_CreateSubs)
{
    QryDDLCreateSubs(&req, dyAlgoCtxVertex);
}

TEST_F(UtSysview, QRY_DDL_CreateSubs1)
{
    DrtConnectionT *conn = NULL;
    uint32_t ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryTestCreateVertexLabel(g_cfgJson, g_labelJsonIp4, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);

    char subsName[] = "sub_xxxx";
    char subsJson[] = R"({
            "label_name":"ip4forward",
            "persist":false,
            "comment":"the edge 7 to 8",
            "events":
                [
                    {"type":"insert", "msgTypes":["new object", "old object"]},
                    {"type":"delete", "msgTypes":["new object", "old object"]}
                ],
            "retry":true,
            "constraint":
                {
                    "operator_type":"and",
                    "conditions":
                        [
                            {
                                "property":"subs_char",
                                "value":"x"
                            },
                            {
                                "property":"nhp_group_id1"
                            },
                            {
                                "property":"subs_int32",
                                "value":2147483647
                            }
                        ]
                }
        })";
    char *subsConn = (char *)QryGetBaseSubsChanneName();

    TextT putText;
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    MsgHeaderT *msgHeader = NULL;
    EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
    EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
    msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
    msgHeader->stmtId = 0;

    putText.str = subsName;
    putText.len = sizeof(subsName);
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = subsJson;
    putText.len = sizeof(subsJson);
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = subsConn;
    putText.len = strlen(subsConn) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    putText.str = (char *)"TEST";
    putText.len = strlen(putText.str) + 1;
    ret = FixBufPutText(&req, &putText);
    EXPECT_EQ(GMERR_OK, ret);

    msgHeader->size = FixBufGetPos(&req);
    RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_CREATE_SUBSCRIPTION,
        req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = NULL;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    AddSubsUserCbFuncNameStub();
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    CataUnBindChannelByName(QryGetBaseSubsChanneName(), NULL);
    EXPECT_EQ(GMERR_OK, QryTestDropVertexLabel(g_labelNameIp4, true, NULL));
    QryTestReleaseSession(conn);
    printf("QRY_DDL_CreateSubs success\n");
}

static void SvTestCustomisedCondition(char *labelName, char *condStr, FixBufferT *req_start, FixBufferT *req_scan,
    FixBufferT *req_next, FixBufferT *req_close)
{
    Status ret;
    /* RpcMsgHeader  | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t fetch = INVALID_VALUE32;
    uint32_t labelNameLen = strlen(labelName) + 1;
    uint32_t condStrLen = strlen(condStr) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(req_start, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    ASSERT_EQ(GMERR_OK, ret);

    ret = FixBufPutUint32(req_start, 0);
    ASSERT_EQ(GMERR_OK, ret);
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(req_start, &putText);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(req_start, 1);  // isView
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(req_start, 0);  // isCsMode
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_start, DB_INVALID_UINT32);
    ASSERT_EQ(GMERR_OK, ret);

    // 由于视图做了延后申请操作，因此需要先进行MSG_OP_RPC_GET_VERTEX_LABEL测试
    // test MSG_OP_RPC_GET_VERTEX_LABEL
    DbFillMsgHeader(req_start, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);

    MsgHeaderT *msgHeader = RpcGetMsgHeader(req_start);
    (void)RpcGetOpHeader(req_start);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    SvInstanceT *svInst = DbSvGetSvInstance(NULL);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);

    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = (void *)svInst;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_start, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");
    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t labelId = vertexLabel->metaCommon.metaId;
    CataReleaseVertexLabel(vertexLabel);
    // test MSG_OP_RPC_SCAN_VERTEX_BEGIN
    uint32_t headerOffsetScan = 0;
    ret = FixBufReserveDataOffset(req_scan, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffsetScan);
    ASSERT_EQ(GMERR_OK, ret);
    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(req_scan, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = fetch;

    ret = FixBufPutUint32(req_scan, labelId);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_scan, vertexLabel->metaCommon.version);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_scan, vertexLabel->metaVertexLabel->uuid);
    ASSERT_EQ(GMERR_OK, ret);
    FixBufPutUint32(req_scan, 0);  // scan flag
    FixBufPutUint32(req_scan, 0);  // left indexKey
    FixBufPutUint32(req_scan, 0);  // right indexKey
    FixBufPutUint32(req_scan, 1);  // isSysviewRecordCmd
    putText.str = condStr;
    putText.len = condStrLen;
    ret = FixBufPutText(req_scan, &putText);
    FixBufPutUint32(req_scan, 0);  // result set
    FixBufPutUint32(req_scan, 0);  // sort count
    FixBufPutUint64(req_scan, 0);  // limit count
    DbFillMsgHeader(req_scan, MSG_OP_RPC_SCAN_VERTEX_BEGIN, headerOffsetScan, CS_NEVER_TIMEOUT, 0);

    MsgHeaderT *msgScanHeader = RpcGetMsgHeader(req_scan);
    (void)RpcGetOpHeader(req_scan);
    EXPECT_TRUE(msgScanHeader != NULL);
    msgScanHeader->stmtId = 0;
    svInst = DbSvGetSvInstance(NULL);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);

    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgScanHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_scan, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");

    // test MSG_OP_RPC_FETCH_CYPHER
    uint32_t headerNextOffset = 0;
    ret = FixBufReserveDataOffset(req_next, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerNextOffset);
    ASSERT_EQ(GMERR_OK, ret);

    DbFillMsgHeader(req_next, MSG_OP_RPC_FETCH_CYPHER, headerNextOffset, CS_NEVER_TIMEOUT, QryTestGetCurStmtID());

    MsgHeaderT *msgHeaderNext = (MsgHeaderT *)FixBufGetData(req_next, MSG_HEADER_SIZE);
    EXPECT_TRUE(msgHeaderNext != NULL);
    msgScanHeader->stmtId = 0;
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgHeaderNext;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_next, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");

    uint32_t headerCloseOffset = 0;
    ret = FixBufReserveDataOffset(req_close, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerCloseOffset);
    EXPECT_EQ(GMERR_OK, ret);

    DbFillMsgHeader(req_close, MSG_OP_RPC_RELEASE_STMT, headerCloseOffset, CS_NEVER_TIMEOUT, QryTestGetCurStmtID());

    MsgHeaderT *msgHeaderClose = (MsgHeaderT *)FixBufGetData(req_close, MSG_HEADER_SIZE);
    EXPECT_TRUE(msgHeaderClose != NULL);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgHeaderClose;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_close, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

Status SvQueryProcQryDmlOperStatisStub(void *vertex, SvCursorT *cursor)
{
    DB_POINTER2(vertex, cursor);
    uint16_t *id = (uint16_t *)cursor->rowId;

    printf("sysview filter is: %s.\n", cursor->viewCond->str);
    const char *conStr = "TestConditionForDmlOperStatisView";
    EXPECT_STREQ(conStr, cursor->viewCond->str);

    while (*id < 2) {
        (*id)++;
    }
    if ((*id) >= 2) {
        DB_LOG_DBG_INFO("[DRT] Get worker view record finish.");
        return GMERR_NO_DATA;
    }
    return GMERR_OK;
}

TEST_F(UtSysview, SysviewCustomisedcondition)
{
    QryDDLCreateSubs(&req, dyAlgoCtxVertex);
    SvInstanceT *svInst = DbSvGetSvInstance(NULL);
    SvDefT *view = NULL;
    Status ret = SvGetViewByName(svInst, (char *)"V$QRY_DML_OPER_STATIS", &view);
    EXPECT_EQ(GMERR_OK, ret);
    view->isSupportCond = true;
    view->isCreatedTable = false;

    (void)setStubC((void *)SvQueryProcQryDmlOperStatis, (void *)SvQueryProcQryDmlOperStatisStub);

    SvTestCustomisedCondition((char *)"V$QRY_DML_OPER_STATIS", (char *)"TestConditionForDmlOperStatisView", &req_start,
        &req_scan, &req_next, &req_close);
}

static void SvTestBase(
    char *labelName, FixBufferT *req_start, FixBufferT *req_next, FixBufferT *req_scan, FixBufferT *req_close)
{
    Status ret;
    /* RpcMsgHeader  | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t fetch = INVALID_VALUE32;
    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(req_start, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    ASSERT_EQ(GMERR_OK, ret);

    FixBufPutUint32(req_start, 0);  // get vertexLable id
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(req_start, &putText);  // get vertexLable name
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(req_start, 1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(req_start, 0);  // isCsMode
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_start, DB_INVALID_UINT32);
    ASSERT_EQ(GMERR_OK, ret);

    // 由于视图做了延后申请操作，因此需要先进行MSG_OP_RPC_GET_VERTEX_LABEL测试
    // test MSG_OP_RPC_GET_VERTEX_LABEL
    DbFillMsgHeader(req_start, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);

    MsgHeaderT *msgHeader = RpcGetMsgHeader(req_start);
    (void)RpcGetOpHeader(req_start);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    SvInstanceT *svInst = DbSvGetSvInstance(NULL);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);

    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = (void *)svInst;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_start, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");

    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName);
    CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    uint32_t labelId = vertexLabel->metaCommon.metaId;
    CataReleaseVertexLabel(vertexLabel);

    // test MSG_OP_RPC_SCAN_VERTEX_BEGIN
    uint32_t headerOffsetScan = 0;
    ret = FixBufReserveDataOffset(req_scan, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffsetScan);
    ASSERT_EQ(GMERR_OK, ret);
    DbPrepExecReqT *reqHead = (DbPrepExecReqT *)FixBufReserveData(req_scan, sizeof(DbPrepExecReqT));
    reqHead->exec.preFetchRows = fetch;

    ret = FixBufPutUint32(req_scan, labelId);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_scan, vertexLabel->metaCommon.version);
    ASSERT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(req_scan, vertexLabel->metaVertexLabel->uuid);
    ASSERT_EQ(GMERR_OK, ret);
    FixBufPutUint32(req_scan, 0);  // scan flag
    FixBufPutUint32(req_scan, 0);  // left indexKey
    FixBufPutUint32(req_scan, 0);  // right indexKey
    FixBufPutUint32(req_scan, 1);  // isSysviewRecordCmd
    FixBufPutUint32(req_scan, 0);  // condition
    FixBufPutUint32(req_scan, 0);  // result set
    FixBufPutUint32(req_scan, 0);  // sort count
    FixBufPutUint64(req_scan, 0);  // limit count
    // test MSG_OP_RPC_SYSVIEW_SCAN_VERTEX_BEGIN
    DbFillMsgHeader(req_scan, MSG_OP_RPC_SCAN_VERTEX_BEGIN, headerOffsetScan, CS_NEVER_TIMEOUT, 0);

    MsgHeaderT *msgScanHeader = RpcGetMsgHeader(req_scan);
    (void)RpcGetOpHeader(req_scan);
    EXPECT_TRUE(msgScanHeader != NULL);

    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgScanHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_scan, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");

    // test MSG_OP_RPC_SYSVIEW_SCAN_VERTEX_NEXT
    uint32_t headerNextOffset = 0;
    ret = FixBufReserveDataOffset(req_next, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerNextOffset);
    ASSERT_EQ(GMERR_OK, ret);

    DbFillMsgHeader(req_next, MSG_OP_RPC_FETCH_CYPHER, headerNextOffset, CS_NEVER_TIMEOUT, QryTestGetCurStmtID());

    MsgHeaderT *msgHeaderNext = (MsgHeaderT *)FixBufGetData(req_next, MSG_HEADER_SIZE);
    EXPECT_TRUE(msgHeaderNext != NULL);
    msgHeaderNext->stmtId = 0;
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgHeaderNext;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_next, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    printf("--------------------------------------------------------------------\n");

    uint32_t headerCloseOffset = 0;
    ret = FixBufReserveDataOffset(req_close, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerCloseOffset);
    EXPECT_EQ(GMERR_OK, ret);

    DbFillMsgHeader(req_close, MSG_OP_RPC_RELEASE_STMT, headerCloseOffset, CS_NEVER_TIMEOUT, QryTestGetCurStmtID());

    MsgHeaderT *msgHeaderClose = (MsgHeaderT *)FixBufGetData(req_close, MSG_HEADER_SIZE);
    EXPECT_TRUE(msgHeaderClose != NULL);
    msgHeaderNext->stmtId = 0;
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);

    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    // update serviceCtx

    serviceCtx.ctx = (void *)svInst;
    procCtx.msgHeader = msgHeaderClose;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(req_close, &procCtx.msg);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    ret = FastpathEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

Status QryAllocSessionByView(DrtConnectionT **conn)
{
    Status ret = QryTestAllocConn(conn);
    if (ret != GMERR_OK) {
        return ret;
    }
    CliConnectResponseT connResp = {0};
    DrtInstanceT *drtInstance = DrtGetInstance(NULL);
    SessionParamT param = {.logThreshold = 0, .rollBackThreshold = 0, .userName = "", .pwd = ""};
    return QryAllocSessionForDrt(*conn, &connResp, drtInstance, &param);
}

void *SysviewGetLabel(void *tmpDyCtxVertex)
{
    DbSetServerThreadFlag();
    DbMemCtxT *dyCtxVertex = (DbMemCtxT *)tmpDyCtxVertex;
    char *labelName = (char *)"V$QRY_DML_OPER_STATIS";
    FixBufferT req_start = {0};
    uint32_t ret;
    (DbMemCtxT *)DbMemCtxSwitchTo(dyCtxVertex);
    FixBufCreate(&req_start, dyCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    /* RpcMsgHeader  | fetchNum | labelNameLen | labelName | filterLen | filterBuf */
    DrtConnectionT *conn = NULL;
    ret = QryAllocSessionByView(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t labelNameLen = strlen(labelName) + 1;
    TextT putText;

    uint32_t headerOffset = 0;
    ret = FixBufReserveDataOffset(&req_start, MSG_HEADER_ALIGN_SIZE + MSG_OP_HEADER_ALIGN_SIZE, &headerOffset);
    EXPECT_EQ(GMERR_OK, ret);

    FixBufPutUint32(&req_start, 0);  // get vertexLable id
    putText.str = labelName;
    putText.len = labelNameLen;
    ret = FixBufPutText(&req_start, &putText);  // get vertexLable name
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(&req_start, 1);  // get isView
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint16(&req_start, 0);  // get isCsMode
    EXPECT_EQ(GMERR_OK, ret);
    ret = FixBufPutUint32(&req_start, DB_INVALID_UINT32);
    EXPECT_EQ(GMERR_OK, ret);

    // 由于视图做了延后申请操作，因此需要先进行MSG_OP_RPC_GET_VERTEX_LABEL测试
    // test MSG_OP_RPC_GET_VERTEX_LABEL
    DbFillMsgHeader(&req_start, MSG_OP_RPC_GET_VERTEX_LABEL, headerOffset, CS_NEVER_TIMEOUT, 0);

    MsgHeaderT *msgHeader = RpcGetMsgHeader(&req_start);
    (void)RpcGetOpHeader(&req_start);
    EXPECT_TRUE(msgHeader != NULL);
    msgHeader->stmtId = 0;
    SvInstanceT *svInst = DbSvGetSvInstance(NULL);
    // init serviceCtx
    DrtServiceCtxT serviceCtx;
    serviceCtx.ctx = (void *)svInst;
    DrtProcCtxT procCtx = {0};
    procCtx.conn = conn;
    procCtx.msgHeader = msgHeader;
    procCtx.type = PROC_CTX_TYPE_RPC_MSG;
    (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
    FixBufDup(&req_start, &procCtx.msg);
    ret = PublicServiceEntry(&serviceCtx, &procCtx);
    EXPECT_EQ(GMERR_OK, ret);
    return 0;
}

TEST_F(UtSysview, SysviewConcurrentTest)
{
    (void)setStubC((void *)QryInitUserAndGroup, (void *)QryInitUserAndGroupStub);
    (void)setStubC((void *)QryCheckLoginPrivByGroupList, (void *)QryCheckLoginPrivByGroupListStub);
    (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
    (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWritePackStub);
    (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
    int32_t ret;
    int thread_nums = 10;
    pthread_t tid[10];
    for (int i = 0; i < thread_nums; i++) {
        ret = pthread_create(&tid[i], NULL, SysviewGetLabel, dyAlgoCtxVertex);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thread_nums; i++) {
        pthread_join(tid[i], NULL);
    }
}

TEST_F(UtSysview, SysviewGetDrtWorkerStat)
{
    SvTestBase((char *)"V$DRT_WORKER_STAT", &req_start, &req_scan, &req_next, &req_close);
}
TEST_F(UtSysview, SysviewGetDrtPipeStat)
{
    SvTestBase((char *)"V$DRT_PIPE_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetDrtComStat)
{
    SvTestBase((char *)"V$DRT_COM_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetDrtConnStat)
{
    SvTestBase((char *)"V$DRT_CONN_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, DISABLED_SysviewGetDirectMsgPoolStat)
{
    SvTestBase((char *)"V$DRT_DIRECT_MSG_POOL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetComShmemStat)
{
    SvTestBase((char *)"V$COM_SHMEM_CTX", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetComSynCtxStat)
{
    SvTestBase((char *)"V$COM_DYN_CTX", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetComMemSummaryStat)
{
    SvTestBase((char *)"V$COM_MEM_SUMMARY", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetTableMemSummaryStat)
{
    SvTestBase((char *)"V$COM_TABLE_MEM_SUMMARY", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetShmemGroupStat)
{
    SvTestBase((char *)"V$COM_SHMEM_GROUP", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetStorageHeapStat)
{
    DbSrvDfgmtTaskMgrInit(NULL);
    SvTestBase((char *)"V$STORAGE_HEAP_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetStorageMedataStat)
{
    SvTestBase((char *)"V$STORAGE_MEMDATA_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetStorageFsmStat)
{
    SvTestBase((char *)"V$STORAGE_FSM_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetEdgeLabelMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_EDGE_LABEL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_HEAP_VERTEX_LABEL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetUndoLabelMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_UNDO_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetLockPoolOverview)
{
    SvTestBase((char *)"V$STORAGE_LOCK_OVERVIEW", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelHashCollisionStat)
{
    SvTestBase((char *)"V$STORAGE_HASH_COLLISION_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelUniqueIndexMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_HASH_INDEX_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelNonUniqueIndexMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_HASH_LINKLIST_INDEX_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelSecIndexArtMemoryUsage)
{
    SvTestBase((char *)"V$STORAGE_ART_INDEX_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetVertexLabelIndexGlobalStat)
{
    SvTestBase((char *)"V$STORAGE_INDEX_GLOBAL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetServerMemoryOverHead)
{
    SvTestBase((char *)"V$SERVER_MEMORY_OVERHEAD", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetTxOverallUsage)
{
    SvTestBase((char *)"V$STORAGE_TRX_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetTxDetail)
{
    SvTestBase((char *)"V$STORAGE_TRX_DETAIL", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetCataGeneralInfo)
{
    SvTestBase((char *)"V$CATA_GENERAL_INFO", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetCataVertexLabel)
{
    SvTestBase((char *)"V$CATA_VERTEX_LABEL_INFO", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetCataEdgeLabel)
{
    SvTestBase((char *)"V$CATA_EDGE_LABEL_INFO", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetCataSubs)
{
    SvTestBase((char *)"V$CATA_LABEL_SUBS_INFO", &req_start, &req_scan, &req_next, &req_close);
}

// 资源池不支持持久化
#ifndef FEATURE_PERSISTENCE
TEST_F(UtSysview, SysviewGetResAllPoolStat)
{
    SvTestBase((char *)"V$STORAGE_RESOURCE_ALL_POOL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetResSinglePoolStat)
{
    SvTestBase((char *)"V$STORAGE_RESOURCE_SINGLE_POOL_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewGetResSinglePoolBitmapStat)
{
    SvTestBase((char *)"V$STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT", &req_start, &req_scan, &req_next, &req_close);
}
#endif

TEST_F(UtSysview, SysviewGetDmlOperStatis)
{
    SvTestBase((char *)"V$QRY_DML_OPER_STATIS", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewVertexLabelCheckInfo)
{
    uint32_t ret;

    char *labelJson1 = (char *)R"([{
        "type":"record",
        "name":"ip4forwardwithpartition",
        "fields":[
            {"name":"nhp_group_id1", "type":"int32"},
            {"name":"nhp_group_name1", "type":"int32"},
            {"name":"subs_string", "type":"string", "size":8},
            {"name":"subs_char", "type":"char"},
            {"name":"subs_int32", "type":"int32"},
            {"name":"subs_F7_partion", "type":"partition", "nullable":false}
        ],
        "keys":
        [
            {"node":"ip4forwardwithpartition", "name":"ip4forward_K0", "fields":["nhp_group_id1"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"ip4forwardwithpartition", "name":"nhp_group_name1", "fields":["nhp_group_name1"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(g_cfgJson, labelJson1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    SvTestBase((char *)"V$CATA_VERTEX_LABEL_CHECK_INFO", &req_start, &req_scan, &req_next, &req_close);
}

int32_t DbCfgGetInt32Stub(const DbCfgMgrT *cfgMgr, uint32_t cfgItemsId, bool isNeedLock, int32_t *value)
{
    if (cfgItemsId == DB_CFG_DML_OPER_STAT_IS_ENABLED || cfgItemsId == DB_CFG_DML_PERF_STAT_IS_ENABLED) {
        *value = 1;
        return GMERR_OK;
    }
    DbCfgMgrHandleT cfgHandle = DbGetCfgHandle(NULL);
    if (cfgHandle == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NO_DATA, "[Common] handle is null when getting db cfg int32!");
        return GMERR_NO_DATA;
    }

    DbCfgMgrT *mgr = (DbCfgMgrT *)cfgHandle;
    if (cfgItemsId >= mgr->cfgItemNum) {
        return GMERR_DATA_EXCEPTION;
    }
    *value = mgr->cfgItems[cfgItemsId].value.int32Val;
    return GMERR_OK;
}

TEST_F(UtSysview, SysviewDmlInfo)
{
    (void)setStubC((void *)DbCfgGetInt32, (void *)DbCfgGetInt32Stub);
    SvTestBase((char *)"V$QRY_DML_INFO", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewDrtConnSubsInfo)
{
    SvTestBase((char *)"V$DRT_CONN_SUBS_STAT", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewDrtConnStatInfoWithNodeInvalid)
{
    DrtConnectionT *conn;
    char *connName = (char *)"SysviewDrtConnStatInfoWithNodeInvalid";

    QryTestAllocSubConn(&conn, connName);
    uint16_t nodeId = conn->nodeId.nodeId;
    conn->nodeId.nodeId = DB_INVALID_UINT16;

    SvTestBase((char *)"V$DRT_CONN_STAT", &req_start, &req_scan, &req_next, &req_close);

    conn->nodeId.nodeId = nodeId;
    QryTestReleaseSubConn(conn);
}

TEST_F(UtSysview, SysviewDrtSubsConnStatInfoWithNodeInvalid)
{
    DrtConnectionT *conn;
    char *connName = (char *)"SysviewDrtConnInfoWithNodeInvalid";

    QryTestAllocSubConn(&conn, connName);
    uint16_t nodeId = conn->nodeId.nodeId;
    conn->nodeId.nodeId = DB_INVALID_UINT16;

    SvTestBase((char *)"V$DRT_CONN_SUBS_STAT", &req_start, &req_scan, &req_next, &req_close);

    conn->nodeId.nodeId = nodeId;
    QryTestReleaseSubConn(conn);
}

TEST_F(UtSysview, SysviewQrySession)
{
    SvTestBase((char *)"V$QRY_SESSION", &req_start, &req_scan, &req_next, &req_close);
}

TEST_F(UtSysview, SysviewDestroy)
{
    SvTestBase((char *)"V$QRY_SESSION", &req_start, &req_scan, &req_next, &req_close);
    SvInstanceT *inst = DbSvGetSvInstance(NULL);
    SvDestroy(inst);
}

static DbMemCtxT *g_amTopMemCtx = NULL;
class UtSysviewAccessMethod : public testing::Test {
protected:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
    static void SetUpTestCase()
    {
        CommonInit();
        DbMemCtxArgsT args = {0};
        args.init = DynamicAlgoInit;
        args.memType = DB_DYNAMIC_MEMORY;
        args.ctxSize = sizeof(DbDynamicMemCtxT);
        args.algoType = ALGO_INVALID_TYPE;
        args.ctxId = DB_INVALID_UINT32;
        g_amTopMemCtx = DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        assert(g_amTopMemCtx != NULL);
    }
    static void TearDownTestCase()
    {
        DbDeleteDynMemCtx((DbMemCtxT *)g_amTopMemCtx);
        CommonRelease();
    };

    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}
};

TEST_F(UtSysviewAccessMethod, SysviewLabelAM)
{
    LabelBeginCfgT beginCfg = {0};
    beginCfg.eeMemCtx = g_amTopMemCtx;
    DmVertexLabelT vertexLabel = {0};
    beginCfg.vertexLabel = &vertexLabel;
    LabelScanDescT dest;
    LabelScanDescT *scanDesc = &dest;

    EXPECT_EQ(GMERR_OK, SysviewLabelBeginScan(beginCfg, NULL, &scanDesc));
    EXPECT_EQ(GMERR_OK, SysviewLabelReScan(scanDesc));
    uint64_t phyItemNum = 0;
    EXPECT_EQ(GMERR_OK, SysviewLabelAccessGetPhyItemNum(scanDesc, &phyItemNum));
}
