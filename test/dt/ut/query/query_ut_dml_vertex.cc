#include "query_ut_base.h"
#include "db_text.h"
#include "dm_meta_basic.h"
#include "db_server_dfgmt_task.h"
#include "se_resource_session_pub.h"

Status QryCheckLoginPrivWithDeltaStoreStub(SessionT *session, bool *login, CataRoleT *role)
{
    const SessionParamT param = {0};
    Status ret = QryInitUserAndGroup(session, &param);
    if (ret != GMERR_OK) {
        return ret;
    }
    CataUserNameInfoT user = {
        session->externalUser.dbUserName,
        session->externalUser.dbGroupName,
        session->externalUser.dbProcessName,
    };
    bool isGroupLogin;
    ret = (Status)CataLoginVerify(&user, login, &session->isDBA, &isGroupLogin, role);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Internal error occurs when check login.");
        return ret;
    }
    *login = true;
    session->isDBA = false;
    session->sesType = SESSION_TYPE_NO_CONN;
    return GMERR_OK;
}

Status QryExecuteInsertVertexStub(QryStmtT *stmt)
{
    return GMERR_OK;
}
int32_t DrtConnWriteBatchPackStub(const DrtConnectionT *conn, FixBufferT *msg)
{
    SessionT *session = (SessionT *)conn->session;
    FixBufferT *rsp = QrySessionGetRsp(session);
    RpcSeekFirstOpMsg(rsp);
    uint32_t totalNum, successNum;
    EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &totalNum));
    EXPECT_EQ((uint32_t)2, totalNum);
    EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &successNum));
    EXPECT_EQ((uint32_t)2, successNum);
    return GMERR_OK;
}

static char *labelName = (char *)"labelvertex1";

class UtQueryDMLVertex : public testing::Test {
protected:
    DbMemCtxT *dyAlgoCtxVertex;
    DbMemCtxT *old;
    FixBufferT req = {0};
    static void SetUpTestCase()
    {
        BaseInit();
    }
    static void TearDownTestCase()
    {
        BaseUninit();
    };

    virtual void SetUp()
    {
        clearAllStub();
        DbMemCtxArgsT args = {0};
        dyAlgoCtxVertex =
            DbCreateDynMemCtx((DbMemCtxT *)DbGetTopDynMemCtx(NULL), true, "dynamic memory context", &args);
        old = DbMemCtxSwitchTo((DbMemCtxT *)dyAlgoCtxVertex);
        FixBufCreate(&req, dyAlgoCtxVertex, CS_PACK_SIZE, FIX_BUF_FLAG_EXTEND_BUFFER);
    }
    virtual void TearDown()
    {
        FixBufRelease(&req);
        if (old != NULL) {
            DbMemCtxSwitchTo(old);
        }
        DbDeleteDynMemCtx((DbMemCtxT *)dyAlgoCtxVertex);
        clearAllStub();
    }
    virtual int AllocObjects()
    {
        return 0;
    }
    virtual void FreeObjects()
    {}

    void InsertVertex(uint32_t f0, char f1, int64_t f3, const char *f5)
    {
        uint32_t ret;
        char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F3", (char *)"F5"};
        DmValueT propertyValue[4];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = f0;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = f1;
        propertyValue[2].type = DB_DATATYPE_INT64;
        propertyValue[2].value.longValue = f3;
        propertyValue[3].type = DB_DATATYPE_STRING;
        propertyValue[3].value.strAddr = (void *)f5;
        propertyValue[3].value.length = DM_STR_LEN(f5);

        ret = QryTestInsertVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void InsertVertexWithoutpropertyValue()
    {
        uint32_t ret;
        char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F3", (char *)"F5"};
        ret = QryTestInsertVertex(labelName, 0, propertyName, NULL);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        char *result1 = (char *)"Data exception occurs. read vertex buf.";
        EXPECT_STREQ(result1, lastError1->str);
    }

    void InsertVertex3(uint32_t f0, char f1, uint32_t f3, const char *f5)
    {
        uint32_t ret;
        char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F3", (char *)"F5"};
        DmValueT propertyValue[4];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = f0;
        propertyValue[1].type = DB_DATATYPE_UCHAR;
        propertyValue[1].value.ucharValue = f1;
        propertyValue[2].type = DB_DATATYPE_UINT32;
        propertyValue[2].value.uintValue = f3;
        propertyValue[3].type = DB_DATATYPE_STRING;
        propertyValue[3].value.strAddr = (void *)f5;
        propertyValue[3].value.length = DM_STR_LEN(f5);

        ret = QryTestInsertVertex(
            (char *)"labelvertex3", sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void CreateVertexLabel3()
    {
        uint32_t ret;
        char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";

        char *labelJson = (char *)R"([{
        "type":"record",
        "name":"labelvertex3",
        "id":12,
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uchar"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"uint32"},
            {"name":"F4", "type":"time", "nullable":true},
            {"name":"F5", "type":"string", "size":20, "nullable":true}
        ],
        "keys":
        [
            {"node":"labelvertex3", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"labelvertex3", "name":"T39_K1", "fields":["F1"], "index":{"type":"local"}, "constraints":{"unique":false}},
            {"node":"labelvertex3", "name":"T39_K2", "fields":["F3"], "index":{"type":"hashcluster"}, "constraints":{"unique":false}}
        ]
    }])";
        ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void RangeScan3()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        DmValueT leftPropertyValues[1];
        leftPropertyValues[0].type = DB_DATATYPE_UCHAR;
        leftPropertyValues[0].value.ucharValue = 'a';

        DmValueT rightPropertyValues[1];
        rightPropertyValues[0].type = DB_DATATYPE_UCHAR;
        rightPropertyValues[0].value.ucharValue = 'c';
        // begin scan
        uint32_t scanFlag = CS_RANGE_SCAN_ENABLED + CS_RANGE_SCAN_LEFT_INCLUDE + CS_RANGE_SCAN_RIGHT_INCLUDE;
        ret = QryTestBeginScanVertex(conn, (char *)"labelvertex3", (char *)"T39_K1", 1, leftPropertyValues,
            rightPropertyValues, scanFlag, NULL, NULL, 0, 10);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 4);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        QryReleaseRsp(rsp);
    }

    void FullScan3()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        // begin scan
        uint32_t scanFlag = CS_RANGE_SCAN_ENABLED + CS_RANGE_SCAN_LEFT_INCLUDE + CS_RANGE_SCAN_RIGHT_INCLUDE;
        ret = QryTestBeginScanVertex(
            conn, (char *)"labelvertex3", (char *)"T39_K1", 0, NULL, NULL, scanFlag, NULL, NULL, 0, 10);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 5);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        QryReleaseRsp(rsp);
    }

    void PointDelete3()
    {
        uint32_t ret;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 300;

        ret = QryTestDeleteVertex((char *)"labelvertex3", indexName, 1, indexPropertyValue, NULL, 0, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void RangeDelete3()
    {
        uint32_t ret;
        const char *indexName = "T39_K1";
        DmValueT leftPropertyValues[1];
        leftPropertyValues[0].type = DB_DATATYPE_UCHAR;
        leftPropertyValues[0].value.ucharValue = 'a';

        DmValueT rightPropertyValues[1];
        rightPropertyValues[0].type = DB_DATATYPE_UCHAR;
        rightPropertyValues[0].value.ucharValue = 'c';

        // begin scan
        FixBufferT *rsp = NULL;
        uint32_t scanFlag = CS_RANGE_SCAN_ENABLED + CS_RANGE_SCAN_LEFT_INCLUDE + CS_RANGE_SCAN_RIGHT_INCLUDE;
        ret = QryTestDeleteVertex(
            (char *)"labelvertex3", indexName, 1, leftPropertyValues, rightPropertyValues, 0, scanFlag, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t affectRows;
        RpcSeekFirstOpMsg(rsp);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)3, affectRows);
        QryReleaseRsp(rsp);
    }

    void GetAllCount()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        ret = QryTestGetCount(conn, labelName, NULL, 0, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        RpcSeekFirstOpMsg(rsp);
        uint64_t count;
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &count));
        EXPECT_EQ((uint64_t)7, count);
        QryReleaseRsp(rsp);
        QryTestReleaseSession(conn);
    }

    void GetPrimaryCount()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = {0};
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        DmValueT propertyValues[1];
        propertyValues[0].type = DB_DATATYPE_UINT32;
        propertyValues[0].value.uintValue = 300;

        ret = QryTestGetCount(conn, labelName, "T39_K0", sizeof(propertyValues) / sizeof(DmValueT), propertyValues);
        EXPECT_EQ(GMERR_OK, ret);

        RpcSeekFirstOpMsg(rsp);
        uint64_t count;
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &count));
        EXPECT_EQ((uint64_t)1, count);
        QryReleaseRsp(rsp);

        QryTestReleaseSession(conn);
    }

    void GetSecondryCount()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        DmValueT propertyValues[1];
        propertyValues[0].type = DB_DATATYPE_INT64;
        propertyValues[0].value.longValue = 4;
        ret = QryTestGetCount(conn, labelName, "T39_K2", sizeof(propertyValues) / sizeof(DmValueT), propertyValues);
        EXPECT_EQ(GMERR_OK, ret);

        RpcSeekFirstOpMsg(rsp);
        uint64_t count;
        EXPECT_EQ(GMERR_OK, FixBufGetUint64(rsp, &count));
        EXPECT_EQ((uint64_t)2, count);
        QryReleaseRsp(rsp);
        QryTestReleaseSession(conn);
    }

    void SeqScan()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        // begin scan
        ret = QryTestBeginScanVertex(conn, labelName, NULL, 0, NULL, NULL, 0, NULL, NULL, 0, 10);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 7);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        QryReleaseRsp(rsp);
    }

    void SeqSort()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufferT *rsp = NULL;
        // begin scan
        ret = QryTestBeginSortScanVertex(conn, labelName, (char *)"F1", 0, NULL, 0, &rsp, 10, ORDER_ASC);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 7);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
    }

    void Uniqidxscan()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        DmValueT propertyValues[1];
        propertyValues[0].type = DB_DATATYPE_UINT32;
        propertyValues[0].value.uintValue = 300;
        // begin scan
        ret = QryTestBeginScanVertex(conn, labelName, "T39_K0", sizeof(propertyValues) / sizeof(DmValueT),
            propertyValues, NULL, 0, NULL, NULL, 0, 10);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 1);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        QryReleaseRsp(rsp);
    }

    void SecIdxScan()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        FixBufferT *rsp = NULL;
        ret = QryTestAllocSession(&conn, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        DmValueT propertyValues[1];
        propertyValues[0].type = DB_DATATYPE_INT64;
        propertyValues[0].value.longValue = 4;
        // begin scan
        ret = QryTestBeginScanVertex(conn, labelName, "T39_K2", sizeof(propertyValues) / sizeof(DmValueT),
            propertyValues, NULL, 0, NULL, NULL, 0, 10);
        EXPECT_EQ(GMERR_OK, ret);
        RpcSeekFirstOpMsg(rsp);
        DbExecuteAckT *ack = (DbExecuteAckT *)FixBufGetData(rsp, sizeof(DbExecuteAckT));
        EXPECT_EQ(ack->eof, true);
        EXPECT_EQ(ack->fetchRows, 2);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
        QryReleaseRsp(rsp);
    }

    void Update1()
    {
        uint32_t ret;
        char *propertyName[2] = {(char *)"F1", (char *)"F3"};
        DmValueT propertyValue[2];
        propertyValue[0].type = DB_DATATYPE_CHAR;
        propertyValue[0].value.charValue = 'b';
        propertyValue[1].type = DB_DATATYPE_INT64;
        propertyValue[1].value.longValue = 4;

        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 300;

        ret = QryTestUpdateVertex(labelName, indexName, 1, indexPropertyValue, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void Update2()
    {
        uint32_t ret;
        char *propertyName[2] = {(char *)"F1", (char *)"F5"};
        DmValueT propertyValue[2];
        propertyValue[0].type = DB_DATATYPE_CHAR;
        propertyValue[0].value.charValue = 'c';
        propertyValue[1].type = DB_DATATYPE_STRING;
        propertyValue[1].value.strAddr = (void *)"zhangzhang";
        propertyValue[1].value.length = 11;

        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 300;

        ret = QryTestUpdateVertex(labelName, indexName, 1, indexPropertyValue, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void UpdateUniqueIndex()
    {
        uint32_t ret;
        char *propertyName[1] = {(char *)"F5"};
        DmValueT propertyValue[1];
        propertyValue[0].type = DB_DATATYPE_STRING;
        propertyValue[0].value.strAddr = (void *)"zhangzhang";
        propertyValue[0].value.length = 11;

        const char *indexName = "T39_K1";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_CHAR;
        indexPropertyValue[0].value.charValue = 'e';

        ret = QryTestUpdateVertex(labelName, indexName, 1, indexPropertyValue, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void UpdateNonUniqueIndexDup()
    {
        uint32_t ret;
        char *propertyName[1] = {(char *)"F1"};
        DmValueT propertyValue[1];
        propertyValue[0].type = DB_DATATYPE_CHAR;
        propertyValue[0].value.charValue = 'g';

        const char *indexName = "T39_K2";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_INT64;
        indexPropertyValue[0].value.longValue = 5;

        ret = QryTestUpdateVertex(labelName, indexName, 1, indexPropertyValue, sizeof(propertyName) / sizeof(char *),
            propertyName, propertyValue, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void Replace()
    {
        uint32_t ret;
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
        DmValueT propertyValue[3];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 300;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'a';
        propertyValue[2].type = DB_DATATYPE_INT64;
        propertyValue[2].value.longValue = 3;

        FixBufferT *rsp = NULL;
        ret = QryTestReplaceOrMergeVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue,
            MSG_OP_RPC_REPLACE_VERTEX, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        FixBufSeek(rsp, 0);
        MsgHeaderT *header = RpcGetMsgHeader(rsp);
        (void)RpcGetOpHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        uint32_t affectRows, group;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)2, affectRows);
        EXPECT_EQ((uint32_t)8, datalen);
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &group));
        EXPECT_EQ((uint32_t)0, group);

        QryReleaseRsp(rsp);
    }

    void Replace2()
    {
        uint32_t ret;
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
        DmValueT propertyValue[3];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 800;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'i';
        propertyValue[2].type = DB_DATATYPE_INT64;
        propertyValue[2].value.longValue = 33;

        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void Replace3()
    {
        uint32_t ret;
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
        DmValueT propertyValue[3];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 200;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'i';
        propertyValue[2].type = DB_DATATYPE_INT64;
        propertyValue[2].value.longValue = 33;

        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_REPLACE_VERTEX);
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    }

    void Merge()
    {
        uint32_t ret;
        char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
        DmValueT propertyValue[3];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 700;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'l';
        propertyValue[2].type = DB_DATATYPE_INT64;
        propertyValue[2].value.longValue = 33;

        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_MERGE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void Merge2()
    {
        uint32_t ret;
        char *propertyName[2] = {(char *)"F0", (char *)"F3"};
        DmValueT propertyValue[2];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 700;
        propertyValue[1].type = DB_DATATYPE_INT64;
        propertyValue[1].value.longValue = 3;

        FixBufferT *rsp = NULL;
        ret = QryTestReplaceOrMergeVertex(labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue,
            MSG_OP_RPC_MERGE_VERTEX, &rsp);
        EXPECT_EQ(GMERR_OK, ret);

        FixBufSeek(rsp, 0);
        MsgHeaderT *header = RpcGetMsgHeader(rsp);
        (void)RpcGetOpHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        uint32_t affectRows;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)2, affectRows);
        EXPECT_EQ((uint32_t)4, datalen);

        QryReleaseRsp(rsp);
    }

    void Merge3()
    {
        uint32_t ret;
        char *propertyName[2] = {(char *)"F0", (char *)"F1"};
        DmValueT propertyValue[2];
        propertyValue[0].type = DB_DATATYPE_UINT32;
        propertyValue[0].value.uintValue = 200;
        propertyValue[1].type = DB_DATATYPE_CHAR;
        propertyValue[1].value.charValue = 'a';

        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_MERGE_VERTEX);
        EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
    }

    void Merge4()
    {
        uint32_t ret;
        char *propertyName[2] = {(char *)"F1", (char *)"F3"};
        DmValueT propertyValue[2];
        propertyValue[0].type = DB_DATATYPE_CHAR;
        propertyValue[0].value.charValue = 'l';
        propertyValue[1].type = DB_DATATYPE_INT64;
        propertyValue[1].value.longValue = 33;

        ret = QryTestReplaceOrMergeVertex(
            labelName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_MERGE_VERTEX);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    }

    void Delete()
    {
        uint32_t ret;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 100;
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue, NULL, 0, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void DeleteGraphVertex()
    {
        uint32_t ret;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 160;
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue, NULL, QRY_AUTO_DEL_RELATION_VERTEX, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void DeleteUniqueSecondIndex()
    {
        uint32_t ret;
        const char *indexName = "T39_K1";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_CHAR;
        indexPropertyValue[0].value.charValue = 'g';
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue, NULL, 0, 0);
        EXPECT_EQ(GMERR_OK, ret);
    }

    void DeleteNonUniqueSecondIndex()
    {
        uint32_t ret;
        const char *indexName = "T39_K2";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_INT64;
        indexPropertyValue[0].value.longValue = 4;

        FixBufferT *rsp = NULL;
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue, NULL, 0, 0, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufSeek(rsp, 0);
        MsgHeaderT *header = RpcGetMsgHeader(rsp);
        (void)RpcGetOpHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        uint32_t affectRows;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)2, affectRows);
        EXPECT_EQ((uint32_t)4, datalen);
        QryReleaseRsp(rsp);
    }

    void DeleteVertexErr()
    {
        uint32_t ret;
        const char *indexName = "T39_K0";
        DmValueT indexPropertyValue[1];
        indexPropertyValue[0].type = DB_DATATYPE_UINT32;
        indexPropertyValue[0].value.uintValue = 900;
        FixBufferT *rsp = NULL;
        ret = QryTestDeleteVertex(labelName, indexName, 1, indexPropertyValue, NULL, 0, 0, &rsp);
        EXPECT_EQ(GMERR_OK, ret);
        FixBufSeek(rsp, 0);
        MsgHeaderT *header = RpcGetMsgHeader(rsp);
        (void)RpcGetOpHeader(rsp);
        uint32_t datalen = header->size - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE;
        uint32_t affectRows;
        EXPECT_EQ(GMERR_OK, FixBufGetUint32(rsp, &affectRows));
        EXPECT_EQ((uint32_t)0, affectRows);
        EXPECT_EQ((uint32_t)4, datalen);
        QryReleaseRsp(rsp);
    }

    void Batch()
    {
        uint32_t ret;

        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        MsgHeaderT *msgHeader = NULL;
        EXPECT_EQ(GMERR_OK, RpcReserveMsgHeader(&req, &msgHeader, NULL));
        EXPECT_EQ(GMERR_OK, RpcReserveOpHeader(&req, NULL, NULL));
        msgHeader->reqTimeOut = CS_NEVER_TIMEOUT;
        msgHeader->stmtId = 0;
        const char *keyName = "T39_K0";
        TextT labelName = {.len = strlen("labelvertex1") + 1, .str = (char *)"labelvertex1"};
        TextT putText, filterBuf;
        DmIndexKeyT *filter = NULL;

        // 构造insert
        DmVertexLabelT *vertexLabel = NULL;
        DmVertexT *vertex = NULL;
        CataKeyT cataKey;
        CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, ((Session *)conn->session)->namespaceId, labelName.str);
        CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
        DmCreateEmptyVertexWithMemCtx((DbMemCtxT *)dyAlgoCtxVertex, vertexLabel, &vertex);
        // set the porperty of vertex
        DmValueT propertyValue;
        propertyValue.type = DB_DATATYPE_UINT32;
        propertyValue.value.uintValue = 1;
        DmVertexSetPropeByName("F0", propertyValue, vertex);
        propertyValue.type = DB_DATATYPE_CHAR;
        propertyValue.value.charValue = 'd';
        DmVertexSetPropeByName("F1", propertyValue, vertex);
        propertyValue.type = DB_DATATYPE_INT64;
        propertyValue.value.longValue = 1;
        DmVertexSetPropeByName("F3", propertyValue, vertex);
        DmSerializeVertex(vertex, (uint8_t **)&putText.str, &putText.len);

        BatchHeaderT *batchHead = (BatchHeaderT *)FixBufReserveData(&req, sizeof(BatchHeaderT));
        batchHead->totalNum = 2;
        batchHead->totalOpNum = 2;
        batchHead->batchErrCtl = 0;
        batchHead->batchOrder = 0;

        ret = FixBufPutUint32(&req, MSG_OP_RPC_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaCommon.metaId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaCommon.version);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, 1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutText(&req, &putText);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, 0);
        EXPECT_EQ(GMERR_OK, ret);

        // 构造delete
        DmValueT propertyValues;
        propertyValues.type = DB_DATATYPE_UINT32;
        propertyValues.value.uintValue = 1;
        ret = DmCreateIndexKeyByNameWithMemCtx(
            (DbMemCtxT *)dyAlgoCtxVertex, vertexLabel, keyName, &propertyValues, 1, &filter);
        EXPECT_EQ(GMERR_OK, ret);
        filterBuf.len = DmIndexKeyGetSeriBufLength(filter);
        filterBuf.str = (char *)DbDynMemCtxAlloc((DbMemCtxT *)dyAlgoCtxVertex, filterBuf.len);
        ret = DmSerializeIndexKey2InvokerBuf(filter, filterBuf.len, (uint8_t *)filterBuf.str);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, MSG_OP_RPC_DELETE_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaCommon.metaId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaCommon.version);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, vertexLabel->metaVertexLabel->uuid);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(
            GMERR_OK, FixBufPutUint32(&req, vertexLabel->metaVertexLabel->pkIndex->idxLabelBase.indexId));  // index id
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));                                                      // condition
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 1));  // vertex num
        ret = FixBufPutUint32(&req, 0);
        EXPECT_EQ(GMERR_OK, ret);  // scan flag
        ret = FixBufPutText(&req, &filterBuf);
        EXPECT_EQ(GMERR_OK, ret);
        ret = FixBufPutUint32(&req, 0);  // right indexKey
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, FixBufPutUint32(&req, 0));
        DmDestroyIndexKey(filter);
        CataReleaseVertexLabel(vertexLabel);
        DmDestroyVertex(vertex);
        msgHeader->size = FixBufGetPos(&req);
        RpcFillOpHeader(ProtocolPeekFirstOpHeader(&req), MSG_OP_RPC_BATCH,
            req.pos - MSG_HEADER_ALIGN_SIZE - MSG_OP_HEADER_ALIGN_SIZE);
        (void)setStubC((void *)DrtConnWritePack, (void *)DrtConnWriteBatchPackStub);
        (void)setStubC((void *)DrtGetConnFlowCtrlLevel, (void *)DrtGetConnFlowCtrlLevelStub);
        DrtServiceCtxT serviceCtx;
        serviceCtx.ctx = NULL;
        serviceCtx.procStartTime = DbRdtsc();
        DrtProcCtxT procCtx = {0};
        procCtx.conn = conn;
        procCtx.msgHeader = msgHeader;
        procCtx.type = PROC_CTX_TYPE_RPC_MSG;
        (void)setStubC((void *)DrtLongOpLog, (void *)DrtLongOpLogStub);
        FixBufDup(&req, &procCtx.msg);
        (void)setStubC((void *)DrtFreeProcCtx, (void *)DrtFreeProcCtxStub);
        ret = FastpathEntry(&serviceCtx, &procCtx);
        EXPECT_EQ(GMERR_OK, ret);

        DbDynMemCtxFree((DbMemCtxT *)dyAlgoCtxVertex, filterBuf.str);
        QryTestReleaseSession(conn);
    }

    void SeqSortScanVertexWithWrongDirection()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        // begin scan
        ret = QryTestBeginSortScanVertex(conn, labelName, (char *)"F1", 0, NULL, 0, NULL, 1, ORDER_BUTT);
        EXPECT_EQ(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        char *result1 =
            (char *)"Order direction should be ascending and descending when scan sort vertexLabel labelvertex1.";
        EXPECT_STREQ(result1, lastError1->str);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
    }

    void SeqSortScanVertexWithoutPropName()
    {
        uint32_t ret;
        DrtConnectionT *conn = NULL;
        ret = QryTestAllocSession(&conn);
        EXPECT_EQ(GMERR_OK, ret);

        // begin scan
        ret = QryTestBeginSortScanVertex(conn, labelName, NULL, 0, NULL, 0, NULL, 1, ORDER_ASC);
        EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
        TextT *lastError1 = DbGetLastErrorInfo();
        const char *result1 = "Data exception occurs. Fail to get field name when scan sort.";
        EXPECT_STREQ(result1, lastError1->str);

        // release stmt
        ret = QryTestReleaseStmt(conn);
        EXPECT_EQ(GMERR_OK, ret);
        QryTestReleaseSession(conn);
    }
};

TEST_F(UtQueryDMLVertex, QRY_DML_DuplicateIndexName)
{
    uint32_t ret;

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"duplIdxNameLabel",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type":"int32"}
        ],
        "keys":
        [
            {"node":"duplIdxNameLabel", "name":"T39_K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"duplIdxNameLabel", "name":"T39_K0_1", "fields":["F0"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"duplIdxNameLabel", "name":"T39_K1", "fields":["F1"], "index":{"type":"localhash"}},
            {"node":"duplIdxNameLabel", "name":"T39_K1", "fields":["F2"], "index":{"type":"localhash"}}
        ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result = (char *)"Duplicate object. The index name T39_K1 is repeated in vertexLabel "
                                 "duplIdxNameLabel.vertexLabel name:duplIdxNameLabel.";
    EXPECT_STREQ(result, lastError1->str);
}

TEST_F(UtQueryDMLVertex, QRY_DML_BIG_TEST)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    EXPECT_EQ(GMERR_OK, QryTestCreateBaseVertexLabelFirst(cfgJson));
    InsertVertex(300, 'a', 3, "xyz");
    InsertVertex(600, 'e', 4, "zhang");
    InsertVertex(500, 'f', 5, "zhangzhang");
    InsertVertex(400, 'u', 6, "zhangzhangzhang");
    InsertVertex(100, 'p', 7, "zhangzhangzhang");
    InsertVertex(1000, 'q', 4, "zhangzhangzhang");
    InsertVertex(160, 'o', 8, "zhangzhangzhang00");
    InsertVertexWithoutpropertyValue();
    GetAllCount();
    SeqScan();
    SeqSort();
    GetPrimaryCount();
    Uniqidxscan();
    GetSecondryCount();
    SecIdxScan();
    Update1();
    Update2();
    UpdateUniqueIndex();
    UpdateNonUniqueIndexDup();
    Replace();
    Replace2();
    Replace3();
    Merge();
    Merge2();
    Merge3();
    Merge4();
    Delete();
    DeleteGraphVertex();
    DeleteUniqueSecondIndex();
    DeleteNonUniqueSecondIndex();
    DeleteVertexErr();
    Batch();
}

#ifndef FEATURE_PERSISTENCE
TEST_F(UtQueryDMLVertex, QRY_DML_WithLocal)
{
    CreateVertexLabel3();
    InsertVertex3(300, 'a', 300, "xyz");
    InsertVertex3(400, 'b', 400, "xyz");
    InsertVertex3(500, 'b', 400, "xyz");
    InsertVertex3(600, 'c', 500, "xyz");
    InsertVertex3(700, 'd', 600, "xyz");
    RangeScan3();
    FullScan3();
    PointDelete3();
    RangeDelete3();
}
#endif

TEST_F(UtQueryDMLVertex, QRY_DML_DeleteOrUpdateVertexWithLpm)
{
    uint32_t ret;
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
            "type":"record",
            "name":"LPM00",
            "fields":
                [
                    {"name":"vr_id", "type":"uint32", "size":300},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}
                ],
            "keys":
                [
                    {
                        "node":"LPM00",
                        "name":"lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    const char *indexName = "lpm";
    DmValueT leftPropertyValues[4];
    leftPropertyValues[0].type = DB_DATATYPE_UINT32;
    leftPropertyValues[0].value.uintValue = 111;
    leftPropertyValues[1].type = DB_DATATYPE_UINT32;
    leftPropertyValues[1].value.uintValue = 111;
    leftPropertyValues[2].type = DB_DATATYPE_UINT32;
    leftPropertyValues[2].value.uintValue = 111;
    leftPropertyValues[3].type = DB_DATATYPE_UINT8;
    leftPropertyValues[3].value.ucharValue = 11;
    ret = QryTestDeleteVertex((char *)"LPM00", indexName, 4, leftPropertyValues, NULL, 0, 0);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    TextT *lastError = DbGetLastErrorInfo();
    const char *result = (char
            *)"Feature is not supported. Delete or update by Lpm index is not supported.The vertexLabel name is LPM00.";
    EXPECT_STREQ(result, lastError->str);
    ret = QryTestUpdateVertex((char *)"LPM00", indexName, 4, leftPropertyValues, 0, NULL, NULL, 0);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    const char *result1 = (char
            *)"Feature is not supported. Delete or update by Lpm index is not supported.The vertexLabel name is LPM00.";
    EXPECT_STREQ(result1, lastError1->str);

    QryTestDropVertexLabel((char *)"LPM00", true);
}

TEST_F(UtQueryDMLVertex, QRY_DML_InsertVertexWithoutLabelName)
{
    uint32_t ret;
    char *propertyName[4] = {(char *)"F0", (char *)"F1", (char *)"F3", (char *)"F5"};
    DmValueT propertyValue[4];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 600;
    propertyValue[1].type = DB_DATATYPE_UCHAR;
    propertyValue[1].value.ucharValue = 'd';
    propertyValue[2].type = DB_DATATYPE_UINT32;
    propertyValue[2].value.uintValue = 600;
    propertyValue[3].type = DB_DATATYPE_STRING;
    propertyValue[3].value.strAddr = (void *)"xyz";
    propertyValue[3].value.length = 4;

    ret = QryTestInsertVertex(NULL, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    char *result1 = (char *)"Data exception occurs. The labelID is 0.";
    EXPECT_STREQ(result1, lastError1->str);
}

TEST_F(UtQueryDMLVertex, QRY_DML_UpdateVertexWithoutLabelName)
{
    uint32_t ret;
    char *propertyName[2] = {(char *)"F1", (char *)"F3"};
    DmValueT propertyValue[2];
    propertyValue[0].type = DB_DATATYPE_CHAR;
    propertyValue[0].value.charValue = 'b';
    propertyValue[1].type = DB_DATATYPE_INT64;
    propertyValue[1].value.longValue = 4;

    const char *indexName = "T39_K0";
    DmValueT indexPropertyValue[1];
    indexPropertyValue[0].type = DB_DATATYPE_UINT32;
    indexPropertyValue[0].value.uintValue = 300;

    ret = QryTestUpdateVertex(
        NULL, indexName, 1, indexPropertyValue, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, 0);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    char *result1 = (char *)"Data exception occurs. The labelID is 0.";
    EXPECT_STREQ(result1, lastError1->str);
}

TEST_F(UtQueryDMLVertex, QRY_DML_SeqSortScanVertexWithTree)
{
    uint32_t ret;
    DrtConnectionT *conn = NULL;
    ret = QryTestAllocSession(&conn);
    EXPECT_EQ(GMERR_OK, ret);

    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    char *labelJson = (char *)R"([{
    "type":"record",
    "name":"duplIdxNameLabel",
    "fields": [
        { "name": "c0", "type": "uint32" },
        {
            "name": "c1",
            "type": "record",
            "array": true,
            "size": 1024,
            "fields": [
                { "name": "f1", "type": "uint32" },
                {
                    "name": "f2",
                    "type": "record",
                    "vector": true,
                    "size": 1024,
                    "fields": [
                        { "name": "b1", "type": "uint32", "nullable": true },
                        { "name": "b2", "type": "uint32" }
                    ]
                }
            ]
        }
    ]
    }])";

    ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // begin scan
    ret = QryTestBeginSortScanVertex(conn, (char *)"duplIdxNameLabel", (char *)"F1", 0, NULL, 0, NULL, 1, ORDER_ASC);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    char *result1 = (char
            *)"Feature is not supported. The label duplIdxNameLabel should be vertex label, don't support tree model.";
    EXPECT_STREQ(result1, lastError1->str);

    QryTestDropVertexLabel((char *)"duplIdxNameLabel", true);

    // release stmt
    ret = QryTestReleaseStmt(conn);
    EXPECT_EQ(GMERR_OK, ret);
    QryTestReleaseSession(conn);
}

// 可以独立执行
TEST_F(UtQueryDMLVertex, QRY_DML_MergeVertexWithoutLabelName)
{
    uint32_t ret;
    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 200;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'i';
    propertyValue[2].type = DB_DATATYPE_INT64;
    propertyValue[2].value.longValue = 33;

    ret = QryTestReplaceOrMergeVertex(
        NULL, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue, MSG_OP_RPC_MERGE_VERTEX);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    TextT *lastError1 = DbGetLastErrorInfo();
    char *result1 = (char *)"Data exception occurs. The labelID is 0.";
    EXPECT_STREQ(result1, lastError1->str);
}

TEST_F(UtQueryDMLVertex, QRY_DML_DeleteVertexNonUniqueIndexFailed)
{
    uint32_t ret;
    const char *vertexLabelName = "labelvertex1";
    DbSrvDfgmtTaskMgrInit(NULL);
    QryTestDropBaseVertexLabelFirst();
    char *cfgJson = (char *)"{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    EXPECT_EQ(GMERR_OK, QryTestCreateBaseVertexLabelFirst(cfgJson));
    InsertVertex(300, 'a', 4, "xyz");
    InsertVertex(600, 'e', 4, "zhang");
    InsertVertex(500, 'f', 4, "zhangzhang");

    g_enterDeleteTime = 0;
    g_stubId = setStubC((void *)QryExecuteDeleteVertexInfo, (void *)QryExecuteDeleteVertexInfoStub);

    const char *indexName = "T39_K2";
    DmValueT indexPropertyValue[1];
    indexPropertyValue[0].type = DB_DATATYPE_INT64;
    indexPropertyValue[0].value.longValue = 4;

    FixBufferT *rsp = NULL;
    ret = QryTestDeleteVertex((char *)vertexLabelName, indexName, 1, indexPropertyValue, NULL, 0, 0, &rsp);
    EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);

    DmVertexLabelT *vertexLabel = NULL;
    CataKeyT cataKey;
    CataSetKeyForLabel(&cataKey, DEFAULT_DATABASE_ID, 0, vertexLabelName);
    ret = (Status)CataGetNamespaceIdByName(NULL, "public", &cataKey.nspId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = (Status)CataGetVertexLabelByName(NULL, &cataKey, &vertexLabel);
    EXPECT_EQ(GMERR_OK, ret);
#ifndef FEATURE_PERSISTENCE
    DmCheckInfoT *checkInfo = &vertexLabel->commonInfo->accCheckAddr->checkInfo[0];
    HeapPerfStatT perfStat;
    HeapCfgStatT heapCfgStat = {0};
    HeapCntrAcsInfoT heapCntrAcsInfo = {.heapShmAddr = vertexLabel->commonInfo->heapInfo.heapShmAddr,
        .isPersistent = false,
        .isUseRsm = false,
        .instanceId = DbGetProcGlobalId()};
    ret = HeapLabelGetPerfStat(&heapCntrAcsInfo, &perfStat, &heapCfgStat);  // 获取统计信息
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(perfStat.phyItemNum, checkInfo->recordCnt);
#endif
    QryTestDropBaseVertexLabelFirst();
}

#ifndef FEATURE_PERSISTENCE
extern "C" Status QryRebuildVertexLabelIndex(uint16_t instanceId);
TEST_F(UtQueryDMLVertex, RebuildIndex4NormalVrtxLabel)
{
    char *cfgJson = (char *)"{\"max_record_count\":1000}";

    char *labelJson = (char *)R"([{
        "type":"record",
        "name":"RebuildIndex",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"char"},
            {"name":"F2", "type":"boolean", "default":true},
            {"name":"F3", "type":"int64"},
            {"name":"F4", "type":"time", "nullable":true},
            {"name":"F5", "type":"string", "size":20, "nullable":true},
            {"name":"F6", "type":"fixed", "default":"fff", "size":3},
            {"name":"F77", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"RebuildIndex", "name":"K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}},
            {"node":"RebuildIndex", "name":"K1", "fields":["F1"], "index":{"type":"localhash"}, "constraints":{"unique":true}},
            {"node":"RebuildIndex", "name":"K2", "fields":["F3"], "index":{"type":"localhash"}, "constraints":{"unique":false}}
        ]
    }])";

    // create label
    Status ret = QryTestCreateVertexLabel(cfgJson, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // insert data1
    char *tableName = (char *)"RebuildIndex";
    char *propertyName[3] = {(char *)"F0", (char *)"F1", (char *)"F3"};
    DmValueT propertyValue[3];
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 200;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'b';
    propertyValue[2].type = DB_DATATYPE_FLOAT;
    propertyValue[2].value.floatValue = 2.00;
    ret = QryTestInsertVertex(tableName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);
    // insert data2
    propertyValue[0].type = DB_DATATYPE_UINT32;
    propertyValue[0].value.uintValue = 300;
    propertyValue[1].type = DB_DATATYPE_CHAR;
    propertyValue[1].value.charValue = 'c';
    propertyValue[2].type = DB_DATATYPE_FLOAT;
    propertyValue[2].value.floatValue = 3.00;
    ret = QryTestInsertVertex(tableName, sizeof(propertyName) / sizeof(char *), propertyName, propertyValue);
    EXPECT_EQ(GMERR_OK, ret);

    ret = QryRebuildVertexLabelIndex(GET_INSTANCE_ID);
    EXPECT_EQ(ret, GMERR_UNIQUE_VIOLATION);
}
#endif
