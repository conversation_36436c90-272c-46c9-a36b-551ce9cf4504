#include "gtest/gtest.h"
#include <limits.h>
#include <unistd.h>
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "dm_data_basic.h"
#include "connection/clt_conn.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "dm_data_prop.h"
#include "db_utils.h"
#include "stub.h"
#include "db_list.h"
#include "clt_stmt.h"
#include "adpt_sleep.h"
#include "storage_st_common.h"
#include "file_op.h"
#include "adpt_string.h"

#define MAX_CMD_SIZE 1024
extern const char *serverLocator;
extern const char *userName;
extern const char *pwd;
char g_command[MAX_CMD_SIZE];

char *keyInfo = (char *)"keykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeyk"
                        "eykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeykeyke"
                        "ykeykeykeykeykeykeykeykeykeykeykeykeykey";

#ifndef FEATURE_PERSISTENCE
class StStorage_Defragment : public StStorage {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    void SetUp()
    {
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
    }
    void TearDown()
    {
        ShutDownDbServer();
        SaveLogAfterFailed();
    }
};

char g_condition[100];

static void CLientInit(GmcConnT **connection, GmcStmtT **stmt)
{
    CreateSyncConnectionAndStmt(connection, stmt);
}

static void CLientFinal(GmcConnT *connection, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    int32_t ret = GmcDisconnect(connection);
    EXPECT_EQ(ret, GMERR_OK);
}

static int32_t InsertVertex(GmcStmtT *stmt, uint64_t valueF0, uint64_t valueF1F2)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &valueF0, sizeof(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &valueF1F2, sizeof(valueF1F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &valueF1F2, sizeof(valueF1F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

static int32_t DeleteVertex(GmcStmtT *stmt, uint64_t value)
{
    // 删除顶点
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "T35_K0"));
    int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

static int32_t FetchVertex(GmcStmtT *stmt, uint64_t priK)
{
    // 查询
    char lable_name_PK[] = "T35_K0";
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, lable_name_PK));
    int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &priK, sizeof(uint64_t));
    do {
        ret = GmcExecute(stmt);
        // EXPECT_EQ(GMERR_OK, ret); // todo 此处记了遗留问题：hashEntry的插入顺序需要保证
    } while (ret != GMERR_OK);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    // 校验F1的值
    unsigned int sizeValue = 0;
    bool isNull = false;
    ret = GmcGetVertexPropertySizeByName(stmt, "F1", &sizeValue);
    EXPECT_EQ(GMERR_OK, ret);

    char *pValue = (char *)malloc(sizeValue);
    EXPECT_NE((void *)NULL, pValue);
    ret = GmcGetVertexPropertyByName(stmt, "F1", pValue, sizeValue, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    if (isNull == false) {
        EXPECT_EQ(priK, (*(uint64_t *)pValue));
    } else {
        EXPECT_EQ(0, 1);
    }
    free(pValue);
    return ret;
}

static int32_t InsertKv(GmcStmtT *stmt, uint64_t value)
{
    // data 1 set
    char key[256];
    sprintf(key, "%" PRIu64 "%s", value, keyInfo);
    int32_t ret = GmcKvSet(stmt, key, strlen(key) + 1, key, strlen(key));
    return ret;
}

static int32_t DeleteKv(GmcStmtT *stmt, uint64_t value)
{
    char key[256];
    sprintf(key, "%" PRIu64 "%s", value, keyInfo);
    return GmcKvRemove(stmt, key, strlen(key) + 1);
}

static int32_t FetchKv(GmcStmtT *stmt, uint64_t value)
{
    char key[256];
    sprintf(key, "%" PRIu64 "%s", value, keyInfo);
    char output[256] = {};
    uint32_t outputLen = sizeof(output);
    int32_t ret = GmcKvGet(stmt, key, strlen(key) + 1, output, &outputLen);
    EXPECT_EQ(strlen(key), outputLen);
    EXPECT_STREQ(key, output);
    return ret;
}
#ifndef RTOSV2X
static const char *g_label_config = R"({"max_record_count":10000, "defragmentation":true})";
static const char *g_label_name = "TestDefragmentation";
static const char *g_kv_config = R"({"max_record_count":100000, "defragmentation":true})";
static const char *g_kv_name = "TestKvDefragmentation";
static const char *g_merge_label_name = "TestDefragmentationWithStatusMerge";
static const char *g_merge_label_config =
    R"({"max_record_count":10000, "defragmentation":true, "status_merge_sub":true})";
TEST_F(StStorage_Defragment, defragmentation)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT));
    printf("begin Insert\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Insert : %d\n", i);
        }
        ret = InsertVertex(stmt, i, i);
        ASSERT_EQ(GMERR_OK, ret);
    }
#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE));
    // 此处删除数据前1000条和后2000条的删除间隔不一致，是为了让所有页的空闲等级不是一样的
    // 缩容查找页是不会在自己所在的空闲等级的页
    printf("begin Delete\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Delete : %d\n", i);
        }
        if (i % 5 == 0 && i < 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 != 0 && i >= 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 预期触发一次缩容
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN));
    printf("begin Fetch\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Fetch : %d\n", i);
        }
        if (i % 5 != 0 && i < 1000) {
            ret = FetchVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 == 0 && i >= 1000) {
            ret = FetchVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    DbSleep(200);  // 等待压缩完成

    printf("--------------------after first delete---------------------------\n");

#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif

    // 校验调度任务统计
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_TYPE", cmdOutput2, 64);
    uint32_t taskType = atoi(cmdOutput2);
    ASSERT_EQ(taskType, 0u);

    // 检查后台压缩线程的运行状态
    int32_t retry = 5;
    while (true) {
        if (retry > 0) {
            (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
            GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_STATUS", cmdOutput2, 64);
            if (strncmp(" finished", cmdOutput2, strlen(" finished")) != 0) {
                printf("retry: %d, TASK_STATUS:%s\n", retry, cmdOutput2);
            } else {
                break;
            }
        } else {
            ASSERT_EQ(0, 1);
        }
        DbSleep(200);
        retry--;
    }
    // 获取 压缩次数
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    const char *filter = "LABEL_NAME=\'TestDefragmentation\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter, cmdOutput2, 64);  // 过滤默认KV表
    uint32_t defragmentationCnt = atoi(cmdOutput2);
    ASSERT_GE(defragmentationCnt, (uint32_t)1);
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "CREATE_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "DESTROY_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    printf("Delete all remaining vertex \n");
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE));
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 5 != 0 && i < 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 == 0 && i >= 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    DbSleep(200);  // 等待压缩完成
    printf("--------------------after second delete---------------------------\n");

#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter, cmdOutput2, 64);  // 过滤默认KV表
    defragmentationCnt = atoi(cmdOutput2);
    EXPECT_EQ(true, defragmentationCnt >= 2);  // 预计触发第二次缩容，是表清空时触发的

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput2, 64);  // 过滤默认KV表
    uint32_t pageCount = atoi(cmdOutput2);
    ASSERT_EQ((uint32_t)0, pageCount);  // 预计此时全部回收

    GmcFreeIndexKey(stmt);
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);
}

// Insert KV
void testInsertVertexKV(GmcStmtT *stmt, uint32_t insert_times)
{
    uint32_t count = 0;
    char keyName[10] = {0};
    GmcKvTupleT kvInfo = {0};
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < 1024 * insert_times; i++) {
        snprintf(keyName, sizeof(keyName), "Key_%d", i);
        char *Keyvalue = (char *)malloc(1024);
        memset(Keyvalue, 'A', 1024);
        kvInfo.key = keyName;
        kvInfo.keyLen = strlen(keyName);
        kvInfo.value = Keyvalue;
        kvInfo.valueLen = 1024;
        ret = GmcKvSet(stmt, keyName, strlen(keyName) + 1, Keyvalue, 1024);
        EXPECT_EQ(GMERR_OK, ret);
        memset(&kvInfo, 0, sizeof(GmcKvTupleT));
        memset(keyName, 0, 10);
        free(Keyvalue);
    }
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("insert keys num is:%d\n", count);
}

void testDeleteVertexKV(GmcStmtT *stmt, uint32_t times)
{
    char ks[16] = {0};
    uint32_t count = 0;
    Status ret = GMERR_OK;
    ASSERT_NE((void *)NULL, stmt);
    for (uint32_t i = 0; i < times; i++) {
        if (i % 2 == 0) {
            snprintf(ks, sizeof(ks), "Key_%d", i);
            ret = GmcKvRemove(stmt, ks, strlen(ks) + 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcKvTableRecordCount(stmt, &count);
    EXPECT_EQ(GMERR_OK, ret);
    printf("after delete num is:%d\n", count);
}
// kv表触发缩容
TEST_F(StStorage_Defragment, defragmentationKvLabel)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"  "
                            "\"minFragmentationRateThreshold=1\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GMERR_OK;
    // GmcInit 内部已经调用了DbCommInit()函数
    ASSERT_EQ(GMERR_OK, GmcInit());
    CLientInit(&connection, &stmt);
    char kvLableName[128] = "KV5";
    // MS config
    const char *msConfig = "{\"max_record_num\" : 5000000000, \"isFastReadUncommitted\":1,\"defragmentation\":true }";
    ret = GmcKvCreateTable(stmt, kvLableName, msConfig);
    ASSERT_EQ(GMERR_OK, ret);

    // 打开kv表
    ret = GmcKvPrepareStmtByLabelName(stmt, kvLableName);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t insertTimes = 4;
    uint32_t deleteTimes = 1536 * 2;
    // 写数据
    testInsertVertexKV(stmt, insertTimes);

    testDeleteVertexKV(stmt, deleteTimes);
    // 检查缩容
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'KV5\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t defragmentationCnt = atoi(cmdOutput);
    ASSERT_GE(defragmentationCnt, (uint32_t)1);
    ret = GmcKvDropTable(stmt, kvLableName);
    ASSERT_EQ(GMERR_OK, ret);
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);
}

TEST_F(StStorage_Defragment, defragmentationWithStatusMergeLabel)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    int ret = GMERR_OK;
    // GmcInit 内部已经调用了DbCommInit()函数
    ASSERT_EQ(GMERR_OK, GmcInit());
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentationWithStatusMerge.gmjson");
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_schema.c_str(), g_merge_label_config));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_INSERT));
    printf("begin Insert\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Insert : %d\n", i);
        }
        ASSERT_EQ(GMERR_OK, InsertVertex(stmt, i, i));
    }
#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_DELETE));
    // 此处删除数据前1000条和后2000条的删除间隔不一致，是为了让所有页的空闲等级不是一样的
    // 缩容查找页是不会在自己所在的空闲等级的页
    printf("begin Delete\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Delete : %d\n", i);
        }
        if (i % 5 == 0 && i < 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 != 0 && i >= 1000) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    system("gmsysview -q V\\$STORAGE_HEAP_STAT");
    // 预期触发一次缩容
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_SCAN));
    printf("begin Fetch\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Fetch : %d\n", i);
        }
        if (i % 5 != 0 && i < 1000) {
            ret = FetchVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 == 0 && i >= 1000) {
            ret = FetchVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    DbSleep(200);  // 等待压缩完成
    printf("--------------------after first delete---------------------------\n");

#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif

    // 校验调度任务统计
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_TYPE", cmdOutput2, 64);
    uint32_t taskType = atoi(cmdOutput2);
    ASSERT_EQ(taskType, 0u);

    // 检查后台压缩线程的运行状态
    int32_t retry = 5;
    while (true) {
        if (retry > 0) {
            (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
            GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_STATUS", cmdOutput2, 64);
            if (strncmp(" finished", cmdOutput2, strlen(" finished")) != 0) {
                printf("retry: %d, TASK_STATUS:%s\n", retry, cmdOutput2);
            } else {
                break;
            }
        } else {
            ASSERT_EQ(0, 1);
        }
        DbSleep(200);
        retry--;
    }
    // 获取 压缩次数
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    const char *filter = "LABEL_NAME=\'TestDefragmentationWithStatusMerge\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter, cmdOutput2, 64);  // 过滤默认KV表
    uint32_t defragmentationCnt = atoi(cmdOutput2);
    ASSERT_GE(defragmentationCnt, (uint32_t)1);
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "CREATE_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "DESTROY_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);
    GmcFreeIndexKey(stmt);
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);
}

bool g_isInsertReady = false;
bool g_isDeleteReady = false;
int g_dataCount = 9000;
uint64_t g_valueF1F2 = 10000;
int g_valueMask = 3;

void *ThreadInsertAndDel(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    int ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT));
    for (int i = 0; i < g_dataCount; i++) {
        if (i % g_valueMask == 0) {
            ret = InsertVertex(stmt, i, g_valueF1F2);
        } else {
            ret = InsertVertex(stmt, i, i);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    g_isInsertReady = true;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE));
    for (int i = g_dataCount - 1; i >= 0; i--) {
        if (i % g_valueMask != 0) {
            ret = DeleteVertex(stmt, i);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    g_isDeleteReady = true;
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadHashClusterRead(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &g_valueF1F2, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashclusterIndex");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    while (!g_isDeleteReady) {
    };
    bool isFinish = false;
    int count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK or isFinish) {
            EXPECT_EQ(g_dataCount / (int)g_valueMask, count);
            break;
        }
        count++;
    }
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadLocalHashRead(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &g_valueF1F2, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhashIndex");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    while (!g_isDeleteReady) {
    };
    bool isFinish = false;
    int count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK or isFinish) {
            EXPECT_EQ(g_dataCount / (int)g_valueMask, count);
            break;
        }
        count++;
    }
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadScanButNotFinish(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    sleep(20);  // 持有scan cursor 20s，观测服务端是否会挂死
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

TEST_F(StStorage_Defragment, defragAndHashclusterRead)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程1：整理时并发hashcluster读未删除的3000条，预期读到3000条记录
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDel, NULL);
    pthread_create(&threads[1], NULL, ThreadHashClusterRead, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, "TestDefragmentation"));
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, defragAndLocalhashRead)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程1：整理时并发Localhash读未删除的3000条，预期读到3000条记录
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDel, NULL);
    pthread_create(&threads[1], NULL, ThreadLocalHashRead, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, "TestDefragmentation"));

    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, defragAndScanNotFinish)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程3：开启全表扫描后未结束，持有scan cursor，预期服务端不挂死
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDel, NULL);
    pthread_create(&threads[1], NULL, ThreadScanButNotFinish, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, g_label_name));

    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}

void *ThreadInsertAndDelStatusMerge(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    int ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_INSERT));
    for (int i = 0; i < g_dataCount; i++) {
        if (i % g_valueMask == 0) {
            ret = InsertVertex(stmt, i, g_valueF1F2);
        } else {
            ret = InsertVertex(stmt, i, i);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("gmsysview count");
    g_isInsertReady = true;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_DELETE));
    for (int i = g_dataCount - 1; i >= 0; i--) {
        if (i % g_valueMask != 0) {
            ret = DeleteVertex(stmt, i);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    system("gmsysview count");
    g_isDeleteReady = true;
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadHashClusterReadStatusMerge(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &g_valueF1F2, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "hashclusterIndex");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    while (!g_isDeleteReady) {
    };
    bool isFinish = false;
    int count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK or isFinish) {
            if (isFinish) {
                printf("TRUE");
            } else {
                printf("FALSE");
            }
            EXPECT_EQ(g_dataCount / (int)g_valueMask, count);
            break;
        }
        count++;
    }
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadLocalHashReadStatusMerge(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &g_valueF1F2, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhashIndex");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    while (!g_isDeleteReady) {
    };
    bool isFinish = false;
    int count = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK or isFinish) {
            EXPECT_EQ(g_dataCount / (int)g_valueMask, count);
            break;
        }
        count++;
    }
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

void *ThreadScanButNotFinishStatusMerge(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    while (!g_isInsertReady) {
    };
    int ret = GmcPrepareStmtByLabelName(stmt, g_merge_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);  // alloc cursor
    EXPECT_EQ(GMERR_OK, ret);
    sleep(20);  // 持有scan cursor 20s，观测服务端是否会挂死
    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    return NULL;
}

TEST_F(StStorage_Defragment, defragAndHashclusterReadStatusMerge)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentationWithStatusMerge.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_merge_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程1：整理时并发hashcluster读未删除的3000条，预期读到3000条记录
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDelStatusMerge, NULL);
    pthread_create(&threads[1], NULL, ThreadHashClusterReadStatusMerge, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, g_merge_label_name));
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, defragAndLocalhashReadStatusMerge)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentationWithStatusMerge.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_merge_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程1：整理时并发Localhash读未删除的3000条，预期读到3000条记录
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDelStatusMerge, NULL);
    pthread_create(&threads[1], NULL, ThreadLocalHashReadStatusMerge, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, g_merge_label_name));

    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}
TEST_F(StStorage_Defragment, defragAndScanNotFinishStatusMerge)
{
    g_isInsertReady = false;
    g_isDeleteReady = false;
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentationWithStatusMerge.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_merge_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    // 线程0：插入9000行记录，删除6000行记录，触发整理
    // 线程3：开启全表扫描后未结束，持有scan cursor，预期服务端不挂死
    int threadsNum = 2;
    pthread_t threads[threadsNum] = {0};
    pthread_create(&threads[0], NULL, ThreadInsertAndDelStatusMerge, NULL);
    pthread_create(&threads[1], NULL, ThreadScanButNotFinishStatusMerge, NULL);

    for (int i = 0; i < threadsNum; i++) {
        pthread_join(threads[i], NULL);
    }

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, g_merge_label_name));

    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    ASSERT_EQ(GMERR_OK, ret);
}

static bool CheckCommand()
{
    char buf[10000] = {};
    const char *command = "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO";
    FILE *file = popen(command, "r");
    fread(buf, sizeof(buf) - 1, 1, file);
    pclose(file);
    if (strstr(buf, g_label_name)) {
        printf("CheckCommand unsuccessful,print info:\n%s\n", buf);
        return false;
    }
    return true;
}

TEST_F(StStorage_Defragment, defragmentationRefcountCheck)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT));
    printf("begin Insert\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Insert : %d\n", i);
        }
        ret = InsertVertex(stmt, i, i);
        ASSERT_EQ(GMERR_OK, ret);
    }
#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE));
    printf("begin Delete\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Delete : %d\n", i);
        }
        if (i % 5 != 0) {
            ret = DeleteVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 预期触发一次缩容
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN));
    printf("begin Fetch\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Fetch : %d\n", i);
        }
        if (i % 5 == 0) {
            ret = FetchVertex(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    DbSleep(200);  // 等待压缩完成

    printf("--------------------after first delete---------------------------\n");

#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$MEM_COMPACT_TASKS_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif

    // 校验调度任务统计
    char cmdOutput2[64] = {0};
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_TYPE", cmdOutput2, 64);
    uint32_t taskType = atoi(cmdOutput2);
    ASSERT_EQ(taskType, 0u);

    // 检查后台压缩线程的运行状态
    int32_t retry = 5;
    while (true) {
        if (retry > 0) {
            (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
            GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_STATUS", cmdOutput2, 64);
            if (strncmp(" finished", cmdOutput2, strlen(" finished")) != 0) {
                printf("retry: %d, TASK_STATUS:%s\n", retry, cmdOutput2);
            } else {
                break;
            }
        } else {
            ASSERT_EQ(0, 1);
        }
        DbSleep(200);
        retry--;
    }
    // 获取 压缩次数
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    const char *filter = "LABEL_NAME=\'TestDefragmentation\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", filter, cmdOutput2, 64);  // 过滤默认KV表
    uint32_t defragmentationCnt = atoi(cmdOutput2);
    ASSERT_GE(defragmentationCnt, (uint32_t)1);
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "CREATE_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "DESTROY_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    // 压缩结束后删除表,最后判断是否物理删除以校验引用计数正确性
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, g_label_name));
    DbSleep(500);

    GmcFreeIndexKey(stmt);
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);

    EXPECT_EQ(true, CheckCommand());
}

#if (defined RTOSV2 || defined RTOSV2X)
#else
TEST_F(StStorage_Defragment, PageReCycle)
{
    StartDbServerWithConfig(NULL);  // 不开启缩容，用默认的页回收机制回收内存
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    string test_schema = GetFileContext("./009_table_vacuum/allowlist/defragmentation.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT));
    printf("begin Insert\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Insert : %d\n", i);
        }
        ret = InsertVertex(stmt, i, i);
        ASSERT_EQ(GMERR_OK, ret);
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE));
    printf("begin Delete\n");
    for (int32_t i = 0; i < 3000; i++) {
        if (i % 1000 == 0) {
            printf("Delete : %d\n", i);
        }
        ret = DeleteVertex(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf("--------------------after delete all pages---------------------------\n");

#if (defined RTOSV2 || defined RTOSV2X)
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD -s channel: > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#else
    ret = system("gmsysview -q V\\$STORAGE_HEAP_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_FSM_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$STORAGE_MEMDATA_STAT -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
    ret = system("gmsysview -q V\\$SERVER_MEMORY_OVERHEAD -s usocket:/run/verona/unix_emserver > /dev/null");
    EXPECT_EQ(ret, GMERR_OK);
#endif

    char cmdOutput[64] = {0};
    const char *filter = "LABEL_NAME=\'TestDefragmentation\'";

    // 校验heap的页数量
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "PAGE_COUNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t pageCount = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, pageCount);

    // 校验FSM的页数量
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_FSM_STAT", "ACTUAL_PAGE_COUNT", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t actualPageCount = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, actualPageCount);

    // 校验heap的使用空间
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$SERVER_MEMORY_OVERHEAD", "HEAP_SHM", filter, cmdOutput, 64);  // 过滤默认KV表
    uint32_t heapShm = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, heapShm);

    GmcFreeIndexKey(stmt);
    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);
}

TEST_F(StStorage_Defragment, KvDefragmentation)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"minFragmentationMemThreshold=1\" \"workerHungThreshold=3,4,5\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    // GmcInit 内部已经调用了DbCommInit()函数
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    CLientInit(&connection, &stmt);

    ret = GmcKvCreateTable(stmt, g_kv_name, g_kv_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, g_kv_name);
    EXPECT_EQ(GMERR_OK, ret);

    printf("begin Insert\n");
    int32_t kvNum = 3000;
    for (int32_t i = 0; i < kvNum; i++) {
        if (i % 1000 == 0) {
            printf("Insert : %d\n", i);
        }
        ret = InsertKv(stmt, i);
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 此处删除数据前1000条和后2000条的删除间隔不一致，是为了让所有页的空闲等级不是一样的
    // 缩容查找页是不会在自己所在的空闲等级的页
    printf("begin Delete\n");
    for (int32_t i = 0; i < kvNum; i++) {
        if (i % 1000 == 0) {
            printf("Delete : %d\n", i);
        }
        if (i % 5 == 0 && i < 1000) {
            ret = DeleteKv(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 != 0 && i >= 1000) {
            ret = DeleteKv(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    // 预期触发一次缩容
    printf("begin Fetch\n");
    for (int32_t i = 0; i < kvNum; i++) {
        if (i % 1000 == 0) {
            printf("Fetch : %d\n", i);
        }
        if (i % 5 != 0 && i < 1000) {
            ret = FetchKv(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        } else if (i % 5 == 0 && i >= 1000) {
            ret = FetchKv(stmt, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }

    DbSleep(200);  // 等待压缩完成

    // 获取 压缩次数
    char cmdOutput2[64] = {0};
    const char *kv_filter = "LABEL_NAME=\'TestKvDefragmentation\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", "DEFRAGMENTATION_CNT", kv_filter, cmdOutput2, 64);
    uint32_t defragmentationCnt = atoi(cmdOutput2);
    ASSERT_GE(defragmentationCnt, 1u);
    // 校验调度任务统计
    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_TYPE", cmdOutput2, 64);
    uint32_t taskType = atoi(cmdOutput2);
    ASSERT_EQ(taskType, 0u);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "TASK_STATUS", cmdOutput2, 64);
    if (strncmp(" finished", cmdOutput2, strlen(" finished")) != 0) {
        printf("TASK_STATUS:%s\n", cmdOutput2);
    }
    ASSERT_EQ(strncmp(" finished", cmdOutput2, strlen(" finished")), 0);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "CREATE_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    (void)memset_s(cmdOutput2, sizeof(cmdOutput2), 0, sizeof(cmdOutput2));
    GetViewFieldResult("V\\$MEM_COMPACT_TASKS_STAT", "DESTROY_COUNT", cmdOutput2, 64);
    ASSERT_EQ(atoi(cmdOutput2), 1);

    CLientFinal(connection, stmt);
    ret = GmcUnInit();
    assert(ret == 0);
}

#define LPM_IPV4_INSERT_NUM 20000

Status Lpm4StBatchDelete(GmcStmtT *stmt, uint32_t vrId, uint32_t vrfId)
{
    uint32_t basePriKeyVal = (vrId << 22 | vrfId << 12);
    int ret = 0;
    for (uint32_t i = 1; i < LPM_IPV4_INSERT_NUM; i++) {
        if (i % 30 == 0) {
            continue;
        }
        ret = GmcPrepareStmtByLabelName(stmt, "Lpm4IndexInsert", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priKeyVal = basePriKeyVal + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priKeyVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "Lpm4IndexInsert_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

static Status Lpm4StInsertAllArt(GmcStmtT *stmt, uint32_t vrId, uint32_t vrfId, int insertNum = LPM_IPV4_INSERT_NUM)
{
    uint32_t basePriKeyVal = (vrId << 22 | vrfId << 12);
    uint8_t mask_len = 16;
    int ret = 0;
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, "Lpm4IndexInsert", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priKeyVal = basePriKeyVal + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priKeyVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t ipv4 = i << 16;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &ipv4, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

TEST_F(StStorage_Defragment, Lpm4IndexScaleIn)
{
    StartDbServerWithConfig("\"minFragmentationRateThreshold=80\" \"minFragmentationMemThreshold=1\" "
                            "\"memCompactEnable=1\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *test_config_json = R"({"max_record_count":30000, "isFastReadUncommitted":1, "defragmentation":true})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Lpm4IndexInsert",
            "fields":
                [
                    {"name":"F0", "type":"uint32","nullable":false},
                    {"name":"vr_id", "type":"uint32"},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}],
            "keys":
                [
                    {
                        "node":"Lpm4IndexInsert",
                        "name":"Lpm4IndexInsert_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"Lpm4IndexInsert",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // 当前用例需要触发缩容，数据量需要更大
    ret = Lpm4StInsertAllArt(stmt, 0, 0, LPM_IPV4_INSERT_NUM + 100);
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -rf gmdb-tmp-release.1.log");
    system("rm -rf gmdb-tmp-release.2.log");
    system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT | grep PAGE_RELEASED_COUNT > gmdb-tmp-release.1.log");
    ret = Lpm4StBatchDelete(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT | grep PAGE_RELEASED_COUNT > gmdb-tmp-release.2.log");

    FILE *fp1 = fopen("./gmdb-tmp-release.1.log", "r");
    ASSERT_TRUE(fp1 != NULL);

    FILE *fp2 = fopen("./gmdb-tmp-release.2.log", "r");
    ASSERT_TRUE(fp2 != NULL);

    char str1[128];
    char str2[128];
    fgets(str1, 128, fp1);
    fgets(str2, 128, fp2);
    printf("before delete: %s\n", str1);
    printf(" after delete: %s\n", str2);
    ASSERT_STREQ("  PAGE_RELEASED_COUNT: 0\n", str1);
    ASSERT_STRNE(str1, str2);

    ret = GmcDropVertexLabel(stmt, "Lpm4IndexInsert");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    system("rm -rf gmdb-tmp-release.1.log");
    system("rm -rf gmdb-tmp-release.2.log");
    fclose(fp1);
    fclose(fp2);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

#define LPM_IPV6_INSERT_NUM 20000

Status Lpm6StInsertAllArt(GmcStmtT *stmt, uint32_t vrId, uint32_t vrfId)
{
    uint32_t basePriKeyVal = (vrId << 22 | vrfId << 12);
    uint8_t mask_len = 127;
    uint64_t ipv6[2] = {100, 100};
    int ret = 0;
    for (uint32_t i = 0; i < LPM_IPV6_INSERT_NUM; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, "Lpm6IndexInsert", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priKeyVal = basePriKeyVal + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &priKeyVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vrId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrfId, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        (*(uint64_t *)ipv6)++;
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, ipv6, sizeof(ipv6));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

Status Lpm6StBatchDelete(GmcStmtT *stmt, uint32_t vrId, uint32_t vrfId)
{
    uint32_t basePriKeyVal = (vrId << 22 | vrfId << 12);
    int ret = 0;
    for (uint32_t i = 1; i < LPM_IPV6_INSERT_NUM; i++) {
        if (i % 30 == 0) {
            continue;
        }
        ret = GmcPrepareStmtByLabelName(stmt, "Lpm6IndexInsert", GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priKeyVal = basePriKeyVal + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &priKeyVal, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "Lpm6IndexInsert_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return ret;
}

TEST_F(StStorage_Defragment, Lpm6IndexScaleIn)
{
    StartDbServerWithConfig("\"minFragmentationRateThreshold=80\" \"minFragmentationMemThreshold=1\" "
                            "\"memCompactEnable=1\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *test_config_json = R"({"max_record_count":20000, "isFastReadUncommitted":1, "defragmentation":true})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Lpm6IndexInsert",
            "fields":
                [
                    {"name":"F0", "type":"uint32","nullable":false},
                    {"name":"vr_id", "type":"uint32"},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name": "dest_ip_addr", "type": "fixed", "size": 16, "nullable":false},
                    {"name":"mask_len", "type":"uint8"}],
            "keys":
                [
                    {
                        "node":"Lpm6IndexInsert",
                        "name":"Lpm6IndexInsert_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"Lpm6IndexInsert",
                        "name":"ip6forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm6_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = Lpm6StInsertAllArt(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("rm -rf gmdb-tmp-release.1.log");
    system("rm -rf gmdb-tmp-release.2.log");
    system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT | grep PAGE_RELEASED_COUNT > gmdb-tmp-release.1.log");
    ret = Lpm6StBatchDelete(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_ART_INDEX_STAT | grep PAGE_RELEASED_COUNT > gmdb-tmp-release.2.log");

    FILE *fp1 = fopen("./gmdb-tmp-release.1.log", "r");
    ASSERT_TRUE(fp1 != NULL);

    FILE *fp2 = fopen("./gmdb-tmp-release.2.log", "r");
    ASSERT_TRUE(fp2 != NULL);

    char str1[128];
    char str2[128];
    fgets(str1, 128, fp1);
    fgets(str2, 128, fp2);
    printf("before delete: %s\n", str1);
    printf(" after delete: %s\n", str2);
    ASSERT_STREQ("  PAGE_RELEASED_COUNT: 0\n", str1);
    ASSERT_STRNE(str1, str2);

    ret = GmcDropVertexLabel(stmt, "Lpm6IndexInsert");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    system("rm -rf gmdb-tmp-release.1.log");
    system("rm -rf gmdb-tmp-release.2.log");
    fclose(fp1);
    fclose(fp2);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

static int32_t ChInsertVertexData(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertVertexData2(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 2;
        uint32_t f3 = i * 3;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            DB_ASSERT(false);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChInsertVertexData2WithVersion(
    GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, uint32_t version)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, version, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 2;
        uint32_t f3 = i * 3;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        if (ret != GMERR_OK) {
            DB_ASSERT(false);
        }
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexData(GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static int32_t ChDeleteVertexDataWithVersion(
    GmcStmtT *stmt, uint32_t start, uint32_t end, const char *labelName, uint32_t version)
{
    int ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, version, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 用例执行时间60s+过长，下架处理
TEST_F(StStorage_Defragment, DISABLED_ClusteredHashWaitScaleIn1)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_vrfid",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F3" ],
            "constraints": { "unique": false }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData2(stmt, 0, 10000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 10000);

    ret = ChDeleteVertexData(stmt, 0, 7000, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    sleep(60);

    ret = ChDeleteVertexData(stmt, 0, 9999, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn2)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_vrfid",
            "index": { "type": "localhash" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": true }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 1000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1000);

    for (uint32_t cycle = 0; cycle < 10; cycle++) {
        ret = ChDeleteVertexData(stmt, 0, 900, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
        ASSERT_EQ(atoi(cmdOutput), 100);

        ret = ChInsertVertexData(stmt, 0, 100, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn3)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 2000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ret = ChDeleteVertexData(stmt, 0, 1900, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 100);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn4)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 1000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1000);

    ret = ChDeleteVertexData(stmt, 0, 1000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn5)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" },
        { "name":"F3", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        },
        {
            "name": "ip4_vrfid",
            "index": { "type": "localhash" },
            "node": "chDefragment",
            "fields": [ "F3" ],
            "config": { "init_hash_capacity": 200000 },
            "constraints": { "unique": true }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData2(stmt, 0, 2000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ret = ChDeleteVertexData(stmt, 0, 1900, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 100);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn6)
{
    StartDbServerWithConfig("\"minFragmentationRateThreshold=30\" \"minFragmentationMemThreshold=10\" "
                            "\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":100000, "defragmentation":true})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"Lpm4IndexInsert",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"vr_id", "type":"uint32"},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name":"dest_ip_addr", "type":"uint32"},
                    {"name":"mask_len", "type":"uint8"}],
            "keys":
                [
                    {
                        "node":"Lpm4IndexInsert",
                        "name":"Lpm4IndexInsert_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"Lpm4IndexInsert",
                        "name":"ip4forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm4_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = Lpm4StInsertAllArt(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 20000);

    ret = Lpm4StBatchDelete(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 667);

    ret = GmcDropVertexLabel(stmt, "Lpm4IndexInsert");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn7)
{
    StartDbServerWithConfig("\"minFragmentationRateThreshold=30\" \"minFragmentationMemThreshold=10\" "
                            "\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":100000, "defragmentation":true})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"Lpm6IndexInsert",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"vr_id", "type":"uint32"},
                    {"name":"vrf_index", "type":"uint32"},
                    {"name": "dest_ip_addr", "type": "fixed", "size": 16, "nullable":false},
                    {"name":"mask_len", "type":"uint8"}],
            "keys":
                [
                    {
                        "node":"Lpm6IndexInsert",
                        "name":"Lpm6IndexInsert_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"Lpm6IndexInsert",
                        "name":"ip6forward_lpm",
                        "fields":["vr_id", "vrf_index", "dest_ip_addr", "mask_len"],
                        "index":{"type":"lpm6_tree_bitmap"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = Lpm6StInsertAllArt(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 20000);

    ret = Lpm6StBatchDelete(stmt, 0, 0);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 667);

    ret = GmcDropVertexLabel(stmt, "Lpm6IndexInsert");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn8)
{
    StartDbServerWithConfig("\"minFragmentationRateThreshold=30\" \"minFragmentationMemThreshold=10\" "
                            "\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *labelName = "sorted_label";
    const char *labelConfig = R"({"max_record_count":4000000, "defragmentation":true})";
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"sorted_label",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":true},
                    {"name":"F2", "type":"uint32", "nullable":true},
                    {"name":"F3", "type":"uint32", "nullable":true},
                    {"name":"F4", "type":"uint32", "nullable":true}
                ],
            "keys":
                [
                    {
                        "node":"sorted_label",
                        "name":"pkIndex",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"sorted_label",
                        "name":"rangeIndex",
                        "fields":["F1", "F4", "F3"],
                        "index":{"type":"local"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelSchema, labelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 1000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 1000);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = nullptr;
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    uint32_t idValue = 0;
    uint32_t batchCount = 1000;
    uint32_t ip4forwardInitSize = 100000;
    for (uint32_t i = 0; i < ip4forwardInitSize / batchCount; i++) {
        for (uint32_t j = 0; j < batchCount; j++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
            idValue++;
        }
        GmcBatchRetT batchRet;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 100000);

    idValue = 0;
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < ip4forwardInitSize / batchCount; i++) {
        for (uint32_t j = 0; j < batchCount; j++) {
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &idValue, sizeof(idValue));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(stmt, "rangeIndex");
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
            idValue++;
        }
        GmcBatchRetT batchRet;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1000);

    GmcDropVertexLabel(stmt, labelName);
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn9)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":false})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_vrfid",
            "index": { "type": "localhash" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": true }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 3000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 3000);

    ret = ChDeleteVertexData(stmt, 0, 2800, labelName);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    // SCALE_IN_THREHOLD_MEET_COUNT的看护后续解决再加上
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 200);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 不开启缩容，空闲页自动回收
TEST_F(StStorage_Defragment, ClusteredHashScaleIn10)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 5000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SEGMENT_PAGE_COUNT", cmdOutput, 64);
    int32_t insertPages = atoi(cmdOutput);

    ret = ChDeleteVertexData(stmt, 0, 5000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(3);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "DIRECTORY_PAGE_COUNT", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SEGMENT_PAGE_COUNT", cmdOutput, 64);
    ASSERT_LT(atoi(cmdOutput), insertPages);

    ret = ChInsertVertexData(stmt, 0, 5000, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

int32_t ChReplaceVertexData(GmcStmtT *stmt, uint32_t recordCount, const char *labelName)
{
    int ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < recordCount; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 1;
        uint32_t f3 = i * 2;
        uint32_t f4 = i * 3;
        uint32_t f5 = i * 4;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(f3));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(f4));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &f5, sizeof(f5));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void TestScaleInWriteAndDeleteRepeat(const char *labelName, const char *labelConfig, const char *label)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    ret = GmcCreateVertexLabel(stmt, label, labelConfig);
    ASSERT_EQ(GMERR_OK, ret);

    // replace1
    ret = ChReplaceVertexData(stmt, 5000, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");

    uint64_t count = 0;
    ret = GmcGetVertexRecordCount(stmt, &count);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, 5000u);

    // 删除数据，触发缩容
    ret = ChDeleteVertexData(stmt, 0, 5000, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");

    char cmdOutput[64] = {0};
    // 校验索引记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_ART_INDEX_STAT", "RECORD_COUNT", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HASH_LINKLIST_INDEX_STAT", "NODE_COUNT", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_VERTEX_COUNT", "record count", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);

    ret = GmcGetVertexRecordCount(stmt, &count);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, 0u);

    DbSleep(1000);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");

    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);

    // replace2
    ret = ChReplaceVertexData(stmt, 5000, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");

    // 校验索引记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_ART_INDEX_STAT", "RECORD_COUNT", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HASH_LINKLIST_INDEX_STAT", "NODE_COUNT", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 校验记录数
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_VERTEX_COUNT", "record count", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    ret = GmcGetVertexRecordCount(stmt, &count);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, 5000u);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

// 缩容场景下，反复写删，第一个二级索引类型为local
TEST_F(StStorage_Defragment, ClusteredHashScaleIn11)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" },
                { "name":"F3", "type":"uint32" },
                { "name":"F4", "type":"uint32" },
                { "name":"F5", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "local1",
                    "index": { "type": "local" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "hashcluster1",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F3" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "localhash1",
                    "index": { "type": "localhash" },
                    "node": "chDefragment",
                    "fields": [ "F4" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "hashcluster2",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F5" ]
                }
            ]
        }])";
    TestScaleInWriteAndDeleteRepeat(labelName, labelConfig, label);
}

// 缩容场景下，反复写删，第一个二级索引类型为localhash
TEST_F(StStorage_Defragment, ClusteredHashScaleIn12)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" },
                { "name":"F3", "type":"uint32" },
                { "name":"F4", "type":"uint32" },
                { "name":"F5", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "localhash1",
                    "index": { "type": "localhash" },
                    "node": "chDefragment",
                    "fields": [ "F4" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "hashcluster1",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F3" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "local1",
                    "index": { "type": "local" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                },
                {
                    "name": "hashcluster2",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F5" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";
    TestScaleInWriteAndDeleteRepeat(labelName, labelConfig, label);
}

static Status ChReplaceVertexData2(
    GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName, bool isDirectWrite)
{
    if (isDirectWrite) {
        EXPECT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_REPLACE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE));
    }
    for (uint32_t i = startCnt; i < startCnt + recordCnt; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 1;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        Status ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ChUpdateVertexData(
    GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName, bool isDirectWrite)
{
    if (isDirectWrite) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_UPDATE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    }
    for (uint32_t i = startCnt; i < startCnt + recordCnt; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 1;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        Status ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ChDeleteVertexData2(
    GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName, bool isDirectWrite)
{
    if (isDirectWrite) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE));
    }
    for (uint32_t i = startCnt; i < startCnt + recordCnt; i++) {
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        Status ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ChDeleteVertexDataByHc(
    GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName, bool isDirectWrite)
{
    if (isDirectWrite) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, DB_MAX_UINT32, GMC_OPERATION_DELETE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE));
    }
    for (uint32_t i = startCnt; i < startCnt + recordCnt; i++) {
        uint32_t f2 = i * 2;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "hashcluster"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        Status ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ChInsertVertexData3(GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = startCnt; i < startCnt + recordCnt; i++) {
        uint32_t f1 = i;
        uint32_t f2 = i * 2;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(f2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ChFetchVertexDataByPk(GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName,
    const char *indexName, uint32_t multiple)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));

    for (uint32_t i = startCnt; i < recordCnt; i++) {
        uint32_t f1 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, indexName));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        bool eof;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

        uint32_t pValue = 0;
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &pValue, sizeof(uint32_t), &isNull));
        if (!isNull) {
            EXPECT_EQ(f1 * multiple, pValue);
        } else {
            EXPECT_EQ(0, 1);
        }
    }
    return GMERR_OK;
}

static Status ChFetchVertexDataByHc(GmcStmtT *stmt, uint32_t startCnt, uint32_t recordCnt, const char *labelName,
    const char *indexName, uint32_t multiple)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));

    for (uint32_t i = startCnt; i < recordCnt; i++) {
        uint32_t f1 = i * multiple;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, indexName));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
        bool eof;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

        uint32_t pValue = 0;
        bool isNull = false;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &pValue, sizeof(uint32_t), &isNull));
        if (!isNull) {
            EXPECT_EQ(f1 / multiple, pValue);
        } else {
            EXPECT_EQ(0, 1);
        }
    }
    return GMERR_OK;
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn013)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));

    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做replace操作
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData2(stmt, 4500, 500, labelName, false));
    // 插入部分数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 1500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ChFetchVertexDataByPk(stmt, 0, 1500, labelName, "pk", 2);
    ChFetchVertexDataByPk(stmt, 4500, 500, labelName, "pk", 1);

    ChFetchVertexDataByHc(stmt, 0, 1500, labelName, "hashcluster", 2);
    ChFetchVertexDataByHc(stmt, 4500, 500, labelName, "hashcluster", 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn014)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做replace操作
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData(stmt, 4500, 500, labelName, false));
    // 插入部分数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 1500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ChFetchVertexDataByPk(stmt, 0, 1500, labelName, "pk", 2);
    ChFetchVertexDataByPk(stmt, 4500, 500, labelName, "pk", 1);

    ChFetchVertexDataByHc(stmt, 0, 1500, labelName, "hashcluster", 2);
    ChFetchVertexDataByHc(stmt, 4500, 500, labelName, "hashcluster", 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn015)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做delete操作
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData2(stmt, 4500, 500, labelName, false));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    // 新插入刚被删除的数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData3(stmt, 4500, 500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn016)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据按hashcluster进行delete操作
    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataByHc(stmt, 4500, 500, labelName, false));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    // 新插入刚被删除的数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData3(stmt, 4500, 500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn017)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));

    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做replace操作，直连写
    EXPECT_EQ(GMERR_OK, ChReplaceVertexData2(stmt, 4500, 500, labelName, true));
    // 插入部分数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 1500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ChFetchVertexDataByPk(stmt, 0, 1500, labelName, "pk", 2);
    ChFetchVertexDataByPk(stmt, 4500, 500, labelName, "pk", 1);

    ChFetchVertexDataByHc(stmt, 0, 1500, labelName, "hashcluster", 2);
    ChFetchVertexDataByHc(stmt, 4500, 500, labelName, "hashcluster", 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn018)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做replace操作，直连写
    EXPECT_EQ(GMERR_OK, ChUpdateVertexData(stmt, 4500, 500, labelName, true));
    // 插入部分数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 1500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ChFetchVertexDataByPk(stmt, 0, 1500, labelName, "pk", 2);
    ChFetchVertexDataByPk(stmt, 4500, 500, labelName, "pk", 1);

    ChFetchVertexDataByHc(stmt, 0, 1500, labelName, "hashcluster", 2);
    ChFetchVertexDataByHc(stmt, 4500, 500, labelName, "hashcluster", 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn019)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(500);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据做delete操作，直连写
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData2(stmt, 4500, 500, labelName, true));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    // 新插入刚被删除的数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData3(stmt, 4500, 500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn020)
{
    const char *labelName = "chDefragment";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    const char *label =
        R"([{
            "version":"2.0",
            "type":"record",
            "name":"chDefragment",
            "fields":[
                { "name":"F1", "type":"uint32" },
                { "name":"F2", "type":"uint32" }
            ],
            "keys":[
                {
                    "node":"chDefragment",
                    "name":"pk",
                    "fields":["F1"],
                    "index":{ "type":"primary" },
                    "constraints": {"unique": true}
                },
                {
                    "name": "hashcluster",
                    "index": { "type": "hashcluster" },
                    "node": "chDefragment",
                    "fields": [ "F2" ],
                    "constraints": { "unique": false }
                }
            ]
        }])";

    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    // 创建label
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, label, labelConfig));
    // 插入数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);
    // 删除数据触发缩容
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 0, 4500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);
    DbSleep(1000);
    // 校验是否进行缩容
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    // 对剩余数据按hashcluster进行delete操作，直连写
    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataByHc(stmt, 4500, 500, labelName, true));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 0);
    // 新插入刚被删除的数据
    EXPECT_EQ(GMERR_OK, ChInsertVertexData3(stmt, 4500, 500, labelName));
    // 校验记录数
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 500);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashUpgradeAndScaleIn021)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    const char *labelName = "chSimpleLabel";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    string labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v1.json");

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson.c_str(), labelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v2.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 5000, 10000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 10000);

    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 5000, 9000, labelName));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 6000);
    DbSleep(1000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashUpgradeAndScaleIn022)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    const char *labelName = "chSimpleLabel";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    string labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v1.json");

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson.c_str(), labelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v2.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 5000, 10000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 10000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v3.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 10000, 15000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 15000);

    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 0, 4800, labelName, 1));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 5000, 9800, labelName, 2));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 10000, 12500, labelName));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2900);
    DbSleep(1000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_UPGRADE_PAGE_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

Status WaitDegradeEnd(GmcStmtT *stmt, const char *vertexLabelName)
{
    Status ret = GMERR_OK;
    // 等待表降级执行结束
    uint32_t degradeProcess = 0;
    while (true) {
        ret = GmcGetVertexLabelDegradeProgress(stmt, vertexLabelName, &degradeProcess);
        if (ret != GMERR_OK) {
            break;
        }
        sleep(1);
    }
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    return ret;
}

TEST_F(StStorage_Defragment, ClusteredHashDegradeAndScaleIn023)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    const char *labelName = "chSimpleLabel";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    string labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v1.json");

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson.c_str(), labelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v2.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 5000, 10000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 10000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v3.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 10000, 15000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 15000);

    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 0, 4000, labelName, 1));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 5000, 9000, labelName, 2));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 10000, 12500, labelName));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 4500);
    DbSleep(1000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_UPGRADE_PAGE_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);

    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, labelName, 2));
    ASSERT_EQ(GMERR_OK, WaitDegradeEnd(stmt, labelName));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 3, GMC_OPERATION_INSERT));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashDegradeAndScaleIn024)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    EXPECT_EQ(GMERR_OK, GmcInit());

    const char *labelName = "chSimpleLabel";
    const char *labelConfig = R"({"max_record_count":9999999, "defragmentation":true})";
    string labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v1.json");

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson.c_str(), labelConfig));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData(stmt, 0, 5000, labelName));
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 5000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v2.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 5000, 10000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 10000);

    labelJson = GetFileContext("./009_table_vacuum/st_data/chSimpleLabel_v3.json");
    ASSERT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, labelJson.c_str(), true, labelName));
    EXPECT_EQ(GMERR_OK, ChInsertVertexData2WithVersion(stmt, 10000, 15000, labelName, 2));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 15000);

    EXPECT_EQ(GMERR_OK, GmcDegradeVertexLabel(stmt, labelName, 2));
    ASSERT_EQ(GMERR_OK, WaitDegradeEnd(stmt, labelName));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 3, GMC_OPERATION_INSERT));

    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 0, 4990, labelName, 1));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexDataWithVersion(stmt, 5000, 9990, labelName, 2));
    EXPECT_EQ(GMERR_OK, ChDeleteVertexData(stmt, 10000, 14990, labelName));

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 30);
    DbSleep(1000);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_UPGRADE_PAGE_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
    EXPECT_EQ(GMERR_OK, GmcUnInit());
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn025)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true, "status_merge_sub":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "subs_type":"status_merge",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_vrfid",
            "index": { "type": "localhash" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": true }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 100, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 100);

    for (uint32_t cycle = 0; cycle < 4; cycle++) {
        ret = ChDeleteVertexData(stmt, 0, 90, labelName);
        EXPECT_EQ(GMERR_OK, ret);
        sleep(1);
        (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
        GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
        ASSERT_EQ(atoi(cmdOutput), 10);

        ret = ChInsertVertexData(stmt, 0, 10, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStorage_Defragment, ClusteredHashScaleIn26)
{
    StartDbServerWithConfig("\"memCompactEnable=1\" \"enableClusterHash=1\" \"userPolicyMode=0\"");
    int32_t ret = GmcInit();
    ASSERT_EQ(GMERR_OK, ret);
    const char *configJson = R"({"max_record_count":999999, "defragmentation":true, "status_merge_sub":true})";
    const char *labelJson =
        R"([{
    "version":"2.0",
    "type":"record",
    "name":"chDefragment",
    "subs_type":"status_merge",
    "fields":[
        { "name":"F1", "type":"uint32" },
        { "name":"F2", "type":"uint32" }
    ],
    "keys":[
        {
            "node":"chDefragment",
            "name":"pk",
            "fields":["F1"],
            "index":{ "type":"primary" },
            "constraints": {"unique": true}
        },
        {
            "name": "ip4_hashcluster",
            "index": { "type": "hashcluster" },
            "node": "chDefragment",
            "fields": [ "F2" ],
            "constraints": { "unique": false }
        }
    ]
}])";
    const char *labelName = "chDefragment";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = ChInsertVertexData(stmt, 0, 2000, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 2000);

    ret = ChDeleteVertexData(stmt, 0, 1900, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 状态合并订阅表通过后台GC线程回收，需要sleep
    sleep(3);
    system("gmsysview -q V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT");
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "SCALE_IN_THREHOLD_MEET_COUNT", cmdOutput, 64);
    ASSERT_GE(atoi(cmdOutput), 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT", "ENTRY_USED", cmdOutput, 64);
    ASSERT_EQ(atoi(cmdOutput), 100);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);

    ret = GmcUnInit();
    EXPECT_EQ(GMERR_OK, ret);
}
#endif

#endif
#endif
