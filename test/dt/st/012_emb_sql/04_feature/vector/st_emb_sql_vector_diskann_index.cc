/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: testsuite for SQL vector basic functions
 * Author: zhuangyifeng
 * Create: 2024-02-21
 */

#include <map>
#include <pthread.h>

#include "se_diskann_algo.h"
#include "se_diskann_types.h"
#include "st_emb_sql_vector_base.h"
#include "st_emb_sql_util.h"
#include "stub.h"
#include "adpt_define.h"
#include "db_json_common.h"

using namespace std;

static constexpr uint32_t T_DEFAULT_VECTOR_DIM = 4;
static constexpr uint32_t T_DEFAULT_BIND_IDX_ID = 1;
static constexpr uint32_t T_DEFAULT_BIND_IDX_REPR = 2;
static constexpr uint32_t MAX_INT_PART = 10;
static constexpr uint16_t LARGE_ANN_INDEX_DIM = 1024;
static constexpr uint16_t T_DEFAULT_EXPECT_COL_CNT = 2;
static constexpr uint16_t JSON_REJECT_DUPLICATES = 0x1;

// ** DO NOT ** drop t_default and t_empty inside test cases
// These table will be created in SetUp() and dropped in TearDown()
static std::string g_sqlSetUpCmd =
    "CREATE TABLE t_empty(id int primary key, repr floatvector(4));"
    "CREATE TABLE t_default(id int primary key, repr floatvector(4));"
    "INSERT INTO t_default VALUES(10000001, '[1, 2, 3, 4]');"
    "INSERT INTO t_default VALUES(10000002, '[2.0, 3.0, 4.0, 5.0]');"
    "INSERT INTO t_default VALUES(10000003, '[+3.0, -4., 5, .6]'), (10000004, '[0.1, 0.1, 0.1, 0.1]');";
static std::string g_sqlReleaseCmd = "DROP TABLE t_default;"
                                     "DROP TABLE t_empty;";
static std::string g_sqlTail = ";";
// static std::string g_sqlTail = " WITH (enable_lvq=true, lvq_quant_bit=8);";

// t_default 表初始化时存在表中的 id 列数据
static vector<int> g_tDefaultInitIds = {10000001, 10000002, 10000003, 10000004};
// t_default 表初始化时存在表中的向量数据
static vector<vector<float>> g_tDefaultInitVecs = {
    {1, 2, 3, 4},
    {2.0, 3.0, 4.0, 5.0},
    {+3.0, -4., 5, .6},
    {0.1, 0.1, 0.1, 0.1},
};

static uint32_t g_randSeed = 0;
constexpr int MIXED_QUERY_TEST_INSERT_NUM_1K = 1024;
constexpr int MIXED_QUERY_TEST_INSERT_NUM_RATIO = 2;

enum class TestMetaChangeTypeE {
    TEST_INDEX_DELETE_ALL,
    TEST_INDEX_DELETE_BTREE,
    TEST_INDEX_DELETE_L2,
    TEST_INDEX_DELETE_L2_WITH_REBUILD,
    TEST_INDEX_DELETE_BTREE_WITH_REBUILD,
    TEST_TABLE_DROP,
    TEST_TABLE_REBUILD_SAME,
    TEST_TABLE_REBUILD_DIFF,
};

// 根据 exptRowId 获取 t_Default 表初始化时的数据, 写入结果集 exptQryResult
// 若 exptRowId 传空则只构建 exptQryResult 的 colNames
static void GetTDefaultRowsByRowId(StEmbSqlQryResultSetExtendT &exptQryResult, const vector<uint32_t> &exptRowId)
{
    exptQryResult.colNames.emplace_back("id");
    exptQryResult.colNames.emplace_back("repr");

    for (uint32_t i : exptRowId) {
        vector<string> rowRes;
        rowRes.push_back(to_string(g_tDefaultInitIds[i]));  // id
        stringstream floatVec;
        PrintFloatVectorToStringStream(floatVec, g_tDefaultInitVecs[i]);
        rowRes.push_back(floatVec.str());
        exptQryResult.colValues.push_back(rowRes);
    }
}

float *GenerateRandomFloatVector(uint32_t dim, uint32_t lowerBound = 0, uint32_t upperBound = RAND_MAX)
{
    // caller should delete vec
    float *vec = new float[dim];
    for (uint32_t i = 0; i < dim; ++i) {
        vec[i] = static_cast<float>(rand_r(&g_randSeed)) / static_cast<float>(RAND_MAX) *
                     static_cast<float>(upperBound - lowerBound) +
                 static_cast<float>(lowerBound);
    }
    return vec;
}

class StEmbSqlVectorDiskAnnIndex : public StEmbSqlTestSuitExtend {
    void SetUp() override
    {
        StEmbSqlTestSuitExtend::SetUp();
        // 建 t_default 和 t_empty 表, 并给 t_default 插入缺省数据
        GmeConnT *conn = StEmbSqlGetConn();
        Status ret = GmeSqlExecute(conn, g_sqlSetUpCmd.c_str(), nullptr, nullptr, nullptr);
        ASSERT_EQ(ret, GMERR_OK);
    }

    void TearDown() override
    {
        // 删 t_default 和 t_empty 表
        GmeConnT *conn = StEmbSqlGetConn();
        Status ret = GmeSqlExecute(conn, g_sqlReleaseCmd.c_str(), nullptr, nullptr, nullptr);
        ASSERT_EQ(ret, GMERR_OK);

        StEmbSqlTestSuitExtend::TearDown();
    }
};

TEST_F(StEmbSqlVectorDiskAnnIndex, Insert)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    const char *insertSql = "INSERT INTO t_default VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused));
    uint32_t id = 0;
    // 插入两条数据, 主键无冲突, 插入成功
    float vec[T_DEFAULT_VECTOR_DIM] = {1, 2, 3, 4};
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, ++id));  // id = 1
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, ++id));  // id = 2
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    // 插入一条数据, 主键冲突, 插入失败
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, id));  // id = 2
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    ++id;  // id = 3
    // 绑定向量列的维度错误, 失败
    float vecWrongDim[T_DEFAULT_VECTOR_DIM - 1] = {1, 2, 3};
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, id));  // id = 3
    ASSERT_EQ(
        GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vecWrongDim, T_DEFAULT_VECTOR_DIM - 1, nullptr));
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    // 不绑定向量列, 失败
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, id));  // id = 3
    ASSERT_EQ(GMERR_DATA_EXCEPTION, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    // 绑定向量维度超过上限
    ASSERT_EQ(GMERR_INVALID_VALUE,
        GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vecWrongDim, DM_FLOAT_VECTOR_MAX_DIM + 1, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // 绑定向量维度为 0
    ASSERT_EQ(GMERR_INVALID_VALUE, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vecWrongDim, 0, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    // 反复插入删除同一条数据 10 次
    const char *repeatSql = "INSERT INTO t_default VALUES(100, '[1,2,3,4]');DELETE FROM t_default WHERE id = 100;";
    for (uint32_t i = 0; i < 10; ++i) {
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, repeatSql, nullptr, nullptr, nullptr));
    }

    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));

    // 预期查出所有的数据
    const char *sql = "SELECT * FROM t_default;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {10000001, 10000002, 10000003, 10000004, 1, 2};
    vector<vector<float>> floatVecs = {
        // 原有的数据
        {1, 2, 3, 4},
        {2.0, 3.0, 4.0, 5.0},
        {+3.0, -4., 5, .6},
        {0.1, 0.1, 0.1, 0.1},
        // 插入了两条新的数据
        {1, 2, 3, 4},
        {1, 2, 3, 4},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));  // id
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, CreateDropIvfIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();

    const char *sql =
        // 在 t_default (非空表) 上创建删除一个 l2 diskann 索引, 重复两次
        "CREATE INDEX diskann_l2_idx ON t_default USING GSDISKANN(repr L2);"
        "DROP INDEX t_default.diskann_l2_idx;"
        "CREATE INDEX diskann_l2_idx ON t_default USING GSDISKANN(repr L2);"
        "DROP INDEX t_default.diskann_l2_idx;"
        // 在 t_default (非空表) 上创建删除一个 cosine diskann 索引, 重复两次
        "CREATE INDEX diskann_cos_idx ON t_default USING GSDISKANN(repr COSINE);"
        "DROP INDEX t_default.diskann_cos_idx;"
        "CREATE INDEX diskann_cos_idx ON t_default USING GSDISKANN(repr COSINE);"
        "DROP INDEX t_default.diskann_cos_idx;"
        // 在 t_empty (空表) 上创建删除一个 l2 diskann 索引, 重复两次
        "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2);"
        "DROP INDEX t_empty.diskann_l2_idx;"
        "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2);"
        "DROP INDEX t_empty.diskann_l2_idx;"
        // 在 t_empty (空表) 上创建删除一个 cosine diskann 索引, 重复两次
        "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE);"
        "DROP INDEX t_empty.diskann_cos_idx;"
        "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE);"
        "DROP INDEX t_empty.diskann_cos_idx;";
    StEmbSqlPrepareUnusedStmt(conn, sql);
}

// 基本的 SELECT 语句验证 (with/wo limit)
TEST_F(StEmbSqlVectorDiskAnnIndex, SelectL2)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // 预期查出 10000002, 10000003, 10000004 对应的三行
    const char *sql = "SELECT * FROM t_default where repr <-> '[1, 2, 3, 4]' > 1.0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集并校验
    const vector<uint32_t> exptRowId = {1, 2, 3};
    StEmbSqlQryResultSetExtendT exptQryResult;
    GetTDefaultRowsByRowId(exptQryResult, exptRowId);
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
    StEmbSqlClearActQryResultSet(nullptr);

    // 预期查出上述结果集的前两行
    const char *sql2 = "SELECT * FROM t_default where repr <-> '[1, 2, 3, 4]' > 1.0 LIMIT 2;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql2, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集并校验
    exptQryResult.colValues.pop_back();
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
    StEmbSqlClearActQryResultSet(nullptr);
}

// 基本的 SELECT 语句验证 (with/wo limit)
TEST_F(StEmbSqlVectorDiskAnnIndex, SelectL0)
{
    GmeConnT *conn = StEmbSqlGetConn();

    // 预期无查询结果集
    const char *sql = "SELECT * FROM t_default where repr <-> '[1, 2, 3, 4]' > 1.0 LIMIT 0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集并校验
    StEmbSqlQryResultSetExtendT exptQryResult{};
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
    StEmbSqlClearActQryResultSet(nullptr);
}

// 插入 5 行, 每一行的格式为 (i, [0.00000, 1.00000, ..., 1535.00000]), i 的值从 0 ~ 4, 向量维度是 1536 维
// 然后 SELECT 全表, 检查结果正确性
TEST_F(StEmbSqlVectorDiskAnnIndex, Select_1536Dim)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *sqlCreateTable = "CREATE TABLE t1(id int primary key, repr floatvector(1536));";
    StEmbSqlPrepareUnusedStmt(conn, sqlCreateTable);

    const char *sqlInsert = "INSERT INTO t1 VALUES(?, ?);";
    GmeSqlStmtT *stmt = nullptr;
    uint32_t len = (uint32_t)(strlen(sqlInsert) + 1);
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, sqlInsert, len, &stmt, &unused));

    float vec[DM_FLOAT_VECTOR_MAX_DIM] = {0};
    for (uint32_t i = 0; i < DM_FLOAT_VECTOR_MAX_DIM; ++i) {
        vec[i] = (float)i;
    }

    const uint32_t rowNum = 5;
    for (uint32_t i = 0; i < rowNum; ++i) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, i));
        ASSERT_EQ(
            GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vec, DM_FLOAT_VECTOR_MAX_DIM, nullptr));
        ASSERT_EQ(GMERR_OK, GmeSqlStep(stmt));
        ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    }
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));

    const char *sql = "SELECT * FROM t1;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "repr"}};
    for (uint32_t i = 0; i < rowNum; ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(i));  // id
        stringstream floatVec;
        // 设置定点 6 位小数输出
        floatVec.setf(ios_base::fixed, ios_base::floatfield);
        floatVec << "[" << static_cast<float>(0);
        for (uint32_t j = 1; j < DM_FLOAT_VECTOR_MAX_DIM; ++j) {
            floatVec << ", " << static_cast<float>(j);
        }
        floatVec << "]";
        rowRes.push_back(floatVec.str());
        exptQryResult.colValues.push_back(rowRes);
    }

    // 检查结果
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropTable = "Drop Table t1;";
    StEmbSqlPrepareUnusedStmt(conn, sqlDropTable);
}

static int GetIndexByColName(ViewResultSet *resultSet, std::string colName)
{
    if (resultSet == nullptr || resultSet->colNames.empty()) {
        return -1;
    }
    for (int i = 0; i < resultSet->colNames.size(); ++i) {
        if (resultSet->colNames[i] == colName) {
            return i;
        }
    }
    return -1;
}

// 全部非模块化支持的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewAll)
{
    GmeConnT *conn = StEmbSqlGetConn();
    string views[] = {"V$CATA_GENERAL_INFO", "V$CATA_LABEL_SUBS_INFO", "V$CATA_NAMESPACE_INFO",
        "V$CATA_VERTEX_LABEL_CHECK_INFO", "V$CATA_VERTEX_LABEL_INFO", "V$COM_DYN_CTX", "V$COM_SHMEM_CTX",
        "V$COM_SHMEM_GROUP", "V$COM_SHMEM_USAGE_STAT", "V$COM_TABLE_MEM_SUMMARY", "V$CONFIG_PARAMETERS",
        "V$CST_SHMEM_INFO", "V$DB_PROBE_DATA", "V$DB_PROBE_DATA", "V$DB_SERVER", "V$DRT_COM_STAT", "V$DRT_CONN_STAT",
        "V$DRT_CONN_SUBS_STAT", "V$DRT_CONN_THREAT_STAT", "V$DRT_DATA_PLANE_CHANNEL_STAT", "V$DRT_LONG_OPERATION_STAT",
        "V$DRT_PIPE_STAT", "V$DRT_SCHEDULE_STAT", "V$DRT_WORKER_POOL_STAT", "V$DRT_WORKER_STAT",
        "V$MEM_COMPACT_TASKS_STAT", "V$PRIVILEGE_ROLE_STAT", "V$PRIVILEGE_USER_STAT", "V$QRY_DML_OPER_STATIS",
        "V$QRY_DYNMEM", "V$QRY_SESSION", "V$QRY_TRX_MONITOR_STAT", "V$STORAGE_ART_INDEX_STAT",
        "V$STORAGE_BTREE_INDEX_STAT", "V$STORAGE_BUFFERPOOL_STAT", "V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT",
        "V$STORAGE_FSM_STAT", "V$STORAGE_HASH_CLUSTER_INDEX_STAT", "V$STORAGE_HASH_COLLISION_STAT",
        "V$STORAGE_HASH_INDEX_STAT", "V$STORAGE_HASH_LINKLIST_INDEX_STAT", "V$STORAGE_HEAP_STAT",
        "V$STORAGE_HEAP_VERTEX_LABEL_STAT", "V$STORAGE_INDEX_GLOBAL_STAT", "V$STORAGE_LOCK_OVERVIEW",
        "V$STORAGE_PERSISTENT_STAT", "V$STORAGE_SPACE_INFO", "V$STORAGE_TABLE_SHM_INFO", "V$STORAGE_TRX_DETAIL",
        "V$STORAGE_TRX_STAT", "V$STORAGE_UNDO_PURGER_INFO", "V$STORAGE_UNDO_STAT", "V$STORAGE_VERTEX_COUNT",
        "V$SYS_MODULE_MEM_INFO", "V$STORAGE_BUFFERPOOL_LOAD_TABLE_STAT"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_EQ(ret, GMERR_OK) << "query view all:" << i << ", " << views[i] << " failed";
        if (ret != 0) {
            printf("%s %d\n", views[i].c_str(), ret);
        }
    }
}

// 全部默认场景下预期有结果的非模块化视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewRes)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string views[] = {"V$CATA_GENERAL_INFO", "V$CATA_NAMESPACE_INFO", "V$CATA_VERTEX_LABEL_CHECK_INFO",
        "V$CATA_VERTEX_LABEL_INFO", "V$COM_DYN_CTX", "V$COM_SHMEM_CTX", "V$COM_SHMEM_USAGE_STAT",
        "V$COM_TABLE_MEM_SUMMARY", "V$CONFIG_PARAMETERS", "V$CST_SHMEM_INFO", "V$DB_PROBE_DATA", "V$DB_PROBE_DATA",
        "V$DB_SERVER", "V$DRT_COM_STAT", "V$DRT_WORKER_POOL_STAT", "V$DRT_WORKER_STAT", "V$PRIVILEGE_ROLE_STAT",
        "V$PRIVILEGE_USER_STAT", "V$QRY_DML_OPER_STATIS", "V$QRY_DYNMEM", "V$QRY_SESSION", "V$QRY_TRX_MONITOR_STAT",
        "V$STORAGE_BTREE_INDEX_STAT", "V$STORAGE_FSM_STAT", "V$STORAGE_HEAP_STAT", "V$STORAGE_HEAP_VERTEX_LABEL_STAT",
        "V$STORAGE_INDEX_GLOBAL_STAT", "V$STORAGE_LOCK_OVERVIEW", "V$STORAGE_PERSISTENT_STAT", "V$STORAGE_SPACE_INFO",
        "V$STORAGE_TABLE_SHM_INFO", "V$STORAGE_TRX_DETAIL", "V$STORAGE_TRX_STAT", "V$STORAGE_UNDO_STAT",
        "V$STORAGE_VERTEX_COUNT", "V$SYS_MODULE_MEM_INFO"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_EQ(ret, GMERR_OK) << "query viewRes:" << i << ", " << views[i] << " failed";
        EXPECT_TRUE(resultSet.colNames.size() > 0);
        EXPECT_TRUE(resultSet.colValues.size() > 0);
        if (ret != 0 || resultSet.colNames.size() == 0 || resultSet.colValues.size() == 0) {
            printf("%s %d\n", views[i].c_str(), ret);
        }
    }
}

// 通过视图获取表结构
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewSchema)
{
    GmeConnT *conn = StEmbSqlGetConn();
    StEmbSqlPrepareUnusedStmt(conn, "create table test_schema_table (id int primary key autoincrement, name "
                                    "varchar(255), vectorCol floatvector(15));");
    ViewResultSet resultSet;
    Status ret =
        GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'TEST_SCHEMA_TABLE';",
            StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "SCHEMA_INFO");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(json, "name"))), "TEST_SCHEMA_TABLE");
    DbJsonDelete(json);
    StEmbSqlPrepareUnusedStmt(conn, "drop table test_schema_table;");
}

// 嵌套视图验证
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewNode)
{
    GmeConnT *conn = StEmbSqlGetConn();
    ViewResultSet resultSet;
    Status ret =
        GmeSqlExecute(conn, "select * from 'V$PRIVILEGE_USER_STAT'", StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "ROLES");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    DbJsonT *item = DbJsonObjectGet(DbJsonArrayGet(json, 0), "ROLE_NAME");
    EXPECT_TRUE(DbJsonIsString(item));
    EXPECT_FALSE(string(DbJsonStringValue(item)).empty());
    DbJsonDelete(json);
}

// 嵌套视图列表验证
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewNode02)
{
    GmeConnT *conn = StEmbSqlGetConn();
    StEmbSqlPrepareUnusedStmt(conn, "CREATE TABLE test_index_table_02 (id int primary key, repr floatvector(4));"
                                    "CREATE INDEX diskann_l2_idx ON test_index_table_02 USING GSDISKANN(repr L2);"
                                    "CREATE INDEX ivf_cos_idx ON test_index_table_02 USING GSDISKANN(repr COSINE);");
    ViewResultSet resultSet;
    Status ret =
        GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'TEST_INDEX_TABLE_02';",
            StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "KEYS");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    ASSERT_EQ(DbJsonGetArraySize(json), 3);
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(json, 0), "INDEX_NAME"))), "primary");
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(json, 1), "INDEX_NAME"))), "diskann_l2_idx");
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(json, 2), "INDEX_NAME"))), "ivf_cos_idx");
    DbJsonDelete(json);
    StEmbSqlPrepareUnusedStmt(conn, "DROP INDEX test_index_table_02.diskann_l2_idx;"
                                    "DROP INDEX test_index_table_02.ivf_cos_idx;"
                                    "DROP TABLE test_index_table_02;");
}

TEST_F(StEmbSqlVectorDiskAnnIndex, ViewIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();
    StEmbSqlPrepareUnusedStmt(conn, "CREATE TABLE test_index_table (id int primary key, repr floatvector(4));"
                                    "CREATE INDEX diskann_l2_idx ON test_index_table USING GSDISKANN(repr L2);"
                                    "CREATE INDEX ivf_cos_idx ON test_index_table USING GSDISKANN(repr COSINE);");
    ViewResultSet resultSet;
    Status ret =
        GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'TEST_INDEX_TABLE';",
            StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "SCHEMA_INFO");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    DbJsonT *item = DbJsonObjectGet(json, "keys");
    ASSERT_EQ(DbJsonGetArraySize(item), 3);
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(item, 0), "name"))), "primary");
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(item, 1), "name"))), "diskann_l2_idx");
    EXPECT_EQ(string(DbJsonStringValue(DbJsonObjectGet(DbJsonArrayGet(item, 2), "name"))), "ivf_cos_idx");
    DbJsonDelete(json);
    StEmbSqlPrepareUnusedStmt(conn, "DROP INDEX test_index_table.diskann_l2_idx;"
                                    "DROP INDEX test_index_table.ivf_cos_idx;"
                                    "DROP TABLE test_index_table;");
}

// 无权限字段不显示验证
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewSensitive)
{
    GmeConnT *conn = StEmbSqlGetConn();
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'T_EMPTY';",
        StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    // 非敏感字段可以查出来
    int i = GetIndexByColName(&resultSet, "KEYS");
    DbJsonT *json = DbJsonLoads(resultSet.colValues[0][i].c_str(), JSON_REJECT_DUPLICATES);
    DbJsonT *nodeNameItem = DbJsonObjectGet(DbJsonArrayGet(json, 0), "NODE_NAME");
    EXPECT_TRUE(DbJsonIsString(nodeNameItem));
    const char *nodeName = DbJsonStringValue(nodeNameItem);
    EXPECT_FALSE(std::string(nodeName).empty());
    DbJsonDelete(json);
    // 敏感字段不可查出来
    i = GetIndexByColName(&resultSet, "OBJ_PRIVILEGE");
    EXPECT_TRUE(resultSet.colValues[0][i].empty());
}

// 通过where语句查询
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewEqual)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 创建自定义表
    StEmbSqlPrepareUnusedStmt(conn,
        "create table my_table (id int primary key autoincrement, name varchar(255), vectorCol floatvector(15));");
    // 全量查询
    ViewResultSet resultSet0;
    Status ret =
        GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO';", StEmbSqlViewResultSet, &resultSet0, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet0.colNames.size() > 0);
    EXPECT_TRUE(resultSet0.colValues.size() > 0);
    // 过滤查询
    ViewResultSet resultSet;
    ret = GmeSqlExecute(conn, "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'MY_TABLE';",
        StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "VERTEX_LABEL_NAME");
    EXPECT_EQ(resultSet.colValues[0][i], "MY_TABLE");
    StEmbSqlPrepareUnusedStmt(conn, "drop table my_table;");
}

// 对结果做ORDERBY
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewOrderby)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql = "select * from 'V$CATA_VERTEX_LABEL_INFO' orderby VERTEX_LABEL_NAME";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
}

// 通过比较符查询结果
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewGreater)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql = "select * from 'V$CATA_GENERAL_INFO' where VERTEX_LABEL_NUM > 0";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "VERTEX_LABEL_NUM");
    for (auto &value : resultSet.colValues) {
        EXPECT_GT(std::stoi(value[i]), 0);
    }
}

// 通过比较符查询结果
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewLess)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // st012目录全跑时，表个数可能增多，放宽查询条件
    const char *viewSql = "select * from 'V$CATA_GENERAL_INFO' where VERTEX_LABEL_NUM < 1000";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "VERTEX_LABEL_NUM");
    for (auto &value : resultSet.colValues) {
        EXPECT_LT(std::stoi(value[i]), 1000);
    }
}

// 通过BETWEEN查询
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewBetween)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql = "select * from 'V$COM_TABLE_MEM_SUMMARY' where VERTEX_LABEL_SIZE BETWEEN 2000 AND 10000";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_TRUE(resultSet.colNames.size() > 0);
    EXPECT_TRUE(resultSet.colValues.size() > 0);
    int i = GetIndexByColName(&resultSet, "VERTEX_LABEL_SIZE");
    for (auto &value : resultSet.colValues) {
        EXPECT_GT(std::stoi(value[i]), 2000);
        EXPECT_LT(std::stoi(value[i]), 10000);
    }
}

// 通过IN查询
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewIn)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql =
        "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME IN ('GM_SYS_VL', 'GM_SYS_EL')";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_GT(resultSet.colNames.size(), 0);
    EXPECT_EQ(resultSet.colValues.size(), 2);
    int i = GetIndexByColName(&resultSet, "VERTEX_LABEL_NAME");
    EXPECT_EQ("GM_SYS_VL", resultSet.colValues[0][i]);
    EXPECT_EQ("GM_SYS_EL", resultSet.colValues[1][i]);
}

// 通过where语句查询，过滤结果不存在
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewWrongFilter)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql = "select * from 'V$CATA_VERTEX_LABEL_INFO' where VERTEX_LABEL_NAME = 'NOT_EXISTS_TABLE'";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(resultSet.colNames.size(), 0);
    EXPECT_EQ(resultSet.colValues.size(), 0);
}

// FEATURE_MEMDATA场景下支持的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewFeature01)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string views[] = {"V$CATA_TABLESPACE_INFO", "V$COM_MEM_SUMMARY", "V$SERVER_MEMORY_OVERHEAD",
        "V$STORAGE_MEMDATA_STAT", "V$STORAGE_SHMEM_INFO"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_FEATURE_NOT_SUPPORTED);
    }
}

// FEATURE_DURABLE_MEMDATA场景下支持的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_ViewFeature02)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string views[] = {"V$STORAGE_DURABLE_MEMDATA_STAT", "V$DB_SERVER_KEY_RESOURCE"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_FEATURE_NOT_SUPPORTED);
    }
}

// DB_MEM_TRACE场景下支持的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewFeature03)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string views[] = {"V$DYN_MEM_TRACE_INFO", "V$SHMEM_TRACE_INFO"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_TRUE(ret == GMERR_OK || ret == GMERR_FEATURE_NOT_SUPPORTED);
    }
}

typedef struct GmeSqlExecuteArgs {
    GmeConnT *conn;
    const char *str;
    GmeSqlExecuteCallback callback;
    void *data;
    char **errMsg;
} GmeSqlExecuteArgsT;

void *GmeSqlExecuteThread(void *args)
{
    GmeSqlExecuteArgsT *arg = (GmeSqlExecuteArgsT *)args;
    Status ret = GmeSqlExecute(arg->conn, arg->str, arg->callback, arg->data, arg->errMsg);
    EXPECT_EQ(ret, GMERR_OK);
    return NULL;
}

// 多线程同时查询同一视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewMultiThreads01)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const int numThreads = 10;
    vector<ViewResultSet> resultSets(numThreads);
    pthread_t threads[numThreads];
    GmeSqlExecuteArgsT args[numThreads];

    // 在循环中，为每个线程启动一个查询任务，并将结果存储到results向量中
    int ret = 0;
    for (int i = 0; i < numThreads; ++i) {
        args[i].conn = conn;
        args[i].str = "select * from 'V$CATA_VERTEX_LABEL_INFO'";
        args[i].callback = StEmbSqlViewResultSet;
        args[i].data = &resultSets[i];
        args[i].errMsg = NULL;
        ret = pthread_create(&threads[i], NULL, GmeSqlExecuteThread, &args[i]);
    }

    // 再次循环，等待每个线程完成，并检查结果
    for (int i = 0; i < numThreads; ++i) {
        ret = pthread_join(threads[i], NULL);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_TRUE(resultSets[i].colNames.size() > 0);
        EXPECT_TRUE(resultSets[i].colValues.size() > 0);
    }
}

// 多线程同时查询多个视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewMultiThreads02)
{
    GmeConnT *conn = StEmbSqlGetConn();
    static const char *queries[] = {"select * from 'V$CATA_GENERAL_INFO'", "select * from 'V$CATA_NAMESPACE_INFO'",
        "select * from 'V$CATA_VERTEX_LABEL_CHECK_INFO'", "select * from 'V$CATA_VERTEX_LABEL_INFO'",
        "select * from 'V$COM_DYN_CTX'", "select * from 'V$COM_SHMEM_CTX'", "select * from 'V$COM_SHMEM_USAGE_STAT'",
        "select * from 'V$COM_TABLE_MEM_SUMMARY'", "select * from 'V$CONFIG_PARAMETERS'",
        "select * from 'V$CST_SHMEM_INFO'", "select * from 'V$DB_PROBE_DATA'", "select * from 'V$DB_PROBE_DATA'",
        "select * from 'V$DB_SERVER'", "select * from 'V$DRT_COM_STAT'", "select * from 'V$DRT_WORKER_POOL_STAT'",
        "select * from 'V$DRT_WORKER_STAT'", "select * from 'V$PRIVILEGE_ROLE_STAT'",
        "select * from 'V$PRIVILEGE_USER_STAT'", "select * from 'V$QRY_DML_OPER_STATIS'",
        "select * from 'V$QRY_DYNMEM'", "select * from 'V$QRY_SESSION'", "select * from 'V$QRY_TRX_MONITOR_STAT'",
        "select * from 'V$STORAGE_BTREE_INDEX_STAT'", "select * from 'V$STORAGE_FSM_STAT'",
        "select * from 'V$STORAGE_HEAP_STAT'", "select * from 'V$STORAGE_HEAP_VERTEX_LABEL_STAT'",
        "select * from 'V$STORAGE_INDEX_GLOBAL_STAT'", "select * from 'V$STORAGE_LOCK_OVERVIEW'",
        "select * from 'V$STORAGE_PERSISTENT_STAT'", "select * from 'V$STORAGE_SPACE_INFO'",
        "select * from 'V$STORAGE_TABLE_SHM_INFO'", "select * from 'V$STORAGE_TRX_DETAIL'",
        "select * from 'V$STORAGE_TRX_STAT'", "select * from 'V$STORAGE_UNDO_STAT'",
        "select * from 'V$STORAGE_VERTEX_COUNT'", "select * from 'V$SYS_MODULE_MEM_INFO'"};
    const int numViews = ELEMENT_COUNT(queries);
    // 存储每个线程的执行结果
    vector<ViewResultSet> resultSets(numViews);  // 每个线程独立的结果集.
    pthread_t threads[numViews];
    GmeSqlExecuteArgsT args[numViews];
    // 启动多线程任务
    int ret = 0;
    for (int i = 0; i < numViews; ++i) {
        args[i].conn = conn;
        args[i].str = queries[i];
        args[i].callback = StEmbSqlViewResultSet;
        args[i].data = &resultSets[i];
        args[i].errMsg = NULL;
        ret = pthread_create(&threads[i], NULL, GmeSqlExecuteThread, &args[i]);
    }
    // 验证每个线程的执行结果
    for (int i = 0; i < numViews; ++i) {
        ret = pthread_join(threads[i], NULL);  // 等待线程完成并获取返回值
        EXPECT_EQ(ret, GMERR_OK);              // 检查返回值
        EXPECT_TRUE(resultSets[i].colNames.size() > 0);
        EXPECT_TRUE(resultSets[i].colValues.size() > 0);
        if (ret != 0 || resultSets[i].colNames.size() == 0 || resultSets[i].colValues.size() == 0) {
            printf("%s %d\n", queries[i], ret);
        }
    }
}

TEST_F(StEmbSqlVectorDiskAnnIndex, ViewDataTypeInt16)
{
    GmeConnT *conn = StEmbSqlGetConn();
    string sqlStr = "select * from 'V$CATA_NAMESPACE_INFO'";
    GmeSqlStmtT *stmt = NULL;
    int ret = GmeSqlPrepare(conn, sqlStr.c_str(), sqlStr.length(), &stmt, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    for (int32_t r = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; r++) {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        if (columnCount == 0) {
            break;
        }
        EXPECT_NE(GmeSqlColumnType(stmt, 3), GME_DB_DATATYPE_NULL);
    }
    GmeSqlFinalize(stmt);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, ViewDataTypeInt32)
{
    GmeConnT *conn = StEmbSqlGetConn();
    string sqlStr = "select * from 'V$CATA_VERTEX_LABEL_INFO'";
    GmeSqlStmtT *stmt = NULL;
    int ret = GmeSqlPrepare(conn, sqlStr.c_str(), sqlStr.length(), &stmt, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    for (int32_t r = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; r++) {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        if (columnCount == 0) {
            break;
        }
        EXPECT_NE(GmeSqlColumnType(stmt, 8), GME_DB_DATATYPE_NULL);
    }
    GmeSqlFinalize(stmt);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, ViewDataTypeUint64)
{
    GmeConnT *conn = StEmbSqlGetConn();
    string sqlStr = "select * from 'V$CONFIG_PARAMETERS'";
    GmeSqlStmtT *stmt = NULL;
    int ret = GmeSqlPrepare(conn, sqlStr.c_str(), sqlStr.length(), &stmt, nullptr);
    EXPECT_EQ(ret, GMERR_OK);
    for (int32_t r = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; r++) {
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        if (columnCount == 0) {
            break;
        }
        EXPECT_NE(GmeSqlColumnType(stmt, 3), GME_DB_DATATYPE_NULL);
    }
    GmeSqlFinalize(stmt);
}

// 支持视图范围以外的，名字正确的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewNotSupport)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string views[] = {"V$CLT_PROCESS_CONN", "V$CLT_PROCESS_FLOWCTRL_INFO_LABEL", "V$CLT_PROCESS_INFO",
        "V$CLT_PROCESS_TIME_CONSUMPTION", "V$CLT_PROCESS_LABEL", "V$DRT_DIRECT_MSG_POOL_STAT", "V$STORAGE_DISK_USAGE"};
    for (int i = 0; i < ELEMENT_COUNT(views); ++i) {
        string sqlStr = "select * from '" + views[i] + "'";
        ViewResultSet resultSet;
        Status ret = GmeSqlExecute(conn, sqlStr.c_str(), StEmbSqlViewResultSet, &resultSet, nullptr);
        EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
    }
}

// 不存在的视图
TEST_F(StEmbSqlVectorDiskAnnIndex, ViewNotExists)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *viewSql = "select * from 'V$NOT_EXISTS_VIEW'";
    ViewResultSet resultSet;
    Status ret = GmeSqlExecute(conn, viewSql, StEmbSqlViewResultSet, &resultSet, nullptr);
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByL2NoIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 预期查出上述结果集的前两行
    const char *sqlL2 = "SELECT * FROM t_default order by repr <-> '[3, 4, 5, 6]';";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlL2, StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 应该会按照下面的行顺序返回结果
    const vector<uint32_t> exptRowId = {1, 0, 3, 2};
    StEmbSqlQryResultSetExtendT exptQryResult;
    GetTDefaultRowsByRowId(exptQryResult, exptRowId);
    // 检查结果
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByCosineNoIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 预期查出上述结果集的前两行
    const char *sqlCosine = "SELECT * FROM t_default order by repr <=> '[2, 3, 4, 5]';";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCosine, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByL2WithIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string sqlCreateIdx = "CREATE INDEX diskann_l2_idx ON t_default USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    std::string sqlSelect = "SELECT * FROM t_default ORDER BY repr <-> '[1, 2, 3, 4]' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlSelect.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {10000001, 10000002, 10000004, 10000003};
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
        {2.000000, 3.000000, 4.000000, 5.000000},
        {0.100000, 0.100000, 0.100000, 0.100000},
        {3.000000, -4.000000, 5.000000, 0.600000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));  // id
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t_default.diskann_l2_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByL2WithIndexNoPk)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 表上无主键
    const char *prepareSql = "CREATE TABLE t0(id int unique, repr floatvector(4));"
                             "INSERT INTO t0 VALUES(0, '[1, 2, 3, 4]'),"
                             "(1, '[2.0, 3.0, 4.0, 5.0]'),"
                             "(2, '[+3.0, -4., 5, .6]'),"
                             "(3, '[2.0, 3.0, 4.0, 6.0]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));

    std::string sqlCreateIdx = "CREATE INDEX diskann_l2_idx ON t0 USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    std::string sqlSelect = "SELECT * FROM t0 ORDER BY repr <-> '[1, 2, 3, 4]' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlSelect.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {0, 1, 3, 2};
    vector<vector<float>> floatVecs = {
        {1, 2, 3, 4},
        {2.0, 3.0, 4.0, 5.0},
        {2.0, 3.0, 4.0, 6.0},
        {+3.0, -4., 5, .6},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));  // id
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropTbl = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropTbl, nullptr, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByCosineWithIndex)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t_default USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    std::string sqlSelect = "SELECT * FROM t_default ORDER BY repr <=> '[1, 2, 3, 4]' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlSelect.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 构造预期结果集
    vector<uint32_t> ids = {10000001, 10000002, 10000004, 10000003};
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
        {2.000000, 3.000000, 4.000000, 5.000000},
        {0.100000, 0.100000, 0.100000, 0.100000},
        {3.000000, -4.000000, 5.000000, 0.600000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));  // id
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t_default.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, SelectOrderByWhere)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *prepareSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(4));"
                             "INSERT INTO t0 VALUES(0, 0, '[1, 2, 3, 4]'),"
                             "(1, 1, '[2.0, 3.0, 4.0, 5.0]'),"
                             "(2, 2, '[+3.0, -4., 5, .6]'),"
                             "(3, 3, '[2.0, 3.0, 4.0, 5.0]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t0 USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    const char *updateSql = "DELETE FROM t0 WHERE repr <=> '[2, 3, 4, 5]' < 0.0001;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, updateSql, nullptr, nullptr, nullptr));

    const char *selectSql =
        "SELECT repr FROM t0 WHERE repr <-> '[2, 3, 4, 5]' > 1.0 ORDER BY repr <=> '[1, 2, 3, 4]' LIMIT 1;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"repr"}};
    for (uint32_t i = 0; i < floatVecs.size(); ++i) {
        vector<string> rowRes;
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t0.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
    const char *dropSql = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, SelectOrderByWhereUpperBound)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *prepareSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(3));"
                             "INSERT INTO t0 VALUES(0, 0, '[2.4,3.3,4.1]'),"
                             "(1, 1, '[4.4,3.3,7.1]'),"
                             "(2, 2, '[3.7,46.7,24.1]'),"
                             "(3, 3, '[4.1,50.1,20.1]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t0 USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    const char *selectSql =
        "SELECT repr FROM t0 WHERE repr <=> '[2, 3, 4]' < 0.1 ORDER BY repr <=> '[2, 3, 4]' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<vector<float>> floatVecs = {
        {2.4, 3.3, 4.1},
        {4.4, 3.3, 7.1},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"repr"}};
    for (uint32_t i = 0; i < floatVecs.size(); ++i) {
        vector<string> rowRes;
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t0.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
    const char *dropSql = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

// 看护 set column = value 场景
TEST_F(StEmbSqlVectorDiskAnnIndex, Update)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *prepareSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(4));"
                             "INSERT INTO t0 VALUES(0, 0, '[1, 2, 3, 4]'),"
                             "(1, 1, '[2.0, 3.0, 4.0, 5.0]'),"
                             "(2, 2, '[+3.0, -4., 5, .6]'),"
                             "(3, 3, '[2.0, 3.0, 4.0, 55.0]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t0 USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    const char *updateSql = "UPDATE t0 SET id = 10, alter_prop = 100 WHERE repr <-> '[2, 3, 4, 5]' < 0.01;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, updateSql, nullptr, nullptr, nullptr));

    const char *selectSql = "SELECT * FROM t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {0, 10, 2, 3};
    vector<uint32_t> alter_prop = {0, 100, 2, 3};
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
        {2.000000, 3.000000, 4.000000, 5.000000},
        {3.000000, -4.000000, 5.000000, 0.600000},
        {2.000000, 3.000000, 4.000000, 55.000000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "alter_prop", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));         // id
        rowRes.push_back(to_string(alter_prop[i]));  // alter_prop
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t0.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
    const char *dropSql = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

// 看护 set columnList = valueList 场景
TEST_F(StEmbSqlVectorDiskAnnIndex, Update_002)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *prepareSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(4));"
                             "INSERT INTO t0 VALUES(0, 0, '[1, 2, 3, 4]'),"
                             "(1, 1, '[2.0, 3.0, 4.0, 5.0]'),"
                             "(2, 2, '[+3.0, -4., 5, .6]'),"
                             "(3, 3, '[2.0, 3.0, 4.0, 55.0]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t0 USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    const char *updateSql = "UPDATE t0 SET (id, alter_prop) = (10, 100) WHERE repr <-> '[2, 3, 4, 5]' < 0.01;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, updateSql, nullptr, nullptr, nullptr));

    const char *selectSql = "SELECT * FROM t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {0, 10, 2, 3};
    vector<uint32_t> alter_prop = {0, 100, 2, 3};
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
        {2.000000, 3.000000, 4.000000, 5.000000},
        {3.000000, -4.000000, 5.000000, 0.600000},
        {2.000000, 3.000000, 4.000000, 55.000000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "alter_prop", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));         // id
        rowRes.push_back(to_string(alter_prop[i]));  // alter_prop
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t0.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
    const char *dropSql = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, Delete)
{
    GmeConnT *conn = StEmbSqlGetConn();
    const char *prepareSql = "CREATE TABLE t0(id int primary key, alter_prop int, repr floatvector(4));"
                             "INSERT INTO t0 VALUES(0, 0, '[1, 2, 3, 4]'),"
                             "(1, 1, '[2.0, 3.0, 4.0, 5.0]'),"
                             "(2, 2, '[+3.0, -4., 5, .6]'),"
                             "(3, 3, '[2.0, 3.0, 4.0, 5.0]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, prepareSql, nullptr, nullptr, nullptr));
    std::string sqlCreateIdx = "CREATE INDEX diskann_cos_idx ON t0 USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    const char *updateSql = "DELETE FROM t0 WHERE repr <=> '[2, 3, 4, 5]' < 0.0001;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, updateSql, nullptr, nullptr, nullptr));

    const char *selectSql = "SELECT * FROM t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 构造预期结果集
    vector<uint32_t> ids = {0, 2};
    vector<uint32_t> alter_prop = {0, 2};
    vector<vector<float>> floatVecs = {
        {1.000000, 2.000000, 3.000000, 4.000000},
        {3.000000, -4.000000, 5.000000, 0.600000},
    };
    StEmbSqlQryResultSetExtendT exptQryResult{{"id", "alter_prop", "repr"}};
    for (uint32_t i = 0; i < ids.size(); ++i) {
        vector<string> rowRes;
        rowRes.push_back(to_string(ids[i]));         // id
        rowRes.push_back(to_string(alter_prop[i]));  // alter_prop
        stringstream vecStr;
        PrintFloatVectorToStringStream(vecStr, floatVecs[i]);
        rowRes.push_back(vecStr.str());
        exptQryResult.colValues.push_back(rowRes);
    }
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);

    const char *sqlDropIdx = "Drop INDEX t0.diskann_cos_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx, nullptr, nullptr, nullptr));
    const char *dropSql = "DROP TABLE t0;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, dropSql, StEmbSqlGetActQryResultSet, nullptr, nullptr));
}

// 空表建索引，空表查询
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalEmpty)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 空表查询
    sql = "SELECT * FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 2;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlQryResultSetExtendT exptQryResult{};
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult);
    StEmbSqlClearActQryResultSet(nullptr);
    sql = "SELECT * FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 2;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlQryResultSetExtendT exptQryResult1{};
    StEmbSqlCheckActQryResultSet(nullptr, exptQryResult1);
}

static void DiskAnnIndexStepInsert(GmeSqlStmtT *stmt, uint32_t dim, uint32_t insertNum, int32_t startId, Status ret)
{
    for (int32_t id = startId; id < startId + insertNum; ++id) {
        float *vec = GenerateRandomFloatVector(dim);
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, id));
        ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, T_DEFAULT_BIND_IDX_REPR, vec, dim, nullptr));
        ASSERT_EQ(ret, GmeSqlStep(stmt));
        if (ret != GMERR_OK) {
            break;
        }
        ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
        delete[] vec;
    }
}

static void DiskAnnIndexIncrementalInsert(GmeConnT *conn, uint32_t insertNum, int32_t startId = 1)
{
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    const char *insertSql = "INSERT INTO t_empty VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused));
    DiskAnnIndexStepInsert(stmt, T_DEFAULT_VECTOR_DIM, insertNum, startId, GMERR_OK);
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

constexpr int INSERT_NUM_FOR_INCREMENTAL_INSERT_001 = 4;
// 空表建索引，插入数据后查询
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_001)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_001);

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_001);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_001);
}

constexpr int INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM = 10;
// 空表建索引，显式事务插入一批数据发生回滚之后，查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalInsert_002)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    // 插入一条主键冲突数据
    std::string insertSql = "INSERT INTO t_empty VALUES(5, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION,
        GmeSqlExecute(conn, insertSql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
}

static Status DiskAnnAlgoInsertMock(DiskAnnCtxT *adpt, const DiskAnnAddrT vertexId, float *insertVector)
{
    return GMERR_INTERNAL_ERROR;
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalInsert_0021)
{
    init();
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    int stubIndex =
        setStubC(reinterpret_cast<void *>(DiskAnnAlgoInsert), reinterpret_cast<void *>(DiskAnnAlgoInsertMock));
    EXPECT_GT(stubIndex, 0);
    std::string insertSql = "INSERT INTO t_empty VALUES(5, '[2, 2, 2, 2]');";
    ASSERT_EQ(
        GMERR_INTERNAL_ERROR, GmeSqlExecute(conn, insertSql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    clearStub(stubIndex);

    // 提交事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "COMMIT;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlClearActQryResultSet(nullptr);
}

// 空表建索引，显式事务插入一批数据, 再插入主键冲突的数据，返回错误，再插入相同向量的数据，发生回滚之后，查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalInsert_003)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    // 插入一条主键冲突数据
    std::string insertSql = "INSERT INTO t_empty VALUES(5, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION,
        GmeSqlExecute(conn, insertSql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一些重复向量的数据
    std::string insertSql1 = "INSERT INTO t_empty VALUES(1300, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, insertSql1.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    std::string insertSql2 = "INSERT INTO t_empty VALUES(1301, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, insertSql2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    std::string insertSql3 = "INSERT INTO t_empty VALUES(1302, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, insertSql3.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
}

// 空表建索引，显式事务插入一批数据, 再回滚，再插入向量数据，查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalInsert_004)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);

    // // 再插入数据
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
}

// 空表建索引，显式事务插入一批数据,删除一半数据后发生回滚之后，查询
constexpr int INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE = 50;
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalDelete_001)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);
    // 删除一半数据
    sql = "DELETE FROM t_empty WHERE id > 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
}

// 空表建索引，插入一批数据，显式事务删除一半数据后发生回滚之后，查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalDelete_002)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);
    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 删除一半数据
    sql = "DELETE FROM t_empty WHERE id > 25;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 50;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 50;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);
}

constexpr int INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE_HALFNUM = 25;
// 空表建索引，插入一批数据，显式事务删除一半数据后发生回滚之后，查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalDelete_003)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);
    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 删除一半数据
    sql = "DELETE FROM t_empty WHERE id > 25;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 50;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE);

    // 删除一半数据
    sql = "DELETE FROM t_empty WHERE id > 25;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 50;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_DISKANN_INCREMENTAL_DELETE_HALFNUM);
}

constexpr int INSERT_NUM_FOR_INCREMENTAL_DELETE = 10;
constexpr int HALF_OF_INSERT_NUM_FOR_INCREMENTAL_DELETE = 5;
// 插入数据，创建索引，删除数据后查询
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalDelete)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL_DELETE);

    // 创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 删除一半数据
    sql = "DELETE FROM t_empty WHERE id > 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, HALF_OF_INSERT_NUM_FOR_INCREMENTAL_DELETE);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, HALF_OF_INSERT_NUM_FOR_INCREMENTAL_DELETE);
}

// 插入数据，创建索引，更新数据后查询
constexpr int INSERT_NUM_FOR_INCREMENTAL = 10;
constexpr int HALF_OF_INSERT_NUM_FOR_INCREMENTAL = 5;
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalUpdate_001)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL);

    // 创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 更新一半数据
    sql = "UPDATE t_empty set repr = '[2, 3, 4, -1]' where id <= 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);

    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
}

// 空表建索引，插入一批数据，显式事务更新一批数据发生回滚之后，查询; DiskAnn支持Undo后开放
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalUpdate_002)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL);
    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一条数据，无冲突
    sql = "INSERT INTO t_empty VALUES(11, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 更新一条数据，发生主键冲突
    sql = "UPDATE t_empty set id = 4, repr = '[2, 2, 2, 2]' where id = 5;";
    ASSERT_EQ(
        GMERR_PRIMARY_KEY_VIOLATION, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalUpdate_003)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL);
    // 开启事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "begin;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一条数据，无冲突
    sql = "INSERT INTO t_empty VALUES(11, '[2, 2, 2, 2]');";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 回滚事务
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "rollback;", StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 更新一条数据，发生主键冲突
    sql = "UPDATE t_empty set id = 12, repr = '[2, 2, 2, 2]' where id = 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
}

// 混合 DML 测试 + 开关库
TEST_F(StEmbSqlVectorDiskAnnIndex, DiskAnnIndexIncrementalHybrid)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建 IVF 向量索引 idx_diskann_cos
    std::string indexSql = "CREATE INDEX idx_diskann_cos ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));

    StEmbSqlClearActQryResultSet(nullptr);
    //  空表用索引查询, idx_diskann_cos
    std::string selectSqlCosine =
        "SELECT repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlQryResultSetExtendT exptQryResult{};
    StEmbSqlCheckDistanceResult(nullptr, 0);

    //  插入一批数据, 并创建 IVF 索引 idx_diskann_l2
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL);
    indexSql = "CREATE INDEX idx_diskann_l2 ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));

    // 重启
    StEmbSqlSuitCloseConn();
    StEmbSqlSuitOpenConn();
    conn = StEmbSqlGetConn();

    //  插入数据后查询，分别使用 idx_diskann_l2 和 idx_diskann_cos 查询
    std::string selectSqlL2 = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);

    // 删除所有数据后查询
    std::string deleteSql = "DELETE FROM t_empty;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, deleteSql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);

    // 重启之后再查询
    StEmbSqlSuitCloseConn();
    StEmbSqlSuitOpenConn();
    conn = StEmbSqlGetConn();
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, 0);

    // 创建 idx_diskann_l2_2
    indexSql = "CREATE INDEX idx_diskann_l2_2 ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));

    // 再次插入数据
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_FOR_INCREMENTAL);

    // 重启之后再查询
    StEmbSqlSuitCloseConn();
    StEmbSqlSuitOpenConn();
    conn = StEmbSqlGetConn();
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);

    // 删除索引 idx_diskann_l2 后查询
    indexSql = "DROP INDEX t_empty.idx_diskann_l2;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);

    // 删除索引 idx_diskann_l2_2 后查询
    indexSql = "DROP INDEX t_empty.idx_diskann_l2_2;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);

    // 重启
    StEmbSqlSuitCloseConn();
    StEmbSqlSuitOpenConn();
    conn = StEmbSqlGetConn();

    // 再次创建索引 idx_diskann_l2 后查询
    indexSql = "CREATE INDEX idx_diskann_l2 ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlL2.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, selectSqlCosine.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL);
}

constexpr int INSERT_NUM_MORE_THAN_ONE_BATCH = 30;

// 投影列为 id, distance or others
static void SqlStepQuery(GmeSqlStmtT *stmt, uint32_t expect, uint32_t colCnt, const string &errMsg)
{
    Status ret = GMERR_OK;
    uint32_t row = 0;
    double lastDistance = 0;
    while (ret = GmeSqlStep(stmt), ret == GMERR_OK) {
        uint32_t columnCnt = GmeSqlColumnCount(stmt);
        ASSERT_EQ(columnCnt, colCnt);
        GmeDbValueT value = GmeSqlColumnValue(stmt, 0);
        ASSERT_EQ(value.type, GME_DB_DATATYPE_INTEGER);
        value = GmeSqlColumnValue(stmt, 1);
        if (value.type == GME_DB_DATATYPE_FLOAT) {
            if (row > 0) {
                ASSERT_LE(lastDistance, value.value.doubleValue);
            }
            lastDistance = value.value.doubleValue;
        }
        row++;
    }
    ASSERT_EQ(GMERR_NO_DATA, ret);
    // 查到的数据可以小于等于 expect, 但不应该一条都没有
    ASSERT_LE(row, expect) << errMsg;
    if (row < expect) {
        printf("WARNING : actual row cnt = %u, expect = %u\n", row, expect);
        ASSERT_NE(row, 0) << errMsg;
    }
}

static void QueryTestWithReset(GmeConnT *conn, const string &sql, uint32_t colCnt, uint32_t expect)
{
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, sql.c_str(), sql.size(), &stmt, &unused));
    SqlStepQuery(stmt, expect, colCnt, sql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新查询
    SqlStepQuery(stmt, expect, colCnt, "After reset, " + sql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 空表建索引，插入数据后查询, prepare + step
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_001)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_MORE_THAN_ONE_BATCH);

    // L2 查询
    string selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty order by repr <-> '[1, 2, 3, 4]' limit 30;";
    QueryTestWithReset(conn, selectSql, T_DEFAULT_EXPECT_COL_CNT, INSERT_NUM_MORE_THAN_ONE_BATCH);
    // COSINE 查询
    selectSql = "SELECT id, repr <=> '[1, 2, 3, 4]' FROM t_empty order by repr <=> '[1, 2, 3, 4]' limit 30;";
    QueryTestWithReset(conn, selectSql, T_DEFAULT_EXPECT_COL_CNT, INSERT_NUM_MORE_THAN_ONE_BATCH);
}

static vector<string> MakeMixedQuerySql(uint32_t insertNum, uint32_t selection, uint32_t limit)
{
    // 构造查询语句
    string baseL2Sql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty ";
    string baseCosineSql = "SELECT id, repr <=> '[1, 2, 3, 4]' FROM t_empty ";
    string filterExpr = "WHERE id <= " + to_string(insertNum * selection / 100) + " ";
    string notIndexedExpr = "NOT INDEXED ";
    string indexedByScalarExpr = "INDEXED BY idx_id ";
    string indexedByL2VectorExpr = "INDEXED BY diskann_l2_idx ";
    string indexedByCosineVectorExpr = "INDEXED BY diskann_cos_idx ";
    string emptyIndexedByExpr = "";
    string orderByL2Expr = "ORDER BY repr <-> '[1, 2, 3, 4]' limit " + to_string(limit) + ";";
    string orderByCosineExpr = "ORDER BY repr <=> '[1, 2, 3, 4]' limit " + to_string(limit) + ";";

    vector<string> selectSql = {baseL2Sql + notIndexedExpr + filterExpr + orderByL2Expr,
        baseL2Sql + indexedByScalarExpr + filterExpr + orderByL2Expr,
        baseL2Sql + indexedByL2VectorExpr + filterExpr + orderByL2Expr,
        baseL2Sql + emptyIndexedByExpr + filterExpr + orderByL2Expr,
        baseCosineSql + notIndexedExpr + filterExpr + orderByCosineExpr,
        baseCosineSql + indexedByScalarExpr + filterExpr + orderByCosineExpr,
        baseCosineSql + indexedByCosineVectorExpr + filterExpr + orderByCosineExpr,
        baseCosineSql + emptyIndexedByExpr + filterExpr + orderByCosineExpr};
    return selectSql;
}

// 向标混合查询, 数据量 1K
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_002)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX idx_id ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    uint32_t insertNum = MIXED_QUERY_TEST_INSERT_NUM_1K;
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, insertNum);

    uint32_t limit = 30;
    // 选择命中率
    vector<uint32_t> selection = {10, 20, 30, 40, 50, 60, 70, 80, 90, 100};
    for (auto tmp : selection) {
        vector<string> selectSql = MakeMixedQuerySql(insertNum, tmp, limit);
        uint32_t expect = DB_MIN(limit, tmp * insertNum / 100);
        for (auto sqlS : selectSql) {
            QueryTestWithReset(conn, sqlS.c_str(), T_DEFAULT_EXPECT_COL_CNT, expect);
        }
    }
}

// 向标混合查询, 数据量 2K
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_003)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX diskann_cos_idx ON t_empty USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX idx_id ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    uint32_t insertNum = MIXED_QUERY_TEST_INSERT_NUM_1K * MIXED_QUERY_TEST_INSERT_NUM_RATIO;
    DiskAnnIndexIncrementalInsert(conn, insertNum);

    uint32_t limit = 30;
    // 选择命中率
    vector<uint32_t> selection = {10, 20, 30, 40, 50, 60, 70, 80, 90, 100};
    for (auto tmp : selection) {
        vector<string> selectSql = MakeMixedQuerySql(insertNum, tmp, limit);
        uint32_t expect = DB_MIN(limit, tmp * insertNum / 100);
        for (auto sqlS : selectSql) {
            QueryTestWithReset(conn, sqlS.c_str(), T_DEFAULT_EXPECT_COL_CNT, expect);
        }
    }
}

static void QueryResetTestWithIncrementalInsert(GmeConnT *conn, bool isCosine)
{
    string distName = isCosine ? "COSINE" : "L2";
    // 空表创建索引
    string indexSql =
        "CREATE INDEX diskann_" + distName + "_idx ON t_empty USING GSDISKANN(repr " + distName + ")" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, indexSql.c_str(), nullptr, nullptr, nullptr));

    // 构造查询语句
    string distType = isCosine ? "<=>" : "<->";
    uint32_t limit = 10;
    string selectSql = "SELECT id, '[1, 2, 3, 4]' " + distType +
                       " repr FROM t_empty WHERE id <= ? ORDER BY '[1, 2, 3, 4]' " + distType + " repr LIMIT " +
                       to_string(limit) + ";";

    // 预编译查询语句
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &stmt, &unused));

    // 空表查询
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, 0));
    ASSERT_EQ(GMERR_NO_DATA, GmeSqlStep(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));

    uint32_t insertNumPerBatch = 100;  // 每批插入 100 条数据
    uint32_t totalBatch = 5;           // 测试 5 个批次
    uint32_t startId = 1;              // 起始 id 为 1
    for (uint32_t i = 0; i < totalBatch; i++) {
        DiskAnnIndexIncrementalInsert(conn, insertNumPerBatch, startId);
        startId += insertNumPerBatch;
        Status ret = GmeSqlBindInt64(stmt, T_DEFAULT_BIND_IDX_ID, startId << 1);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t expect = DB_MIN((startId << 1), limit);
        SqlStepQuery(stmt, expect, T_DEFAULT_EXPECT_COL_CNT, "batch : " + to_string(i));
        ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    }
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 向标混合查询, L2, 增量插入
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_004)
{
    QueryResetTestWithIncrementalInsert(StEmbSqlGetConn(), false);
}

// 向标混合查询, COSINE, 增量插入
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_005)
{
    QueryResetTestWithIncrementalInsert(StEmbSqlGetConn(), true);
}

// 标量条件包含 IN
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_006)
{
    const EmbSqlCommonCaseT cases[] = {{.sql = "CREATE TABLE TESTIN(id int, name text, age int, repr floatvector(4));"},
        {.sql = "CREATE INDEX id_index ON TESTIN(id);"}, {.sql = "CREATE INDEX age_index ON TESTIN(age);"},
        {.sql = "CREATE INDEX id_name_index ON TESTIN(id, name);"},
        {.sql = "INSERT INTO TESTIN VALUES(0, 'n0', 18, '[1, 2, 3, 4]'), (1, 'n1', 19, '[2, 3, 4, 5]'), \
                    (2, 'n2', 18, '[1, 2, 6, 4]'), (3, 'n3', 19, '[2, 1, 4, 5]'), (4, 'n4', 18, '[4, 2, 3, 4]'), \
                    (5, 'n5', 19, '[7, 3, 4, 5]');"},
        {.sql = "SELECT id, age FROM TESTIN WHERE id IN (1, 2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"1", "19"}, {"2", "18"}}}},
        {.sql = "SELECT id, age FROM TESTIN WHERE id IN (1, 2, 1, 1, 2);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"1", "19"}, {"2", "18"}}}},
        {.sql = "SELECT id, age FROM TESTIN WHERE id IN (1, 2) AND id IN (3, 2, 4);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"2", "18"}}}},
        {.sql = "SELECT id, age FROM TESTIN WHERE age IN (19) AND id > 2;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"3", "19"}, {"5", "19"}}}},
        {.sql = "SELECT id, age FROM TESTIN WHERE age IN (?);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"0", "18"}, {"2", "18"}, {"4", "18"}}},
            .bindValue = {{"18"}, {GME_DB_DATATYPE_INTEGER}}},
        {.sql = "SELECT id, age FROM TESTIN WHERE age IN (?, ?) AND id IN (?, ?);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"0", "18"}, {"2", "18"}}},
            .bindValue = {{"18", "19", "0", "2"},
                {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}}},
        {.sql = "SELECT id, name FROM TESTIN WHERE id IN (1, 2, 3) AND name IN ('n2', 'n1');",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"1", "n1"}, {"2", "n2"}}}},
        {.sql = "SELECT id, name FROM TESTIN WHERE id IN (1, 2, 3, 3, 1) AND name IN ('n2', 'n1', 'n2', 'n1');",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"1", "n1"}, {"2", "n2"}}}},
        {.sql = "SELECT id, name FROM TESTIN WHERE id IN (1, ?, ?, 3, 2) AND name IN (?, ?, 'n2', 'n1');",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"1", "n1"}, {"2", "n2"}}},
            .bindValue = {{"2", "1", "n1", "n2"},
                {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_TEXT, GME_DB_DATATYPE_TEXT}}},
        {.sql = "SELECT id, name FROM TESTIN WHERE name > 'n1' AND id IN (1, 2, 3);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"2", "n2"}, {"3", "n3"}}}},
        {.sql = "SELECT id, age, name FROM TESTIN WHERE name IN ('n2', 'n1', 'n0') AND age IN (18);",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age", "name"}, {{"0", "18", "n0"}, {"2", "18", "n2"}}}},
        {.sql = "drop table TESTIN;"}};
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// 标量条件包含 IN
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_007)
{
    const EmbSqlCommonCaseT cases[] = {{.sql = "CREATE TABLE TESTIN(id int, name text, age int, repr floatvector(4));"},
        {.sql = "CREATE INDEX id_index ON TESTIN(id);"}, {.sql = "CREATE INDEX age_index ON TESTIN(age);"},
        {.sql = "CREATE INDEX id_name_index ON TESTIN(id, name);"},
        {.sql = "CREATE INDEX repr_index ON TESTIN USING GSDISKANN(repr L2);"},
        {.sql = "INSERT INTO TESTIN VALUES(0, 'n0', 18, '[1, 2, 3, 4]'), (1, 'n1', 19, '[2, 3, 4, 5]'), \
                    (2, 'n2', 18, '[1, 2, 6, 4]'), (3, 'n3', 19, '[2, 1, 4, 5]'), (4, 'n4', 18, '[4, 2, 3, 4]'), \
                    (5, 'n5', 19, '[7, 3, 4, 5]');"},
        {.sql = "SELECT id, name FROM TESTIN INDEXED BY id_name_index WHERE name > 'n1' AND id IN (1, 2, 3) \
                 ORDER BY repr <-> '[2,4,1,6]' LIMIT 10;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "name"}, {{"3", "n3"}, {"2", "n2"}}}},
        {.sql = "SELECT id, age FROM TESTIN INDEXED BY age_index WHERE age IN (?, ?) AND id IN (?, ?) \
                 ORDER BY repr <-> '[2,4,1,6]' LIMIT 10;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"0", "18"}, {"2", "18"}}},
            .bindValue = {{"18", "19", "0", "2"},
                {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER}}},
        {.sql = "SELECT id, age FROM TESTIN INDEXED BY id_index WHERE id IN (3, 2, 1) AND repr <-> '[2,4,1,6]' < 50 \
                 ORDER BY repr <-> '[2,4,1,6]' LIMIT 10;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"1", "19"}, {"3", "19"}, {"2", "18"}}}},
        {.sql = "SELECT id, age FROM TESTIN INDEXED BY id_index WHERE id IN (1, ?, ?, 3, 2) AND  \
                 name IN (?, ?, 'n2', 'n1') AND repr <-> '[2,4,1,6]' < 50 ORDER BY repr <-> '[2,4,1,6]' LIMIT 10;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"1", "19"}, {"2", "18"}}},
            .bindValue = {{"2", "1", "n1", "n2"},
                {GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_INTEGER, GME_DB_DATATYPE_TEXT, GME_DB_DATATYPE_TEXT}}},
        {.sql = "SELECT id, age FROM TESTIN INDEXED BY id_name_index WHERE id IN (1, 2, 3, 3, 1) AND \
                 name IN ('n2', 'n1', 'n2', 'n1') ORDER BY repr <-> '[2,4,1,6]' LIMIT 10;",
            .expectRet = GMERR_OK,
            .exptQryResult = {{"id", "age"}, {{"1", "19"}, {"2", "18"}}}},
        {.sql = "drop table TESTIN;"}};
    GmeConnT *conn = StEmbSqlGetConn();
    uint32_t bound = sizeof(cases) / sizeof(cases[0]);
    EXPECT_EQ(GMERR_OK, StEmbSqlRunInPrepareMode(cases, bound, conn));
}

// 标量条件包含 IN, Reset 重新查询, 带占位符
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_008)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_MORE_THAN_ONE_BATCH);

    // 向标混合查询, 实际标量索引扫描
    string selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty INDEXED BY id_idx WHERE id IN (?, ?, ?) \
                        ORDER BY repr <-> '[1, 2, 3, 4]' limit 10;";
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &stmt, &unused));
    uint32_t bindIdx = 1;
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, 1));
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, 1));
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, 1));
    uint32_t expect = 1;
    SqlStepQuery(stmt, expect, T_DEFAULT_EXPECT_COL_CNT, selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新查询
    bindIdx = 1;
    uint32_t bind2 = 2;
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, bind2));
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, 1));
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, bind2));
    SqlStepQuery(stmt, bind2, T_DEFAULT_EXPECT_COL_CNT, "After reset, " + selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 标量条件包含 IN, Reset 重新查询, 不带占位符
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_009)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));

    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_MORE_THAN_ONE_BATCH);

    // 向标混合查询, 实际标量索引扫描
    string selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty INDEXED BY id_idx WHERE id IN (1, 2, 1, 3) "
                       "ORDER BY repr <-> '[1, 2, 3, 4]' limit 10;";
    uint32_t expect = 3;
    QueryTestWithReset(conn, selectSql, T_DEFAULT_EXPECT_COL_CNT, expect);
}

// 标量条件包含 IN, Reset 重新查询, 带占位符, 数据量 0.5K, IN 列表 400 个占位符
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_010)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, MIXED_QUERY_TEST_INSERT_NUM_1K / MIXED_QUERY_TEST_INSERT_NUM_RATIO);
    // 向标混合查询, 实际标量索引扫描
    uint32_t inListNum = 400;
    string selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql += ") ORDER BY repr <-> '[1, 2, 3, 4]' limit 10;";
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &stmt, &unused));
    uint32_t bindIdx = 1;
    uint32_t repeat = 5;  // 绑定的 id, 限定在 [1, 5] 之间
    for (uint32_t i = 0; i < inListNum; i++) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, (int64_t)(i % repeat) + 1));
    }
    SqlStepQuery(stmt, repeat, T_DEFAULT_EXPECT_COL_CNT, selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新查询, 绑定的 id 不重复
    bindIdx = 1;
    for (uint32_t i = 1; i <= inListNum; i++) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, (int64_t)(i)));
    }
    uint32_t expect = 10;  // top 10
    SqlStepQuery(stmt, expect, T_DEFAULT_EXPECT_COL_CNT, "After reset, " + selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 标量条件包含 IN, Reset 重新查询, 不带占位符, 数据量 0.5K, IN 列表 400 个
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_011)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, MIXED_QUERY_TEST_INSERT_NUM_1K / MIXED_QUERY_TEST_INSERT_NUM_RATIO);

    uint32_t inListNum = 400;
    string selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty INDEXED BY id_idx WHERE id IN (1";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", " + to_string(i + 1);
    }
    selectSql += ") ORDER BY repr <-> '[1, 2, 3, 4]' limit 10;";
    uint32_t expect = 10;  // top 10
    QueryTestWithReset(conn, selectSql, T_DEFAULT_EXPECT_COL_CNT, expect);

    uint32_t repeat = 5;
    selectSql = "SELECT id, repr <-> '[1, 2, 3, 4]' FROM t_empty INDEXED BY id_idx WHERE id IN (1";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", " + to_string((i % repeat) + 1);
    }
    selectSql += ") ORDER BY repr <-> '[1, 2, 3, 4]' limit 10;";
    QueryTestWithReset(conn, selectSql, T_DEFAULT_EXPECT_COL_CNT, repeat);
}

static void TestQueryOrderByPushDownScan(
    GmeConnT *conn, const string &selectSql, uint32_t inListNum, uint32_t limit, uint32_t offset)
{
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &stmt, &unused));
    float *vec = GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM);
    uint32_t bindIdx = 1;
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, bindIdx++, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    for (uint32_t i = 1; i <= inListNum; i++) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, (int64_t)(i)));
    }
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, bindIdx++, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlBindDouble(stmt, bindIdx++, DB_MAX_FLOAT));
    ASSERT_EQ(GMERR_OK, GmeSqlBindFloatVector(stmt, bindIdx++, vec, T_DEFAULT_VECTOR_DIM, nullptr));
    uint32_t expect = DB_MIN(limit, inListNum);
    SqlStepQuery(stmt, expect - offset, T_DEFAULT_EXPECT_COL_CNT, selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新查询, id 绑定的值最大为 200
    bindIdx = 2;
    uint32_t half = 2;
    uint32_t idMax = inListNum / half;
    for (uint32_t i = 1; i <= inListNum; i++) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, (int64_t)((i % idMax) + 1)));
    }
    expect = DB_MIN(limit, idMax);
    SqlStepQuery(stmt, expect - offset, T_DEFAULT_EXPECT_COL_CNT, "After reset, " + selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
    delete[] vec;
}

// 标量条件包含与order by表达式相同的表达式
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_012)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, MIXED_QUERY_TEST_INSERT_NUM_1K / MIXED_QUERY_TEST_INSERT_NUM_RATIO);
    uint32_t inListNum = 400;
    uint32_t limit = 10;
    string selectSql = "SELECT id, repr <-> ? FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql += ") AND repr <-> ? < ? ORDER BY repr <-> ? LIMIT " + to_string(limit) + ";";
    TestQueryOrderByPushDownScan(conn, selectSql, inListNum, limit, 0);
    // limit 10 offset 20
    selectSql = "SELECT id, repr <-> ? FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql += ") AND repr <-> ? < ? ORDER BY repr <-> ? LIMIT " + to_string(limit) + " OFFSET 20;";
    TestQueryOrderByPushDownScan(conn, selectSql, inListNum, limit, 0);
}

// 标量条件包含与order by表达式相同的表达式, limit 10000;
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalSelect_013)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, MIXED_QUERY_TEST_INSERT_NUM_1K / MIXED_QUERY_TEST_INSERT_NUM_RATIO);
    uint32_t inListNum = 40;
    uint32_t limit = 10000;
    string selectSql = "SELECT id, repr <-> ? FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql += ") AND repr <-> ? < ? ORDER BY repr <-> ? LIMIT " + to_string(limit) + ";";
    TestQueryOrderByPushDownScan(conn, selectSql, inListNum, limit, 0);
    // limit 10000 offset 10
    uint32_t offset = 10;
    selectSql = "SELECT id, repr <-> ? FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql +=
        ") AND repr <-> ? < ? ORDER BY repr <-> ? LIMIT " + to_string(limit) + " OFFSET " + to_string(offset) + ";";
    TestQueryOrderByPushDownScan(conn, selectSql, inListNum, limit, offset);
}

static void TestBindFloatVector(
    GmeSqlStmtT *stmt, uint32_t inListNum, const vector<float *> &vectors, const vector<uint32_t> &bindVectorIdx)
{
    uint32_t bindIdx = 1;
    uint32_t vectorIdx = 0;
    ASSERT_EQ(GMERR_OK,
        GmeSqlBindFloatVector(stmt, bindIdx++, vectors[bindVectorIdx[vectorIdx++]], T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK,
        GmeSqlBindFloatVector(stmt, bindIdx++, vectors[bindVectorIdx[vectorIdx++]], T_DEFAULT_VECTOR_DIM, nullptr));
    bindIdx += inListNum;  // id 列的值不用重新绑定了
    ASSERT_EQ(GMERR_OK,
        GmeSqlBindFloatVector(stmt, bindIdx++, vectors[bindVectorIdx[vectorIdx++]], T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlBindDouble(stmt, bindIdx++, DB_MAX_FLOAT));
    ASSERT_EQ(GMERR_OK,
        GmeSqlBindFloatVector(stmt, bindIdx++, vectors[bindVectorIdx[vectorIdx++]], T_DEFAULT_VECTOR_DIM, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlBindDouble(stmt, bindIdx++, DB_MAX_FLOAT));
    ASSERT_EQ(GMERR_OK,
        GmeSqlBindFloatVector(stmt, bindIdx++, vectors[bindVectorIdx[vectorIdx++]], T_DEFAULT_VECTOR_DIM, nullptr));
}

// 标量条件包含与order by表达式使用向量占位符, 绑定不同参数值;
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TestDiskAnnIndexIncrementalSelect_014)
{
    GmeConnT *conn = StEmbSqlGetConn();
    // 空表创建索引
    std::string sql = "CREATE INDEX diskann_l2_idx ON t_empty USING GSDISKANN(repr L2);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    sql = "CREATE INDEX id_idx ON t_empty(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    // 插入一批数据, 主键无冲突, 插入成功
    DiskAnnIndexIncrementalInsert(conn, INSERT_NUM_MORE_THAN_ONE_BATCH);
    uint32_t inListNum = 40;
    uint32_t limit = 10;
    string selectSql = "SELECT id, repr <-> ?, repr <-> ? FROM t_empty INDEXED BY id_idx WHERE id IN (?";
    for (uint32_t i = 1; i < inListNum; i++) {
        selectSql += ", ?";
    }
    selectSql += ") AND repr <-> ? < ? AND repr <-> ? < ? ORDER BY repr <-> ? LIMIT " + to_string(limit) + ";";
    vector<float *> vectors = {GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM, 0, DB_MAX_UINT8),
        GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM, 0, DB_MAX_UINT8),
        GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM, DB_MAX_UINT8, DB_MAX_UINT16),
        GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM, DB_MAX_UINT8, DB_MAX_UINT16),
        GenerateRandomFloatVector(T_DEFAULT_VECTOR_DIM, DB_MAX_UINT16, RAND_MAX)};
    GmeSqlStmtT *stmt = nullptr;
    const char *unused = nullptr;
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &stmt, &unused));
    // 先绑 id
    uint32_t bindIdx = 3;
    for (uint32_t i = 1; i <= inListNum; i++) {
        ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmt, bindIdx++, (int64_t)(i)));
    }
    vector<uint32_t> bindVectorIdx1 = {0, 1, 2, 3, 4};
    TestBindFloatVector(stmt, inListNum, vectors, bindVectorIdx1);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmeSqlStep(stmt));  // 过滤和投影中都不与order by绑的向量字面量相同
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新绑定
    vector<uint32_t> bindVectorIdx2 = {4, 0, 4, 2, 4};
    TestBindFloatVector(stmt, inListNum, vectors, bindVectorIdx2);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmeSqlStep(stmt));  // 过滤和投影中与order by绑的向量字面量部分相同
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    // Reset 后重新绑定相同的向量字面量，可以正常执行
    vector<uint32_t> bindVectorIdx3 = {4, 4, 4, 4, 4};
    TestBindFloatVector(stmt, inListNum, vectors, bindVectorIdx3);
    uint32_t expect = DB_MIN(limit, inListNum);
    SqlStepQuery(stmt, expect, T_DEFAULT_EXPECT_COL_CNT + 1, "After reset, " + selectSql);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
    for (auto vec : vectors) {
        delete[] vec;
    }
}

// diskann out_degree=1200, pageSize=8k, 预期报错 GMERR_INVALID_PARAMETER_VALUE
// 与终端不一样的地方是GmeOpen加载的是ini文件，里面配置是32K，调成8K 可过
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_CreateIndexWithInvalidPageSize)
{
    Status ret = GMERR_OK;
    GmeConnT *conn = StEmbSqlGetConn();
    ret = GmeSqlExecute(
        conn, "CREATE TABLE tbl (id integer, name text, repr floatvector(128));", nullptr, nullptr, nullptr);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmeSqlExecute(conn, "CREATE INDEX diskann_l2_idx ON tbl USING GSDISKANN(repr L2) WITH (out_degree=1200);",
        nullptr, nullptr, nullptr);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmeSqlExecute(conn, "DROP TABLE tbl;", nullptr, nullptr, nullptr);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, SelectAndInsertWithDiskAnnIndex001)
{
    Status ret = GMERR_OK;
    const char *cfgFilePath = StEmbSqlGetConfigPath();
    GmeConnT *conn = nullptr;
    ret = GmeOpen(cfgFilePath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 建表，维度1024
    std::string sqlCreateTable =
        "CREATE TABLE t_large_num(id int primary key, repr floatvector(" + std::to_string(128) + "));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlCreateTable.c_str()), nullptr, nullptr, nullptr));
    // 插入100条数据
    for (uint16_t i = 0; i < 100; i++) {
        std::string sqlInsert = "INSERT INTO t_large_num VALUES(1000000" + std::to_string(i) + ", '" +
                                GetRandVector(MAX_INT_PART, 128) + "');";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlInsert.c_str()), nullptr, nullptr, nullptr));
    }
    // 创建索引
    std::string sqlCreateIndex = "CREATE INDEX diskann_l2_idx ON t_large_num USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlCreateIndex.c_str()), nullptr, nullptr, nullptr));
    // 查询
    std::string sqlSelect =
        "SELECT * FROM t_large_num ORDER BY repr <-> '" + GetRandVector(MAX_INT_PART, 128) + "' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlSelect.c_str()), nullptr, nullptr, nullptr));

    // 插入1000条数据
    for (uint16_t i = 0; i < 10; i++) {
        std::string sqlInsert = "INSERT INTO t_large_num VALUES(2000000" + std::to_string(i) + ", '" +
                                GetRandVector(MAX_INT_PART, 128) + "');";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlInsert.c_str()), nullptr, nullptr, nullptr));
    }

    // 查询第二次
    sqlSelect = "SELECT * FROM t_large_num ORDER BY repr <-> '" + GetRandVector(MAX_INT_PART, 128) + "' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlSelect.c_str()), nullptr, nullptr, nullptr));

    // 删除表
    std::string sqlDropIndex = "DROP TABLE t_large_num;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlDropIndex.c_str()), nullptr, nullptr, nullptr));

    ASSERT_EQ(GMERR_OK, GmeClose(conn));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, SelectAndDeleteWithDiskAnnIndex002)
{
    Status ret = GMERR_OK;
    const char *cfgFilePath = StEmbSqlGetConfigPath();
    GmeConnT *conn = nullptr;
    ret = GmeOpen(cfgFilePath, GME_OPEN_CREATE, &conn);
    EXPECT_EQ(ret, GMERR_OK);
    // 建表，维度128
    std::string sqlCreateTable =
        "CREATE TABLE t_large_num(id int primary key, repr floatvector(" + std::to_string(128) + "));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlCreateTable.c_str()), nullptr, nullptr, nullptr));
    // 插入1000条数据
    for (uint16_t i = 0; i < 100; i++) {
        std::string sqlInsert = "INSERT INTO t_large_num VALUES(" + std::to_string(1000000 + i) + ", '" +
                                GetRandVector(MAX_INT_PART, 128) + "');";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlInsert.c_str()), nullptr, nullptr, nullptr));
    }
    // 创建索引
    std::string sqlCreateIndex = "CREATE INDEX diskann_l2_idx ON t_large_num USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlCreateIndex.c_str()), nullptr, nullptr, nullptr));
    // 查询
    std::string sqlSelect =
        "SELECT * FROM t_large_num ORDER BY repr <-> '" + GetRandVector(MAX_INT_PART, 128) + "' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlSelect.c_str()), nullptr, nullptr, nullptr));

    // 删除100条数据
    for (uint16_t i = 0; i < 10; i++) {
        std::string sqlDelete = "DELETE FROM t_default WHERE id = " + std::to_string(1000000 + i) + ";";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlDelete.c_str()), nullptr, nullptr, nullptr));
    }

    // 查询第二次
    sqlSelect = "SELECT * FROM t_large_num ORDER BY repr <-> '" + GetRandVector(MAX_INT_PART, 128) + "' LIMIT 5;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlSelect.c_str()), nullptr, nullptr, nullptr));

    // 删除表
    std::string sqlDropIndex = "DROP TABLE t_large_num;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, const_cast<char *>(sqlDropIndex.c_str()), nullptr, nullptr, nullptr));

    ASSERT_EQ(GMERR_OK, GmeClose(conn));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByDesc)
{
    Status ret;
    GmeConnT *conn = StEmbSqlGetConn();
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;

    std::string sqlCreateIdx = "CREATE INDEX diskann_l2_idx ON t_default USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    // 返回结果为空
    std::string selectSql = "SELECT id FROM t_default ORDER BY repr <-> '[1,2,3,4]' DESC LIMIT 4;";
    uint32_t len = (uint32_t)(selectSql.length() + 1);
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), len, &stmt, &unused));

    const uint32_t resColNum = 1;
    const uint32_t resRowNum = 4;
    vector<int> resCol0 = {10000003, 10000004, 10000002, 10000001};

    uint32_t count = 0;
    for (int32_t i = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; ++i) {
        ++count;
        ASSERT_LE(count, resRowNum);
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        ASSERT_EQ(resColNum, columnCount);
        ASSERT_EQ(GMERR_OK, StEmbSqlColumnCheckInt(stmt, 0, resCol0[i]));
    }
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ASSERT_EQ(count, resRowNum);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(ret, GMERR_OK);

    std::string sqlDropIdx = "Drop INDEX t_default.diskann_l2_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx.c_str(), nullptr, nullptr, nullptr));
}

TEST_F(StEmbSqlVectorDiskAnnIndex, OrderByAsc)
{
    Status ret;
    GmeConnT *conn = StEmbSqlGetConn();
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;

    std::string sqlCreateIdx = "CREATE INDEX diskann_l2_idx ON t_default USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx.c_str(), nullptr, nullptr, nullptr));

    // 返回结果为空
    std::string selectSql = "SELECT id FROM t_default ORDER BY repr <-> '[1,2,3,4]' ASC LIMIT 4;";
    uint32_t len = (uint32_t)(selectSql.length() + 1);
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), len, &stmt, &unused));

    const uint32_t resColNum = 1;
    const uint32_t resRowNum = 4;
    vector<int> resCol0 = {10000001, 10000002, 10000004, 10000003};

    uint32_t count = 0;
    for (int32_t i = 0; (ret = GmeSqlStep(stmt)) == GMERR_OK; ++i) {
        ++count;
        ASSERT_LE(count, resRowNum);
        uint32_t columnCount = GmeSqlColumnCount(stmt);
        ASSERT_EQ(resColNum, columnCount);
        ASSERT_EQ(GMERR_OK, StEmbSqlColumnCheckInt(stmt, 0, resCol0[i]));
    }
    ASSERT_EQ(ret, GMERR_NO_DATA);
    ASSERT_EQ(count, resRowNum);
    ret = GmeSqlFinalize(stmt);
    ASSERT_EQ(ret, GMERR_OK);

    std::string sqlDropIdx = "Drop INDEX t_default.diskann_l2_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDropIdx.c_str(), nullptr, nullptr, nullptr));
}

// 创建索引1, prepare insert stmt, 插入一批数据, (没有 finalize stmt), 创建索引2
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_001)
{
    GmeConnT *conn = StEmbSqlGetConn();

    string sql = "CREATE TABLE t_tmp(id int primary key, repr floatvector(4));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 创建索引 1
    sql = "CREATE INDEX diskann_l2_idx_1 ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    string insertSql = "INSERT INTO t_tmp VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql.c_str(), insertSql.size(), &stmt, &unused));

    // 插入数据, 主键无冲突, 插入成功
    DiskAnnIndexStepInsert(stmt, T_DEFAULT_VECTOR_DIM, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM, 0, GMERR_OK);

    // 查询
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "SELECT id FROM t_tmp;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckActQryResultSetSize(nullptr, 1, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    StEmbSqlClearActQryResultSet(nullptr);

    // 创建索引 2
    sql = "CREATE INDEX diskann_l2_idx_2 ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE t_tmp;", nullptr, nullptr, nullptr));
}

// 创建索引1, prepare insert stmt, 插入一批数据, (没有 finalize stmt), 创建索引2, 使用相同 stmt 插入, 查询
TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_002)
{
    GmeConnT *conn = StEmbSqlGetConn();

    string sql = "CREATE TABLE t_tmp(id int primary key, repr floatvector(4));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 创建索引 1
    sql = "CREATE INDEX diskann_l2_idx ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    const char *insertSql = "INSERT INTO t_tmp VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused));

    // 插入数据, 主键无冲突, 插入成功
    DiskAnnIndexStepInsert(stmt, T_DEFAULT_VECTOR_DIM, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM, 0, GMERR_OK);

    // 查询
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "SELECT id FROM t_tmp;", StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckActQryResultSetSize(nullptr, 1, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    StEmbSqlClearActQryResultSet(nullptr);

    // 创建索引 2
    sql = "CREATE INDEX diskann_cosine_idx ON t_tmp USING GSDISKANN(repr COSINE)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 插入数据, 主键无冲突, 插入成功
    DiskAnnIndexStepInsert(stmt, T_DEFAULT_VECTOR_DIM, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM,
        INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM, GMERR_SCHEMA_CHANGED);

    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));

    // COSINE 查询, 确保用新创建的索引能查询到数据
    sql = "SELECT repr <=> '[5, 6, 7, 8]' FROM t_tmp ORDER BY repr <=> '[5, 6, 7, 8]' LIMIT 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM + INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    StEmbSqlClearActQryResultSet(nullptr);

    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE t_tmp;", nullptr, nullptr, nullptr));
}

static void ScanStepAndCheck(GmeSqlStmtT *stmt, uint32_t expectCnt)
{
    Status ret = GMERR_OK;
    uint32_t count = 0;
    while ((ret = GmeSqlStep(stmt)) == GMERR_OK) {
        count++;
    }
    ASSERT_LE(count, expectCnt);
    ASSERT_EQ(GMERR_NO_DATA, ret);
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmt));
}

static void TestPrepareMultipleStmt(GmeConnT *conn, map<string, GmeSqlStmtT *> &stmtMap, uint32_t *totalTupleNum)
{
    // 预编译插入语句
    const char *unused = nullptr;
    GmeSqlStmtT *insertStmt = nullptr;
    string insertSql = "INSERT INTO t_tmp VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql.c_str(), insertSql.size(), &insertStmt, &unused));
    DiskAnnIndexStepInsert(insertStmt, T_DEFAULT_VECTOR_DIM, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM, 0, GMERR_OK);
    stmtMap.insert(map<string, GmeSqlStmtT *>::value_type("insert", insertStmt));

    // 预编译 L2 查询语句, 使用 diskann_l2_idx 查询, limit 20 (表中数据不会超过 20)
    GmeSqlStmtT *l2QueryStmt = nullptr;
    string selectSql = "SELECT * FROM t_tmp ORDER BY repr <-> '[5, 6, 7, 8]' LIMIT 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &l2QueryStmt, &unused));
    ScanStepAndCheck(l2QueryStmt, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    stmtMap.insert(map<string, GmeSqlStmtT *>::value_type("l2Query", l2QueryStmt));

    // 预编译全表顺序扫描语句
    GmeSqlStmtT *seqScanStmt = nullptr;
    selectSql = "SELECT * FROM t_tmp;";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &seqScanStmt, &unused));
    ScanStepAndCheck(seqScanStmt, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    stmtMap.insert(map<string, GmeSqlStmtT *>::value_type("seqScan", seqScanStmt));

    // 预编译 Btree 的索引扫描语句
    GmeSqlStmtT *btreeScanStmt = nullptr;
    selectSql = "SELECT * FROM t_tmp INDEXED BY btree_id_idx;";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, selectSql.c_str(), selectSql.size(), &btreeScanStmt, &unused));
    ScanStepAndCheck(btreeScanStmt, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    stmtMap.insert(map<string, GmeSqlStmtT *>::value_type("btreeScan", btreeScanStmt));

    // 预编译删除语句, 带 SeqScan
    GmeSqlStmtT *deleteStmt = nullptr;
    string deleteSql = "DELETE FROM t_tmp NOT INDEXED WHERE id = ?;";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, deleteSql.c_str(), deleteSql.size(), &deleteStmt, &unused));
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(deleteStmt, 1, 0));  // id = 0
    ASSERT_EQ(GMERR_OK, GmeSqlStep(deleteStmt));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(deleteStmt));
    stmtMap.insert(map<string, GmeSqlStmtT *>::value_type("delete", deleteStmt));
    *totalTupleNum = INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM - 1;
}

static void TestMetaChanged(GmeConnT *conn, TestMetaChangeTypeE changeType)
{
    if (changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_L2 ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_L2_WITH_REBUILD) {
        // 删除索引 diskann_l2_idx
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP INDEX t_tmp.diskann_l2_idx;", nullptr, nullptr, nullptr));
    }
    if (changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE_WITH_REBUILD) {
        // 删除索引 btree_id_idx
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP INDEX t_tmp.btree_id_idx;", nullptr, nullptr, nullptr));
    }

    if (changeType != TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL) {
        // 创建索引 diskann_cosine_idx
        string sql = "CREATE INDEX diskann_cosine_idx ON t_tmp USING GSDISKANN(repr COSINE)" + g_sqlTail;
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    }

    if (changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_L2_WITH_REBUILD) {
        string sql = "CREATE INDEX diskann_l2_idx ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    }

    if (changeType == TestMetaChangeTypeE::TEST_TABLE_DROP ||
        changeType == TestMetaChangeTypeE::TEST_TABLE_REBUILD_SAME ||
        changeType == TestMetaChangeTypeE::TEST_TABLE_REBUILD_DIFF) {
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE t_tmp;", nullptr, nullptr, nullptr));
    }
    if (changeType == TestMetaChangeTypeE::TEST_TABLE_REBUILD_SAME) {
        string sql = "CREATE TABLE t_tmp(id int, repr floatvector(4));";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    }
    if (changeType == TestMetaChangeTypeE::TEST_TABLE_REBUILD_DIFF) {
        string sql = "CREATE TABLE t_tmp(name text, id1 int, repr1 floatvector(8));";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    }
}

// 表中的索引元数据发生变化之后, DML(不带 IndexScan)/SeqScan 可以成功, IndexScan/VectorIndexScan 失败
// 索引扫描是需要限定索引的, 即使是同名索引重建了, 也可能会发生索引字段、向量距离类型的变化, 统一不允许在变更后重新执行
// 预置: btree_id_idx + diskann_l2_idx
// TEST_INDEX_DELETE_ALL : 删除 btree_id_idx 和 diskann_l2_idx
// TEST_INDEX_DELETE_BTREE : 删除 btree_id_idx, 创建 diskann_cosine_idx
// TEST_INDEX_DELETE_L2 : 删除 diskann_l2_idx, 创建 diskann_cosine_idx
// TEST_INDEX_DELETE_L2_WITH_REBUILD : 删除 diskann_l2_idx, 创建 diskann_cosine_idx, 再重建 diskann_l2_idx
// TEST_INDEX_DELETE_BTREE_WITH_REBUILD : 删除 btree_id_idx, 创建 diskann_cosine_idx, 再重建 btree_id_id
static void TestIndexMetaChangeAfterPrepare(GmeConnT *conn, TestMetaChangeTypeE changeType)
{
    string sql = "CREATE TABLE t_tmp(id int, repr floatvector(4));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 创建索引 btree_id_idx
    sql = "CREATE UNIQUE INDEX btree_id_idx ON t_tmp(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    // 创建索引 diskann_l2_idx
    sql = "CREATE INDEX diskann_l2_idx ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));

    map<string, GmeSqlStmtT *> stmtMap;
    uint32_t totalTupleNum = 0;
    TestPrepareMultipleStmt(conn, stmtMap, &totalTupleNum);
    TestMetaChanged(conn, changeType);

    // 插入数据, 无冲突, 预期成功
    DiskAnnIndexStepInsert(stmtMap["insert"], T_DEFAULT_VECTOR_DIM, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM,
        INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM, GMERR_SCHEMA_CHANGED);
    totalTupleNum += INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM;

    // 元数据变化后，该元数据对应的stmt不能在执行，统一返回GMERR_WRONG_STMT_OBJECT错误
    if (changeType <= TestMetaChangeTypeE::TEST_TABLE_DROP) {
        ASSERT_EQ(GMERR_SCHEMA_CHANGED, GmeSqlStep(stmtMap["l2Query"]));
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE t_tmp;", nullptr, nullptr, nullptr));
        return;
    } else {
        ScanStepAndCheck(stmtMap["l2Query"], totalTupleNum);
    }

    if (changeType != TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL) {
        sql = "SELECT repr <=> '[5, 6, 7, 8]' FROM t_tmp ORDER BY repr <=> '[5, 6, 7, 8]' LIMIT 20;";
        string explainSql = (string) "EXPLAIN " + sql;
        StEmbSqlExplainTestT testCase[] = {{explainSql.c_str(), "diskann_cosine_idx", GMERR_OK}};
        StEmbSqlCheckExplainResult(conn, testCase, 1);
        // COSINE 查询, 确保用新创建的索引能查询到数据
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
        StEmbSqlCheckDistanceResult(nullptr, totalTupleNum);

        StEmbSqlClearActQryResultSet(nullptr);
    }

    // 顺序扫描, 预期成功
    ScanStepAndCheck(stmtMap["seqScan"], totalTupleNum);

    // 带顺序扫描的删除, 预期成功
    ASSERT_EQ(GMERR_OK, GmeSqlBindInt64(stmtMap["delete"], 1, 1));  // id = 1
    ASSERT_EQ(GMERR_SCHEMA_CHANGED, GmeSqlStep(stmtMap["delete"]));
    ASSERT_EQ(GMERR_OK, GmeSqlReset(stmtMap["delete"]));
    totalTupleNum -= 1;

    // 因为 Btree 不支持插入数据后创建, 这里先把已有数据删除再重建
    if (changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE_WITH_REBUILD) {
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DELETE FROM t_tmp;", nullptr, nullptr, nullptr));
        sql = "CREATE UNIQUE INDEX btree_id_idx ON t_tmp(id);";
        ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
        DiskAnnIndexStepInsert(stmtMap["insert"], T_DEFAULT_VECTOR_DIM, totalTupleNum, 0, GMERR_SCHEMA_CHANGED);
    }
    // Btree 索引扫描, 只有 TEST_INDEX_DELETE_L2_WITH_REBUILD 和 TEST_INDEX_DELETE_L2 成功
    if (changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE ||
        changeType == TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE_WITH_REBUILD) {
        ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, GmeSqlStep(stmtMap["btreeScan"]));
    } else {
        ScanStepAndCheck(stmtMap["btreeScan"], totalTupleNum);
    }
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE t_tmp;", nullptr, nullptr, nullptr));
    // 不 finalize 也应该保证运行不出现问题
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_003)
{
    TestIndexMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_INDEX_DELETE_ALL);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_004)
{
    TestIndexMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_005)
{
    TestIndexMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_INDEX_DELETE_L2);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_006)
{
    TestIndexMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_INDEX_DELETE_BTREE_WITH_REBUILD);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_IndexMetaChangeAfterPrepare_007)
{
    TestIndexMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_INDEX_DELETE_L2_WITH_REBUILD);
}

// TEST_TABLE_DROP : 删除表
// TEST_TABLE_REBUILD_SAME : 删除表后，重建一样的表
// TEST_TABLE_REBUILD_DIFF : 删除表后，重建不一样的表
static void TestTableMetaChangeAfterPrepare(GmeConnT *conn, TestMetaChangeTypeE changeType)
{
    string sql = "CREATE TABLE t_tmp(id int, repr floatvector(4));";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    // 创建索引 diskann_l2_idx
    sql = "CREATE INDEX diskann_l2_idx ON t_tmp USING GSDISKANN(repr L2)" + g_sqlTail;
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    // 创建索引 btree_id_idx
    sql = "CREATE UNIQUE INDEX btree_id_idx ON t_tmp(id);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    map<string, GmeSqlStmtT *> stmtMap;
    uint32_t totalTupleNum = 0;
    TestPrepareMultipleStmt(conn, stmtMap, &totalTupleNum);
    TestMetaChanged(conn, changeType);

    // 五种类型的预编译语句, 预期报错都是 GMERR_UNDEFINED_TABLE
    for (auto &stmt : stmtMap) {
        ASSERT_EQ(GMERR_UNDEFINED_TABLE, GmeSqlStep(stmt.second));
    }

    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, "DROP TABLE IF EXISTS t_tmp;", nullptr, nullptr, nullptr));
    // 不 finalize 也应该保证运行不出现问题
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TableMetaChangeAfterPrepare_001)
{
    TestTableMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_TABLE_DROP);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TableMetaChangeAfterPrepare_002)
{
    TestTableMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_TABLE_REBUILD_SAME);
}

TEST_F(StEmbSqlVectorDiskAnnIndex, DISABLED_TableMetaChangeAfterPrepare_003)
{
    TestTableMetaChangeAfterPrepare(StEmbSqlGetConn(), TestMetaChangeTypeE::TEST_TABLE_REBUILD_DIFF);
}

// 建表指定INDEX LPASMEM隐藏列建多个索引进行插入和删除
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_WithIndexLpasMemPerf)
{
    std::string sql = "CREATE TABLE t_perf_lpasmem(id int primary key, repr floatvector(4), INDEX LPASMEM);";
    std::string sqlInsert1 = "INSERT INTO t_perf_lpasmem VALUES(10000001, '[1, 2, 3, 4]');";
    std::string sqlInsert2 = "INSERT INTO t_perf_lpasmem VALUES(10000002, '[2.0, 3.0, 4.0, 5.0]');";
    std::string sqlInsert3 =
        "INSERT INTO t_perf_lpasmem VALUES(10000003, '[+3.0, -4., 5, .6]'), (10000004, '[0.1, 0.1, 0.1, 0.1]');";
    GmeConnT *conn = StEmbSqlGetConn();
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert3.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert2.c_str(), nullptr, nullptr, nullptr));
    std::string sqlDrop = "DROP TABLE t_perf_lpasmem;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDrop.c_str(), nullptr, nullptr, nullptr));
}

// 建表指定INDEX LPASMEM隐藏列建多个索引进行插入和删除，以及不能向隐藏列显示的赋值
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_WithIndexLpasMemPerf2)
{
    std::string sql = "CREATE TABLE t_perf_lpasmem(id int primary key, repr floatvector(4), INDEX LPASMEM);";
    std::string sqlInsert1 = "INSERT INTO t_perf_lpasmem VALUES(10000001, '[1, 2, 3, 4]');";
    std::string sqlInsert2 = "INSERT INTO t_perf_lpasmem VALUES(10000002, '[2.0, 3.0, 4.0, 5.0]', 0);";
    std::string sqlInsert3 =
        "INSERT INTO t_perf_lpasmem VALUES(10000003, '[+3.0, -4., 5, .6]'), (10000004, '[0.1, 0.1, 0.1, 0.1]');";
    std::string sqlDelete = "DELETE FROM t_perf_lpasmem WHERE id = 10000001;";
    GmeConnT *conn = StEmbSqlGetConn();
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_PRIMARY_KEY_VIOLATION, GmeSqlExecute(conn, sqlInsert1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDelete.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert3.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_SYNTAX_ERROR, GmeSqlExecute(conn, sqlInsert2.c_str(), nullptr, nullptr, nullptr));
    std::string sqlDrop = "DROP TABLE t_perf_lpasmem;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDrop.c_str(), nullptr, nullptr, nullptr));
}

static void DiskAnnIndexIncrementalInsertPerfLpasmem(GmeConnT *conn, uint32_t insertNum, int32_t startId = 1)
{
    const char *unused = nullptr;
    GmeSqlStmtT *stmt = nullptr;
    const char *insertSql = "INSERT INTO t_perf_lpasmem VALUES(?, ?);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, insertSql, strlen(insertSql) + 1, &stmt, &unused));
    DiskAnnIndexStepInsert(stmt, T_DEFAULT_VECTOR_DIM, insertNum, startId, GMERR_OK);
    ASSERT_EQ(GMERR_OK, GmeSqlFinalize(stmt));
}

// 建表指定INDEX LPASMEM隐藏列建多个索引进行插入和查询
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_WithIndexLpasMemPerf3)
{
    GmeConnT *conn = StEmbSqlGetConn();
    std::string sql = "CREATE TABLE t_perf_lpasmem(id int primary key, repr floatvector(4), INDEX LPASMEM);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    std::string sqlCreateIdx1 = "CREATE INDEX diskann_l2_idx ON t_perf_lpasmem USING GSDISKANN(repr L2);";
    std::string sqlCreateIdx2 = "CREATE INDEX diskann_cos_idx ON t_perf_lpasmem USING GSDISKANN(repr COSINE);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx2.c_str(), nullptr, nullptr, nullptr));
    DiskAnnIndexIncrementalInsertPerfLpasmem(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    // L2 查询
    sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_perf_lpasmem order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_perf_lpasmem order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    std::string sqlDrop = "DROP TABLE t_perf_lpasmem;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDrop.c_str(), nullptr, nullptr, nullptr));
}

// 使用prepare的方式建表
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_WithIndexLpasMemPerf4)
{
    GmeConnT *conn = StEmbSqlGetConn();
    GmeSqlStmtT *stmt = nullptr;
    const char *createSql =
        (char *)"CREATE TABLE t_perf_lpasmem(id int primary key, repr floatvector(4), INDEX LPASMEM);";
    ASSERT_EQ(GMERR_OK, GmeSqlPrepare(conn, createSql, strlen(createSql), &stmt, NULL));
    ASSERT_EQ(GMERR_OK, GmeSqlStep(stmt));

    std::string sqlCreateIdx1 = "CREATE INDEX diskann_l2_idx ON t_perf_lpasmem USING GSDISKANN(repr L2);";
    std::string sqlCreateIdx2 = "CREATE INDEX diskann_cos_idx ON t_perf_lpasmem USING GSDISKANN(repr COSINE);";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlCreateIdx2.c_str(), nullptr, nullptr, nullptr));
    DiskAnnIndexIncrementalInsertPerfLpasmem(conn, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    // L2 查询
    std::string sql = "SELECT repr <-> '[1, 2, 3, 4]' FROM t_perf_lpasmem order by repr <-> '[1, 2, 3, 4]' limit 10;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);
    // COSINE 查询
    sql = "SELECT repr <=> '[1, 2, 3, 4]' FROM t_perf_lpasmem order by repr <=> '[1, 2, 3, 4]' limit 20;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), StEmbSqlGetActQryResultSet, nullptr, nullptr));
    StEmbSqlCheckDistanceResult(nullptr, INSERT_NUM_FOR_INCREMENTAL_INSERT_NUM);

    std::string sqlDrop = "DROP TABLE t_perf_lpasmem;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDrop.c_str(), nullptr, nullptr, nullptr));
}

// 建表指定INDEX LPASMEM隐藏列建多个索引进行删除表中所有数据后重新插入
TEST_F(StEmbSqlVectorDiskAnnIndex, TestDiskAnnIndexIncrementalInsert_WithIndexLpasMemPerf5)
{
    std::string sql = "CREATE TABLE t_perf_lpasmem(id int primary key, repr floatvector(4), INDEX LPASMEM);";
    std::string sqlInsert1 = "INSERT INTO t_perf_lpasmem VALUES(1, '[1, 2, 3, 4]');";
    std::string sqlInsert2 = "INSERT INTO t_perf_lpasmem VALUES(2, '[2.0, 3.0, 4.0, 5.0]');";
    std::string sqlInsert3 = "INSERT INTO t_perf_lpasmem VALUES(3, '[+3.0, -4., 5, .6]'), (4, '[0.1, 0.1, 0.1, 0.1]');";
    std::string sqlDelete1 = "DELETE FROM t_perf_lpasmem WHERE id = 1;";
    std::string sqlDelete2 = "DELETE FROM t_perf_lpasmem WHERE id = 2;";
    std::string sqlDelete3 = "DELETE FROM t_perf_lpasmem WHERE id = 3;";
    GmeConnT *conn = StEmbSqlGetConn();
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sql.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert2.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert3.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDelete3.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDelete2.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDelete1.c_str(), nullptr, nullptr, nullptr));
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlInsert1.c_str(), nullptr, nullptr, nullptr));
    std::string sqlDrop = "DROP TABLE t_perf_lpasmem;";
    ASSERT_EQ(GMERR_OK, GmeSqlExecute(conn, sqlDrop.c_str(), nullptr, nullptr, nullptr));
}
