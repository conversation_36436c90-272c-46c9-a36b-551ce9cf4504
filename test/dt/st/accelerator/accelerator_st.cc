/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2024. All rights reserved.
 * File Name: accelerator_st.cc
 * Description: st for accelerator
 * Author: lijianchuan
 * Create: 2022/9/14
 */

#include "storage_st_common.h"
#include "st_common.h"

long ReadJanssonFile(const char *path, char **buf, bool convertArray = false)
{
    FILE *fp = fopen(path, "rb");
    if (NULL == fp) {
        printf("[ReadJanssonFile] open file:%s fail.\n", path);
        return -1;
    }
    int rc = fseek(fp, 0L, SEEK_END);
    if (rc != 0) {
        printf("[ReadJanssonFile] fseek file:%s to end failed.\n", path);
        fclose(fp);
        return -1;
    }

    long size = ftell(fp);
    if (size < 0) {
        printf("[ReadJanssonFile] read file size:%ld failed.\n", size);
        fclose(fp);
        return -1;
    }

    char *pBuffer = (char *)malloc(size + 8);
    if (pBuffer == NULL) {
        printf("[ReadJanssonFile] malloc memory:%ld for file:%s failed.\n", size + 4, path);
        fclose(fp);
        return -1;
    }
    int i = 0;
    if (convertArray) {
        i = 1;
        pBuffer[0] = ' ';
    }
    rewind(fp);
    long readSize = fread(pBuffer + i, 1, size, fp);
    if (readSize != size) {
        printf("[ReadJanssonFile] read file:%s failed, expectSize:%ld, actualSize:%ld.\n", path, size, readSize);
        free(pBuffer);
        fclose(fp);
        return -1;
    }
    fclose(fp);
    if (convertArray) {
        for (; i < size; ++i) {
            if (pBuffer[i] == ' ') {
                continue;
            }
            if (pBuffer[i] == '{') {
                pBuffer[0] = '[';
                pBuffer[size + 1] = ']';
                pBuffer[size + 2] = '\0';
            } else {
                pBuffer[size + 1] = '\0';
            }
            break;
        }
    } else {
        pBuffer[size] = '\0';
    }
    *buf = pBuffer;
    return size;
}

int testGmcGetStmtAttr(GmcStmtT *stmt, GmcStmtAttrTypeE attr, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, attr, &affect, sizeof(affect));
    if (ret != GMERR_OK) {
        return ret;
    }
    if (affect != expect) {
        printf("[testGmcGetStmtAttr] expect is %d, affect is %d\n", expect, affect);
        return -1;
    }
    return GMERR_OK;
}

class StAccelerator : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(
            "\"multiHashBucketCount=16384\" \"hacMode=2\" \"featureNames=FASTPATH,TRM,MEMDATA,HAC\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
    void SetUp()
    {}
    void TearDown()
    {
        SaveLogAfterFailed();
    }
};

TEST_F(StAccelerator, testCreateAndDropVertexLabel)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000})";
    const char *normalLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"XT60",
                        "name":"XT60_K1",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"XT60",
                        "name":"XT60_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    status_t ret = GmcCreateVertexLabel(stmt, normalLabelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    char cmd[100] = "gmsysview -q V\\$STORAGE_HASH_INDEX_STAT -s usocket:/run/verona/unix_emserver";
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: truncate vertex label
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    // test point: 删除vertex label
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex label
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // test point: 再次删除vertex label
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testCreateAndDropVertexLabelNoAcc)
{
    const char *labelName = "XT61";
    const char *configJson = R"({"max_record_count":1000, "hacMode":0})";
    const char *normalLabelJson =
        R"([{
            "type":"record",
            "name":"XT61",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XT61",
                        "name":"XT61_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    status_t ret = GmcCreateVertexLabel(stmt, normalLabelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // test point: truncate vertex label
    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    // test point: 删除vertex label
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex label
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // test point: 再次删除vertex label
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

const int32_t HP_GET_RECORD_CNT_DATA_NUM = 1000;

static Status StHeapInsertByPartionId(GmcStmtT *stmt, uint8_t partition, int32_t i)
{
    int32_t pk = i + partition * HP_GET_RECORD_CNT_DATA_NUM;
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &pk, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f1 = partition;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partition, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

// DTS2022112511656,加速器模式不支持对账老化
// 修复：恢复对对账老化的支持
TEST_F(StAccelerator, testCheckAndAge)
{
    const char *hpTestGetRecordCountConfigJson = R"({"max_record_count":400000})";
    const char *hpTestGeyRecordCountLabelName = "T0";
    const char *hpTestGetRecordCountPartitionLabelJson =
        R"([
        {"name":"T0",
         "type":"record",
         "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"},
            {"name":"F2", "type": "partition", "nullable":false}
            ],
        "keys":[{"node":"T0", "name":"K0", "fields":["F0"], "index":{"type":"primary"}, "constraints":{"unique":true}}]
        }])";

    const int32_t HP_GET_RECORD_CNT_DATA_NUM = 1000;
    const uint8_t HP_GET_RECORD_CNT_PARTITION_NUM = 16;
    (void)GmcInit();

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    (void)GmcDropVertexLabel(stmt, hpTestGeyRecordCountLabelName);
    Status ret = GmcCreateVertexLabel(stmt, hpTestGetRecordCountPartitionLabelJson, hpTestGetRecordCountConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, hpTestGeyRecordCountLabelName, GMC_OPERATION_INSERT));
    for (uint8_t partition = 0; partition < HP_GET_RECORD_CNT_PARTITION_NUM; partition++) {
        for (int32_t i = 0; i < HP_GET_RECORD_CNT_DATA_NUM; i++) {
            ret = StHeapInsertByPartionId(stmt, partition, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
    }
    uint64_t count;
    ret = GmcGetVertexCount(stmt, hpTestGeyRecordCountLabelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint64_t)HP_GET_RECORD_CNT_PARTITION_NUM * HP_GET_RECORD_CNT_DATA_NUM, count);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, hpTestGeyRecordCountLabelName, GMC_OPERATION_REPLACE));
    // 对每个分区进行对账
    for (uint8_t partition = 0; partition < HP_GET_RECORD_CNT_PARTITION_NUM; partition++) {
        ret = GmcBeginCheck(stmt, hpTestGeyRecordCountLabelName, partition);
        ASSERT_EQ(GMERR_OK, ret);  // 返回特性不支持
        // 处理一半
        for (int32_t i = 0; i < HP_GET_RECORD_CNT_DATA_NUM / 2; i++) {
            ret = StHeapInsertByPartionId(stmt, partition, i);
            ASSERT_EQ(GMERR_OK, ret);
        }
        ret = GmcEndCheck(stmt, hpTestGeyRecordCountLabelName, partition, false);
        ASSERT_EQ(GMERR_OK, ret);  // 返回特性不支持
        ret = GmcGetVertexCount(stmt, hpTestGeyRecordCountLabelName, NULL, &count);
        ASSERT_EQ(GMERR_OK, ret);
        // 对账过的分区剩一半，没对过账的不影响
        uint64_t expCnt = (partition + 1) * (HP_GET_RECORD_CNT_DATA_NUM / 2) +
                          (HP_GET_RECORD_CNT_PARTITION_NUM - partition - 1) * HP_GET_RECORD_CNT_DATA_NUM;
        ASSERT_EQ(expCnt, count);
    }
    GmcResetStmt(stmt);
    ret = GmcDropVertexLabel(stmt, hpTestGeyRecordCountLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char f0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &f0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char f1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t f2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &f2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t f3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t f4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t f5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t f6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &f6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool f8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &f10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float f11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &f11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double f12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &f12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t f13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &f13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char f14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14Value, (strlen(f14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char f15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, f15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char f16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, f16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

// DTS2022112509200，RC+DAF延迟删除
TEST_F(StAccelerator, LightTransClose_test)
{
    int ret = 0;
    char vertexLabelConfig[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    char *normalVertexlabelSchema = NULL;
    const char *normalVertexlabelName = "T39_all_type";
    ReadJanssonFile("schemaFiles/NormalVertexLabel.gmjson", &normalVertexlabelSchema);
    ASSERT_NE((void *)NULL, normalVertexlabelSchema);

    ret = GmcCreateVertexLabel(g_stmt, normalVertexlabelSchema, vertexLabelConfig);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t count = 1000;
    // insert Vertex
    ret = GmcPrepareStmtByLabelName(g_stmt, normalVertexlabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        ASSERT_EQ(GMERR_OK, ret);
    }

    printf("insert vertex 1000 data done\n");
    const char *normalPkName = "T39_K0";

    // delete Vertex
    ret = GmcPrepareStmtByLabelName(g_stmt, normalVertexlabelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < count; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, normalPkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    printf("delete vertex 1000 data done\n");
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, normalVertexlabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StAccelerator, mh_index_graph_insert_non_unique_index_001)
{
    const char *labelName = "InsertSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"InsertSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false}],
            "keys":
                [
                    {
                        "node":"InsertSecondIndex",
                        "name":"InsertSecondIndex_k0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"InsertSecondIndex",
                        "name":"InsertSecondIndex_k1",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex
    uint32_t i;
    uint32_t insertNum = 200;
    uint32_t f0Value, f2Value;
    for (i = 0; i < insertNum; i++) {
        f0Value = i;
        f2Value = 666 + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "err num: " << i << endl;
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_update_unique_sec_index_002)
{
    const char *labelName = "UpdateSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    uint32_t f0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update unique index
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    f2Value = 1318;
    f3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    uint32_t f0V;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_update_non_unique_sec_index_003)
{
    const char *labelName = "UpdateNonUniqueSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":8, "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    char *f0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    f0Value = (char *)"huawe";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    f1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    f2Value = 1313;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    f3Value = (char *)"10.12.12.13";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update non unique index duplicate
    f1Value = 1311;
    f3Value = (char *)"10.12.12.14";

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // update non unique index
    f1Value = 1313;
    f3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    char f0V[500];
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", f0V, 500, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (strcmp(f0V, f0Value) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_delete_vertex_unique_sec_index_004)
{
    const char *labelName = "DeleteSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"DeleteSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":8, "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    char *f0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 构造filter值查询，filter值正确，预期查询到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 查询顶点，顶点存在
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 删除顶点
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 再次查询顶点，预计查询失败
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_delete_vertex_non_unique_sec_index_005)
{
    const char *labelName = "DeleteNonUniqueSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"DeleteNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":8, "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    // insert vertex1
    char *f0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 构造filter值查询，filter值正确，预期查询到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 查询顶点，顶点存在
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除顶点
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 再次查询顶点，预计查询失败
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, f0Value, strlen(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_scan_sec_index_006)
{
    const char *labelName = "ScanSecIndex";
    unsigned int insertNum = 200;
    const char *testDeltaConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"ScanSecIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":true},
                    {"name":"F3", "type":"uint32", "nullable":true}],
            "keys":
                [
                    {
                        "node":"ScanSecIndex",
                        "name":"pkIndex",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"ScanSecIndex",
                        "name":"secIndex",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // set pre fetch rows
    uint32_t preFetch = 0;
    Status ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    preFetch = 2049;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    preFetch = 200;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 创建vertex label (带deltaS)
    ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testDeltaConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    // insert vertex
    unsigned int i;
    int f0Value, f1Value, f2Value;
    for (i = 0; i < insertNum; i++) {
        f0Value = i;
        f1Value = i;
        f2Value = 666;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // qry vertex
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_CACHE_ROWS, &result, sizeof(result));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(200, result);
    bool isFinish;
    unsigned int cnt = 0;
    int num = 200;
    while (num > 0) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
        num--;
    }

    GmcResetStmt(stmt);
    EXPECT_EQ(insertNum, cnt);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    num = 200;
    cnt = 0;
    while (num > 0) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
        num--;
    }
    EXPECT_EQ(insertNum, cnt);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_scan_unique_sec_index_007)
{
    const char *labelName = "ScanSecIndex";
    unsigned int insertNum = 200;
    const char *testDeltaConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"ScanSecIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":true},
                    {"name":"F3", "type":"uint32", "nullable":true}],
            "keys":
                [
                    {
                        "node":"ScanSecIndex",
                        "name":"pkIndex",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"ScanSecIndex",
                        "name":"secIndex",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // set pre fetch rows
    uint32_t preFetch = 0;
    Status ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    preFetch = 2049;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    preFetch = 200;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &preFetch, sizeof(preFetch));
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 创建vertex label (带deltaS)
    ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testDeltaConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex
    unsigned int i;
    int f0Value, f1Value, f2Value;
    for (i = 0; i < 2; i++) {
        f0Value = i;
        f1Value = i;
        f2Value = 666;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (i == 0) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);
        }
    }
    for (i = 1; i < insertNum; i++) {
        f0Value = i;
        f1Value = i;
        f2Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // qry vertex
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t result = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_CACHE_ROWS, &result, sizeof(result));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(200, result);
    bool isFinish;
    unsigned int cnt = 0;
    int num = 200;
    while (num > 0) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
        num--;
    }

    GmcResetStmt(stmt);
    EXPECT_EQ(insertNum, cnt);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    num = 200;
    cnt = 0;
    while (num > 0) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
        num--;
    }
    EXPECT_EQ(insertNum, cnt);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_get_vertex_count_008)
{
    const char *gLabelName = "vertexLabel";
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJson =
        R"([
        {"name":"vertexLabel",
         "type":"record",
         "fields":[
            { "name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"string", "size":20, "nullable":true}
            ],
            "keys":
                [
                    {
                        "node":"vertexLabel",
                        "name":"T0_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"vertexLabel",
                        "name":"T0_K1",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
        }
    ])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, gLabelName, GMC_OPERATION_INSERT));
    for (int i = 0; i < 5; i++) {
        int f0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);

        int f1Value = 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);

        char *f2Value = (char *)"huawei1";
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_STRING, f2Value, strlen(f2Value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 查询Vertex Count的值
    uint64_t count;
    int indexValue = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &indexValue, 4);
    ret = GmcGetVertexCount(stmt, gLabelName, "T0_K0", &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, count);
    int indexValue1 = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &indexValue1, sizeof(indexValue1));
    ret = GmcGetVertexCount(stmt, gLabelName, "T0_K1", &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, count);
    ret = GmcGetVertexCount(stmt, gLabelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5u, count);

    ret = GmcDropVertexLabel(stmt, gLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_get_vertex_count_abnormal_009)
{
    const char *gLabelName = "vertexLabel";
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJson =
        R"([
        {"name":"vertexLabel",
         "type":"record",
         "fields":[
            { "name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"string", "size":20, "nullable":true}
            ],
            "keys":
                [
                    {
                        "node":"vertexLabel",
                        "name":"T0_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"vertexLabel",
                        "name":"T0_K1",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
        }
    ])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 查询Vertex Count的值
    uint64_t *count = NULL;
    ret = GmcGetVertexCount(stmt, gLabelName, NULL, count);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    ret = GmcDropVertexLabel(stmt, gLabelName);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_read_update_non_unique_sec_index_010)
{
    const char *labelName = "UpdateNonUniqueSecondIndex";
    const char *testConfigJson = R"({"max_record_count":10000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    {"name":"F3", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    uint32_t insertNum = 1500;
    int f0Value, f1Value, f2Value, f3Value;

    // insert vertex1
    f2Value = 0;
    for (uint32_t i = 0; i < insertNum; i++) {
        f0Value = i;
        f1Value = i;
        f3Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3Value, sizeof(f3Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // update vertex
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    int f2Value2 = 1;

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value2, sizeof(f2Value2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2"));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, mh_index_graph_scan_sec_index_without_fetch_011)
{
    const char *labelName = "UpdateSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    for (unsigned int loop = 2; loop < 1000; loop++) {
        uint32_t f0Value = loop;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);

        long long f1Value = 1310 + loop;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);

        int f2Value = 1314;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);

        char *f3Value = (char *)"10.12.12.12";
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int f2Value = 1314;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 非唯一二级索引更新，查询localhash（非唯一）所在的记录，更新localhash key所在的字段值；
TEST_F(StAccelerator, mh_index_direct_read_update_non_unique_sec_index_012)
{
    const char *labelName = "UpdateNonUniqueSecondIndex";
    const char *testConfigJson = R"({"max_record_count":10000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint16", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false},
                    {"name":"F2", "type":"uint32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // 写入数据
    uint32_t insertNum = 1500;
    uint16_t f0Value;
    uint32_t f1Value;
    uint32_t f2Value = 199;
    for (uint32_t i = 0; i < insertNum; i++) {
        f0Value = i;
        f1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT16, &f0Value, sizeof(f0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 非唯一hash索引扫描
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2"));

    f2Value = 200;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 非唯一hash索引更新
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f2Value1 = 98755;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value1, sizeof(f2Value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// DTS2024051409595
TEST_F(StAccelerator, testBatchMergeWithSecIndex)
{
    int insertNum = 100;
    const char *vertexLabelName = "batchMergeLabel";
    const char *testDelatConfigJson = R"({"max_record_count":1000,"isFastReadUncommitted":0,"enableTableLock":1})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"batchMergeLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"batchMergeLabel",
                        "name":"batchMergeLabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"batchMergeLabel",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    int ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testDelatConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    // replace not exists vertex
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    int i;
    for (i = 0; i < insertNum; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "batchMergeLabel_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // replace exists vertex
    for (i = 0; i < insertNum; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "batchMergeLabel_K0");
        EXPECT_EQ(GMERR_OK, ret);
        int tmp = 1000 + i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &tmp, sizeof(tmp));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    printf("batch execute ret:%d, %d/%d\n", ret, successNum, totalNum);
    EXPECT_EQ(1u, totalNum);
    EXPECT_EQ(1u, successNum);
    // qry vertex data
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < insertNum; i++) {
        int value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "batchMergeLabel_K0");
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool eof;
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            EXPECT_EQ(insertNum, i);
            break;
        }
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(i, value);
        int expect = i + 1000;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(expect, value);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(99, value);
    }
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// DTS2024052000598
TEST_F(StAccelerator, testTransactionRollbackWithDeleteOperation)
{
    system("cat log/run/rgmserver/rgmserver.log >> log/run/rgmserver/rgmserver.log.bak");
    system("cat /dev/null > log/run/rgmserver/rgmserver.log");
    int32_t insertNum = 100;
    const char *vertexLabelName = "test_label";
    const char *testDelatConfigJson = R"({"max_record_count":1000,"isFastReadUncommitted":0,"enableTableLock":1})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"test_label",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"test_label",
                        "name":"pk_idx",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    int ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testDelatConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    uint64_t result;
    ret = GmcGetVertexCount(stmt, vertexLabelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(insertNum, result);

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.readOnly = false;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);

    int32_t value = 0;
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "pk_idx");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, vertexLabelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(insertNum - 1, result);
    ret = GmcTransRollBack(conn);
    ASSERT_EQ(GMERR_OK, ret);
    StCommonVerifyLogContent("./log/run/rgmserver/rgmserver.log", "Same addr exist in other slot", 0);  // 默认值16k

    ret = GmcGetVertexCount(stmt, vertexLabelName, NULL, &result);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(insertNum, result);

    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testSysviewServerMemoryOverhead)
{
    const char *labelName = "UpdateSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label (带deltaS)
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    uint32_t f0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update unique index
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    f2Value = 1318;
    f3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    uint32_t f0V;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    char cmd[100] = "gmsysview -q V\\$SERVER_MEMORY_OVERHEAD -s usocket:/run/verona/unix_emserver";
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// 配置17个二级索引，超过MAX_SECONDARY_COUNT_WITHOUT_LPM（16），预期建表报错
TEST_F(StAccelerator, testSecIndexCountLimit)
{
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K3",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K4",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K5",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K6",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K7",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K8",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K9",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K10",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K11",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K12",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K13",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K14",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K15",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K16",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K17",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex label
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    // 预期建表报错
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testHacHashSupportPartial)
{
    const char *labelName = "UpdateSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true},
                        "filter":
                        {
                        "operator_type": "and",
                        "conditions":
                        [
                            {"property": "F1", "compare_type": "unequal", "value": 0}
                        ]
                        }
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建vertex label
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    uint32_t f0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    // 不满足filter条件
    long long f1Value = 0;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int f2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据主键scan 查到数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    uint32_t f0V;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    // 根据hac hash scan 前面insert时该索引的数据不满足filter条件 所以该索引没有插入 故查不到数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    char cmd[100] = "gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -s usocket:/run/verona/unix_emserver";
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    // update unique index
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    // 满足filter条件
    f1Value = 88;
    f2Value = 1318;
    f3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据hac hash scan 上一步update时满足该索引的filter条件 故该索引插入了数据 可以查到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testMultiHashSupportPartial)
{
    const char *labelName = "UpdateSecondIndex";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint64", "nullable":false},
                    {"name":"F2", "type":"uint32", "default":2, "nullable":false},
                    {"name":"F3", "type":"fixed", "size":11, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false},
                        "filter":
                        {
                        "operator_type": "and",
                        "conditions":
                        [
                            {"property": "F2", "compare_type": "equal", "value": 1318}
                        ]
                        }
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建vertex label
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // insert vertex1
    uint32_t f0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long f1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    // 不满足filter条件
    int f2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *f3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据主键scan 查到数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    uint32_t f0V;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    long long f1V;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    int f2V;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    char f3V[11];
    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    // 根据multihash scan 前面insert时该索引的数据不满足filter条件 所以该索引没有插入 故查不到数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_NO_DATA, ret);

    char cmd[100] = "gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -s usocket:/run/verona/unix_emserver";
    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    // update unique index
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE));
    // 满足filter条件
    f2Value = 1318;
    f3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, f3Value, strlen(f3Value));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1Value, sizeof(f1Value));
    EXPECT_EQ(GMERR_OK, ret);

    // update vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 根据multihash scan 上一步update时满足该索引的filter条件 故该索引插入了数据 可以查到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K2"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2Value, sizeof(f2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));

    ret = GmcGetVertexPropertyByName(stmt, "F0", &f0V, sizeof(uint32_t), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f0Value, f0V);

    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1V, sizeof(long long), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f1Value, f1V);

    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2V, sizeof(int), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    EXPECT_EQ(f2Value, f2V);

    ret = GmcGetVertexPropertyByName(stmt, "F3", f3V, 11, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(false, isNull);
    if (memcmp(f3V, f3Value, 11) == 0) {
        EXPECT_EQ(0, 0);
    } else {
        EXPECT_EQ(1, 0);
    }

    ret = system(cmd);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

// DTS2024071721513
TEST_F(StAccelerator, testMultiHashScanAndExit)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000})";
    const char *normalLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "config":{"hash_type":"cceh"},
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"XT60",
                        "name":"XT60_K1",
                        "fields":["F1"],
                        "index":{"type":"hashcluster"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, normalLabelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f1Value = 1;
    for (uint32_t i = 0; i < 200; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
        uint32_t f0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(f0Value));
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    {
        // 建连，直连读，未读完，断连
        GmcConnT *conn2;
        GmcStmtT *stmt2;
        CreateSyncConnectionAndStmt(&conn2, &stmt2);
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, "XT60_K1"));
        ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &f1Value, sizeof(f1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);
        bool eof;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt2, &eof));
        EXPECT_EQ(eof, false);
        DestroyConnectionAndStmt(conn2, stmt2);
    }
    // 不再有断连恢复的日志
    StCommonVerifyLogContent("./log/run/rgmserver/rgmserver.log", "recover this lock info is owner:", 0);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testHacIndexAndCollisionStat)
{
    const char *cmd1 = "gmsysview -q V\\$STORAGE_INDEX_GLOBAL_STAT -s usocket:/run/verona/unix_emserver";
    const char *cmd2 = "gmsysview -q V\\$STORAGE_HASH_COLLISION_STAT -s usocket:/run/verona/unix_emserver";
    const int insertNum = 100;
    const char *labelName = "test_hac_collision";
    const char *testConfigJson = R"({"max_record_count":1000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"test_hac_collision",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"test_hac_collision",
                        "name":"pk_idx_000",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"test_hac_collision",
                        "name":"sec_idx_001",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"test_hac_collision",
                        "name":"sec_idx_002",
                        "fields":["F2"],
                        "index":{"type":"hashcluster"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < insertNum; ++i) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // batch replace vertex
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < insertNum; ++i) {
        int j = i + 16384;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &j, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &j, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = system(cmd2);
    EXPECT_EQ(GMERR_OK, ret);

    char cmdOutput[64];
    const std::string expectedPkInsertCnt(" " + std::to_string(insertNum));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "INDEX_TYPE", 1, cmdOutput, 64);
    int cmp = memcmp(" HAC_HASH_INDEX", cmdOutput, 15);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_INSERT_COUNT", 1, cmdOutput, 64);
    cmp = memcmp(expectedPkInsertCnt.c_str(), cmdOutput, expectedPkInsertCnt.size());
    EXPECT_EQ(cmp, 0);
    stringstream ss(cmdOutput);
    uint64_t insertCnt;
    ss >> insertCnt;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_COLLISION_COUNT", 1, cmdOutput, 64);
    ss.str(cmdOutput);
    uint64_t collisionCnt;
    ss >> collisionCnt;
    EXPECT_LT(collisionCnt, insertCnt);
    EXPECT_GT(collisionCnt, 0);

    const std::string expectedSecIdxInsertCnt(" " + std::to_string(insertNum * 2));
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "INDEX_TYPE", 2, cmdOutput, 64);
    cmp = memcmp(" MULTI_HASH_INDEX", cmdOutput, 17);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_INSERT_COUNT", 2, cmdOutput, 64);
    cmp = memcmp(expectedSecIdxInsertCnt.c_str(), cmdOutput, expectedSecIdxInsertCnt.size());
    EXPECT_EQ(cmp, 0);
    ss.str(cmdOutput);
    ss >> insertCnt;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_COLLISION_COUNT", 2, cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> collisionCnt;
    EXPECT_LT(collisionCnt, insertCnt);
    EXPECT_GT(collisionCnt, 0);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "INDEX_TYPE", 3, cmdOutput, 64);
    cmp = memcmp(" HAC_HASH_INDEX", cmdOutput, 15);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_INSERT_COUNT", 3, cmdOutput, 64);
    cmp = memcmp(expectedSecIdxInsertCnt.c_str(), cmdOutput, expectedSecIdxInsertCnt.size());
    EXPECT_EQ(cmp, 0);
    ss.str(cmdOutput);
    ss >> insertCnt;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_COLLISION_COUNT", 3, cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> collisionCnt;
    EXPECT_LT(collisionCnt, insertCnt);
    EXPECT_GT(collisionCnt, 0);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_INDEX_GLOBAL_STAT", "INDEX_PK_CACHELINE_LENGTH", cmdOutput, 64);
    cmp = memcmp(" 128", cmdOutput, 4);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_INDEX_GLOBAL_STAT", "INDEX_LOCALHASH_CACHELINE_LENGTH", cmdOutput, 64);
    cmp = memcmp(" 128", cmdOutput, 4);
    EXPECT_EQ(cmp, 0);

    // batch delete vertex
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < insertNum; ++i) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "pk_idx_000");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system(cmd2);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除并不改变insertCnt和CollisionCnt
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "INDEX_TYPE", 3, cmdOutput, 64);
    cmp = memcmp(" HAC_HASH_INDEX", cmdOutput, 15);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_INSERT_COUNT", 3, cmdOutput, 64);
    cmp = memcmp(expectedSecIdxInsertCnt.c_str(), cmdOutput, expectedSecIdxInsertCnt.size());
    EXPECT_EQ(cmp, 0);
    ss.str(cmdOutput);
    uint64_t insertCntAfterDelete;
    ss >> insertCntAfterDelete;
    EXPECT_EQ(insertCnt, insertCntAfterDelete);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultSelect("V\\$STORAGE_HASH_COLLISION_STAT", "HASH_COLLISION_COUNT", 3, cmdOutput, 64);
    ss.str(cmdOutput);
    uint64_t collisionCntAfterDelete;
    ss >> collisionCntAfterDelete;
    EXPECT_EQ(collisionCnt, collisionCntAfterDelete);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testHacGlobalStat)
{
    const char *cmd1 = "gmsysview -q V\\$STORAGE_HAC_GLOBAL_STAT -s usocket:/run/verona/unix_emserver";
    const int insertNum = 1000;
    const char *labelName = "test_hac_global_stat";
    const char *testConfigJson = R"({"max_record_count":200000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"test_hac_global_stat",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"test_hac_global_stat",
                        "name":"pk_idx_000",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"test_hac_global_stat",
                        "name":"sec_idx_001",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"test_hac_global_stat",
                        "name":"sec_idx_002",
                        "fields":["F2"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 测试空表Create/Drop
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    char cmdOutput[64];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_HASH_INDEX_COUNT", cmdOutput, 64);
    stringstream ss(cmdOutput);
    uint32_t hacHashIdxCnt;
    ss >> hacHashIdxCnt;
    EXPECT_EQ(hacHashIdxCnt, 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "MULTI_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    uint32_t multiHashIdxCnt;
    ss >> multiHashIdxCnt;
    EXPECT_EQ(multiHashIdxCnt, 2);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    uint32_t hacHashIdxCntAfterDrop;
    ss >> hacHashIdxCntAfterDrop;
    EXPECT_EQ(hacHashIdxCntAfterDrop, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "MULTI_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    uint32_t multiHashIdxCntAfterDrop;
    ss >> multiHashIdxCntAfterDrop;
    EXPECT_EQ(multiHashIdxCntAfterDrop, 0);

    // 测试有插入数据的表的Create/Drop
    ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int m = 0; m < 200; ++m) {
        for (int i = m * 1000; i < insertNum + m * 1000; i++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        GmcBatchRetT batchRet;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        printf("batch execute ret:%d, %d/%d\n", ret, successNum, totalNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, totalNum);
        EXPECT_EQ(1u, successNum);
    }

    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> hacHashIdxCnt;
    EXPECT_EQ(hacHashIdxCnt, 1);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "MULTI_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> multiHashIdxCnt;
    EXPECT_EQ(multiHashIdxCnt, 2);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "BATCH_SOFTWARE_COUNT", cmdOutput, 64);
    int cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "BATCH_HAC_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "BATCH_HAC_SYNC_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "BATCH_HAC_ASYNC_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "BATCH_EXCAPED_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_REQUEST_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_RESPONSE_WARNING_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_RESPONSE_NORMAL_ERR_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_RESPONSE_SPECIAL_ERR_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "ALLOC_BLOCK_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint8_t allocBlkCnt;
    ss >> allocBlkCnt;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "ALLOC_HAC_BLOCK_COUNT", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_EQ(cmp, 0);
    ss.str(cmdOutput);
    uint8_t allocHacBlkCnt;
    ss >> allocHacBlkCnt;
    EXPECT_GE(allocBlkCnt, allocHacBlkCnt);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "HAC_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> hacHashIdxCntAfterDrop;
    EXPECT_EQ(hacHashIdxCntAfterDrop, 0);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HAC_GLOBAL_STAT", "MULTI_HASH_INDEX_COUNT", cmdOutput, 64);
    ss.str(cmdOutput);
    ss >> multiHashIdxCntAfterDrop;
    EXPECT_EQ(multiHashIdxCntAfterDrop, 0);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StAccelerator, testMultiHashIndexStat)
{
    const char *cmd1 = "gmsysview -q V\\$STORAGE_MULTI_HASH_INDEX_STAT -s usocket:/run/verona/unix_emserver";
    const int insertNum = 1000;
    const char *labelName = "test_multi_hash_index_stat";
    const char *testConfigJson = R"({"max_record_count":200000})";
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"test_multi_hash_index_stat",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"test_multi_hash_index_stat",
                        "name":"pk_idx_000",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"test_multi_hash_index_stat",
                        "name":"sec_idx_001",
                        "fields":["F1"],
                        "index":{"type":"multi_hash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, testConfigJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int m = 0; m < 200; ++m) {
        for (int i = m * 1000; i < insertNum + m * 1000; i++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }

        GmcBatchRetT batchRet;
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        printf("batch execute ret:%d, %d/%d\n", ret, successNum, totalNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1u, totalNum);
        EXPECT_EQ(1u, successNum);
    }

    ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
    char cmdOutput[64];

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "BUCKET_COUNT", cmdOutput, 64);
    int cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    stringstream ss(cmdOutput);
    uint32_t bktCnt;
    ss >> bktCnt;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "BUCKET_USED", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint32_t bktUsd;
    ss >> bktUsd;
    EXPECT_GE(bktCnt, bktUsd);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "MIN_TABLE_SIZE", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint64_t minTabSize;
    ss >> minTabSize;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "MAX_TABLE_SIZE", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint64_t maxTabSize;
    ss >> maxTabSize;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "AVG_TABLE_SIZE", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint64_t avgTabSize;
    ss >> avgTabSize;
    EXPECT_LE(minTabSize, avgTabSize);
    EXPECT_GE(maxTabSize, avgTabSize);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "TOTAL_MEMORY_SIZE", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint64_t totalMemSize;
    ss >> totalMemSize;
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_MULTI_HASH_INDEX_STAT", "USED_MEM_SIZE", cmdOutput, 64);
    cmp = memcmp(" 0", cmdOutput, 2);
    EXPECT_NE(cmp, 0);
    ss.str(cmdOutput);
    uint64_t usedMemSize;
    ss >> usedMemSize;
    EXPECT_GE(totalMemSize, usedMemSize);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

class StAcceleratorHacModeZero : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig(
            "\"multiHashBucketCount=16384\" \"hacMode=0\" \"featureNames=FASTPATH,TRM,MEMDATA,HAC\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
    void SetUp()
    {}
    void TearDown()
    {
        SaveLogAfterFailed();
    }
};

TEST_F(StAcceleratorHacModeZero, testHacGlobalStatHacModeZero)
{
    const char *cmd1 = "gmsysview -q V\\$STORAGE_HAC_GLOBAL_STAT -s usocket:/run/verona/unix_emserver";
    Status ret = system(cmd1);
    EXPECT_EQ(GMERR_OK, ret);
}

class StAccelerator2 : public StStorage {
protected:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"multiHashBucketCount=16384\" \"hacMode=2\" "
                                "\"enableClusterHash=1\" \"featureNames=FASTPATH,TRM,MEMDATA,HAC\"");
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        st_clt_init();
        st_connect();
    }
    static void TearDownTestCase()
    {
        st_disconnect();
        st_clt_uninit();
        ShutDownDbServer();
    }
    void SetUp()
    {}
    void TearDown()
    {
        SaveLogAfterFailed();
    }
};

// DTS2024071208837
TEST_F(StAccelerator2, testClusterHash)
{
    const char *labelName = "XT60";
    const char *configJson = R"({"max_record_count":1000})";
    const char *normalLabelJson =
        R"([{
            "type":"record",
            "name":"XT60",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"XT60",
                        "name":"XT60_K0",
                        "fields":["F0"],
                        "config":{"hash_type":"cceh"},
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, normalLabelJson, configJson);
    ASSERT_EQ(GMERR_OK, ret);

    char cmdOutput[64];
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HASH_COLLISION_STAT", "INDEX_TYPE", cmdOutput, 64);
    int cmp = memcmp(" HAC_HASH_INDEX", cmdOutput, 15);
    EXPECT_NE(cmp, 0);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}
