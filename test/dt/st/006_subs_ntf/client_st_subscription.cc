#include "client_common_st.h"
#include "client_option.h"
#include "gmc_test.h"
#include <functional>
using std::function;

class StClientSub : public StClient {
public:
    ~StClientSub()
    {
        EXPECT_TRUE(CltCataIsEmpty());
    }

    static void SetEpollRegFunc(GmcConnOptionsT *connOptions)
    {
        int32_t ret;
        if (IsEulerEnv()) {
            ret = GmcConnOptionsSetEpollRegFuncWithUserData(
                connOptions, (GmcEpollRegWithUserDataT)EpollRegWithUserData, &StClientSub::responseEpollFd);
        } else {
            ret = GmcConnOptionsSetEpollRegFunc(connOptions, (GmcEpollRegT)EpollReg);
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
};

TEST_F(StClientSub, testSubSetChanRingLen)
{
    GmcConnT *subConn;
    GmcConnOptionsT *connOptions;
    constexpr auto &subConnName = "subConnName1";
    uint32_t ringLen = 128;
    uint32_t Interval = 12;
    uint32_t ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    GmcConnOptionsSetTimeInterval(connOptions, Interval);
    GmcConnOptionsSetConnName(connOptions, subConnName);
    SetEpollRegFunc(connOptions);
    ret = GmcConnOptionsSetMsgQueueSize(connOptions, ringLen);
    if (IsEulerEnv()) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    ringLen = 129;
    ret = GmcConnOptionsSetMsgQueueSize(connOptions, ringLen);
    if (IsEulerEnv()) {
        EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    } else {
        EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }

    ret = GmcConnect(GMC_CONN_TYPE_SUB, connOptions, &subConn);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientSub, testSubCreateVertexSubWithCond)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subLabel1/F0",
                    "value": 1
                },
                {
                    "property": "F1",
                    "value": 2
                }
            ]
        }
    }
    )";

    ret = GmcSubscribe(
        syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };
}

TEST_F(StClientSub, testSubPushVertexOnInsert)
{
    constexpr auto &labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "super_fields":[
            { "name":"superfield0", "fields":{ "begin":"F0", "end":"F1" } }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    typedef struct sp1 {
        uint32_t F0;
        uint32_t F1;
    } sp1_t;

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++received) {
            ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_TRUE(isNull);

            sp1_t sp1 = {0};
            ret = GmcGetSuperfieldById(stmt, 0, &sp1, sizeof(sp1_t));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sp1.F0, received);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_NE(GMERR_OK, ret);
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
            EXPECT_NE(GMERR_OK, ret);
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_KEY);
            EXPECT_NE(GMERR_OK, ret);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 2; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushMultiVertexOnInsertDoDirectRead)
{
    constexpr auto &labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "super_fields":[
            { "name":"superfield0", "fields":{ "begin":"F0", "end":"F1" } }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";
    typedef struct sp1 {
        uint32_t F0;
        uint32_t F1;
    } sp1_t;

    constexpr auto &labelName2 = "dstVextexLabelName175";
    constexpr auto labelJson2 = R"(
    [{
        "type": "record",
        "name": "dstVextexLabelName175",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true },
            { "name": "F3", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "dstVextexLabelName175",
                "name": "T59_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcCreateVertexLabel(syncStmt, labelJson2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName2);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object", "key"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++received) {
            ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_TRUE(isNull);

            sp1_t sp1 = {0};
            ret = GmcGetSuperfieldById(stmt, 0, &sp1, sizeof(sp1_t));
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sp1.F0, received);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_NE(GMERR_OK, ret);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel2";
    config1.configJson = R"(
    {
        "name": "subVertexLabel2",
        "label_name": "dstVextexLabelName175",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object", "key"]}],
        "retry": true
    }
    )";

    auto callback1 = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName2)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName2, pushLabelName);

        for (bool eof;; ++received) {
            ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 111, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(received + 101, F1V);
            EXPECT_FALSE(isNull);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_NE(GMERR_OK, ret);
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_KEY);
            EXPECT_EQ(GMERR_OK, ret);
        }
    };
    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback1, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 2; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    total = 0;

    for (; total < 2; ++total) {
        uint32_t F0Value = total + 111;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = total + 101;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total || received1 != total);
}

const int MAX_KEY_NAME_LEN = 32;
static Status KvSubscriptionOperation(GmcStmtT *stmt)
{
    uint32_t end = 10;
    // 执行DML操作触发订阅推送
    for (uint32_t i = 0; i < end; ++i) {
        char key[MAX_KEY_NAME_LEN];
        int length = sprintf_s(key, MAX_KEY_NAME_LEN, "zhangsan_%u", i);
        if (length < 0) {
            return -1;
        }
        uint32_t keyLen = 1 + (uint32_t)length;
        uint32_t value = i;
        uint32_t valueLen = sizeof(uint32_t);
        Status ret = GmcKvSet(stmt, key, keyLen, &value, valueLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcKvRemove(stmt, key, keyLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    sleep(1);
    return GMERR_OK;
}

static Status KvSubscriptionInsert(GmcStmtT *stmt)
{
    uint32_t end = 10;
    // 执行DML操作触发订阅推送
    for (uint32_t i = 0; i < end; ++i) {
        char key[MAX_KEY_NAME_LEN];
        int length = sprintf_s(key, MAX_KEY_NAME_LEN, "zhangsan_%u", i);
        if (length < 0) {
            return -1;
        }
        uint32_t keyLen = 1 + (uint32_t)length;
        uint32_t value = i;
        uint32_t valueLen = sizeof(uint32_t);
        Status ret = GmcKvSet(stmt, key, keyLen, &value, valueLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

TEST_F(StClientSub, testSubPushKvOnDirectRead)
{
    constexpr auto &tableName1 = "subkvTable1";
    Status ret = GmcKvCreateTable(syncStmt, tableName1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, tableName1);
    };

    constexpr auto &tableName2 = "subkvTable2";
    ret = GmcKvCreateTable(syncStmt, tableName2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, tableName2);
    };

    GmcSubConfigT config1;
    config1.subsName = "KvSubscription1";
    config1.configJson = R"({
        "label_name": "subkvTable1",
        "events": [
            {"type": "set", "msgTypes": ["key"]}
        ]
    })";

    GmcSubConfigT config2;
    config2.subsName = "KvSubscription2";
    config2.configJson = R"({
        "label_name": "subkvTable2",
        "events": [
            {"type":"set", "msgTypes":["key"]}
        ]
    })";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        for (bool eof; GmcFetch(stmt, &eof) == GMERR_OK && !eof;) {
            if (info->eventType == GMC_SUB_EVENT_KV_SET) {
                uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
                received++;
                // 设置获取新数据
                Status ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
                if (ret != GMERR_OK) {
                    continue;
                }
                // 读出数据
                void *key, *value;
                uint32_t keyLen, valueLen;
                ret = GmcKvGetFromStmt(stmt, &key, &keyLen, &value, &valueLen);
                if (ret != GMERR_OK) {
                    break;
                }
                // kv都是二进制数据，由用户赋予它们意义，进行DML操作时，key是string，value是uint32，这里保持一致
                // 要特别注意key设置时有没有包含结尾的0，对于条件订阅，目标值为string类型时，key需要以0结尾才能匹配条件
            }
        }
    };
    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t received2 = 0;
    ret = GmcSubscribe(syncStmt, &config2, subConn, callback, &received2);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcKvPrepareStmtByLabelName(syncStmt, tableName1);
    EXPECT_EQ(ret, GMERR_OK);

    ret = KvSubscriptionOperation(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcKvPrepareStmtByLabelName(syncStmt, tableName2);
    EXPECT_EQ(ret, GMERR_OK);

    ret = KvSubscriptionOperation(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcUnSubscribe(syncStmt, "KvSubscription1");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcUnSubscribe(syncStmt, "KvSubscription2");
    EXPECT_EQ(ret, GMERR_OK);

    EXPECT_EQ(10u, received1);
    EXPECT_EQ(10u, received2);
    ret = GmcKvDropTable(syncStmt, tableName1);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcKvDropTable(syncStmt, tableName2);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StClientSub, testSubPushKvOnDirectRead1)
{
    constexpr auto &tableName1 = "subkvTable1";
    Status ret = GmcKvCreateTable(syncStmt, tableName1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, tableName1);
    };

    GmcSubConfigT config1;
    config1.subsName = "KvSubscription1";
    config1.configJson = R"({
        "label_name": "subkvTable1",
        "events": [
            {"type": "set", "msgTypes": ["key"]},
            {"type": "initial_scan", "msgTypes":["new object"]}
        ],
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "compare_type": "equal",
                    "key_value": "zhangsan_2"
                }
            ]
        }
    })";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        for (bool eof; GmcFetch(stmt, &eof) == GMERR_OK && !eof;) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD) {
                uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
                received++;
                // 设置获取新数据
                Status ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
                if (ret != GMERR_OK) {
                    continue;
                }
                // 读出数据
                void *key, *value;
                uint32_t keyLen, valueLen;
                ret = GmcKvGetFromStmt(stmt, &key, &keyLen, &value, &valueLen);
                if (ret != GMERR_OK) {
                    break;
                }
                // kv都是二进制数据，由用户赋予它们意义，进行DML操作时，key是string，value是uint32，这里保持一致
                // 要特别注意key设置时有没有包含结尾的0，对于条件订阅，目标值为string类型时，key需要以0结尾才能匹配条件
            }
        }
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, tableName1);
    EXPECT_EQ(ret, GMERR_OK);
    ret = KvSubscriptionInsert(syncStmt);
    EXPECT_EQ(ret, GMERR_OK);

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcUnSubscribe(syncStmt, "KvSubscription1");
    EXPECT_EQ(ret, GMERR_OK);

    EXPECT_EQ(1u, received1);
    ret = GmcKvDropTable(syncStmt, tableName1);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StClientSub, testSubPushVertexOnInsertWithCond)
{
    constexpr auto &labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            {
                "name": "subs_fixed",
                "type": "fixed",
                "default": "ffff",
                "size": 4,
                "nullable": true
            },
            { "name": "subs_bytes", "type": "bytes", "size": 20, "nullable": true},
            { "name": "subs_float", "type": "float", "nullable": true },
            { "name": "subs_double", "type": "double", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
        {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "subs_fixed",
                    "value": "1234"
                },
                {
                    "property": "subs_float",
                    "value": 1.234567
                },
                {
                    "property": "subs_double",
                    "value": 3.234333333333333
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            bool isNull;
            uint64_t t_start = 0;
            uint32_t F0V, F1V;
            ret = GmcSubGetProperty(stmt, GMC_SUB_FETCH_NEW, 0, &t_start, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, t_start);
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 1, F1V);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_NE(GMERR_OK, ret);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    function<void()> conditionalValueSetter[] = {
        [&]() {
            char fixedValue[] = "1234";
            ret = GmcSetVertexProperty(syncStmt, "subs_fixed", GMC_DATATYPE_FIXED, fixedValue, strlen(fixedValue));
            EXPECT_EQ(GMERR_OK, ret);
        },
        [&]() {
            float floatValue = 1.234567;
            ret = GmcSetVertexProperty(syncStmt, "subs_float", GMC_DATATYPE_FLOAT, &floatValue, sizeof(float));
            EXPECT_EQ(GMERR_OK, ret);
        },
        [&]() {
            double doubleValue = 3.234333333333333;
            ret = GmcSetVertexProperty(syncStmt, "subs_double", GMC_DATATYPE_DOUBLE, &doubleValue, sizeof(double));
            EXPECT_EQ(GMERR_OK, ret);
        },
    };

    uint32_t total = 0;

    for (const auto &setConditionalValue : conditionalValueSetter) {
        setConditionalValue();
    }

    uint32_t f0Value = total;
    ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f1Value = total + 1;
    ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ++total;

    /* wait till all events are received */
    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushVertexOnUpdate)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_UPDATE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(87654321u, F1V);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(12345678u, F1V);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    uint32_t total = 0;

    for (; total < 3; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1Value = 87654321;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        F1Value = 12345678;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushVertexOnDelete)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "delete", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_NE(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received + 1, F1V);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 3; ++total) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushVertexOnReplace)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(1u, info->labelCount);
        EXPECT_EQ(GMC_SUB_EVENT_REPLACE, info->eventType);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            uint32_t msgType;
            ret = GmcSubGetMsgType(stmt, &msgType);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            if (msgType & GMC_SUB_MSG_OLD_DATA) {
                // is update from F1 = received to F1 = received + 1
                EXPECT_EQ(received + 1, F1V);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);

                ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(received, F0V);

                ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(received, F1V);
            } else {
                // is insert from F1 = received
                EXPECT_EQ(received, F1V);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
    };

    uint32_t received;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint i = 0; i <= 1; ++i)  // insert first, then update
    {
        uint32_t total = (received = 0);

        for (; total < 3; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1Value = total + i;
            ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(syncStmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(received != total);
    }
}

TEST_F(StClientSub, testSubPushVertexOnReplaceWithCond)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "replace", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1"
                },
                {
                    "property": "F2",
                    "value": 3
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(1u, info->labelCount);
        EXPECT_EQ(GMC_SUB_EVENT_REPLACE, info->eventType);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            uint32_t msgType;
            ret = GmcSubGetMsgType(stmt, &msgType);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V, F2V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(2u, F1V);

            ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(3u, F2V);

            if (msgType & GMC_SUB_MSG_OLD_DATA) {
                // is update (F0 = 1, F1 = 1, F2 = 2) => (F0 = 1, F1 = 2, F2 = 3)
                EXPECT_EQ(1u, F0V);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_OK, ret);

                ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(1u, F0V);

                ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(1u, F1V);

                ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(2u, F2V);
            } else {
                // is insert (F0 = 2, F1 = 2, F2 = 3)
                EXPECT_EQ(2u, F0V);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
    };

    uint32_t received;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint i = 0; i <= 1; ++i)  // insert first, then update
    {
        uint32_t total = (received = 0);

        for (; total < 3; ++total) {
            uint32_t F0Value = total;
            ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1Value = total + i;
            ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F2Value = total + i + 1;
            ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(uint32_t));
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcExecute(syncStmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        WAIT_WHILE(received == 0);
        EXPECT_EQ(1u, received);
    }
}

TEST_F(StClientSub, testSubPushVertexOnInsertTree)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    { "name": "f2", "type": "uint32", "nullable": true }
                ]
            },
            {
                "name": "c2",
                "type": "record",
                "vector": true,
                "size": 1024,
                "fields": [
                    { "name": "b1", "type": "uint32", "nullable": true },
                    { "name": "b2", "type": "uint32", "nullable": true }
                ]
            }
        ],
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c1/f1",
                    "value": 1
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof, &isNull = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            GmcNodeT *c1;
            ret = GmcGetChildNode(stmt, "c1", &c1);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value = 0;
            ret = GmcNodeGetPropertySizeByName(c1, "f1", &value);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(uint32_t), value);

            ret = GmcNodeGetPropertyByName(c1, "f1", &value, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(1u, value);  // 条件订阅，只有一条c1.f1 == 1
        }
    };

    uint32_t received = 0, total = 3;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        GmcNodeT *node;
        ret = GmcGetRootNode(syncStmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(syncStmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(syncStmt, "c2", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}
// cond 为nodename/property的条件订阅
TEST_F(StClientSub, testSubPushVertexOnInsertTreeWithCond)
{
    constexpr auto labelName = "sysModel3";
    const auto labelJson = sysModel3Schema;
    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    GmcSubConfigT config;
    // nodeName不存在
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "sysModel3",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c5/f1",
                    "value": 1
                }
            ]
        }
    }
    )";
    ret = GmcSubscribe(
        syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);
    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Not normal name. Node name is illegal, name: c5.", lastErrorStr);
    // subscribe with correct config
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "sysModel3",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "c2/f1",
                    "value": 1
                }
            ]
        }
    }
    )";
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        for (bool eof, &isNull = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            GmcNodeT *c1;
            GmcNodeT *c2;
            ret = GmcGetChildNode(stmt, "c1", &c1);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcNodeGetChild(c1, "c2", &c2);
            uint32_t value = 0;
            ret = GmcNodeGetPropertySizeByName(c2, "f1", &value);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(uint32_t), value);
            ret = GmcNodeGetPropertyByName(c2, "f1", &value, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(1u, value);
        }
    };
    uint32_t received = 0, total = 3;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        GmcNodeT *node, *c1, *c2;
        ret = GmcGetRootNode(syncStmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetChildNode(syncStmt, "c1", &c1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(c1, "c2", &c2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(c2, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, testSubPushVertexOnInsertTreeWithKeyValue)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "s", "type": "string", "size": 4 },
            { "name": "c0", "type": "uint32" },
            {
                "name": "c1",
                "type": "record",
                "fields": [
                    { "name": "f1", "type": "uint32", "nullable": true },
                    { "name": "f2", "type": "uint32", "nullable": true }
                ]
            },
            {
                "name": "c2",
                "type": "record",
                "vector": true,
                "size": 1024,
                "fields": [
                    { "name": "b1", "type": "uint32", "nullable": true },
                    { "name": "b2", "type": "uint32", "nullable": true }
                ]
            }
        ],
        "keys": [
            {
                "name": "table_pk",
                "index": { "type": "primary" },
                "node": "subLabel1",
                "fields": ["s", "c0"],
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [
            {
                "type": "insert",
                "msgTypes": ["key", "new object"]
            }
        ],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof, &null = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
            EXPECT_EQ(GMERR_OK, ret);

            GmcNodeT *c1;
            ret = GmcGetChildNode(stmt, "c1", &c1);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value = 0;
            ret = GmcNodeGetPropertySizeByName(c1, "f1", &value);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(uint32_t), value);

            ret = GmcNodeGetPropertyByName(c1, "f1", &value, sizeof(uint32_t), &null);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(null);
            EXPECT_EQ(received, value);

            char buf[16] = {};
            ret = GmcGetVertexPropertyByName(stmt, "s", buf, sizeof(buf), &null);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ("pk-" + std::to_string(received), buf);
        }
    };

    uint32_t received = 0, total = 10;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        GmcNodeT *node;
        ret = GmcGetRootNode(syncStmt, &node);
        EXPECT_EQ(GMERR_OK, ret);
        auto s = "pk-" + std::to_string(i);
        ret = GmcNodeSetPropertyByName(node, "s", GMC_DATATYPE_STRING, s.c_str(), s.length());
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "c0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(syncStmt, "c1", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetChildNode(syncStmt, "c2", &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushVertexOnUpdate_constraint)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "F1"
                },
                {
                    "property": "F2",
                    "value": 4
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_UPDATE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(3u, F1V);  // 1 + 2

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(2u, F1V);  // 1 + 1
        }
    };

    uint32_t received = 0, total = 3;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    for (uint32_t i = 1; i < total; i++) {
        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F0Value = i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 2;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 3;  // 条件只推送F2 == 4的数据，对应i == 1
        ret = GmcSetVertexProperty(syncStmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(uint32_t));
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, KvSetSub)
{
    constexpr auto labelName = "edu";
    Status ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "kvTableSub";
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_KV_SET, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            char *outKey = NULL;
            uint32_t outKeyLen = 0;
            uint32_t *outValue = NULL;
            uint32_t outValueLen = 0;
            ret = GmcKvGetFromStmt(stmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ("zhangsan", outKey);
            EXPECT_EQ(strlen("zhangsan") + 1, outKeyLen);
            EXPECT_EQ(30u, *outValue);
            EXPECT_EQ(sizeof(uint32_t), outValueLen);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    {
        char key[] = "zhangsan";
        uint32_t value = 30;
        ret = GmcKvSet(syncStmt, key, strlen(key) + 1, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, KvSetAndRemoveSub)
{
    constexpr auto labelName = "edu";
    auto ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "kvTableSub";
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [
            {
                "type": "set",
                "msgTypes": ["key"]
            },
            {
                "type": "delete",
                "msgTypes": ["key"]
            }
        ],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ((received == 0) ? GMC_SUB_EVENT_KV_SET : GMC_SUB_EVENT_KV_REMOVE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;
            const void *outKey;
            uint32_t outKeyLen;
            ret = GmcSubGetKey(stmt, &outKey, &outKeyLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ("zhangsan", reinterpret_cast<const char *>(outKey));
            EXPECT_EQ(strlen("zhangsan") + 1, outKeyLen);
        }
    };

    uint32_t received = 0, total = 2;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    {
        char key[] = "zhangsan";
        uint32_t value = 30;
        GmcKvTupleT kvInfo = {};
        kvInfo.key = key;
        kvInfo.keyLen = strlen(key) + 1;
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvSet(syncStmt, key, strlen(key) + 1, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcKvRemove(syncStmt, kvInfo.key, kvInfo.keyLen);
        EXPECT_EQ(GMERR_OK, ret);
    }
    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, KvInvalidConditionSub)
{
    constexpr auto labelName = "edu";
    auto ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // 缺少必要的条件字段"key_value"
    GmcSubConfigT config;
    config.subsName = "kvTableSub";
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [
            { "type": "set", "msgTypes":["new object", "old object"]}
        ],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "compare_type": "equal"
                }
            ]
        }
    }
    )";
    ret = GmcSubscribe(
        syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    const char *error = GmcGetLastError();
    EXPECT_STREQ("Undefine column. Key_value", error);

    // 条件字段"key_value"不能空缺
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [
            { "type": "set", "msgTypes":["new object", "old object"]},
            { "type": "delete", "msgTypes":["new object", "old object"]}
        ],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "compare_type": "equal",
                    "key_value": ""
                }
            ]
        }
    }
    )";
    ret = GmcSubscribe(
        syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    error = GmcGetLastError();
    EXPECT_STREQ("Not normal property. The val size of prop key_value is 0.", error);

    // 条件字段"key_value"并为数字形式，需要用“”包含
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTableSub subscription",
        "events": [
            { "type": "set", "msgTypes":["new object", "old object"]},
            { "type": "delete", "msgTypes":["new object", "old object"]}
        ],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "compare_type": "equal",
                    "key_value": 0
                }
            ]
        }
    }
    )";
    ret = GmcSubscribe(
        syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    error = GmcGetLastError();
    EXPECT_STREQ("Datatype mismatch. The val type of prop key_value is not matched, expected string|bytes.", error);
}

TEST_F(StClientSub, KvStringSub)
{
    constexpr auto labelName = "edu";
    auto ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // 条件字段key_value为字符串
    GmcSubConfigT config;
    config.subsName = "kvTableSub";
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "compare_type": "equal",
                    "key_value": "A"
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_KV_SET, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            char *outKey = NULL;
            uint32_t outKeyLen = 0;
            uint32_t *outValue = NULL;
            uint32_t outValueLen = 0;
            ret = GmcKvGetFromStmt(stmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_STREQ("A", outKey);
            EXPECT_EQ(strlen("A") + 1, outKeyLen);
            EXPECT_EQ(30u, *outValue);
            EXPECT_EQ(sizeof(uint32_t), outValueLen);
        }
    };
    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    {
        const char *key = "A";
        uint32_t value = 30;
        ret = GmcKvSet(syncStmt, (void *)key, strlen(key) + 1, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, KvBytesSub)
{
    constexpr auto labelName = "edu";
    auto ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // 条件字段key_value为16进制
    GmcSubConfigT config;
    config.subsName = "kvTableSub";
    config.configJson = R"(
    {
        "name": "kvTableSub",
        "label_name": "edu",
        "comment": "kvTable subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "compare_type": "equal",
                    "key_value": "0x41"
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_KV_SET, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        const uint8_t key[] = {'A'};
        ;
        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            char *outKey = NULL;
            uint32_t outKeyLen = 0;
            uint32_t *outValue = NULL;
            uint32_t outValueLen = 0;
            ret = GmcKvGetFromStmt(stmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ('A', outKey[0]);
            EXPECT_EQ(sizeof(key), outKeyLen);
            EXPECT_EQ(30u, *outValue);
            EXPECT_EQ(sizeof(uint32_t), outValueLen);
        }
    };
    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    {
        const uint8_t key[] = {'A'};
        uint32_t value = 30;
        ret = GmcKvSet(syncStmt, (void *)key, sizeof(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, testSubPushFullSync)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 10;
    struct UserData {
        uint32_t eventType, received;
        bool hasOld;
        uint32_t oldF0, oldF1;
        bool hasNew;
        uint32_t newF0, newF1;
        uint32_t serialNumber;
    } userData;

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        auto data = reinterpret_cast<UserData *>(userData);
        EXPECT_EQ(data->serialNumber, info->serialNumber);
        data->serialNumber++;

        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF)
            return;
        EXPECT_EQ(data->eventType, info->eventType);

        for (bool eof;; ++data->received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            auto verify = [&](bool shouldExist, auto fetchMode, uint32_t F0, uint32_t F1) {
                ret = GmcSubSetFetchMode(stmt, fetchMode);
                if (!shouldExist) {
                    EXPECT_NE(GMERR_OK, ret);
                    return;
                }
                EXPECT_EQ(GMERR_OK, ret);

                uint32_t value;
                bool isNull;
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(data->received + F0, value);

                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
                EXPECT_EQ(GMERR_OK, ret);
                EXPECT_FALSE(isNull);
                EXPECT_EQ(data->received + F1, value);
            };

            verify(data->hasOld, GMC_SUB_FETCH_OLD, data->oldF0, data->oldF1);
            verify(data->hasNew, GMC_SUB_FETCH_NEW, data->newF0, data->newF1);
        }
    };

    /* full sync seq scan */

    userData.eventType = GMC_SUB_EVENT_INITIAL_LOAD, userData.received = 0;
    userData.hasOld = false;
    userData.hasNew = true, userData.newF0 = 0, userData.newF1 = 1;
    userData.serialNumber = 0;

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + userData.newF0;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + userData.newF1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 full sync",
        "events":
        [
            {
                "type":"initial_load"
            },
            {
                "type":"insert",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"delete",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"update",
                "msgTypes":["new object", "old object"]
            },
            {
                "type":"replace update",
                "msgTypes":["new object", "old object"]
            }
        ],
        "retry":true
    }
    )";

    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &userData);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    WAIT_WHILE(userData.received != total);

    /* update */

    userData.eventType = GMC_SUB_EVENT_UPDATE, userData.received = 0;
    userData.hasOld = true, userData.oldF0 = userData.newF0, userData.oldF1 = userData.newF1;
    userData.hasNew = true, userData.newF0 = userData.newF0, userData.newF1 = 2;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + userData.oldF0;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + userData.newF1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);

    /* delete */

    userData.eventType = GMC_SUB_EVENT_DELETE, userData.received = 0;
    userData.hasOld = true, userData.oldF0 = userData.newF0, userData.oldF1 = userData.newF1;
    userData.hasNew = false;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + userData.oldF0;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);

    /* insert */

    userData.eventType = GMC_SUB_EVENT_INSERT, userData.received = 0;
    userData.hasOld = false;
    userData.hasNew = true, userData.newF0 = 0, userData.newF1 = 1;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + userData.newF0;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + userData.newF1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);

    /* replace */

    userData.eventType = GMC_SUB_EVENT_REPLACE_UPDATE, userData.received = 0;
    userData.hasOld = true, userData.oldF0 = userData.newF0, userData.oldF1 = userData.newF1;
    userData.hasNew = true, userData.newF1 = 2;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        uint32_t F0 = i + userData.newF0;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F1 = i + userData.newF1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);
}

TEST_F(StClientSub, testSubPushFullSyncKv)
{
    constexpr auto labelName = "edu";
    auto ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback and user data */

    constexpr uint32_t total = 10;
    constexpr auto &format = "key %u";
    constexpr auto size = sizeof(format) - 2 + 10;

    struct UserData {
        uint32_t eventType, received;
        bool hasOld;
        uint32_t oldValue;
        bool hasNew;
        uint32_t newValue;
        uint32_t serialNumber;
    } userData;

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        auto data = reinterpret_cast<UserData *>(userData);
        EXPECT_EQ(data->serialNumber, info->serialNumber);
        data->serialNumber++;

        if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF)
            return;
        EXPECT_EQ(data->eventType, info->eventType);

        for (bool eof;; ++data->received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            char expectKey[size];
            uint32_t expectKeyLen = sprintf(expectKey, format, data->received) + 1;

            auto verify = [&](bool shouldExist, auto fetchMode, uint32_t valueOffset) {
                ret = GmcSubSetFetchMode(stmt, fetchMode);
                if (!shouldExist) {
                    EXPECT_NE(GMERR_OK, ret);
                    return;
                }
                EXPECT_EQ(GMERR_OK, ret);

                char *key = NULL;
                uint32_t *value = NULL;
                uint32_t keyLen = 0;
                uint32_t valueLen = 0;
                ret = GmcKvGetFromStmt(stmt, (void **)&key, &keyLen, (void **)&value, &valueLen);
                EXPECT_EQ(GMERR_OK, ret);

                EXPECT_STREQ(expectKey, key);
                EXPECT_EQ(expectKeyLen, keyLen);
                EXPECT_EQ(data->received + valueOffset, *value);
                EXPECT_EQ(sizeof(uint32_t), valueLen);
            };

            verify(data->hasOld, GMC_SUB_FETCH_OLD, data->oldValue);
            verify(data->hasNew, GMC_SUB_FETCH_NEW, data->newValue);
        }
    };

    /* full sync seq scan */

    userData.eventType = GMC_SUB_EVENT_INITIAL_LOAD, userData.received = 0;
    userData.hasOld = false;
    userData.hasNew = true, userData.newValue = 0;
    userData.serialNumber = 0;

    for (uint32_t i = 0; i < total; ++i) {
        char key[size];
        uint32_t value = i + userData.newValue;
        ret = GmcKvSet(syncStmt, key, sprintf(key, format, i) + 1, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "edu",
        "comment": "KvLabel subscription",
        "events":
        [
            {
                "type":"initial_load"
            },
            {
                "type":"set",
                "msgTypes": ["new object"]
            },
            {
                "type":"delete",
                "msgTypes":["new object", "old object"]
            }
        ]
    }
    )";

    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &userData);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    WAIT_WHILE(userData.received != total);

    /* update */

    userData.eventType = GMC_SUB_EVENT_KV_SET, userData.received = 0;
    userData.hasOld = false;
    userData.hasNew = true, userData.newValue = 666;

    for (uint32_t i = 0; i < total; ++i) {
        char key[size];
        uint32_t value = i + userData.newValue;
        ret = GmcKvSet(syncStmt, key, sprintf(key, format, i) + 1, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);

    /* delete */

    userData.eventType = GMC_SUB_EVENT_KV_REMOVE, userData.received = 0;
    userData.hasOld = true, userData.oldValue = userData.newValue;
    userData.hasNew = false;

    for (uint32_t i = 0; i < total; ++i) {
        GmcKvTupleT kvInfo;
        char key[size];
        kvInfo.key = key;
        kvInfo.keyLen = sprintf(key, format, i) + 1;

        ret = GmcKvRemove(syncStmt, kvInfo.key, kvInfo.keyLen);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(userData.received != total);
}

TEST_F(StClientSub, kv_namespace_sub)
{
    constexpr auto testNamespace1 = "test_namespace_1";
    auto ret = GmcCreateNamespace(syncStmt, testNamespace1, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropNamespace(syncStmt, testNamespace1);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcUseNamespace(syncStmt, testNamespace1);
    EXPECT_EQ(GMERR_OK, ret);

    constexpr auto &labelName = "kv1";
    ret = GmcKvCreateTable(syncStmt, labelName, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcKvDropTable(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel";
    config.configJson = R"(
    {
        "name": "subVertexLabel",
        "label_name": "kv1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "set", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_KV_SET, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            char *outKey = NULL;
            uint32_t outKeyLen = sizeof(outKey);

            uint32_t *outValue = NULL;
            uint32_t outValueLen = sizeof(outValue);

            ret = GmcKvGetFromStmt(stmt, (void **)&outKey, &outKeyLen, (void **)&outValue, &outValueLen);
            EXPECT_EQ(GMERR_OK, ret);

            EXPECT_STREQ("zhaoliu", outKey);
            EXPECT_EQ(sizeof("zhaoliu"), outKeyLen);

            EXPECT_EQ(36u, *outValue);
            EXPECT_EQ(sizeof(uint32_t), outValueLen);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcKvPrepareStmtByLabelName(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);

    {
        char key[] = "zhaoliu";
        uint32_t value = 36;
        ret = GmcKvSet(syncStmt, key, sizeof(key), &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received == 0);
    EXPECT_EQ(1u, received);
}

TEST_F(StClientSub, testAccountCheck)
{
    auto indexName = "pk";
    auto labelName = "label";
    auto labelJson = R"(
    [{
        "version": "2.0",
        "type": "record",
        "name": "label",
        "fields": [
            { "name": "k0", "type": "uint32" },
            { "name": "k1", "type": "uint32" },
            { "name": "v0", "type": "uint32" }
        ],
        "keys": [
            {
                "name": "pk",
                "index": { "type": "primary" },
                "fields": [ "k0", "k1" ],
                "node": "label",
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* operation wrappers */

    auto setProperty = [&](uint32_t k0, uint32_t k1, uint32_t v0) {
        ret = GmcSetVertexProperty(syncStmt, "k0", GMC_DATATYPE_UINT32, &k0, sizeof(k0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "k1", GMC_DATATYPE_UINT32, &k1, sizeof(k1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "v0", GMC_DATATYPE_UINT32, &v0, sizeof(v0));
        EXPECT_EQ(GMERR_OK, ret);
    };

    auto checkProperty = [&](uint32_t k0, uint32_t k1, uint32_t v0) {
        uint32_t val;
        bool isNull;
        ret = GmcGetVertexPropertyByName(syncStmt, "k0", &val, sizeof(val), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(k0, val);
        EXPECT_FALSE(isNull);
        ret = GmcGetVertexPropertyByName(syncStmt, "k1", &val, sizeof(val), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(k1, val);
        EXPECT_FALSE(isNull);
        ret = GmcGetVertexPropertyByName(syncStmt, "v0", &val, sizeof(val), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(v0, val);
        EXPECT_FALSE(isNull);
    };

    auto setIndexKey = [&](uint32_t k0, uint32_t k1) {
        ret = GmcSetIndexKeyName(syncStmt, indexName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &k0, sizeof(k0));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(syncStmt, 1, GMC_DATATYPE_UINT32, &k1, sizeof(k1));
        EXPECT_EQ(GMERR_OK, ret);
    };

    auto fetchVertex = [&]() {
        bool eof;
        ret = GmcFetch(syncStmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        return !eof;
    };

    /* prepare data */

    setProperty(1, 22, 333);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    setProperty(4, 55, 666);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    setProperty(7, 88, 999);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* test update version */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(syncStmt, labelName, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(1, 22);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(4, 55);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(syncStmt, labelName, GMC_FULL_TABLE, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(syncStmt, labelName, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    /* check result */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(1, 22);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_TRUE(fetchVertex());
    checkProperty(1, 22, 333);
    ASSERT_FALSE(fetchVertex());

    setIndexKey(4, 55);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_TRUE(fetchVertex());
    checkProperty(4, 55, 666);
    ASSERT_FALSE(fetchVertex());

    setIndexKey(7, 88);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_FALSE(fetchVertex());  // expired, invisible

    /* restore expired record */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    setProperty(7, 88, 999);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    /* test batch update version error */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE_VERSION);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(syncStmt->conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(4, 55);
    ret = GmcBatchAddDML(batch, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(7, 88);
    ret = GmcBatchAddDML(batch, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchRetT batchRet;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);
    uint32_t totalNum = 0, successNum = 0;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, totalNum);
    EXPECT_EQ(0u, successNum);

    /* test batch update version normal */

    ret = GmcBeginCheck(syncStmt, labelName, GMC_FULL_TABLE);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(7, 88);
    ret = GmcBatchAddDML(batch, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(4, 55);
    ret = GmcBatchAddDML(batch, syncStmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(2u, totalNum);
    EXPECT_EQ(totalNum, successNum);

    ret = GmcEndCheck(syncStmt, labelName, GMC_FULL_TABLE, GMC_CHECK_STATUS_NORMAL);
    EXPECT_EQ(GMERR_OK, ret);

    /* check result */

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    setIndexKey(1, 22);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_FALSE(fetchVertex());  // expired, invisible

    setIndexKey(4, 55);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_TRUE(fetchVertex());
    checkProperty(4, 55, 666);
    ASSERT_FALSE(fetchVertex());

    setIndexKey(7, 88);
    ret = GmcExecute(syncStmt);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_TRUE(fetchVertex());
    checkProperty(7, 88, 999);
    ASSERT_FALSE(fetchVertex());
}

TEST_F(StClientSub, testSubPushVertexOnInsertWithAutoGetVertexByKey)
{
    constexpr auto &labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";
    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    /* subscribe */
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes": ["key"]}],
        "retry": true
    }
    )";
    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++received) {
            ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t F0V, F1V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(received, F0V);
            ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_TRUE(isNull);
        }
    };
    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };
    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t total = 0;
    for (; total < 2; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    /* wait till all events are received */
    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubPushDeltaVertex)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto indexName = "subLabel1_K0";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [
            { "type": "update", "msgTypes": ["old object", "delta object"] },
            { "type": "merge update", "msgTypes": ["delta object"] }
        ],
        "retry": true
    }
    )";

    constexpr uint32_t total = 1000;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)  //
    {
        uint32_t value, &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof, &null = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            ASSERT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            if (info->eventType == GMC_SUB_EVENT_UPDATE)  // update (primary key is not included in delta )
            {
                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received, value);
                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received, value);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DELTA);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_TRUE(null);
                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received + 1, value);
            }

            if (info->eventType == GMC_SUB_EVENT_MERGE)  // merge (primary key is included in delta )
            {
                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DELTA);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_FALSE(null);
                ASSERT_EQ(received + 1, value);
                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received + 2, value);
            }
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t j = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received != total);
    received = 0;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t j = i + 2;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testSubCbTimeout)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto indexName = "subLabel1_K0";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [
            { "type": "update", "msgTypes": ["old object", "delta object"] },
            { "type": "delete", "msgTypes": ["old object"] }
        ],
        "retry": true
    }
    )";

    constexpr uint32_t total = 2;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)  //
    {
        uint32_t value, &received = *reinterpret_cast<uint32_t *>(userData);

        for (bool eof, &null = eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            ASSERT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            if (info->eventType == GMC_SUB_EVENT_UPDATE)  // update (primary key is not included in delta )
            {
                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received, value);
                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received, value);

                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DELTA);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_TRUE(null);
                ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(received + 1, value);
                usleep(210000);
            }

            if (info->eventType == GMC_SUB_EVENT_DELETE)  // merge (primary key is included in delta )
            {
                ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(uint32_t), &null);
                ASSERT_EQ(GMERR_OK, ret);
                ASSERT_EQ(received, value);
                usleep(210000);
            }
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t j = i + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received != total);
    received = 0;

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < total; ++i) {
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "subLabel1_K0");
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    WAIT_WHILE(received != total);
}

TEST_F(StClientSub, testMultiSubCount)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    GmcSubConfigT config2;
    config2.subsName = "subVertexLabel2";
    config2.configJson = R"(
    {
        "name": "subVertexLabel2",
        "label_name": "subLabel1",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0, received2 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSubscribe(syncStmt, &config2, subConn, callback, &received2);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcUnSubscribe(syncStmt, config2.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    uint32_t total = 0;

    while (total < 500) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        if (ret == GMERR_OK) {
            ++total;
            continue;
        }

        EXPECT_EQ(GMERR_SUB_PUSH_QUEUE_FULL, ret);
        std::cout << "inserted count: " << total << std::endl;
        break;
    }

    WAIT_WHILE(received1 != total);
    WAIT_WHILE(received2 != total);

    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT");

    // expected no more data
    EXPECT_EQ(total, received1);
    EXPECT_EQ(total, received2);
}

void *ThreadDml(void *args)
{
    Status ret;
    uint32_t total = 0;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    while (total < 500) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
    return NULL;
}

void *ThreadDisconnect(void *args)
{
    GmcConnT *subConn = (GmcConnT *)args;
    system("gmsysview -q V\\$DRT_CONN_SUBS_STAT");
    Status ret = GmcDisconnect(subConn);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// test DML insert and disconnect run concurrently
TEST_F(StClientSub, testInsertAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadDml, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

typedef struct sp1 {
    uint32_t F0;
    uint32_t F1;
} sp1_t;

constexpr auto &labelNameForCallBack = "subLabel1";
void SubcallBack(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    DbUsleep(2000);
    EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    char pushLabelName[sizeof(labelNameForCallBack)] = {};
    uint32_t pushLabelNameSize = sizeof(pushLabelName);
    auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
    EXPECT_STREQ(labelNameForCallBack, pushLabelName);

    for (bool eof;; ++received) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F0V, F1V;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(received, F0V);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isNull);

        sp1_t sp1 = {0};
        ret = GmcGetSuperfieldById(stmt, 0, &sp1, sizeof(sp1_t));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sp1.F0, received);

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
        EXPECT_NE(GMERR_OK, ret);
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
        EXPECT_NE(GMERR_OK, ret);
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_KEY);
        EXPECT_NE(GMERR_OK, ret);
    }
};

void SubcallBack2(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    sleep(1);
    EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    char pushLabelName[sizeof(labelNameForCallBack)] = {};
    uint32_t pushLabelNameSize = sizeof(pushLabelName);
    auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
    EXPECT_STREQ(labelNameForCallBack, pushLabelName);

    for (bool eof;; ++received) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t F0V, F1V;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(received, F0V);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isNull);

        sp1_t sp1 = {0};
        ret = GmcGetSuperfieldById(stmt, 0, &sp1, sizeof(sp1_t));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sp1.F0, received);

        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
        EXPECT_NE(GMERR_OK, ret);
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_DATA_BY_KEY);
        EXPECT_NE(GMERR_OK, ret);
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_KEY);
        EXPECT_NE(GMERR_OK, ret);
    }
};

// 1 单个订阅关系回调平均时间超时打warning日志
TEST_F(StClientSub, testSubPushCallBackLog1)
{
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "super_fields":[
            { "name":"superfield0", "fields":{ "begin":"F0", "end":"F1" } }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelNameForCallBack);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, SubcallBack, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelNameForCallBack, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 12; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total);
}

// 2 多个订阅关系回调平均时间超时打warning日志
TEST_F(StClientSub, testSubPushCallBackLog2)
{
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "super_fields":[
            { "name":"superfield0", "fields":{ "begin":"F0", "end":"F1" } }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelNameForCallBack);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // 订阅关系1
    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, SubcallBack, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // 订阅关系2
    GmcSubConfigT config2;
    config2.subsName = "subVertexLabel2";
    config2.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    uint32_t received2 = 0;
    ret = GmcSubscribe(syncStmt, &config2, subConn, SubcallBack, &received2);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config2.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelNameForCallBack, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 22; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */
    WAIT_WHILE(received1 != total);
    WAIT_WHILE(received2 != total);
}

// 2 单个回调时间超时打warning日志
TEST_F(StClientSub, testSubPushCallBackLog3)
{
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": true }
        ],
        "super_fields":[
            { "name":"superfield0", "fields":{ "begin":"F0", "end":"F1" } }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelNameForCallBack);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true
    }
    )";

    uint32_t received = 0;
    ret = GmcSubscribe(syncStmt, &config, subConn, SubcallBack2, &received);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* make data for subscription */
    ret = GmcPrepareStmtByLabelName(syncStmt, labelNameForCallBack, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t total = 0;

    for (; total < 1; ++total) {
        uint32_t F0Value = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    /* wait till all events are received */

    WAIT_WHILE(received != total);
}

// 4 使用接口设置订阅平均超时回调阈值
TEST_F(StClientAsync, testSubPushCallBackLog4)
{
    Status ret;
    static const char *serverLocator = "usocket:/run/verona/unix_emserver";
    GmcConnOptionsT *connOptions;
    ret = GmcConnOptionsCreate(&connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnOptionsSetServerLocator(connOptions, serverLocator);
    ret = GmcConnOptionsSetCallbackTimeout(connOptions, 50, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetCallbackTimeout(connOptions, 0, false);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    const char *lastError = GmcGetLastError();
    cout << "lastError: " << lastError << endl;

    ret = GmcConnOptionsSetCallbackTimeout(connOptions, 1001, false);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    lastError = GmcGetLastError();
    cout << "lastError: " << lastError << endl;

    ret = GmcConnOptionsSetCallbackTimeout(connOptions, 4294967295, false);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    lastError = GmcGetLastError();
    cout << "lastError: " << lastError << endl;

    ret = GmcConnOptionsSetCallbackTimeout(connOptions, -1, false);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    lastError = GmcGetLastError();
    cout << "lastError: " << lastError << endl;
}

void *ThreadTruncate(void *args)
{
    const char *labelName = "subLabel1";
    Status ret;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    ret = GmcDeleteAllFast(syncStmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// test DML truncate and disconnect run concurrently
TEST_F(StClientSub, testTruncateAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "delete", "msgTypes":["old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // prepare data
    uint32_t total = 0;
    while (total < 2000) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadTruncate, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

#pragma pack(1)
struct TempInfo {
    GmcStmtT *stmt;
    GmcConnT *conn;
};
#pragma pack()

uint32_t received = 0;

void *ThreadMerge(void *args)
{
    Status ret;
    uint32_t total = 0;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    while (total < 500) {
        uint32_t i = total;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t j = i + 2;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
    return NULL;
}

// test DML merge and disconnect run concurrently
TEST_F(StClientSub, testMergeAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    constexpr auto indexName = "subLabel1_K0";
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_MERGE, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "merge", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadMerge, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

void *ThreadReplace(void *args)
{
    Status ret;
    uint32_t total = 0;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    while (total < 500) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
    return NULL;
}

// test DML replace and disconnect run concurrently
TEST_F(StClientSub, testReplaceAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_REPLACE, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */

    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "replace", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadReplace, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

void *ThreadUpdate(void *args)
{
    Status ret;
    uint32_t total = 0;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    while (total < 500) {
        uint32_t i = total;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t j = i + 2;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &j, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
    return NULL;
}

// test DML update and disconnect run concurrently
TEST_F(StClientSub, testUpdateAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // prepare data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 0;
    while (total < 500) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    constexpr auto indexName = "subLabel1_K0";
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_UPDATE, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */
    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "update", "msgTypes":["new object", "old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadUpdate, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

void *ThreadDelete(void *args)
{
    Status ret;
    uint32_t total = 0;
    GmcStmtT *syncStmt = (GmcStmtT *)args;
    while (total < 500) {
        uint32_t i = total;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }
    return NULL;
}

// test DML delete and disconnect run concurrently
TEST_F(StClientSub, testDeleteAndDisconnect)
{
    int32_t ret;
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    // prepare data
    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t total = 0;
    while (total < 500) {
        uint32_t f0 = total;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1 = total;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);
        total++;
    }

    ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    constexpr auto indexName = "subLabel1_K0";
    ret = GmcSetIndexKeyName(syncStmt, indexName);
    EXPECT_EQ(GMERR_OK, ret);

    /* callback */

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        volatile uint32_t &received = *reinterpret_cast<uint32_t *>(userData);
        EXPECT_EQ(GMC_SUB_EVENT_DELETE, info->eventType);

        for (bool eof;; ++received) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }
            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t value;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
        }
    };

    /* subscribe */
    GmcSubConfigT config1;
    config1.subsName = "subVertexLabel1";
    config1.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "events": [{ "type": "delete", "msgTypes":["old object"]}],
        "is_reliable": true
    }
    )";

    uint32_t received1 = 0;
    ret = GmcSubscribe(syncStmt, &config1, subConn, callback, &received1);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcUnSubscribe(syncStmt, config1.subsName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    pthread_t thread1;
    pthread_t thread2;

    pthread_create(&thread1, NULL, ThreadDelete, (void *)syncStmt);
    pthread_create(&thread2, NULL, ThreadDisconnect, (void *)subConn);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    // avoid double disconnect
    subConn = NULL;
}

const int subUnsubTime = 100;
class StClientSubMem : public StClientSub {
public:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"udfEnable=1\" \"enableDmlPerfStat=1\" \"auditLogEnableDML=0\" "
                                "\"maxNormalTableNum=10000\" ");
        uint32_t liteDynMem = 1;
        GmcSetCltCfg("clientLiteDynMemModeEnable", GMC_DATATYPE_INT32, &liteDynMem, (uint32_t)sizeof(int32_t));
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    }
    static void TearDownTestCase()
    {
        StopAndDestroyEpoll(responseEpollThreadId, &responseEpollFd);
        StopAndDestroyEpoll(timeoutEpollThreadId, &timeoutEpollFd);
        st_clt_uninit();
        DbCltCfgSetDefaultByName("clientLiteDynMemModeEnable");
        ShutDownDbServer();
    }
};

TEST_F(StClientSubMem, VertexLabelSubUnsubMemTest)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subLabel1/F0",
                    "value": 1
                },
                {
                    "property": "F1",
                    "value": 2
                }
            ]
        }
    }
    )";

    GmcClientMemCtxStatInfoT memCtxInfo1, memCtxInfo2;

    for (int i = 0; i < subUnsubTime; i++) {
        ret = GmcSubscribe(
            syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo1);
        }
    }

    GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo2);
    EXPECT_EQ(memCtxInfo2.clientTotalMemSize, memCtxInfo1.clientTotalMemSize);
}

TEST_F(StClientSubMem, VertexLabelSubUnsubFailMemTest)
{
    constexpr auto labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            { "name": "F2", "type": "uint32", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                {
                    "property": "subLabel1/F0",
                    "value": 1
                },
                {
                    "property": "F1",
                    "value":
                }
            ]
        }
    }
    )";

    GmcClientMemCtxStatInfoT memCtxInfo1, memCtxInfo2;

    for (int i = 0; i < subUnsubTime; i++) {
        ret = GmcSubscribe(
            syncStmt, &config, subConn, [](auto, auto, auto) {}, NULL);
        EXPECT_NE(GMERR_OK, ret);

        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo1);
        }
    }

    GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo2);
    EXPECT_EQ(memCtxInfo2.clientTotalMemSize, memCtxInfo1.clientTotalMemSize);
}

TEST_F(StClientSubMem, SubPushVertexMemTest)
{
    constexpr auto &labelName = "subLabel1";
    constexpr auto labelJson = R"(
    [{
        "type": "record",
        "name": "subLabel1",
        "fields": [
            { "name": "F0", "type": "uint32", "nullable": false },
            { "name": "F1", "type": "uint32", "nullable": false },
            {
                "name": "subs_fixed",
                "type": "fixed",
                "default": "ffff",
                "size": 4,
                "nullable": true
            },
            { "name": "subs_bytes", "type": "bytes", "size": 20, "nullable": true},
            { "name": "subs_float", "type": "float", "nullable": true },
            { "name": "subs_double", "type": "double", "nullable": true }
        ],
        "keys": [
            {
                "node": "subLabel1",
                "name": "subLabel1_K0",
                "fields": ["F0"],
                "index": { "type": "primary" },
                "constraints": { "unique": true }
            }
        ]
    }]
    )";

    auto ret = GmcCreateVertexLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    defer
    {
        ret = GmcDropVertexLabel(syncStmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    };

    /* subscribe */

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
        {
        "name": "subVertexLabel1",
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "insert", "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "or",
            "conditions": [
                {
                    "property": "subs_fixed",
                    "value": "1234"
                },
                {
                    "property": "subs_float",
                    "value": 1.234567
                },
                {
                    "property": "subs_double",
                    "value": 3.234333333333333
                }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData) {
        EXPECT_EQ(GMC_SUB_EVENT_INSERT, info->eventType);
        EXPECT_EQ(1u, info->labelCount);
        uint32_t &recUsrData = *reinterpret_cast<uint32_t *>(userData);

        char pushLabelName[sizeof(labelName)] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        auto ret = GmcSubGetLabelName(stmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ(labelName, pushLabelName);

        for (bool eof;; ++recUsrData) {
            auto ret = GmcFetch(stmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof)
                break;

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);
            bool isNull;
            uint64_t tStart = 0;
            ret = GmcSubGetProperty(stmt, GMC_SUB_FETCH_NEW, 0, &tStart, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);

            ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_OLD);
            EXPECT_NE(GMERR_OK, ret);
        }
    };

    function<void()> conditionalValueSetter[] = {
        [&]() {
            char fixedValue[] = "1234";
            ret = GmcSetVertexProperty(syncStmt, "subs_fixed", GMC_DATATYPE_FIXED, fixedValue, strlen(fixedValue));
            EXPECT_EQ(GMERR_OK, ret);
        },
        [&]() {
            float floatValue = 1.234567;
            ret = GmcSetVertexProperty(syncStmt, "subs_float", GMC_DATATYPE_FLOAT, &floatValue, sizeof(float));
            EXPECT_EQ(GMERR_OK, ret);
        },
        [&]() {
            double doubleValue = 3.234333333333333;
            ret = GmcSetVertexProperty(syncStmt, "subs_double", GMC_DATATYPE_DOUBLE, &doubleValue, sizeof(double));
            EXPECT_EQ(GMERR_OK, ret);
        },
    };

    GmcClientMemCtxStatInfoT memCtxInfo1, memCtxInfo2;

    for (int i = 0; i < subUnsubTime; i++) {
        received = 0;
        ret = GmcSubscribe(syncStmt, &config, subConn, callback, &received);
        EXPECT_EQ(GMERR_OK, ret);

        /* make data for subscription */

        ret = GmcPrepareStmtByLabelName(syncStmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t total = 0;

        for (const auto &setConditionalValue : conditionalValueSetter) {
            setConditionalValue();
        }

        uint32_t f0Value = total + i;
        ret = GmcSetVertexProperty(syncStmt, "F0", GMC_DATATYPE_UINT32, &f0Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t f1Value = total + 1;
        ret = GmcSetVertexProperty(syncStmt, "F1", GMC_DATATYPE_UINT32, &f1Value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(syncStmt);
        EXPECT_EQ(GMERR_OK, ret);

        ++total;

        /* wait till all events are received */
        WAIT_WHILE(received != total);

        ret = GmcUnSubscribe(syncStmt, config.subsName);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo1);
        }
    }

    GmcGetClientMemCtxPhySize(syncStmt->conn, syncStmt, &memCtxInfo2);
    EXPECT_EQ(memCtxInfo2.clientTotalMemSize, memCtxInfo1.clientTotalMemSize);
}
