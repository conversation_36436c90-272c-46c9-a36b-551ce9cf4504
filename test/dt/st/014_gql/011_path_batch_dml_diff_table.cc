/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st file for path batch dml
 * Author: GQL
 * Create: 2023-08-29
 */

#include <string>
#include "gtest/gtest.h"
#include "stub.h"
#include "ee_session_interface.h"
#include "ee_session.h"
#include "ee_stmt_fusion.h"
#include "ee_gql_batch_dml_receiver.h"
#include "gmc_gql.h"
#include "srv_data_gql_path_summary.h"
#include "client_option.h"
#include "client_common_st.h"
#include "fes_common.h"
#include "fes_tables_data_structure.h"

const uint32_t WAIT_TIME = 1000;  // ms
static pthread_t g_epollThreadId;

class PathBatchDmlDiffTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // FES PATH需要硬件加速提供的hash
        StartDbServer((char *)"gmserver_gql.ini");
        ImportAllowList();
        st_clt_init();
        pthread_create(&g_epollThreadId, NULL, GmcStartEpoll, NULL);
    }

    static void TearDownTestCase()
    {
        GmcStopEpoll();
        pthread_join(g_epollThreadId, NULL);
        st_clt_uninit();
        ShutDownDbServer();
    }

    virtual void SetUp()
    {
        CreateSyncConnectionAndStmt(&conn, &stmt);
        CreateAsyncConnectionAndStmt(&asyncConn, &asyncStmt);
        // create table
        const char *createTables = R"(
            CREATE VERTEXLABEL test_table1 (
                property1 uint32,
                property2 uint32,
                property3 uint32,
                property4 uint16
                PRIMARY INDEX pk(property1)
                HAC_HASH INDEX index1(property2) UNIQUE,
                MULTI_HASH INDEX index2(property3)
            );
            CREATE VERTEXLABEL test_table2 (
                property1 uint32,
                property2 uint32,
                property3 uint32,
                property4 uint16
                PRIMARY INDEX pk(property1)
                HAC_HASH INDEX index1(property2) UNIQUE,
                MULTI_HASH INDEX index2(property3)
            );
            CREATE VERTEXLABEL test_table3 (
                property1 uint32,
                property2 uint32,
                property3 uint32,
                property4 uint16
            );
        )";
        ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, createTables));
    }

    virtual void TearDown()
    {  // drop table
        const char *dropTables = R"(
            DROP VERTEXLABEL test_table3;
            DROP VERTEXLABEL test_table2;
            DROP VERTEXLABEL test_table1;
        )";
        ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, dropTables));
        GmcFreeStmt(stmt);
        GmcDisconnect(conn);
        GmcFreeStmt(asyncStmt);
        GmcDisconnect(asyncConn);
    }

    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcConnT *asyncConn;
    GmcStmtT *asyncStmt;
};

/*
 * 同时在两张表上插入500条数据
 * 表1上主键删除，表2上主键删除
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_001)
{
    uint32_t num = 1;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * 表1上主键删除，表2上二级唯一索引删除
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_002)
{
    uint32_t num = 250;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * 表1上主键删除，表2上二级非唯一索引删除
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_003)
{
    uint32_t num = 300;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * 表1上主键删除，表2上filter删除
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_004)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * 表1上主键删除，表2上GmcSetVertexPropertyAll删除
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_005)
{
    uint32_t num = 1000;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}
/*
 * 同时在两张表上插入一条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_006)
{
    uint32_t num = 1;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入250条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_007)
{
    uint32_t num = 250;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入250条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_008)
{
    uint32_t num = 300;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_009)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_insert_010)
{
    uint32_t num = 1000;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 2;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(2u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}
/*
 * 同时在两张表上插入一条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_replace_001)
{
    uint32_t num = 1;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入250条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_replace_002)
{
    uint32_t num = 250;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入250条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_replace_003)
{
    uint32_t num = 300;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入500条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_replace_004)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_replace_005)
{
    uint32_t num = 1000;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetMaxBatchOpNum(&batchOption, 2);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
}

/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_delete_001)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
}
/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_delete_002)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "index1"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
}
/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_delete_003)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "index2"));
        uint32_t value = 1;
        EXPECT_EQ(GMERR_OK,
            GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(0u, cnt);
}

/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_delete_004)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        uint16_t value = 1;
        GmcFilterStructT filter = {0};
        filter.fieldId = 3;
        filter.compOp = GMC_OP_EQUAL;
        filter.value = &value;
        filter.valueLen = sizeof(uint16_t);
        EXPECT_EQ(GMERR_OK, GmcSetFilterStructure(PathBatchDmlDiffTable::stmt, &filter));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num / 2, cnt);
}

/*
 * 同时在两张表上插入1000条数据
 * return GMERR_OK
 */
TEST_F(PathBatchDmlDiffTable, batch_dml_diff_table_delete_005)
{
    uint32_t num = 500;
    // batch execute
    uint32_t modelType = MODEL_GQL;  // set model type to router into path service
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    FesTestTableWith4PropT obj = {0};
    for (uint32_t i = 0; i < num; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_REPLACE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i % 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(PathBatchDmlDiffTable::stmt, &obj, sizeof(obj)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    GmcBatchRetT batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));

    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num, cnt);
    ret = GmcBatchPrepare(PathBatchDmlDiffTable::stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 1; i++) {
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(PathBatchDmlDiffTable::stmt, "pk"));
        EXPECT_EQ(
            GMERR_OK, GmcSetIndexKeyValue(PathBatchDmlDiffTable::stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
        ASSERT_EQ(
            GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_DELETE));
        ASSERT_EQ(GMERR_OK,
            GmcSetStmtAttr(PathBatchDmlDiffTable::stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
        obj.property1 = i;
        obj.property2 = i;
        obj.property3 = 1;
        obj.property4 = i / 2;
        ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(FesTestTableWith4PropT)));
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, PathBatchDmlDiffTable::stmt));
    }
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table1", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
    // check insert data
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(PathBatchDmlDiffTable::stmt, "test_table2", GMC_OPERATION_SCAN));
    ASSERT_EQ(GMERR_OK, GmcExecute(PathBatchDmlDiffTable::stmt));
    cnt = 0;
    isFinish = false;
    while (true) {
        ASSERT_EQ(GMERR_OK, GmcFetch(PathBatchDmlDiffTable::stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(PathBatchDmlDiffTable::stmt, "property3", &size);
        ASSERT_EQ(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(PathBatchDmlDiffTable::stmt, "property3", &property3, size, &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(1u, property3);
        cnt++;
    }
    ASSERT_EQ(num - 1, cnt);
}
