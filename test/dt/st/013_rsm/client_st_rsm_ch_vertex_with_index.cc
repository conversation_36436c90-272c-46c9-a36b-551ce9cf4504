/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: client_st_rsm_ch_vertex_with_index.cc
 * Description: testcase for rsm ch label with index
 * Author: lujiahao
 * Create: 2024-07-09
 */
#include "tools_st_common.h"
#include "storage_st_common.h"
#include "rsm_st_common.h"

class StRsmChVertexWithIndex : public RsmStGmcUtil, public StStorage {
protected:
    uint32_t setUpLogNum = 0;
    uint32_t tearDownLogNum = 0;
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}
    void SetUp()
    {
        DoSetUp();
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        setUpLogNum = atoi(errorNum);
    }
    void TearDown()
    {
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        tearDownLogNum = atoi(errorNum);
        EXPECT_EQ(tearDownLogNum, setUpLogNum);  // 预期该测试套用例都不应该有memctx对账过程中删掉的内存
        DoTearDownCheckAndUninit();
        SaveLogAfterFailed();
    }
};

#ifdef ENABLE_CRASHPOINT

const char *g_rsmLabelName = "rsm_clustered_hash_table";
const char *g_rsmLabelConfig =
    R"({"max_record_count":1000000, "is_support_reserved_memory":1, "isFastReadUncommitted":true})";
const char *g_rsmLabelConfig2 = R"({
        "max_record_count":1000000,
        "is_support_reserved_memory":1,
        "isFastReadUncommitted":true,
        "defragmentation":true
    })";
char *g_rsmLabelSchema = NULL;
char *g_rsmLabelSchema2 = NULL;
char *g_rsmLabelSchema3 = NULL;

typedef struct VertexDataParam {
    uint32_t start;
    uint32_t end;
    const char *labelName;
    bool isCheckVersion;
    uint32_t schemaVersion;
} VertexDataParamT;

static Status InsertChVertexData(GmcStmtT *stmt, VertexDataParamT *param, Status checkRet = GMERR_OK)
{
    if (param->isCheckVersion) {
        EXPECT_EQ(GMERR_OK,
            GmcPrepareStmtByLabelNameWithVersion(stmt, param->labelName, param->schemaVersion, GMC_OPERATION_INSERT));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, param->labelName, GMC_OPERATION_INSERT));
    }
    Status ret = GMERR_OK;
    for (uint32_t i = param->start; i < param->end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 1;
        uint32_t f2 = i + 2;
        uint32_t f3 = i + 3;
        uint32_t f4 = (i + 4) % 16;
        uint32_t f5 = (i + 5) % 1024;
        uint32_t f6 = i + 6;
        uint8_t f7 = (i + 7) % 33;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &f5, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &f6, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7, sizeof(uint8_t)));
        ret = GmcExecute(stmt);
        if (ret != checkRet) {
            std::cout << "insert err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DeleteChVertexData(GmcStmtT *stmt, VertexDataParamT *param, Status checkRet = GMERR_OK)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, param->labelName, GMC_OPERATION_DELETE));
    Status ret = GMERR_OK;
    for (uint32_t i = param->start; i < param->end; i++) {
        uint32_t pk = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
        if (ret != checkRet) {
            std::cout << "delete err data i: " << i << std::endl;
            return GMERR_INTERNAL_ERROR;
        }
    }
    return GMERR_OK;
}

static Status UpdateChVertexData(GmcStmtT *stmt, VertexDataParamT *param, Status checkRet = GMERR_OK)
{
    if (param->isCheckVersion) {
        EXPECT_EQ(GMERR_OK,
            GmcPrepareStmtByLabelNameWithVersion(stmt, param->labelName, param->schemaVersion, GMC_OPERATION_UPDATE));
    } else {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, param->labelName, GMC_OPERATION_UPDATE));
    }
    Status ret = GMERR_OK;
    for (uint32_t i = param->start; i < param->end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 100001;
        uint32_t f2 = i + 100002;
        uint32_t f3 = i + 100003;

        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
        if (ret != checkRet) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CheckData(GmcStmtT *stmt, uint32_t i, bool isFind, bool isUpdated)
{
    bool isNull;
    uint32_t f0Res;
    Status ret = GmcGetVertexPropertyByName(stmt, "F0", &f0Res, sizeof(f0Res), &isNull);
    if (!isFind) {
        EXPECT_EQ(ret, GMERR_NO_DATA);
        return ret == GMERR_NO_DATA ? GMERR_OK : GMERR_INTERNAL_ERROR;
    }
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_NE(isNull, isFind);
    EXPECT_EQ(f0Res, i);

    uint32_t f1Res;
    ret = GmcGetVertexPropertyByName(stmt, "F1", &f1Res, sizeof(f1Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f1Res, isUpdated ? i + 100001 : i + 1);
    } else {
        EXPECT_NE(f1Res, isUpdated ? i + 100001 : i + 1);
    }

    uint32_t f2Res;
    ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Res, sizeof(f2Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f2Res, isUpdated ? i + 100002 : i + 2);
    } else {
        EXPECT_NE(f2Res, isUpdated ? i + 100002 : i + 2);
    }

    uint32_t f3Res;
    ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Res, sizeof(f3Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f3Res, isUpdated ? i + 100003 : i + 3);
    } else {
        EXPECT_NE(f3Res, isUpdated ? i + 100003 : i + 3);
    }

    uint32_t f4Res;
    ret = GmcGetVertexPropertyByName(stmt, "F4", &f4Res, sizeof(f4Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f4Res, (i + 4) % 16);
    } else {
        EXPECT_NE(f4Res, (i + 4) % 16);
    }

    uint32_t f5Res;
    ret = GmcGetVertexPropertyByName(stmt, "F5", &f5Res, sizeof(f5Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f5Res, (i + 5) % 1024);
    } else {
        EXPECT_NE(f5Res, (i + 5) % 1024);
    }

    uint32_t f6Res;
    ret = GmcGetVertexPropertyByName(stmt, "F6", &f6Res, sizeof(f6Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f6Res, i + 6);
    } else {
        EXPECT_NE(f6Res, i + 6);
    }

    uint8_t f7Res;
    ret = GmcGetVertexPropertyByName(stmt, "F7", &f7Res, sizeof(f7Res), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    if (isFind) {
        EXPECT_NE(isNull, isFind);
        EXPECT_EQ(f7Res, (i + 7) % 33);
    } else {
        EXPECT_NE(f7Res, (i + 7) % 33);
    }
    return GMERR_OK;
}

static Status LookUpVertexByPk(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, false);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLocal(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i + 1;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "local"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by local, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, false);
        if (ret != GMERR_OK) {
            std::cout << "look by local, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLocalhash(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f2 = i + 2;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "localhash"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by localhash, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, false);
        if (ret != GMERR_OK) {
            std::cout << "look by localhash, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByHashCluster(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f3 = i + 3;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "hashcluster"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by hashcluster, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, false);
        if (ret != GMERR_OK) {
            std::cout << "look by hashcluster, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLpm(GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f4 = (i + 4) % 16;
        uint32_t f5 = (i + 5) % 1024;
        uint32_t f6 = i + 6;
        uint8_t f7 = (i + 7) % 33;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f5, sizeof(f5)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f6, sizeof(f6)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &f7, sizeof(f7)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "lpm4_key"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by lpm, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, false);
        if (ret != GMERR_OK) {
            std::cout << "look by lpm, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByPkAfterUpdate(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f0 = i;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, true);
        if (ret != GMERR_OK) {
            std::cout << "look by pk, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLocalAfterUpdate(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f1 = i + 100001;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "local"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f1, sizeof(f1)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by local, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, true);
        if (ret != GMERR_OK) {
            std::cout << "look by local, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLocalhashAfterUpdate(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f2 = i + 100002;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "localhash"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f2, sizeof(f2)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by localhash, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, true);
        if (ret != GMERR_OK) {
            std::cout << "look by localhash, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByHashClusterAfterUpdate(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f3 = i + 100003;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "hashcluster"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3, sizeof(f3)));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by hashcluster, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, true);
        if (ret != GMERR_OK) {
            std::cout << "look by hashcluster, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

static Status LookUpVertexByLpmAfterUpdate(
    GmcStmtT *stmt, uint32_t start, uint32_t end, bool isFind, const char *labelName)
{
    Status ret;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN));
    for (uint32_t i = start; i < end; i++) {
        uint32_t f4 = (i + 4) % 16;
        uint32_t f5 = (i + 5) % 1024;
        uint32_t f6 = i + 6;
        uint8_t f7 = (i + 7) % 33;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f4, sizeof(f4)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f5, sizeof(f5)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &f6, sizeof(f6)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &f7, sizeof(f7)));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "lpm4_key"));
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            std::cout << "look by lpm, err data i: " << i << std::endl;
            return ret;
        }

        bool isFinish;
        EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));

        ret = CheckData(stmt, i, isFind, true);
        if (ret != GMERR_OK) {
            std::cout << "look by lpm, err data i: " << i << std::endl;
            return ret;
        }
    }
    return GMERR_OK;
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertLocalEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        // SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_PREV_BITMAP, // 走local entry分支
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
        SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_VERSION,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1000;
        param.end = 1001;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 1001, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertPrevEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_PREV_BITMAP,
        // SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT, // 走prev entry分支
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
        SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_VERSION,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 805;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 805, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 805, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 805, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 805, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 805, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 805"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 805;
        param.end = 806;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 805"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 805, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 805, 806, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 805, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 805, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 805, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 805, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1805"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1805"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 805"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertStashedEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 18;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_STASH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_STASH_BUCKET_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_INSERT_ALLOC_STASH_ENTRY,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
        SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_VERSION,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_STASHED_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_STASHED_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 803;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 803, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 803, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 803, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 803, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 803, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 803"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 803;
        param.end = 804;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 803"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 803, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 803, 804, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 803, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 803, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 803, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 803, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1803"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1803"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 803"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertDisplaceCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 14;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_DISPLACE_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_PREV_BITMAP,
        // SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_INSERT_DISPLACE_COPY,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
        SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
        SHM_CRASH_CH_INSERT_DISPLACE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 827;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 827, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 827, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 827, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 827, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 827, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 827"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 827;
        param.end = 828;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 827"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 827, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 827, 828, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 827, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 827, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 827, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 827, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1827"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1827"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 827"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertDisplaceCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 13;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_DISPLACE_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        // SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_INSERT_DISPLACE_COPY,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_PREV_BITMAP,
        // SHM_CRASH_CH_BUCKET_FREE_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
        SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
        SHM_CRASH_CH_INSERT_DISPLACE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1154;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1154, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1154, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1154, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1154, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1154"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1154;
        param.end = 1155;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1154"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1154, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1154, 1155, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1154, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1154, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1154, true, g_rsmLabelName));

        param.start = 2000;
        param.end = 3000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 2000, 3000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2154"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2154"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1154"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexDeleteNormalEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 16;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
        SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_COUNT,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_USED,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 999;
        param.end = 1000;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexDeleteNormalLocalEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_BUCKET_FREE_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
        SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_COUNT,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_USED,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        param.start = 0;
        param.end = 1;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1;
        param.end = 2;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 999"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1999"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1999"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 999"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexDeleteStashedEntryCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_DELETE_BEFORE,
        SHM_CRASH_CH_DELETE_CLEAR_BUCKET,
        SHM_CRASH_CH_STASHED_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_STASHED_FREE_UPDATE_CNT,
        SHM_CRASH_CH_STASH_BUCKET_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_STASH_BUCKET_FREE_UPDATE_CNT,
        SHM_CRASH_CH_ENTRY_CLEAR_HASHCODE,
        SHM_CRASH_CH_ENTRY_CLEAR_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_CLEAR_VERSION,
        SHM_CRASH_CH_DELETE_CLEAR_ENTRY,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_BITMAP,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_FREE_SLOT_COUNT,
        SHM_CRASH_CH_TUPLE_FREE_UPDATE_USED,
        SHM_CRASH_CH_DELETE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        param.start = 0;
        param.end = 86;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 86;
        param.end = 87;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 914"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 86, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 86, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 86, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 86, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 86, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 86, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1914"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1914"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 914"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexUpdateCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_UPDATE_BEFORE,
        SHM_CRASH_CH_UPDATE_CHANGE_TRX_ID,
        SHM_CRASH_CH_UPDATE_CHANGE_BUFFER_SIZE,
        SHM_CRASH_CH_UPDATE_CHANGE_ROW,
        SHM_CRASH_CH_UPDATE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 999;
        param.end = 1000;
        EXPECT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexRecycleEmptyPageCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 8;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_RECYCLE_PAGE_BEFORE,
        SHM_CRASH_CH_RECYCLE_PAGE_FREE_PAGE,
        SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_TYPE,
        SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_PAGE_ADDR,
        SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_DEPTH,
        SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_OTHER_SEG,
        SHM_CRASH_CH_RECYCLE_PAGE_UPDATE_SEG_CNT,
        SHM_CRASH_CH_RECYCLE_PAGE_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        param.start = 0;
        param.end = 952;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 952;
        param.end = 953;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 47"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 953, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 953, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 953, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 953, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 953, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 953, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1047"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1047"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 47"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexScaleInDirCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_SCALEIN_DIR_BEFORE,
        SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE_FINISHED,
        SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_PAGE_CNT,
        SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_CAP,
        SHM_CRASH_CH_SCALEIN_DIR_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_CAPACITY: 16", "ENTRY_USED: 1000"));

        param.start = 0;
        param.end = 990;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 990;
        param.end = 991;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_CAPACITY: 8", "ENTRY_USED: 9"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 991, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 991, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 991, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 991, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 991, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 991, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1009"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1009"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 9"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

const char *g_rsmHugeLabelName = "rsm_clustered_hash_huge_table";
char *g_rsmHugeLabelSchema = NULL;
TEST_F(StRsmChVertexWithIndex, TestRsmChVertexFreeDirPageCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_huge_table.gmjson", &g_rsmHugeLabelSchema);
    uint32_t testNum = 6;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_SCALEIN_DIR_BEFORE,
        SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE,
        SHM_CRASH_CH_SCALEIN_DIR_FREE_DIR_PAGE_FINISHED,
        SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_PAGE_CNT,
        SHM_CRASH_CH_SCALEIN_DIR_UPDATE_DIR_CAP,
        SHM_CRASH_CH_SCALEIN_DIR_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmHugeLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 30000;
        param.labelName = g_rsmHugeLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 30000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 30000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 30000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 30000, true, g_rsmHugeLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_PAGE_COUNT: 2", "ENTRY_USED: 30000"));

        param.start = 0;
        param.end = 29812;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 29812;
        param.end = 29813;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmHugeLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "DIRECTORY_PAGE_COUNT: 1", "ENTRY_USED: 187"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 29813, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 29813, 30000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 29813, 30000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 29813, 30000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 29813, 30000, true, g_rsmHugeLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmHugeLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1187"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1187"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 187"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmHugeLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertExpandCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 51;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        SHM_CRASH_CH_EXPAND_SEG_SPLIT_END,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,
        SHM_CRASH_SPACE_INIT_PAGE_BEFORE,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_LOCK,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_CHECK_SUM,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_BEGIN_POS,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_END_POS,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_ADDR,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_ENTRY_USED_NUM,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_PAGE_STATE,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_TRM_ID,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_LSN,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_NEXT_PAGE_ID,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_SET_COMPRESS_STATE,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_MEMSET_RESERVE,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER_UNLOCK,
        SHM_CRASH_SPACE_INIT_PAGE_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 806;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 806, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 806, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 806, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 806, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 806, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 806"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 806;
        param.end = 807;
        ASSERT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 806"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 806, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 806, 807, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 806, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 806, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 806, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 806, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1806"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1806"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 806"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertExpandCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_huge_table.gmjson", &g_rsmHugeLabelSchema);
    uint32_t testNum = 39;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        SHM_CRASH_CH_EXPAND_SEG_SPLIT_END,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_ALLOC_PAGE,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_RECORD_PAGE_COUNT,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_DEPTH,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_CAP,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmHugeLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 22017;
        param.labelName = g_rsmHugeLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 22017, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 22017, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 22017, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 22017, true, g_rsmHugeLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 22017"));

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 22017;
        param.end = 22018;
        ASSERT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmHugeLabelSchema, g_rsmLabelConfig));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 22017"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 22017, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 22017, 22018, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 22017, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 22017, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 22017, true, g_rsmHugeLabelName));

        param.start = 0;
        param.end = 20000;
        EXPECT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 20000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 20000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 20000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 20000, false, g_rsmHugeLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2017"));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmHugeLabelName));

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 3017"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 3017"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2017"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmHugeLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmHugeLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexScaleInCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 11;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_BEFORE,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_HALF,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_STASH_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_END,
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_BEFORE,
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE,
        SHM_CRASH_CH_SCALE_IN_UPDATE_DIR_BEFORE,
        SHM_CRASH_CH_SCALE_IN_UPDATE_PATTERN,
        SHM_CRASH_CH_SCALE_IN_UPDATE_SEG_DEPTH,
        SHM_CRASH_CH_SCALE_IN_FREE_EMPTY_PAGE_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig2));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        DbShmCrashPointSet(testCases[i]);
        param.start = 0;
        param.end = 233;
        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));  // 添加缩容任务
        sleep(5);                                               // 休眠5s，等待缩容线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig2));
        RsmWaitRecoveryFinish();

        system(command);
        if (i < 5) {
            ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 16", "ENTRY_USED: 767",
                                    "SCALE_IN_THREHOLD_MEET_COUNT: 0", "SCALE_IN_ACTUAL_BEGIN_COUNT: 0"));
        } else {
            ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 15", "ENTRY_USED: 767",
                                    "SCALE_IN_THREHOLD_MEET_COUNT: 0", "SCALE_IN_ACTUAL_BEGIN_COUNT: 0"));
        }

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 233, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 233, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1767"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1767"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 767"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexScaleInCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_BEFORE,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_HALF,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_MERGE_STASH_BUCKET,
        SHM_CRASH_CH_HORIZONTAL_SCALE_IN_END,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig2));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 1000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        DbShmCrashPointSet(testCases[i]);
        param.start = 0;
        param.end = 233;
        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));  // 添加缩容任务
        sleep(5);                                               // 休眠5s，等待缩容线程触发崩溃恢复

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHashAndCompress(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig2));
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 16", "ENTRY_USED: 767",
                                "SCALE_IN_THREHOLD_MEET_COUNT: 0", "SCALE_IN_ACTUAL_BEGIN_COUNT: 0"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 233, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 233, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 233, 1000, true, g_rsmLabelName));

        param.start = 233;
        param.end = 234;
        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));  // 添加缩容任务
        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "SEGMENT_PAGE_COUNT: 14", "ENTRY_USED: 766",
                                "SCALE_IN_THREHOLD_MEET_COUNT: 1", "SCALE_IN_ACTUAL_BEGIN_COUNT: 1"));

        sleep(5);  // 休眠5s，等待缩容任务结束
        system(command);

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1766"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1766"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 766"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertAllocNewPageCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 8;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_EXPAND_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_CHAIN,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_ADD_LOCK,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_DEVICE_ID,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_BLOCK_ID,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_DIR,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_OUTPUT,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 500;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 500, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 500;
        param.end = 1000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 500, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1000;
        param.end = 1001;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 2000;
        param.end = 2001;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 1001, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        param.start = 1000;
        param.end = 2000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpmAfterUpdate(stmt, 1000, 2000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 1000, 2000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexInsertAllocNewPageCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 8;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_MUL_VERSION_EXPAND_BEFORE,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_CHAIN,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_ADD_LOCK,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_DEVICE_ID,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_SEG_BLOCK_ID,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_UPDATE_DIR,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_SET_OUTPUT,
        SHM_CRASH_CH_MUL_VERSION_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 10000;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 10000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 10000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 10000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 10000, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 10000;
        param.end = 20000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 10000, 20000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 10000, 20000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 10000, 20000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 10000, 20000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20000"));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 20000;
        param.end = 20001;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 30000;
        param.end = 30001;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 20000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 20000, 20001, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 20000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 20000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 20000, true, g_rsmLabelName));

        param.start = 21000;
        param.end = 22000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 21000, 22000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 21000, 22000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 21000, 22000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 21000, 22000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 21000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 21000, 22000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 21000, 22000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 21000, 22000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 21000, 22000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 21000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 21000, 22000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 21000, 22000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 21000, 22000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 21000, 22000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 20000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexMulVersionExpandCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 40;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,
        SHM_CRASH_CH_EXPAND_SEG_SPLIT_END,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_OLD_DIR,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_NEW_DIR,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_DEPTH,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_UPDATE_DIR_CAP,
        SHM_CRASH_CH_EXPAND_DOUBLE_DIR_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 500;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 500, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 500;
        param.end = 1000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 500, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        param.start = 1000;
        param.end = 1565;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1565;
        param.end = 1566;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 2000;
        param.end = 2001;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1565"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1565, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1565, 1566, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1565, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1565, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1565, true, g_rsmLabelName));

        param.start = 2000;
        param.end = 3000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2565"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2565"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1565"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexMulVersionExpandCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 38;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_EXPAND_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE,
        SHM_CRASH_CH_EXPAND_RECORD_SEGPAGE_COUNT,
        SHM_CRASH_CH_EXPAND_ALLOC_NEXT_SEGPAGE_BEFORE,
        SHM_CRASH_CH_EXPAND_ALLOC_SEGPAGE_AFTER,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_BEFORE,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_SEG_DEPTH,
        SHM_CRASH_CH_EXPAND_UPDATE_DIRSEG_AFTER,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_BEFORE,
        SHM_CRASH_CH_EXPAND_SPLIT_BUCKET_AFTER,
        SHM_CRASH_CH_EXPAND_MEMCPY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_ALLOC_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_PREV_BITMAP,
        SHM_CRASH_CH_PROC_BUCKET_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_CLEAR_ENTRY,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_ALLOC_BIMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_CNT,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_BITMAP,
        SHM_CRASH_CH_PROC_STASHED_UPDATE_STASH_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_RESET_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_EXPAND_UPDATE_NEXT_SLOT_ID,
        SHM_CRASH_CH_EXPAND_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_EXPAND_PROC_OLD_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEW_PAGE,
        SHM_CRASH_CH_EXPAND_PROC_NEXT_PAGE,
        SHM_CRASH_CH_EXPAND_SEG_SPLIT_END,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_BEFORE,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_HALF,
        SHM_CRASH_CH_EXPAND_FRESH_DIR_AFTER,
        SHM_CRASH_CH_EXPAND_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 500;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 500, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 500;
        param.end = 1000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 500, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        param.start = 1000;
        param.end = 1585;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1585;
        param.end = 1586;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 2000;
        param.end = 2001;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1585"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1585, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1585, 1586, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1585, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1585, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1585, true, g_rsmLabelName));

        param.start = 2000;
        param.end = 3000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2585"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2585"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1585"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexMulVersionInsertCollapse1)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
        SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_VERSION,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 500;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 500, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 500;
        param.end = 1000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 500, 1000, true, g_rsmLabelName));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1000;
        param.end = 1001;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 2000;
        param.end = 2001;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 1001, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

        param.start = 2000;
        param.end = 3000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2000"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexMulVersionInsertCollapse2)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table3.gmjson", &g_rsmLabelSchema3);
    uint32_t testNum = 17;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_CH_INSERT_BEFORE,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_CNT,
        SHM_CRASH_CH_BUCKET_ALLOC_UPDATE_LOCAL_ENTRY_CNT,
        SHM_CRASH_CH_INSERT_ALLOC_ENTRY,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_ID,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_FREE_SLOT_CNT,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_USED,
        SHM_CRASH_CH_TUPLE_ALLOC_UPDATE_BITMAP,
        SHM_CRASH_CH_INSERT_ALLOC_TUPLE_BEFORE_WRITE,
        SHM_CRASH_CH_ENTRY_INIT_HASHCODE,
        SHM_CRASH_CH_ENTRY_INIT_LOGIC_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_PHY_SLOT,
        SHM_CRASH_CH_ENTRY_INIT_VERSION,
        SHM_CRASH_CH_INSERT_WRITE_ENTRY,
        SHM_CRASH_CH_INSERT_WRITE_TUPLE,
        SHM_CRASH_CH_INSERT_FINISHED,
    };
    for (uint32_t i = 0; i < testNum; i++) {
        RsmStartServerAndGmcInitWithClusteredHash(false);
        std::cout << "BeforeReboot, num: " << i << std::endl;
        GmcConnT *conn = NULL;
        GmcStmtT *stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));

        VertexDataParamT param = {0};
        param.start = 0;
        param.end = 500;
        param.labelName = g_rsmLabelName;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 500, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 500, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);

        param.start = 500;
        param.end = 1000;
        param.isCheckVersion = true;
        param.schemaVersion = 2;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 500, 1000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 500, 1000, true, g_rsmLabelName));

        UpgradeVertexLabel(stmt, g_rsmLabelSchema3, g_rsmLabelName, GMERR_OK);
        param.start = 1000;
        param.end = 1500;
        param.schemaVersion = 3;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));

        char command[512];
        char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
        (void)snprintf(command, 512, "gmsysview -q %s", viewName);
        std::cout << "command = " << command << std::endl;

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1500"));

        DowngradeVertexLabel(stmt, g_rsmLabelName, 2, GMERR_OK);

        DbShmCrashPointSet(testCases[i]);
        // 预期崩溃恢复
        param.start = 1500;
        param.end = 1501;
        param.isCheckVersion = false;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
        // 服务端重启，warm reboot
        RsmStartServerAndGmcInitWithClusteredHash(true);
        std::cout << "RecoveryOnReboot, num: " << i << std::endl;

        conn = NULL;
        stmt = NULL;
        CreateSyncConnectionAndStmt(&conn, &stmt);

        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
        param.start = 2000;
        param.end = 2001;

        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param, GMERR_UNDEFINED_TABLE));
        UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
        RsmWaitRecoveryFinish();

        system(command);
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1500"));

        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1500, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1500, 1501, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1500, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1500, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1500, true, g_rsmLabelName));

        param.start = 2000;
        param.end = 3000;
        EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
        EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, true, g_rsmLabelName));
        EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2500"));

        ASSERT_EQ(GMERR_OK, UpdateChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPkAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhashAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashClusterAfterUpdate(stmt, 2000, 3000, true, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 2500"));

        ASSERT_EQ(GMERR_OK, DeleteChVertexData(stmt, &param));
        ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 2000, 3000, false, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1500"));

        ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
        ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    FreeJsonBuf(&g_rsmLabelSchema);
    FreeJsonBuf(&g_rsmLabelSchema2);
    FreeJsonBuf(&g_rsmLabelSchema3);
    RsmStGmcUtilSetHasUnInit(true);
}

static Status BatchInsertChVertexData(GmcStmtT *stmt, VertexDataParamT *param, Status checkRet = GMERR_OK)
{
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 1000));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI));
    GmcBatchT *batch = nullptr;
    EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, param->labelName, GMC_OPERATION_REPLACE));

    for (uint32_t i = param->start; i < param->end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 1;
        uint32_t f2 = i + 2;
        uint32_t f3 = i + 3;
        uint32_t f4 = (i + 4) % 16;
        uint32_t f5 = (i + 5) % 1024;
        uint32_t f6 = i + 6;
        uint8_t f7 = (i + 7) % 33;
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &f4, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &f5, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &f6, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7, sizeof(uint8_t)));
        EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet;
    Status ret = GmcBatchExecute(batch, &batchRet);
    if (ret != checkRet) {
        return ret != GMERR_OK ? ret : GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

TEST_F(StRsmChVertexWithIndex, TestRsmChVertexBatchInsertCollapse)
{
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);

    RsmStartServerAndGmcInitWithClusteredHash(false);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
    VertexDataParamT param = {0};
    param.start = 0;
    param.end = 1000;
    param.labelName = g_rsmLabelName;
    param.isCheckVersion = false;
    EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));
    EXPECT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
    EXPECT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

    char command[512];
    char const *viewName = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    (void)snprintf(command, 512, "gmsysview -q %s", viewName);
    std::cout << "command = " << command << std::endl;

    system(command);
    ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

    DbShmCrashPointSet(SHM_CRASH_CH_BATCH_REPLACE_WITHOUT_UPDATE_CNT);
    // 预期崩溃恢复
    param.start = 1000;
    param.end = 1002;
    EXPECT_EQ(GMERR_OK, BatchInsertChVertexData(stmt, &param, GMERR_CONNECTION_RESET_BY_PEER));

    DbShmCrashPointDetach();
    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    // 服务端重启，warm reboot
    RsmStartServerAndGmcInitWithClusteredHash(true);

    conn = NULL;
    stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
    RsmWaitRecoveryFinish();

    system(command);
    ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 1000"));

    ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 0, 1000, true, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, LookUpVertexByPk(stmt, 1000, 1002, false, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, LookUpVertexByLocal(stmt, 0, 1000, true, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, LookUpVertexByLocalhash(stmt, 0, 1000, true, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, LookUpVertexByHashCluster(stmt, 0, 1000, true, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, LookUpVertexByLpm(stmt, 0, 1000, true, g_rsmLabelName));

    ASSERT_EQ(GMERR_OK, GmcTruncateVertexLabel(stmt, g_rsmLabelName));
    ASSERT_EQ(GMERR_OK, executeCommand(command, "ENTRY_USED: 0"));

    char command2[512];
    (void)snprintf(command2, 512, "grep -rn 'GMERR-1015000, userDefItemTotalNum' ./log/  | wc -l");
    system(command2);
    ASSERT_EQ(GMERR_OK, executeCommand(command2, "0"));

    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    FreeJsonBuf(&g_rsmLabelSchema);
    RsmStGmcUtilSetHasUnInit(true);
}

static const char *g_labelSchemaVarWithId =
    R"([{
        "type":"record",
        "name":%s%s%s,
        "schema_version": 1,
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false},
                {"name":"F5", "type":"uint32", "nullable":false},
                {"name":"F6", "type":"string", "nullable":true, "size":100}
            ],
        "keys":
            [
                {"node":%s%s%s, "name":"pk", "fields":["F0"], "index":{"type":"primary"},
                 "constraints":{ "unique":true}}
            ]
        }])";
static const char *g_labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";

static Status UpdateChVertexDataWithViolation(GmcStmtT *stmt, VertexDataParamT *param, Status checkRet = GMERR_OK)
{
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, param->labelName, GMC_OPERATION_UPDATE));
    Status ret = GMERR_OK;
    for (uint32_t i = param->start; i < param->end; i++) {
        uint32_t f0 = i;
        uint32_t f1 = i + 2;
        uint32_t f2 = i + 3;
        uint32_t f3 = i + 4;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0, sizeof(f0)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2, sizeof(uint32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3, sizeof(uint32_t)));
        ret = GmcExecute(stmt);
        if (ret != checkRet) {
            return ret;
        }
    }
    return GMERR_OK;
}

TEST_F(StRsmChVertexWithIndex, DISABLED_TestRsmChVertexRollBackUseEscapeCtxCollapse)
{
    RsmStopServerAndGmcUnInit();
    // 客户端建链
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 查看日志中的error数量
    FILE *fp = popen("grep -rn '(SE-Transaction) TrxSetEscapeMemCtx' ./log/run/rgmserver/ | wc -l", "r");
    EXPECT_TRUE(fp != NULL);
    char errorNum[32] = {0};
    char *fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t beforeLogNum = atoi(errorNum);

    RsmStartServerAndGmcInit(false,
        R"(maxSeMem=512  isUseRsm=1 RsmBlockSize=2 deviceSize=2 defaultTablespaceMaxSize=2 "
        "pageSize=64 enableClusterHash=1)");
    SCOPED_TRACE("BeforeReboot");
    CreateSyncConnectionAndStmt(&conn, &stmt);

    (void)ReadJsonFile("./schema/rsm_clustered_hash_table.gmjson", &g_rsmLabelSchema);
    (void)ReadJsonFile("./schema/rsm_clustered_hash_table2.gmjson", &g_rsmLabelSchema2);
    // 客户端建表、写数据
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, g_rsmLabelSchema, g_rsmLabelConfig));
    VertexDataParamT param = {0};
    param.start = 0;
    param.end = 500;
    param.labelName = g_rsmLabelName;
    param.isCheckVersion = false;
    EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));

    UpgradeVertexLabel(stmt, g_rsmLabelSchema2, g_rsmLabelName, GMERR_OK);
    param.start = 500;
    param.end = 1000;
    param.isCheckVersion = true;
    param.schemaVersion = 2;
    EXPECT_EQ(GMERR_OK, InsertChVertexData(stmt, &param));

    // 建表占用block0
    for (int32_t i = 0; i < 1000; i++) {
        char labelName[100] = {0};
        int32_t len = sprintf_s(labelName, sizeof(labelName), "label%d", i);
        ASSERT_TRUE(len >= 0);
        char labelJson[2000];
        len = sprintf_s(
            labelJson, sizeof(labelJson), g_labelSchemaVarWithId, "\"", labelName, "\"", "\"", labelName, "\"");
        ASSERT_TRUE(len >= 0);
        uint32_t ret = GmcCreateVertexLabel(stmt, labelJson, g_labelConfig);
        if (ret != 0) {
            break;
        }
    }

    param.start = 0;
    param.end = 500;
    param.isCheckVersion = false;
    UpdateChVertexDataWithViolation(stmt, &param, GMERR_UNIQUE_VIOLATION);

    // 查看日志中的error数量
    fp = popen("grep -rn '(SE-Transaction) TrxSetEscapeMemCtx' ./log/run/rgmserver/ | wc -l", "r");
    EXPECT_TRUE(fp != NULL);

    fret = fgets(errorNum, 32, fp);
    EXPECT_TRUE(fret != NULL);
    pclose(fp);
    uint32_t afterLogNum = atoi(errorNum);
    EXPECT_EQ(afterLogNum, beforeLogNum + 1u);

    DestroyConnectionAndStmt(conn, stmt);
    RsmStopServerAndGmcUnInit();
    RsmStGmcUtilSetHasUnInit();
    conn = NULL;
    stmt = NULL;
}

#endif  // ENABLE_CRASHPOINT
