/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: testcases abort sotrage
 * Author: baiyang
 * Create: 2022-9-19
 */
#include <climits>
#include <unistd.h>
#include "gtest/gtest.h"
#include "pthread.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "gmc_tablespace.h"
#include "dm_data_basic.h"
#include "clt_conn.h"
#include "StartDbServer.h"
#include "InitClt.h"
#include "dm_data_prop.h"
#include "db_utils.h"
#include "stub.h"
#include "db_list.h"
#include "clt_stmt.h"
#include "adpt_sleep.h"
#include "storage_st_common.h"
#include "file_op.h"
#include "adpt_string.h"
#include "db_table_space.h"
#include "rsm_st_common.h"
#include "gmc_internal.h"

class StorageStRsmTableSpace : public StStorage {
protected:
    uint32_t setUpLogNum = 0;
    uint32_t tearDownLogNum = 0;
    uint32_t logNumInTest = 0;
    static void SetUpTestCase();
    static void TearDownTestCase()
    {}
    void SetUp()
    {
        ASSERT_EQ(GMERR_OK, GmcInit());
#if (defined RTOSV2 || defined RTOSV2X)
        st_check_hpe_server_running();
#else
        DbSleep(1000);
#endif
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        setUpLogNum = atoi(errorNum);
    }
    void TearDown()
    {
        FILE *fp = popen("grep -rn 'topRsmCtx check' ./log/run/rgmserver/* | wc -l", "r");
        EXPECT_TRUE(fp != NULL);
        char errorNum[32] = {0};
        char *fret = fgets(errorNum, 32, fp);
        EXPECT_TRUE(fret != NULL);
        pclose(fp);
        tearDownLogNum = atoi(errorNum);
        EXPECT_EQ(tearDownLogNum, setUpLogNum + logNumInTest);
        ShutDownDbServer();
        SaveLogAfterFailed();
        ASSERT_EQ(GMERR_OK, GmcUnInit());
    }
};

static const char *g_labelName1 = "TestLargeObject1";
static const char *g_labelName2 = "TestLargeObject2";

void StorageStRsmTableSpace::SetUpTestCase()
{}

static void CLientInit(GmcConnT **connection, GmcStmtT **stmt)
{
    CreateSyncConnectionAndStmt(connection, stmt);
}

static void CLientFinal(GmcConnT *connection, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    int32_t ret = GmcDisconnect(connection);
    EXPECT_EQ(ret, GMERR_OK);
}

static int32_t InsertVertex(GmcStmtT *stmt, uint64_t value)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

static int32_t DeleteVertex(GmcStmtT *stmt, uint64_t value)
{
    // 删除顶点
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "T35_K0"));
    int32_t ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    return GmcExecute(stmt);
}

TEST_F(StorageStRsmTableSpace, rsmTableSpaceCreateAndDrop)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd", "is_support_reserved_memory":1})";
    string test_schema = GetFileContext("./schema/tableSpace1.gmjson");
    auto ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId);
    ASSERT_EQ(GMERR_OK, ret);

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);  // 预期创建rsm的tableSpace成功

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd");
    ASSERT_EQ(GMERR_OK, ret);

    // 预期共享内存rsm_tablespace创建成功
    ret = GmcDropTablespace(stmt, "tablespace1");
    ASSERT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
}

TEST_F(StorageStRsmTableSpace, rsmTableSpaceInsertUntilOutOfTspMem)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd", "is_support_reserved_memory":1})";
    string test_schema = GetFileContext("./schema/tableSpace1.gmjson");
    auto ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
    printf("begin tsp1 Insert\n");
    int32_t recNum = 0;
    for (;; recNum++) {
        ret = InsertVertex(stmt, recNum);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            break;
        }
    }

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd");
    ASSERT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
}

// 重复创建tsp,插满数据，再droptsp，测试能否正常回收
TEST_F(StorageStRsmTableSpace, rsmTableSpaceCreateTspRepeat)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\" \"RsmKeyRange=0,2\" "
                            "\"defaultTablespaceMaxSize=132\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId = R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd",
        "tablespace_name":"index", "is_support_reserved_memory":1})";
    string test_schema = GetFileContext("./schema/tableSpace1.gmjson");
    uint32_t recNum0 = 0;
    int32_t ret = GMERR_OK;
    for (uint32_t i = 0; i < 5; i++) {
        ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId);
        ASSERT_EQ(GMERR_OK, ret);

        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
        printf("begin tsp1 Insert\n");
        uint32_t recNum1 = 0;
        for (;; recNum1++) {
            ret = InsertVertex(stmt, recNum1);
            if (ret != GMERR_OK) {
                EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
                break;
            }
        }
        if (i == 0) {
            recNum0 = recNum1;
        } else {
            EXPECT_EQ(recNum0, recNum1);
        }

        ret = GmcDropVertexLabel(stmt, g_labelName1);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcDropTablespace(stmt, "abcd");
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcDropTablespace(stmt, "index");
        ASSERT_EQ(GMERR_OK, ret);
    }

    CLientFinal(connection, stmt);
}

// 测试多个tsp的使用，每个tsp跨rsmBlock进行使用
TEST_F(StorageStRsmTableSpace, rsmTableSpaceInsertTsps)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\" \"RsmKeyRange=0,3\" "
                            "\"defaultTablespaceMaxSize=132\"");
    // 需要注意RsmKeyRange=0,3，是要预留一个给控制区的，因此只有3*128M的空间给存储，每个tsp 132M，因此最多创建2个
    // 创建tsp1时，从首块rsmBlock分配了第0号device给tsp1，创建tsp2时，从首块rsmBlock分配了第1号device给tsp2
    // 然后再执行插入操作占满tsp，所以tsp1占用了第0号device，2-33号device，tsp2占用了第1号device，34-65号device
    // todo baiyang 回头视图补充相关能力验证tsp占用device的情况
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId1 =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd", "is_support_reserved_memory":1})";
    string test_schema = GetFileContext("./schema/tableSpace1.gmjson");
    auto ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId1);
    ASSERT_EQ(GMERR_OK, ret);

    const char *labelConfigId2 =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd2", "is_support_reserved_memory":1})";
    test_schema = GetFileContext("./schema/tableSpace2.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId2);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
    printf("begin tsp1 Insert\n");
    int32_t recNum1 = 0;
    for (;; recNum1++) {
        ret = InsertVertex(stmt, recNum1);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            break;
        }
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_INSERT));
    printf("begin tsp2 Insert\n");
    int32_t recNum2 = 0;
    for (;; recNum2++) {
        ret = InsertVertex(stmt, recNum2);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            break;
        }
    }
    ASSERT_EQ(recNum1, recNum2);

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd2");
    ASSERT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
}

TEST_F(StorageStRsmTableSpace, rsmTableSpaceInsertAndDelete)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"");
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd", "is_support_reserved_memory":1})";
    string test_schema = GetFileContext("./schema/tableSpace1.gmjson");
    auto ret = GmcCreateVertexLabel(stmt, test_schema.c_str(), labelConfigId);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
    for (int i = 0; i < 10; i++) {
        ret = InsertVertex(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_DELETE));
    for (int i = 0; i < 10; i++) {
        ret = DeleteVertex(stmt, i);
        EXPECT_EQ(GMERR_OK, ret);
    }

    system("gmsysview -q V\\$STORAGE_HEAP_VERTEX_LABEL_STAT");
    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd");
    ASSERT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
}

TEST_F(StorageStRsmTableSpace, CreateTableSpaceWhenWarmReboot)
{
    GmcTspCfgT tspCfg = {
        .tablespaceName = "tsp",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 正常拉起
    RsmStartServerAndInit(false);
    {
        SCOPED_TRACE("BeforeReboot");
        CreateSyncConnectionAndStmt(&conn, &stmt);

        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

        DestroyConnectionAndStmt(conn, stmt);
    }

    // 服务端重启，warm reboot
    RsmStartServerAndInit(true);
    {
        SCOPED_TRACE("RecoveryOnReboot");
        CreateSyncConnectionAndStmt(&conn, &stmt);

        // 重新创建tableSpace
        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));
        RsmWaitRecoveryFinish();

        // 可成功，表示可查找到且已全部恢复
        ASSERT_EQ(GMERR_OK, GmcDropTablespace(stmt, tspCfg.tablespaceName));

        DestroyConnectionAndStmt(conn, stmt);
    }
}

void RsmImportAllowList(void)
{
    ASSERT_EQ(GMERR_OK, system("gmrule -c import_allowlist -f ./base/base_conf/base.gmuser"));
    ASSERT_EQ(GMERR_OK, system("gmrule -c import_policy -f ./base/base_sys_priv/base_sys_priv.gmpolicy"));
}

TEST_F(StorageStRsmTableSpace, CreateTableSpaceWhenWarmRebootWithPriv)
{
    const char *serverConfig = "userPolicyMode=2 maxSeMem=512  isUseRsm=1";

    GmcTspCfgT tspCfg = {
        .tablespaceName = "tsp",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 正常拉起
    RsmStartServerAndInit(false, serverConfig);
    {
        SCOPED_TRACE("BeforeReboot");
        RsmImportAllowList();
        CreateSyncConnectionAndStmt(&conn, &stmt);

        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

        DestroyConnectionAndStmt(conn, stmt);
    }

    // 服务端重启，warm reboot
    RsmStartServerAndInit(true, serverConfig);
    {
        SCOPED_TRACE("RecoveryOnReboot");
        RsmImportAllowList();
        CreateSyncConnectionAndStmt(&conn, &stmt);

        // 重新创建tableSpace
        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));
        RsmWaitRecoveryFinish();

        // 可成功，表示可查找到且已全部恢复
        ASSERT_EQ(GMERR_OK, GmcDropTablespace(stmt, tspCfg.tablespaceName));

        DestroyConnectionAndStmt(conn, stmt);
    }
}

// 表tablespace不能指定保留内存tsp
TEST_F(StorageStRsmTableSpace, CreateTableUseRsmTspWrong1WhenNeedShmTsp)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建rsm_tablespace的保留内存tsp
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsm_tablespace",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "tablespace":"rsm_tablespace",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    // 使用rsm_tablespace作为保留内存tsp，报错
    ASSERT_NE(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// 表tablespace_name不能指定保留内存tsp
TEST_F(StorageStRsmTableSpace, CreateTableUseRsmTspWrong2WhenNeedShmTsp)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建rsm_tablespace的保留内存tsp
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsm_tablespace",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "tablespace_name":"rsm_tablespace"
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    // 使用rsm_tablespace作为保留内存tsp，报错
    ASSERT_NE(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// 表配置tablespace_name不能指定保留内存tsp
TEST_F(StorageStRsmTableSpace, CreateTableUseRsmTspWrong3WhenNeedShmTsp)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建rsm_tablespace的保留内存tsp
    GmcTspCfgT tspCfg = {
        .tablespaceName = "rsm_tablespace",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    const char *labelConfig =
        R"({"max_record_count":1000000, "tablespace_name":"rsm_tablespace", "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    // 使用rsm_tablespace作为保留内存tsp，报错
    ASSERT_NE(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// 表rsm_tablespace_name不能指定共享内存tsp
TEST_F(StorageStRsmTableSpace, CreateTableUseShmTspWrong1WhenNeedRsmTsp)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建shm_tablespace的共享内存tsp
    GmcTspCfgT tspCfg = {
        .tablespaceName = "shm_tablespace",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateTablespace(stmt, &tspCfg));

    const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "rsm_tablespace_name":"shm_tablespace"
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    // 使用shm_tablespace作为保留内存tsp，报错
    ASSERT_NE(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// 表配置rsm_tablespace_name不能指定共享内存tsp
TEST_F(StorageStRsmTableSpace, CreateTableUseShmTspWrong2WhenNeedRsmTsp)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建shm_tablespace的共享内存tsp
    GmcTspCfgT tspCfg = {
        .tablespaceName = "shm_tablespace",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_EQ(GMERR_OK, GmcCreateTablespace(stmt, &tspCfg));

    const char *labelConfig =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"shm_tablespace", "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    // 使用shm_tablespace作为保留内存tsp，报错
    ASSERT_NE(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    DestroyConnectionAndStmt(conn, stmt);
}

// 创建保留内存默认tsp成共享内存
TEST_F(StorageStRsmTableSpace, CreateRsmPublicTspButShm)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = "reservedMemPublic",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_NE(GMERR_OK, GmcCreateTablespace(stmt, &tspCfg));

    DestroyConnectionAndStmt(conn, stmt);
}

// 创建兼容tsp成保留内存
TEST_F(StorageStRsmTableSpace, CreateCompatibleTspButRsm)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcTspCfgT tspCfg = {
        .tablespaceName = "compatible",
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };
    ASSERT_NE(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    DestroyConnectionAndStmt(conn, stmt);
}

// 看护tsp上限
TEST_F(StorageStRsmTableSpace, CreateTspNum)
{
    // 正常拉起
    const char *serverConfig = "defaultTablespaceMaxSize=1 deviceSize=1 "
                               "maxSeMem=512  isUseRsm=1";
    RsmStartServerAndInit(false, serverConfig);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char tspName[128] = {0};
    GmcTspCfgT tspCfg = {
        .tablespaceName = tspName,
        .initSize = 0,
        .stepSize = 0,
        .maxSize = 0,
    };

    uint32_t shmTspNum = DB_USER_TABLE_SPACE_NUM;
    for (uint32_t i = 0; i < shmTspNum; i++) {
        (void)sprintf_s(tspName, sizeof(tspName), "shm_tsp%u", i);
        ASSERT_EQ(GMERR_OK, GmcCreateTablespace(stmt, &tspCfg));
    }
    (void)sprintf_s(tspName, sizeof(tspName), "shm_tsp");
    ASSERT_NE(GMERR_OK, GmcCreateTablespace(stmt, &tspCfg));

    uint32_t rsmTspNum = RSMEM_TABLESPACE_ARRAY_NUM;
    for (uint32_t i = 0; i < rsmTspNum; i++) {
        (void)sprintf_s(tspName, sizeof(tspName), "rsm_tsp%u", i);
        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));
    }
    (void)sprintf_s(tspName, sizeof(tspName), "rsm_tsp");
    ASSERT_NE(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg));

    DestroyConnectionAndStmt(conn, stmt);
}

// 表配置未配置tablespace_name(rsm_tablespace_name)时表tablespace_name(rsm_tablespace_name)可生效
TEST_F(StorageStRsmTableSpace, CreateTableWithTablespaceConfig1)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelConfig = R"({"max_record_count":1000000, "is_support_reserved_memory":1})";
    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "tablespace_name":"shm_tablespace",
            "rsm_tablespace_name":"rsm_tablespace"
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, labelConfig));

    // tablespace均创建出来
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'shm_tablespace\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, isRsmSpace);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "TABLESPACE_NAME=\'rsm_tablespace\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);

    DestroyConnectionAndStmt(conn, stmt);
}

// 无表配置时表tablespace_name(rsm_tablespace_name)可生效
TEST_F(StorageStRsmTableSpace, CreateTableWithTablespaceConfig2)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "tablespace_name":"shm_tablespace",
            "rsm_tablespace_name":"rsm_tablespace",
            "is_support_reserved_memory":true
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, NULL));

    // tablespace均创建出来
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'shm_tablespace\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, isRsmSpace);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "TABLESPACE_NAME=\'rsm_tablespace\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)1, isRsmSpace);

    DestroyConnectionAndStmt(conn, stmt);
}

// 无表配置时表space_id可生效
TEST_F(StorageStRsmTableSpace, CreateTableWithTablespaceConfig3)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "space_id":1
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, NULL));

    // tablespace均创建出来
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'compatible\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, isRsmSpace);

    DestroyConnectionAndStmt(conn, stmt);
}

// 无表配置且表无config,有tablespace时预期不是public
TEST_F(StorageStRsmTableSpace, CreateTableWithTablespaceConfig4)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "tablespace":"rsm_tablespace",
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, NULL));

    // tablespace创建出来
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'rsm_tablespace\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "IS_RSM_SPACE", filter, cmdOutput, 64);
    uint32_t isRsmSpace = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)0, isRsmSpace);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StorageStRsmTableSpace, DropTspWrongWhenTableNotClean)
{
    // 正常拉起
    RsmStartServerAndInit(false);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelSchema =
        R"([{
        "type":"record",
        "name":"rsm_table",
        "config":{
            "tablespace_name":"shm_tablespace",
            "rsm_tablespace_name":"rsm_tablespace",
            "is_support_reserved_memory":true
        },
        "fields":
            [
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"rsm_table",
                    "name":"pk",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelSchema, NULL));

    // 未清空table，不能drop tsp
    ASSERT_NE(GMERR_OK, GmcDropTablespace(stmt, "shm_tablespace"));
    ASSERT_NE(GMERR_OK, GmcDropTablespace(stmt, "rsm_tablespace"));

    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, "rsm_table"));

    // 已清空table，可drop tsp
    ASSERT_EQ(GMERR_OK, GmcDropTablespace(stmt, "shm_tablespace"));
    ASSERT_EQ(GMERR_OK, GmcDropTablespace(stmt, "rsm_tablespace"));

    DestroyConnectionAndStmt(conn, stmt);
}

// 测试重启后，重建rsmTableSpace
TEST_F(StorageStRsmTableSpace, rsmTableSpaceRecovery)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\" \"RsmKeyRange=0,3\" "
                            "\"defaultTablespaceMaxSize=132\"");
    // 需要注意RsmKeyRange=0,3，是要预留一个给控制区的，因此只有3*128M的空间给存储，每个tsp 132M，因此最多创建2个
    // 创建tsp1时，从首块rsmBlock分配了第0号device给tsp1，创建tsp2时，从首块rsmBlock分配了第1号device给tsp2
    // 然后再执行插入操作占满tsp，所以tsp1占用了第0号device，2-33号device，tsp2占用了第1号device，34-65号device
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    const char *labelConfigId1 =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd", "is_support_reserved_memory":1})";
    string test_schema1 = GetFileContext("./schema/tableSpace1.gmjson");
    auto ret = GmcCreateVertexLabel(stmt, test_schema1.c_str(), labelConfigId1);
    ASSERT_EQ(GMERR_OK, ret);

    const char *labelConfigId2 =
        R"({"max_record_count":1000000, "rsm_tablespace_name":"abcd2", "is_support_reserved_memory":1})";
    string test_schema2 = GetFileContext("./schema/tableSpace2.gmjson");
    ret = GmcCreateVertexLabel(stmt, test_schema2.c_str(), labelConfigId2);
    ASSERT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName1, GMC_OPERATION_INSERT));
    printf("begin tsp1 Insert\n");
    int32_t recNum1 = 0;
    for (;; recNum1++) {
        ret = InsertVertex(stmt, recNum1);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            break;
        }
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_labelName2, GMC_OPERATION_INSERT));
    printf("begin tsp2 Insert\n");
    int32_t recNum2 = 0;
    for (;; recNum2++) {
        ret = InsertVertex(stmt, recNum2);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
            break;
        }
    }
    ASSERT_EQ(recNum1, recNum2);

    system("gmsysview -q V\\$CATA_TABLESPACE_INFO");

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "USED_RATIO", filter, cmdOutput, 64);
    uint32_t usedRatio = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)100, usedRatio);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "TABLESPACE_NAME=\'abcd2\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "USED_RATIO", filter, cmdOutput, 64);
    usedRatio = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)100, usedRatio);
    CLientFinal(connection, stmt);
    RsmStopServerAndGmcUnInit();

    connection = NULL;
    stmt = NULL;
    RsmStartServerAndGmcInit(true, "\"maxSeMem=512\"  \"isUseRsm=1\" \"RsmKeyRange=0,3\" "
                                   "\"defaultTablespaceMaxSize=132\"");
    SCOPED_TRACE("RecoveryOnReboot");
    CreateSyncConnectionAndStmt(&connection, &stmt);
    // 客户端建表
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_schema1.c_str(), labelConfigId1));
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_schema2.c_str(), labelConfigId2));
    RsmWaitRecoveryFinish();

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "TABLESPACE_NAME=\'abcd\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "USED_RATIO", filter, cmdOutput, 64);
    usedRatio = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)100, usedRatio);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "TABLESPACE_NAME=\'abcd2\'";
    GetViewFieldResultFilter("V\\$CATA_TABLESPACE_INFO", "USED_RATIO", filter, cmdOutput, 64);
    usedRatio = atoi(cmdOutput);
    EXPECT_EQ((uint32_t)100, usedRatio);

    ret = GmcDropVertexLabel(stmt, g_labelName1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_labelName2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropTablespace(stmt, "abcd2");
    ASSERT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
}

#ifdef ENABLE_CRASHPOINT
// 测试rsm-tsp创建流程中退出，保留内存能否恢复
TEST_F(StorageStRsmTableSpace, rsmTableSpaceCreateCrash)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"");
    uint32_t testNum = 5;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_RSM_TSP_INFO_LOCK,
        SHM_CRASH_RSM_TSP_INFO_ALLOC_MEM,
        SHM_CRASH_RSM_TSP_INFO_INSERT_BEFORE,
        SHM_CRASH_RSM_TSP_INFO_INSERT_AFTER,
        SHM_CRASH_RSM_TSP_INFO_INSERT_FINISH,
    };
    // 客户端建链
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    for (uint32_t i = 0; i < testNum; i++) {
        if (i != 0) {
            RsmStartServerAndGmcInit(false);
        }
        SCOPED_TRACE("BeforeReboot");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
        CreateSyncConnectionAndStmt(&conn, &stmt);
        DbShmCrashPointSet(testCases[i]);
        // 建tsp
        GmcTspCfgT tspCfg1 = {.tablespaceName = "rsm-tablespace-1", .initSize = 4, .stepSize = 4, .maxSize = 32};
        ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, GmcCreateRsmTablespace(stmt, &tspCfg1));
        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();

        // 服务端重启，warm reboot
        conn = NULL;
        stmt = NULL;
        RsmStartServerAndGmcInit(true);
        SCOPED_TRACE("RecoveryOnReboot");
        CreateSyncConnectionAndStmt(&conn, &stmt);
        if (testCases[i] == SHM_CRASH_RSM_TSP_INFO_INSERT_FINISH) {
            // 如果是恢复之前的tsp，重启恢复后得重新创建，恢复阶段才能结束
            tspCfg1.maxSize = 48;
            int ret = GmcCreateRsmTablespace(stmt, &tspCfg1);
            ASSERT_EQ(GMERR_OK, ret);
            RsmWaitRecoveryFinish();
        } else {
            // 恢复阶段完成后才能创建新的tsp
            RsmWaitRecoveryFinish();
            tspCfg1.maxSize = 48;
            int ret = GmcCreateRsmTablespace(stmt, &tspCfg1);
            ASSERT_EQ(GMERR_OK, ret);
        }

        char cmdOutput1[64] = {0};
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        GetViewFieldResult("V\\$CATA_TABLESPACE_INFO", "TABLESPACE_NAME", cmdOutput1, 64);
        cmdOutput1[strlen(cmdOutput1) - 1] = '\0';
        EXPECT_STREQ(cmdOutput1, " rsm-tablespace-1");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        GetViewFieldResult("V\\$CATA_TABLESPACE_INFO", "MAX_SIZE", cmdOutput1, 64);
        cmdOutput1[strlen(cmdOutput1) - 1] = '\0';
        if (testCases[i] == SHM_CRASH_RSM_TSP_INFO_INSERT_FINISH) {
            EXPECT_STREQ(cmdOutput1, " [32] MB");  // 如果是恢复之前的tsp，配置修改不会生效
        } else {
            EXPECT_STREQ(cmdOutput1, " [48] MB");
        }

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    conn = NULL;
    stmt = NULL;
    /*
        RsmUndo只回收tsp的槽位，不回收内存
        SHM_CRASH_RSM_TSP_INFO_LOCK, ---预期不会发生对账回收
        ---内存申请
        SHM_CRASH_RSM_TSP_INFO_ALLOC_MEM, ---预期会发生3次对账回收（RsmemTableSpaceEntryT、tspNamePtr、creatorPtr）
        SHM_CRASH_RSM_TSP_INFO_INSERT_BEFORE, ---预期会发生3次对账回收（RsmemTableSpaceEntryT、tspNamePtr、creatorPtr）
        SHM_CRASH_RSM_TSP_INFO_INSERT_AFTER, ---预期会发生3次对账回收（RsmemTableSpaceEntryT、tspNamePtr、creatorPtr）
        ---rsmUndo回退
        SHM_CRASH_RSM_TSP_INFO_INSERT_FINISH, ---预期不会发生对账回收
        总计9次对账回收
    */
    logNumInTest = 9;
}

// 测试rsm-tsp删除流程中退出，保留内存能否恢复
TEST_F(StorageStRsmTableSpace, rsmTableSpaceDropCrash)
{
    StartDbServerWithConfig("\"maxSeMem=512\"  \"isUseRsm=1\"");
    uint32_t testNum = 4;
    DbShmCrashPointTypeT testCases[testNum] = {
        SHM_CRASH_RSM_TSP_INFO_FREE_MEM,
        SHM_CRASH_RSM_TSP_INFO_REMOVE_BEFORE,
        SHM_CRASH_RSM_TSP_INFO_REMOVE_AFTER,
        SHM_CRASH_RSM_TSP_INFO_REMOVE_FINISH,
    };
    // 客户端建链
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    for (uint32_t i = 0; i < testNum; i++) {
        if (i != 0) {
            RsmStartServerAndGmcInit(false);
        }
        SCOPED_TRACE("BeforeReboot");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
        CreateSyncConnectionAndStmt(&conn, &stmt);

        // 建tsp
        GmcTspCfgT tspCfg1 = {.tablespaceName = "rsm-tablespace-1", .initSize = 4, .stepSize = 4, .maxSize = 32};
        ASSERT_EQ(GMERR_OK, GmcCreateRsmTablespace(stmt, &tspCfg1));
        DbShmCrashPointSet(testCases[i]);
        ASSERT_EQ(GMERR_CONNECTION_RESET_BY_PEER, GmcDropTablespace(stmt, "rsm-tablespace-1"));
        DbShmCrashPointDetach();
        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();

        // 服务端重启，warm reboot
        conn = NULL;
        stmt = NULL;
        RsmStartServerAndGmcInit(true);
        SCOPED_TRACE("RecoveryOnReboot");
        CreateSyncConnectionAndStmt(&conn, &stmt);
        RsmWaitRecoveryFinish();

        tspCfg1.maxSize = 48;  // 恢复阶段完成后才能创建新的tsp
        int ret = GmcCreateRsmTablespace(stmt, &tspCfg1);
        ASSERT_EQ(GMERR_OK, ret);
        char cmdOutput1[64] = {0};
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        GetViewFieldResult("V\\$CATA_TABLESPACE_INFO", "TABLESPACE_NAME", cmdOutput1, 64);
        cmdOutput1[strlen(cmdOutput1) - 1] = '\0';
        EXPECT_STREQ(cmdOutput1, " rsm-tablespace-1");
        system("gmsysview -q V\\$CATA_TABLESPACE_INFO");
        (void)memset_s(cmdOutput1, sizeof(cmdOutput1), 0, sizeof(cmdOutput1));
        GetViewFieldResult("V\\$CATA_TABLESPACE_INFO", "MAX_SIZE", cmdOutput1, 64);
        cmdOutput1[strlen(cmdOutput1) - 1] = '\0';
        EXPECT_STREQ(cmdOutput1, " [48] MB");

        DestroyConnectionAndStmt(conn, stmt);
        RsmStopServerAndGmcUnInit();
    }
    conn = NULL;
    stmt = NULL;
    /*
        RsmUndo只回收tsp的槽位，不回收内存
        ---记录rsmUndo
        SHM_CRASH_RSM_TSP_INFO_FREE_MEM, ---预期会发生3次对账回收（RsmemTableSpaceEntryT、tspNamePtr、creatorPtr）
        SHM_CRASH_RSM_TSP_INFO_REMOVE_BEFORE, ---预期不会发生对账回收
        SHM_CRASH_RSM_TSP_INFO_REMOVE_AFTER, ---预期不会发生对账回收
        ---rsmUndo回退
        SHM_CRASH_RSM_TSP_INFO_REMOVE_FINISH, ---预期不会发生对账回收
        总计3次对账回收
    */
    logNumInTest = 3;
}

#endif
