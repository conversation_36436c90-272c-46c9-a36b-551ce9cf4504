/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: create table on demand test
 * Create: 2024/3/9
 */
#ifndef EXPERIMENTAL_GUANGQI
#include <sys/epoll.h>
#include <thread>
#include <stdio.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include "client_common_st.h"
#include "st_common.h"
#include "tools_st_common.h"

class ClientStCreateTableFromShmem : public testing::Test {
protected:
    static void SetUpTestCase(){};
    static void TearDownTestCase(){};
    virtual void SetUp()
    {
        printf("testCase begin \n");
    }
    virtual void TearDown()
    {
        printf("testCase end \n");
    }
};

#define JSON_KEY 123123
#define POLICY_KEY 123126

void LoadFileToShmem(char *jsonFile, char *policyFile)
{
    uint32_t gmjsonKey = JSON_KEY;
    uint32_t gmpolicyKey = POLICY_KEY;
    FILE *gmjsonFileFd = fopen(jsonFile, "r");
    FILE *gmPolicyFileFd = fopen(policyFile, "r");
    EXPECT_EQ(true, gmjsonFileFd != NULL);
    EXPECT_EQ(true, gmPolicyFileFd != NULL);
    fseek(gmjsonFileFd, 0, SEEK_END);
    long json_file_size = ftell(gmjsonFileFd);
    rewind(gmjsonFileFd);

    fseek(gmPolicyFileFd, 0, SEEK_END);
    long policy_file_size = ftell(gmPolicyFileFd);
    rewind(gmPolicyFileFd);

    // 创建共享内存
    int32_t gmjsonShmId = shmget(gmjsonKey, json_file_size + 1024, IPC_CREAT | 0666);
    int32_t gmpolicyShmId = shmget(gmpolicyKey, policy_file_size + 1024, IPC_CREAT | 0666);
    EXPECT_EQ(true, gmjsonShmId != -1);
    EXPECT_EQ(true, gmpolicyShmId != -1);
    void *gmjsonAddr = shmat(gmjsonShmId, NULL, 0);
    void *gmpolicyAddr = shmat(gmpolicyShmId, NULL, 0);
    EXPECT_EQ(true, gmjsonAddr != NULL);
    EXPECT_EQ(true, gmpolicyAddr != NULL);

    int32_t jsonRet = fread(gmjsonAddr, json_file_size, 1, gmjsonFileFd);
    int32_t policyRet = fread(gmpolicyAddr, policy_file_size, 1, gmPolicyFileFd);
    EXPECT_EQ(true, jsonRet == 1);
    EXPECT_EQ(true, policyRet == 1);
    // 创建path：/run/verona/schema_ready
    system("touch /run/verona/schema_ready");
    shmdt(gmjsonAddr);
    shmdt(gmpolicyAddr);
    fclose(gmjsonFileFd);
    fclose(gmPolicyFileFd);
}

void FreeSource(void)
{
    system("rm -f /run/verona/schema_ready");
    int32_t gmjsonShmId = shmget(JSON_KEY, 0, 0);
    int32_t gmpolicyShmId = shmget(POLICY_KEY, 0, 0);
    shmctl(gmjsonShmId, IPC_RMID, 0);
    shmctl(gmpolicyShmId, IPC_RMID, 0);
}

static void WaitDbServerStart(uint32_t timeout = 1)
{
    GmcConnT *conn = NULL;
    GmcConnOptionsT *connOptions;
    Status ret = GmcConnOptionsCreate(&connOptions);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetServerLocator(connOptions, "usocket:/run/verona/unix_emserver");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);

    uint64_t timeMs = 0;
    uint64_t timeoutMs = timeout * 100;
    for (;;) {
        DbUsleep(1000);
        timeMs++;
        ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, &conn);
        if (ret == GMERR_OK || timeMs > timeoutMs) {
            break;
        }
    }

    if (conn != NULL) {
        ret = GmcDisconnect(conn);
        ASSERT_EQ(GMERR_OK, ret);
    }
}

static const int BUF_MAX_SIZE = 1024;
static char g_cmd[BUF_MAX_SIZE];

// 门槛用例，预期vl、kv表创建成功，配置文件生效，权限导入成功。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// 门槛用例，预期vl、kv表创建成功，配置文件生效，权限导入成功。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_001)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    LoadFileToShmem(jsonFile, policyFile);
    st_clt_init();
    WaitDbServerStart();
    // DT覆盖率场景，用例执行慢，后续用例执行的时候，对象权限有可能还没导入完毕，因此增加sleep。
    sleep(1);
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// 1、配置项schemaInfoShmemKey两个key相同，预期db拉起失败
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_key_inv_01)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123123\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    Status ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);
    FreeSource();
    ShutDownDbServer();
}

// 2、配置项schemaInfoShmemKey key不在合法范围，预期db拉起失败
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_key_inv_02)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=-1,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    Status ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);
    FreeSource();
    ShutDownDbServer();
}

// 3、配置项schemaInfoShmemKey key不在合法范围，预期db拉起失败
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_key_inv_03)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=1,4294967296\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    Status ret = system("gmsysview -q V\\$DRT_CONN_STAT");
    EXPECT_NE(GMERR_OK, ret);
    FreeSource();
    ShutDownDbServer();
}

// gmjson、gmconfig、gmpolicy均包含空元素，预期不影响导入。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_01)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all01.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all01.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// gmpolicy为空文件（共享内存配置了key但是没有内容），预期不影响导入。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_02)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all02.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all02.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123127\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"simpleLabel"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);

    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"kvtable"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// gmpolicy为空文件，预期不影响导入。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_02_01)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all03.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all03.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"simpleLabel"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);

    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"kvtable"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// gmpolicy、gmjson为空文件，预期不影响起服务。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_03)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    system("ipcs");
    char jsonFile[] = "./012_schema_loader/shmem_info/all04.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all04.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = system("gmsysview -q V\\$CATA_KV_TABLE_INFO");
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// 预期vl、kv表创建成功(使用table_name)，配置文件生效，权限导入成功。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_05)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all05.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all05.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=0\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);
    uint32_t index = 0;
    while (true) {
        Status ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f0Value = index;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f1Value = index + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2Value = index + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f3Value = index + 3;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &f3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        index++;
        if (index > 10) {
            break;
        }
    }

    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    DestroyConnectionAndStmt(syncConn, stmt);
    st_clt_uninit();
    ShutDownDbServer();
}

// gmjson、gomconfig数量不一致，啥也不导入。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_06)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all06.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all06.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_NE(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_NE(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_NE(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// vltable_name 长度校验，超过长度512, 预期导入失败。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_07)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all07.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all07.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限, 表名长度512导入成功
    char vertexLabelName[] =
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfs512";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep %s -A 5",
        vertexLabelName);
    const char *matchVertex[] = {
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfs512"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 校验simpleLabel(vertex label)权限, 表名长度513导入失败
    char vl01[] =
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfss513";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO | grep %s -A 5", vl01);
    const char *match01[] = {
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfss513"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, match01, 1);
    EXPECT_NE(GMERR_OK, ret);
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName01[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName01);
    const char *matchVertex01[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex01, 2);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// kvtable_name校验，超过长度512, 预期导入失败。
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_08)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all08.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all08.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 表名长度512导入成功
    char vertexLabelName[] =
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfs512";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(
        g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO | grep %s -A 5", vertexLabelName);
    const char *matchVertex[] = {
        "adsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfs512"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 表名长度513导入失败
    char vertexLabelName01[] =
        "bdsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfss513";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(
        g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO | grep %s -A 5", vertexLabelName01);
    const char *matchVertex01[] = {
        "bdsfaadsfdfsafdasfasdfdasfadsfasdfadfsafwerqqwerqteqtggqwrqwefdsgfasrwerwafdasdgrwerqwfdsadfasdfadsfasdfasfdas"
        "dfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfa"
        "sdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfas"
        "fdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafad"
        "sdfasdfasdffasdfasfdasdfafdsafadsdfasdfasdffasdfasfdasdfafdsafadsdfss513"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex01, 1);
    EXPECT_NE(GMERR_OK, ret);
    // 正常表可以导入成功
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_NE(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_NE(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// gmpolicy有重复权限，预期vl、kv表创建成功，配置文件生效，权限导入成功。
TEST_F(ClientStCreateTableFromShmem, import_schema_dumpli_policy_mod_3)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all09.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all09.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    system("ipcs");
    st_clt_uninit();
    ShutDownDbServer();
}

// 服务端导表，存在相同对象名字
TEST_F(ClientStCreateTableFromShmem, import_schema_dumpli_policy_mod_1)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=1\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/shmem_info/mod_1;./"
                            "012_schema_loader/shmem_info/mod_1;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=0\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "V5test106";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    char vertexLabelName01[] = "V5test107";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName01);
    const char *match001[] = {"PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, match001, 1);
    EXPECT_EQ(GMERR_OK, ret);
    st_clt_uninit();
    ShutDownDbServer();
}

// 不写入数据和ready文件，配置项importSchemaFromShmWaitTime改为120，等待时间结束后预期起服务失败，校验报错日志
// DISABLED: 该用例受到问题单DTS2025020620040影响，需等问题修复后该用例重新上架
TEST_F(ClientStCreateTableFromShmem, DISABLED_import_schema_from_shmem_09)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    system("ipcs");
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=2\" \"importSchemaFromShmWaitTime=120\" ",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$DB_SERVER");
    const char *matchConfig[] = {"sysview connect unsucc"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    const char *logPath = "log/run/rgmserver/rgmserver.log";
    const char *matchLog1 = {"schema_ready still doesn't exist, DB has been waiting for 60s, remaining wait time: 60s"};
    const char *matchLog2 = {"schema_ready still doesn't exist, DB has been waiting for 120s, remaining wait time: 0s"};
    const char *matchLog3 = {"Wait time exceeds the limit: 120s, schema_ready doesn't exist"};
    StCommonVerifyLogContent(logPath, matchLog1, 1);
    StCommonVerifyLogContent(logPath, matchLog2, 1);
    StCommonVerifyLogContent(logPath, matchLog3, 1);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// 写入数据和ready文件，配置项importSchemaFromShmWaitTime改为65，在等待时间内导入，预期导入成功
// gmadmin查询&修改配置项importSchemaFromShmWaitTime
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_10)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=0\" \"importSchemaFromShmWaitTime=65\" ",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // gmadmin 修改配置项importSchemaFromShmWaitTime为120，预期修改失败
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmadmin -cfgName importSchemaFromShmWaitTime -cfgVal 120");
    const char *matchConfig1[] = {"set config"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchConfig1, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // gmadmin 查询配置项importSchemaFromShmWaitTime
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmadmin -cfgName importSchemaFromShmWaitTime");
    const char *matchConfig2[] = {"config current value: 65"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchConfig2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验simpleLabel(vertex label)权限
    char vertexLabelName[] = "simpleLabel";
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_VERTEX_LABEL_INFO -f VERTEX_LABEL_NAME=%s",
        vertexLabelName);
    const char *matchVertex[] = {"PRIVILEGES: SELECT", "PRIVILEGES: SELECT,DELETE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchVertex, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验权限
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmsysview -q V\\$CATA_KV_TABLE_INFO");
    const char *matchKv[] = {"PRIVILEGES: SELECT,DELETE,REPLACE", "PRIVILEGES: SELECT,DELETE,REPLACE"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKv, 2);
    EXPECT_EQ(GMERR_OK, ret);
    // 校验配置项
    const char *matchKvConfig[] = {"\"max_record_count\": 2001"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchKvConfig, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

// 写入数据和ready文件，不配置项importSchemaFromShmWaitTime，gmadmin查询配置项
TEST_F(ClientStCreateTableFromShmem, import_schema_from_shmem_11)
{
    ShutDownDbServer();
    system("rm -rf ./log");
    char jsonFile[] = "./012_schema_loader/shmem_info/all.gmjson";
    char policyFile[] = "./012_schema_loader/shmem_info/all.gmpolicy";
    LoadFileToShmem(jsonFile, policyFile);
    StartDbServerWithConfig("\"schemaInfoShmemKey=123123,123126\" \"schemaLoader=3\" "
                            "\"schemaPath=./012_schema_loader/shmem_info/gmuser;./012_schema_loader/shmem_info/"
                            "sys_priv;./012_schema_loader/etc/gmjson;./"
                            "012_schema_loader/etc/gmpolicy/yunshan.gmpolicies;\" \"isFastReadUncommitted=1\" "
                            "\"userPolicyMode=0\"",
        false, false);
    st_clt_init();
    WaitDbServerStart();
    Status ret = GMERR_OK;
    // gmadmin 查询配置项importSchemaFromShmWaitTime
    memset_s(g_cmd, sizeof(g_cmd), 0, sizeof(g_cmd));
    snprintf_s(g_cmd, BUF_MAX_SIZE, BUF_MAX_SIZE - 1, "gmadmin -cfgName importSchemaFromShmWaitTime");
    const char *matchConfig2[] = {"config current value: 60"};
    ret = StExecuteCommandWithMatch((char *)g_cmd, matchConfig2, 1);
    EXPECT_EQ(GMERR_OK, ret);
    FreeSource();
    st_clt_uninit();
    ShutDownDbServer();
}

#endif
