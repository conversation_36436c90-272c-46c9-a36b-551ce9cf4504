/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: test cases for yang constraint parser
 * Author: LiuMengjie
 * Create: 2022-8-26
 */

#include "yang/yang_common_st.h"

const char *g_treeCfg = R"({"max_record_count":1000, "auto_increment": 1, "yang_model": 1})";
// 测试场景：yang支持不同父亲节点下nodename相同
TEST_F(StYang, TestYangSameNodeNameThenSuc)
{
    const char *vertexFile = "006_graph/yang/yang_st_data/vertex_vertexLabel.json";
    const char *edgeFile = "006_graph/yang/yang_st_data/vertex_edgeLabel.json";

    ASSERT_NO_FATAL_FAILURE(LltCreateVertexAndEdgeLabelAsync(stmt, vertexFile, edgeFile, g_treeCfg));

    ASSERT_NO_FATAL_FAILURE(LltDropVertexAndEdgeLabelAsync(stmt, vertexFile, edgeFile));
}

template <Status expectedStatus = GMERR_DUPLICATE_COLUMN>
void AsyncOperationCbFail(void *userData, Status status, const char *errMsg)
{
    ASSERT_EQ(expectedStatus, status) << errMsg;
    std::atomic_uint32_t *step = reinterpret_cast<std::atomic_uint32_t *>(userData);
    (*step)++;
}

// 测试场景：yang同一父节点下nodename相同报错
TEST_F(StYang, TestYangIllegalNodeNameThenFailed)
{
    const char *vertexFile = "006_graph/yang/yang_st_data/vertex_NodeNamefail.json";

    std::string vertexJson = GetFileContext(vertexFile);

    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, vertexJson.c_str(), g_treeCfg, AsyncOperationCbFail, &step))
        << "labeljsonFile: " << vertexFile;
    EpollWaitAndCheck(step, 1);
}

const char *g_t1List = R"({
    "type":"list",
    "name":"T1List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T1Choice",
            "type":"choice",
            "fields":[
                {"name":"F0", "type":"int32"},
                {
                    "name":"CaseA",
                    "type":"case",
                    "default":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T1List",
            "name":"T1.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：choice node 不允许设置普通字段，建表失败
TEST_F(StYang, PartialTreeChoiceWithBasicField)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t1List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t2List = R"({
    "type":"list",
    "name":"T2List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T2Choice",
            "type":"choice",
            "nullable":false,
            "fields":[
                {
                    "name":"CaseA",
                    "type":"case",
                    "default":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T2List",
            "name":"T2.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：mandatory choice不能包含default case节点，建表失败
TEST_F(StYang, PartialTreeMandatoryChoiceWithDefaultCase)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t2List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t3List = R"({
    "type":"list",
    "name":"T3List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T3Choice",
            "type":"choice",
            "fields":[
                {
                    "name":"CaseA",
                    "type":"case",
                    "default":true,
                    "fields":[
                        {"name":"F0", "type":"int32"},
                        {
                            "name":"caseAChoice",
                            "type":"choice",
                            "nullable":false,
                            "fields":[{"name":"F0", "type":"int32"}]
                        }
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T3List",
            "name":"T3.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：default case不能包含mandatory choice节点，建表失败
TEST_F(StYang, PartialTreeDefaultCaseWithMandatoryChoice)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t3List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t4List = R"({
    "type":"list",
    "name":"T4List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T4Choice",
            "type":"choice",
            "fields":[
                {
                    "name":"T4Con",
                    "type":"container",
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T4List",
            "name":"T2.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：choice不能包含container节点，建表失败
TEST_F(StYang, PartialTreeChoiceWithContainer)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t4List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t5List = R"({
    "type":"list",
    "name":"T5List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T5Con",
            "type":"container",
            "presence":true,
            "is_config":false,
            "fields":[
                {
                    "name":"childCon",
                    "type":"container",
                    "is_config":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T5List",
            "name":"T5.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：非config节点的子节点不能是config节点，建表失败
TEST_F(StYang, PartialTreeNotConfigWithConfig)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t5List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t6List = R"({
    "type":"list",
    "name":"T6List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"list",
            "type":"list",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T6List",
            "name":"T6.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：不支持list类型的子node，建表失败
TEST_F(StYang, PartialTreeWithListNode)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t6List, g_treeCfg, AsyncOperationCb<GMERR_DATATYPE_MISMATCH>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t7List = R"({
    "type":"list",
    "name":"T7List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T7Record",
            "type":"record",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T7List",
            "name":"T7.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：不支持record类型的子node，建表失败
TEST_F(StYang, PartialTreeWithRecordNode)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t7List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t8List = R"({
    "type":"list",
    "name":"T8List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T8con",
            "type":"container",
            "array":true,
            "size":6,
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T8List",
            "name":"T8.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：不支持array类型的子node，建表失败
TEST_F(StYang, PartialTreeWithArrayNode)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t8List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t0Container = R"({
    "type":"container",
    "name":"T0Container",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"int32", "nullable":false}
    ],
    "keys":[
        {
            "node":"T0Container",
            "name":"T0.F0",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
const char *g_t9List = R"({
    "type":"list",
    "name":"T9List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T9Con",
            "type":"container",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T9List",
            "name":"T9.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
const char *g_t9EdgeLabel = R"([
    {
        "name":"T0_T9",
        "source_vertex_label":"T0Container",
        "dest_vertex_label":"T9List",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }
])";
const char *g_graphCfg = R"({"max_record_count":1000, "auto_increment": 1, "yang_model": 0})";
// 测试场景：混用部分打散和全打散模型，建边失败
#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, PartialTreeWithGraphModel)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t0Container, g_graphCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t9List, g_treeCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
    ASSERT_EQ(GMERR_OK, GmcCreateEdgeLabelAsync(
                            stmt, g_t9EdgeLabel, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 3);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T0Container", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 4);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T9List", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 5);
}
#endif

const char *g_t1Container = R"({
    "type":"container",
    "name":"T1Container",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false},
        {"name":"PID", "type":"uint32", "nullable":false},
        {
            "name":"childCon",
            "type":"container",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T1Container",
            "name":"T1.PK",
            "fields":["ID"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
const char *g_t1EdgeLabel = R"([
    {
        "name":"T0_T1",
        "source_vertex_label":"T0Container",
        "dest_vertex_label":"T1Container",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }
])";
// 测试场景：部分打散模型，非根节点用container类型，建边失败
#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, PartialTreeWithContainer)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t0Container, g_graphCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t1Container, g_treeCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
    ASSERT_EQ(GMERR_OK, GmcCreateEdgeLabelAsync(
                            stmt, g_t1EdgeLabel, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 3);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T0Container", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 4);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T1Container", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 5);
}
#endif

const char *g_t10List = R"({
    "type":"list",
    "name":"T10List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T10Con",
            "type":"container",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T10List",
            "name":"T10.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
const char *g_t10EdgeLabel = R"([
    {
        "name":"T0_T10",
        "source_vertex_label":"T0Container",
        "dest_vertex_label":"T10List",
        "dest_node_path":"/T10Con",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {
                    "source_property": "ID",
                    "dest_property": "PID"
                }
            ]
        }
    }
])";
// 测试场景：destNodePath填入非“/”字符串，建边失败
TEST_F(StYang, PartialTreeWithIllegalDestPath)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t0Container, g_treeCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 1);
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(stmt, g_t10List, g_treeCfg, AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 2);
    ASSERT_EQ(GMERR_OK, GmcCreateEdgeLabelAsync(
                            stmt, g_t10EdgeLabel, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 3);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T0Container", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 4);
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, "T10List", AsyncOperationCb, &step));
    EpollWaitAndCheck(step, 5);
}

const char *g_illegalCfg = R"({"max_record_count":1000, "auto_increment": 1, "yang_model": 2})";
#ifdef YANG_SUPPORT_GRAPH
TEST_F(StYang, PartialTreeWithIllegalYangModel)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabelAsync(
                            stmt, g_t0Container, g_illegalCfg, AsyncOperationCb<GMERR_INVALID_JSON_CONTENT>, &step));
    EpollWaitAndCheck(step, 1);
}
#endif

const char *g_t11List = R"({
    "type":"list",
    "name":"T11List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T11Case",
            "type":"case",
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T11List",
            "name":"T11.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：list node下有case节点，建表失败
TEST_F(StYang, PartialTreeWithListCaseNode)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t11List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t12List = R"({
    "type":"list",
    "name":"T12List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T12Choice",
            "type":"choice",
            "default":true,
            "fields":[]
        }
    ],
    "keys":[
        {
            "node":"T12List",
            "name":"T12.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：choice节点default=true，建表失败
TEST_F(StYang, PartialTreeWithDefaultChoiceNode)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t12List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t13List = R"({
    "type":"list",
    "name":"T13List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T13Container",
            "type":"container",
            "default":true,
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T13List",
            "name":"T13.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：choice节点default=true，建表失败
TEST_F(StYang, PartialTreeWithDefaultContainer)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t13List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t14List = R"({
    "type":"list",
    "name":"T14List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T13Container",
            "type":"container",
            "nullable":false,
            "fields":[{"name":"F0", "type":"int32"}]
        }
    ],
    "keys":[
        {
            "node":"T14List",
            "name":"T14.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：container节点nullable=false，建表失败
TEST_F(StYang, PartialTreeWithMandatoryContainer)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t14List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t15List = R"({
    "type":"list",
    "name":"T16List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T16Choice",
            "type":"choice",
            "presence":true,
            "fields":[]
        }
    ],
    "keys":[
        {
            "node":"T16List",
            "name":"T16.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：choice节点presence=true，建表失败
TEST_F(StYang, PartialTreeWithPresenceChoice)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t15List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t16List = R"({
    "type":"list",
    "name":"T16List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T16Choice",
            "type":"choice",
            "fields":[
                {
                    "name":"CaseA",
                    "type":"case",
                    "presence":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T16List",
            "name":"T16.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：case节点presence=true，建表失败
TEST_F(StYang, PartialTreeWithPresenceCase)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t16List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t17List = R"({
    "type":"list",
    "name":"T17List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T17Choice",
            "type":"choice",
            "fields":[
                {
                    "name":"CaseA",
                    "type":"case",
                    "nullable":false,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T17List",
            "name":"T17.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：case节点nullable=false，建表失败
TEST_F(StYang, PartialTreeWithMandatoryCase)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t17List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t18List = R"({
    "type":"list",
    "name":"T18List",
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false},
        {
            "name":"T18Choice",
            "type":"choice",
            "fields":[
                {
                    "name":"CaseA",
                    "type":"case",
                    "default":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                },
                                {
                    "name":"CaseB",
                    "type":"case",
                    "default":true,
                    "fields":[
                        {"name":"F0", "type":"int32"}
                    ]
                }
            ]
        }
    ],
    "keys":[
        {
            "node":"T18List",
            "name":"T18.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：有两个default case节点，建表失败
TEST_F(StYang, PartialTreeWithTwoDefaultCase)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t18List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

const char *g_t19List = R"({
    "type":"list",
    "name":"T19List",
    "is_config":true,
    "fields":[
        {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
        {"name":"PID", "type":"uint32", "nullable":false},
        {"name":"F0", "type":"uint32", "nullable":false, "is_config":false},
        {"name":"F1", "type":"uint32", "nullable":false}
    ],
    "keys":[
        {
            "node":"T19List",
            "name":"T19.PK",
            "fields":["PID", "F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]
})";
// 测试场景：list节点的config类型和list key的config类型不一致，建表失败
TEST_F(StYang, PartialTreeWithIllegalConfig)
{
    std::atomic_uint32_t step{0};
    ASSERT_EQ(GMERR_OK,
        GmcCreateVertexLabelAsync(stmt, g_t19List, g_treeCfg, AsyncOperationCb<GMERR_INVALID_TABLE_DEFINITION>, &step));
    EpollWaitAndCheck(step, 1);
}

#define YangTestTemplate                                                                \
    "[{\"type\":\"container\", \"name\":\"Container%d\","                               \
    "\"fields\":[{\"name\":\"ID\", \"type\":\"uint32\", \"nullable\":false},"           \
    "{\"name\":\"PID\", \"type\":\"uint32\", \"nullable\":false},"                      \
    "{\"name\":\"F0\", \"type\":\"string\", \"size\":200, \"nullable\":false},"         \
    "{\"name\":\"F1\", \"type\":\"int32\", \"nullable\":false}],"                       \
    "\"keys\":[{\"node\":\"Container%d\", \"name\":\"T1_%d.PK\", \"fields\":[\"ID\"], " \
    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]"

#define NormalTestTemplate                                                                           \
    "[{\"type\":\"record\", \"name\":\"VertexLabel%d\", \"fields\":[{\"name\":\"F0\","               \
    "\"type\":\"uint32\"},"                                                                          \
    "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},"                 \
    "{\"name\":\"F3\", \"type\":\"record\",\"vector\":true,\"size\":3,\"fields\":[{\"name\":\"A1\"," \
    "\"type\":\"uint32\"},{\"name\":\"A2\", \"type\":\"uint32\"}]}],"                                \
    "\"keys\":[{\"node\":\"VertexLabel%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"],"            \
    "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]"

enum VertexLabelTypeE { NORMAL = 0, YANG };

const char *g_vConfig = R"({"max_record_count":100000000,"defragmentation":true})";

template <Status expectedStatus = GMERR_OK>
static void CreateSingleVertexLabel(GmcStmtT *stmt, uint32_t vlId, VertexLabelTypeE type)
{
    Status ret = GMERR_OK;
    std::atomic_uint32_t step{0};
    char labelJson[1024] = "";
    if (type == YANG) {
        snprintf(labelJson, 1024, YangTestTemplate, vlId, vlId, vlId);
        ret = GmcCreateVertexLabelAsync(stmt, labelJson, g_treeCfg, AsyncOperationCb<expectedStatus>, &step);
    } else if (type == NORMAL) {
        snprintf(labelJson, 1024, NormalTestTemplate, vlId, vlId);
        ret = GmcCreateVertexLabelAsync(stmt, labelJson, g_vConfig, AsyncOperationCb<expectedStatus>, &step);
    } else {
        ASSERT_EQ(0, 1);
    }
    ASSERT_EQ(GMERR_OK, ret);
    EpollWaitAndCheck(step, 1);
}

static void CreateBatchVertexLabel(GmcStmtT *stmt, uint32_t vlNum, VertexLabelTypeE type)
{
    for (uint32_t i = 1; i <= vlNum; i++) {
        CreateSingleVertexLabel<GMERR_OK>(stmt, i, type);
    }
}

static void CreateBatchKvTable(GmcStmtT *stmt, uint32_t kvNum)
{
    Status ret;
    std::atomic_uint32_t step{0};
    char kvTableName[20] = "";
    for (uint32_t i = 1; i <= kvNum; i++) {
        snprintf(kvTableName, 20, "KvTable%d", i);
        ret = GmcKvCreateTableAsync(stmt, kvTableName, NULL, AsyncOperationCb<GMERR_OK>, &step);
        ASSERT_EQ(GMERR_OK, ret);
        EpollWaitAndCheck(step, i);
    }
}

static void DropSingleVertexLabel(GmcStmtT *stmt, uint32_t vlId, VertexLabelTypeE type)
{
    std::atomic_uint32_t step{0};
    char vertexLabelName[20] = "";
    if (type == YANG) {
        snprintf(vertexLabelName, 20, "Container%d", vlId);
    } else if (type == NORMAL) {
        snprintf(vertexLabelName, 20, "VertexLabel%d", vlId);
    } else {
        ASSERT_EQ(0, 1);
    }
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabelAsync(stmt, vertexLabelName, AsyncOperationCb<GMERR_OK>, &step));
    EpollWaitAndCheck(step, 1);
}

static void DropBatchVertexLabel(GmcStmtT *stmt, uint32_t vlNum, VertexLabelTypeE type)
{
    for (uint32_t i = 1; i <= vlNum; i++) {
        DropSingleVertexLabel(stmt, i, type);
    }
}

static void DropBatchKvTable(GmcStmtT *stmt, uint32_t kvNum)
{
    Status ret;
    std::atomic_uint32_t step{0};
    char kvTableName[20] = "";
    for (uint32_t i = 1; i <= kvNum; i++) {
        snprintf(kvTableName, 20, "KvTable%d", i);
        ret = GmcKvDropTableAsync(stmt, kvTableName, AsyncOperationCb<GMERR_OK>, &step);
        ASSERT_EQ(GMERR_OK, ret);
        EpollWaitAndCheck(step, i);
    }
}

/*
 * maxYangTableNum默认值2000情况下
 * 创建2000张vertexLabel，预期成功；
 * 创建第2001张，预期失败；
 * 删除1张，预期成功；
 * 再次创建1张，预期成功；
 * 再创建1张，预期失败。
 * */
TEST_F(StYang, TestMaxYangTableNum2000)
{
    CreateBatchVertexLabel(stmt, 2000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2000, YANG);
    DropSingleVertexLabel(stmt, 2000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 2000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, YANG);
    DropBatchVertexLabel(stmt, 2000, YANG);
}

/*
 * maxYangTableNum=1000情况下
 * 创建1000张vertexLabel，预期成功；
 * 创建第1001张，预期失败；
 * 删除1张，预期成功；
 * 再次创建1张，预期成功；
 * 再创建1张，预期失败。
 * */
TEST_F(StYang, TestMaxYangTableNum1000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    DropSingleVertexLabel(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

#ifndef ASAN
void *SyncBatchCreateNormalVl(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *nameSpace = "public";
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, nameSpace));

    char labelJson[1024] = "";
    for (int i = 1; i <= 2000; ++i) {
        snprintf(labelJson, 1024, NormalTestTemplate, i, i);
        Status ret = GmcCreateVertexLabel(stmt, labelJson, g_vConfig);
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *SyncBatchCreateYanglVl(void *args)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *nameSpace = "public";
    EXPECT_EQ(GMERR_OK, GmcUseNamespace(stmt, nameSpace));

    char labelJson[1024] = "";
    for (int i = 1; i <= 2000; ++i) {
        snprintf(labelJson, 1024, YangTestTemplate, i, i, i);
        Status ret = GmcCreateVertexLabel(stmt, labelJson, g_treeCfg);
        EXPECT_EQ(GMERR_OK, ret);
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

/*
 * maxYangTableNum、maxNormalTableNum默认值2000情况下
 * 并发创建Yang表和Norma表，应当互不影响
 * */
TEST_F(StYang, TestMultiThreadCreateNormalAndYang)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=2000\"");
    int32_t ret = 0;

    pthread_t yangThread1;
    pthread_t yangThread2;

    ret = pthread_create(&yangThread1, NULL, SyncBatchCreateNormalVl, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&yangThread2, NULL, SyncBatchCreateYanglVl, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(yangThread1, NULL);
    pthread_join(yangThread2, NULL);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, NORMAL);

    ASSERT_NO_FATAL_FAILURE(LltUseNamespaceAsync(stmt, "public"));
    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 2000, YANG);
}

/*
 * maxYangTableNum默认值10000情况下
 * 创建10000张vertexLabel，预期成功；
 * 创建第10001张，预期失败；
 * 删除1张，预期成功；
 * 再次创建1张，预期成功；
 * 再创建1张，预期失败。
 * */
TEST_F(StYang, TestMaxYangTableNum10000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxYangTableNum=10000\"");
    CreateBatchVertexLabel(stmt, 10000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 10001, YANG);
    DropSingleVertexLabel(stmt, 10000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 10000, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 10001, YANG);
    DropBatchVertexLabel(stmt, 10000, YANG);
}

// 1 maxYangTableNum为默认值，分别创建2000张vertex和2000张yang表，创建1024张kv表，预期成功
TEST_F(StYang, TestNormal2000AndYang2000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=2000\"");
    CreateBatchVertexLabel(stmt, 2000, NORMAL);
    CreateBatchVertexLabel(stmt, 2000, YANG);
    CreateBatchKvTable(stmt, 1024);
    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 2000, YANG);
    DropBatchKvTable(stmt, 1024);
}

// 2
// maxYangTableNum为1000，创建2000张vertex和1000张yang表，预期成功；创建第1001张yang，预期失败，创建第2001张vertex表、预期失败
TEST_F(StYang, TestNormal2000AndYang1000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 2000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, NORMAL);

    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

// 3 maxYangTableNum为10000，创建2000张vertex和10000张yang表，预期成功；创建2001张vertex表，预期失败。
TEST_F(StYang, TestNormal2000AndYang10000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=10000\"");
    CreateBatchVertexLabel(stmt, 2000, NORMAL);
    CreateBatchVertexLabel(stmt, 10000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, NORMAL);

    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 10000, YANG);
}

// 4
// maxYangTableNum为1000，创建1999张vertex+1000yang，成功；1001张yang失败；2000张vertex成功；2001vertex失败；1024张kv成功
TEST_F(StYang, TestNormal2000AndYang1000_a)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 1999, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 2000, NORMAL);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, NORMAL);
    CreateBatchKvTable(stmt, 1024);

    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
    DropBatchKvTable(stmt, 1024);
}

// 5
// maxYangTableNum为1000，创建1000张yang+2000张vertex，预期成功；删除1张vertex、成功，再创建1张vertex、成功；删除1张yang、成功，再次创建1张yang、成功
TEST_F(StYang, TestNormal2000AndYang1000_b)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=2000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 2000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    DropSingleVertexLabel(stmt, 2000, NORMAL);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 2000, NORMAL);

    DropSingleVertexLabel(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, YANG);

    DropBatchVertexLabel(stmt, 2000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

// 1 maxYangTableNum为默认值，分别创建1000张vertex和2000张yang表，预期成功；创建1001张vertex表，预期失败
TEST_F(StYang, TestNormal1000AndYang2000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=1000\" \"maxYangTableNum=2000\"");
    CreateBatchVertexLabel(stmt, 1000, NORMAL);
    CreateBatchVertexLabel(stmt, 2000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, NORMAL);

    DropBatchVertexLabel(stmt, 1000, NORMAL);
    DropBatchVertexLabel(stmt, 2000, YANG);
}

// 2 maxYangTableNum为1000，创建1000张vertex和1000张yang表，预期成功；创建1001张yang，预期失败
TEST_F(StYang, TestNormal1000AndYang1000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=1000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 1000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);

    DropBatchVertexLabel(stmt, 1000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

// 3 maxYangTableNum为10000，创建1000张vertex和10000张yang表，预期成功；创建1001张vertex表，预期失败。
TEST_F(StYang, TestNormal1000AndYang10000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=1000\" \"maxYangTableNum=10000\"");
    CreateBatchVertexLabel(stmt, 1000, NORMAL);
    CreateBatchVertexLabel(stmt, 10000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, NORMAL);

    DropBatchVertexLabel(stmt, 1000, NORMAL);
    DropBatchVertexLabel(stmt, 10000, YANG);
}

// 4
// maxYangTableNum为1000，创建999张vertex+1000yang，成功；1001张yang失败；1000张vertex成功；1001vertex失败；1024张kv成功
TEST_F(StYang, TestNormal1000AndYang1000_a)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=1000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 999, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, NORMAL);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, NORMAL);
    CreateBatchKvTable(stmt, 1024);

    DropBatchVertexLabel(stmt, 1000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
    DropBatchKvTable(stmt, 1024);
}

// 5
// maxYangTableNum为1000，创建1000张yang+1000张vertex，预期成功；删除1张vertex、成功，再创建1张vertex、成功；删除1张yang、成功，再次创建1张yang、成功
TEST_F(StYang, TestNormal1000AndYang1000_b)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=1000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 1000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    DropSingleVertexLabel(stmt, 1000, NORMAL);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, NORMAL);

    DropSingleVertexLabel(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, YANG);

    DropBatchVertexLabel(stmt, 1000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

// 1 maxYangTableNum为默认值，分别创建10000张vertex和2000张yang表，预期成功；创建2001张yang表，预期失败
TEST_F(StYang, TestNormal10000AndYang2000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=10000\" \"maxYangTableNum=2000\"");
    CreateBatchVertexLabel(stmt, 10000, NORMAL);
    CreateBatchVertexLabel(stmt, 2000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 2001, YANG);

    DropBatchVertexLabel(stmt, 10000, NORMAL);
    DropBatchVertexLabel(stmt, 2000, YANG);
}

// 2 maxYangTableNum为1000，创建10000张vertex和1000张yang表，预期成功；创建1001张yang，预期失败
TEST_F(StYang, TestNormal10000AndYang1000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=10000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 10000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);

    DropBatchVertexLabel(stmt, 10000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}

// 3 maxYangTableNum为10000，创建10000张vertex和10000张yang表，预期成功；创建10001张vertex表，预期失败。kv1024成功
TEST_F(StYang, TestNormal10000AndYang10000)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=10000\" \"maxYangTableNum=10000\"");
    CreateBatchVertexLabel(stmt, 10000, NORMAL);
    CreateBatchVertexLabel(stmt, 10000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 10001, NORMAL);
    CreateBatchKvTable(stmt, 1024);

    DropBatchVertexLabel(stmt, 10000, NORMAL);
    DropBatchVertexLabel(stmt, 10000, YANG);
    DropBatchKvTable(stmt, 1024);
}

// 4
// maxYangTableNum为1000，创建9999张vertex+1000yang，成功；1001张yang失败；10000张vertex成功；10001vertex失败；1024张kv成功
TEST_F(StYang, TestNormal10000AndYang1000_a)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=10000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 9999, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 1001, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 10000, NORMAL);

    CreateSingleVertexLabel<GMERR_PROGRAM_LIMIT_EXCEEDED>(stmt, 10001, NORMAL);
    CreateBatchKvTable(stmt, 1024);

    DropBatchVertexLabel(stmt, 10000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
    DropBatchKvTable(stmt, 1024);
}

// 5
// maxYangTableNum为1000，创建1000张yang+10000张vertex，预期成功；删除1张vertex、成功，再创建1张vertex、成功；删除1张yang、成功，再次创建1张yang、成功
TEST_F(StYang, TestNormal10000AndYang1000_b)
{
    StYang::RestartDbServerWithAdditionConfig("\"maxNormalTableNum=10000\" \"maxYangTableNum=1000\"");
    CreateBatchVertexLabel(stmt, 10000, NORMAL);
    CreateBatchVertexLabel(stmt, 1000, YANG);

    DropSingleVertexLabel(stmt, 10000, NORMAL);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 10000, NORMAL);

    DropSingleVertexLabel(stmt, 1000, YANG);
    CreateSingleVertexLabel<GMERR_OK>(stmt, 1000, YANG);

    DropBatchVertexLabel(stmt, 10000, NORMAL);
    DropBatchVertexLabel(stmt, 1000, YANG);
}
#endif
