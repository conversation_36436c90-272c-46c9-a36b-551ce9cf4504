/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: st_stream_ddl.cc
 * Description:
 * Create: 2024-08-16
 */

#include <netdb.h>
#include "st_stream_common.h"

#pragma pack(1)
typedef struct StreamStructWriteData {
    int64_t age;
    int64_t water_mark;
    int64_t event_time;
    char name[256];
} StreamStructWriteDataT;
#pragma pack()

#pragma pack(1)
typedef struct StreamStructWriteDoubleData {
    double val;
    int64_t water_mark;
    int64_t event_time;
    uint16_t nameLen;
    char *name;
} StreamStructWriteDoubleDataT;
#pragma pack()

#pragma pack(1)
typedef struct StreamStructWriteTextData {
    int64_t age;
    int64_t water_mark;
    int64_t event_time;
    uint16_t nameLen;
    char *name;
} StreamStructWriteTextDataT;
#pragma pack()

#pragma pack(1)
typedef struct StreamStructWriteTowTextData {
    int64_t age;
    int64_t water_mark;
    int64_t event_time;
    uint16_t nameLen;
    char *name;
    uint16_t remarkLen;
    char *remark;
} StreamStructWriteTowTextDataT;
#pragma pack()

static uint32_t CheckSimpleTupleValue(GmcStmtT *stmt, int64_t value, bool isIncreased = false)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 0;
    char name[256] = {0};
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        size = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(0, strcmp(name, "GMDB"));
        i++;
        if (isIncreased) {
            value++;
        }
    }
    return i;
}

/**
 * 校验结果
 * @param stmt
 * @param tupleNum 总条数
 * @param windowStartId 不校验就MAX
 * @param windowEndId 不校验就MAX
 * @param windowStartValue
 * @param windowEndValue
 * @return
 */
static uint32_t CheckSimpleTupleValueWithWindow(GmcStmtT *stmt, uint32_t tupleNum, uint32_t windowStartId,
    uint32_t windowEndId, uint32_t *windowStartValue, uint32_t *windowEndValue)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 8;
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        if (windowStartId != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, windowStartId, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)windowStartValue[i], val);
        }
        if (windowEndId != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, windowEndId, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)windowEndValue[i], val);
        }
        i++;
    }
    return i;
}

static uint32_t CheckSimpleTupleValueWithRef(GmcStmtT *stmt, int64_t value, int64_t refValue, bool isIncreased = false)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 0;
    char name[256] = {0};
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(refValue, val);
        size = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(0, strcmp(name, "GMDB"));
        i++;
        if (isIncreased) {
            value++;
        }
    }
    return i;
}

/**
 * 校验结果
 * @param stmt
 * @param tupleNum 总条数
 * @param checkProp1Index 不校验就MAX
 * @param checkProp2Index 不校验就MAX
 * @param prop1Value
 * @param prop2Value
 * @return
 */
static uint32_t CheckSimpleDoubleValueWithWindow(GmcStmtT *stmt, uint32_t tupleNum, uint32_t checkProp1Index,
    uint32_t checkProp2Index, int64_t *prop1Value, int64_t *prop2Value)
{
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 8;
    bool eof = false;
    bool isNull = false;
    Status ret;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof || ret != GMERR_OK) {
            break;
        }
        if (checkProp1Index != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, checkProp1Index, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)prop1Value[i], val);
        }
        if (checkProp2Index != UINT32_MAX && i < tupleNum) {
            ret = GmcGetPropertyById((const GmcStmtT *)stmt, checkProp2Index, &val, &size, &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ((int64_t)prop2Value[i], val);
        }
        i++;
    }
    return i;
}

/**
 * name写入GMDB+value浮点数
 */
static void StreamStructWriteSimpleDoubleSingle2(
    GmcStmtT *stmt, double value, int64_t time, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDoubleDataT v1 = {};
    v1.val = value;
    v1.water_mark = time;
    v1.event_time = time;
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s%lf%s", "GMDB", value, "\0");
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 *
 * @param stmt
 * @param index
 * @param number 写入条数
 * @param tableName
 * @param operationType
 */
static void StreamStructWriteSimpleTuple(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value;
        v1.event_time = value;
        (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

/**
 *
 * @param stmt
 * @param index
 * @param number 写入条数
 * @param tableName
 * @param operationType
 */
static void StreamStructWriteSimpleDouble(
    GmcStmtT *stmt, double val, int64_t number, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDoubleDataT v1 = {};
    for (int64_t i = 0; i < number; i++) {
        v1.val = val;
        v1.water_mark = 1;
        v1.event_time = (int64_t)val + i;
        (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void StreamStructWriteSimpleTupleSingle(
    GmcStmtT *stmt, int64_t value, const char *tableName, GmcOperationTypeE operationType)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    v1.age = value;
    v1.water_mark = value;
    v1.event_time = value;
    (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
    EXPECT_EQ(GMERR_OK, ret);
}

static void StreamStructWriteSimpleTuple2(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value + 1;
        v1.event_time = value + 2;
        (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s", "GMDB\0");
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void StreamStructWriteTextTuple(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteTextDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value;
        v1.event_time = value;
        char name[value] = "GMDB";
        v1.name = name;
        v1.nameLen = value;
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void StreamStructWriteTowTextTuple(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteTowTextDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value;
        v1.event_time = value;
        char name[value] = "GMDB";
        v1.name = name;
        v1.nameLen = value;
        char remark[value] = "remark";
        v1.remark = remark;
        v1.remarkLen = value;
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

static void StreamStructWriteLargeTextTuple(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    char largeText[65534] = {0};
    for (uint32_t i = 0; i < 65534; ++i) {
        size_t sizeTotal = sizeof(largeText) + sizeof("a") + 1;
        strcat_s(largeText, sizeTotal, "a");
    }
    StreamStructWriteTextDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value;
        v1.event_time = value;
        v1.name = largeText;
        v1.nameLen = DM_STR_LEN(largeText);
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

class StStreamDml : public StStreamCommon {};

TEST_F(StStreamDml, TestStreamTrxLockTableBucketNum)
{
    DestroyConnectionAndStmt(conn, stmt);
    conn = NULL;
    stmt = NULL;
    st_clt_uninit();
    ShutDownDbServer();
    GmcDetachAllShmSeg();

    const char *cfgPersistErr = "gmserver_ts_error_config.ini";
    StartDbServer((char *)cfgPersistErr);
    DbSleep(waitTime);

    int ret = system("ps aux | grep gmserver | grep -v grep | awk '{print $2}' | xargs kill -15");
    EXPECT_NE(ret, GMERR_OK);

    const char *cfgPersist = "gmserver_ts.ini";
    StartDbServer((char *)cfgPersist);
    DbSleep(waitTime);
    st_clt_init();
}

TEST_F(StStreamDml, TestInsertStreamTableV1)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestInsertStreamTableSelectStar)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] = "CREATE STREAM SINK ab AS select * FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";

    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestInsertStreamDropTsTable)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(ret, GMERR_OK);

    StreamStructWriteSimpleTuple(stmt, 700, 1, tableName, GMC_OPERATION_SQL_INSERT);

    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    // 删除sink 流表

    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestInsertStreamWithBatchSize)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'5');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写1条
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    // 啥也没有
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    // write 9 more
    uint32_t batchSize = 9;
    for (uint32_t i = 0; i < batchSize; i++) {
        StreamStructWriteSimpleTuple(stmt, 500 + i + 1, 1, tableName, GMC_OPERATION_SQL_INSERT);
    }

    uint32_t numInTable = (uint32_t)((batchSize + 1) / 5) * 5;
    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    // 应该有5条，最后一条记录还在buffer中。
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, numInTable);

    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500, true);
    EXPECT_EQ(numInTable, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除sink 流表

    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
}

TEST_F(StStreamDml, TestInsertStreamMultiTsTable)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";
    char commandDropSink1[200] = "drop stream sink ab1;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    char createCommand1[cmdLen] =
        "create table tssink1(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand1[cmdLen] = "drop table tssink1;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    char qryCommand3[40] = "select * from tssink;";
    char qryCommand4[40] = "select * from tssink1;";
    uint32_t value = 0;
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    ret = GmcExecDirect(stmt, qryCommand4, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char command4[200] =
        "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink1) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSink1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);

    GmcResetStmt(stmt);

    // cmd 2
    ret = GmcExecDirect(stmt, qryCommand4, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand1, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestInsertStream1Sink2Table)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";
    char commandDropSink1[200] = "drop stream sink ab1;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command4[200] =
        "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command4, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSink1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(2u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

// 在PrepareStmtByLabelName中根据opType设置stmt的ModelType
TEST_F(StStreamDml, TestSetStmtModelType)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml001)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view1-sink2-tsdb1
 *       -view1-sink2-tsdb2
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中各查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml002)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView1[200] = "CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView1, 200));
    char createView2[200] = "CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView2, 200));

    char createTs1[cmdLen] =
        "create table tssink1(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs1, cmdLen));
    char createTs2[cmdLen] =
        "create table tssink2(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs2, cmdLen));

    char createSink1[200] =
        "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM abView1 INTO tsdb(tssink1) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink1, 200));

    char createSink2[200] =
        "CREATE STREAM SINK ab2 AS select age, water_mark, event_time, name FROM abView2 INTO tsdb(tssink2) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink2, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs1[40] = "select * from tssink1;";
    ret = GmcExecDirect(stmt, qryTs1, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    // 只校验条数

    char qryTs2[40] = "select * from tssink2;";
    ret = GmcExecDirect(stmt, qryTs2, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink1[200] = "drop stream sink ab1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink1, 200));
    char dropSink2[200] = "drop stream sink ab2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink2, 200));

    char dropView1[200] = "drop stream view abView1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView1, 200));
    char dropView2[200] = "drop stream view abView2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView2, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs1[cmdLen] = "drop table tssink1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs1, cmdLen));
    char dropTs2[cmdLen] = "drop table tssink2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs2, cmdLen));
}

/**
 * 场景：source-view1-sink2-tsdb1
 *       -view1-sink2-tsdb1
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中各查询到2条数据
 */
TEST_F(StStreamDml, TestStreamDml003)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView1[200] = "CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView1, 200));
    char createView2[200] = "CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView2, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink1[200] =
        "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM abView1 INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink1, 200));

    char createSink2[200] =
        "CREATE STREAM SINK ab2 AS select age, water_mark, event_time, name FROM abView2 INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink2, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink1[200] = "drop stream sink ab1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink1, 200));
    char dropSink2[200] = "drop stream sink ab2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink2, 200));

    char dropView1[200] = "drop stream view abView1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView1, 200));
    char dropView2[200] = "drop stream view abView2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView2, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-sink-tsdb
 * 行为：配置batch_window_size=10, timeout=1 ，向流表插入一条数据
 * 结果：马上查询数据-无数据;等待1秒后查询数据-查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml004)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=10,timeout=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml005)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=10,timeout=1，向流表插入9条数据
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到9条数据
 */
TEST_F(StStreamDml, TestStreamDml006)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 9, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)9);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=10,timeout=1，向流表插入10条数据
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml007)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=10,timeout=1，向流表插入19条数据
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml008)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 19, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)19);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view1-sink2-tsdb1
 *       -view1-sink2-tsdb1
 * 行为：配置batch_window_size=10，sink1的timeout=1，sink2的timeout=3，向流表插入一条数据
 * 结果：马上查询：无数据
 * 等待1秒查询：1条数据
 * 再等待2秒查询：2条数据
 */
TEST_F(StStreamDml, TestStreamDml009)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView1[200] = "CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView1, 200));
    char createView2[200] = "CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView2, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink1[200] =
        "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM abView1 INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 1);";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink1, 200));

    char createSink2[200] =
        "CREATE STREAM SINK ab2 AS select age, water_mark, event_time, name FROM abView2 INTO tsdb(tssink) with "
        "(batch_window_size = '10', timeout = 3);";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink2, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    DbSleep(1100);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    DbSleep(2000);

    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink1[200] = "drop stream sink ab1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink1, 200));
    char dropSink2[200] = "drop stream sink ab2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink2, 200));

    char dropView1[200] = "drop stream view abView1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView1, 200));
    char dropView2[200] = "drop stream view abView2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView2, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 向integer字段写入INT64_MAX
 * DTS2024092002926
 */
TEST_F(StStreamDml, TestStreamDml010)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'2');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    for (int64_t i = 0; i < 200; i++) {
        StreamStructWriteSimpleTupleSingle(stmt, ((int64_t)INT64_MAX), tableName, GMC_OPERATION_SQL_INSERT);
    }
    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(200u, value);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, INT64_MAX);
    EXPECT_EQ(200u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * sink节点select部分字段
 * DTS2024092019706
 */
TEST_F(StStreamDml, TestStreamDml011)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'2');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    for (int64_t i = 0; i < 2; i++) {
        StreamStructWriteSimpleTupleSingle(stmt, (200), tableName, GMC_OPERATION_SQL_INSERT);
    }
    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    ASSERT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(2u, value);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-sink-tsdb1, sink节点上存在一个ref字段
 * 行为：向流表插入一条数据
 * 结果：注入数据中新增ref字段结果
 */
TEST_F(StStreamDml, TestStreamDml012)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandRef[200] = "create stream reference aref(integer, integer);";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (500, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 2);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-view(带投影）-sink（带投影）-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 * DTS2024091938786
 */
TEST_F(StStreamDml, TestStreamDml013)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name FROM abView INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

TEST_F(StStreamDml, TestStreamDml021)
{
    Status ret = GMERR_OK;

    char commandRef[200] = "create stream reference aref(integer, integer);";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (1, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除reference
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (1, 234);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 创建同名的ref表
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef3[200] = "upsert into streamref aref values (2, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef4[200] = "upsert into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向ref表插入多条k-v值
    char commandUpsertIntoRef5[200] = "upsert into streamref aref values (3, 1), (4, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef5, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入非int类型值
    char commandUpsertIntoRef6[200] = "upsert into streamref aref values (3.5, 1.4);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef6, 200);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
    // 向不存在的ref表插入数据
    char commandUpsertIntoRef7[200] = "upsert into streamref bref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef7, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 向ref表中插入数据，指定ref_name为非ref(stream、sink、view、ts)表
    char commandUpsertIntoRef8[200] = "upsert into streamref a values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef8, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 关键字缺失、大小写交叠、拼写错误
    char commandUpsertIntoRef9[200] = "upst into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef9, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    char commandUpsertIntoRef10[200] = "into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef10, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    char commandUpsertIntoRef11[200] = "upsert into STREAMREF aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef11, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向ref表中插入数据，refName缺失
    char commandUpsertIntoRef12[200] = "upsert into streamref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef12, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，key缺失
    char commandUpsertIntoRef13[200] = "upsert into streamref aref values (, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef13, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，value缺失
    char commandUpsertIntoRef14[200] = "upsert into streamref aref values (1, );";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef14, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，括号缺失
    char commandUpsertIntoRef15[200] = "upsert into streamref aref values 1, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef15, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，多个分号
    char commandUpsertIntoRef16[200] = "upsert into streamref aref values (1, 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef16, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，k-v值中间逗号缺失
    char commandUpsertIntoRef17[200] = "upsert into streamref aref values (1 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef17, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，k-v值有单引号
    char commandUpsertIntoRef18[200] = "upsert into streamref aref values ('1', 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef18, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 大小写不敏感
    char commandUpsertIntoRef19[200] = "upsert into streamref AREF values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef19, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除reference
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
}
/**
 * 场景：source-sink-tsdb1, sink节点上存在一个ref字段
 */
TEST_F(StStreamDml, TestStreamDml022)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandCreateRef1[200] = "create stream reference aref(integer, integer);";
    char dropRefCommand1[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandCreateRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建第二个ref表
    char commandCreateRef2[200] = "create stream reference bref(integer, integer);";
    char dropRefCommand2[200] = "drop stream reference bref;";
    ret = GmcExecDirect(stmt, commandCreateRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 实现aref upsert覆盖场景
    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (500, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向aref表中插入多条数据
    char commandUpsertIntoRef3[200] = "upsert into streamref aref values (1, 3);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向bref表中插入数据
    char commandUpsertIntoRef4[200] = "upsert into streamref bref values (1, 5);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向bref表中插入数据
    char commandUpsertIntoRef5[200] = "upsert into streamref bref values (1, -3);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef5, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 创建ref表，插入值为INT64_MAX
    char commandUpsertIntoRef6[200] = "upsert into streamref bref values (1, 9223372036854775807);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef6, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建ref表，插入值为INT64_MIN
    char commandUpsertIntoRef7[200] = "upsert into streamref bref values (1, -9223372036854775808);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef7, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 创建ref表，插入值为INT64_MAX+1 默认截断
    char commandUpsertIntoRef8[200] = "upsert into streamref bref values (1, 9223372036854775808);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef8, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['bref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char tableName[30] = "a";
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 1, 1, tableName, GMC_OPERATION_SQL_INSERT);
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop aref表
    ret = GmcExecDirect(stmt, dropRefCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop bref表
    ret = GmcExecDirect(stmt, dropRefCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 1, 9223372036854775807);
    EXPECT_EQ(1u, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/*
    Format operator
*/
constexpr int MAX_STREAM_FORMAT_BUFFER_SIZE = 1024u;

/*
 * 场景：Format operator - working example
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Format string with several invalid conversion specifiers, and two valid conv specs matching the two parameters
 * 结果：stream sink created correctly; the format operator generates a new column containing the result of the
 *      formatting string
 */
TEST_F(StStreamDml, TestStreamDml_Format_positive1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT("
                           "'%++5k watermark: %d %% name: %s %+-5k more', water_mark, name) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result value
    uint32_t size;
    char formattedStr[MAX_STREAM_FORMAT_BUFFER_SIZE] = {0};
    memset_s(formattedStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, MAX_STREAM_FORMAT_BUFFER_SIZE);
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = MAX_STREAM_FORMAT_BUFFER_SIZE;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(0, strcmp(formattedStr, "%++5k watermark: 500 % name: GMDB %+-5k more"));

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - working example
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Format string with no parameters and no conversion specifiers - just a plain string
 * 结果：stream sink created correctly; the format operator generates a new column containing a copy of the
 *      format string
 */
TEST_F(StStreamDml, TestStreamDml_Format_positive2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('abcdef') "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result value
    uint32_t size;
    char formattedStr[MAX_STREAM_FORMAT_BUFFER_SIZE];
    (void)memset_s(formattedStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, MAX_STREAM_FORMAT_BUFFER_SIZE);
    memset_s(formattedStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, MAX_STREAM_FORMAT_BUFFER_SIZE);
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = MAX_STREAM_FORMAT_BUFFER_SIZE;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(0, strcmp(formattedStr, "abcdef"));

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      No format string in the parameters of the FORMAT() operator
 * 结果：stream sink not created; GMERR_SEMANTIC_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Format_noFormatString)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT(water_mark, name) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Empty format string in the parameters of the FORMAT() operator
 * 结果：stream sink not created; GMERR_INVALID_PARAMETER_VALUE
 */
TEST_F(StStreamDml, TestStreamDml_Format_emptyFormatString)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('', water_mark) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Format string with no conversion specifiers and one column. This generates a mismatch error, as the number
 *      of valid conversion specifiers of the format string must be equal to the number of properties
 *      in the argument of the FORMAT() call
 * 结果：stream sink not created; GMERR_INVALID_PARAMETER_VALUE
 */
TEST_F(StStreamDml, TestStreamDml_Format_parameterMismatch)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('abcdefg', water_mark) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Format string with one invalid conversion specifiers (%*) and two constants.
 *      Format string with one invalid conversion specifiers (%.*).
 *      Format string with one invalid conversion specifiers (%p).
 *      Format string with one invalid conversion specifiers (%n).
 *      Variable width and precision modifiers are not allowed.
 *      Pointer conversion specifiers are not allowed.
 *      Character counters specifiers are not allowed.
 * 结果：stream sink not created; GMERR_FEATURE_NOT_SUPPORTED
 */
TEST_F(StStreamDml, TestStreamDml_Format_unsupportedCS)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSinkA[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%*d\n', 5, 6) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createSinkA, 200));

    char createSinkB[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%.*d') "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createSinkB, 200));

    char createSinkC[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%p', 5, 6) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createSinkC, 200));

    char createSinkD[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%n', 5, 6) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createSinkD, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Format string with one conversion specifiers (%*) and one invalid column.
 * 结果：stream sink not created; GMERR_UNDEFINE_COLUMN
 */
TEST_F(StStreamDml, TestStreamDml_Format_undefinedColumn)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%d', water_markio) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - Parser error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      No argument to the FORMAT() operator.
 * 结果：stream sink not created, error triggered while parsing sql statement; GMERR_SYNTAX_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Format_parserEmpty)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT() "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - Parser error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Various combination of wrong brackets
 * 结果：stream sink not created, error triggered while parsing sql statement; GMERR_SYNTAX_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Format_parserBrackets)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSinkA[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%d', water_mark "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkA, 200));

    char createSinkB[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT '%d', water_mark) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkB, 200));

    char createSinkC[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%d', water_mark)) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkC, 200));

    char createSinkD[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT(('%d', water_mark) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkD, 200));

    char createSinkE[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT '%d', water_mark "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkE, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - Parser error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Various combination of wrong quotation marks around the format string
 * 结果：stream sink not created, error triggered while parsing sql statement; GMERR_SYNTAX_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Format_parserQuotes)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSinkA[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT(%d, water_mark) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkA, 200));

    char createSinkB[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT('%d, water_mark) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkB, 200));

    char createSinkC[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT(%d', water_mark) "
                            "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createSinkC, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - Parser ok - input verification error
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *      Multiple balanced brackets. Parser is ok, but verification fails
 * 结果：stream sink not created, error triggered while verifying the inputs; GMERR_SEMANTIC_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Format_parserBrackets2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT(((('%d', water_mark)))) "
                           "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_SEMANTIC_ERROR, GmcExecDirect(stmt, createSink, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - testing the buffer limits - within the limits
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *       Output buffer is 1024 bytes long, but last element must be 0, so the max length of a string could be 1023
 bytes.
 *       Long input format string (1019 + one %s conversion specifier at the end).
 *       Input column contains the string "GMDB".
 * 结果：stream sink created correctly; the format operator generates a new column containing the following string:
        "aaa...aaauvGMDB"
        No truncation of the input string or the parameter
 */
TEST_F(StStreamDml, TestStreamDml_Format_BufferLimit1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, name char(256), format_name char(2048))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[1500] = "CREATE STREAM SINK ab AS select water_mark, name, FORMAT("
                            "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaauv%s', "
                            "name) FROM a INTO tsdb(tssink) with (batch_window_size = '1');\0";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 1500));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result values
    uint32_t size;
    char formattedStr[2048] = {0};
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = 2048;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);

    // Building the expected string "aaa...aaauvGMDB" and compare
    char longStr[MAX_STREAM_FORMAT_BUFFER_SIZE];
    memset_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, 1024);
    for (int i = 0; i < 1017; ++i) {
        longStr[i] = 'a';
    }

    longStr[1017] = 0;
    strcat_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, "uvGMDB");
    EXPECT_EQ(0, strcmp(formattedStr, longStr));

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - testing the buffer limits - across the limits by one character
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *       Output buffer is 1024 bytes long, but last element must be 0, so the max length of a string could be 1023
 bytes.
 *       Long input format string (1020 + one %s conversion specifier at the end).
 *       Input column contains the string "GMDB".
 * 结果：stream sink created correctly; the format operator generates a new column containing the following string:
        "aaa...aaauvwGMD"
        String "GMDB" is truncated by one character. No visible failure.
 */
TEST_F(StStreamDml, TestStreamDml_Format_BufferLimit2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "at25";

    // ddl
    char createStream[200] =
        "create stream table at25(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink25(water_mark integer, name char(256), format_name char(2048))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[1500] = "CREATE STREAM SINK as25 AS select water_mark, name, FORMAT("
                            "'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaauvw%s', "
                            "name) FROM at25 INTO tsdb(tssink25) with (batch_window_size = '1');\0";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 1500));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink25;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result values
    uint32_t size;
    char formattedStr[2048] = {0};
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = 2048;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);

    // Building the expected string "aaa...aaauvwGMD" and compare
    char longStr[MAX_STREAM_FORMAT_BUFFER_SIZE];
    memset_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, 1024);
    for (int i = 0; i < 1017; ++i) {
        longStr[i] = 'a';
    }
    longStr[1017] = 0;
    strcat_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, "uvwGMD");
    EXPECT_EQ(0, strcmp(formattedStr, longStr));

    char dropSink[200] = "drop stream sink as25;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table at25;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink25;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - testing the buffer limits - across the limits by one character
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *       Output buffer is 1024 bytes long, but last element must be 0, so the max length of a string could be 1023
 bytes.
 *       Long input format string (one %s conversion specifier at the beginning + 1020).
 *       Input column contains the string "GMDB".
 * 结果：stream sink created correctly; the format operator generates a new column containing the following string:
        "GMDBaaa...aaarstuvwx"
        String "GMDB" is correctly written; format string truncated by one character. No visible failure.
 */
TEST_F(StStreamDml, TestStreamDml_Format_BufferLimit3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "at26";

    // ddl
    char createStream[200] =
        "create stream table at26(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink26(water_mark integer, name char(256), format_name char(2048))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[1500] = "CREATE STREAM SINK as26 AS select water_mark, name, FORMAT("
                            "'%saaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
                            "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaarstuvwxyz', "
                            "name) FROM at26 INTO tsdb(tssink26) with (batch_window_size = '1');\0";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 1500));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink26;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result values
    uint32_t size;
    char formattedStr[2048] = {0};
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = 2048;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);

    // Building the expected string "GMDBaaa...aaarstuvwx" and compare
    char longStr[MAX_STREAM_FORMAT_BUFFER_SIZE];
    memset_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, 0, 1024);
    longStr[0] = 'G';
    longStr[1] = 'M';
    longStr[2] = 'D';
    longStr[3] = 'B';
    for (int i = 4; i < 1016; ++i) {
        longStr[i] = 'a';
    }
    strcat_s(longStr, MAX_STREAM_FORMAT_BUFFER_SIZE, "rstuvwx");
    EXPECT_EQ(0, strcmp(formattedStr, longStr));

    char dropSink[200] = "drop stream sink as26;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table at26;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink26;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Format operator - testing the buffer limits - a very bad but totally legal conversion specifier
 * 行为：Stream table - tssink table - stream sink with FORMAT operator
 *       Output buffer is 1024 bytes long, but last element must be 0, so the max length of a string could be 1023
 bytes.
 *       Long input format string containing a single hideous but totally legal conversion specifier (one %d with many 0
 flags).
 *       Total length of the conversion specifier (1044) is greater than the output buffer (1024).
 *       Input column contains the string "GMDB".
 * 结果：stream sink created correctly; the format operator generates a new column containing the following string:
        "GMDB"
        Although the input format string being longer than the output, it does not matter, as we do not
        allocate temporary buffers while parsing and formatting. No visible failure.
 */
TEST_F(StStreamDml, TestStreamDml_Format_BufferLimit4)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "at27";

    // ddl
    char createStream[200] =
        "create stream table at27(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createTs[cmdLen] =
        "create table tssink27(water_mark integer, name char(256), format_name char(2048))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[1500] = "CREATE STREAM SINK as27 AS select water_mark, name, FORMAT("
                            "'%000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000000000000000000000000000000000000000000000000"
                            "0000000000000000000d', "
                            "water_mark) FROM at27 INTO tsdb(tssink27) with (batch_window_size = '1');\0";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 1500));

    // 结构化写
    StreamStructWriteSimpleTupleSingle(stmt, 500, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink27;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    // Check the result values
    uint32_t size;
    char formattedStr[2048] = {0};
    bool eof, isNull = false;

    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);

    size = 2048;
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &formattedStr, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);

    // Building the expected string "500" and compare
    const char *longStr = "500";
    EXPECT_EQ(0, strcmp(formattedStr, longStr));

    char dropSink[200] = "drop stream sink as27;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));
    char dropStream[200] = "drop stream table at27;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));
    char dropTs[cmdLen] = "drop table tssink27;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
业务不感知modelType的设置
    1、对于结构化插入，只需要调用GmcPrepareStmtByLabelName，根据opCode和vertexLabelType设置modelType；
    2、对sql语句的ddl、dml操作，通过调用GmcExecDirect根据正则匹配来设置stream和其他特性的modelType，随后对特性进行分流。
*/
TEST_F(StStreamDml, TestStreamDml028)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 直接根据ddl建表语句创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;
    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    // 直接根据ddl建表语句创建ts表
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandCreateRef1[200] = "create stream reference      aref(integer, integer);";
    char dropRefCommand1[200] = "drop     stream reference aref;";
    // 直接根据ddl建表语句创建ref表
    ret = GmcExecDirect(stmt, commandCreateRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandInsertIntoRef1[200] = "    upsert into streamref aref values (500, 0);";
    // 直接根据dml对ref表进行插入
    ret = GmcExecDirect(stmt, commandInsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] =
        "    CREATE       sTReAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
        "FROM a INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    // 创建sink
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";
    // 结构化写，只需要传入opCode和表名即可
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop aref表
    ret = GmcExecDirect(stmt, dropRefCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 0);
    EXPECT_EQ(1u, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestStreamDml029)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char command3[200] = "CREATE STREAM SINK ab AS select * FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";

    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

// 客户端接口重构，使业务不需手动设置service模型
TEST_F(StStreamDml, TestStreamDml030)
{
    Status ret = GMERR_OK;
    // 创建时序表
    const static uint32_t cmdLen = 200;
    char createTsCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    ret = GmcExecDirect(stmt, createTsCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建流表
    char tableName[30] = "a";
    char createStreamCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    ret = GmcExecDirect(stmt, createStreamCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建ref表
    char commandCreateRef[200] = "create stream reference aref(integer, integer);";
    ret = GmcExecDirect(stmt, commandCreateRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 向ref表插入数据
    char commandUpsertIntoRef[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建视图
    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";
    ret = GmcExecDirect(stmt, createView, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;
    ret = GmcExecDirect(stmt, createSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    // 删除sink节点
    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    // 删除视图
    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    // 删除流表
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    // 删除时序表
    char dropTs[200] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, 200));

    // 删除ref表
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, 200));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: <= ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml031)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age <= 504;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 500, true);
    EXPECT_EQ(tupleNum, 5);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: = ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml032)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age = 504;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 504, true);
    EXPECT_EQ(tupleNum, 1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: != ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml033)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age != 504;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)9);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: and or ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml034)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age != 504 and age != 503;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)8);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: and or ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml035)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age = "
                           "500 or age = 501 or age = 502;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: and or ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml036)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age = "
                           "500 or (age >= 505 and age <= 507) ;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: and or ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml037)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age < water_mark;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple2(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: and or ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml038)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE 1 < 2;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple2(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: > ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml039)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age > 504;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    uint32_t tupleNum = CheckSimpleTupleValue(stmt, 505, true);
    EXPECT_EQ(tupleNum, 5);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
使用ref过滤
*/
TEST_F(StStreamDml, TestStreamDml040)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 直接根据ddl建表语句创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;
    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    // 直接根据ddl建表语句创建ts表
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandCreateRef1[200] = "create stream reference      aref(integer, integer) with (mini = 'true');";
    char dropRefCommand1[200] = "drop     stream reference aref;";
    // 直接根据ddl建表语句创建ref表
    ret = GmcExecDirect(stmt, commandCreateRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandInsertIntoRef1[200] = " upsert into streamref aref values (500, 0);";
    // 直接根据dml对ref表进行插入
    ret = GmcExecDirect(stmt, commandInsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] =
        "    CREATE       sTReAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
        "FROM a where REF['aref'][event_time] < 0 INTO tsdb(tssink) with "
        "(batch_window_size = "
        "'1');";
    // 创建sink
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";
    // 结构化写，只需要传入opCode和表名即可
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop aref表
    ret = GmcExecDirect(stmt, dropRefCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-view-sink-tsdb 使用text变长字段
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml041)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // 使用text变长字段建表
    char createStream[200] = "create stream table a(age integer, water_mark integer, event_time integer, name text);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name text)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteTextTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb 使用text变长字段，且结构化写包括两个变长字段
 * 行为：配置batch_window_size=1，向流表插入2条数据
 * 结果：在TSDB中查询到2条数据
 */
TEST_F(StStreamDml, TestStreamDml042)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // 使用text变长字段建表
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name text, remark text);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name, remark FROM a";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name text, remark text)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, remark FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteTowTextTuple(stmt, 500, 2, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
支持server_socket外发
正向场景：1个sink对应一个server socket，并且连接数为1
*/
TEST_F(StStreamDml, TestStreamDml044)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char commandSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name "
                            "FROM a INTO server_socket('/tmp/unix_sock.sock') with "
                            "(batch_window_size = "
                            "'1');";
    char commandDropSink[200] = "drop stream sink ab;";
    ret = GmcExecDirect(stmt, commandSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket
    const char *socketAddr = "/tmp/unix_sock.sock";
    int32_t socketFd = ConnectSinkSocket(socketAddr);
    sleep(2);

    // 结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf[1024] = {0};
    read(socketFd, buf, 1024);

    EXPECT_EQ(0, strcmp(buf, "500,500,500,GMDB"));
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd);
}

/*
支持server_socket外发
正向场景：1个sink对应一个server socket，连接数为16
*/
TEST_F(StStreamDml, TestStreamDml045)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char commandSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name "
                            "FROM a INTO server_socket('/tmp/unix_sock1.sock') with "
                            "(batch_window_size = "
                            "'1');";
    char commandDropSink[200] = "drop stream sink ab;";
    ret = GmcExecDirect(stmt, commandSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket
    const char *socketAddr = "/tmp/unix_sock1.sock";
    int32_t socketFd1 = ConnectSinkSocket(socketAddr);
    int32_t socketFd2 = ConnectSinkSocket(socketAddr);
    int32_t socketFd3 = ConnectSinkSocket(socketAddr);
    int32_t socketFd4 = ConnectSinkSocket(socketAddr);
    int32_t socketFd5 = ConnectSinkSocket(socketAddr);
    int32_t socketFd6 = ConnectSinkSocket(socketAddr);
    int32_t socketFd7 = ConnectSinkSocket(socketAddr);
    int32_t socketFd8 = ConnectSinkSocket(socketAddr);
    int32_t socketFd9 = ConnectSinkSocket(socketAddr);
    int32_t socketFd10 = ConnectSinkSocket(socketAddr);
    int32_t socketFd11 = ConnectSinkSocket(socketAddr);
    int32_t socketFd12 = ConnectSinkSocket(socketAddr);
    int32_t socketFd13 = ConnectSinkSocket(socketAddr);
    int32_t socketFd14 = ConnectSinkSocket(socketAddr);
    int32_t socketFd15 = ConnectSinkSocket(socketAddr);
    int32_t socketFd16 = ConnectSinkSocket(socketAddr);
    sleep(2);
    // 结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf1[1024] = {0};
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);

    char buf2[1024] = {0};
    EXPECT_GE(read(socketFd2, buf2, 1024), 0);

    char buf3[1024] = {0};
    EXPECT_GE(read(socketFd3, buf3, 1024), 0);

    char buf4[1024] = {0};
    EXPECT_GE(read(socketFd4, buf4, 1024), 0);

    char buf5[1024] = {0};
    EXPECT_GE(read(socketFd5, buf5, 1024), 0);

    char buf6[1024] = {0};
    EXPECT_GE(read(socketFd6, buf6, 1024), 0);

    char buf7[1024] = {0};
    EXPECT_GE(read(socketFd7, buf7, 1024), 0);

    char buf8[1024] = {0};
    EXPECT_GE(read(socketFd8, buf8, 1024), 0);

    char buf9[1024] = {0};
    EXPECT_GE(read(socketFd9, buf9, 1024), 0);

    char buf10[1024] = {0};
    EXPECT_GE(read(socketFd10, buf10, 1024), 0);

    char buf11[1024] = {0};
    EXPECT_GE(read(socketFd11, buf11, 1024), 0);

    char buf12[1024] = {0};
    EXPECT_GE(read(socketFd12, buf12, 1024), 0);

    char buf13[1024] = {0};
    EXPECT_GE(read(socketFd13, buf13, 1024), 0);

    char buf14[1024] = {0};
    EXPECT_GE(read(socketFd14, buf14, 1024), 0);

    char buf15[1024] = {0};
    EXPECT_GE(read(socketFd15, buf15, 1024), 0);

    char buf16[1024] = {0};
    EXPECT_GE(read(socketFd16, buf16, 1024), 0);
    // 检验buf
    EXPECT_EQ(0, strcmp(buf1, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf2, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf3, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf4, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf5, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf6, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf7, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf8, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf9, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf10, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf11, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf12, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf13, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf14, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf15, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf16, "500,500,500,GMDB"));

    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd1);
    close(socketFd2);
    close(socketFd3);
    close(socketFd4);
    close(socketFd5);
    close(socketFd6);
    close(socketFd7);
    close(socketFd8);
    close(socketFd9);
    close(socketFd10);
    close(socketFd11);
    close(socketFd12);
    close(socketFd13);
    close(socketFd14);
    close(socketFd15);
    close(socketFd16);
}
/*
支持server_socket外发
负向场景：1个sink对应一个server socket，连接数为17，写错误日志
*/
TEST_F(StStreamDml, DISABLED_TestStreamDml046)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char commandSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name "
                            "FROM a INTO server_socket('/tmp/unix_sock2.sock') with "
                            "(batch_window_size = "
                            "'1');";
    char commandDropSink[200] = "drop stream sink ab;";
    ret = GmcExecDirect(stmt, commandSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket
    const char *socketAddr = "/tmp/unix_sock2.sock";
    int32_t socketFd1 = ConnectSinkSocket(socketAddr);
    int32_t socketFd2 = ConnectSinkSocket(socketAddr);
    int32_t socketFd3 = ConnectSinkSocket(socketAddr);
    int32_t socketFd4 = ConnectSinkSocket(socketAddr);
    int32_t socketFd5 = ConnectSinkSocket(socketAddr);
    int32_t socketFd6 = ConnectSinkSocket(socketAddr);
    int32_t socketFd7 = ConnectSinkSocket(socketAddr);
    int32_t socketFd8 = ConnectSinkSocket(socketAddr);
    int32_t socketFd9 = ConnectSinkSocket(socketAddr);
    int32_t socketFd10 = ConnectSinkSocket(socketAddr);
    int32_t socketFd11 = ConnectSinkSocket(socketAddr);
    int32_t socketFd12 = ConnectSinkSocket(socketAddr);
    int32_t socketFd13 = ConnectSinkSocket(socketAddr);
    int32_t socketFd14 = ConnectSinkSocket(socketAddr);
    int32_t socketFd15 = ConnectSinkSocket(socketAddr);
    int32_t socketFd16 = ConnectSinkSocket(socketAddr);
    int32_t socketFd17 = ConnectSinkSocket(socketAddr);
    // 结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf1[1024] = {0};
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);

    char buf2[1024] = {0};
    EXPECT_GE(read(socketFd2, buf2, 1024), 0);

    char buf3[1024] = {0};
    EXPECT_GE(read(socketFd3, buf3, 1024), 0);

    char buf4[1024] = {0};
    EXPECT_GE(read(socketFd4, buf4, 1024), 0);

    char buf5[1024] = {0};
    EXPECT_GE(read(socketFd5, buf5, 1024), 0);

    char buf6[1024] = {0};
    EXPECT_GE(read(socketFd6, buf6, 1024), 0);

    char buf7[1024] = {0};
    EXPECT_GE(read(socketFd7, buf7, 1024), 0);

    char buf8[1024] = {0};
    EXPECT_GE(read(socketFd8, buf8, 1024), 0);

    char buf9[1024] = {0};
    EXPECT_GE(read(socketFd9, buf9, 1024), 0);

    char buf10[1024] = {0};
    EXPECT_GE(read(socketFd10, buf10, 1024), 0);

    char buf11[1024] = {0};
    EXPECT_GE(read(socketFd11, buf11, 1024), 0);

    char buf12[1024] = {0};
    EXPECT_GE(read(socketFd12, buf12, 1024), 0);

    char buf13[1024] = {0};
    EXPECT_GE(read(socketFd13, buf13, 1024), 0);

    char buf14[1024] = {0};
    EXPECT_GE(read(socketFd14, buf14, 1024), 0);

    char buf15[1024] = {0};
    EXPECT_GE(read(socketFd15, buf15, 1024), 0);

    char buf16[1024] = {0};
    EXPECT_GE(read(socketFd16, buf16, 1024), 0);
    char buf17[1024] = {0};
    EXPECT_EQ(read(socketFd17, buf17, 1024), -1);
    // 检验buf
    EXPECT_EQ(0, strcmp(buf1, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf2, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf3, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf4, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf5, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf6, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf7, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf8, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf9, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf10, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf11, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf12, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf13, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf14, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf15, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf16, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf17, ""));

    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd1);
    close(socketFd2);
    close(socketFd3);
    close(socketFd4);
    close(socketFd5);
    close(socketFd6);
    close(socketFd7);
    close(socketFd8);
    close(socketFd9);
    close(socketFd10);
    close(socketFd11);
    close(socketFd12);
    close(socketFd13);
    close(socketFd14);
    close(socketFd15);
    close(socketFd16);
    close(socketFd17);
}
/*
支持server_socket外发
正向场景：1个sink对应一个server socket，先连接16个，关闭首连接，随后建立一个连接，
在关闭尾连接以及中间两个连续的连接，又建联三个连接（符合预期）
负向场景：在正向场景成功之后，建立第17个连接（最大连接数为16），预期写错误日志，读不到数据（符合预期）
*/
TEST_F(StStreamDml, DISABLED_TestStreamDml047)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char commandSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name "
                            "FROM a INTO server_socket('/tmp/unix_sock3.sock') with "
                            "(batch_window_size = "
                            "'1');";
    char commandDropSink[200] = "drop stream sink ab;";
    ret = GmcExecDirect(stmt, commandSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket
    const char *socketAddr = "/tmp/unix_sock3.sock";
    int32_t socketFd1 = ConnectSinkSocket(socketAddr);
    int32_t socketFd2 = ConnectSinkSocket(socketAddr);
    int32_t socketFd3 = ConnectSinkSocket(socketAddr);
    int32_t socketFd4 = ConnectSinkSocket(socketAddr);
    int32_t socketFd5 = ConnectSinkSocket(socketAddr);
    int32_t socketFd6 = ConnectSinkSocket(socketAddr);
    int32_t socketFd7 = ConnectSinkSocket(socketAddr);
    int32_t socketFd8 = ConnectSinkSocket(socketAddr);
    int32_t socketFd9 = ConnectSinkSocket(socketAddr);
    int32_t socketFd10 = ConnectSinkSocket(socketAddr);
    int32_t socketFd11 = ConnectSinkSocket(socketAddr);
    int32_t socketFd12 = ConnectSinkSocket(socketAddr);
    int32_t socketFd13 = ConnectSinkSocket(socketAddr);
    int32_t socketFd14 = ConnectSinkSocket(socketAddr);
    int32_t socketFd15 = ConnectSinkSocket(socketAddr);
    int32_t socketFd16 = ConnectSinkSocket(socketAddr);
    int32_t socketFd17 = ConnectSinkSocket(socketAddr);
    close(socketFd1);
    socketFd1 = ConnectSinkSocket(socketAddr);
    close(socketFd9);
    close(socketFd8);
    close(socketFd16);
    socketFd16 = ConnectSinkSocket(socketAddr);
    socketFd8 = ConnectSinkSocket(socketAddr);
    socketFd9 = ConnectSinkSocket(socketAddr);
    // 结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);
    char buf1[1024] = {0};
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);

    char buf2[1024] = {0};
    EXPECT_GE(read(socketFd2, buf2, 1024), 0);

    char buf3[1024] = {0};
    EXPECT_GE(read(socketFd3, buf3, 1024), 0);

    char buf4[1024] = {0};
    EXPECT_GE(read(socketFd4, buf4, 1024), 0);

    char buf5[1024] = {0};
    EXPECT_GE(read(socketFd5, buf5, 1024), 0);

    char buf6[1024] = {0};
    EXPECT_GE(read(socketFd6, buf6, 1024), 0);

    char buf7[1024] = {0};
    EXPECT_GE(read(socketFd7, buf7, 1024), 0);

    char buf8[1024] = {0};
    EXPECT_GE(read(socketFd8, buf8, 1024), 0);

    char buf9[1024] = {0};
    EXPECT_GE(read(socketFd9, buf9, 1024), 0);

    char buf10[1024] = {0};
    EXPECT_GE(read(socketFd10, buf10, 1024), 0);

    char buf11[1024] = {0};
    EXPECT_GE(read(socketFd11, buf11, 1024), 0);

    char buf12[1024] = {0};
    EXPECT_GE(read(socketFd12, buf12, 1024), 0);

    char buf13[1024] = {0};
    EXPECT_GE(read(socketFd13, buf13, 1024), 0);

    char buf14[1024] = {0};
    EXPECT_GE(read(socketFd14, buf14, 1024), 0);

    char buf15[1024] = {0};
    EXPECT_GE(read(socketFd15, buf15, 1024), 0);

    char buf16[1024] = {0};
    EXPECT_GE(read(socketFd16, buf16, 1024), 0);
    char buf17[1024] = {0};
    EXPECT_EQ(read(socketFd17, buf17, 1024), -1);
    // 检验buf
    EXPECT_EQ(0, strcmp(buf1, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf2, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf3, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf4, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf5, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf6, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf7, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf8, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf9, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf10, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf11, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf12, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf13, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf14, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf15, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf16, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf17, ""));

    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd1);
    close(socketFd2);
    close(socketFd3);
    close(socketFd4);
    close(socketFd5);
    close(socketFd6);
    close(socketFd7);
    close(socketFd8);
    close(socketFd9);
    close(socketFd10);
    close(socketFd11);
    close(socketFd12);
    close(socketFd13);
    close(socketFd14);
    close(socketFd15);
    close(socketFd16);
    close(socketFd17);
}
/*
支持server_socket外发
正向场景：1个sink对应一个server socket，分先后两次写入不同内容到socket，
第二次写入前新增一个client连接，该新增client连接能够正确读到第二次写入的buff
*/
TEST_F(StStreamDml, TestStreamDml048)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建sink节点
    char commandSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name "
                            "FROM a INTO server_socket('/tmp/unix_sock4.sock') with "
                            "(batch_window_size = "
                            "'1');";
    char commandDropSink[200] = "drop stream sink ab;";
    ret = GmcExecDirect(stmt, commandSink, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket
    const char *socketAddr = "/tmp/unix_sock4.sock";
    int32_t socketFd1 = ConnectSinkSocket(socketAddr);
    sleep(2);

    // 第一次结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf1[1024] = {0};
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);
    EXPECT_EQ(0, strcmp(buf1, "500,500,500,GMDB"));
    // 新增client连接
    int32_t socketFd2 = ConnectSinkSocket(socketAddr);
    sleep(2);
    // 第二次结构化写
    StreamStructWriteSimpleTuple(stmt, 400, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf2[1024] = {0};
    EXPECT_GE(read(socketFd2, buf2, 1024), 0);
    EXPECT_EQ(0, strcmp(buf2, "400,400,400,GMDB"));
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);
    EXPECT_EQ(0, strcmp(buf1, "400,400,400,GMDB"));
    // 删除sink 流表
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd1);
    close(socketFd2);
}
/*
支持server_socket外发
DDL
正向场景：1个sink对应一个server socket，并且连接数为1
*/
TEST_F(StStreamDml, TestStreamDml049)
{
    Status ret = GMERR_OK;
    // 创建流表
    char sourceCommand[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char commandDropSource[200] = "drop stream table a;";
    ret = GmcExecDirect(stmt, sourceCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 创建tsdb
    char tsdbCommand[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char commandDropTsdb[200] = "drop table tssink;";
    ret = GmcExecDirect(stmt, tsdbCommand, 200);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建sink节点1，绝对addr成功
    char commandSink1[200] = "CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name "
                             "FROM a INTO server_socket('/tmp/unix_sock5.sock') with "
                             "(batch_window_size = "
                             "'1');";
    char commandDropSink1[200] = "drop stream sink ab1;";
    ret = GmcExecDirect(stmt, commandSink1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 连接server socket1
    const char *socketAddr1 = "/tmp/unix_sock5.sock";
    int32_t socketFd1 = ConnectSinkSocket(socketAddr1);
    sleep(2);
    // 创建sink节点5，多个sink连接同一个socket，成功
    char commandSink2[200] = "CREATE STREAM SINK ab2 AS select age, water_mark, event_time, name "
                             "FROM a INTO server_socket('/tmp/unix_sock5.sock') with "
                             "(batch_window_size = "
                             "'1');";
    ret = GmcExecDirect(stmt, commandSink2, 200);
    EXPECT_EQ(ret, GMERR_CONNECTION_EXCEPTION);
    // 连接server socket5
    int32_t socketFd5 = ConnectSinkSocket(socketAddr1);
    sleep(2);
    // 结构化写
    char tableName[30] = "a";
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char buf1[1024] = {0};
    EXPECT_GE(read(socketFd1, buf1, 1024), 0);
    char buf5[1024] = {0};
    EXPECT_GE(read(socketFd5, buf5, 1024), 0);
    EXPECT_EQ(0, strcmp(buf1, "500,500,500,GMDB"));
    EXPECT_EQ(0, strcmp(buf5, "500,500,500,GMDB"));
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropSource, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropTsdb, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // 关闭client socket连接
    close(socketFd1);
    close(socketFd5);
}

#pragma pack(1)
typedef struct StreamStructWriteTextDataDefault01 {
    char logContent[256];
    int64_t id;
    int64_t event_time;
    char thatName[64];
    char thatHstName[256];
} StreamStructWriteTextDataDefault01T;
#pragma pack()

/*
 * 场景：Default operator - working example
 * 行为：Stream table - tssink table - stream sink with DEFAULT operator defined on the stream table
 *       for some of the properties. All valid definitions
 * 结果：stream table created correctly; during data submission, missing data are substituted with
 *       those defined in the default clauses
 */
TEST_F(StStreamDml, TestStreamDml_Default_positive01)
{
    Status ret = GMERR_OK;
    constexpr int bufferSize = 256;
    // Sum of the columns size of stream table (256 + 8 + 8 + 64 + 256)
    constexpr int payloadSize = sizeof(StreamStructWriteTextDataDefault01T);

    // Create the stream table with default values on some of the properties
    char createStreamTableCommand[220] =
        "create stream table a(log_content char(256), id integer DEFAULT(27), "
        "time integer DEFAULT current_time_second(), name char(64) DEFAULT 'test_string_replacement', "
        "hostnm char(256) DEFAULT get_hostname()"
        ");";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTableCommand, 220));

    char createTableSinkCommand[200] =
        "create table tssink(log_content char(256), id integer, time integer, name char(64), hostnm char(256))"
        " with (time_col = 'time', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] =
        "CREATE STREAM SINK ab AS select log_content, id, time, name, hostnm FROM a INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char tableName[30] = "a";
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    StreamStructWriteTextDataDefault01T payload;
    memset_s(&payload, payloadSize, 0, payloadSize);
    (void)snprintf_s(
        payload.logContent, sizeof(payload.logContent), sizeof(payload.logContent) - 1, "%s", "GMDB_test_string\0");

    // Unset the flag for each property that is NULL
    // WARNING: Currently there are no checks in the testing framework for out-of-boundary
    // accesses of this array
    int8_t notNullProperties[5] = {1, 0, 0, 0, 0};
    // Pass the optional array to the send function when dealing with NULL properties
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&payload, notNullProperties);
    EXPECT_EQ(GMERR_OK, ret);

    // Check that the missing values have been replaced by those specified in the DEFAULT constraints
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);

    bool eof = false;
    bool isNull = false;
    uint32_t size = 0;
    char buffer[bufferSize];
    int64_t val;
    memset_s(buffer, bufferSize, 0, bufferSize);
    EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);

    // Get the current time, for property 3
    // The returned value should be included in a window of 30 seconds centered on the actual time
    constexpr int64_t timeWindowDelta = 15;
    tzset();
    time_t stTime = time(NULL);
    int64_t timeWindowMin = stTime - timeWindowDelta;
    int64_t timeWindowMax = stTime + timeWindowDelta;

    // Get the hostname, for property 5
    // This is STREAM_MAX_HOSTNAME_STRING_LEN
    constexpr int streamMaxHostnameStringLen = 256;
    char realHostname[streamMaxHostnameStringLen];
    (void)memset_s(realHostname, streamMaxHostnameStringLen, 0x00, streamMaxHostnameStringLen);
    gethostname(realHostname, streamMaxHostnameStringLen - 1);

    // Property 1: log_content char(256) == "GMDB_test_string"
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(size, (uint32_t)256);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, buffer, &size, &isNull);
    EXPECT_STREQ("GMDB_test_string", buffer);

    // Property 2: id integer == 27
    ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
    EXPECT_EQ(27, val);

    // Property 3: time integer == 'whatever the clock is +/- 15 seconds'
    ret = GmcGetVertexPropertySizeById(stmt, 2, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
    EXPECT_GT(val, timeWindowMin);
    EXPECT_LT(val, timeWindowMax);

    // Property 4: name char(64) == "test_string_replacement"
    memset_s(buffer, bufferSize, 0, bufferSize);
    ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
    EXPECT_EQ(size, (uint32_t)64);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, buffer, &size, &isNull);
    EXPECT_STREQ("test_string_replacement", buffer);

    // Property 5: hostnm char(256) == 'the current hostname'
    memset_s(buffer, bufferSize, 0, bufferSize);
    ret = GmcGetVertexPropertySizeById(stmt, 4, &size);
    EXPECT_EQ(size, (uint32_t)256);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 4, buffer, &size, &isNull);
    EXPECT_STREQ(realHostname, buffer);

    char dropStreamSinkCommand[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamSinkCommand, 200));
    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}

/*
 * 场景：Default operator - Parser errors
 * 行为：Stream table with DEFAULT operator
 *      Various combination of wrong and correct definitions
 * 结果：Correct (GMERR_OK):
 *      - Default value can be passed inside round brackets, ex. id integer DEFAULT(37)
 *      - lowercase, uppercase and mixedcase accepted, ex. prop1 integer default 104, id integer DefaULt 33,
 *        id integer DEFAULT 22
 *      Incorrect:
 *      - Default value can be passed inside round brackets, but as not multiple arguments,
 *        ex. id integer DEFAULT(37, 12, 55)
 *        --> GMERR_FEATURE_NOT_SUPPORTED
 *      - Missing value definitions, ex. id integer DEFAULT, prop integer DEFAULT()
 *        --> GMERR_SYNTAX_ERROR
 *      - Incorrect spelling of the keyword, ex. DEFFAULT, DEFAUL, DIFAULT, etc...
 *        --> GMERR_SYNTAX_ERROR
 *      - Expressions cannot be column references, ex. id integer DEFAULT(37), prop1 integer id
 *        --> GMERR_FEATURE_NOT_SUPPORTED
 *      - Improperly quoted strings, ex. name char(32) DEFAULT 'test_string
 *        --> GMERR_FEATURE_NOT_SUPPORTED or GMERR_SYNTAX_ERROR
 */
TEST_F(StStreamDml, TestStreamDml_Default_parser)
{
    const Status retOk = GMERR_OK;

    // Default value can be passed inside round brackets
    char createStreamTableCommand01[100] = "create stream table a1(prop1 integer, id integer DEFAULT(37));";
    EXPECT_EQ(retOk, GmcExecDirect(stmt, createStreamTableCommand01, 100));

    // Default value can be passed inside round brackets, but multiple arguments, as they are
    // interpreted as lists. Note that parser does not complain.
    char createStreamTableCommand02[100] = "create stream table a2(prop1 integer, id integer DEFAULT(37, 32));";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamTableCommand02, 100));

    // Missing value 01
    char createStreamTableCommand03[100] = "create stream table a3(prop1 integer DEFAULT, id integer DEFAULT 37);";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand03, 100));

    // Missing value 02
    char createStreamTableCommand04[100] = "create stream table a4(prop1 integer DEFAULT 22, id integer DEFAULT);";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand04, 100));

    // Missing value 03
    char createStreamTableCommand05[100] = "create stream table a5(prop1 integer DEFAULT(), id integer);";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand05, 100));

    // Typo
    char createStreamTableCommand06[100] = "create stream table a6(prop1 integer DEFFAULT 104, id integer);";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand06, 100));

    // Lowercase and mixedcase are OK
    char createStreamTableCommand07[100] = "create stream table a7(prop1 integer default 104, id integer DefaULt 33);";
    EXPECT_EQ(retOk, GmcExecDirect(stmt, createStreamTableCommand07, 100));

    // Using a col_ref as default value is not allowed
    char createStreamTableCommand08[100] =
        "create stream table a8(prop1 integer DEFAULT 104, id integer DEFAULT prop1);";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamTableCommand08, 100));

    // Missing quotes around strings 01
    char createStreamTableCommand09[100] =
        "create stream table a9(prop1 integer, name char(64) DEFAULT unquoted_test_string_without_spaces);";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamTableCommand09, 100));

    // Missing quotes around strings 02
    char createStreamTableCommand10[100] =
        "create stream table a10(prop1 integer, name char(64) DEFAULT unquoted test string with spaces);";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand10, 100));

    // Missing quotes around strings 03
    char createStreamTableCommand11[100] =
        "create stream table a11(prop1 integer, name char(64) DEFAULT(unquoted_test_string_without_spaces));";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamTableCommand11, 100));

    // Missing quotes around strings 04
    char createStreamTableCommand12[100] =
        "create stream table a12(prop1 integer, name char(64) DEFAULT(unquoted test string with spaces));";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamTableCommand12, 100));

    char dropStreamTableCommand01[50] = "drop stream table a1;";
    EXPECT_EQ(retOk, GmcExecDirect(stmt, dropStreamTableCommand01, 50));

    char dropStreamTableCommand07[50] = "drop stream table a7;";
    EXPECT_EQ(retOk, GmcExecDirect(stmt, dropStreamTableCommand07, 50));
}

/*
 * 场景：Default operator - Definition error - Operator used on wrong tables
 * 行为：Stream table - tssink table - stream sink; DEFAULT operator on tssink or sink
 *      The DEFAULT operator can only be used on properties declared on stream table creation instructions,
 *      not on other tables (tssink) or STREAM SINKs
 * 结果：GMERR_SYNTAX_ERROR: tables and STREAM SINKs are not created
 */
TEST_F(StStreamDml, TestStreamDml_Default_tableCreation)
{
    const Status ret = GMERR_OK;

    // Create the stream table with default values on some of the properties
    char createStreamTableCommand01[100] = "create stream table a1(prop1 integer, id integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTableCommand01, 100));

    char createTableSinkCommandFail[200] =
        "create table tssink_fail(time integer, id integer DEFAULT 123)"
        " with (time_col = 'time', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createTableSinkCommandFail, 200));

    char createTableSinkCommand[200] =
        "create table tssink(time integer, id integer)"
        " with (time_col = 'time', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] =
        "CREATE STREAM SINK ab AS select time, id DEFAULT 152 FROM a INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand01[50] = "drop stream table a1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTableCommand01, 50));
}

/*
 * 场景：Default operator - Definition error - Type mismatch
 * 行为：Stream table - tssink table - stream sink; DEFAULT operator on stream table
 *      The DEFAULT must assign a default value that is coherent with the datatype of the relative column.
 *      This is also valid for the datatype of the return values of the expression declared as default.
 *      Ex. the following definitions are invalid:
 *      - id integer DEFAULT 'this_is_a_string'
 *      - stringVariable char(32) DEFAULT 100
 *      Note that these errors are detected during table definition, and not during data submission!
 * 结果：GMERR_DATATYPE_MISMATCH: stream table is not created
 */
TEST_F(StStreamDml, TestStreamDml_Default_typesMismatch)
{
    Status ret = GMERR_OK;
    constexpr int bufferSize = 32;
    constexpr int payloadSize = 80;

    // Create the stream table with default values on some of the properties
    char createStreamTableCommandBroken1[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT 55, int_var2 integer DEFAULT 'not_very_ok_str');";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createStreamTableCommandBroken1, 220));

    char createStreamTableCommandBroken2[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT 'Ok_str2', int_var2 integer DEFAULT 'not_very_ok_str');";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createStreamTableCommandBroken2, 220));

    char createStreamTableCommand[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT 'Ok_str2', int_var2 integer DEFAULT 64);";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamTableCommand, 220));

    char createTableSinkCommand[200] =
        "create table tssink(string_var1 char(32), int_var1 integer, string_var2 char(32), int_var2 integer)"
        " with (time_col = 'int_var1', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] =
        "CREATE STREAM SINK ab AS select string_var1, int_var1, string_var2, int_var2 FROM a INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char tableName[30] = "a";
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    char payload[payloadSize];
    memset_s(payload, payloadSize, 0, payloadSize);
    (void)snprintf_s(payload, payloadSize, payloadSize - 1, "%s", "mini_test_string\0");
    payload[32] = (char)66;

    int8_t notNullProperties[4] = {1, 1, 0, 0};
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&payload, notNullProperties);
    EXPECT_EQ(GMERR_OK, ret);

    // Check that the missing values have been replaced by those specified in the DEFAULT constraints
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(GMERR_OK, ret);

    bool eof = false;
    bool isNull = false;
    uint32_t size = 0;
    char buffer[bufferSize];
    int64_t val;
    memset_s(buffer, bufferSize, 0, bufferSize);
    EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);

    // Property 1: string_var1 char(32) == "mini_test_string"
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(size, (uint32_t)32);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, buffer, &size, &isNull);
    EXPECT_STREQ("mini_test_string", buffer);

    // Property 2: int_var1 integer == 66
    ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
    EXPECT_EQ(66, val);

    // Property 3: string_var2 char(32) == "Ok_str2"
    ret = GmcGetVertexPropertySizeById(stmt, 2, &size);
    EXPECT_EQ(size, (uint32_t)32);
    memset_s(buffer, bufferSize, 0, bufferSize);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, buffer, &size, &isNull);
    EXPECT_STREQ("Ok_str2", buffer);

    // Property 4: int_var2 integer == 64
    ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &val, &size, &isNull);
    EXPECT_EQ(64, val);

    char dropStreamSinkCommand[200] = "drop stream sink ab;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamSinkCommand, 200));
    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}

/*
 * 场景：Default operator - Definition error - Type mismatch
 * 行为：Stream table with DEFAULT operator
 *      The DEFAULT must assign a default value that is coherent with the datatype of the relative column.
 *      This is also valid for the datatype of the return values of the expression declared as default.
 *      Ex. the following definitions are invalid:
 *      - id integer DEFAULT get_hostname()               <<--- this returns a fixed length string
 *      - strVar char(32) DEFAULT current_time_second()   <<--- this returns a long integer
 *      Note that these errors are detected during table definition, and not during data submission!
 * 结果：GMERR_DATATYPE_MISMATCH: stream table is not created
 */
TEST_F(StStreamDml, TestStreamDml_Default_functionRetValTypeMismatch)
{
    // Create the stream table with default values on some of the properties
    char createStreamTableCommandBroken1[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT current_time_second(), int_var2 integer DEFAULT get_hostname());";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createStreamTableCommandBroken1, 220));

    char createStreamTableCommandBroken2[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT get_hostname(), int_var2 integer DEFAULT get_hostname());";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createStreamTableCommandBroken2, 220));

    char createStreamTableCommand[220] =
        "create stream table a(string_var1 char(32) DEFAULT 'Ok_str', int_var1 integer DEFAULT 33, "
        "string_var2 char(32) DEFAULT 'Ok_str2', int_var2 integer DEFAULT current_time_second());";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamTableCommand, 220));

    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}

/*
 * 场景：Default operator - Definition error - unsupported function
 * 行为：Stream table with DEFAULT operator
 *      The DEFAULT value can also be a sql expression / operator which is evaluated during
 *      data submission (effective stream execution). However, only of handful of functions
 *      are actually supported. Precisely:
 *      - get_hostname(): returns a fixed string (max 256 characters) containing the hostname
 *      - current_time_second(): returns an integer with the current time (in seconds, from the
 *        beginning of the current Epoch)
 *      All the other functions, even if syntactically correct, will return an error
 * 结果：GMERR_FEATURE_NOT_SUPPORTED: stream table is not created
 */
TEST_F(StStreamDml, TestStreamDml_Default_unsupportedFunctions)
{
    // Create the stream table with default values on some of the properties
    char createStreamTableCommand01[100] =
        "create stream table a1(prop1 integer, name char(32) DEFAULT FORMAT('test format'));";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamTableCommand01, 100));

    char dropStreamTableCommand01[50] = "drop stream table a1;";
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecDirect(stmt, dropStreamTableCommand01, 50));
}

/*
 * 场景：Default operator - Execution behavior - Very long default string
 * 行为：Stream table with DEFAULT operator - tssink table - stream sink
 *      Strings declared as default values can be longer than the fixed buffer
 *      specified at table definition. This is not a problem, as during stream execution
 *      the long default string is trimmed to fit the output buffer.
 * 结果：GMERR_OK: stream table, tssink table and stream sink are crated; data correctly
 *      submitted and processed; default string silently truncated to fit fixed string
 *      buffer.
 */
TEST_F(StStreamDml, TestStreamDml_Default_stringTooLong)
{
    Status ret = GMERR_OK;
    constexpr int bufferSize = 32;
    constexpr int payloadSize = 40;

    char createStreamTableCommand01[150] =
        "create stream table a(prop1 char(32) DEFAULT "
        "'I love sending over the top incredibly long strings', id integer DEFAULT 8);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTableCommand01, 150));

    char createTableSinkCommand[200] =
        "create table tssink(prop1 char(32), id integer)"
        " with (time_col = 'id', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] = "CREATE STREAM SINK ab AS select prop1, id FROM a INTO tsdb(tssink) with "
                                        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char tableName[30] = "a";
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    char payload[payloadSize];
    memset_s(payload, payloadSize, 0, payloadSize);
    payload[32] = (char)12;

    // Unset the flag for the first property that is NULL, and not use the default for the second
    int8_t notNullProperties[2] = {0, 1};
    // Pass the optional array to the send function when dealing with NULL properties
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&payload, notNullProperties);
    EXPECT_EQ(GMERR_OK, ret);

    // Check that the missing values have been replaced by those specified in the DEFAULT constraints
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);

    bool eof = false;
    bool isNull = false;
    uint32_t size = 0;
    char buffer[bufferSize];
    int64_t val;
    memset_s(buffer, bufferSize, 0, bufferSize);
    EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);

    // Property 1: string_var1 char(32) == "I love sending over the top inc"  <--- 31 chars + \0
    ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
    EXPECT_EQ(size, (uint32_t)32);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, buffer, &size, &isNull);
    EXPECT_STREQ("I love sending over the top inc", buffer);

    // Property 2: int_var1 integer == 12  <--- This must not be overwritten!
    ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
    EXPECT_EQ(12, val);

    char dropStreamSinkCommand[200] = "drop stream sink ab;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamSinkCommand, 200));
    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}

/*
 * 场景：Default operator - Execution error - No value submitted for a property without DEFAULT expr
 * 行为：Stream table with DEFAULT operator - tssink table - stream sink
 *      To Properties which have not been assigned a DEFAULT expression to, it is not possible
 *      to send NULL value in the payload buffer.
 * 结果：GMERR_NULL_VALUE_NOT_ALLOWED: stream table, tssink table and stream sink are crated;
 *      data with missing payload for a property without DEFAULT clause is sent; stream is not
 *      executed and the vertex is destroyed
 */
TEST_F(StStreamDml, TestStreamDml_Default_noValueAndNoDefault)
{
    Status ret = GMERR_OK;

    char createStreamTableCommand01[100] = "create stream table a(prop1 char(32), id integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTableCommand01, 100));

    char createTableSinkCommand[200] =
        "create table tssink(prop1 char(32), id integer)"
        " with (time_col = 'id', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] = "CREATE STREAM SINK ab AS select prop1, id FROM a INTO tsdb(tssink) with "
                                        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char tableName[30] = "a";
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    char payload[40];
    (void)snprintf_s(payload, sizeof(payload), sizeof(payload) - 1, "%s", "mini_test_string\0");

    // Unset the flag for each property that is NULL, without having defined a DEFAULT expression
    int8_t notNullProperties[2] = {1, 0};
    // Pass the optional array to the send function when dealing with NULL properties
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&payload, notNullProperties);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    char dropStreamSinkCommand[200] = "drop stream sink ab;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamSinkCommand, 200));
    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}

/*
 * 场景：Default operator - working example
 * 行为：Stream table - tssink table - stream sink with DEFAULT operator defined on the stream table
 *       for some of the properties. All valid definitions. Stream sink has a FORMAT operator which
 *       uses the two default values defined in the stream table and populated at runtime
 * 结果：stream table created correctly; during data submission, missing data are substituted with
 *       those defined in the default clauses, FORMAT operator correctly uses the default values
 */
TEST_F(StStreamDml, TestStreamDml_Default_MultipleFunc)
{
    Status ret = GMERR_OK;
    constexpr int bufferSize = 256;
    constexpr int payloadSize = 336;

    // Create the stream table with default values on some of the properties
    char createStreamTableCommand[220] =
        "create stream table a(id integer, time integer DEFAULT current_time_second(), "
        "hostnm char(256) DEFAULT get_hostname()"
        ");";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamTableCommand, 220));

    char createTableSinkCommand[200] =
        "create table tssink(id integer, time integer, format_time_hostnm char(256))"
        " with (time_col = 'time', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createTableSinkCommand, 200));

    char createStreamSinkCommand[200] =
        "CREATE STREAM SINK ab AS select id, time, FORMAT('Time: %d - Host name: %s', time, hostnm) "
        "FROM a INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamSinkCommand, 200));

    char tableName[30] = "a";
    ret = GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_SQL_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    char payload[payloadSize];
    memset_s(payload, payloadSize, 0, payloadSize);
    payload[0] = 15;

    // Unset the flag for each property that is NULL
    int8_t notNullProperties[5] = {1, 0, 0};
    // Pass the optional array to the send function when dealing with NULL properties
    ret = StreamStructWriteSingle(stmt, (uint8_t *)&payload, notNullProperties);
    EXPECT_EQ(GMERR_OK, ret);

    // Check that the missing values have been replaced by those specified in the DEFAULT constraints
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);

    bool eof = false;
    bool isNull = false;
    uint32_t size = 0;
    char buffer[bufferSize];
    char bufferComp[bufferSize];
    int64_t val;
    memset_s(buffer, bufferSize, 0, bufferSize);
    memset_s(bufferComp, bufferSize, 0, bufferSize);
    EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);

    // Get the current time
    // The returned value should be included in a window of 30 seconds centered on the actual time
    constexpr int64_t timeWindowDelta = 15;
    tzset();
    time_t stTime = time(NULL);
    int64_t timeWindowMin = stTime - timeWindowDelta;
    int64_t timeWindowMax = stTime + timeWindowDelta;

    // Get the hostname
    constexpr int streamMaxHostnameStringLen = 256;
    char realHostname[streamMaxHostnameStringLen];
    (void)memset_s(realHostname, streamMaxHostnameStringLen, 0x00, streamMaxHostnameStringLen);
    gethostname(realHostname, streamMaxHostnameStringLen - 1);

    // Property 2 is the time, we use that to precisely build the verification string
    ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
    EXPECT_GT(val, timeWindowMin);
    EXPECT_LT(val, timeWindowMax);

    // Build the verification string
    (void)snprintf_s(bufferComp, bufferSize, bufferSize - 1, "Time: %d - Host name: %s", val, realHostname);

    // Property 3: formatted string == "time: xxxxxx - host name: yyyyyy"
    memset_s(buffer, bufferSize, 0, bufferSize);
    ret = GmcGetVertexPropertySizeById(stmt, 2, &size);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(size, (uint32_t)256);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, buffer, &size, &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_STREQ(bufferComp, buffer);

    char dropStreamSinkCommand[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamSinkCommand, 200));
    char dropTableSinkCommand[200] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTableSinkCommand, 200));
    char dropStreamTableCommand[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTableCommand, 200));
}
/**
 * 场景：source-view1-sink-tsdb
 *       source-view2-sink-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到2条数据
 */
TEST_F(StStreamDml, TestStreamDml050)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createView1[200] = "CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView1, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select water_mark, event_time, name FROM TABLE(UNION(TABLE abView,TABLE abView1)) "
        "where age < 10000 INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropView1[200] = "drop stream view abView1;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView1, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml053)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, 100 as bbbbb, name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select 9 as age, water_mark, bbbbb, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 100, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)100);
    value = 500;
    uint32_t i = 0;
    int64_t val = 0;
    uint32_t size = 0;
    char name[256] = {0};
    bool eof = false;
    bool isNull = false;
    while (true) {
        ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(ret, GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 0, &size);
        EXPECT_EQ(size, (uint32_t)8);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(9, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 1, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(value, val);
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(100, val);
        size = 256;
        ret = GmcGetPropertyById((const GmcStmtT *)stmt, 3, &name, &size, &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(0, strcmp(name, "GMDB"));
        i++;
        value++;
    }
    EXPECT_EQ(100u, i);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

TEST_F(StStreamDml, TestStream050_Basic_Calculation1)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age + 3 * 2, water_mark, event_time, name FROM abView "
                     "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {506};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Complex_Calculation1)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    // ?column?
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age + 3 * 2, water_mark + age / 2, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select _column_0, _column_1, event_time, name FROM abView "
                     " INTO tsdb(tssink) with (batch_window_size = '1');");
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {506};
    uint32_t waterMark[] = {750};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, 1, age, waterMark);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Divide_Tiny_Number)
{
    Status ret = GMERR_OK;
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    // ?column?
    executor.ExecSQL("create stream table a(val real, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL(
        "CREATE STREAM VIEW abView AS select val / 0.000000001, (water_mark % 3) + 4, event_time, name FROM a");
    executor.ExecSQL("create table tssink(val integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select _column_0, _column_1, event_time, name FROM abView "
                     " INTO tsdb(tssink) with (batch_window_size = '1');");
    // 结构化写
    StreamStructWriteSimpleDoubleSingle2(stmt, 3.0, 7, "a", GMC_OPERATION_SQL_INSERT);

    char qryTs[100] = "select val, water_mark, event_time from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 100);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t val[] = {3000000000};
    uint32_t waterMark[] = {5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, 1, val, waterMark);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Complex_Calculation2)
{
    Status ret = GMERR_OK;
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    // ?column?
    executor.ExecSQL("create stream table a(val real, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL(
        "CREATE STREAM VIEW abView AS select (val * 0.5) / 0.2 + 3.0, (water_mark % 3) + 4, event_time, name FROM a");
    executor.ExecSQL("create table tssink(val integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select _column_0, _column_1, event_time, name FROM abView "
                     " INTO tsdb(tssink) with (batch_window_size = '1');");
    // 结构化写
    StreamStructWriteSimpleDoubleSingle2(stmt, 8.0, 7, "a", GMC_OPERATION_SQL_INSERT);

    char qryTs[100] = "select val, water_mark, event_time from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 100);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t val[] = {23};
    uint32_t waterMark[] = {5};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, 1, val, waterMark);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Basic_Calculation2)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select age / 100 - 1, water_mark + 100 * age, event_time, name FROM abView "
        "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {4};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Mod_Zero)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age % 0, water_mark + 100 * age, event_time, name FROM abView "
                     "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');",
        GMERR_DIVISION_BY_ZERO);
}

TEST_F(StStreamDml, TestStream050_Syntax_Error)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select {3 + 4}, water_mark + 100 * age, event_time, name FROM abView "
                     "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');",
        GMERR_SYNTAX_ERROR);
}

TEST_F(StStreamDml, TestStream050_In_Int_Double)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark + 100 * age, event_time, name FROM abView "
                     "where age in (1, 2.0) INTO tsdb(tssink) with (batch_window_size = '1');",
        GMERR_DATATYPE_MISMATCH);
}

TEST_F(StStreamDml, TestStream050_Where_Cond)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark + 100 * age, event_time, name FROM abView "
                     "where (age + 3) * 3 > 12 INTO tsdb(tssink) with (batch_window_size = '1');");
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleTuple(stmt, 1, 1, tableName, GMC_OPERATION_SQL_INSERT);
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_nested_Calculation3)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select (age + 3) * 3, water_mark, event_time, name FROM abView "
                     "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {1509};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Wrong_Calculation1)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age real, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select (age + 3.0) * (2.0 - age) / 2, water_mark, event_time, name FROM abView "
        "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');",
        GMERR_DATATYPE_MISMATCH);
}

TEST_F(StStreamDml, TestStream050_nested_Calculation6)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age / 0, water_mark, event_time, name FROM abView "
                     "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');",
        GMERR_DIVISION_BY_ZERO);
}

TEST_F(StStreamDml, TestStream050_nested_Calculation4)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select (age + water_mark) * 3 + event_time, water_mark, event_time, name FROM abView "
        "where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {3500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Alter_Union_Sink_Drop_Normal)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";

    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM TABLE(UNION(TABLE "
                     "abView,TABLE abView1)) where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    executor.ExecSQL("ALTER STREAM SINK ab ALTER FROM_UNION DROP abView1;");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t age[] = {500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 1);
}

TEST_F(StStreamDml, TestStream050_Alter_Union_Sink_Add_Normal)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";

    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM TABLE(UNION(TABLE "
                     "abView,TABLE abView1)) where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    executor.ExecSQL("ALTER STREAM SINK ab ALTER FROM_UNION ADD abView2;");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t age[] = {500, 500, 500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 3u);
}

TEST_F(StStreamDml, TestStream050_Alter_Union_Sink_Add_Small)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";

    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView where age < 10000 "
                     "INTO tsdb(tssink) with (batch_window_size = '1');");
    executor.ExecSQL("ALTER STREAM SINK ab ALTER FROM_UNION ADD abView1;");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t age[] = {500, 500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 2u);
}

// a (Stream Table)
// ├── abView (Stream View) ───┐
// ├── abView1 (Stream View) ──┼──> abViewUnion (Stream View) ───┐
// ├── abView2 (Stream View) ──┘                                 │
// ├── abView3 (Stream View) ────────────────────────────────────┼──> ab (Stream Sink) ───> tssink (Table)
// └─────────────────────────────────────────────────────────────┘
TEST_F(StStreamDml, TestStream050_Alter_Union_View_Add_Normal)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView3 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abViewUnion AS select age, water_mark, event_time, name FROM "
                     "TABLE(UNION(TABLE abView,TABLE abView1))");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM TABLE(UNION(TABLE abViewUnion, TABLE "
        "abView3)) where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    executor.ExecSQL("ALTER STREAM VIEW abViewUnion ALTER FROM_UNION ADD abView2;");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    uint32_t age[] = {500, 500, 500, 500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 4u);
}

// a (Stream Table)
// ├── abView (Stream View) ───┐
// ├── abView1 (Stream View) ──┼──> abViewUnion (Stream View) ───┐                                │
// ├── abView3 (Stream View) ────────────────────────────────────┼──> ab (Stream Sink) ───> tssink (Table)
// └─────────────────────────────────────────────────────────────┘
TEST_F(StStreamDml, TestStream050_Alter_Union_View_Drop_Normal)
{
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView3 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abViewUnion AS select age, water_mark, event_time, name FROM "
                     "TABLE(UNION(TABLE abView,TABLE abView1, TABLE abView2))");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM TABLE(UNION(TABLE abViewUnion, TABLE "
        "abView3)) where age < 10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    executor.ExecSQL("ALTER STREAM VIEW abViewUnion ALTER FROM_UNION DROP abView2;");

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    uint32_t age[] = {500, 500, 500};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 3u);
}

TEST_F(StStreamDml, TestStream050_Alter_Union_Dispatch_View_Failure)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abViewUnion AS select age, water_mark, event_time, name FROM "
                     "TABLE(UNION(TABLE abView, TABLE(DISPATCH(TABLE abView1, 1))))");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abViewUnion where age < "
                     "10000 INTO tsdb(tssink) with (batch_window_size = '1');");

    char alterCmd[200] = "ALTER STREAM VIEW abViewUnion ALTER FROM_UNION ADD TABLE(DISPATCH(TABLE abView1, 1));";

    EXPECT_EQ(GMERR_DUPLICATE_TABLE, GmcExecDirect(stmt, alterCmd, 200));
}

// a (Stream Table)
// ├── abView (Stream View) ──────────────────────────────────────┐
// └── abView1 (Stream View) ──                                   |
//                             │                                  |
//                             └──> DISPATCH(TABLE abView1, 1) ───┐──> abViewUnion (Stream View) ──...
//                             └──> DISPATCH(TABLE abView1, 2) ───┘
TEST_F(StStreamDml, TestStream050_Alter_Union_Dispatch_Add_View)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abViewUnion AS select age, water_mark, event_time, name FROM "
                     "TABLE(UNION(TABLE abView, TABLE(DISPATCH(TABLE abView1, 1))))");
    executor.ExecSQL("ALTER STREAM VIEW abViewUnion ALTER FROM_UNION ADD TABLE(DISPATCH(TABLE abView1, 2));");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abViewUnion where age < "
                     "10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 1, 3, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    uint32_t age[] = {1, 1, 1, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 5u);
}

// a (Stream Table)
// ├── abView (Stream View) ──────────────────────────────────────┐
// └── abView1 (Stream View) ──                                   |
//                             │                                  |
//                             └──> DISPATCH(TABLE abView1, 1) ───┐──> abViewUnion (Stream View) ──...
TEST_F(StStreamDml, TestStream050_Alter_Union_Dispatch_Drop_View)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    char tableName[30] = "a";
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a dispatch by age");
    executor.ExecSQL("CREATE STREAM VIEW abViewUnion AS select age, water_mark, event_time, name FROM "
                     "TABLE(UNION(TABLE abView, TABLE(DISPATCH(TABLE abView1, 1))))");
    executor.ExecSQL("ALTER STREAM VIEW abViewUnion ALTER FROM_UNION ADD TABLE(DISPATCH(TABLE abView1, 2));");
    executor.ExecSQL("ALTER STREAM VIEW abViewUnion ALTER FROM_UNION DROP TABLE(DISPATCH(TABLE abView1, 1));");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abViewUnion where age < "
                     "10000 INTO tsdb(tssink) with (batch_window_size = '1');");
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 1, 3, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    uint32_t age[] = {1, 1, 1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 4u);
}

TEST_F(StStreamDml, TestStreamDml050_Alter_Union_NotExists)
{
    char createStream[200] =
        "create table ts1 (id integer, name char(50), water_mark integer, event_time integer, mini_ref_key integer, "
        "description text) with (time_col = 'id', interval= '1 hour', compression = 'no');";

    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStream, 200));
    char createTable[200] = "create stream table stream1 (id integer, name char(50), water_mark integer, event_time "
                            "integer, mini_ref_key integer, description text);";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createTable, 200));
    char createView1[200] = "create stream view view1 AS select * FROM stream1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createView1, 200));
    char createSink1[200] =
        "create stream sink sink1 AS select * from view1 into tsdb(ts1) with (batch_window_size = 1);";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createSink1, 200));
    char alterNotExists[200] = "alter stream view view1 alter from_union add stream2;";
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecDirect(stmt, alterNotExists, 200));
    char alterNotExists2[200] = "alter stream sink sink1 alter from_union add view2;";
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcExecDirect(stmt, alterNotExists2, 200));

    char dropSink1[200] = "drop stream sink sink1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropSink1, 200));
    char dropV1[200] = "drop stream view view1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropV1, 200));
    char dropS1[200] = "drop stream table stream1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropS1, 200));
    char dropTs[200] = "drop table ts1;";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, dropTs, 200));
}

TEST_F(StStreamDml, TestStreamDml050_Alter_Union_MultiSrc)
{
    Status ret = GMERR_OK;
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("create stream table b(age integer, water_mark integer, event_time integer, name char(256));");
    executor.ExecSQL("CREATE STREAM VIEW abView1 AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("CREATE STREAM VIEW abView2 AS select age, water_mark, event_time, name FROM b");
    executor.ExecSQL("create table tssink1(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("create table tssink2(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL("CREATE STREAM SINK ab1 AS select age, water_mark, event_time, name FROM abView1 where age < "
                     "10000 INTO tsdb(tssink1) with (batch_window_size = '1');");

    executor.ExecSQL("CREATE STREAM SINK ab2 AS select age, water_mark, event_time, name FROM abView2 where age < "
                     "10000 INTO tsdb(tssink2) with (batch_window_size = '1');");

    executor.ExecSQL("alter stream sink ab1 alter from_union add abView2");

    // 结构化写
    char qryTs[40] = "select * from tssink2;";
    StreamStructWriteSimpleTuple(stmt, 1, 2, "b", GMC_OPERATION_SQL_INSERT);
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t age[] = {1, 1};
    uint32_t tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age, NULL);
    EXPECT_EQ(tupleNum, 2u);

    executor.ExecSQL("alter stream sink ab1 alter from_union drop abView2");

    char qryTs2[40] = "select * from tssink1;";
    StreamStructWriteSimpleTuple(stmt, 1, 2, "b", GMC_OPERATION_SQL_INSERT);
    ret = GmcExecDirect(stmt, qryTs2, 40);
    EXPECT_EQ(ret, GMERR_OK);
    value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);
    uint32_t age2[] = {1, 1};
    tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age2, NULL);
    EXPECT_EQ(tupleNum, 2u);

    char qryTs3[40] = "select * from tssink2;";
    StreamStructWriteSimpleTuple(stmt, 1, 2, "b", GMC_OPERATION_SQL_INSERT);
    ret = GmcExecDirect(stmt, qryTs3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)6);
    uint32_t age3[] = {1, 1, 1, 1, 1, 1};
    tupleNum = CheckSimpleTupleValueWithWindow(stmt, 1, 0, UINT32_MAX, age3, NULL);
    EXPECT_EQ(tupleNum, 6u);
}

/**
 *
 * @param stmt
 * @param index
 * @param number 写入条数
 * @param tableName
 * @param operationType
 */
static void StreamStructWriteSimpleTupleForLike(
    GmcStmtT *stmt, int64_t index, uint32_t number, const char *tableName, GmcOperationTypeE operationType)
{
    ASSERT_GE(INT64_MAX, index);
    Status ret = GmcPrepareStmtByLabelName(stmt, tableName, operationType);
    ASSERT_EQ(GMERR_OK, ret);
    StreamStructWriteDataT v1 = {};
    for (int64_t value = index; value < index + number; value++) {
        v1.age = value;
        v1.water_mark = value;
        v1.event_time = value;
        (void)snprintf_s((char *)v1.name, sizeof(v1.name), sizeof(v1.name) - 1, "%s%d%s", "GMDB", value, "\0");
        ret = StreamStructWriteSingle(stmt, (uint8_t *)&v1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
/**
 * 场景：source-view-sink-tsdb  （select where condition测试: like模糊匹配 ）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到10条数据
 */
TEST_F(StStreamDml, TestStreamDml_WHERE_FIXED_LIKE)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE name like 'GMDB51%';";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTupleForLike(stmt, 500, 20, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)10);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: in）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到3条数据
 */
TEST_F(StStreamDml, TestStreamDml056)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age in (500, 501, 502);";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: not in）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到3条数据
 */
TEST_F(StStreamDml, TestStreamDml057)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age not "
                           "in (500, 501, 502);";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)7);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：select mini ref场景
 */
TEST_F(StStreamDml, TestStreamDml058)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandRef[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (137760792, 5);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 0);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StStreamDml, TestStreamDml060)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, current_time_second(), name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, current_time_second, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    // Get the current time
    // The returned value should be included in a window of 30 seconds centered on the actual time
    constexpr int64_t timeWindowDelta = 15;
    tzset();
    time_t stTime = time(NULL);
    int64_t timeWindowMin = stTime - timeWindowDelta;
    int64_t timeWindowMax = stTime + timeWindowDelta;

    bool eof = false;
    bool isNull = false;
    uint32_t size = 0;

    int64_t val;

    EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);

    // Property 2 is the time, we use that to precisely build the verification string
    ret = GmcGetVertexPropertySizeById(stmt, 1, &size);
    EXPECT_EQ(size, (uint32_t)8);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 2, &val, &size, &isNull);
    EXPECT_GT(val, timeWindowMin);
    EXPECT_LT(val, timeWindowMax);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

TEST_F(StStreamDml, TestStreamDml061)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView0[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE name not "
        "in (2,3,5);";

    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createView0, 200));

    char createView1[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age not "
        "in ('GMDB');";

    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createView1, 200));

    char createView2[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age not "
        "in (2.3, 2.5);";

    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createView2, 200));

    char createView3[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age not "
        "in ();";

    EXPECT_EQ(GMERR_SYNTAX_ERROR, GmcExecDirect(stmt, createView3, 200));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE name not "
        "in ('GMDB');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：正在被使用的ref表无法被删除
 */
TEST_F(StStreamDml, TestStreamDml062)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandRef[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (137760792, 5);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表 ref
    // 先删除mini ref，报错无法删除
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_RESTRICT_VIOLATION);
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 待sink删除后，再删除mini ref，成功
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 0);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：view节点存在一个mini ref字段，sink使用select *，写入数据查询
 */
TEST_F(StStreamDml, TestStreamDml063)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandRef[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandRef2[200] = "create stream reference aref2(integer, integer) with (mini = 'true');";
    char dropRefCommand2[200] = "drop stream reference aref2;";
    ret = GmcExecDirect(stmt, commandRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref2 values (500, 5);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name, "
                           "REF['aref'][event_time] FROM a WHERE REF['aref2'][event_time]";
    char commandDropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char command3[200] = "CREATE STREAM SINK ab AS select * FROM abView INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表 ref
    // 先删除mini ref，报错无法删除
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_RESTRICT_VIOLATION);
    ret = GmcExecDirect(stmt, dropRefCommand2, 200);
    EXPECT_EQ(ret, GMERR_RESTRICT_VIOLATION);
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, commandDropView, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 待sink删除后，再删除mini ref，成功
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, dropRefCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 0);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-sink-tsdb1, sink节点上存在一个mini ref字段
 * 行为：向流表插入一条数据
 * 结果：注入数据中新增ref字段结果
 */
TEST_F(StStreamDml, TestStreamDml064)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);

    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandRef[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (500, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char tableName[30] = "a";

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 2);
    EXPECT_EQ(1u, tupleNum);

    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

// mini ref词法
TEST_F(StStreamDml, TestStreamDml065)
{
    Status ret = GMERR_OK;

    char commandRef[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (1, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除reference
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (1, 234);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 创建同名的ref表
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef3[200] = "upsert into streamref aref values (2, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char commandUpsertIntoRef4[200] = "upsert into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向ref表插入多条k-v值
    char commandUpsertIntoRef5[200] = "upsert into streamref aref values (3, 1), (4, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef5, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入非int类型值
    char commandUpsertIntoRef6[200] = "upsert into streamref aref values (3.5, 1.4);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef6, 200);
    EXPECT_EQ(ret, GMERR_DATA_EXCEPTION);
    // 向不存在的ref表插入数据
    char commandUpsertIntoRef7[200] = "upsert into streamref bref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef7, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 向ref表中插入数据，指定ref_name为非ref(stream、sink、view、ts)表
    char commandUpsertIntoRef8[200] = "upsert into streamref a values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef8, 200);
    EXPECT_EQ(ret, GMERR_UNDEFINED_TABLE);
    // 关键字缺失、大小写交叠、拼写错误
    char commandUpsertIntoRef9[200] = "upst into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef9, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    char commandUpsertIntoRef10[200] = "into streamref aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef10, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    char commandUpsertIntoRef11[200] = "upsert into STREAMREF aref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef11, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向ref表中插入数据，refName缺失
    char commandUpsertIntoRef12[200] = "upsert into streamref values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef12, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，key缺失
    char commandUpsertIntoRef13[200] = "upsert into streamref aref values (, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef13, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，value缺失
    char commandUpsertIntoRef14[200] = "upsert into streamref aref values (1, );";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef14, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，括号缺失
    char commandUpsertIntoRef15[200] = "upsert into streamref aref values 1, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef15, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，多个分号
    char commandUpsertIntoRef16[200] = "upsert into streamref aref values (1, 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef16, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，k-v值中间逗号缺失
    char commandUpsertIntoRef17[200] = "upsert into streamref aref values (1 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef17, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 向ref表中插入数据，k-v值有单引号
    char commandUpsertIntoRef18[200] = "upsert into streamref aref values ('1', 1);;";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef18, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 大小写不敏感
    char commandUpsertIntoRef19[200] = "upsert into streamref AREF values (3, 1);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef19, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 删除reference
    ret = GmcExecDirect(stmt, dropRefCommand, 200);
    EXPECT_EQ(ret, GMERR_OK);
}
/**
 * 场景：source-sink-tsdb1, sink节点上存在一个ref字段，能够对重复值进行覆盖
 */
TEST_F(StStreamDml, TestStreamDml066)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandCreateRef1[200] = "create stream reference aref(integer, integer) with (mini = 'true');";
    char dropRefCommand1[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandCreateRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 实现aref upsert覆盖场景
    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (500, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向aref表中插入多条数据
    char commandUpsertIntoRef3[200] = "upsert into streamref aref values (1, 3);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['aref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char tableName[30] = "a";
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop aref表
    ret = GmcExecDirect(stmt, dropRefCommand1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 500, 2);
    EXPECT_EQ(1u, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-sink-tsdb1, sink节点上存在一个ref字段，能够对超过UINT32_T进行截断
 */
TEST_F(StStreamDml, TestStreamDml067)
{
    Status ret = GMERR_OK;
    char command[200] = "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    char command1[200] = "drop stream table a;";
    char commandDropSink[200] = "drop stream sink ab;";

    // 创建流表
    ret = GmcExecDirect(stmt, command, 200);
    EXPECT_EQ(ret, GMERR_OK);
    const static uint32_t cmdLen = 200;

    char createCommand[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), refa integer)"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    char dropCommand[cmdLen] = "drop table tssink;";
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecDirect(stmt, createCommand, cmdLen);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    char qryCommand3[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)0);

    char commandCreateRef2[200] = "create stream reference bref(integer, integer) with (mini = 'true');";
    char dropRefCommand2[200] = "drop stream reference bref;";
    ret = GmcExecDirect(stmt, commandCreateRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向bref表中插入数据
    char commandUpsertIntoRef4[200] = "upsert into streamref bref values (1, 5);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef4, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 向bref表中插入数据
    char commandUpsertIntoRef5[200] = "upsert into streamref bref values (1, -3);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef5, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 创建ref表，插入值为INT64_MAX
    char commandUpsertIntoRef6[200] = "upsert into streamref bref values (1, 9223372036854775807);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef6, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // 创建ref表，插入值为INT64_MIN
    char commandUpsertIntoRef7[200] = "upsert into streamref bref values (1, -9223372036854775808);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef7, 200);
    EXPECT_EQ(ret, GMERR_SYNTAX_ERROR);
    // 创建ref表，插入值为INT64_MAX+1 默认截断
    char commandUpsertIntoRef8[200] = "upsert into streamref bref values (1, 9223372036854775808);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef8, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char command3[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, REF['bref'][event_time] "
                         "FROM a INTO tsdb(tssink) with "
                         "(batch_window_size = "
                         "'1');";
    ret = GmcExecDirect(stmt, command3, 200);
    EXPECT_EQ(ret, GMERR_OK);
    char tableName[30] = "a";
    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 1, 1, tableName, GMC_OPERATION_SQL_INSERT);
    // 删除sink 流表 ref
    ret = GmcExecDirect(stmt, commandDropSink, 200);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecDirect(stmt, command1, 200);
    EXPECT_EQ(ret, GMERR_OK);
    // drop bref表
    ret = GmcExecDirect(stmt, dropRefCommand2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // cmd 1
    ret = GmcExecDirect(stmt, qryCommand3, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);
    uint32_t tupleNum = CheckSimpleTupleValueWithRef(stmt, 1, 4294967295);
    EXPECT_EQ(1u, tupleNum);
    ret = GmcExecDirect(stmt, dropCommand, cmdLen);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 场景：source-view1-sink-tsdb
 *       source-sink-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到2条数据
 */
TEST_F(StStreamDml, TestStreamDmlUnion01)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select * FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select water_mark, event_time, name FROM TABLE(UNION(TABLE abView,TABLE a)) "
        "where age < 10000 INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}
/**
 * 场景：source-view1-sink-tsdb
 *       source-sink-tsdb
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到2条数据
 */
TEST_F(StStreamDml, TestStreamDmlUnion02)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select Age, Water_mArk, event_Time, Name FROM a";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select water_mark, event_time, name FROM TABLE(UNION(TABLE abView,TABLE a)) "
        "where age < 10000 INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 1, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/**
 * 场景：source-view-sink-tsdb  （select where condition测试: in with ref）
 * 行为：向流表插入10条数据 (500,...,509)
 * 结果：马上查询数据-无数据；等待3秒后查询数据-查询到3条数据
 */
TEST_F(StStreamDml, TestStreamDml068)
{
    Status ret = GMERR_OK;

    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char commandRef[200] = "create stream reference aref(integer, integer);";
    char dropRefCommand[200] = "drop stream reference aref;";
    ret = GmcExecDirect(stmt, commandRef, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef1, 200);
    EXPECT_EQ(ret, GMERR_OK);

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (500, 2);";
    ret = GmcExecDirect(stmt, commandUpsertIntoRef2, 200);
    EXPECT_EQ(ret, GMERR_OK);

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE "
                           "ref['aref'][age] in (2, 3, 4);";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";

    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    ;

    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数

    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, 200));

    char dropView[200] = "drop stream view abView;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, 200));

    char dropStream[200] = "drop stream table a;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, 200));

    char dropTs[cmdLen] = "drop table tssink;";

    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

TEST_F(StStreamDml, TestStreamDml069)
{
    SQLExecutor executor(stmt);
    // 创建流表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256)) "
                     "dispatch by age, name;");

    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name char(256)) with "
                     "(time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream view aview as select age, water_mark, count(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB'))",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream view aview as select age, water_mark, max(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB'))",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream view aview as select age, water_mark, min(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB'))",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream view aview as select age, water_mark, sum(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB'))",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream sink asink as select age, water_mark, count(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB')) INTO tsdb(tssink)",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream sink asink as select age, water_mark, max(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB')) INTO tsdb(tssink)",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream sink asink as select age, water_mark, min(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB')) INTO tsdb(tssink)",
        GMERR_SEMANTIC_ERROR);
    executor.ExecSQL("create stream sink asink as select age, water_mark, sum(age) "
                     "from TABLE(DISPATCH(TABLE a, 505, 'GMDB')) INTO tsdb(tssink)",
        GMERR_SEMANTIC_ERROR);
}

TEST_F(StStreamDml, TestStreamDml070)
{
    SQLExecutor executor(stmt);
    // 创建流表
    executor.ExecSQL(
        "create table ts1 (id integer, name char(50), time integer, level integer, content text, count integer) "
        "with (time_col = 'time', interval = '1 hour');");

    executor.ExecSQL("create stream table t1 (id integer, name char(50), time integer, level integer, content text, "
                     "watermark for time as time - interval '1' seconds strict);");

    executor.ExecSQL("create stream view v1 as select *, count(level) "
                     "from table(hop(table t1, time, interval '3' seconds, interval '9' seconds)) "
                     "where window_start > 0 and window_end <= 9 "
                     "group by window_start, window_end, level "
                     "with (tuple_buffer_size = 1);");
    executor.ExecSQL("create stream view v2 as select * from v1 with (tuple_buffer_size = 1);");
    executor.ExecSQL("create stream view v3 as select * from v2 with (tuple_buffer_size = 1);");
    executor.ExecSQL(
        "create stream sink s1 as select first_id, first_name, window_end, level, first_content, count_level from v3 "
        "into tsdb (ts1) with (batch_window_size = 1);");
}

/**
 * 场景：source-view-sink-tsdb 使用text变长字段，长度为65535最大长度
 * 行为：配置batch_window_size=1，向流表插入一条数据
 * 结果：在TSDB中查询到1条数据
 */
TEST_F(StStreamDml, TestStreamDml071)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name text);");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a");
    executor.ExecSQL("create table tssink(age integer, water_mark integer, event_time integer, name text)"
                     " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    executor.ExecSQL(
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');");

    // 结构化写
    StreamStructWriteLargeTextTuple(stmt, 500, 1, "a", GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    // 校验查询结果属性值
    uint32_t size = 0;
    int64_t i = 0;
    bool eof = false;
    while (true) {
        EXPECT_EQ(GmcFetch(stmt, &eof), GMERR_OK);
        if (eof || ret != GMERR_OK) {
            break;
        }
        ret = GmcGetVertexPropertySizeById(stmt, 3, &size);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_EQ(size, 65535);
        i++;
    }
    EXPECT_EQ(i, (uint32_t)1);
}

// ALTER STREAM .. WHERE statement
/*
 * 场景：Alter where - working example: simple creation and alteration of a view node
 * 行为：Stream table - stream view with where clause
 *      Modify the where condition with a different one
 * 结果：where condition of the stream view is correctly changed
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewCreation)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a WHERE age <= 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, cmdLen));

    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age not in (503, 504, 505);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 * 行为：Stream table - stream view without a where clause
 *      Add a new where condition and submit data
 * 结果：where condition of the stream view is correctly added; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewSimpleAdd_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    // We expect all the tuples to be presented by the stream view
    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // Now we expect only two tuples from the query
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age >= 508;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 * 行为：Stream table - stream view with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewSimpleChange_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, 200));

    // We expect 5 tuples from this condition
    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a"
                           " WHERE age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 200));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, 200));

    // Now we expect only one tuple from the query
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age > 508;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)1);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 * 行为：Stream table - stream view with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewSimpleChange_2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    // We expect 5 tuples from this condition
    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a"
                           " WHERE age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, cmdLen));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // Now we expect only three tuples from the query ('204' is not present in the records)
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age in (503, 507, 204, 505);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 * 行为：Stream table - stream view with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewSimpleChange_3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    // We expect 5 tuples from this condition
    char createView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, name FROM a"
                           " WHERE age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, cmdLen));

    char createTs[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs, cmdLen));

    char createSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView INTO tsdb(tssink) with "
        "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // Now we expect only three tuples from the query
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age in (503, 502) OR age > 508;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    // Manually check the contents of the records in the view
    int64_t val = 0;
    uint32_t size = 8;
    bool isNull = false;
    bool eof = false;
    // 1)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(502, val);
    // 2)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(503, val);
    // 3)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(509, val);
    // 4 - empty record)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, true);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references
 * 行为：Stream table - stream view with where clause with one stream ref in the condition
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewRef_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // With this where condition we would eventually get only one record, as the key values defined
    // in the reference are (500, 0) and (502, 2).
    char createStreamView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
                                 "name FROM a WHERE ref['aref'][age] in (2, 3, 4);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By altering the where condition, we will get both records
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS ref['aref'][age] < 3;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256), ll integer) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    char createStreamSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name, ref['aref'][age] FROM abView "
        "INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references
 * 行为：Stream table - stream view with where clause
 *      Modify the where condition with a different one that features a stream ref
 *      and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewRef_2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // With this where condition we would get five records
    char createStreamView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
                                 "name FROM a WHERE age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By altering the where condition, we will get two records
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS ref['aref'][age] in (0, 1, 2, 3);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    char createStreamSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView "
                                 "INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references
 * 行为：Stream table - stream view with where clause with one stream ref in the condition
 *      Modify the where condition with a different one which does not feature a stream ref
 *      and submit data
 * 结果：where condition of the stream view is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_viewRef_3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // With this where condition we would get two records
    char createStreamView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
                                 "name FROM a WHERE ref['aref'][age] in (0, 1, 2, 3);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By altering the where condition, we will get five records
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    char createStreamSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM abView "
                                 "INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: simple creation and alteration of a sink node
 * 行为：Stream table - stream sink with where clause
 *      Modify the where condition with a different one
 * 结果：where condition of the stream sink is correctly changed
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkCreation)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    // ddl
    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createSinkTable[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256))"
        " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    char createSink[200] = "CREATE STREAM SINK ab AS select * FROM a WHERE age > 504 INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    char alterSink[200] = "ALTER STREAM SINK ab ALTER WHERE AS age not in (503, 504, 505);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 * 行为：Stream table - stream sink without a where clause
 *      Add a new where condition and submit data
 * 结果：where condition of the stream sink is correctly added; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkSimpleAdd_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createSinkTable[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // We will submit 10 records, without filtering anything, so we would expect 10 records in the tssink table
    char createSink[200] = "CREATE STREAM SINK ab AS select * FROM a INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // However, we now alter the WHERE condition, putting a more restrictive clause, so the tssink table
    // will end up with only three records
    char alterSink[200] = "ALTER STREAM SINK ab ALTER WHERE AS age > 506;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 * 行为：Stream table - stream sink with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkSimpleChange_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createSinkTable[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // We will submit 10 records, whose 'age' field will range from 500 to 509, both included. The WHERE
    // condition would then filter out the first 5 records
    char createSink[200] = "CREATE STREAM SINK ab AS select * FROM a WHERE age > 504 INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // However, we now alter the WHERE condition, putting a more restrictive clause, so the tssink table
    // will end up with only three records
    char alterSink[200] = "ALTER STREAM SINK ab ALTER WHERE AS age > 506;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 * 行为：Stream table - stream sink with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkSimpleChange_2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createSinkTable[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // We will submit 10 records, filtering the first 3
    char createSink[200] = "CREATE STREAM SINK ab AS select * FROM a WHERE age > 502 INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // However, we now alter the WHERE condition, putting a more restrictive clause, so the tssink table
    // will end up with only four records
    char alterSink[200] = "ALTER STREAM SINK ab ALTER WHERE AS age in (506, 507, 204, 1054, 508, 509);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a v node
 * 行为：Stream table - stream sink with where clause
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkSimpleChange_3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStream[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createSinkTable[200] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // We will submit 10 records, filtering the first 3
    char createSink[200] = "CREATE STREAM SINK ab AS select * FROM a WHERE age > 502 INTO tsdb(tssink) with "
                           "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    // However, we now alter three records
    char alterSink[200] = "ALTER STREAM SINK ab ALTER WHERE AS age in (505, 1023, 1024, 1054, 506) OR age > 508;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);

    // Manually check the contents of the records in the view
    int64_t val = 0;
    uint32_t size = 8;
    bool isNull = false;
    bool eof = false;
    // 1)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(505, val);
    // 2)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(506, val);
    // 3)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, false);
    ret = GmcGetPropertyById((const GmcStmtT *)stmt, 0, &val, &size, &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(509, val);
    // 4 - empty record)
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(eof, true);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));

    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 *      featuring stream references
 * 行为：Stream table - stream sink with where clause with one stream ref in the condition
 *      Modify the where condition with a different one and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkRef_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // With this where condition we would eventually get only one record, as the key values defined
    // in the reference are (500, 0) and (502, 2).
    char createStreamSink[200] =
        "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a "
        "WHERE ref['aref'][age] in (2, 3, 4) INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // By altering the where condition, we will get both records
    char alterSinkRef[200] = "ALTER STREAM SINK ab ALTER WHERE AS ref['aref'][age] < 3;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSinkRef, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 *      featuring stream references
 * 行为：Stream table - stream sink with where clause
 *      Modify the where condition with a different one that features a stream ref
 *      and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkRef_2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // With this where condition we would get five records
    char createStreamSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a "
                                 "WHERE age > 504 INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // By altering the where condition we will get two records
    char alterSinkRef[200] = "ALTER STREAM SINK ab ALTER WHERE AS ref['aref'][age] in (0, 1, 2, 3);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSinkRef, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)2);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a sink node
 *      featuring stream references
 * 行为：Stream table - stream sink with where clause with one stream ref in the condition
 *      Modify the where condition with a different one which does not feature a stream ref
 *      and submit data
 * 结果：where condition of the stream sink is correctly changed; data submitted to
 *      the streaming engine is filtered according to the new where condition
 */
TEST_F(StStreamDml, TestStreamDml_alter_sinkRef_3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char tableName[30] = "a";

    char createStreamRef[200] = "create stream reference aref(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));

    char commandUpsertIntoRef2[200] = "upsert into streamref aref values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    // ddl
    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createSinkTable[cmdLen] =
        "create table tssink(age integer, water_mark integer, event_time integer, name char(256)) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSinkTable, cmdLen));

    // With this where condition we would get two records
    char createStreamSink[200] = "CREATE STREAM SINK ab AS select age, water_mark, event_time, name FROM a "
                                 "WHERE ref['aref'][age] <= 3 INTO tsdb(tssink) with (batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamSink, cmdLen));

    // With this where condition we will get five records
    char alterSinkRef[200] = "ALTER STREAM SINK ab ALTER WHERE AS age > 504;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSinkRef, cmdLen));

    // 结构化写
    StreamStructWriteSimpleTuple(stmt, 500, 10, tableName, GMC_OPERATION_SQL_INSERT);

    // 只校验条数
    uint32_t value = 0;
    char qryTs[40] = "select * from tssink;";
    ret = GmcExecDirect(stmt, qryTs, 40);
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)4);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)5);

    char dropSink[200] = "drop stream sink ab;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs[cmdLen] = "drop table tssink;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs, cmdLen));
    char dropRefCommand[200] = "drop stream reference aref;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references; global ref counters updates
 * 行为：Stream table - stream view with where clause; stream references are used the property
 *      list and in the where condition.
 *      Modify the where condition with a different one which does not feature a stream ref
 *      and submit data.
 * 结果：where condition of the stream view is correctly changed; global ref list is correctly
 *      updated; data submitted to the streaming engine is filtered according to the new
 *      where condition; attempts to drop refs before the nodes violates the dependency rule
 *      and fails.
 */
TEST_F(StStreamDml, TestStreamDml_alter_RefCounting_1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStreamRef1[200] = "create stream reference aref1(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef1, cmdLen));
    char createStreamRef2[200] = "create stream reference aref2(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef2, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref1 values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));
    char commandUpsertIntoRef2[200] = "upsert into streamref aref2 values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // We use aref1 in the property list, and aref1 and aref2 on the where condition
    // We espect both aref1 and aref2 being added to the temporary refName list
    // We expect the abView vertexLabelId to be added to both aref1 and aref2 StreamRefSlot.vertexLabelIds lists
    char createStreamView[200] =
        "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
        "name, ref['aref1'][age] FROM a WHERE ref['aref1'][age] in (0, 1) AND ref['aref2'][event_time] = 2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By changing the where condition, we are effectively removing abView dependency from aref2, but not aref1,
    // because this last one is still used in the property list.
    // We expect abView vertexLabelId to be purged from aref2 vertexLabelIds list, but not from aref1's.
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS age > 505;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // It should be possible to drop aref2, as there are no more direct dependencies.
    char dropRefCommand2[200] = "drop stream reference aref2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand2, cmdLen));
    // However, this should fail, as abView still relies on aref1.
    char dropRefCommand1[200] = "drop stream reference aref1;";
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, GmcExecDirect(stmt, dropRefCommand1, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    // Just now we can drop aref1
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand1, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references; global ref counters updates
 * 行为：Stream table - stream view with where clause; stream references are used the property
 *      list and in the where condition.
 *      Modify the where condition with a different one which features a different stream ref
 *      and submit data.
 * 结果：where condition of the stream view is correctly changed; global ref list is correctly
 *      updated; data submitted to the streaming engine is filtered according to the new
 *      where condition; attempts to drop refs before the nodes violates the dependency rule
 *      and fails.
 */
TEST_F(StStreamDml, TestStreamDml_alter_RefCounting_2)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStreamRef1[200] = "create stream reference aref1(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef1, cmdLen));
    char createStreamRef2[200] = "create stream reference aref2(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef2, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref1 values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));
    char commandUpsertIntoRef2[200] = "upsert into streamref aref2 values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // We use aref1 in the property list and on the where condition
    // We espect aref1 being added to the temporary refName list
    // We expect the abView vertexLabelId to be added to aref1 StreamRefSlot.vertexLabelIds lists
    char createStreamView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
                                 "name, ref['aref1'][age] FROM a WHERE ref['aref1'][age] in (0, 1);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By changing the where condition, we are are adding abview as a dependency of aref2 too
    // We expect abView vertexLabelId to be added to aref2 vertexLabelIds list.
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS ref['aref2'][event_time] = 2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // It should not be possible to drop aref2 or aref1.
    char dropRefCommand2[200] = "drop stream reference aref2;";
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, GmcExecDirect(stmt, dropRefCommand2, cmdLen));
    // However, this should fail, as abView still relies on aref1.
    char dropRefCommand1[200] = "drop stream reference aref1;";
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, GmcExecDirect(stmt, dropRefCommand1, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));

    // Just now we can drop aref1 and aref2
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand1, cmdLen));
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand2, cmdLen));
}

/*
 * 场景：Alter where - working example: creation and alteration of a view node
 *      featuring stream references; global ref counters updates
 * 行为：Stream table - stream view with where clause; stream references are used in the
 *      where condition.
 *      Modify the where condition with a different one which does not feature stream refs
 *      and submit data.
 * 结果：where condition of the stream view is correctly changed; global ref list is correctly
 *      updated; data submitted to the streaming engine is filtered according to the new
 *      where condition; stream refs can be released before dropping the stream nodes, as
 *      they have been freed from any dependency.
 */
TEST_F(StStreamDml, TestStreamDml_alter_RefCounting_3)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStreamRef1[200] = "create stream reference aref1(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef1, cmdLen));
    char createStreamRef2[200] = "create stream reference aref2(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef2, cmdLen));

    char commandUpsertIntoRef1[200] = "upsert into streamref aref1 values (500, 0);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef1, cmdLen));
    char commandUpsertIntoRef2[200] = "upsert into streamref aref2 values (502, 2);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, commandUpsertIntoRef2, cmdLen));

    char createStreamTable[200] =
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    // We use aref1 and aref2 just in the where condition.
    // We espect aref1 and aref2 being added to the temporary refName list.
    // We expect the abView vertexLabelId to be added to aref1 and aref2 StreamRefSlot.vertexLabelIds lists
    char createStreamView[200] = "CREATE STREAM VIEW abView AS select age, water_mark, event_time, "
                                 "name FROM a WHERE ref['aref1'][age] in (0, 1) and ref['aref2'][event_time] = 2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamView, cmdLen));

    // By changing the where condition, we are are releasing any vertexLabel dependency from aref1
    // and aref2.
    // We expect abView vertexLabelId to be removed from aref1 and aref2 vertexLabelIds list.
    char alterView[200] = "ALTER STREAM VIEW abview ALTER WHERE AS event_time < 507;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    // It should be possible to drop aref1 and aref2.
    char dropRefCommand2[200] = "drop stream reference aref2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand2, cmdLen));
    char dropRefCommand1[200] = "drop stream reference aref1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand1, cmdLen));

    char dropView[200] = "drop stream view abView;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteAvg)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL("create stream table a(age integer, water_mark integer, event_time integer, name char(256),"
                     " watermark for event_time as event_time - INTERVAL '1' SECONDS);");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select avg(age) over(PARTITION by window_start, window_end) + 3, "
                     "water_mark, event_time, name "
                     " FROM TABLE(HOP(TABLE a, event_time, INTERVAL '3' SECONDS, INTERVAL '9' SECONDS))",
        GMERR_FEATURE_NOT_SUPPORTED);
}

TEST_F(StStreamDml, TestStreamDmlWriteWrongDouble)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(age integer, water_mark integer, event_time integer) "
                     "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(age real, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select age, water_mark, event_time "
                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleTuple(stmt, 500, 3, "t1", GMC_OPERATION_SQL_INSERT);
    executor.ExecSQL("select * from tstable1");
    Status ret = GMERR_OK;
    uint32_t value = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_EQ(value, (uint32_t)3);
}

TEST_F(StStreamDml, TestStreamDmlWriteFormat1)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char createTsTable[cmdLen] =
        "create table tstable1(val integer, water_mark char(100), event_time char(100)) "
        "with (time_col = 'val', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTsTable, cmdLen));
    char createStreamTable[cmdLen] =
        "create stream table t1(val integer, water_mark integer, event_time integer, name char(256), "
        "watermark for event_time as event_time - INTERVAL '1' SECONDS);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createStreamSink3[cmdLen] =
        "create stream sink s3 as select val, format('water_mark:%d', water_mark), format('event_time:%d', event_time) "
        "from t1 into TSDB(tstable1) with (batch_window_size = 1)";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamSink3, cmdLen));

    char dropTsTable[cmdLen] = "drop table tstable1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTsTable, cmdLen));
    char dropSink3[cmdLen] = "drop stream sink s3;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink3, cmdLen));
    char dropStreamTable[cmdLen] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTable, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteFormat2)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char createTsTable[cmdLen] =
        "create table tstable1(val integer, water_mark char(100), event_time char(100)) "
        "with (time_col = 'val', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTsTable, cmdLen));
    char createStreamTable[cmdLen] =
        "create stream table t1(val integer, water_mark integer, event_time integer, name char(256), "
        "watermark for event_time as event_time - INTERVAL '1' SECONDS);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createStreamSink3[cmdLen] =
        "create stream sink s3 as select val, format('water_mark:%f', water_mark), format('event_time:%f', event_time) "
        "from t1 into TSDB(tstable1) with (batch_window_size = 1)";
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, GmcExecDirect(stmt, createStreamSink3, cmdLen));

    char dropTsTable[cmdLen] = "drop table tstable1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTsTable, cmdLen));
    char dropStreamTable[cmdLen] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTable, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteFormat3)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char createTsTable[cmdLen] =
        "create table tstable1(val char(100), water_mark integer, event_time integer) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTsTable, cmdLen));
    char createStreamTable[cmdLen] =
        "create stream table t1(val real, water_mark integer, event_time integer, name char(256), "
        "watermark for event_time as event_time - INTERVAL '1' SECONDS);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createStreamSink3[cmdLen] = "create stream sink s3 as select format('val:%f', val), water_mark, event_time "
                                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamSink3, cmdLen));

    char dropTsTable[cmdLen] = "drop table tstable1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTsTable, cmdLen));
    char dropStreamTable[cmdLen] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTable, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteFormat4)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char createTsTable[cmdLen] =
        "create table tstable1(val char(100), water_mark integer, event_time integer) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTsTable, cmdLen));
    char createStreamTable[cmdLen] =
        "create stream table t1(val real, water_mark integer, event_time integer, name char(256), "
        "watermark for event_time as event_time - INTERVAL '1' SECONDS);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createStreamSink3[cmdLen] = "create stream sink s3 as select format('val:%d', val), water_mark, event_time "
                                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)";
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, GmcExecDirect(stmt, createStreamSink3, cmdLen));

    char dropTsTable[cmdLen] = "drop table tstable1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTsTable, cmdLen));
    char dropStreamTable[cmdLen] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTable, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteIntFormat)
{
    SQLExecutor executor(stmt);
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;
    char createTsTable[cmdLen] =
        "create table tstable1(val char(100), water_mark integer, event_time integer) "
        "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTsTable, cmdLen));
    char createStreamTable[cmdLen] =
        "create stream table t1(val integer, water_mark integer, event_time integer, name char(256), "
        "watermark for event_time as event_time - INTERVAL '1' SECONDS);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamTable, cmdLen));

    char createStreamSink3[cmdLen] = "create stream sink s3 as select format('val:%d', val), water_mark, event_time "
                                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)";
    EXPECT_EQ(GMERR_OK, GmcExecDirect(stmt, createStreamSink3, cmdLen));

    char dropTsTable[cmdLen] = "drop table tstable1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTsTable, cmdLen));
    char dropSink3[cmdLen] = "drop stream sink s3;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink3, cmdLen));
    char dropStreamTable[cmdLen] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStreamTable, cmdLen));
}

TEST_F(StStreamDml, TestStreamDmlWriteWrongInteger)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(val integer, water_mark integer, event_time integer) "
                     "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(val integer, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select val, water_mark, event_time "
                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleDoubleSingle2(stmt, 3.5, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleDoubleSingle2(stmt, 3.5, 4, "t1", GMC_OPERATION_SQL_INSERT);
    executor.ExecSQL("select * from tstable1");
}

TEST_F(StStreamDml, TestStreamDmlSelectConst)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(val integer, water_mark integer, event_time integer) "
                     "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(val real, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select 3.1415926, 4.99, 5.75 "
                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleDoubleSingle2(stmt, 3.5, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleDoubleSingle2(stmt, 3.5, 4, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleDoubleSingle2(stmt, 100, 12, "t1", GMC_OPERATION_SQL_INSERT);
    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    int64_t sumVal[] = {3, 3};
    int64_t avgVal[] = {4, 4};
    CheckSimpleDoubleValueWithWindow(stmt, 1, 0, 1, sumVal, avgVal);
}

TEST_F(StStreamDml, TestStreamDmlSelectDistinct)
{
    SQLExecutor executor(stmt);
    executor.ExecSQL("create table tstable1(val integer, water_mark integer, event_time integer) "
                     "with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");

    executor.ExecSQL("create stream table t1(val real, water_mark integer, event_time integer, name char(256), "
                     "watermark for event_time as event_time - INTERVAL '1' SECONDS);");

    executor.ExecSQL("create stream sink s1 as select SEQ_DISTINCT_COUNT(val), water_mark, event_time "
                     "from t1 into TSDB(tstable1) with (batch_window_size = 1)");

    StreamStructWriteSimpleDoubleSingle2(stmt, 3.78, 1, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleDoubleSingle2(stmt, 3.78, 3, "t1", GMC_OPERATION_SQL_INSERT);
    StreamStructWriteSimpleDoubleSingle2(stmt, 100, 12, "t1", GMC_OPERATION_SQL_INSERT);
    executor.ExecSQL("select * from tstable1");
    uint32_t value = 0;
    Status ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_COLS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(3u, value);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_RESULT_ROWS, &value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1u, value);
    int64_t val1[] = {2};
    int64_t val2[] = {1};
    CheckSimpleDoubleValueWithWindow(stmt, 2, 0, 1, val1, val2);
}

TEST_F(StStreamDml, TestStreamDmlAlterMultiTimes)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL(
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256), name1 text);");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select * FROM a where name > name1");
    executor.ExecSQL("ALTER STREAM VIEW abview ALTER WHERE AS event_time < 507;");
    executor.ExecSQL("ALTER STREAM VIEW abview ALTER WHERE AS event_time < 506;");
}

TEST_F(StStreamDml, TestStreamDmlAlterDistinctCnt)
{
    SQLExecutor executor(stmt);
    // 使用text变长字段建表
    executor.ExecSQL(
        "create stream table a(age integer, water_mark integer, event_time integer, name char(256), name1 text);");
    executor.ExecSQL("CREATE STREAM VIEW abView AS select SEQ_DISTINCT_COUNT(age) FROM a where name > name1");
    executor.ExecSQL("ALTER STREAM VIEW abview ALTER WHERE AS event_time < 507;");
    executor.ExecSQL("ALTER STREAM VIEW abview ALTER WHERE AS event_time < 506;");
}

TEST_F(StStreamDml, TestStreamDml_alter_fix1)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createTable[200] = "create table ts1 (id integer, name text, time integer, age integer, address char(50)) "
                            "with (time_col = 'id', interval = '1 hour');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTable, cmdLen));

    char createStream[200] =
        "create stream table t1 (id integer, name text, time integer, age integer, address char(50));";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createStreamRef1[200] = "create stream reference aref1(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef1, cmdLen));
    char createStreamRef2[200] = "create stream reference aref2(integer, integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStreamRef2, cmdLen));
    char upsertIntoStreamRef1[200] = "upsert into streamref aref1 values (15, 20);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, upsertIntoStreamRef1, cmdLen));
    char upsertIntoStreamRef2[200] = "upsert into streamref aref2 values (15, 20);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, upsertIntoStreamRef2, cmdLen));

    char createView[250] = "create stream view v1 as select id, name, REF['aref2'][time], REF['aref1'][age], address "
                           "from t1 where address "
                           "not in ('address1', 'address7') and name not in ('name2', 'name3') "
                           "with (tuple_buffer_size = 1);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView, 250));

    char createSink[200] =
        "create stream sink s1 as select id, name, ref_aref2_time, REF['aref2'][ref_aref1_age], address from v1 where "
        "id not in (18) into tsdb(ts1) with (batch_window_size = '1', tuple_buffer_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink, cmdLen));

    char alterView[200] = "alter stream view v1 alter where as REF['aref2'][time] = 16;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterView, cmdLen));

    char alterSink[200] = "alter stream sink s1 alter where as REF['aref2'][ref_aref1_age] = 16;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, alterSink, cmdLen));

    char dropSink[200] = "drop stream sink s1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink, cmdLen));
    char dropView[200] = "drop stream view v1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView, cmdLen));
    char dropRefCommand2[200] = "drop stream reference aref2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand2, cmdLen));
    char dropRefCommand1[200] = "drop stream reference aref1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropRefCommand1, cmdLen));
    char dropStream[200] = "drop stream table t1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTable[200] = "drop table ts1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTable, cmdLen));
}

TEST_F(StStreamDml, TestTSTableReboot)
{
    SQLExecutor tsExecutor(stmt);
    // 使用text变长字段建表
    tsExecutor.ExecSQL("create table tssink(water_mark integer, name char(256), format_water_mark_name char(256))"
                       " with (time_col = 'water_mark', interval = '1 hour', ttl = '1000 hours', compression = 'no');");
    {
        // 流表在结构化写后就销毁掉
        SQLExecutor streamExecutor(stmt);
        streamExecutor.ExecSQL(
            "create stream table a(age integer, water_mark integer, event_time integer, name char(256));");
        streamExecutor.ExecSQL("CREATE STREAM SINK ab AS select water_mark, name, FORMAT('abcdef') "
                               "FROM a INTO tsdb(tssink) with (batch_window_size = '1');");

        // 结构化写
        char tableName[30] = "a";
        StreamStructWriteSimpleTuple(stmt, 500, 1000, tableName, GMC_OPERATION_SQL_INSERT);
    }

    // 只校验条数
    tsExecutor.ExecSQL("select * from tssink;");

    // 重启
    DestroyConnectionAndStmt(conn, stmt);
    conn = NULL;
    stmt = NULL;
    st_clt_uninit();
    ShutDownDbServer();
    GmcDetachAllShmSeg();
    system("ls -sh /data/gmdb");
    StartDbServer((char *)cfgPersist);
    DbSleep(waitTime);
    st_clt_init();
    CreateSyncConnectionAndStmt(&conn, &stmt, 60000, NULL);

    // 重启后校验时序表条数
    tsExecutor.ExecSQL("select * from tssink;");
}

TEST_F(StStreamDml, TestStreamDmlGraphVisual001)
{
    Status ret = GMERR_OK;
    const static uint32_t cmdLen = 200;

    char createStream[200] = "create stream table a(ttt integer);";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createStream, cmdLen));

    char createView1[200] = "CREATE STREAM VIEW abView1 AS select ttt FROM a";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView1, cmdLen));
    char createView2[200] = "CREATE STREAM VIEW abView2 AS select ttt FROM a";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createView2, cmdLen));

    char createTs1[200] = "create table tssink1(ttt integer) with (time_col = 'ttt', interval = '1 hour', "
                          "ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs1, cmdLen));
    char createTs2[200] = "create table tssink2(ttt integer) with (time_col = 'ttt', interval = '1 hour', "
                          "ttl = '1000 hours', compression = 'no');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createTs2, cmdLen));

    char createSink1[200] = "CREATE STREAM SINK ab1 AS select ttt FROM abView1 INTO tsdb(tssink1) with "
                            "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink1, cmdLen));

    char createSink2[200] = "CREATE STREAM SINK ab2 AS select ttt FROM abView2 INTO tsdb(tssink2) with "
                            "(batch_window_size = '1');";
    EXPECT_EQ(ret, GmcExecDirect(stmt, createSink2, cmdLen));

    // Print the stream graph to the screen
    string args = string("gmsysview --stream --dag ");
    ASSERT_EQ(GMERR_OK, system(args.c_str()));

    char dropSink1[200] = "drop stream sink ab1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink1, cmdLen));
    char dropSink2[200] = "drop stream sink ab2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropSink2, cmdLen));
    char dropView1[200] = "drop stream view abView1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView1, cmdLen));
    char dropView2[200] = "drop stream view abView2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropView2, cmdLen));
    char dropStream[200] = "drop stream table a;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropStream, cmdLen));
    char dropTs1[200] = "drop table tssink1;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs1, cmdLen));
    char dropTs2[200] = "drop table tssink2;";
    EXPECT_EQ(ret, GmcExecDirect(stmt, dropTs2, cmdLen));
}
