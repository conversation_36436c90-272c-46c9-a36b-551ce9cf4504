{"comment": "DACL Group table", "version": "2.0", "type": "record", "name": "nac_dacl_group", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "group_name", "type": "fixed", "size": 65, "comment": "Group名"}, {"name": "class_id", "type": "uint32", "comment": "Dacl group ID"}, {"name": "ref_count", "type": "uint32", "comment": "用户引用计数"}, {"name": "rule_len", "type": "uint32", "comment": "rule数目"}, {"name": "rule", "type": "record", "array": true, "size": 128, "comment": "匹配rule", "fields": [{"name": "rule_idx", "type": "uint32", "comment": "rule的内部索引"}, {"name": "dip", "type": "uint32", "comment": "目的ip"}, {"name": "dip_mask", "type": "uint32", "comment": "目的IP长度"}, {"name": "sip", "type": "uint32", "comment": "源IP"}, {"name": "sip_mask", "type": "uint32", "comment": "源IP长度"}, {"name": "dipv6", "type": "fixed", "size": 16, "comment": "目的IPv6地址"}, {"name": "dipv6_mask", "type": "uint32", "comment": "目的IPv6地址长度"}, {"name": "sipv6", "type": "fixed", "size": 16, "comment": "源IPv6地址"}, {"name": "sipv6_mask", "type": "uint32", "comment": "源IPv6地址长度"}, {"name": "v6_flag", "type": "uint8", "comment": "V6标识"}, {"name": "protocol", "type": "uint8", "comment": "ip protocol type"}, {"name": "icmp_type", "type": "uint8", "comment": "icmp type"}, {"name": "icmp_code", "type": "uint8", "comment": "icmp code"}, {"name": "sport", "type": "uint16", "comment": "l4 src port"}, {"name": "dport", "type": "uint16", "comment": "L4 Dst Port"}, {"name": "action", "type": "uint32", "comment": "ACL action"}]}], "keys": [{"name": "dacl_group_key", "index": {"type": "primary"}, "node": "nac_dacl_group", "fields": ["group_name"], "constraints": {"unique": true}, "comment": "基于Group name的Key"}]}