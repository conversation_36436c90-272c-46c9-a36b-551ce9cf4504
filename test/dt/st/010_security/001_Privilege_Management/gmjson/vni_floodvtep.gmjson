{"comment": "vni flood-vtep的配置表项", "version": "2.0", "type": "record", "name": "vni_floodvtep", "config": {"check_validity": false}, "max_record_count": 256000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "VR索引"}, {"name": "vni_id", "type": "uint32", "comment": "VNI编号"}, {"name": "vrf_id", "type": "uint32", "comment": "VRF索引"}, {"name": "tunnel_id", "type": "uint32", "comment": "隧道ID"}, {"name": "tunnel_type", "type": "uint8", "comment": "隧道类型"}, {"name": "primary_type", "type": "uint8", "comment": "主备标记"}, {"name": "rsv1", "type": "uint16", "comment": "四字节填充保留位"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}], "keys": [{"name": "vni_floodvtep_key", "index": {"type": "primary"}, "node": "vni_floodvtep", "fields": ["vr_id", "vni_id", "vrf_id", "tunnel_id", "tunnel_type"], "constraints": {"unique": true}, "comment": "根据vni_id和tunnel key索引"}, {"name": "vni_index", "index": {"type": "hashcluster"}, "node": "vni_floodvtep", "fields": ["vr_id", "vni_id"], "constraints": {"unique": false}, "comment": "根据vni索引"}, {"name": "tunnel_index", "index": {"type": "hashcluster"}, "node": "vni_floodvtep", "fields": ["vr_id", "vrf_id", "tunnel_id", "tunnel_type"], "constraints": {"unique": false}, "comment": "根据tunnel索引"}]}