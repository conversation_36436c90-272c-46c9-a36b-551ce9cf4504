{"comment": "bfd下发芯片发送表", "version": "2.0", "type": "record", "name": "bfd_np_sess_snd", "max_record_count": 2048, "fields": [{"name": "my_discr", "type": "uint32", "comment": ""}, {"name": "pkt_len", "type": "uint8", "comment": "pkt_len"}, {"name": "digest_off", "type": "uint8", "comment": "digest_off"}, {"name": "send_cycle", "type": "uint16", "comment": "send_cycle"}, {"name": "stat_en", "type": "uint8", "comment": "stat_en"}, {"name": "bfd_type", "type": "uint8", "comment": "bfd_type"}, {"name": "link_type", "type": "uint8", "comment": "link_type"}, {"name": "pw_ttl_en", "type": "uint8", "comment": "pw_ttl_en"}, {"name": "pw_ttl", "type": "uint8", "comment": "pw_ttl"}, {"name": "send_en", "type": "uint8", "comment": "send_en"}, {"name": "is_trunk_port", "type": "uint8", "comment": "is_trunk_port"}, {"name": "peer_ip", "type": "uint32", "comment": "peer_ip"}, {"name": "vpls_pw_index", "type": "uint16", "comment": "vpls_pw_index"}, {"name": "vsi_id", "type": "uint16", "comment": "vsi_id"}, {"name": "pwe3_pw_index", "type": "uint16", "comment": "pwe3_pw_index"}, {"name": "dvp_index", "type": "uint16", "comment": "dvp_index"}, {"name": "tunnel_id", "type": "uint16", "comment": "tunnel_id"}, {"name": "re_index", "type": "uint32", "comment": "re_index"}, {"name": "nhp_index", "type": "uint32", "comment": "nhp_index"}, {"name": "bfd6_vsi_id", "type": "uint16", "comment": "bfd6_vsi_id"}, {"name": "is_force_port", "type": "uint8", "comment": "is_force_port"}, {"name": "vrf_id", "type": "uint16", "comment": "vrf_id"}, {"name": "mod", "type": "uint16", "comment": "mod"}, {"name": "port", "type": "uint32", "comment": "port"}, {"name": "tb", "type": "uint16", "comment": "tb"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "bfd_np_sess_snd_pk", "index": {"type": "primary"}, "node": "bfd_np_sess_snd", "fields": ["my_discr"], "constraints": {"unique": true}}]}