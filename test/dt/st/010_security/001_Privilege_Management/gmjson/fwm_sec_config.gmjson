{"comment": "FWM SEC CONFIG", "version": "2.0", "type": "record", "name": "fwm_sec_config", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "module", "type": "string", "size": 64}, {"name": "option", "type": "string", "size": 64}, {"name": "value", "type": "uint32"}], "keys": [{"name": "sec_conf_key", "index": {"type": "primary"}, "node": "fwm_sec_config", "fields": ["module", "option"], "constraints": {"unique": true}, "comment": "fwm_sec module name"}]}