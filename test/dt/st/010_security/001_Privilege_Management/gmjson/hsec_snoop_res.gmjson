{"comment": "hsec snoop res manage", "version": "2.0", "type": "record", "name": "hsec_snoop_res", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "unit", "type": "uint8", "comment": "芯片号"}, {"name": "resNum", "type": "uint8", "comment": "资源数"}, {"name": "type", "type": "uint8", "comment": "资源类型"}, {"name": "dest", "type": "uint32", "comment": "dest值，pktQueue或端口号等"}, {"name": "snpId", "type": "uint16", "comment": "申请到的snoop索引"}, {"name": "trapId", "type": "uint16", "comment": "trapId"}, {"name": "strength", "type": "uint32", "comment": "strength"}, {"name": "prob", "type": "uint32", "comment": "采样比"}, {"name": "probFlag", "type": "uint8", "comment": "是否采样"}], "keys": [{"name": "hsec_snoop_res_key", "index": {"type": "primary"}, "node": "hsec_snoop_res", "fields": ["unit", "resNum", "type", "dest"], "constraints": {"unique": true}, "comment": "主键索引"}]}