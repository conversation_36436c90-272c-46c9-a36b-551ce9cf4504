{"comment": "port voice-v<PERSON> config", "version": "2.0", "type": "record", "name": "port_voicevlan", "config": {"check_validity": false}, "max_record_count": 7680, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "ifIndex", "type": "uint32", "comment": "ifIndex"}, {"name": "vlanId", "type": "uint16", "comment": "vlanId"}, {"name": "mode", "type": "uint8", "comment": "mode"}, {"name": "remarkMode", "type": "uint8", "comment": "remarkMode"}, {"name": "workMode", "type": "uint8", "comment": "workMode"}, {"name": "untagged", "type": "uint8", "comment": "untagged"}, {"name": "val8021p", "type": "uint8", "comment": "val8021p"}, {"name": "dscpValue", "type": "uint8", "comment": "dscpValue"}], "keys": [{"name": "key_vrid_ifindex_index", "index": {"type": "primary"}, "node": "port_voicevlan", "fields": ["vrid", "ifIndex"], "constraints": {"unique": true}, "comment": "port_voicevlan config"}], "super_fields": [{"name": "port_voicevlan_sf", "fields": ["vrid", "ifIndex", "vlanId", "mode", "remarkMode", "workMode", "untagged", "val8021p", "dscpValue"]}]}