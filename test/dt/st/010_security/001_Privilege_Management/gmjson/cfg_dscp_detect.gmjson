{"comment": "dscp路径探测功能配置表", "version": "2.0", "type": "record", "name": "cfg_dscp_detect", "config": {"check_validity": false}, "max_record_count": 32, "fields": [{"name": "vrid", "type": "uint32", "comment": "VR索引"}, {"name": "type", "type": "int8", "comment": "探测类型,4:IPv4,6:IPv6"}, {"name": "enable", "type": "int8", "comment": "使能状态,1:使能,0:未使能"}, {"name": "dscp", "type": "int8", "comment": "dscp值"}, {"name": "rsv", "type": "fixed", "size": 1, "comment": "保留"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}], "keys": [{"name": "vrid_type", "index": {"type": "primary"}, "node": "cfg_dscp_detect", "fields": ["vrid", "type"], "constraints": {"unique": true}, "comment": "key,基于vrid和type查询"}]}