{"comment": "GROUP6_SESSION 对应78#表", "version": "2.0", "type": "record", "name": "group6_sess", "config": {"check_validity": true}, "max_record_count": 1000, "fields": [{"name": "sock_id", "type": "uint32", "comment": "socket id"}, {"name": "group_ip", "type": "fixed", "size": 16, "comment": "组播ip"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "src_addr", "type": "fixed", "size": 16, "comment": "源地址"}, {"name": "src_port", "type": "uint16", "comment": "源端口"}, {"name": "protocol", "type": "uint8", "comment": "协议类型"}, {"name": "flag", "type": "uint8", "comment": "flag"}, {"name": "app_proto_id", "type": "uint16", "comment": "应用层协议id"}, {"name": "ttl_min", "type": "uint8", "comment": "ttl 最小值"}, {"name": "ttl_max", "type": "uint8", "comment": "ttl 最大值"}, {"name": "hpid", "type": "uint32", "comment": "hpid"}, {"name": "track_id", "type": "uint32", "comment": "track id"}, {"name": "hcid", "type": "uint32", "comment": "hcid"}, {"name": "sockid_slave", "type": "uint32", "comment": "slave sockid"}, {"name": "hpid_slave", "type": "uint32", "comment": "slave hpid"}, {"name": "hcid_slave", "type": "uint32", "comment": "slave hcid"}, {"name": "vs_id", "type": "uint32", "comment": "vs id"}, {"name": "proc_type", "type": "uint32", "comment": "协议类型"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "app serial id"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和data相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}, {"name": "time_stamp_create", "type": "time", "comment": "记录表项创建及更新的时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "group6_sess", "fields": ["sock_id", "group_ip", "if_index", "src_addr", "src_port", "protocol"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "group6_sess_ripng_key", "index": {"type": "hashcluster"}, "node": "group6_sess", "fields": ["protocol", "src_port", "app_proto_id", "vs_id"], "comment": "根据ripng索引"}, {"name": "group6_sess_ospfv3_key", "index": {"type": "hashcluster"}, "node": "group6_sess", "fields": ["protocol", "flag", "app_proto_id", "vs_id"], "comment": "根据ospfv3索引"}, {"name": "group6_sess_vrrp6_key", "index": {"type": "hashcluster"}, "node": "group6_sess", "fields": ["protocol", "vs_id"], "comment": "根据vrrp6索引"}, {"name": "group6_sess_ifindex_key", "index": {"type": "hashcluster"}, "node": "group6_sess", "fields": ["if_index"], "comment": "根据ifindex索引"}, {"name": "appsourceid_localhash_key", "index": {"type": "hashcluster"}, "node": "group6_sess", "fields": ["app_source_id"], "comment": "根据app_source_id索引"}]}