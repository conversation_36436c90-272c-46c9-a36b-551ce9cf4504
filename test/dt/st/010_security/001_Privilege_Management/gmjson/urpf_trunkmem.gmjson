{"comment": "urpf 维护的trunk口对应成员口信息", "version": "2.0", "type": "record", "name": "urpf_trunkmem", "config": {"check_validity": false}, "max_record_count": 131072, "fields": [{"name": "memberIfIndex", "type": "uint32", "comment": " Eth-trunk成员端口索引"}, {"name": "trunkIfIndex", "type": "uint32", "comment": "Eth-trunk口的接口索引"}, {"name": "flag", "type": "uint32", "comment": "URPF的下设标记"}, {"name": "time_stamp_create", "type": "time", "comment": "记录表项创建及更新的时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "urpf_trunkmem", "fields": ["memberIfIndex"], "constraints": {"unique": true}, "comment": "urpf_trunkmem的主key"}, {"name": "trunkhash_key", "index": {"type": "hashcluster"}, "node": "urpf_trunkmem", "fields": ["trunkIfIndex"], "constraints": {"unique": false}, "comment": "trunk口信息对应的HASH"}]}