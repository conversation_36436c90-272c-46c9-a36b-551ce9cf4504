{"comment": "qos remark模板", "version": "2.0", "type": "record", "name": "qos_pro_remark", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "profileId", "type": "uint32", "comment": "模板ID"}, {"name": "remarkType", "type": "uint32", "comment": "优先级配置类型"}, {"name": "direction", "type": "uint32", "comment": "优先级配置方向"}, {"name": "vrId", "type": "uint32", "comment": "虚拟路由"}, {"name": "remarkValue", "type": "uint32", "comment": "优先级配置值"}], "keys": [{"name": "qos_pro_remark_key", "index": {"type": "primary"}, "node": "qos_pro_remark", "fields": ["profileId", "remarkType", "direction", "vrId"], "constraints": {"unique": true}, "comment": "key值"}]}