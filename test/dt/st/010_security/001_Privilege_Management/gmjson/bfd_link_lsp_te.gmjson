{"comment": "bfd lsp te链路信息", "version": "2.0", "type": "record", "name": "bfd_link_lsp_te", "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "vr_id", "type": "uint32", "comment": "vr_id"}, {"name": "vrf_id", "type": "uint32", "comment": "vrf_id"}, {"name": "out_ifindex", "type": "uint32", "comment": "out_ifindex"}, {"name": "tnl_type", "type": "uint8", "comment": "tnl_type"}, {"name": "bfd_flag", "type": "uint8", "comment": "bfd_flag"}, {"name": "link_flag", "type": "uint16", "comment": "link_flag"}, {"name": "tnl_id", "type": "uint32", "comment": "tnl_id"}, {"name": "tx_xc_id", "type": "uint32", "comment": "tx_xc_id"}, {"name": "fwd_vrf_id", "type": "uint32", "comment": "fwd_vrf_id"}, {"name": "lsp_xc_type", "type": "uint8", "comment": "lsp_xc_type"}, {"name": "rsv2", "type": "uint8", "comment": "rsv2"}, {"name": "rsv3", "type": "uint16", "comment": "rsv3"}, {"name": "rvs_bsid", "type": "uint32", "comment": "rvs_bsid"}, {"name": "rvs_v6_bsid", "type": "fixed", "size": 16, "comment": "rvs_v6_bsid"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "reserved field."}, {"name": "time_stamp_create", "type": "time", "comment": "table create time"}], "keys": [{"name": "bfd_link_lsp_te_key", "index": {"type": "primary"}, "node": "bfd_link_lsp_te", "fields": ["my_discr"], "constraints": {"unique": true}}, {"name": "srv6_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "bfd_link_lsp_te", "fields": ["vr_id", "vrf_id", "tx_xc_id", "lsp_xc_type"], "comment": "vrId+vrfId+xcId+xcType查找key，同于srv6 policy list场景查询是否配置bfd"}]}