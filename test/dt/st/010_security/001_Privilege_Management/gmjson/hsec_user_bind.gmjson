{"comment": "dhcp snooping绑定表", "version": "2.0", "type": "record", "name": "hsec_user_bind", "config": {"check_validity": false}, "max_record_count": 36864, "fields": [{"name": "srcMac", "type": "fixed", "size": 6, "comment": "MAC地址"}, {"name": "pVlan", "type": "uint16", "comment": "PE VLAN"}, {"name": "c<PERSON>lan", "type": "uint16", "comment": "CE VLAN"}, {"name": "prefixLen", "type": "uint8", "comment": "IPV6前缀长度"}, {"name": "isIPv6", "type": "uint8", "comment": "是否ipv6绑定表"}, {"name": "ipv6Addr", "type": "fixed", "size": 16, "comment": "IPV6地址"}, {"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "isStatic", "type": "uint8", "comment": "是否是静态绑定表"}, {"name": "checkItem", "type": "uint8", "comment": "下发硬件的key，实际就是checkItem"}, {"name": "res2", "type": "uint16", "comment": "保留字2"}, {"name": "acl", "type": "record", "array": true, "size": 2, "comment": "acl", "fields": [{"name": "entrys", "type": "record", "array": true, "size": 8, "comment": "auiEntry", "fields": [{"name": "entryId", "type": "uint32", "comment": "entry ID"}]}]}], "keys": [{"name": "hsec_user_bind_pk", "index": {"type": "primary"}, "node": "hsec_user_bind", "fields": ["srcMac", "pVlan", "c<PERSON>lan", "prefixLen", "isIPv6", "ipv6Addr", "ifIndex", "isStatic"], "constraints": {"unique": true}, "comment": "表项唯一索引"}, {"name": "if_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hsec_user_bind", "fields": ["ifIndex", "isStatic"], "constraints": {"unique": false}, "comment": "根据ifindex索引"}, {"name": "vlan_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hsec_user_bind", "fields": ["pVlan", "isStatic"], "constraints": {"unique": false}, "comment": "根据vlan索引"}, {"name": "static_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "hsec_user_bind", "fields": ["isStatic"], "constraints": {"unique": false}, "comment": "遍历所有的静态绑定表"}]}