{"comment": "gre订阅dam类", "version": "2.0", "type": "record", "name": "gre_tunnel_cfg", "max_record_count": 8192, "fields": [{"name": "if_index", "type": "uint32", "comment": "隧道接口索引"}, {"name": "tunnel_name_id", "type": "uint32", "comment": "隧道索引"}, {"name": "src_type", "type": "uint8", "comment": "隧道源类型"}, {"name": "tnl_type", "type": "uint8", "comment": "隧道类型"}, {"name": "reserved", "type": "fixed", "size": 2, "comment": "保留字段"}, {"name": "src_ip", "type": "uint32", "comment": "源IP"}, {"name": "dst_ip", "type": "uint32", "comment": "目的IP"}, {"name": "src_if_index", "type": "uint32", "comment": "隧道源接口索引"}, {"name": "dst_vrf_id", "type": "uint32", "comment": "隧道目的VRF索引"}, {"name": "status_high_prio", "type": "uint8", "comment": "高优先级表下发SVC状态"}, {"name": "status_normal_prio", "type": "uint8", "comment": "低优先级表下发SVC状态"}, {"name": "errcode_high_prio", "type": "uint8", "comment": "高优先级表下发SVC返回错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "comment": "低优先级表下发SVC返回错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 36, "comment": "保存高优先级SVC返回的资源，3个UINT32 3个UINT64数组，用来保存SVC_ENP返回的tnlId资源"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 36, "comment": "保存低优先级SVC返回的资源，用来保存SVC_HPP返回的tnlId资源"}], "keys": [{"name": "gre_tunnel_cfg_key", "index": {"type": "primary"}, "node": "gre_tunnel_cfg", "fields": ["if_index"], "constraints": {"unique": true}}]}