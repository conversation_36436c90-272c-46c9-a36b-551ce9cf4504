{"version": "2.0", "type": "record", "name": "qos_queue_sch", "config": {"check_validity": false}, "max_record_count": 9216, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口ID"}, {"name": "queue_index", "type": "uint8", "comment": "队列索引，取值范围是0～7"}, {"name": "schdle_mode", "type": "uint8", "comment": "调度模式"}, {"name": "queue_weight", "type": "uint8", "comment": "队列比重"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "qos_queue_sch_pk", "index": {"type": "primary"}, "node": "qos_queue_sch", "fields": ["ifindex", "queue_index"], "constraints": {"unique": true}, "comment": "表项唯一索引"}]}