{"comment": "端口队列拥塞管理，FES174#，667#，175#，817#", "version": "2.0", "type": "record", "name": "qos_queue_wred", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口ID"}, {"name": "queue_wred", "type": "record", "array": true, "size": 8, "fields": [{"name": "queue_index", "type": "uint8"}, {"name": "wred_id", "type": "uint8"}, {"name": "ecn_id", "type": "uint8"}], "comment": "队列wred"}, {"name": "qos_queue_shaper_id", "type": "uint32", "comment": "队伍shaper ID"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}, {"name": "vrid", "type": "uint16", "comment": "虚拟设备ID"}], "keys": [{"name": "qos_queue_wred_pk", "index": {"type": "primary"}, "node": "qos_queue_wred", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "表项唯一索引"}, {"name": "queue_wred_key", "node": "queue_wred", "fields": ["queue_index"], "constraints": {"unique": true}, "comment": "表项唯一索引"}]}