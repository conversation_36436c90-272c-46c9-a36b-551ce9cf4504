{"comment": "Table of eap_global_cfg", "version": "2.0", "type": "record", "name": "eap_global_cfg", "config": {"check_validity": false}, "max_record_count": 1, "fields": [{"name": "globalKey", "type": "uint32", "comment": "Table global key"}, {"name": "macaForceDomain", "type": "record", "nullable": true, "array": true, "size": 128, "comment": "Mac-authen force domains", "fields": [{"name": "domain", "type": "bytes", "size": 65, "comment": "domain"}, {"name": "mac", "type": "bytes", "size": 6, "comment": "mac address after mask"}, {"name": "macMask", "type": "bytes", "size": 6, "comment": "Mask of mac address"}]}, {"name": "dot1xTimerTxPeriod", "type": "uint32", "comment": "dot1x timer tx-period"}, {"name": "dot1xAbnormalTrackNum", "type": "uint32", "comment": "dot1x abnormal-track record-cache-num"}, {"name": "dot1xTimerMacBypassDelay", "type": "uint32", "comment": "dot1x timer mac-bypass delay"}, {"name": "dot1xIdentitySpdLimit", "type": "uint32", "comment": "access-user dot1x-identity speed-limit"}, {"name": "dot1xMcTrigEn", "type": "uint32", "comment": "dot1x mc-trigger"}, {"name": "dot1xMcTrigPortUpSendEn", "type": "uint32", "comment": "dot1x mc-trigger port-up-send enable"}, {"name": "dot1xQuietPeriodEn", "type": "uint32", "comment": "dot1x quiet-period"}, {"name": "dot1xQuietTimes", "type": "uint32", "comment": "dot1x quiet-times"}, {"name": "dot1xTimerQuietPeriod", "type": "uint32", "comment": "dot1x timer quiet-period"}, {"name": "macaTimerQuietPeriod", "type": "uint32", "comment": "mac-authen timer quiet-period"}, {"name": "macaQuietTimes", "type": "uint32", "comment": "mac-authen quiet-times"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "eap_global_cfg", "fields": ["globalKey"], "constraints": {"unique": true}, "comment": "Key of table"}]}