{"comment": "MAC ping acl下发信息表", "version": "2.0", "type": "record", "name": "mac_ping_state", "config": {"check_validity": false}, "max_record_count": 400, "fields": [{"name": "vrid", "type": "uint32", "comment": "VR索引"}, {"name": "src_ip", "type": "uint32", "comment": "源IPv4地址"}, {"name": "dst_ip", "type": "uint32", "comment": "目的IPv4地址"}, {"name": "src_mac", "type": "fixed", "size": 6, "comment": "源MAC"}, {"name": "dst_mac", "type": "fixed", "size": 6, "comment": "目的MAC"}, {"name": "state", "type": "uint32", "comment": "下发状态"}, {"name": "ver_no", "type": "uint32", "comment": "版本号"}, {"name": "entry_ids", "type": "record", "array": true, "size": 8, "comment": "ACL entryId数组", "fields": [{"name": "unit", "type": "uint8", "comment": "芯片号"}, {"name": "rsv", "type": "fixed", "size": 3, "comment": "保留"}, {"name": "entry_id", "type": "uint32", "comment": "ACL entry id"}]}], "keys": [{"name": "vrid_src_ip_dst_ip", "index": {"type": "primary"}, "node": "mac_ping_state", "fields": ["vrid", "src_ip", "dst_ip"], "constraints": {"unique": true}, "comment": "key,基于vr_id/src_ip/dst_ip查询"}, {"name": "vrid", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mac_ping_state", "fields": ["vrid"], "constraints": {"unique": false}, "comment": "vrid索引"}]}