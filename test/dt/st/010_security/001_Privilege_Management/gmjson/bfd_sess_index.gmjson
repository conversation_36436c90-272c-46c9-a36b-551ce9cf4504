{"comment": "bfd session index", "version": "2.0", "type": "record", "name": "bfd_sess_index", "max_record_count": 2048, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "sess_index", "type": "resource", "comment": "sess_index"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "bfd_sess_index_pk", "index": {"type": "primary"}, "node": "bfd_sess_index", "fields": ["my_discr"], "constraints": {"unique": true}}]}