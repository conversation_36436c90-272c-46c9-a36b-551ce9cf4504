{"comment": "FWM SEC CPU TABLE", "version": "2.0", "type": "record", "name": "sec_cpu_table", "config": {"check_validity": false}, "max_record_count": 8, "fields": [{"name": "mod", "type": "int8"}, {"name": "tbtp", "type": "fixed", "size": 2048}, {"name": "cpunode", "type": "fixed", "size": 1024}, {"name": "update_type", "type": "uint16"}, {"name": "update_node", "type": "uint32"}, {"name": "update_id", "type": "uint64"}, {"name": "tbtp_det", "type": "fixed", "size": 2048}, {"name": "cpunode_det", "type": "fixed", "size": 1024}], "keys": [{"name": "cputbl_key", "index": {"type": "primary"}, "node": "sec_cpu_table", "fields": ["mod"], "constraints": {"unique": true}, "comment": "cputbl id key"}]}