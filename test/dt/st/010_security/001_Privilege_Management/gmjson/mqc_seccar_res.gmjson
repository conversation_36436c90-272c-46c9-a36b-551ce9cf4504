{"version": "2.0", "type": "record", "name": "mqc_seccar_res", "config": {"check_validity": false}, "max_record_count": 256000, "fields": [{"name": "unit_id", "type": "uint32", "comment": "芯片ID"}, {"name": "inst_id", "type": "uint32", "comment": "应用实例ID"}, {"name": "share_car_idx", "type": "uint32", "comment": "car模板索引"}, {"name": "gid", "type": "uint32", "comment": "组ID"}, {"name": "local_pri", "type": "uint32", "comment": "规则优先级"}], "keys": [{"name": "seccar_key", "index": {"type": "primary"}, "node": "mqc_seccar_res", "fields": ["unit_id", "inst_id", "share_car_idx"], "constraints": {"unique": true}, "comment": "基于共享car index全匹配查找"}, {"name": "inst_scan", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mqc_seccar_res", "fields": ["unit_id", "inst_id"], "constraints": {"unique": false}, "comment": "基于应用实例遍历"}], "super_fields": [{"name": "seccar_sf", "fields": ["unit_id", "inst_id", "share_car_idx", "gid", "local_pri"]}]}