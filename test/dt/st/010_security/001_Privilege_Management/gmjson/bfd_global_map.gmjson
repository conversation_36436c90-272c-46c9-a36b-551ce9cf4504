{"comment": "bfd全局联动表", "version": "2.0", "type": "record", "name": "bfd_global_map", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "global_index", "type": "uint32", "comment": "global_index"}, {"name": "srv_switch_en", "type": "uint8", "comment": "srv_switch_en"}, {"name": "service_type", "type": "uint8", "comment": "service_type"}, {"name": "state", "type": "uint8", "comment": "state"}, {"name": "srv_index_flag", "type": "uint8", "comment": "srv_index_flag"}, {"name": "service_index", "type": "uint32", "comment": "service_index"}, {"name": "node_group_id", "type": "uint32", "comment": "node_group_id"}, {"name": "out_ifindex", "type": "uint32", "comment": "out_ifindex"}, {"name": "diag_code", "type": "uint8", "comment": "diag_code"}, {"name": "res1", "type": "uint8", "comment": "res"}, {"name": "res2", "type": "uint16", "comment": "res"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "reserved field."}, {"name": "time_stamp_create", "type": "time", "comment": "table create time"}], "keys": [{"name": "bfd_global_map_pk", "index": {"type": "primary"}, "node": "bfd_global_map", "fields": ["my_discr"], "constraints": {"unique": true}}]}