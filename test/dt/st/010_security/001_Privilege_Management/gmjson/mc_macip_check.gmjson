{"version": "2.0", "type": "record", "name": "mc_macip_check", "config": {"check_validity": false}, "max_record_count": 1, "fields": [{"name": "vrId", "type": "uint32", "comment": "VR索引"}, {"name": "enable", "type": "uint32", "comment": "使能标记"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "serviceStatus", "type": "uint32", "comment": "下发SVC状态"}, {"name": "errCode", "type": "uint32", "comment": "下发SVC错误码"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "mc_macip_check", "fields": ["vrId"], "constraints": {"unique": true}, "comment": "主键索引"}]}