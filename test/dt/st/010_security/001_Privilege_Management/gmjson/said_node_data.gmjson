{"comment": "said_frame save said-nodes' data", "version": "2.0", "type": "record", "name": "said_node_data", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "cpuId", "type": "uint16", "comment": "said-node所在的cpu节点id（slot+cpu子卡）"}, {"name": "nodeList", "type": "record", "array": true, "size": 1024, "comment": "指定cpu节点上的所有said节点", "fields": [{"name": "nodeId", "type": "uint32", "comment": "said节点id"}, {"name": "nodeName", "type": "string", "size": 32, "comment": "said节点名"}, {"name": "state", "type": "uint8", "comment": "said节点状态（disable/init/detecting/diagnosing/recovering/judging）"}, {"name": "stateTimeRec", "type": "record", "array": true, "size": 6, "comment": "记录said节点各个状态的处理时间", "fields": [{"name": "timestamp", "type": "record", "vector": true, "size": 5, "comment": "记录每个状态的5个时间戳：当前还未结束的启动时间，耗时最长一次的开始/结束时间，最后一次开始/结束时间", "fields": [{"name": "year", "type": "uint16"}, {"name": "month", "type": "uint8"}, {"name": "date", "type": "uint8"}, {"name": "hour", "type": "uint8"}, {"name": "minute", "type": "uint8"}, {"name": "second", "type": "uint8"}, {"name": "week", "type": "uint8"}, {"name": "milliSec", "type": "uint32"}], "super_fields": [{"name": "timeStamp", "fields": ["year", "month", "date", "hour", "minute", "second", "week", "milliSec"]}]}, {"name": "runCount", "type": "uint32", "comment": "该状态运行过的总次数"}, {"name": "maxRun", "type": "uint32", "comment": "最长的运行时间（ms）"}, {"name": "lastRun", "type": "uint32", "comment": "最后一次的运行时间"}, {"name": "totalRun", "type": "uint64", "comment": "总共运行时间"}, {"name": "cntOver10ms", "type": "uint32", "comment": "运行时间超过10ms的次数"}, {"name": "cntOver50ms", "type": "uint32", "comment": "运行时间超过50ms的次数"}]}, {"name": "reason", "type": "uint32", "comment": "最后一次故障的原因"}, {"name": "recoverLevel", "type": "uint8", "comment": "最后一次故障的重复恢复（恢复后判断还有故障，需要重新恢复）次数"}, {"name": "result", "type": "uint8", "comment": "最后一次故障的恢复结果成功、失败）"}, {"name": "recoverTotalCnt", "type": "uint32", "comment": "总恢复次数"}, {"name": "recoverCntIn24h", "type": "uint32", "comment": "过去24小时内的总复位次数"}, {"name": "recoverReasonNum", "type": "uint32", "comment": "复位原因类别数目"}, {"name": "recoverReason", "type": "bytes", "size": 1024, "comment": "每种复位原因在过去24h内的恢复（复位）次数"}, {"name": "nodeDataLen", "type": "uint32", "comment": "said框架代said节点备份的数据长度"}, {"name": "nodeData", "type": "bytes", "size": 13312, "comment": "备份的节点数据"}, {"name": "recoverTimer", "type": "uint16", "comment": "监控节点recover过程的定时器"}, {"name": "seq", "type": "uint32", "comment": "主备板DB数据同步的标记"}, {"name": "crc", "type": "uint32", "comment": "用于比较主备板数据一致性"}], "super_fields": [{"name": "lastFaultInfo", "fields": ["reason", "recoverLevel", "result"]}]}, {"name": "version", "type": "uint16", "comment": "用于版本升级比较"}], "keys": [{"name": "pkey", "index": {"type": "primary"}, "node": "said_node_data", "fields": ["cpuId"], "constraints": {"unique": true}, "comment": "查找一个cpu节点上的所有said节点数据"}, {"name": "nodeKey", "node": "nodeList", "fields": ["nodeId"], "constraints": {"unique": true}, "comment": "查找一个said节点数据的key"}]}