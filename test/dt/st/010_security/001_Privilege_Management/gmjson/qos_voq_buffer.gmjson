{"comment": "QOS voq buffer information", "version": "2.0", "type": "record", "name": "qos_voq_buffer", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifIndex", "type": "uint32"}, {"name": "cellSize", "type": "uint32"}, {"name": "isSelf", "type": "uint32"}, {"name": "voq_buffer", "type": "record", "array": true, "size": 8, "fields": [{"name": "used", "type": "uint32"}, {"name": "total", "type": "uint32"}]}], "keys": [{"name": "qos_voq_buffer_pk", "index": {"type": "primary"}, "node": "qos_voq_buffer", "fields": ["ifIndex"], "constraints": {"unique": true}}]}