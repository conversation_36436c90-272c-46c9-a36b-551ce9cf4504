{"comment": "capset_esnitem", "version": "2.0", "type": "record", "name": "capset_esnitem", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "chassisid", "type": "uint8", "comment": "框ID"}, {"name": "slotid", "type": "uint16", "comment": "槽位ID"}, {"name": "fruid", "type": "uint8", "comment": "FRU ID"}, {"name": "esnitem", "type": "string", "size": 32, "comment": "电子标签item"}], "keys": [{"name": "capset_esnitem_pk", "index": {"type": "primary"}, "node": "capset_esnitem", "fields": ["chassisid", "slotid", "fruid"], "constraints": {"unique": true}, "comment": "用来查询电子标签item的key组合"}]}