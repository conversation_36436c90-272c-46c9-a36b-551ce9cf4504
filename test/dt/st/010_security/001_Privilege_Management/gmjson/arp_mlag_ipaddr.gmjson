{"comment": "arp_mlag_ipaddr表", "version": "2.0", "type": "record", "name": "arp_mlag_ipaddr", "max_record_count": 40960, "fields": [{"name": "vr_id", "type": "uint32", "comment": "vrId"}, {"name": "if_type", "type": "uint32", "comment": "接口类型,vlan or vbdif"}, {"name": "vlan_id", "type": "uint32", "comment": "vlanId"}, {"name": "vni_id", "type": "uint32", "comment": "vniId"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "src_ip", "type": "uint32", "comment": "本端ip地址"}, {"name": "src_mask_len", "type": "uint32", "comment": "本端ip地址掩码长度"}, {"name": "peer_ip", "type": "uint32", "comment": "对端ip地址"}, {"name": "peer_mask_len", "type": "uint32", "comment": "对端ip地址掩码长度"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识,对应VRP8 hSrcPid字段"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号, 用于跟踪同一条记录的变化情形"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "arp_mlag_ipaddr_key", "index": {"type": "primary"}, "node": "arp_mlag_ipaddr", "fields": ["vr_id", "if_type", "vlan_id", "vni_id"], "constraints": {"unique": true}, "comment": "主索引"}, {"name": "arp_mlag_ipaddr_if_index_index", "index": {"type": "hashcluster"}, "node": "arp_mlag_ipaddr", "fields": ["if_index"], "constraints": {"unique": false}, "comment": "根据if_index索引arp_mlag_ipaddr表"}]}