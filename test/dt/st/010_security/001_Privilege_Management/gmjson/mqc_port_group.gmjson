{"version": "2.0", "type": "record", "name": "mqc_port_group", "config": {"check_validity": false}, "max_record_count": 128, "fields": [{"name": "vr_id", "type": "uint32", "comment": "虚拟路由"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "group_id", "type": "uint32", "comment": "组ID"}, {"name": "if_type", "type": "uint32", "comment": "接口类型"}], "keys": [{"name": "mqc_port_group_key", "index": {"type": "primary"}, "node": "mqc_port_group", "fields": ["vr_id", "if_index", "group_id"], "constraints": {"unique": true}, "comment": "端口组名称"}], "super_fields": [{"name": "portgroup_key", "fields": ["vr_id", "if_index", "group_id", "if_type"]}]}