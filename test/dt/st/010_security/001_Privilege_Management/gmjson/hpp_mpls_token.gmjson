{"comment": "分配mpls token索引", "version": "2.0", "type": "record", "name": "hpp_mpls_token_index", "config": {"check_validity": false}, "max_record_count": 143360, "fields": [{"name": "vrIndex", "type": "uint32", "comment": "vs索引"}, {"name": "vrfIndex", "type": "uint32", "comment": "vpninstance索引"}, {"name": "nhlfeId", "type": "uint32", "comment": "xc类型，MPLS NHP GROUP 使用"}, {"name": "lspXcIndex", "type": "uint32", "comment": "xc索引，MPLS NHP GROUP 使用"}, {"name": "lspXcType", "type": "uint8", "comment": "NhlfeId，MPLS NHP GROUP 使用"}, {"name": "mplsToken", "type": "resource", "comment": "hpp分配mpls token索引"}], "keys": [{"name": "primerykey", "index": {"type": "primary"}, "node": "hpp_mpls_token_index", "fields": ["vrIndex", "vrfIndex", "nhlfeId", "lspXcIndex", "lspXcType"], "constraints": {"unique": true}, "comment": "hpp分配mpls token索引"}]}