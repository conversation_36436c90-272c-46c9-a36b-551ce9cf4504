{"comment": "对应FES#4466", "version": "2.0", "type": "record", "name": "pp4ext_srv6_te", "config": {"check_validity": true}, "max_record_count": 4000000, "fields": [{"name": "iid_index", "type": "uint32", "comment": "iid索引"}, {"name": "vr_index", "type": "uint32", "comment": "vr索引"}, {"name": "tvrf_index", "type": "uint32", "comment": "隧道vrf索引"}, {"name": "tunnel_id", "type": "uint32", "comment": "隧道ID"}, {"name": "vpn_sid", "type": "fixed", "size": 16, "comment": "vpnsid"}, {"name": "tunne_type", "type": "uint8", "comment": "隧道类型"}, {"name": "iid_type", "type": "uint8", "comment": "IID类型标识VPN/EVPN/FLOW"}, {"name": "rsv1", "type": "uint16"}, {"name": "vrf_Index", "type": "uint32", "comment": "vrf索引"}, {"name": "origin_nhp", "type": "fixed", "size": 16, "comment": "原始下一跳"}, {"name": "src_pid", "type": "uint32", "comment": "表项生产者PID"}, {"name": "flags", "type": "uint32"}, {"name": "status_high_prio", "type": "uint8", "comment": "SERVICE高优先级下发状态,1对应成功,如下发错误,错误状态见errcode_high_prio"}, {"name": "status_normal_prio", "type": "uint8", "comment": "SERVICE普通优先级下发状态,1对应成功,如下发错误,错误状态见errcode_normal_prio"}, {"name": "errcode_high_prio", "type": "uint8", "comment": "SERVICE高优先级下发状态错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "comment": "SERVICE普通优先级下发状态错误码"}, {"name": "rsv2", "type": "uint16"}, {"name": "next_nhp_type_high_prio", "type": "uint8", "nullable": true, "comment": "关联表项的高优先级的nhp类型,0为单下一跳,1为ecmp,2为frr"}, {"name": "next_nhp_type_normal_prio", "type": "uint8", "nullable": true, "comment": "关联表项的普通优先级的nhp类型,0为单下一跳,1为ecmp,2为frr"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "高优先级FWM_SERVICE返回的svcCtx"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "普通优先级FWM_SERVICE返回的svcCtx"}, {"name": "next_svc_ctx_high_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "关联的高优先级FWM_SERVICE的svcCtx"}, {"name": "next_svc_ctx_normal_prio", "type": "fixed", "size": 16, "default": "0xffffffffffffffffffffffffffffffff", "comment": "关联的普通优先级FWM_SERVICE的svcCtx"}, {"name": "table_smooth_id", "type": "uint32", "comment": "表项平滑版本号"}, {"name": "time_stamp_create", "type": "time", "comment": "表项创建的时间戳"}, {"name": "time_stamp_smooth", "type": "time", "comment": "表项对账的时间戳"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "pp4ext_srv6_te", "fields": ["iid_index", "vr_index", "tvrf_index", "tunnel_id", "vpn_sid", "tunne_type"], "constraints": {"unique": true}}, {"name": "iidindex_localhash_key", "index": {"type": "hashcluster"}, "node": "pp4ext_srv6_te", "fields": ["iid_index"], "constraints": {"unique": false}, "comment": "根据iid_index索引"}, {"name": "policy_localhash_key", "index": {"type": "hashcluster"}, "node": "pp4ext_srv6_te", "fields": ["vr_index", "tvrf_index", "tunnel_id", "tunne_type"], "constraints": {"unique": false}, "comment": "根据 Policy 索引"}]}