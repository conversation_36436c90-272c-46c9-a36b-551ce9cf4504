{"comment": "bfd sess create表", "version": "2.0", "type": "record", "name": "bfd_sess_create", "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "vs_id", "type": "uint32", "comment": "vr_id"}, {"name": "ctrl_flag", "type": "uint32", "comment": "defend_type"}, {"name": "if_index", "type": "uint32", "comment": "defend_src_ip"}, {"name": "vc_label", "type": "uint32", "comment": "defend_dst_ip"}, {"name": "vni", "type": "uint32", "comment": "vni"}, {"name": "agg_group_id", "type": "uint16", "comment": "agg_group_id"}, {"name": "port_ptp_id", "type": "uint16", "comment": "port_ptp_id"}, {"name": "group_flow_idx", "type": "uint32", "comment": "group_flow_idx"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "reserved field"}], "keys": [{"name": "bfd_sess_create_pk", "index": {"type": "primary"}, "node": "bfd_sess_create", "fields": ["my_discr", "vs_id"], "constraints": {"unique": true}}]}