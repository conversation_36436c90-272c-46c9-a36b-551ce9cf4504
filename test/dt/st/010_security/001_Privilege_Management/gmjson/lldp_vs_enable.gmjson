{"version": "2.0", "type": "record", "name": "lldp_vs_enable", "config": {"check_validity": false}, "max_record_count": 9216, "fields": [{"name": "ifindex", "type": "uint32"}, {"name": "vrid", "type": "uint32"}, {"name": "app_source_id", "type": "uint32"}, {"name": "app_serial_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}], "keys": [{"name": "lldp_vs_enable_pk", "index": {"type": "primary"}, "node": "lldp_vs_enable", "fields": ["ifindex"], "constraints": {"unique": true}}]}