{"comment": "ifm_system_mac表", "version": "2.0", "type": "record", "name": "ifm_system_mac", "max_record_count": 1000000, "fields": [{"name": "vsid", "type": "uint32", "comment": "vsid"}, {"name": "sys_mac", "type": "fixed", "size": 6, "comment": "sys_mac 系统mac"}, {"name": "ref_count", "type": "uint32", "comment": "引用计数 一个sysmac可能对应对个ifindex"}, {"name": "smac_index", "type": "resource", "comment": "smac_index资源"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "ifm_system_mac", "fields": ["vsid", "sys_mac"], "constraints": {"unique": true}}]}