{"comment": "dns全局配置", "version": "2.0", "type": "record", "name": "dns_global_cfg", "config": {"check_validity": false}, "max_record_count": 20000, "fields": [{"name": "vsys_id", "type": "uint32", "comment": "虚系统ID，DNS不支持默认为0"}, {"name": "resolve_enable", "type": "uint8", "default": 0, "comment": "dns动态域名解析功能开关"}], "keys": [{"name": "dns_global_cfg_pk", "index": {"type": "primary"}, "node": "dns_global_cfg", "fields": ["vsys_id"], "constraints": {"unique": true}, "comment": "表项唯一索引"}]}