{"comment": "分配ipv6下一跳索引", "version": "2.0", "type": "record", "name": "hpp_nhp6_index", "config": {"check_validity": false}, "max_record_count": 143360, "fields": [{"name": "user_type", "type": "uint8", "comment": "类型"}, {"name": "nhp_flag", "type": "uint8", "comment": "标志位"}, {"name": "vs_id", "type": "uint32", "comment": "vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "vpninstance索引"}, {"name": "next_hop6", "type": "fixed", "size": 16, "comment": "fib6下一跳"}, {"name": "out_ifindex", "type": "uint32", "comment": "出接口"}, {"name": "nhp6_index", "type": "resource", "comment": "hpp分配nhp6索引"}], "keys": [{"name": "primerykey", "index": {"type": "primary"}, "node": "hpp_nhp6_index", "fields": ["user_type", "nhp_flag", "vs_id", "vrf_index", "next_hop6", "out_ifindex"], "constraints": {"unique": true}, "comment": "hpp分配nhp6索引"}]}