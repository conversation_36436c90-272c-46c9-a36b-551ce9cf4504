{"comment": "capset_bdmtype", "version": "2.0", "type": "record", "name": "capset_bdmtype", "config": {"check_validity": false}, "max_record_count": 65535, "fields": [{"name": "esn1", "type": "string", "size": 16, "comment": "电子标签1"}, {"name": "esn2", "type": "string", "size": 16, "comment": "电子标签2"}, {"name": "cpuid", "type": "uint8", "comment": "硬件CPU编号"}, {"name": "format_version", "type": "uint8", "comment": "data的格式版本"}, {"name": "data", "type": "bytes", "size": 2048, "comment": "数据空间"}, {"name": "len", "type": "uint16", "comment": "data的实际长度"}], "keys": [{"name": "capset_bdmtype_pk", "index": {"type": "primary"}, "node": "capset_bdmtype", "fields": ["esn1", "esn2", "cpuid"], "constraints": {"unique": true}, "comment": "board的能力集ID和电子标签的映射关系"}]}