{"comment": "l2mc bd 使能数据信息, VRP fes 1310 L2MC_BD_ENABLE", "version": "2.0", "type": "record", "name": "l2mc_bd_enable", "config": {"check_validity": false}, "max_record_count": 65536, "fields": [{"name": "vrIndex", "type": "uint32", "comment": "VS索引"}, {"name": "bdId", "type": "uint32", "comment": "bd 索引"}, {"name": "addrFamily", "type": "uint32", "comment": "地址族，标记是IPv4   L2MC使能还是IPv6   L2MC使能（1：IPv4；2：IPv6）"}, {"name": "vrfIndex", "type": "uint32", "comment": "vrf索引, 对l2mc 暂时未使用, 预留"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "igmpVersion", "type": "uint32", "comment": "配置的IGMP协议版本号（1：V1版本；2：V2版本；3：V3版本）"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "appVersion", "type": "uint32", "comment": "对账版本号，与APP对账使用"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "下发SVC状态"}, {"name": "errCode", "type": "fixed", "size": 2, "comment": "下发SVC错误码，包括svc enp和svc hpp"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}, {"name": "l2bdEnable", "type": "uint32", "comment": "bd内igmp Snooping标记位（0：未使能，1：使能）"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l2mc_bd_enable", "fields": ["vrIndex", "bdId", "addrFamily"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "srcpid_key", "index": {"type": "hashcluster"}, "node": "l2mc_bd_enable", "fields": ["vrIndex", "vrfIndex", "appSrcPid"], "constraints": {"unique": false}, "comment": "老化查询索引（vrId, vrfId, appSrcPid）"}]}