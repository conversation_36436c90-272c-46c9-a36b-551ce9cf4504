{"comment": "vlan流量统计", "version": "2.0", "type": "record", "name": "vlan_stat_info", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid"}, {"name": "vlan_id", "type": "uint16", "comment": "802.1q vlan 信息"}, {"name": "unit", "type": "uint8", "comment": "芯片id"}, {"name": "vsi_index", "type": "uint32", "comment": "vsi转发资源索引"}, {"name": "static_enable", "type": "uint16", "comment": "vlan流量统计使能标记"}, {"name": "direction", "type": "uint8", "comment": "vlan流量统计方向"}, {"name": "ingress_entry_id", "type": "uint32", "comment": "上行统计entry"}, {"name": "egress_entry_id", "type": "uint32", "comment": "下行统计entry"}, {"name": "ingress_engine_id", "type": "uint32", "comment": "上行统计entry"}, {"name": "egress_engine_id", "type": "uint32", "comment": "下行统计entry"}], "keys": [{"name": "vlan_stat_info_pk", "index": {"type": "primary"}, "node": "vlan_stat_info", "fields": ["vrid", "vlan_id", "unit"], "constraints": {"unique": true}}], "super_fields": [{"name": "vlan_stat_info_sf", "fields": ["vrid", "vlan_id", "unit", "vsi_index", "static_enable", "direction", "ingress_entry_id", "egress_entry_id", "ingress_engine_id", "egress_engine_id"]}]}