{"comment": "NVE接口是否使能DSCP继承功能", "version": "2.0", "type": "record", "name": "qos_vxlan_dscp", "config": {"check_validity": false}, "max_record_count": 1, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "vrId", "type": "uint32", "comment": "虚拟路由"}, {"name": "isNable", "type": "uint32", "comment": "是否使能DSCP继承功能"}, {"name": "sourceIp", "type": "uint32", "comment": "源IP"}, {"name": "dsId", "type": "uint32", "comment": "diffserv domain id"}, {"name": "rsv", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "qos_vxlan_dscp_key", "index": {"type": "primary"}, "node": "qos_vxlan_dscp", "fields": ["ifIndex", "vrId"], "constraints": {"unique": true}, "comment": "NVE接口是否使能DSCP继承功能"}]}