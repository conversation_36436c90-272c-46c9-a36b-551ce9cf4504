{"comment": "4over6隧道表", "version": "2.0", "type": "record", "name": "tunnel_4over6", "config": {"check_validity": false}, "max_record_count": 1048576, "fields": [{"name": "vrIndex", "type": "uint32", "comment": "VS索引"}, {"name": "vrfIndex", "type": "uint32", "comment": "VRF索引"}, {"name": "tnlId", "type": "uint32", "comment": "隧道ID"}, {"name": "tnlType", "type": "uint8", "comment": "隧道类型， 5是v4 GRE，40是v6 GRE"}, {"name": "tnlStatus", "type": "uint8", "comment": "隧道物理状态"}, {"name": "encapLimit", "type": "uint8", "comment": "封装限制"}, {"name": "hopLimit", "type": "uint8", "comment": "HopLimit"}, {"name": "trafficClass", "type": "uint8"}, {"name": "original", "type": "uint8"}, {"name": "pathflag", "type": "uint16"}, {"name": "flowLable", "type": "uint32", "comment": "流标签"}, {"name": "protoStatus", "type": "uint8", "comment": "隧道协议状态"}, {"name": "resv1", "type": "uint8", "comment": "保留字段"}, {"name": "resv2", "type": "uint16", "comment": "保留字段"}, {"name": "srcIP6", "type": "fixed", "size": 16, "comment": "隧道源ipv6地址"}, {"name": "dstIP6", "type": "fixed", "size": 16, "comment": "隧道目的ipv6地址"}, {"name": "ifIndex", "type": "uint32", "comment": "接口索引"}, {"name": "pid4o6", "type": "uint32", "comment": "4over6组件ID"}, {"name": "srcIfIndex", "type": "uint32", "comment": "源接口索引"}, {"name": "dstVrfId", "type": "uint32", "comment": "目的地址绑定的VRF"}, {"name": "srcPid", "type": "uint32", "comment": "来源组件ID"}, {"name": "verNo", "type": "uint32", "comment": "版本号"}, {"name": "appVersion", "type": "uint64", "comment": "对账版本号，与APP对账使用"}, {"name": "smoothVersion", "type": "uint64", "comment": "平滑版本号，主备倒换各板表项平滑使用"}, {"name": "flags", "type": "uint32", "comment": "完备标志，关联111号表获得接口索引"}, {"name": "nhpType", "type": "uint32", "comment": "ECMP或单下一跳类型"}, {"name": "nhpGroupID", "type": "fixed", "size": 8, "comment": "隧道目的关联的NHP/NHPGROUP索引，保存高低优先级两个索引"}, {"name": "status_high_prio", "type": "uint8", "comment": "高优先级表下发SVC状态"}, {"name": "status_normal_prio", "type": "uint8", "comment": "低优先级表下发SVC状态"}, {"name": "errcode_high_prio", "type": "uint8", "comment": "高优先级表下发SVC返回错误码"}, {"name": "errcode_normal_prio", "type": "uint8", "comment": "低优先级表下发SVC返回错误码"}, {"name": "svc_ctx_high_prio", "type": "fixed", "size": 24, "comment": "保存高优先级SVC返回的资源，6个UINT32数组，ORCH层只保存，不做解释，SVC_ENP层分别将DvpIndex、SvpIndex、AIB2Index及各资源对应的版本保存其中"}, {"name": "svc_ctx_normal_prio", "type": "fixed", "size": 24, "comment": "保存低优先级SVC返回的资源，ORCH层只保存，不做解释"}, {"name": "time_stamp_create", "type": "time", "comment": "记录表项创建时的时间"}], "keys": [{"name": "tunnel_4over6_key", "index": {"type": "primary"}, "node": "tunnel_4over6", "fields": ["vrIndex", "vrfIndex", "tnlId", "tnlType"], "constraints": {"unique": true}}, {"name": "tunnel_4over6_ifindex_key", "index": {"type": "hashcluster"}, "node": "tunnel_4over6", "fields": ["ifIndex"], "constraints": {"unique": false}}, {"name": "tunnel_4over6_vrfid_key", "index": {"type": "hashcluster"}, "node": "tunnel_4over6", "fields": ["vrIndex", "vrfIndex", "srcPid"], "constraints": {"unique": false}}]}