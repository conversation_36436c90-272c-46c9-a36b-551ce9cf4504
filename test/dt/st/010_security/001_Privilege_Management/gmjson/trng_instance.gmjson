{"comment": "Time Range Instance表", "version": "2.0", "type": "record", "name": "trng_instance", "config": {"check_validity": true}, "max_record_count": 65535, "fields": [{"name": "trngid", "type": "uint32", "comment": "Time Range的索引"}, {"name": "trngname", "type": "string", "size": 33, "comment": "Time Range的名称"}, {"name": "vsysid", "type": "uint16", "comment": "为防火墙提供，vsys索引"}, {"name": "vrid", "type": "uint16", "comment": "虚拟路由器的索引"}], "keys": [{"name": "trng_instance_pk", "index": {"type": "primary"}, "node": "trng_instance", "fields": ["trngid"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "trng_name_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "trng_instance", "fields": ["trngname", "vsysid", "vrid"], "constraints": {"unique": true}, "comment": "根据trngname、vsysid和vrid索引"}]}