{"version": "2.0", "type": "record", "name": "ifm_if_forceup", "config": {"check_validity": false}, "max_record_count": 524288, "fields": [{"name": "sourceId", "type": "uint32"}, {"name": "ifIndex", "type": "uint32"}, {"name": "phyStateFlag", "type": "uint8"}], "keys": [{"name": "if_forceup_key", "index": {"type": "primary"}, "node": "ifm_if_forceup", "fields": ["sourceId", "ifIndex"], "constraints": {"unique": true}}]}