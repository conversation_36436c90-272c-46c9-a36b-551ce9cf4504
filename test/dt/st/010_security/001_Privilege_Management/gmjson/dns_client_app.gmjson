{"comment": "dns client app转发表", "version": "2.0", "type": "record", "name": "dns_client_app", "config": {"check_validity": false}, "max_record_count": 20000, "fields": [{"name": "msg_para", "type": "uint32", "comment": "ipv4 dns服务器地址"}, {"name": "module_flag", "type": "uint16", "comment": "模块的标识"}, {"name": "domain_name", "type": "string", "size": 256, "comment": "域名"}, {"name": "protocol", "type": "uint8", "comment": "协议类型，Bit0=1 UDP，Bit1=1 TCP；Bit2=1 SCTP；0：无效"}, {"name": "service", "type": "uint8", "comment": "业务类型 Bit0=1 SIP；Bit1=1 SIPS；0：无效"}, {"name": "domain_type", "type": "uint32", "comment": "查询类型A 或者 AAAA"}, {"name": "vpn_id", "type": "uint32", "comment": "vpn Id值"}, {"name": "retry_time", "type": "uint32", "comment": "重传的时间，系统本地时间"}, {"name": "forward_id", "type": "uint16", "comment": "转发查询报文的packet-id"}, {"name": "retry_count", "type": "uint8", "comment": "重传次数"}, {"name": "staticip_flag", "type": "uint8", "comment": "静态查询结果标识"}, {"name": "send_to_v4_count", "type": "fixed", "size": 6, "comment": "记录向每台IPV4 DNS服务器的发送次数"}, {"name": "send_to_v6_count", "type": "fixed", "size": 6, "comment": "记录向每台IPV4 DNS服务器的发送次数"}, {"name": "send_success_count", "type": "uint8", "comment": "记录发送到成功回应报文的server的次数"}, {"name": "update_time", "type": "uint32", "comment": "表项刷新时间"}, {"name": "address_num", "type": "uint8", "comment": "解析得到的地址数目"}, {"name": "socket_index", "type": "uint32", "comment": "存储随机发包的socket索引"}, {"name": "server_index", "type": "uint32", "comment": "v4服务器的索引"}, {"name": "serverv6_index", "type": "uint32", "comment": "v6服务器的索引"}, {"name": "server_type", "type": "uint32", "comment": "报文发到的服务器类型v4、v6"}, {"name": "address_info", "type": "record", "array": true, "size": 4, "comment": "转发表中储存的IP地址等信息", "fields": [{"name": "ipv6_addr", "type": "fixed", "size": 16, "comment": "ipv6地址"}, {"name": "ipv4_addr", "type": "uint32", "comment": "ipv4地址"}, {"name": "ttl", "type": "uint32", "comment": "生存时间"}]}, {"name": "request_info", "type": "record", "array": true, "size": 32, "comment": "转发表中请求模块的信息", "fields": [{"name": "module_type", "type": "uint32", "comment": "dns 请求模块的类型"}, {"name": "session_id", "type": "uint32", "comment": "会话标识"}, {"name": "need_refresh", "type": "uint8", "comment": "域名请求数据是否需要刷新标志"}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "dns_client_app", "fields": ["msg_para", "module_flag", "domain_name", "protocol", "service", "domain_type", "vpn_id"], "constraints": {"unique": true}, "comment": "表项唯一索引"}, {"name": "address_info_key", "node": "address_info", "fields": ["ipv6_addr", "ipv4_addr"], "constraints": {"unique": true}, "comment": "子节点存储地址信息的key值"}, {"name": "request_info_key", "node": "request_info", "fields": ["module_type", "session_id"], "constraints": {"unique": true}, "comment": "子节点模块请求信息的key值"}]}