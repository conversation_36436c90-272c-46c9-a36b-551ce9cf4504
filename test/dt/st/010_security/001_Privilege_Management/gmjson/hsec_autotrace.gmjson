{"version": "2.0", "type": "record", "name": "hsec_autotrace", "config": {"check_validity": false}, "max_record_count": 48, "fields": [{"name": "vrid", "type": "uint32", "comment": "vrid暂时未用"}, {"name": "secPolicyId", "type": "uint32", "comment": "策略ID"}, {"name": "enableFlag", "type": "uint32", "comment": "使能标记"}, {"name": "sampleValue", "type": "uint32", "comment": "采样速率"}, {"name": "intervalValue", "type": "uint32", "comment": "采样时间"}], "keys": [{"name": "hsec_autotrace_index", "index": {"type": "primary"}, "node": "hsec_autotrace", "fields": ["vrid", "secPolicyId"], "constraints": {"unique": true}, "comment": "主键索引"}]}