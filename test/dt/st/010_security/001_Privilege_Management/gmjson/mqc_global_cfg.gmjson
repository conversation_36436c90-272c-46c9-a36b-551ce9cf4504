{"comment": "mqc全局配置表", "version": "2.0", "type": "record", "name": "mqc_global_cfg", "config": {"check_validity": false}, "max_record_count": 1, "fields": [{"name": "index", "type": "uint16", "comment": "索引"}, {"name": "atomic_mode", "type": "uint8", "comment": "原子更新"}, {"name": "chip_base_mode", "type": "uint8", "comment": "基于芯片精确下发"}], "keys": [{"name": "key", "index": {"type": "primary"}, "node": "mqc_global_cfg", "fields": ["index"], "constraints": {"unique": true}, "comment": "全局配置Key"}], "super_fields": [{"name": "global_cfg", "fields": ["index", "atomic_mode", "chip_base_mode"]}]}