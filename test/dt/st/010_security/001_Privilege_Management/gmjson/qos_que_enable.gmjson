{"comment": "logic port queue capability", "version": "2.0", "type": "record", "name": "qos_que_enable", "config": {"check_validity": false}, "max_record_count": 42, "fields": [{"name": "vrid_id", "type": "uint32", "comment": "虚拟路由"}, {"name": "ifindex", "type": "uint32", "comment": "接口索引"}, {"name": "queflag", "type": "uint32", "comment": "队列使能"}, {"name": "iftype", "type": "uint32", "comment": "队列使能"}, {"name": "srcPid", "type": "uint32", "comment": "保留字段"}, {"name": "serialId", "type": "uint32", "comment": "保留字段"}, {"name": "objId", "type": "uint64", "comment": "保留字段"}, {"name": "verNo", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "que_enable_pk", "index": {"type": "primary"}, "node": "qos_que_enable", "fields": ["vrid_id", "ifindex"], "constraints": {"unique": true}, "comment": "主键索引"}]}