{"version": "2.0", "type": "record", "name": "hsec_ipsg_intf", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "vrId", "type": "uint32"}, {"name": "ifIndex", "type": "uint32"}, {"name": "checkItem", "type": "uint8"}, {"name": "alarmEn", "type": "uint8"}, {"name": "alarmThrd", "type": "uint16"}, {"name": "v4enable", "type": "uint8"}, {"name": "v6enable", "type": "uint8"}, {"name": "res", "type": "uint16"}, {"name": "acl", "type": "record", "array": true, "size": 2, "comment": "acl", "fields": [{"name": "entrys", "type": "record", "array": true, "size": 8, "comment": "auiEntry", "fields": [{"name": "entryId", "type": "uint32", "comment": "entry ID"}]}]}, {"name": "logTime", "type": "uint32"}, {"name": "alarmTime", "type": "uint32"}, {"name": "alarmDropCnt", "type": "uint64"}, {"name": "totalDropCnt", "type": "uint64"}], "keys": [{"name": "hsec_ipsg_intf_pk", "index": {"type": "primary"}, "node": "hsec_ipsg_intf", "fields": ["vrId", "ifIndex"], "constraints": {"unique": true}}]}