{"comment": "保存当前单板发生丢包的端口", "version": "2.0", "type": "record", "name": "qos_ingress_if", "config": {"check_validity": false}, "max_record_count": 512, "fields": [{"name": "ifIndex", "type": "uint32", "comment": "映射索引"}, {"name": "slot", "type": "uint32", "comment": "槽位号"}, {"name": "unit", "type": "uint32", "comment": "unit"}], "keys": [{"name": "qos_ingress_if_pk", "index": {"type": "primary"}, "node": "qos_ingress_if", "fields": ["ifIndex"], "constraints": {"unique": true}, "comment": "主键"}, {"name": "unit_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "qos_ingress_if", "fields": ["slot", "unit"], "constraints": {"unique": false}, "comment": "用于基于芯片读取丢包的端口"}]}