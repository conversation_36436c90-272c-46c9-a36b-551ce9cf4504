{"version": "2.0", "type": "record", "name": "Interface_Ipv6Route_S", "config": {"check_validity": false}, "max_record_count": 655400, "fields": [{"name": "entry_index", "type": "uint64", "auto_increment": true, "comment": "唯一主键索引"}, {"name": "vrf_id", "type": "uint32", "comment": "前缀地址所在vrf"}, {"name": "prefix", "type": "fixed", "size": 16, "comment": "前缀地址"}, {"name": "nhp_vrf_id", "type": "uint32", "comment": "下一跳地址所在vrf"}, {"name": "next_hop", "type": "fixed", "size": 16, "comment": "下一跳地址"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "mask_len", "type": "uint8", "comment": "掩码长度"}, {"name": "route_type", "type": "uint8", "comment": "路由类型"}, {"name": "preference", "type": "uint8", "comment": "优先级"}, {"name": "reserve", "type": "uint8", "comment": "保留字"}, {"name": "cost", "type": "uint32", "comment": "cost"}, {"name": "tag", "type": "uint32", "comment": "tag"}, {"name": "route_flag", "type": "uint32", "comment": "route_flag"}, {"name": "route_source", "type": "uint32", "comment": "路由来源"}, {"name": "version", "type": "uint32", "comment": "version"}], "keys": [{"name": "entry_index", "index": {"type": "primary"}, "node": "Interface_Ipv6Route_S", "fields": ["entry_index"], "constraints": {"unique": true}, "comment": "唯一主键"}, {"name": "prefix", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Interface_Ipv6Route_S", "fields": ["vrf_id", "prefix", "mask_len", "route_source"], "constraints": {"unique": false}, "comment": "副键"}, {"name": "route_source", "index": {"type": "hashcluster"}, "node": "Interface_Ipv6Route_S", "fields": ["route_source"], "constraints": {"unique": false}, "comment": "副键"}, {"name": "prefix_and_nhp", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Interface_Ipv6Route_S", "fields": ["vrf_id", "prefix", "next_hop", "if_index", "mask_len", "route_source"], "constraints": {"unique": false}, "comment": "副键"}, {"name": "route_source_ver", "index": {"type": "hashcluster"}, "node": "Interface_Ipv6Route_S", "fields": ["route_source", "version"], "constraints": {"unique": false}, "comment": "副键"}]}