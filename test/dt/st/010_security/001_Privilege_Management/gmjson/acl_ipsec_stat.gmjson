{"comment": "IPSEC引用ACL的统计表", "version": "2.0", "type": "record", "name": "acl_ipsec_stat", "config": {"check_validity": false}, "max_record_count": 60000, "fields": [{"name": "groupid", "type": "uint32", "comment": "ACLGroup的索引"}, {"name": "ruleid", "type": "uint32", "comment": "规则的索引"}, {"name": "match_nums", "type": "uint64", "comment": "统计计数"}], "keys": [{"name": "acl_ipsec_pk", "index": {"type": "primary"}, "node": "acl_ipsec_stat", "fields": ["groupid", "ruleid"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}