{"comment": "nac用户授权acl规则entryid", "version": "2.0", "type": "record", "name": "fwmsvc_nac_acl", "config": {"check_validity": false}, "max_record_count": 102400, "fields": [{"name": "acl_id", "type": "uint32", "comment": "授权的acl id"}, {"name": "unit", "type": "uint32", "comment": "acl规则下发的芯片号"}, {"name": "if_index", "type": "uint32", "comment": "acl规则下发的端口索引, portbase使用"}, {"name": "class_id", "type": "uint32", "comment": "acl规则对应的classid"}, {"name": "acl_entryid_list", "type": "record", "array": true, "size": 128, "comment": "下发的ACL entryid列表", "fields": [{"name": "acl_entryid", "type": "uint32", "default": 0, "comment": "下发的ACL规则对应的entryid"}]}], "keys": [{"name": "nac_acl_key", "index": {"type": "primary"}, "node": "fwmsvc_nac_acl", "fields": ["acl_id", "unit", "if_index"], "constraints": {"unique": true}, "comment": "基于acl_id,ifindex和unit查询本表"}]}