{"comment": "单条IF-MATCH匹配项(FEI_MQC_SUBCLASS_DATA_S)", "version": "2.0", "type": "record", "name": "mqc_class_rule", "config": {"check_validity": true}, "max_record_count": 4194304, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟路由"}, {"name": "classifier_id", "type": "uint32", "comment": "流分类ID"}, {"name": "match_id", "type": "uint32", "comment": "匹配ID"}, {"name": "rule_id", "type": "uint32", "comment": "流分类规则ID"}, {"name": "match_pri", "type": "uint32", "comment": "匹配优先级"}, {"name": "rule_pri", "type": "uint32", "comment": "规则优先级"}, {"name": "operator", "type": "uint16", "comment": "流分类模式and/or"}, {"name": "rule_type", "type": "uint16", "comment": "流分类的类型"}, {"name": "rule_mask", "type": "fixed", "size": 20, "comment": "规则掩码"}, {"name": "proto_family", "type": "uint32", "comment": "协议族"}, {"name": "acl_group", "type": "uint32", "comment": "acl的group号"}, {"name": "acl_type", "type": "uint32", "comment": "acl类型"}, {"name": "rule_num", "type": "uint32", "comment": "规则数目"}, {"name": "acl_refresh_flag", "type": "uint32", "comment": "acl刷新标记"}, {"name": "src_mac", "type": "fixed", "size": 6, "comment": "源mac"}, {"name": "src_mac_mask", "type": "fixed", "size": 6, "comment": "源mac掩码"}, {"name": "dst_mac", "type": "fixed", "size": 6, "comment": "目的mac"}, {"name": "dst_mac_mask", "type": "fixed", "size": 6, "comment": "目的mac掩码"}, {"name": "filter_flag", "type": "uint32", "comment": "过滤器标志"}, {"name": "double_tag", "type": "uint32", "comment": "QinQ报文双层Tag"}, {"name": "fwd_type", "type": "uint32", "comment": "二层转发状态(已知单播/未知单播)"}, {"name": "discard_flag", "type": "uint32", "comment": "丢弃报文标志"}, {"name": "tcp_flag", "type": "uint32", "comment": "TCP报文SYN标志"}, {"name": "any_flag", "type": "uint32", "comment": "匹配所有报文标志"}, {"name": "outer_8021p", "type": "uint32", "comment": "VLAN报文802.1p优先级"}, {"name": "inner_8021p", "type": "uint32", "comment": "QinQ报文内层VLAN的802.1p优先级"}, {"name": "dscp_low", "type": "uint32", "comment": "dscp低位"}, {"name": "dscp_high", "type": "uint32", "comment": "dscp高位"}, {"name": "ip_prec", "type": "uint32", "comment": "ip优先级"}, {"name": "l2_proto", "type": "uint32", "comment": "以太网帧头中协议类型字段"}, {"name": "vlan_begin", "type": "uint16", "comment": "vlan初始值"}, {"name": "vlan_end", "type": "uint16", "comment": "vlan解析时的最大值"}, {"name": "cvlan_begin", "type": "uint16", "comment": "cvlan初始值"}, {"name": "cvlan_end", "type": "uint16", "comment": "cvlan解析时的最大值"}, {"name": "ecn_pri", "type": "uint32", "comment": "ecn阻塞优先级"}, {"name": "ipidvalue", "type": "uint16", "comment": "IP标识值"}, {"name": "mask", "type": "uint16", "comment": "IP标识掩码长度"}, {"name": "local_id", "type": "uint32", "comment": "localid字段"}, {"name": "acl_loose_mode", "type": "uint32", "comment": "松散模式"}, {"name": "rocev2_type", "type": "uint32", "comment": "rocev2类型"}, {"name": "rocev2_opcode", "type": "uint32", "comment": "rocev2操作码"}, {"name": "rocev2_qpair", "type": "uint32", "comment": "rocev2Qpair"}, {"name": "rocev2_nack", "type": "uint32", "comment": "rocev2Nack"}, {"name": "rocev2_udf_base", "type": "uint32", "comment": "rocev2四层头部开始偏移"}, {"name": "rocev2_udf", "type": "fixed", "size": 48, "comment": "rocev2四层头部"}, {"name": "ip_type", "type": "uint32", "comment": "ip类型"}, {"name": "if_index", "type": "uint32", "comment": "接口索引"}, {"name": "ipv4_addr", "type": "uint32", "comment": "Ipv4目的地址列表"}, {"name": "ipv6_addr", "type": "fixed", "size": 16, "comment": "Ipv6目的地址列表"}, {"name": "nhp_index", "type": "uint32", "comment": "下一跳索引"}, {"name": "acl_tunnel_type", "type": "uint32", "comment": "acl 规则隧道类型"}, {"name": "vxlan_vsi", "type": "uint32", "comment": "vxlan vsi"}, {"name": "vxlan_vni", "type": "uint32", "comment": "vxlan vni"}, {"name": "vxlan_rsv", "type": "uint32", "comment": "vxlan保留字段"}, {"name": "inner_srcIp", "type": "uint32", "comment": "隧道内层源Ip"}, {"name": "inner_srcIpMask", "type": "uint32", "comment": "隧道内层源Ip掩码"}, {"name": "inner_dstIp", "type": "uint32", "comment": "隧道内层目的Ip"}, {"name": "inner_dstIpMask", "type": "uint32", "comment": "隧道内层目的Ip掩码"}, {"name": "inner_srcPort", "type": "uint16", "comment": "隧道内层源port"}, {"name": "inner_dstPort", "type": "uint16", "comment": "隧道内层目的port"}, {"name": "protocol_num", "type": "uint8", "comment": "协议号"}, {"name": "inner_tcpEstablished", "type": "uint8", "comment": "tcp established"}, {"name": "tunnel_cond_mask", "type": "uint16", "comment": "隧道内层子段掩码"}, {"name": "tunnel_type", "type": "uint8", "comment": "隧道类型"}, {"name": "rsv1", "type": "uint8", "comment": "保留字段"}, {"name": "app_group", "type": "uint16", "comment": "自定义应用组"}, {"name": "time_range_group", "type": "uint32", "comment": "app-group约束时间段"}, {"name": "time_range", "type": "uint32", "comment": "application约束时间段"}, {"name": "application", "type": "uint16", "comment": "自定义应用"}, {"name": "rsv2", "type": "uint16", "comment": "保留字段"}, {"name": "metadata", "type": "fixed", "size": 360, "comment": "公共的FES通信数据"}], "keys": [{"name": "mqc_class_rule_pk", "index": {"type": "primary"}, "node": "mqc_class_rule", "fields": ["vrid", "classifier_id", "match_id", "rule_id"], "constraints": {"unique": true}, "comment": "流分类规则关键字名称"}, {"name": "mqc_class_index", "index": {"type": "hashcluster"}, "node": "mqc_class_rule", "fields": ["classifier_id"], "constraints": {"unique": false}, "comment": "流分类索引关键字名称"}, {"name": "mqc_class_pri", "index": {"type": "local"}, "node": "mqc_class_rule", "fields": ["classifier_id", "match_pri", "rule_pri"], "comment": "流分类优先级关键字名称"}, {"name": "mqc_group_index", "index": {"type": "hashcluster"}, "node": "mqc_class_rule", "fields": ["acl_group"], "constraints": {"unique": false}, "comment": "acl的group关键字名称"}, {"name": "mqc_trng_index", "index": {"type": "hashcluster"}, "node": "mqc_class_rule", "fields": ["time_range"], "constraints": {"unique": false}, "comment": "流分类time_range关键字名称"}, {"name": "mqc_group_trng_index", "index": {"type": "hashcluster"}, "node": "mqc_class_rule", "fields": ["time_range_group"], "constraints": {"unique": false}, "comment": "app-group的time_range关键字名称"}], "super_fields": [{"name": "class_rule_key", "fields": ["vrid", "classifier_id", "match_id", "rule_id", "match_pri", "rule_pri", "operator", "rule_type", "rule_mask", "proto_family", "acl_group", "acl_type", "rule_num", "acl_refresh_flag", "src_mac", "src_mac_mask", "dst_mac", "dst_mac_mask", "filter_flag", "double_tag", "fwd_type", "discard_flag", "tcp_flag", "any_flag", "outer_8021p", "inner_8021p", "dscp_low", "dscp_high", "ip_prec", "l2_proto", "vlan_begin", "vlan_end", "cvlan_begin", "cvlan_end", "ecn_pri", "ipidvalue", "mask", "local_id", "acl_loose_mode", "rocev2_type", "rocev2_opcode", "rocev2_qpair", "rocev2_nack", "rocev2_udf_base", "rocev2_udf", "ip_type", "if_index", "ipv4_addr", "ipv6_addr", "nhp_index", "acl_tunnel_type", "vxlan_vsi", "vxlan_vni", "vxlan_rsv", "inner_srcIp", "inner_srcIpMask", "inner_dstIp", "inner_dstIpMask", "inner_srcPort", "inner_dstPort", "protocol_num", "inner_tcpEstablished", "tunnel_cond_mask", "tunnel_type", "rsv1", "app_group", "time_range_group", "time_range", "application", "rsv2", "metadata"]}]}