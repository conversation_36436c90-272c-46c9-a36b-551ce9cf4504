{"comment": "bfd下发芯片接收表", "version": "2.0", "type": "record", "name": "bfd_np_sess_rcv", "max_record_count": 2048, "fields": [{"name": "my_discr", "type": "uint32", "comment": ""}, {"name": "bfd_type", "type": "uint8", "comment": "bfd_type"}, {"name": "bfd_mode", "type": "uint8", "comment": "bfd_mode"}, {"name": "defend_dst_ipv4", "type": "uint32", "comment": "defend_dst_ipv4"}, {"name": "defend_ipv6", "type": "fixed", "size": 16, "comment": "defend_ipv6"}, {"name": "defend_src_ipv4", "type": "uint32", "comment": "defend_src_ipv4"}, {"name": "key_chain_id", "type": "uint8", "comment": "key_chain_id"}, {"name": "detect_time", "type": "uint8", "comment": "detect_time"}, {"name": "stat_en", "type": "uint8", "comment": "stat_en"}, {"name": "rcv_cycle", "type": "uint32", "comment": "rcv_cycle"}, {"name": "downtosendpktctrl", "type": "uint8", "comment": "downtosendpktctrl"}, {"name": "bfd_global_index", "type": "uint32", "comment": "bfd_global_index"}, {"name": "bfd_rcv_disable", "type": "uint8", "comment": "bfd_rcv_disable"}, {"name": "rcv_pkt_info", "type": "uint8", "comment": "rcv_pkt_info"}, {"name": "sbfd_status", "type": "uint8", "comment": "sbfd_status"}, {"name": "pkt_hdr_len", "type": "uint8", "comment": "pkt_hdr_len"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "补丁预留保留字段"}], "keys": [{"name": "bfd_np_sess_rcv_pk", "index": {"type": "primary"}, "node": "bfd_np_sess_rcv", "fields": ["my_discr"], "constraints": {"unique": true}}]}