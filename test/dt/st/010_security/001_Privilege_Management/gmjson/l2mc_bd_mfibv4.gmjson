{"comment": "L2MC over VXLAN MFIB数据信息, VRP fes 1303 L2MC_BD_MFIB4", "version": "2.0", "type": "record", "name": "l2mc_bd_mfibv4", "config": {"check_validity": true}, "max_record_count": 65536, "fields": [{"name": "vrIndex", "type": "uint32", "comment": "VS索引"}, {"name": "groupAddr", "type": "uint32", "comment": "组播组地址"}, {"name": "sourceAddr", "type": "uint32", "comment": "组播源地址"}, {"name": "bdId", "type": "uint32", "comment": "bd 索引"}, {"name": "vrfIndex", "type": "uint32", "comment": "vrf索引, 对l2mc 暂时未使用, 预留"}, {"name": "pathFlags", "type": "uint32", "comment": "表项完备性标识"}, {"name": "mcId", "type": "uint32", "comment": "组播组Id"}, {"name": "mcidVersion", "type": "uint64", "comment": "组播组Id的版本号"}, {"name": "mfibVersion", "type": "uint32", "comment": "组mfib和elb一致性判断的版本号，mfib删除场景仅删除<=version的elb"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "appVersion", "type": "uint32", "comment": "对账版本号，与APP对账使用"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "svcCtxHighPrio", "type": "fixed", "size": 4, "comment": "从bd enable表中获取的svcCtxHighPrio"}, {"name": "svcCtxNormalPrio", "type": "fixed", "size": 4, "comment": "从MFIB表中获取的svcCtxNormalPrio"}, {"name": "serviceStatus", "type": "fixed", "size": 2, "comment": "下发SVC状态"}, {"name": "hppErrCode", "type": "uint32", "comment": "下发SVC错误码，svc hpp"}, {"name": "enpErrCode", "type": "uint32", "comment": "下发SVC错误码，svc enp"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l2mc_bd_mfibv4", "fields": ["vrIndex", "groupAddr", "sourceAddr", "bdId"], "constraints": {"unique": true}, "comment": "主键索引"}, {"name": "bdId_local_key", "index": {"type": "local"}, "node": "l2mc_bd_mfibv4", "fields": ["bdId"], "comment": "bdId排序索引"}, {"name": "vr_bd_key", "index": {"type": "hashcluster"}, "node": "l2mc_bd_mfibv4", "fields": ["vrIndex", "bdId"], "constraints": {"unique": false}, "comment": "命令行指定BD查询"}, {"name": "vr_bd_grp_key", "index": {"type": "hashcluster"}, "node": "l2mc_bd_mfibv4", "fields": ["vrIndex", "bdId", "groupAddr"], "constraints": {"unique": false}, "comment": "命令行指定groupAddr查询"}, {"name": "srcpid_key", "index": {"type": "hashcluster"}, "node": "l2mc_bd_mfibv4", "fields": ["vrIndex", "vrfIndex", "appSrcPid"], "constraints": {"unique": false}, "comment": "老化查询索引（vrId, vrfId, appSrcPid）"}]}