{"comment": "端口的队列调度，FES170#", "version": "2.0", "type": "record", "name": "qos_port_queue", "config": {"check_validity": false}, "max_record_count": 4096, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口索引"}, {"name": "queue", "type": "record", "array": true, "size": 8, "comment": "队列", "fields": [{"name": "queue_index", "type": "uint32", "comment": "队列索引"}, {"name": "queue_weight", "type": "int64", "comment": "队列权重"}, {"name": "priority_map", "type": "uint32", "comment": "优先级"}]}, {"name": "schedule_mode", "type": "uint32", "comment": "调度模式"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}, {"name": "vrid", "type": "uint16", "comment": "虚拟路由"}], "keys": [{"name": "qos_port_queue_pk", "index": {"type": "primary"}, "node": "qos_port_queue", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "主键"}]}