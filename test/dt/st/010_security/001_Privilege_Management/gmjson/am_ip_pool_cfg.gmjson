{"comment": "地址池配置", "version": "2.0", "type": "record", "name": "am_ip_pool_cfg", "config": {"check_validity": false}, "max_record_count": 100000, "fields": [{"name": "vsys_id", "type": "uint32", "comment": "虚系统ID"}, {"name": "pool_name", "type": "string", "size": 256, "comment": "地址池名称"}, {"name": "pool_id", "type": "uint16", "comment": "地址池ID"}], "keys": [{"name": "am_ip_pool_cfg_pk", "index": {"type": "primary"}, "node": "am_ip_pool_cfg", "fields": ["vsys_id", "pool_name"], "constraints": {"unique": true}, "comment": "地址池名称主键"}, {"name": "pool_id_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "am_ip_pool_cfg", "fields": ["vsys_id", "pool_id"], "constraints": {"unique": true}, "comment": "地址池ID主键"}]}