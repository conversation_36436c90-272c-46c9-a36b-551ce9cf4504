{"version": "2.0", "type": "record", "name": "syslog_module", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "vsid", "type": "uint32", "comment": "vs id"}, {"name": "chnl_no", "type": "uint32", "comment": "channel number"}, {"name": "module_id", "type": "uint32", "comment": "module id"}, {"name": "module_name", "type": "string", "size": 32, "default": "default", "comment": "module name"}, {"name": "log_enflag", "type": "uint16", "default": 1, "comment": "syslog enable status"}, {"name": "log_level", "type": "uint32", "default": 4, "comment": "syslog level"}, {"name": "reserve", "type": "uint32", "comment": "reserve field"}], "keys": [{"name": "syslog_module_key", "index": {"type": "primary"}, "node": "syslog_module", "fields": ["vsid", "chnl_no", "module_id"], "constraints": {"unique": true}, "comment": "primay key"}]}