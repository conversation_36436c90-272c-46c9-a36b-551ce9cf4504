{"comment": "弱口令字典", "version": "2.0", "type": "record", "name": "weakpasswd_cfg", "config": {"check_validity": false}, "max_record_count": 1000, "fields": [{"name": "vsId", "type": "uint32"}, {"name": "weakPasswd", "type": "fixed", "size": 129}], "keys": [{"name": "weakpasswd_cfg_pk", "index": {"type": "primary"}, "node": "weakpasswd_cfg", "fields": ["vsId", "weakPasswd"], "constraints": {"unique": true}}]}