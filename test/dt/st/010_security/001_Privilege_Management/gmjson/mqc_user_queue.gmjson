{"comment": "hqos resource", "version": "2.0", "type": "record", "name": "mqc_user_queue", "config": {"check_validity": false}, "max_record_count": 10000, "fields": [{"name": "inst_id", "type": "uint32", "comment": "father instid or subpolicy instid"}, {"name": "classfier_id", "type": "uint32", "comment": "father class or subpolicy class"}, {"name": "queue_id", "type": "uint32", "comment": "alloced queue index"}, {"name": "sub_inst_id", "type": "uint32", "comment": "sub policy instid"}, {"name": "queue_profile", "type": "uint32", "comment": "queue profile id"}, {"name": "ifindex", "type": "uint32", "comment": "ifindex"}, {"name": "shp_is_pct", "type": "uint8", "comment": "shaping by percent"}, {"name": "parent_shp", "type": "uint32", "comment": "parent shaping value"}], "keys": [{"name": "mqc_user_queue_pk", "index": {"type": "primary"}, "node": "mqc_user_queue", "fields": ["inst_id", "classfier_id"], "constraints": {"unique": true}}, {"name": "mqc_user_queue_scan", "index": {"type": "hashcluster"}, "node": "mqc_user_queue", "fields": ["inst_id"], "constraints": {"unique": false}}, {"name": "mqc_user_queue_profile_scan", "index": {"type": "hashcluster"}, "node": "mqc_user_queue", "fields": ["queue_profile"], "constraints": {"unique": false}}, {"name": "mqc_user_queue_ifindex_scan", "index": {"type": "hashcluster"}, "node": "mqc_user_queue", "fields": ["ifindex"], "constraints": {"unique": false}}]}