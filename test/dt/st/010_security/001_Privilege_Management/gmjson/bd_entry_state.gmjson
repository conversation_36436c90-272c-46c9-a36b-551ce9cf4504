{"comment": "BD表项接口板下发状态和临时状态的维护", "version": "2.0", "type": "record", "name": "bd_entry_state", "config": {"check_validity": false}, "max_record_count": 512000, "fields": [{"name": "vr_id", "type": "uint32", "comment": "VR索引"}, {"name": "bd_id", "type": "uint32", "comment": "BD编号"}, {"name": "bd_type", "type": "uint8", "comment": "BD类型，1（BD配置），2（分布式网关虚拟BD），3（任意解封装虚拟BD）"}, {"name": "vsi_err_bit", "type": "uint8", "comment": "按bit记录特定芯片下发失败情况，0标识下发成功，单芯片下发失败回滚"}, {"name": "vni_err_bit", "type": "uint8", "comment": "按bit记录特定芯片下发失败情况，0标识下发成功，单芯片下发失败回滚"}, {"name": "map_vni_err_bit", "type": "uint8", "comment": "按bit记录特定芯片下发失败情况，0标识下发成功，单芯片下发失败回滚"}, {"name": "ing_stat_id", "type": "fixed", "size": 96, "comment": "入方向统计ID"}, {"name": "egr_stat_id", "type": "fixed", "size": 96, "comment": "出方向统计ID"}, {"name": "stat_id_err_bit", "type": "uint8", "comment": "按bit记录特定芯片下发失败情况，0标识下发成功，单芯片下发失败回滚"}, {"name": "vsitoken_err", "type": "uint8", "comment": "vsi token资源锁失败记录"}, {"name": "mcid<PERSON><PERSON>_err", "type": "uint8", "comment": "mcid token资源锁失败记录"}, {"name": "vsi_maclearn_err", "type": "uint8", "comment": "设置VSI MAC学习失败标识"}, {"name": "vsi_maclearn_id", "type": "fixed", "size": 32, "comment": "设置VSI MAC学习的芯片资源id"}], "keys": [{"name": "bd_entry_state_key", "index": {"type": "primary"}, "node": "bd_entry_state", "fields": ["vr_id", "bd_id", "bd_type"], "constraints": {"unique": true}, "comment": "根据vr_id，bd_id和bd_type索引"}, {"name": "type_stat_id_err", "index": {"type": "hashcluster"}, "node": "bd_entry_state", "fields": ["bd_type", "stat_id_err_bit"], "constraints": {"unique": false}, "comment": "根据bd_type和stat_id_err_bit索引"}, {"name": "stat_id_err", "index": {"type": "hashcluster"}, "node": "bd_entry_state", "fields": ["stat_id_err_bit"], "constraints": {"unique": false}, "comment": "根据stat_id_err_bit索引"}, {"name": "vsi_maclearn_err", "index": {"type": "hashcluster"}, "node": "bd_entry_state", "fields": ["vsi_maclearn_err"], "constraints": {"unique": false}, "comment": "根据vsi_maclearn_err索引"}]}