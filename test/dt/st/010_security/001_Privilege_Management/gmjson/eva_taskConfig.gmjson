{"version": "2.0", "name": "eva_taskConfig", "type": "record", "max_record_count": 30000, "fields": [{"name": "id", "type": "uint32", "auto_increment": true}, {"name": "scriptName", "type": "string"}, {"name": "taskName", "type": "string"}, {"name": "loopIndex", "type": "string", "nullable": true}, {"name": "loopArray", "type": "string", "nullable": true}, {"name": "actionName", "type": "string"}, {"name": "parameters", "type": "string", "nullable": true}, {"name": "para1", "type": "string", "nullable": true}, {"name": "para2", "type": "string", "nullable": true}, {"name": "para3", "type": "string", "nullable": true}, {"name": "para4", "type": "string", "nullable": true}, {"name": "para5", "type": "string", "nullable": true}], "keys": [{"name": "pk", "node": "eva_taskConfig", "index": {"type": "primary"}, "fields": ["id"]}]}