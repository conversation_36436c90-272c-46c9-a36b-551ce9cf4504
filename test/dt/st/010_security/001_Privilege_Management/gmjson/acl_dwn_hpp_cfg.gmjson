{"comment": "ACL下发HPP配置表", "version": "2.0", "type": "record", "name": "acl_dwn_hpp_cfg", "config": {"check_validity": false}, "max_record_count": 10000, "fields": [{"name": "acl_userid", "type": "uint32", "comment": "ACL用户索引"}, {"name": "slottype", "type": "uint8", "comment": "Slot类型"}, {"name": "cpuid", "type": "uint8", "comment": "CPU索引"}, {"name": "slotid", "type": "uint16", "comment": "Slot索引"}, {"name": "userkey1", "type": "uint32", "comment": "用户键值1"}, {"name": "userkey2", "type": "uint32", "comment": "用户键值2"}, {"name": "userkey3", "type": "uint32", "comment": "用户键值3"}, {"name": "acl_groupid", "type": "uint32", "comment": "ACLGroup索引"}, {"name": "acl_group_type", "type": "uint8", "comment": "ACLGroup类型"}], "keys": [{"name": "acl_dwn_hpp_cfg_pk", "index": {"type": "primary"}, "node": "acl_dwn_hpp_cfg", "fields": ["acl_userid", "slottype", "cpuid", "slotid", "userkey1", "userkey2", "userkey3"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}