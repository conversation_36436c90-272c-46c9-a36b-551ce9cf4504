{"comment": "端口基础信息配置表", "version": "2.0", "type": "record", "name": "port_base_info", "config": {"check_validity": false}, "max_record_count": 128000, "fields": [{"name": "dev_id", "type": "uint32", "comment": "设备id"}, {"name": "ifindex", "type": "uint32", "comment": "端口索引"}, {"name": "fe_nodeid", "type": "uint32", "nullable": true, "comment": "fe节点id"}, {"name": "vrid", "type": "uint32", "nullable": true, "comment": "虚拟设备id"}, {"name": "dev_name", "type": "fixed", "nullable": true, "size": 20, "comment": "设备名称"}, {"name": "dev_type", "type": "uint32", "nullable": true, "comment": "设备类型"}, {"name": "hard_type", "type": "uint32", "nullable": true, "comment": "硬件类型"}, {"name": "slotid", "type": "uint32", "nullable": true, "comment": "槽位id"}, {"name": "cardid", "type": "uint32", "nullable": true, "comment": "单板id"}, {"name": "chassisid", "type": "uint32", "nullable": true, "comment": "底板id"}, {"name": "unitid", "type": "uint32", "nullable": true, "comment": "芯片号id"}, {"name": "portid", "type": "uint32", "nullable": true, "comment": "端口id"}, {"name": "tb", "type": "uint16", "nullable": true, "comment": "tb"}, {"name": "tp", "type": "uint16", "nullable": true, "comment": "tp"}, {"name": "port_lrcfgwriteover", "type": "uint32", "comment": "lr端口配置重写"}, {"name": "port_fecfgwriteover", "type": "uint32", "comment": "fe端口配置重写"}, {"name": "device_cfgwriteover", "type": "uint32", "comment": "设备配置重写"}, {"name": "if_type", "type": "uint32", "comment": "端口类型"}, {"name": "device_portoverflag", "type": "uint32", "comment": "设备管理端口创建完成"}, {"name": "coreId", "type": "uint32", "comment": "端口的coreId"}], "keys": [{"name": "port_base_info_pk", "index": {"type": "primary"}, "node": "port_base_info", "fields": ["dev_id"], "constraints": {"unique": true}, "comment": "主键"}, {"name": "writeOver_spk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "port_base_info", "fields": ["port_lrcfgwriteover", "port_fecfgwriteover", "device_cfgwriteover"], "constraints": {"unique": false}, "comment": "WriteOver键"}, {"name": "ifindex_pk", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "port_base_info", "fields": ["ifindex"], "constraints": {"unique": false}, "comment": "ifindex键"}]}