{"comment": "下一跳组结点表，对应9#表", "version": "2.0", "type": "record", "name": "nhp_group_node", "config": {"check_validity": true}, "max_record_count": 4096000, "fields": [{"name": "nhp_group_id", "type": "uint32", "comment": "下一跳组ID"}, {"name": "attribute_id", "type": "uint32", "comment": "属性"}, {"name": "primary_nhp_id", "type": "uint32", "comment": "主下一跳索引"}, {"name": "primary_label", "type": "uint32", "comment": "主标签"}, {"name": "backup_nhp_id", "type": "uint32", "comment": "备下一跳索引"}, {"name": "backup_label", "type": "uint32", "comment": "备标签"}, {"name": "vr_id", "type": "uint32", "comment": "Vs索引"}, {"name": "vrf_index", "type": "uint32", "comment": "VpnInstace索引"}, {"name": "flags", "type": "uint32", "comment": "标志(包括path完备性等)"}, {"name": "app_source_id", "type": "uint32"}, {"name": "group_smooth_id", "type": "uint32"}, {"name": "app_obj_id", "type": "uint64"}, {"name": "app_version", "type": "uint32"}, {"name": "time_stamp_create", "type": "time"}, {"name": "time_stamp_smooth", "type": "time"}, {"name": "bandwidth", "type": "uint32"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "nhp_group_node", "fields": ["nhp_group_id", "attribute_id", "primary_nhp_id", "primary_label", "backup_nhp_id", "backup_label", "vr_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}, {"name": "vrid_nhpgroupid_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_group_node", "fields": ["vr_id", "nhp_group_id"], "comment": "根据vr_id + nhp_group_id索引"}, {"name": "primarynhpid_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_group_node", "fields": ["primary_nhp_id"], "comment": "根据primary_nhp_id索引"}, {"name": "backupnhpid_localhash_key", "index": {"type": "hashcluster"}, "node": "nhp_group_node", "fields": ["backup_nhp_id"], "comment": "根据backup_nhp_id索引"}]}