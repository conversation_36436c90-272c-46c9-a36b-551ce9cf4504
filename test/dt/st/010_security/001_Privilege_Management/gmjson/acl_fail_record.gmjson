{"version": "2.0", "type": "record", "name": "acl_fail_record", "config": {"check_validity": true}, "max_record_count": 1024, "fields": [{"name": "index", "type": "uint32"}, {"name": "unit", "type": "uint32"}, {"name": "gid", "type": "uint32"}, {"name": "<PERSON><PERSON><PERSON>", "type": "uint32"}, {"name": "groupid", "type": "uint32"}, {"name": "year", "type": "uint16"}, {"name": "month", "type": "uint8"}, {"name": "day", "type": "uint8"}, {"name": "hour", "type": "uint8"}, {"name": "minute", "type": "uint8"}, {"name": "second", "type": "uint8"}], "keys": [{"name": "acl_fail_record_pk", "index": {"type": "primary"}, "node": "acl_fail_record", "fields": ["index"], "constraints": {"unique": true}}, {"name": "index_scan", "index": {"type": "local"}, "node": "acl_fail_record", "fields": ["index"], "comment": "按照scan index降序遍历搜索"}], "super_fields": [{"name": "acl_fail_record_sf", "fields": ["index", "unit", "gid", "<PERSON><PERSON><PERSON>", "groupid", "year", "month", "day", "hour", "minute", "second"]}]}