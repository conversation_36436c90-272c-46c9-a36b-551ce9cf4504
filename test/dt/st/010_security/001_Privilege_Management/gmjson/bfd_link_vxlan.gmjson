{"comment": "bfd vxlan链路信息", "version": "2.0", "type": "record", "name": "bfd_link_vxlan", "max_record_count": 8192, "fields": [{"name": "my_discr", "type": "uint32", "comment": "my_discr"}, {"name": "vsid", "type": "uint32", "comment": "vsid"}, {"name": "vrf_id", "type": "uint32", "comment": "vrf_id"}, {"name": "peer_ip", "type": "fixed", "size": 16, "comment": "peer_ip"}, {"name": "src_ip", "type": "fixed", "size": 16, "comment": "src_ip"}, {"name": "vni", "type": "uint32", "comment": "vni"}, {"name": "des_mac", "type": "fixed", "size": 6, "comment": "des_mac"}, {"name": "flag", "type": "uint16", "comment": "flag"}, {"name": "link_flag", "type": "uint16", "comment": "link vxlan表完备性标记"}, {"name": "fwd_vrfid", "type": "uint32", "comment": "fwd_vrfid"}, {"name": "app_source_id", "type": "uint32", "comment": "app_source_id"}, {"name": "app_serial_id", "type": "uint32", "comment": "app_serial_id"}, {"name": "app_obj_id", "type": "uint64", "comment": "app_obj_id"}, {"name": "app_version", "type": "uint32", "comment": "app_version"}, {"name": "reserved", "type": "uint32", "nullable": true, "comment": "reserved field."}], "keys": [{"name": "bfd_link_vxlan_pk", "index": {"type": "primary"}, "node": "bfd_link_vxlan", "fields": ["my_discr"], "constraints": {"unique": true}}]}