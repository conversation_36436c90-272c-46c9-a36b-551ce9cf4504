{"comment": "vlan 路由器端口列表", "version": "2.0", "type": "record", "name": "l2mc_vlan_port", "config": {"check_validity": false}, "max_record_count": 524288, "fields": [{"name": "vrId", "type": "uint32", "comment": "VR索引"}, {"name": "vlanId", "type": "uint16", "comment": "Vlan Id值"}, {"name": "type", "type": "uint16", "comment": "路由端口类型"}, {"name": "portId", "type": "uint32", "comment": "接口索引"}, {"name": "vrfId", "type": "uint32", "comment": "VRF索引"}, {"name": "appSrcPid", "type": "uint32", "comment": "vrp生产者appid"}, {"name": "smoothVersion", "type": "uint32", "comment": "平滑对账版本号"}, {"name": "serviceStatus", "type": "uint32", "comment": "下发SVC状态"}, {"name": "errCode", "type": "uint32", "comment": "下发SVC错误码"}, {"name": "timeStampCreate", "type": "time", "comment": "创建时间"}, {"name": "timeStampSmooth", "type": "time", "comment": "平滑时间"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "l2mc_vlan_port", "fields": ["vrId", "vlanId", "type", "portId"], "constraints": {"unique": true}, "comment": "主键索引"}]}