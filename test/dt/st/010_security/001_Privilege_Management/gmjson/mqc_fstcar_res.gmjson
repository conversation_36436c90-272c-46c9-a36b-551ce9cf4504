{"version": "2.0", "type": "record", "name": "mqc_fstcar_res", "config": {"check_validity": false}, "max_record_count": 256000, "fields": [{"name": "unit_id", "type": "uint32", "comment": "芯片ID"}, {"name": "inst_id", "type": "uint32", "comment": "应用实例ID"}, {"name": "class_id", "type": "uint32", "comment": "流分类ID"}, {"name": "match_id", "type": "uint32", "comment": "规则匹配ID"}, {"name": "rule_id", "type": "uint32", "comment": "规则ID"}, {"name": "gid", "type": "uint32", "comment": "组ID"}, {"name": "local_pri", "type": "uint32", "comment": "规则优先级"}], "keys": [{"name": "fstcar_key", "index": {"type": "primary"}, "node": "mqc_fstcar_res", "fields": ["unit_id", "inst_id", "class_id", "match_id", "rule_id"], "constraints": {"unique": true}, "comment": "基于Class的关键字名称"}, {"name": "inst_scan", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "mqc_fstcar_res", "fields": ["unit_id", "inst_id"], "constraints": {"unique": false}, "comment": "基于应用实例遍历"}], "super_fields": [{"name": "fstcar_sf", "fields": ["unit_id", "inst_id", "class_id", "match_id", "rule_id", "gid", "local_pri"]}]}