{"comment": "capset_itemname", "version": "2.0", "type": "record", "name": "capset_itemname", "config": {"check_validity": false}, "max_record_count": 262144, "fields": [{"name": "set_code", "type": "uint32", "comment": "能力集合编码"}, {"name": "set_type", "type": "uint8", "comment": "能力集合类型"}, {"name": "item_name", "type": "string", "size": 64, "comment": "属性或能力名称"}, {"name": "data", "type": "bytes", "size": 13312, "comment": "数据空间"}, {"name": "len", "type": "uint16", "comment": "data的实际长度"}], "keys": [{"name": "capset_itemname_pk", "index": {"type": "primary"}, "node": "capset_itemname", "fields": ["set_code", "set_type", "item_name"], "constraints": {"unique": true}, "comment": "用来查询能力集的key组合"}]}