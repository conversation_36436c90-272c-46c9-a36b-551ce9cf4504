{"comment": "nqa inst track", "version": "2.0", "type": "record", "name": "nqa_inst_track", "max_record_count": 4096, "fields": [{"name": "vr_id", "type": "uint32", "comment": "vr id"}, {"name": "test_index", "type": "uint32", "comment": "test index"}, {"name": "track_type", "type": "uint32", "comment": "track type"}, {"name": "seq_num", "type": "uint32", "comment": "序列号"}, {"name": "nqa_state", "type": "uint32", "comment": "nqa state"}, {"name": "fail_times", "type": "uint32", "comment": "fail times"}, {"name": "hsrc_pid", "type": "uint32", "comment": "hsrc pid"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "app serial id"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和data相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}], "keys": [{"name": "nqa_inst_track_key", "index": {"type": "primary"}, "node": "nqa_inst_track", "fields": ["vr_id", "test_index", "track_type"], "constraints": {"unique": true}}, {"name": "appsourceid_localhash_key", "index": {"type": "hashcluster"}, "node": "nqa_inst_track", "fields": ["app_source_id"], "constraints": {"unique": false}}]}