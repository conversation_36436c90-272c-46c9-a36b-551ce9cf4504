{"comment": "table of tac_group_data", "version": "2.0", "type": "record", "name": "tac_group_data", "config": {"check_validity": false}, "max_record_count": 8192, "fields": [{"name": "ulVsysID", "type": "uint32", "comment": "VsysID"}, {"name": "szName", "type": "bytes", "size": 64, "comment": "Name"}, {"name": "ulGroupIndex", "type": "uint32", "comment": "Group Index"}, {"name": "usEncrypt", "type": "uint16", "comment": "Encrypt Flag"}, {"name": "szSharedKey", "type": "bytes", "nullable": true, "size": 512, "comment": "Shared Key"}, {"name": "szCipherSharedKey", "type": "bytes", "nullable": true, "size": 1024, "comment": "Cipher Shared Key"}, {"name": "ucOctFmt", "type": "uint8", "comment": "Oct Fmt"}, {"name": "ucDeadtime", "type": "uint8", "comment": "Deadtime"}, {"name": "ucDomain", "type": "uint8", "comment": "Domain"}, {"name": "ulSourceIp", "type": "uint32", "comment": "Source Ip"}, {"name": "stSourceIPv6", "type": "fixed", "nullable": true, "size": 16, "comment": "Source IPv6"}, {"name": "usTimeout", "type": "uint16", "comment": "Timeout"}, {"name": "ulLoopBackv4", "type": "uint32", "comment": "LoopBackv4"}, {"name": "ulLoopBackv6", "type": "uint32", "comment": "LoopBackv6"}, {"name": "sourceVlanNov4", "type": "uint32", "comment": "sourceVlanNov4"}, {"name": "sourceVlanNov6", "type": "uint32", "comment": "sourceVlanNov6"}, {"name": "server", "type": "record", "nullable": true, "array": true, "size": 1024, "comment": "server Info", "fields": [{"name": "position", "type": "uint32", "comment": "position"}, {"name": "serverType", "type": "uint32", "comment": "serverType"}, {"name": "stIPv6Addr", "type": "fixed", "size": 16, "comment": "IPv6 Addr"}, {"name": "ulIPv4Addr", "type": "uint32", "comment": "IPv4 Addr"}, {"name": "ulGroup", "type": "uint32", "comment": "Group"}, {"name": "usIPv6VrfIndex", "type": "uint16", "comment": "IPv6 VrfIndex"}, {"name": "szVpnInstanceName", "type": "bytes", "nullable": true, "size": 64, "comment": "Vpn Instance Name"}, {"name": "szIPv6VpnInstanceName", "type": "bytes", "nullable": true, "size": 64, "comment": "IPv6 Vpn Instance Name"}, {"name": "usIPv6PortNumber", "type": "uint16", "comment": "IPv6 Port Number"}, {"name": "usPortNumber", "type": "uint16", "comment": "Port Number"}, {"name": "szSharedKey", "type": "bytes", "nullable": true, "size": 512, "comment": "Shared Key"}, {"name": "szCipherSharedKey", "type": "bytes", "nullable": true, "size": 1024}, {"name": "szIPv6SharedKey", "type": "bytes", "nullable": true, "size": 512}, {"name": "szIPv6CipherSharedKey", "type": "bytes", "nullable": true, "size": 1024, "comment": "IPv6 Cipher Shared Key"}]}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}], "keys": [{"name": "tac_group_data_pk", "index": {"type": "primary"}, "node": "tac_group_data", "fields": ["ulVsysID", "szName"], "constraints": {"unique": true}, "comment": "primary key of tac_group_data"}, {"name": "server_key", "node": "server", "fields": ["position", "serverType"], "constraints": {"unique": true}, "comment": "server key of tac_group_data"}]}