{"comment": "存储全局数据", "version": "2.0", "type": "record", "name": "qos_globle_cfg", "config": {"check_validity": false}, "max_record_count": 256, "fields": [{"name": "cfg_type", "type": "uint32", "comment": "全局变量的类型"}, {"name": "vrid", "type": "uint32", "comment": "虚拟路由"}, {"name": "value1", "type": "uint32", "comment": "第一个值"}, {"name": "value2", "type": "uint32", "comment": "第二个值"}, {"name": "reserve", "type": "uint32", "comment": "保留"}], "keys": [{"name": "index", "index": {"type": "primary"}, "node": "qos_globle_cfg", "fields": ["cfg_type", "vrid"], "constraints": {"unique": true}, "comment": "主键索引"}]}