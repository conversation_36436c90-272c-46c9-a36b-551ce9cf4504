{"comment": "ACL Time Range表", "version": "2.0", "type": "record", "name": "acl_time_range", "max_record_count": 8192, "fields": [{"name": "time_range_id", "type": "uint32", "comment": "Time Range的索引"}, {"name": "vrid", "type": "uint32", "comment": "虚拟路由器的索引"}, {"name": "inner_id", "type": "uint32", "comment": "Time Range的内部索引"}, {"name": "active_status", "type": "uint32", "comment": "Time Range的激活状态"}, {"name": "app_source_id", "type": "uint32", "comment": "生产者源标识，对应VRP8 hSrcPid字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "生产者源标识"}, {"name": "app_obj_id", "type": "uint64", "comment": "用于记录的生命周期管理，即使KEY和DATA相同，但删除后再添加时这个ID也会不同，具体使用场景不明确，暂时保留不用"}, {"name": "app_version", "type": "uint32", "comment": "记录版本号，用于跟踪同一条记录的变化情形，具体使用场景不明确，暂时保留不用"}], "keys": [{"name": "acl_time_range_pk", "index": {"type": "primary"}, "node": "acl_time_range", "fields": ["time_range_id"], "constraints": {"unique": true}, "comment": "根据主键索引"}]}