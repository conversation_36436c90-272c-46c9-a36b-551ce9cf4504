{"comment": "保存fes表的版本号表项", "version": "2.0", "type": "record", "name": "evpn_pid_table", "config": {"check_validity": false}, "max_record_count": 1024, "fields": [{"name": "table_id", "type": "uint32", "comment": "fes表ID"}, {"name": "pid", "type": "uint32", "comment": "生产者的pid"}, {"name": "smooth_id", "type": "uint32", "comment": "平滑的版本号"}, {"name": "db_type", "type": "uint32", "comment": "DB定义的类型"}, {"name": "smooth_state", "type": "uint32", "comment": "平滑状态"}], "keys": [{"name": "table_key", "index": {"type": "primary"}, "node": "evpn_pid_table", "fields": ["table_id", "pid"], "constraints": {"unique": true}, "comment": "根据表和pid查询"}, {"name": "pid", "index": {"type": "hashcluster"}, "node": "evpn_pid_table", "fields": ["pid"], "constraints": {"unique": false}, "comment": "根据pid索引"}]}