#include "st_common.h"
#include "gmc.h"
#include "tools_st_common.h"
#include "storage_st_common.h"

#ifndef FEATURE_PERSISTENCE
using namespace std;

const char *normal_label_name = "CHECK_LABEL";
const char *test_config_json = R"({"max_record_count":400000})";
const char *test_config_json_no_lite = R"({"max_record_count":400000, "isFastReadUncommitted":false})";
const char *test_normal_label_json =
    R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"}
                    },
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

const char *partition_label_name = "CHECK_PARTITION_LABEL";
const char *test_partition_label_json =
    R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
class StQueryCheck : public StTestSuitBase {};

TEST_F(StQueryCheck, CreateKvTableCheckErr)
{
    const char *cfgJson = R"({"max_record_count":100})";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    char kvTableName[] = "student";
    int32_t ret = GmcKvCreateTable(stmt, kvTableName, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, kvTableName, 0xff);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcEndCheck(stmt, kvTableName, 0xff, true);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testCreateDeltaStoreCheckErr)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelName = "TRUN";
    const char *test_mainS_config_json = R"({"max_record_count":1000, "delta_store_name":"zhou", "writers":"XXuser"})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"TRUN",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"TRUN",
                        "name":"TRUN_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // test point: 创建vertex lable
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, labelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, labelName, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void *app1_begin_check_not_end(void *arg)
{
    const char *normal_label_name = "CHECK_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *app2_begin_check_error(void *arg)  // app1 not end check
{
    const char *normal_label_name = "CHECK_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *app3_end_check_abnormal(void *arg)  // app1 not end check
{
    const char *normal_label_name = "CHECK_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcEndCheck(stmt, normal_label_name, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *three_app_begin_check_atTheSameTime(void *arg)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    GmcBeginCheck(stmt, partition_label_name, 14);
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *app7_begin_check_partition_1(void *arg)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, partition_label_name, 1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, partition_label_name, 1, false);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *app8_begin_check_partition_2(void *arg)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, partition_label_name, 2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, partition_label_name, 2, false);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *app9_begin_check_partition_3(void *arg)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, partition_label_name, 3);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, partition_label_name, 3, false);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *AppBeginCheckPartition(void *arg)
{
    uint8_t partitionId = *(uint8_t *)arg;
    const char *labelName = "CHECK_PARTITION_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcBeginCheck(stmt, labelName, partitionId);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, labelName, partitionId, false);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StQueryCheck, testCheck)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *test_partition_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建非分区表
    ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建分区表
    ret = GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取元数据

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 分区表插入数据
    value_F0 = (char *)"aaa";
    value_F1 = 100;
    uint8_t value_F2 = 1;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    value_F2 = 2;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"ccc";
    value_F1 = 300;
    value_F2 = 15;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"ddd";
    value_F1 = 400;
    value_F2 = 15;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;

    // 非分区表 全表对账，分区不为0xff 报错
    ret = GmcBeginCheck(stmt, normal_label_name, 1);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    const char *lastErrorStr1 = GmcGetLastError();
    const char *result =
        "Program limit exceeded. If partition label, partition ID 1 exceeds limit: 16, else partition ID must be 255.";
    EXPECT_STREQ(result, lastErrorStr1);

    // 全表对账，分区为0xff 成功
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 未结束对账，再次开始对账，报错
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 非分区表，分区不为0xFF,查询checkInfo报错
    ret = GmcGetCheckInfo(stmt, normal_label_name, 100, &checkInfo);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    // 未启动对账时，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 分区对账，分区不为 0到15 报错
    ret = GmcBeginCheck(stmt1, partition_label_name, 0xff);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    lastErrorStr1 = GmcGetLastError();
    result = (char *)"Program limit exceeded. If partition label, partition ID 255 exceeds limit: 16, else partition "
                     "ID must be 255.";
    EXPECT_STREQ(result, lastErrorStr1);

    // 分区对账，分区为15 成功
    ret = GmcBeginCheck(stmt1, partition_label_name, 15);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动对账后，查询checkInfo，显示checking状态
    ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_CHECKING);

    // 对账期间不允许删表
    ret = GmcDropVertexLabel(stmt1, partition_label_name);
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, ret);

    // 结束对账
    ret = GmcEndCheck(stmt1, partition_label_name, 15, true);
    EXPECT_EQ(GMERR_OK, ret);

    // 分区表，分区不在0-15范围内，查询checkInfo报错
    ret = GmcGetCheckInfo(stmt1, partition_label_name, 100, &checkInfo);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    lastErrorStr1 = GmcGetLastError();
    result = (char *)"Program limit exceeded. If partition label, partition ID 100 exceeds limit: 16, else partition "
                     "ID must be 255.";
    EXPECT_STREQ(result, lastErrorStr1);

    pthread_t tid[9];

    // 启动对账后异常终止对账，再次启动报错
    ret = pthread_create(&tid[0], NULL, app1_begin_check_not_end, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[0], NULL);

    ret = pthread_create(&tid[1], NULL, app2_begin_check_error, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[1], NULL);

    ret = pthread_create(&tid[2], NULL, app3_end_check_abnormal, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[2], NULL);

    // 多线程并发启动对账，只有一个线程返回成功
    uint8_t oldCheckVersion, newCheckVersion;
    ret = GmcGetCheckInfo(stmt, partition_label_name, 14, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);

    oldCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;

    ret = pthread_create(&tid[3], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[4], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[5], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[3], NULL);
    pthread_join(tid[4], NULL);
    pthread_join(tid[5], NULL);

    ret = GmcGetCheckInfo(stmt, partition_label_name, 14, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    newCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;
    EXPECT_EQ(newCheckVersion, oldCheckVersion + 1);

    // 分区表，多线程同时启动不同分区的对账, 都成功
    ret = pthread_create(&tid[6], NULL, app7_begin_check_partition_1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[7], NULL, app8_begin_check_partition_2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[8], NULL, app9_begin_check_partition_3, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[6], NULL);
    pthread_join(tid[7], NULL);
    pthread_join(tid[8], NULL);

    ret = GmcEndCheck(stmt, partition_label_name, 14, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt1, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testCheckUpdateCheckVersion)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    int32_t row1_f0 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &row1_f0, sizeof(row1_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &row1_f0, sizeof(row1_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t row2_f0 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t row3_f0 = 300;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &row3_f0, sizeof(row3_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &row3_f0, sizeof(row3_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 未开启对账，更新报错
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE_VERSION));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row1_f0, sizeof(row1_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_TABLE_NOT_IN_CHECKING, ret);

    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 非主键报错
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE_VERSION));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX1"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row1_f0, sizeof(row1_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ("Feature is not supported. Update check version request CHECK_LABEL_INDEX1 should be a primary key.",
        lastErrorStr);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE_VERSION));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row1_f0, sizeof(row1_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE_VERSION));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex 目前查询结果是0条，等适配之后是2条
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0)));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = 0;
    bool isNull;
    int expectF0 = 200;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(false, isNull);
        EXPECT_EQ(expectF0, value);
        expectF0 = 100;
    }

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testDmlDuringChecking)
{

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // 对账过程中，写入10条新数据(100-109)
    for (int32_t i = 100; i < 110; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    // 对账过程中，修改10条老版本的数据(0-9更新，不用老化)
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，删除10条老版本的数据(10-19删除，不用老化)
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_DELETE));
    for (int32_t i = 10; i < 20; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，删除新版本数据，不影响统计
    for (int32_t i = 100; i < 105; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE));
    for (int32_t i = 105; i < 110; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 结束对账，需要老化80条数据
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(80, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testRollbackDuringChecking)
{

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json_no_lite);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账过程中，修改10条老版本的数据, 然后回滚
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransRollBack(conn);
    EXPECT_EQ(GMERR_OK, ret);
    // 对账过程中，再修改10条老版本的数据, 然后提交事务
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账，需要老化90条数据
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(90, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testTruncateDuringChecking)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    // 对账过程中，修改10条老版本的数据
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 对账过程中，执行truncate
    ret = GmcTruncateVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账，需要老化0条数据
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(0, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void checkPrintLog(char *substr, bool *matched)
{
    int32_t fd = DB_INVALID_FD;
    size_t rowLen;
    int32_t ret;
    char rowBuf[LOG_MAX_SIZE_OF_LOG_MSG];
    char *runLogDir = (char *)"./log/run/rgmserver/rgmserver.log";

    (void)DbOpenFile(runLogDir, READ_ONLY, PERM_GRPR, &fd);

    while (true) {
        (void)memset_s(rowBuf, LOG_MAX_SIZE_OF_LOG_MSG, 0, LOG_MAX_SIZE_OF_LOG_MSG);
        ret = DbReadLine(fd, rowBuf, LOG_MAX_SIZE_OF_LOG_MSG, &rowLen);
        if (ret != GMERR_OK) {
            DbCloseFile(fd);
            fd = DB_INVALID_FD;
            break;
        }
        if (strstr(rowBuf, substr) != NULL) {
            *matched = true;
            break;
        }
    }
    if (fd != DB_INVALID_FD) {
        DbCloseFile(fd);
        fd = DB_INVALID_FD;
    }
}

#define TEST_QRY_AGE_BATCH 300

TEST_F(StQueryCheck, testAge)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建非分区表
    ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建分区表
    ret = GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));

    for (int32_t value = 0; value < TEST_QRY_AGE_BATCH; value++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint8_t f3;
    for (int32_t value = 0; value < TEST_QRY_AGE_BATCH; value++) {
        f3 = (uint8_t)(value % 16);
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    uint32_t expectedCnt;
    uint32_t recoveryCnt;
    GmcCheckInfoT *checkInfo;
    // case 1: 全表Recovery
    // 操作：开启对账，isAbnormal = true.
    // 预期：Recovery，记录剩余300条，version = 1.
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    expectedCnt = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE));
    for (int32_t i = 0; i < TEST_QRY_AGE_BATCH; i += 2) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        expectedCnt++;
    }

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realRecoveryCnt < expectedCnt) {
        ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 0);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 0);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, expectedCnt);

    // case 2: 全表Delete
    // 操作：开启对账，isAbnormal = false.
    // 预期：Delete， 记录剩余150条，version = 2.
    int32_t setValue = 1;
    ret = GmcSetCfg(stmt, "enablePrintAgedInfo", GMC_DATATYPE_INT32, &setValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    recoveryCnt = expectedCnt;
    expectedCnt = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_UPDATE));
    for (int32_t i = 0; i < TEST_QRY_AGE_BATCH; i += 2) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        expectedCnt++;
    }

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < expectedCnt) {
        ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realAgedCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, recoveryCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, recoveryCnt);
#ifndef RTOSV2X  // 暂不支持DbOpenFile
    bool matched = false;
    char *subStr = (char *)"Aged record, nameSpace:public, tableName:CHECK_LABEL, partition:255, F0: 9.";
    checkPrintLog(subStr, &matched);
    EXPECT_EQ(true, matched);
#endif
    // case 3: 分区表Recovery
    // 操作：开启对账，partitionId = 15, isAbnormal = true.
    // 预期：Recovery，version = 1.
    ret = GmcBeginCheck(stmt1, partition_label_name, 15);
    EXPECT_EQ(GMERR_OK, ret);

    expectedCnt = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_UPDATE));
    for (int32_t i = 15; i < TEST_QRY_AGE_BATCH / 2; i += 16) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "CHECK_PARTITION_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        expectedCnt++;
    }

    ret = GmcEndCheck(stmt1, partition_label_name, 15, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realRecoveryCnt < expectedCnt) {
        ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, expectedCnt);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 0);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 0);

    // case 4: 分区表Delete
    // 操作：开启对账，partitionId = 15, isAbnormal = false.
    // 预期：Delete， version = 2.
    ret = GmcBeginCheck(stmt1, partition_label_name, 15);
    EXPECT_EQ(GMERR_OK, ret);

    recoveryCnt = expectedCnt;
    expectedCnt = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_UPDATE));
    for (int32_t i = 15; i < TEST_QRY_AGE_BATCH; i += 32) {
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "CHECK_PARTITION_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
        expectedCnt++;
    }

    ret = GmcEndCheck(stmt1, partition_label_name, 15, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < expectedCnt) {
        ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realAgedCnt, expectedCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, recoveryCnt);
    EXPECT_EQ(((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, recoveryCnt);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt1, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testSubsSystemFields)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, NULL));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "constraint": {
            "operator_type": "and",
            "conditions": [
                { "property": "sys_check_version" }
            ]
        }
    }
    )";

    auto callback = [](GmcStmtT *, const GmcSubMsgInfoT *, void *) {};

    ret = GmcSubscribe(stmt, &config, subChan, callback, NULL);
    EXPECT_EQ(GMERR_INVALID_OBJECT_DEFINITION, ret);

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryCheck, testSubPushVertexOnUpdateCheckVersion_NoChange)
{
    GmcConnT *subChan = NULL;
    const char *subConnName = "subConnName1";
    int32_t ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subConnName, &subChan);
    EXPECT_EQ(GMERR_OK, ret);

    GmcConnT *syncConn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&syncConn, &stmt);

    const char *subLabelName = "subLabel1";
    const char *subLabelJson =
        R"([{
            "type":"record",
            "name":"subLabel1",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"subLabel1",
                        "name":"subLabel1_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, subLabelJson, NULL));

    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"(
    {
        "label_name": "subLabel1",
        "comment": "VertexLabel1 subscription",
        "events": [{ "type": "update" , "msgTypes":["new object", "old object"]}],
        "retry": true,
        "is_reliable":true
    }
    )";

    auto callback = [](GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData) {
        uint32_t &count = *reinterpret_cast<uint32_t *>(userData);

        EXPECT_EQ(GMC_SUB_EVENT_UPDATE, info->eventType);
        EXPECT_EQ(1u, info->labelCount);

        char pushLabelName[MAX_TABLE_NAME_LEN] = {};
        uint32_t pushLabelNameSize = sizeof(pushLabelName);
        int ret = GmcSubGetLabelName(subStmt, 0, pushLabelName, &pushLabelNameSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(strlen(pushLabelName), pushLabelNameSize);
        EXPECT_STREQ("subLabel1", pushLabelName);

        for (bool eof;; ++count) {
            ret = GmcFetch(subStmt, &eof);
            EXPECT_EQ(GMERR_OK, ret);
            if (eof) {
                break;
            }

            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            EXPECT_EQ(GMERR_OK, ret);

            uint32_t propSize;
            ret = GmcGetVertexPropertySizeByName(subStmt, "F0", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int32_t), propSize);

            uint32_t F0V;
            bool isNull;
            ret = GmcGetVertexPropertyByName(subStmt, "F0", &F0V, sizeof(int32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(1, (int32_t)F0V);
        }
    };

    uint32_t received = 0;
    ret = GmcSubscribe(stmt, &config, subChan, callback, &received);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_INSERT));

    uint32_t total = 3;
    for (unsigned int i = 0; i < total; i++) {
        // insert
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, 4);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, subLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    total = 1;
    int32_t F0Value = 2;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_UPDATE_VERSION));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 1;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, subLabelName, GMC_OPERATION_UPDATE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "subLabel1_K0"));
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F0Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F0Value, 4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, subLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    /* wait untill all events are received */
    while (received != total) {
        printf("received: %u, total: %u.\n", received, total);
        DbSleep(1);
    }

    ret = GmcDisconnect(subChan);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, "subVertexLabel1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, subLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(syncConn, stmt);
}

TEST_F(StQueryCheck, testcataLog16)
{
    ASSERT_TRUE(CltCataIsEmpty());
}

#define TEST_QRY_AGE_NUM 300
TEST_F(StQueryCheck, testAgeTaskView)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));

    uint8_t f3;
    for (int32_t value = 0; value < TEST_QRY_AGE_NUM; value++) {
        f3 = (uint8_t)(value % 16);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char cmd[1024];
    char viewRes[1024];
    char const *viewName = "V\\$QRY_AGE_TASK";
    snprintf(cmd, 1024, "gmsysview -s %s -q %s", serverLocator, viewName);
    memset_s(viewRes, 1024, 0, 1024);

    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcBeginCheck(stmt, partition_label_name, partitionIdx);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    sleep(1);
    ret = executeCommand(cmd, "TASK_STATUS: FINISHED", "START_TIME", "FINISH_TIME");
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(cmd, "PARTITION_ID: 13", "PARTITION_ID: 14", "PARTITION_ID: 15");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgeTaskViewLabelNull)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t f3;
    for (int32_t value = 0; value < 300; value++) {
        f3 = (uint8_t)(value % 16);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    char cmd[1024];
    char viewRes[1024];
    char const *viewName = "V\\$QRY_AGE_TASK";
    snprintf(cmd, 1024, "gmsysview -s %s -q %s", serverLocator, viewName);
    (void)memset_s(viewRes, 1024, 0, 1024);

    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcBeginCheck(stmt, partition_label_name, partitionIdx);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 在老化任务完成后，删除对应的label，查阅老化视图
    DbUsleep(1);
    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = executeCommand(cmd, "LABEL_NAME: null");
    ASSERT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testIsAgingTaskView)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcDropVertexLabel(stmt, normal_label_name);
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 20000; i++) {  // 大数据量，避免查视图时老化已结束
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    char cmd[1024];
    char const *viewName = "V\\$QRY_AGE_TASK";
    snprintf(cmd, 1024, "gmsysview -s %s -q %s", serverLocator, viewName);

    // 校验无老化任务时老化视图可以正常返回
    ret = executeCommand(cmd, "fetched", "finish");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = executeCommand(cmd, "LABEL_NAME: CHECK_LABEL");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgeStatistic)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));

    uint8_t f3;
    for (int32_t value = 0; value < 1600; value++) {
        f3 = (uint8_t)(value % 16);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int32_t loop = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
    while (loop < 2) {
        for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
            ret = GmcBeginCheck(stmt, partition_label_name, partitionIdx);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (loop % 2 != 0) {
            for (int32_t value = 0; value < 1600; value++) {
                f3 = (uint8_t)(value % 16);
                ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
            ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
        loop++;
    }

    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->shouldAgedCnt < 100) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 100);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 100);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgePrint)
{
    const char *all_key_label_name = "ALL_PRIMARY_KEY_LABEL";
    const char *all_key_label_json =
        R"([{
            "type":"record",
            "name":"ALL_PRIMARY_KEY_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"string", "size":300},
                    {"name":"F3", "type":"fixed","size":11, "nullable":false},
                    {"name":"F4", "type":"float"},
                    {"name":"F5", "type":"double"},
                    {"name":"F6", "type":"uint32"},
                    {"name":"F7", "type":"bytes", "size":10, "default":"FFFF"},
                    {"name":"F8", "type":"fixed","size":180, "nullable":false},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ALL_PRIMARY_KEY_LABEL",
                        "name":"AGE_PRINT_LABEL_INDEX",
                        "fields":["F0","F1","F3","F6","F7","F8"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, all_key_label_json, test_config_json);
    ASSERT_EQ(GMERR_OK, ret);

    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, all_key_label_name, GMC_OPERATION_INSERT));

    uint8_t partitionProp;
    char *strProp1 =
        (char *)"111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111\
111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111\
111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111";
    char *strProp2 = (char *)"2222222222";
    char *fixProp1 = (char *)"11.11.11.11";
    char *fixProp2 =
        (char *)"22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.\
22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.";
    float floatProp = 0.111222;
    double doubleProp = 123.45678987654321;

    for (uint32_t i = 0; i < 10; i++) {
        partitionProp = i % 16;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        if (i % 2 == 0) {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, strProp1, strlen(strProp1));
        } else {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, strProp2, strlen(strProp2));
        }
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionProp, sizeof(uint8_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, fixProp1, strlen(fixProp1));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FLOAT, &floatProp, sizeof(float));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_DOUBLE, &doubleProp, sizeof(double));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_FIXED, fixProp2, strlen(fixProp2));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcBeginCheck(stmt, all_key_label_name, partitionIdx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, all_key_label_name, partitionIdx, false);
        ASSERT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, all_key_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgePrintAbnormal)
{
    const char *all_key_label_name = "ALL_PRIMARY_KEY_LABEL";
    const char *all_key_label_json =
        R"([{
            "type":"record",
            "name":"ALL_PRIMARY_KEY_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"string", "size":300},
                    {"name":"F3", "type":"fixed","size":11, "nullable":false},
                    {"name":"F4", "type":"float"},
                    {"name":"F5", "type":"double"},
                    {"name":"F6", "type":"uint32"},
                    {"name":"F7", "type":"bytes", "size":10, "default":"FFFF"},
                    {"name":"F8", "type":"fixed","size":180, "nullable":false},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"ALL_PRIMARY_KEY_LABEL",
                        "name":"AGE_PRINT_LABEL_INDEX",
                        "fields":["F0","F1","F3","F6","F7","F8"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, all_key_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, all_key_label_name, GMC_OPERATION_INSERT));

    int32_t setValue = 1;
    ret = GmcSetCfg(stmt, "enablePrintAgedInfo", GMC_DATATYPE_INT32, &setValue, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t partitionProp;
    char *strProp1 =
        (char *)"111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111\
111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111\
111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111";
    char *strProp2 = (char *)"2222222222";
    char *fixProp1 = (char *)"11.11.11.11";
    char *fixProp2 =
        (char *)"22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.\
22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.22.";
    float floatProp = 0.111222;
    double doubleProp = 123.45678987654321;

    for (uint32_t i = 0; i < 5000; i++) {
        partitionProp = i % 16;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        if (i % 2 == 0) {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, strProp1, strlen(strProp1));
        } else {
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_STRING, strProp2, strlen(strProp2));
        }
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionProp, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_FIXED, fixProp1, strlen(fixProp1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_FLOAT, &floatProp, sizeof(float));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_DOUBLE, &doubleProp, sizeof(double));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_FIXED, fixProp2, strlen(fixProp2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int32_t partitionIdx = 0; partitionIdx < 16; partitionIdx++) {
        ret = GmcBeginCheck(stmt, all_key_label_name, partitionIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, all_key_label_name, partitionIdx, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, all_key_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DbSleep(100);

    DestroyConnectionAndStmt(conn, stmt);
}

#define QRY_MAX_RECORD_IN_SPLIT 880
TEST_F(StQueryCheck, testFullTabAgeWithSplitTask)
{
    // edgeLabel
    const char *edgeLabelName = "ageEdgelable";
    const char *EdgeCfgJson = R"({"max_record_count":1000})";
    const char *edgeLabelJson =
        R"([{
            "name":"ageEdgelable",
            "source_vertex_label":"srcVertexLabel","comment":"the edge 7 to 8",
            "dest_vertex_label":"dstVertexLabel",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // srcVertexLabel
    const char *srcVertexLabelName = "srcVertexLabel";
    const char *srcCfgJson = R"({"max_record_count":1000, "push_age_record_batch":200, "isFastReadUncommitted":false})";
    const char *srcVertexLabelJson =
        R"([{
            "type":"record",
            "name":"srcVertexLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"srcVertexLabel",
                        "name":"srcVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    // destVertexLabel
    const char *dstVertexLabelName = "dstVertexLabel";
    const char *dstCfgJson = R"({"max_record_count":1000, "push_age_record_batch":200, "isFastReadUncommitted":false})";
    const char *dstVertexLabelJson =
        R"([{
            "type":"record",
            "name":"dstVertexLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"dstVertexLabel",
                        "name":"dstVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 建立连接
    GmcConnT *connUSCOK1;
    GmcStmtT *stmt1 = NULL;
    CreateSyncConnectionAndStmt(&connUSCOK1, &stmt1);

    GmcStmtT *stmt2 = NULL;
    status_t ret = GmcAllocStmt(connUSCOK1, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmt3 = NULL;
    ret = GmcAllocStmt(connUSCOK1, &stmt3);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建非分区表
    ret = GmcCreateVertexLabel(stmt1, srcVertexLabelJson, srcCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt2, dstVertexLabelJson, dstCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置自动建出入边
    int setDirect = GMC_EDGE_DIRECTION_BOTH;
    ret = GmcSetStmtAttr(stmt3, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &setDirect, sizeof(setDirect));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned int getDirect = 9999;
    ret = GmcGetStmtAttr(stmt3, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &getDirect, sizeof(getDirect));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((unsigned int)GMC_EDGE_DIRECTION_BOTH, getDirect);

    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(stmt3, edgeLabelJson, EdgeCfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    void *edgeLabel = NULL;
    ret = GmcOpenEdgeLabelByName(stmt3, edgeLabelName, &edgeLabel);
    EXPECT_EQ(GMERR_OK, ret);

    // srcLabel 插入数据
    ret = GmcPrepareStmtByLabelName(stmt1, srcVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t value = 0; value < QRY_MAX_RECORD_IN_SPLIT; value++) {
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // dstLabel 插入数据
    ret = GmcPrepareStmtByLabelName(stmt2, dstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t value = 0; value < QRY_MAX_RECORD_IN_SPLIT; value++) {
        ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcCheckInfoT *checkInfo;
    // case 1: 全表Recovery
    // 操作：开启对账，isAbnormal = true.
    // 预期：Recovery，记录剩余880条，version = 1.
    ret = GmcBeginCheck(stmt1, srcVertexLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt1, srcVertexLabelName, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realRecoveryCnt < QRY_MAX_RECORD_IN_SPLIT) {
        ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, QRY_MAX_RECORD_IN_SPLIT);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, QRY_MAX_RECORD_IN_SPLIT);
    // case 2: 全表age
    // 操作：开启对账，isAbnormal = false.
    // 预期：age，记录剩余0条，version = 2.
    ret = GmcBeginCheck(stmt1, srcVertexLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt1, srcVertexLabelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < QRY_MAX_RECORD_IN_SPLIT) {
        ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, 0xff, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, QRY_MAX_RECORD_IN_SPLIT);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, QRY_MAX_RECORD_IN_SPLIT);

    ret = GmcCloseEdgeLabel(stmt3, edgeLabel);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt1, srcVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt2);
    GmcFreeStmt(stmt3);
    DestroyConnectionAndStmt(connUSCOK1, stmt1);
}

TEST_F(StQueryCheck, testPartTabAgeWithSplitTask)
{
    // edgeLabel
    const char *edgeLabelName = "ageEdgelable";
    const char *EdgeCfgJson = R"({"max_record_count":1000})";
    const char *edgeLabelJson =
        R"([{
            "name":"ageEdgelable",
            "source_vertex_label":"srcVertexLabel","comment":"the edge 7 to 8",
            "dest_vertex_label":"dstVertexLabel",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // srcVertexLabel
    const char *srcVertexLabelName = "srcVertexLabel";
    const char *srcCfgJson = R"({"max_record_count":1000, "push_age_record_batch":200, "isFastReadUncommitted":false})";
    const char *srcVertexLabelJson =
        R"([{
            "type":"record",
            "name":"srcVertexLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"partition", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"srcVertexLabel",
                        "name":"srcVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    // destVertexLabel
    const char *dstVertexLabelName = "dstVertexLabel";
    const char *dstCfgJson = R"({"max_record_count":1000, "push_age_record_batch":200, "isFastReadUncommitted":false})";
    const char *dstVertexLabelJson =
        R"([{
            "type":"record",
            "name":"dstVertexLabel",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"partition", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"dstVertexLabel",
                        "name":"dstVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 建立连接
    GmcConnT *connUSCOK1;
    GmcStmtT *stmt1 = NULL;
    CreateSyncConnectionAndStmt(&connUSCOK1, &stmt1);

    GmcStmtT *stmt2 = NULL;
    status_t ret = GmcAllocStmt(connUSCOK1, &stmt2);
    ASSERT_EQ(GMERR_OK, ret);

    GmcStmtT *stmt3 = NULL;
    ret = GmcAllocStmt(connUSCOK1, &stmt3);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建分区表
    ret = GmcCreateVertexLabel(stmt1, srcVertexLabelJson, srcCfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt2, dstVertexLabelJson, dstCfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置自动建出入边
    int setDirect = GMC_EDGE_DIRECTION_BOTH;
    ret = GmcSetStmtAttr(stmt3, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &setDirect, sizeof(setDirect));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned int getDirect = 9999;
    ret = GmcGetStmtAttr(stmt3, GMC_STMT_ATTR_AUTO_CREATE_EDGE, &getDirect, sizeof(getDirect));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ((unsigned int)GMC_EDGE_DIRECTION_BOTH, getDirect);

    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(stmt3, edgeLabelJson, EdgeCfgJson);
    ASSERT_EQ(GMERR_OK, ret);

    void *edgeLabel = NULL;
    ret = GmcOpenEdgeLabelByName(stmt3, edgeLabelName, &edgeLabel);
    ASSERT_EQ(GMERR_OK, ret);

    // srcLabel 插入数据
    ret = GmcPrepareStmtByLabelName(stmt1, srcVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t partitionId;
    for (int32_t value = 0; value < QRY_MAX_RECORD_IN_SPLIT; value++) {
        partitionId = (value % 16);
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // dstLabel 插入数据
    ret = GmcPrepareStmtByLabelName(stmt2, dstVertexLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    for (int32_t value = 0; value < QRY_MAX_RECORD_IN_SPLIT; value++) {
        partitionId = (value % 16);
        ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F1", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F2", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt2, "F3", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcCheckInfoT *checkInfo;
    // case 1: 分区表Recovery
    // 操作：开启对账，isAbnormal = true.
    // 预期：Recovery，每个分区记录剩余880/16=55条，version = 1.
    for (int32_t i = 0; i < 16; i++) {
        ret = GmcBeginCheck(stmt1, srcVertexLabelName, i);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < 16; i++) {
        ret = GmcEndCheck(stmt1, srcVertexLabelName, i, true);
        ASSERT_EQ(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < 16; i++) {
        ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, i, &checkInfo);
        ASSERT_EQ(GMERR_OK, ret);
        while (((GmcCheckInfoT *)checkInfo)->realRecoveryCnt < QRY_MAX_RECORD_IN_SPLIT / 16) {
            ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, i, &checkInfo);
            ASSERT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt, QRY_MAX_RECORD_IN_SPLIT / 16);
        EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realRecoveryCnt, QRY_MAX_RECORD_IN_SPLIT / 16);
    }
    // case 2: 分区表age
    // 操作：开启对账，isAbnormal = false.
    // 预期：age，每个分区记录剩余0条，version = 2.
    for (int32_t i = 0; i < 16; i++) {
        ret = GmcBeginCheck(stmt1, srcVertexLabelName, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int32_t i = 0; i < 16; i++) {
        ret = GmcEndCheck(stmt1, srcVertexLabelName, i, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int32_t i = 0; i < 16; i++) {
        ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, i, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
        while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < QRY_MAX_RECORD_IN_SPLIT / 16) {
            ret = GmcGetCheckInfo(stmt1, srcVertexLabelName, i, &checkInfo);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, QRY_MAX_RECORD_IN_SPLIT / 16);
        EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, QRY_MAX_RECORD_IN_SPLIT / 16);
    }

    ret = GmcCloseEdgeLabel(stmt3, edgeLabel);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt1, srcVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt2);
    GmcFreeStmt(stmt3);
    DestroyConnectionAndStmt(connUSCOK1, stmt1);
}

//  空表 异常结束对账，对账状态要为normal
TEST_F(StQueryCheck, testCheckNoDataAbnormal)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    GmcCheckInfoT *checkInfo = NULL;
    GmcCheckStatusE checkStatus;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    int32_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, true);
    EXPECT_EQ(GMERR_OK, ret);

    do {
        ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetCheckStatus(checkInfo, &checkStatus);
        EXPECT_EQ(GMERR_OK, ret);
        printf("the staus is %d.\n", checkStatus);
        DbSleep(1000);
    } while (checkStatus != GMC_CHECK_STATUS_NORMAL);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgeCheckCnt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    int32_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    for (int32_t i = 0; i < 260; i++) {
        ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

#define PARTITION_NUM_16 16

void *testMergeAgedVertex(void *arg)
{
    Status ret;
    GmcConnT *conn;
    GmcStmtT *stmt;
    int8_t partitionId = *(int8_t *)arg;
    int32_t idx = (int32_t)partitionId;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    ret = GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    while (idx < 100) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &idx, sizeof(idx));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "CHECK_PARTITION_LABEL_INDEX");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &idx, sizeof(idx));
        EXPECT_EQ(GMERR_OK, ret);
        partitionId = idx % PARTITION_NUM_16;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(partitionId));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        idx += PARTITION_NUM_16;
    }
    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StQueryCheck, testMergeAndAgeParallel)
{
    int32_t ret;
    GmcConnT *conn;
    GmcStmtT *stmt;
    int8_t partitionId;
    GmcCheckInfoT *checkInfo;
    bool isAbnormal = false;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json_no_lite));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));

    // 预置100条数据(0-159)
    for (int32_t i = 0; i < 1600; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        partitionId = (int8_t)(i % PARTITION_NUM_16);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(partitionId));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_t tid[PARTITION_NUM_16];
    for (int i = 0; i < PARTITION_NUM_16; i++) {
        partitionId = (int8_t)i;
        ret = pthread_create(&tid[i], NULL, testMergeAgedVertex, &partitionId);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = PARTITION_NUM_16 - 1; i >= 0; i--) {
        ret = GmcBeginCheck(stmt, partition_label_name, i);
        EXPECT_EQ(GMERR_OK, ret);
        isAbnormal = i % 2 == 0;
        ret = GmcEndCheck(stmt, partition_label_name, i, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < PARTITION_NUM_16; i++) {
        pthread_join(tid[i], NULL);
    }
    // 检查数据realProcessCnt = shouldProcessCnt
    for (int32_t i = 0; i < PARTITION_NUM_16; i++) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
        while ((((GmcCheckInfoT *)checkInfo)->realRecoveryCnt != ((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt) ||
               (((GmcCheckInfoT *)checkInfo)->realAgedCnt != ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt)) {
            ret = GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    char cmd[1024];
    char const *viewName = "V\\$QRY_AGE_TASK";
    snprintf(cmd, 1024, "gmsysview -q %s", viewName);
    ret = executeCommand(cmd, "TASK_STATUS: FINISHED", "fetched all records, finish!");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testBackGroundTruncateDuringChecking)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    // 对账过程中，修改10条老版本的数据
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，执行truncate
    ret = GmcDeleteAllFast(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_CHECKING, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    // 结束对账，需要老化0条数据
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testMultiBackGroundTruncateDuringChecking)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcCheckInfoT *checkInfo = NULL;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // begin 0 : 预置100条数据(1000-1099)
    for (int32_t i = 1000; i < 1100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    // begin 1 : 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    // 对账过程中，修改10条老版本的数据
    for (int32_t i = 0; i < 10; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，删除5条老版本的数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    for (int32_t i = 10; i < 15; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // end 1 : 对账过程中，执行truncate
    ret = GmcDeleteAllFast(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(95, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // begin 2 : 预置30条数据(100-129)
    for (int32_t i = 100; i < 130; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，删除5条老版本的数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    for (int32_t i = 100; i < 105; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // end 2 : 对账过程中，执行truncate
    ret = GmcDeleteAllFast(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(120, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // begin 3 : 预置70条数据(130-199)
    for (int32_t i = 130; i < 200; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    // 对账过程中，修改30条老版本的数据
    for (int32_t i = 130; i < 160; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对账过程中，删除5条老版本的数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_DELETE));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    for (int32_t i = 160; i < 165; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(i));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // end 3 : 对账过程中，执行truncate
    ret = GmcDeleteAllFast(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(185, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    // 结束对账，需要老化0条数据
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_CHECK_STATUS_NORMAL, ((GmcCheckInfoT *)checkInfo)->checkStatus);
    EXPECT_EQ(100, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt);
    EXPECT_EQ(185, (int64_t)((GmcCheckInfoT *)checkInfo)->shouldTruncateCnt);
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testBackGroundTruncate)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 100; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDeleteAllFast(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void *testTruncateAgedVertex(void *arg)
{
    Status ret;
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    for (int i = 0; i < 10; i++) {
        ret = GmcTruncateVertexLabel(stmt, partition_label_name);
        if (ret != GMERR_LOCK_NOT_AVAILABLE) {
            EXPECT_EQ(GMERR_OK, ret);
        }
        DbSleep(10);
    }

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StQueryCheck, testTruncateAndAgeParallel)
{
    int32_t ret;
    GmcConnT *conn;
    GmcStmtT *stmt;
    int8_t partitionId;
    GmcCheckInfoT *checkInfo;
    bool isAbnormal = false;

    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));

    // 预置100条数据(0-159)
    for (int32_t i = 0; i < 1600; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        partitionId = (int8_t)(i % PARTITION_NUM_16);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(partitionId));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_t tid;
    ret = pthread_create(&tid, NULL, testTruncateAgedVertex, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = PARTITION_NUM_16 - 1; i > 0; i--) {
        ret = GmcBeginCheck(stmt, partition_label_name, i);
        EXPECT_EQ(GMERR_OK, ret);
        isAbnormal = (1 % 2 == 0 ? true : false);
        ret = GmcEndCheck(stmt, partition_label_name, i, isAbnormal);
        EXPECT_EQ(GMERR_OK, ret);
    }

    pthread_join(tid, NULL);
    // 检查数据realProcessCnt = shouldProcessCnt
    for (int32_t i = 0; i < PARTITION_NUM_16; i++) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
        while ((((GmcCheckInfoT *)checkInfo)->realRecoveryCnt != ((GmcCheckInfoT *)checkInfo)->shouldRecoveryCnt) ||
               (((GmcCheckInfoT *)checkInfo)->realAgedCnt != ((GmcCheckInfoT *)checkInfo)->shouldAgedCnt)) {
            ret = GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    char cmd[1024];
    char viewRes[1024];
    char const *viewName = "V\\$QRY_AGE_TASK";
    snprintf(cmd, 1024, "gmsysview -q %s", viewName);
    memset_s(viewRes, 1024, 0, 1024);

    int count = StGetViewFieldResult(cmd, viewRes, 1024);
    EXPECT_GT(count, 1);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

#define AGE_DELETE_BATCH_NUM_X10 (4000)
#define MAX_NUM 64000

void QryUpdatePar(GmcStmtT *stmt1, uint8_t id)
{
    Status ret;
    for (int32_t i = 0; i < 303; i++) {
        int32_t keyvalue = id * AGE_DELETE_BATCH_NUM_X10 + i;
        int32_t value = MAX_NUM + keyvalue;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "CHECK_PARTITION_LABEL_INDEX"));
        ret = GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_INT32, &keyvalue, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *QryTestUpdateParVertex(void *arg)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn, &stmt1);
    Status ret;
    for (uint8_t i = 0; i < 16; i++) {
        ret = GmcBeginCheck(stmt1, partition_label_name, i);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_UPDATE));
    // 进行更新
    for (int32_t i = 0; i < 16; i++) {
        QryUpdatePar(stmt1, i);
    }
    for (int32_t i = 0; i < 16; i++) {
        ret = GmcEndCheck(stmt1, partition_label_name, i, false);
        EXPECT_EQ(GMERR_OK, ret);
    }
    DestroyConnectionAndStmt(conn, stmt1);
    return (void *)GMERR_OK;
}

TEST_F(StQueryCheck, testPartitionCount)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn, &stmt1);

    Status ret = GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t f3;
    for (int32_t value = 0; value < MAX_NUM; value++) {
        f3 = value / AGE_DELETE_BATCH_NUM_X10;
        ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    pthread_t tid;
    int err = pthread_create(&tid, NULL, QryTestUpdateParVertex, NULL);
    EXPECT_EQ(GMERR_OK, err);
    pthread_join(tid, NULL);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetOutputFormat(stmt1, "COUNT(1)"));
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    int64_t aggrValue = 0;
    while (!isEof) {
        ret = GmcFetch(stmt1, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        bool isNull = true;
        uint32_t aggrSize = sizeof(int64_t);
        ret = GmcGetPropertyById(stmt1, 0, &aggrValue, &aggrSize, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
    }
    EXPECT_EQ(aggrValue, 4848);
    ret = GmcDropVertexLabel(stmt1, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt1);
}

TEST_F(StQueryCheck, testDeleteVertexDuringCheck)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    // 预置100条数据(0-99)
    for (int32_t i = 0; i < 10000; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_DELETE));

    int32_t F1Value = 10;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX1");
    ASSERT_EQ(GMERR_OK, ret);
    // 删除顶点
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, "COUNT(1)");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEof = false;
    int64_t cnt = 0;
    while (!isEof) {
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        bool isNull = true;
        uint32_t size = sizeof(int64_t);
        ret = GmcGetPropertyById(stmt, 0, &cnt, &size, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
    }
    EXPECT_EQ(cnt, 0);

    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, CheckCompV3Mode)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    char *simple_table_name = (char *)"simple_table";
    char *simple_label_schema = (char *)R"([{
        "type":"record",
        "name":"simple_table",
        "fields":[
            {"name":"F0", "type":"int32"},
            {"name":"F1", "type":"int32"}
        ],
        "keys":
                [
                    {
                        "node":"simple_table",
                        "name":"simple_table_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"}
                    },
                    {
                        "node":"simple_table",
                        "name":"simple_table_K2",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
    }])";

    status_t ret = GmcCreateVertexLabel(stmt, simple_label_schema, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, simple_table_name, GMC_OPERATION_INSERT));
    int32_t row2_f0 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, simple_table_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "simple_table_K0"));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &row2_f0, sizeof(row2_f0)));
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, simple_table_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testAgeMixRecoveryAndDelete)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));

    uint8_t f3;
    for (int32_t value = 0; value < 50000; value++) {
        f3 = (uint8_t)(value % 16);
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }

    for (int32_t i = 0; i < 16; i++) {
        EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, partition_label_name, i));
    }
    for (int32_t i = 0; i < 16; i++) {
        EXPECT_EQ(GMERR_OK, GmcEndCheck(stmt, partition_label_name, i, false));
    }
    for (int32_t i = 0; i < 16; i++) {
        EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, partition_label_name, i));
        EXPECT_EQ(GMERR_OK, GmcEndCheck(stmt, partition_label_name, i, true));
    }
    for (int32_t i = 0; i < 16; i++) {
        GmcCheckInfoT *checkInfo = NULL;
        EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo));
        while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 2) {
            EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, partition_label_name, i, &checkInfo));
        }
    }

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, partition_label_name));
    DestroyConnectionAndStmt(conn, stmt);
}

typedef struct data {
    char *f0;
    int32_t f1;
} dataT;

bool CheckCmpFunc(GmcStmtT *readStmt, void *userData)
{
    dataT *data = (dataT *)userData;
    char *currF0 = data->f0;
    int32_t currF1 = data->f1;

    // F0
    char oldF0[5] = {};
    uint32_t oldF0Size;
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "F0", &oldF0Size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "F0", oldF0, oldF0Size, &isNull));

    // F1
    int32_t oldF1;
    uint32_t oldF1size;
    isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "F1", &oldF1size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "F1", &oldF1, oldF1size, &isNull));
    if (strcmp(currF0, oldF0) != 0) {
        return false;
    }
    if (currF1 != oldF1) {
        return false;
    }
    printf("check correct\n");
    return true;
}

bool CheckCmpFunc2(GmcStmtT *readStmt, void *userData)
{

    Status ret;
    // 创建非分区表
    // Status ret = GmcCreateVertexLabel(readStmt, test_normal_label_json, test_config_json);
    // EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(readStmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(readStmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(readStmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(readStmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("check correct\n");
    return true;
}

// 在prepare check_replace之前设置比较函数，报错
TEST_F(StQueryCheck, testCheckReplace0)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表对账，在prepare前进行设置比较函数
    dataT currData = {.f0 = (char *)"aaa", .f1 = 100};
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// check replace接口兼容V3，replace操作在GmcSetCheckReplaceCmpFunc内被转换为check replace操作
TEST_F(StQueryCheck, testReplaceToCheckReplace0)
{
    const char *labelName = "REPLACE_LABEL";
    const char *configJson = R"({"max_record_count":1000})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"REPLACE_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"REPLACE_LABEL",
                        "name":"REPLACE_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, labelJson, configJson);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *valueF0 = (char *)"aaa";
    int32_t valueF1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, valueF0, strlen(valueF0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &valueF1, sizeof(valueF1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 全表对账
    dataT currData = {.f0 = (char *)"aaa", .f1 = 100};
    ret = GmcBeginCheck(stmt, labelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    // 其他类型调GmcSetCheckReplaceCmpFunc报错且比较函数不能为NULL
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcSetCheckReplaceCmpFunc(stmt, NULL, &currData);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // GMC_OPERATION_REPLACE类型调GmcSetCheckReplaceCmpFunc比较函数可以为NULL且类型会被转换为GMC_OPERATION_CHECK_REPLACE
    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 100;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE));
    ret = GmcSetCheckReplaceCmpFunc(stmt, NULL, &currData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_OPERATION_CHECK_REPLACE, stmt->operationType);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, labelName, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，非结构化check_replace, 使用自定义比较函数
TEST_F(StQueryCheck, testCheckReplace11)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 全表对账，分区为0xff 成功

    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 测试点1：比较一致，只做updateCheckVersion
    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 100;
    dataT currData = {.f0 = (char *)"aaa", .f1 = 100};
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");
    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点2：比较不一致，做replace
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    check_F0 = (char *)"aaa";
    check_F1 = 200;
    currData = {.f0 = (char *)"aaa", .f1 = 200};
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare not same, check record\n");
    system("gmsysview record CHECK_LABEL");
    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，非结构化check_replace, 使用默认比较函数
TEST_F(StQueryCheck, testCheckReplace12)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 测试点1：比较一致，只做updateCheckVersion
    // 全表对账，使用默认比较函数
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");
    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点2：F2比较不一致，做replace
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    check_F0 = (char *)"aaa";
    check_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare not same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

typedef struct StructTestVertex {
    int32_t F0;
    int32_t F1;
    int32_t F2;
    int32_t F3;
} StructTestVertex;

bool CheckCmpFuncStruct(GmcStmtT *readStmt, void *userData)
{
    StructTestVertex *data = (StructTestVertex *)userData;
    int32_t currF0 = data->F0;
    int32_t currF1 = data->F1;
    int32_t currF2 = data->F2;

    // F0
    int32_t oldF0;
    uint32_t oldF0size;
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "F0", &oldF0size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "F0", &oldF0, oldF0size, &isNull));
    if (currF0 != oldF0) {
        printf("check not same, currF0 is %d, oldF0 is %d\n", currF0, oldF0);
        return false;
    }

    // F1
    int32_t oldF1;
    uint32_t oldF1size;
    isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "F1", &oldF1size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "F1", &oldF1, oldF1size, &isNull));
    if (currF1 != oldF1) {
        printf("check not same, currF1 is %d, oldF1 is %d\n", currF1, oldF1);
        return false;
    }

    // F2
    int32_t oldF2;
    uint32_t oldF2size;
    isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "F2", &oldF2size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "F2", &oldF2, oldF2size, &isNull));
    if (currF2 != oldF2) {
        printf("check not same, currF2 is %d, oldF2 is %d\n", currF2, oldF2);
        return false;
    }
    printf("check correct\n");
    return true;
}

static Status SeriConvertUint32ToVarint(uint32_t input, uint32_t *output, uint32_t *byteNum)
{
    if (input <= 0x0000003F) {
        *output = input;
        *byteNum = 1;  // 此时可以只使用1字节来存储数据
    } else if (input <= 0x00003FFF) {
        // 注意：移动运算与大小端无关，左移是往高位移动，右移是往低位移动
        // 小端在存储时是低到高，但是在运算时依然是高到低
        // 8: 将2字节的前后字节序逆转
        *output = ((input & 0x000000FF) << 8) | (input >> 8) | 0x00000040;
        *byteNum = 2;  // 此时可以只使用2字节来存储数据
    } else if (input <= 0x003FFFFF) {
        // 16: 将3字节的前后字节序逆转
        *output = ((input & 0x000000FF) << 16) | (input & 0x0000FF00) | (input >> 16) | 0x00000080;
        *byteNum = 3;  // 此时可以只使用3字节来存储数据
    } else if (input <= 0x3FFFFFFF) {
        // 24: 将4字节的前后字节序逆转
        *output = (input << 24) | (input & 0x00FFFF00) | (input >> 24) | 0x000000C0;
        *byteNum = 4;  // 此时需要使用全部的4字节来存储数据
    } else {
        return -1;
    }
    return GMERR_OK;
}

static uint32_t GetSerialBufLen()
{
    uint32_t tempBuf;
    uint32_t byteNum;
    uint32_t bufLen = 0;
    bufLen += 1;
    uint32_t propertyLen = sizeof(StructTestVertex) + 1;  // 最后要预留一个字段给系统预留的属性
    SeriConvertUint32ToVarint(propertyLen, &tempBuf, &byteNum);
    bufLen += byteNum;
    bufLen += propertyLen;
    uint32_t propeNum = 4;  // vertex有4个属性
    SeriConvertUint32ToVarint(propeNum, &tempBuf, &byteNum);
    bufLen += byteNum;
    bufLen += 1;
    return bufLen;
}

static Status SeriFullObject(void *seri, uint8_t *destBuf, GmcStructureResvT *reservedSize)
{
    GmcSeriT *s = (GmcSeriT *)seri;
    StructTestVertex *vertexInfo = (StructTestVertex *)s->obj;
    char *buf = (char *)destBuf;
    *buf = DM_FIXED_VERTEX;  // 第1byte有特殊含义，根据实际情况填写
    buf += 1;
    uint32_t byteNum;
    uint32_t propertyLen =
        sizeof(StructTestVertex) + reservedSize->fixedPropResvSize;  // 最后要留一个字节给系统预留的属性
    SeriConvertUint32ToVarint(propertyLen, (uint32_t *)buf, &byteNum);
    buf += byteNum;
    int err = memcpy_s(buf, propertyLen, vertexInfo, propertyLen - 1);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    buf += propertyLen;
    uint32_t propeNum = 4;  // vertex有4个属性
    SeriConvertUint32ToVarint(propeNum + reservedSize->propertyAddNum, (uint32_t *)buf, &byteNum);
    buf += byteNum;
    *buf = 0x0f;  // 表示4个属性都是非空类型，并且8bit对齐
    buf += 1;
    *(uint32_t *)buf = 0;  // edgeNum为0
    buf += sizeof(uint32_t);
    return GMERR_OK;
}

// 全表对账，简单表，结构化check_replace,使用自定义比较函数，比较结果一致，做update version
TEST_F(StQueryCheck, testCheckReplace21)
{
    const char *normal_label_name = "Test";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Test",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable": false},
                    {"name":"F1", "type":"int32", "nullable": false},
                    {"name":"F2", "type":"int32", "nullable": false},
                    {"name":"F3", "type":"int32", "nullable": false}
                ],
            "keys":
                [
                    {
                        "node":"Test",
                        "name":"Test_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 结构化插入一条数据
    StructTestVertex obj;
    GmcSeriT serStr;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();

    obj.F0 = 1;
    obj.F1 = obj.F0 + 1;
    obj.F2 = obj.F1 + 1;
    obj.F3 = obj.F2 + 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record Test");

    // 全表对账，分区为0xff 成功
    GmcCheckInfoT *checkInfo;
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFuncStruct, &obj);
    EXPECT_EQ(GMERR_OK, ret);

    StructTestVertex obj2;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj2;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();
    obj2.F0 = 1;
    obj2.F1 = obj2.F0 + 1;
    obj2.F2 = obj2.F1 + 1;
    obj2.F3 = obj2.F2 + 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，结构化check_replace,使用默认比较函数,比较结果一致，做update version
TEST_F(StQueryCheck, testCheckReplace22)
{
    const char *normal_label_name = "Test";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Test",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable": false},
                    {"name":"F1", "type":"int32", "nullable": false},
                    {"name":"F2", "type":"int32", "nullable": false},
                    {"name":"F3", "type":"int32", "nullable": false}
                ],
            "keys":
                [
                    {
                        "node":"Test",
                        "name":"Test_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 结构化插入一条数据
    StructTestVertex obj;
    GmcSeriT serStr;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();

    obj.F0 = 1;
    obj.F1 = obj.F0 + 1;
    obj.F2 = obj.F1 + 1;
    obj.F3 = obj.F2 + 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record Test");

    // 全表对账，分区为0xff 成功
    GmcCheckInfoT *checkInfo;
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    StructTestVertex obj2;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj2;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();
    obj2.F0 = 1;
    obj2.F1 = obj2.F0 + 1;
    obj2.F2 = obj2.F1 + 1;
    obj2.F3 = obj2.F2 + 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，非结构化，check_replace,1.主键未设置 2.可空的其他字段未设置 3.非空的其他字段未设置
TEST_F(StQueryCheck, testCheckReplaceNoPk1)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    int32_t value_F2 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 1 主键未设置，客户端报错
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // replace
    // 2 主键设置，可空的其他字段未设置，客户端不报错
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    value_F0 = (char *)"bbb";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 2 主键设置，非空的其他字段未设置，客户端也不报错，比较不一致，变成replace,服务端报错
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    value_F0 = (char *)"bbb";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，结构化，check_replace,1.主键未设置
// 结构化主键未设置等效与主键为随机值或者默认的0,直连读不到数据，做replace操作
TEST_F(StQueryCheck, testCheckReplaceNoPk2)
{
    const char *normal_label_name = "Test";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Test",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable": false},
                    {"name":"F1", "type":"int32", "nullable": false},
                    {"name":"F2", "type":"int32", "nullable": false},
                    {"name":"F3", "type":"int32", "nullable": false}
                ],
            "keys":
                [
                    {
                        "node":"Test",
                        "name":"Test_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 结构化插入一条数据
    StructTestVertex obj;
    GmcSeriT serStr;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();

    obj.F0 = 1;
    obj.F1 = obj.F0 + 1;
    obj.F2 = obj.F1 + 1;
    obj.F3 = obj.F2 + 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record Test");

    // 全表对账，分区为0xff 成功
    GmcCheckInfoT *checkInfo;
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    StructTestVertex obj2;
    serStr.seriFunc = SeriFullObject;
    serStr.version = GMC_SERI_VERSION_DEFAULT;
    serStr.obj = (uint8_t *)&obj2;
    serStr.userData = NULL;
    serStr.bufSize = GetSerialBufLen();
    // F0未设置
    obj2.F1 = 1;
    obj2.F2 = 1;
    obj2.F3 = 1;
    ret = GmcSetVertexWithBuf(stmt, &serStr);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，简单表，非结构化check_replace, 使用自定义比较函数
TEST_F(StQueryCheck, testCheckReplaceNewSub)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({
        "max_record_count":1000,
        "status_merge_sub":true
        })";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 全表对账，分区为0xff 成功

    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 比较一致，只做updateCheckVersion
    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 100;
    dataT currData = {.f0 = (char *)"aaa", .f1 = 100};
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");
    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 正常replace:主键未设置，非空的其他字段未设置
TEST_F(StQueryCheck, testReplaceNotSet)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    int32_t value_F2 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    value_F2 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 1 主键设置，非空的其他字段未设置
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    value_F0 = (char *)"bbb";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // 2 主键未设置，设置了其他字段
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    system("gmsysview record CHECK_LABEL");

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 聚簇容器(不能有变长字段)，全表对账，简单表，非结构化, 默认比较函数,比较一致，做check update version
TEST_F(StQueryCheck, testCheckReplacejucu)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    int32_t value_F0 = 100;
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 全表对账，分区为0xff 成功
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 测试点1：比较一致，只做updateCheckVersion
    value_F0 = 100;
    value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 测试点2：比较不一致，做replace
    value_F0 = 100;
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare not same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 非聚簇容器(不能有变长字段)，全表对账，简单表，非结构化，默认比较函数,比较一致，做check update version
TEST_F(StQueryCheck, testCheckReplacefeijucu)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"support_undetermined_length":true})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    int32_t value_F0 = 100;
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = 100;
    value_F1 = 200;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = 100;
    value_F1 = 300;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_REPLACE));
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 全表对账，分区为0xff 成功
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 测试点1：比较一致，只做updateCheckVersion
    value_F0 = 100;
    value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 测试点2：比较不一致，做replace
    value_F0 = 100;
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare not same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 分区对账，默认比较函数，比较一致，做check update version
TEST_F(StQueryCheck, testCheckReplacefenqu)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_partition_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建分区表
    ret = GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取元数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));

    // 分区表插入数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    uint8_t value_F2 = 1;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    value_F2 = 2;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"ccc";
    value_F1 = 300;
    value_F2 = 15;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"ddd";
    value_F1 = 400;
    value_F2 = 15;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;
    // 未启动对账时，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt1, partition_label_name, 15, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 测试点：分区对账，分区为15 check_replace数据
    ret = GmcBeginCheck(stmt1, partition_label_name, 15);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_CHECK_REPLACE));
    value_F0 = (char *)"ddd";
    value_F1 = 400;
    value_F2 = 15;
    ret = GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, &value_F2, sizeof(value_F2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt1, partition_label_name, 15, true);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt1, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

#define WAIT_WHILE(condition) \
    do {                      \
        usleep(5000);         \
    } while (condition);
// 带上推送

void SubsCallback(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    EXPECT_EQ(GMC_SUB_EVENT_REPLACE, info->eventType);
    EXPECT_EQ(1u, info->labelCount);
    uint32_t &received = *reinterpret_cast<uint32_t *>(userData);

    for (bool eof, &isNull = eof;; ++received) {
        auto ret = GmcFetch(stmt, &eof);
        EXPECT_EQ(GMERR_OK, ret);
        if (eof) {
            break;
        }
        ret = GmcSubSetFetchMode(stmt, GMC_SUB_FETCH_NEW);
        EXPECT_EQ(GMERR_OK, ret);

        uint32_t value;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(value), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
    }
}

// 拿到数据不一致，执行replace,触发推送
TEST_F(StQueryCheck, testCheckReplacePubSub)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建订阅连接和订阅关系
    const char *subsConnName = "subsConnName1";
    GmcConnT *subsChan = NULL;
    Status ret = ConnectWrapper(GMC_CONN_TYPE_SUB, serverLocator, userName, subsConnName, &subsChan);
    EXPECT_EQ(GMERR_OK, ret);
    GmcSubConfigT config;
    config.subsName = "subVertexLabel1";
    config.configJson = R"({
            "label_name":"CHECK_LABEL",
            "comment":"CHECK_LABEL subscription",
            "events":
                [
                    {"type":"replace", "msgTypes":["new object", "old object"]}
                ],
            "retry":true
        })";

    // 创建非分区表
    ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建订阅关系
    uint32_t recived = 0;
    ret = GmcSubscribe(stmt, &config, subsChan, SubsCallback, &recived);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;

    // 测试点1：全表对账，分区为0xff 成功，设置自定义比较函数，f1比较不一致，触发replace推送。
    dataT currData = {.f0 = (char *)"aaa", .f1 = 200};
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_OK, ret);

    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 订阅回调触发，recived = 1
    WAIT_WHILE(recived != 1);

    // 测试点2：全表对账，分区为0xff 成功，设置自定义比较函数，比较一致，只做updateCheckVersion，不触发推送。
    currData = {.f0 = (char *)"aaa", .f1 = 200};
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckCmpFunc, &currData);
    EXPECT_EQ(GMERR_OK, ret);

    check_F0 = (char *)"aaa";
    check_F1 = 200;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 等待1s,recived始终为1，不增加，说明无推送触发
    int count = 1000;
    while (count > 0) {
        count--;
        usleep(1000);
        EXPECT_EQ((uint32_t)1, recived);
    }

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    ret = GmcUnSubscribe(stmt, config.subsName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDisconnect(subsChan);
    EXPECT_EQ(GMERR_OK, ret);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 对账中交互式事务

typedef struct CheckAgeThreadCfgT {
    GmcStmtT *stmt;
    // LabelOpCfgT *cfg;
    const char *vertexLabelName;
} CheckAgeThreadCfgT;

void *ThreadForClientA(void *config)
{
    CheckAgeThreadCfgT *threadCfg = (CheckAgeThreadCfgT *)config;
    GmcStmtT *stmt = threadCfg->stmt;
    const char *vertexLabelName = threadCfg->vertexLabelName;
    GmcCheckInfoT *checkInfo;
    // 全表对账，使用默认比较函数
    Status ret = GmcBeginCheck(stmt, vertexLabelName, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_CHECK_REPLACE));
    // 测试点1：比较一致，只做updateCheckVersion
    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    // 测试点：客户端a和b会有冲突
    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        printf("check repalce ret is %d\n", ret);
        EXPECT_EQ(GMERR_REPLACE_CONFLICT_IN_CHECKING, ret);
    }
    printf("client a sysview record\n");
    system("gmsysview record CHECK_LABEL");

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    return NULL;
}

void *ThreadForClientB(void *config)
{
    CheckAgeThreadCfgT *threadCfg = (CheckAgeThreadCfgT *)config;
    GmcStmtT *stmt = threadCfg->stmt;
    const char *vertexLabelName = threadCfg->vertexLabelName;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_DELETE));
    Status ret = GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, "aaa", strlen("aaa"));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    printf("client b sysview record\n");
    system("gmsysview record CHECK_LABEL");
    return NULL;
}

void *ThreadForClientC(void *config)
{
    CheckAgeThreadCfgT *threadCfg = (CheckAgeThreadCfgT *)config;
    GmcStmtT *stmt = threadCfg->stmt;
    const char *vertexLabelName = threadCfg->vertexLabelName;

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));

    char *check_F0 = (char *)"aaa";
    int32_t check_F1 = 200;
    Status ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, check_F0, strlen(check_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &check_F1, sizeof(check_F1));
    EXPECT_EQ(GMERR_OK, ret);
    // 测试点：客户端a和b会有冲突
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    printf("client c sysview record\n");
    system("gmsysview record CHECK_LABEL");

    return NULL;
}

// 对账冲突，客户端a对账check_repalce, 客户端b删除数据，冲突返回错误码，可能预期不到该错误码
TEST_F(StQueryCheck, testCheckReplaceconflict1)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入1条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t thread_t[2];
    CheckAgeThreadCfgT threadCfg1 = {0};
    threadCfg1.stmt = stmt;
    threadCfg1.vertexLabelName = normal_label_name;

    CheckAgeThreadCfgT threadCfg2 = {0};
    threadCfg2.stmt = stmt1;
    threadCfg2.vertexLabelName = normal_label_name;

    ret = pthread_create(&thread_t[0], NULL, ThreadForClientA, (void *)&threadCfg1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thread_t[1], NULL, ThreadForClientB, (void *)&threadCfg2);
    ASSERT_EQ(GMERR_OK, ret);
    pthread_join(thread_t[0], NULL);
    pthread_join(thread_t[1], NULL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
}

// 对账冲突，客户端a对账check_repalce, 客户端C更新数据，冲突返回错误码，可能预期不到该错误码
TEST_F(StQueryCheck, testCheckReplaceconflict2)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1;
    CreateSyncConnectionAndStmt(&conn1, &stmt1);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入1条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t thread_t[2];
    CheckAgeThreadCfgT threadCfg1 = {0};
    threadCfg1.stmt = stmt;
    threadCfg1.vertexLabelName = normal_label_name;

    CheckAgeThreadCfgT threadCfg2 = {0};
    threadCfg2.stmt = stmt1;
    threadCfg2.vertexLabelName = normal_label_name;

    ret = pthread_create(&thread_t[0], NULL, ThreadForClientA, (void *)&threadCfg1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = pthread_create(&thread_t[1], NULL, ThreadForClientC, (void *)&threadCfg2);
    ASSERT_EQ(GMERR_OK, ret);

    pthread_join(thread_t[0], NULL);
    pthread_join(thread_t[1], NULL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
    DestroyConnectionAndStmt(conn1, stmt1);
}

typedef struct StructTestTreeVertexc1 {
    uint32_t f1;
    uint16_t f2Len;
    char *f2;
} StructTestTreeVertexc1;

typedef struct StructTestTreeVertexc2 {
    uint32_t b0;
    uint16_t b1Len;
    char *b1;
} StructTestTreeVertexc2;
typedef struct StructTestTreeVertex {
    uint32_t c0;
    StructTestTreeVertexc1 c1;
    StructTestTreeVertexc2 c2;
} StructTestTreeVertex;

bool CheckTreeCmpFuncStruct(GmcStmtT *readStmt, void *userData)
{
    StructTestTreeVertex *data = (StructTestTreeVertex *)userData;
    uint32_t currc0 = data->c0;
    char *currf2 = data->c1.f2;
    uint16_t currf2Len = data->c1.f2Len;

    // c0
    uint32_t oldc0;
    uint32_t oldc0size;
    bool isNull = false;
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertySizeByName(readStmt, "c0", &oldc0size));
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(readStmt, "c0", &oldc0, oldc0size, &isNull));
    if (currc0 != oldc0) {
        printf("check not same, currF0 is %d, oldF0 is %d\n", currc0, oldc0);
        return false;
    }

    // F2
    char oldF2[300] = {0};
    uint32_t oldF2size;
    isNull = false;
    GmcNodeT *node = NULL;
    Status ret = GmcGetChildNode(readStmt, "c1", &node);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcNodeGetPropertySizeByName(node, "f2", &oldF2size));
    EXPECT_EQ(GMERR_OK, GmcNodeGetPropertyByName(node, "f2", oldF2, oldF2size, &isNull));
    if (oldF2size != currf2Len || memcmp(currf2, oldF2, currf2Len) != 0) {
        printf("f2 check not same,\n currf2 is %s, len is %d \n oldF2 is %s, len is %d\n", currf2, currf2Len, oldF2,
            oldF2size);
        return false;
    }
    printf("check same\n");
    return true;
}

const char *test_tree_schema =
    R"([{
    "version": "2.0",
    "type": "record",
    "name": "treeVertex",
    "fields": [
        { "name": "c0", "type": "uint32" },
        {
            "name": "c1",
            "type": "record",
            "fields": [
                { "name": "f1", "type": "uint32"},
                { "name": "f2", "type": "string", "size": 300, "nullable": true }
            ]
        },
        {
            "name": "c2",
            "type": "record",
            "vector": true,
            "size": 1024,
            "fields": [
                { "name": "b1", "type": "uint32", "nullable": true },
                { "name": "b2", "type": "string", "size": 10, "nullable": true }
            ]
        },
        { "name": "c3", "type": "uint32" ,"auto_increment": true}
    ],
    "keys": [
        {
            "name": "table_pk",
            "index": { "type": "primary" },
            "node": "treeVertex",
            "fields": ["c0"],
            "constraints": { "unique": true }
        }
    ]
}])";

void scanData(GmcStmtT *stmt, const char *normal_label_name, uint32_t expect_c3)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, true);
    uint32_t tmp = 100;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &tmp, sizeof(uint32_t));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "table_pk");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    bool eof;
    ret = GmcFetch(stmt, &eof);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(eof);

    uint32_t c3;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "c3", &c3, sizeof(c3), &isNull);
    EXPECT_EQ(ret, GMERR_OK);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(expect_c3, c3);
}

// 全表对账，tree表，非结构化，check_replace，用户指定比较函数
TEST_F(StQueryCheck, testCheckReplaceTreeWithUserDefCmp)
{
    const char *normal_label_name = "treeVertex";
    const char *test_config_json = R"({"max_record_count":1000, "auto_increment":1})";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_tree_schema, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 插入数据
    uint32_t value_c0 = 100;
    uint32_t value_F1 = 200;
    char *value_F2 = (char *)"aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaabbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbc"
                             "ccccccccccccccccccccccccccccccccccccdddddddddddddddddddddddddddddddddddddddddeeeeeeeeeeee"
                             "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeefffffffffffffffffffffffffffgggggggggggggggggggggggggg";
    GmcNodeT *node = NULL;

    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, value_F2, strlen(value_F2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t value = 0; value <= 2; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    scanData(stmt, normal_label_name, 1);

    // 开启对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    StructTestTreeVertex obj = {0};
    obj.c0 = value_c0;
    obj.c1.f2 = value_F2;
    obj.c1.f2Len = strlen(value_F2) + 1;

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetCheckReplaceCmpFunc(stmt, CheckTreeCmpFuncStruct, &obj);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入部分数据
    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, value_F2, strlen(value_F2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    scanData(stmt, normal_label_name, 1);
    system("gmsysview record treeVertex");

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 全表对账，tree表，非结构化，check_replace，默认比较函数
TEST_F(StQueryCheck, testCheckReplaceTreeWithDefaultCmp)
{
    const char *normal_label_name = "treeVertex";
    const char *test_config_json = R"({"max_record_count":1000, "auto_increment":1})";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret = GmcCreateVertexLabel(stmt, test_tree_schema, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    uint32_t value_c0 = 100;
    uint32_t value_F1 = 200;
    char *value_F2 = (char *)"aaaaaaaaaaaaaaaaaaaaaaaaaaaaabbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbccccccccccccccccccccccccc"
                             "ccccccccccccdddddddddddddddddddddddddddddddddddddddddeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"
                             "eeeeeeeeeeefffffffffffffffffffffffffffgggggggggggggggggggggggggg";
    GmcNodeT *node = NULL;

    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(value_c0));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, value_F2, strlen(value_F2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t value = 0; value <= 2; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    scanData(stmt, normal_label_name, 1);

    // 第一次对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 插入完全相同的数据, 自增列数值不同
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(value_c0));
    EXPECT_EQ(GMERR_OK, ret);
    // 设值自增列
    uint32_t value_c3 = 111;
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_UINT32, &value_c3, sizeof(value_c3));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, value_F2, strlen(value_F2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t value = 0; value <= 2; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 判断c3数据
    scanData(stmt, normal_label_name, 1);

    // 第二次对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 插入element个数不同, 自增列数值不同
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(value_c0));
    EXPECT_EQ(GMERR_OK, ret);
    // 设值自增列
    value_c3 = 222;
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_UINT32, &value_c3, sizeof(value_c3));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, value_F2, strlen(value_F2));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t value = 0; value <= 1; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record treeVertex");
    scanData(stmt, normal_label_name, 2);

    // 第三次对账
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);

    // replace
    // 插入string不同, 自增列数值不同
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));
    ret = GmcSetVertexProperty(stmt, "c0", GMC_DATATYPE_UINT32, &value_c0, sizeof(value_c0));
    EXPECT_EQ(GMERR_OK, ret);
    // 设值自增列
    value_c3 = 333;
    ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_UINT32, &value_c3, sizeof(value_c3));
    EXPECT_EQ(GMERR_OK, ret);
    // tree node
    ret = GmcGetChildNode(stmt, "c1", &node);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f1", GMC_DATATYPE_UINT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "f2", GMC_DATATYPE_STRING, "CHECK_REPLACE", strlen("CHECK_REPLACE"));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetChildNode(stmt, "c2", &node);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t value = 0; value <= 2; ++value) {
        ret = GmcNodeAppendElement(node, &node);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b1", GMC_DATATYPE_UINT32, &value, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetPropertyByName(node, "b2", GMC_DATATYPE_STRING, "huawei", strlen("huawei"));
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    system("gmsysview record treeVertex");
    scanData(stmt, normal_label_name, 3);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

void vertexBatchWrite(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, GmcConnT *conn, const char *labelName)
{
    int ret = 0;
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_CHECK_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        int32_t F0 = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1));
        EXPECT_EQ(GMERR_OK, ret);
        // check replace不允许batch操作，预期失败
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    }
}

// 全表对账，不支持batch
TEST_F(StQueryCheck, testCheckReplaceBatch)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建非分区表
    Status ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    int32_t value_F0 = 100;
    int32_t value_F1 = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value_F0, sizeof(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value_F1, sizeof(value_F1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    // 全表对账，分区为0xff 成功
    ret = GmcBeginCheck(stmt, normal_label_name, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_CHECK_REPLACE));

    // 测试点1：使用batch,报错
    vertexBatchWrite(0, 10, stmt, conn, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    printf("compare same, check record\n");
    system("gmsysview record CHECK_LABEL");

    // 结束对账
    ret = GmcEndCheck(stmt, normal_label_name, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);

    // 正常结束对账，查询checkInfo，显示normal状态
    ret = GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 删表，断连
    ret = GmcDropVertexLabel(stmt, normal_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const uint8_t g_invalidPartitionId = 0xff;
void FetchData(
    uint32_t start, uint32_t end, int32_t cnt, GmcStmtT *stmt, bool isFetched, uint8_t F2 = g_invalidPartitionId)
{
    Status ret = GMERR_OK;
    for (uint32_t idx = start; idx < end; idx++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_SCAN));
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_PARTITION_LABEL_INDEX"));
        int32_t f0 = idx;
        EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f0, sizeof(f0)));
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t f1 = 0;
        uint8_t f2 = 0;
        int32_t expectF1 = idx * cnt;
        uint8_t expectF2 = F2 == g_invalidPartitionId ? idx % 2 : F2;
        bool isNull = true;
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(int32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFetched) {
                EXPECT_EQ(false, isNull);
                EXPECT_EQ(expectF1, f1);
            } else {
                EXPECT_EQ(true, isNull);
            }
            ret = GmcGetVertexPropertyByName(stmt, "F2", &f2, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFetched) {
                EXPECT_EQ(false, isNull);
                EXPECT_EQ(expectF2, f2);
            } else {
                EXPECT_EQ(true, isNull);
            }
        }
    }
}

TEST_F(StQueryCheck, testImmediateRestartCheck1)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t f3;
    for (int32_t value = 0; value < 4; value++) {
        f3 = (uint8_t)(value % 2);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 循环开启和停止对账，不老化
    for (uint32_t idx = 0; idx < 100; idx++) {
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            while (GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                usleep(1000);
            }
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, true);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // 数据应该都能读到
    FetchData(0, 4, 1, stmt, true);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
    // 循环开启和结束对账，插入新数据，老化旧数据
    for (uint32_t idx = 0; idx < 100; idx++) {
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            while (GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                usleep(1000);
            }
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int32_t value = 4; value < 8; value++) {
            f3 = (uint8_t)(value % 2);
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // 旧数据不能读到
    FetchData(0, 4, 1, stmt, false);
    // 新数据能读到
    FetchData(4, 8, 1, stmt, true);

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
    // 循环开启和停止对账，更新旧数据，不老化旧数据
    int32_t f1_new = 0;
    for (uint32_t idx = 0; idx < 100; idx++) {
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            while (GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                usleep(1000);
            }
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int32_t value = 0; value < 4; value++) {
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            f1_new = value * 10;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1_new, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            f3 = (uint8_t)(value % 2);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, true);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    // 旧数据能读到
    FetchData(0, 4, 10, stmt, true);
    // 新数据能读到
    FetchData(4, 8, 1, stmt, true);

    int32_t loop = 0;
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
    while (loop < 100) {
        // 循环开启和结束对账，更新旧数据，老化旧数据
        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            while (GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                usleep(1000);
            }
            EXPECT_EQ(GMERR_OK, ret);
        }

        for (int32_t value = 0; value < 4; value++) {
            f3 = (uint8_t)(value % 2);
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }

        for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
            ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
            EXPECT_EQ(GMERR_OK, ret);
        }
        loop++;
    }

    // 新数据能读到
    FetchData(0, 4, 1, stmt, true);
    // 旧数据不能读到
    FetchData(4, 8, 10, stmt, false);

    usleep(100000);
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 4) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 4);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 4);

    ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 4) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 4);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 4);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testImmediateRestartCheck2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t f3;
    for (int32_t value = 0; value < 4; value++) {
        f3 = (uint8_t)(value % 2);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 数据都能读到
    FetchData(0, 4, 1, stmt, true);
    uint32_t cnt = 0;
    while (cnt < 10) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
        // 循环开启和停止对账，插入新数据，回滚旧数据
        for (uint32_t idx = 0; idx < 1; idx++) {
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                while (
                    GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                    usleep(1000);
                }
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t value = 4; value < 8; value++) {
                f3 = (uint8_t)(value % 2);
                ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, true);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        // 新旧数据都能读到
        FetchData(0, 8, 1, stmt, true);

        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
        // 循环开启和结束对账，更新旧数据，老化旧数据
        int32_t f1_new = 0;
        for (uint32_t idx = 0; idx < 1; idx++) {
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                while (
                    GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                    usleep(1000);
                }
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t value = 4; value < 8; value++) {
                ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                f1_new = value * 10;
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1_new, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                f3 = (uint8_t)(value % 2);
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        // 旧数据不能读到
        FetchData(0, 4, 1, stmt, false);
        // 新数据能读到
        FetchData(4, 8, 10, stmt, true);
        cnt++;
    }

    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 2) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 2);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 2);

    ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 2) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 2);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 2);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testImmediateRestartCheck3)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // 插入数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t f3;
    for (int32_t value = 0; value < 4; value++) {
        f3 = (uint8_t)(value % 2);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 数据都能读到
    FetchData(0, 4, 1, stmt, true);
    uint32_t cnt = 0;
    while (cnt < 300) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
        // 循环开启和停止对账，插入新数据，回滚旧数据
        for (uint32_t idx = 0; idx < 1; idx++) {
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                while (
                    GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                    usleep(1000);
                }
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t value = 4; value < 8; value++) {
                f3 = (uint8_t)(value % 2);
                ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, true);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        // 新旧数据能读到
        FetchData(0, 8, 1, stmt, true);

        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_REPLACE));
        // 循环开启和结束对账，更新旧数据，老化旧数据
        int32_t f1_new = 0;
        for (uint32_t idx = 0; idx < 1; idx++) {
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                while (
                    GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE == GmcBeginCheck(stmt, partition_label_name, partitionIdx)) {
                    usleep(1000);
                }
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t value = 4; value < 8; value++) {
                ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                f1_new = value * 10;
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1_new, sizeof(int32_t));
                EXPECT_EQ(GMERR_OK, ret);
                f3 = (uint8_t)(value % 2);
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &f3, sizeof(uint8_t));
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                EXPECT_EQ(GMERR_OK, ret);
            }
            for (int32_t partitionIdx = 0; partitionIdx < 2; partitionIdx++) {
                ret = GmcEndCheck(stmt, partition_label_name, partitionIdx, false);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }

        // 旧数据不能读到
        FetchData(0, 4, 1, stmt, false);
        // 新数据能读到
        FetchData(4, 8, 10, stmt, true);
        cnt++;
    }

    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 2) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 0, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 2);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 2);

    ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    while (((GmcCheckInfoT *)checkInfo)->realAgedCnt < 2) {
        ret = GmcGetCheckInfo(stmt, partition_label_name, 1, &checkInfo);
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->realAgedCnt, 2);
    EXPECT_EQ((int64_t)((GmcCheckInfoT *)checkInfo)->shouldAgedCnt, 2);

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testTruncateAbnormal)
{
    const char *labelName = "CHECK_LABEL";
    const char *labelConfigJson = R"({"max_record_count":100000})";
    const char *labelJson =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 建表
    ASSERT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, labelJson, labelConfigJson));

    // 版本号0
    int32_t f0 = 1;
    int32_t f1 = 1;
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0)));
    ASSERT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &f1, sizeof(f1)));
    ASSERT_EQ(GMERR_OK, GmcExecute(stmt));

    // 全表对账，版本号1
    ASSERT_EQ(GMERR_OK, GmcBeginCheck(stmt, labelName, GMC_FULL_TABLE));
    ASSERT_EQ(GMERR_OK, GmcEndCheck(stmt, labelName, GMC_FULL_TABLE, true));

    ASSERT_EQ(GMERR_OK, GmcDeleteAllFast(stmt, labelName));

    sleep(1);

    // 结束对账，查询checkInfo
    GmcCheckInfoT *checkInfo;
    ASSERT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo));
    ASSERT_EQ(checkInfo->checkStatus, GMC_CHECK_STATUS_NORMAL);
    ASSERT_EQ(checkInfo->realRecoveryCnt, 1);
    ASSERT_EQ(checkInfo->shouldRecoveryCnt, 1);

    // 删表，断连
    ASSERT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, AgedAfterTruncateBg)
{
    int32_t recordNum = 10000;

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    status_t ret = GmcCreateVertexLabel(stmt, test_partition_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // 版本0，插入数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, partition_label_name, GMC_OPERATION_INSERT));
    uint8_t partitionId = 0;
    for (int32_t value = 0; value < recordNum; value++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 数据都能读到
    FetchData(0, recordNum, 1, stmt, true, 0);

    // 版本1，此时仅可见版本1
    EXPECT_EQ(GMERR_OK, GmcDeleteAllFast(stmt, partition_label_name));
    // 版本2，此时仅可见版本1,2
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, partition_label_name, partitionId));

    // 不可见版本0,读不到数据
    FetchData(0, recordNum, 1, stmt, false, 0);

    EXPECT_EQ(GMERR_OK, GmcEndCheck(stmt, partition_label_name, partitionId, false));

    ret = GmcDropVertexLabel(stmt, partition_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

void LoopInsertPartitionLabel(GmcStmtT *stmt, uint8_t partitionId, int32_t start, int32_t end)
{
    for (int32_t i = start; i < end; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &partitionId, sizeof(uint8_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }
}

// 生成多个老化任务，并进行降级，查看老化降级任务同时执行功能
TEST_F(StQueryCheck, testPartitionCheckAndDegrade)
{
    const char *labelName = "CHECK_PARTITION_LABEL";
    const char *testConfigJson = R"({"max_record_count":10000})";

    const char *testLabelJsonV1 =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "schema_version": 1,
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *testLabelJsonV2 =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "schema_version": 2,
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"partition", "nullable":false},
                    {"name":"F2", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 分区表插入数据
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testLabelJsonV1, testConfigJson));
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, testLabelJsonV2, true, labelName));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 2u, GMC_OPERATION_INSERT));
    for (int32_t i = 0; i < 6; i++) {
        int32_t start = 100 * i;
        int32_t end = start + 100;
        LoopInsertPartitionLabel(stmt, i, start, end);
    }

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;
    // 未启动对账时，查询checkInfo，显示normal状态
    for (uint8_t i = 1; i < 6; i++) {
        EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, labelName, i, &checkInfo));
        EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
        EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    }

    Status ret;
    pthread_t tids[6];
    uint8_t partitionIds[6] = {0, 1, 2, 3, 4, 5};
    for (uint32_t i = 0; i < 6; i++) {
        // 分区表，多线程同时启动不同分区的对账, 都成功
        ret = pthread_create(&tids[i], NULL, AppBeginCheckPartition, &partitionIds[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDegradeVertexLabel(stmt, labelName, 1u);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < 6; i++) {
        pthread_join(tids[i], NULL);
    }
    system("gmsysview -q V$\\QRY_AGE_TASK");

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

// 耗时老化任务，并进行降级，分片超时老化降级任务同时执行功能
TEST_F(StQueryCheck, testHighLoadPartitionCheckAndDegrade)
{
    const char *labelName = "CHECK_PARTITION_LABEL";
    const char *testConfigJson = R"({"max_record_count":100000})";

    const char *testLabelJsonV1 =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "schema_version": 1,
            "fields":
                [
                    {"name":"F0", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *testLabelJsonV2 =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "schema_version": 2,
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, testLabelJsonV1, testConfigJson));
    EXPECT_EQ(GMERR_OK, GmcAlterVertexLabelWithName(stmt, testLabelJsonV2, true, labelName));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelNameWithVersion(stmt, labelName, 2u, GMC_OPERATION_INSERT));

    for (int32_t i = 0; i < 80000; i++) {
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t)));
        EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    }

    pthread_t tid;
    // 分区表，多线程同时启动不同分区的对账, 都成功
    uint8_t partitionId = DB_INVALID_ID8;
    Status ret = pthread_create(&tid, NULL, AppBeginCheckPartition, &partitionId);

    DbSleep(500);
    ret = GmcDegradeVertexLabel(stmt, labelName, 1u);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid, NULL);

    sleep(5);
    system("gmsysview -q V$\\QRY_AGE_TASK");

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, labelName));
    DestroyConnectionAndStmt(conn, stmt);
}

#ifdef WARM_REBOOT

void *app3_end_all_check_abnormal(void *arg)  // app1 not end check
{
    const char *normal_label_name = "CHECK_LABEL";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcEndAllPartitionCheck(stmt, normal_label_name, true);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void InsertNormalLabel(GmcStmtT *stmt, char *value_F0, int32_t *value_F1)
{
    // 非分区表插入数据
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, value_F1, sizeof(int32_t)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
}

void InsertPartitionLabel(GmcStmtT *stmt1, char *value_F0, int32_t *value_F1, uint8_t *value_F2)
{
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F1", GMC_DATATYPE_INT32, value_F1, sizeof(int32_t)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt1, "F2", GMC_DATATYPE_PARTITION, value_F2, sizeof(uint8_t)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt1));
}

TEST_F(StQueryCheck, testCheckEndAllPartition)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *test_partition_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建非分区表和分区表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json));
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json));

    // 获取元数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入两条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    InsertNormalLabel(stmt, value_F0, &value_F1);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    InsertNormalLabel(stmt, value_F0, &value_F1);

    // 分区表插入数据
    value_F0 = (char *)"aaa";
    value_F1 = 100;
    uint8_t value_F2 = 1;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    value_F2 = 2;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    value_F0 = (char *)"ccc";
    value_F1 = 300;
    value_F2 = 3;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    value_F0 = (char *)"ddd";
    value_F1 = 400;
    value_F2 = 3;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    GmcCheckInfoT *checkInfo;

    // 非分区表 全表对账，分区为0xff 成功
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, normal_label_name, 0xff));

    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, normal_label_name, false));

    // 正常结束对账，查询checkInfo，显示normal状态
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo));
    GmcCheckStatusE checkStatus;
    EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);

    // 非分区表，分区不为0xFF,查询checkInfo报错
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, GmcGetCheckInfo(stmt, normal_label_name, 100, &checkInfo));

    // 未启动对账时，查询checkInfo，显示normal状态
    for (uint8_t i = 1; i < 4; i++) {
        EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt1, partition_label_name, i, &checkInfo));
        EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
        EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    }

    // 分区对账，分区为3 成功
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt1, partition_label_name, 3));

    // 启动对账后，查询checkInfo，显示checking状态
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt1, partition_label_name, 3, &checkInfo));
    EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_CHECKING);

    // 对账期间不允许删表
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, GmcDropVertexLabel(stmt1, partition_label_name));

    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, true));

    // 分区表，分区不在0-15范围内，查询checkInfo报错
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, GmcGetCheckInfo(stmt1, partition_label_name, 100, &checkInfo));
    const char *lastErrorStr1 = GmcGetLastError();
    lastErrorStr1 = GmcGetLastError();
    const char *result = (char *)"If partition label, partition ID 100 exceeds the upper limit 16, "
                                 "else partition ID must be 255.";
    EXPECT_STREQ(result, lastErrorStr1);

    pthread_t tid[9];

    // 启动对账后异常终止对账，再次启动报错
    ret = pthread_create(&tid[0], NULL, app1_begin_check_not_end, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[0], NULL);

    ret = pthread_create(&tid[1], NULL, app2_begin_check_error, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[1], NULL);

    ret = pthread_create(&tid[2], NULL, app3_end_all_check_abnormal, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[2], NULL);

    // 多线程并发启动对账，只有一个线程返回成功
    uint8_t oldCheckVersion, newCheckVersion;
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, partition_label_name, 14, &checkInfo));

    oldCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;

    ret = pthread_create(&tid[3], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[4], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[5], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[3], NULL);
    pthread_join(tid[4], NULL);
    pthread_join(tid[5], NULL);

    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, partition_label_name, 14, &checkInfo));
    newCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;
    EXPECT_EQ(newCheckVersion, oldCheckVersion + 1);

    // 分区表，多线程同时启动不同分区的对账, 都成功
    ret = pthread_create(&tid[6], NULL, app7_begin_check_partition_1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[7], NULL, app8_begin_check_partition_2, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[8], NULL, app9_begin_check_partition_3, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[6], NULL);
    pthread_join(tid[7], NULL);
    pthread_join(tid[8], NULL);

    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, partition_label_name, true));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, normal_label_name));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, partition_label_name));

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testCheckEndAllPartitionWithView)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *test_partition_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建非分区表和分区表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json));
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json));

    // 获取元数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_INSERT));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_INSERT));

    // 非分区表插入一条数据
    char *value_F0 = (char *)"aaa";
    int32_t value_F1 = 100;
    InsertNormalLabel(stmt, value_F0, &value_F1);

    // 分区表插入数据
    value_F0 = (char *)"aaa";
    value_F1 = 100;
    uint8_t value_F2 = 1;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    value_F0 = (char *)"bbb";
    value_F1 = 200;
    value_F2 = 2;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    value_F0 = (char *)"ccc";
    value_F1 = 300;
    value_F2 = 3;
    InsertPartitionLabel(stmt1, value_F0, &value_F1, &value_F2);

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;

    // 开始前正常或异常结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, normal_label_name, true));
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, normal_label_name, false));
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, true));
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, false));
    // 状态应该都为还没开启对账
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo));
    EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    for (uint8_t i = 1; i < 4; i++) {
        EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt1, partition_label_name, i, &checkInfo));
        EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
        EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    }

    // 非分区表 全表对账，分区为0xff 成功
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt, normal_label_name, 0xff));
    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, normal_label_name, false));
    // 正常结束对账，查询checkInfo，显示normal状态
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, normal_label_name, 0xff, &checkInfo));
    EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    // 查找记录"aaa"，应该找不到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, normal_label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "CHECK_LABEL_INDEX"));
    value_F0 = (char *)"aaa";
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, value_F0, 3));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));
    bool eof;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt, &eof));
    EXPECT_EQ(eof, true);
    bool isNull;
    EXPECT_EQ(GMERR_NO_DATA, GmcGetVertexPropertyByName(stmt, "F1", &value_F1, sizeof(int32_t), &isNull));

    // 分区对账，分区为3 成功
    EXPECT_EQ(GMERR_OK, GmcBeginCheck(stmt1, partition_label_name, 3));
    // 启动对账后，查询checkInfo，显示checking状态
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt1, partition_label_name, 3, &checkInfo));
    EXPECT_EQ(GMERR_OK, GmcGetCheckStatus(checkInfo, &checkStatus));
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_CHECKING);
    // 对账期间不允许删表
    EXPECT_EQ(GMERR_TABLE_IN_CHECKING, GmcDropVertexLabel(stmt1, partition_label_name));
    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, true));
    // 查找记录"ccc"，应该能找到
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt1, partition_label_name, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt1, "CHECK_PARTITION_LABEL_INDEX"));
    value_F0 = (char *)"ccc";
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt1, 0, GMC_DATATYPE_STRING, value_F0, 3));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt1));
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt1, &eof));
    EXPECT_EQ(eof, false);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F1", &value_F1, sizeof(int32_t), &isNull));
    EXPECT_EQ(value_F1, 300);
    EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt1, "F2", &value_F2, sizeof(uint8_t), &isNull));
    EXPECT_EQ(value_F2, 3);

    pthread_t tid[9];

    // 启动对账后异常终止对账，再次启动报错
    ret = pthread_create(&tid[0], NULL, app1_begin_check_not_end, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[0], NULL);

    ret = pthread_create(&tid[1], NULL, app2_begin_check_error, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[1], NULL);

    ret = pthread_create(&tid[2], NULL, app3_end_check_abnormal, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[2], NULL);

    ret = pthread_create(&tid[3], NULL, app3_end_all_check_abnormal, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    pthread_join(tid[3], NULL);

    // 旧对账版本信息
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt, partition_label_name, 14, &checkInfo));
    uint8_t oldCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;
    // 多线程并发启动对账，只有一个线程返回成功
    ret = pthread_create(&tid[4], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[5], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[6], NULL, three_app_begin_check_atTheSameTime, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[4], NULL);
    pthread_join(tid[5], NULL);
    pthread_join(tid[6], NULL);
    // 新对账版本信息
    EXPECT_EQ(GMERR_OK, GmcGetCheckInfo(stmt1, partition_label_name, 14, &checkInfo));
    uint8_t newCheckVersion = ((GmcCheckInfoT *)checkInfo)->checkVersion;
    EXPECT_EQ(newCheckVersion, oldCheckVersion + 1);

    // 分区表，多线程同时启动不同分区的对账, 都成功
    ret = pthread_create(&tid[7], NULL, app7_begin_check_partition_1, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[8], NULL, app8_begin_check_partition_2, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[7], NULL);
    pthread_join(tid[8], NULL);

    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, false));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, normal_label_name));
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, partition_label_name));

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testCheckEndNonCheckingPartition)
{
    const char *partition_label_name = "CHECK_PARTITION_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_partition_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_PARTITION_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"partition", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_PARTITION_LABEL",
                        "name":"CHECK_PARTITION_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcStmtT *stmt1 = NULL;
    status_t ret = GmcAllocStmt(conn, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建分区表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt1, test_partition_label_json, test_config_json));

    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt1, partition_label_name, false));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt1, partition_label_name));

    GmcFreeStmt(stmt1);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StQueryCheck, testCheckEndAllCheckingNormalWithoutBegin)
{
    const char *normal_label_name = "CHECK_LABEL";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"CHECK_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"int32"}
                ],
            "keys":
                [
                    {
                        "node":"CHECK_LABEL",
                        "name":"CHECK_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建分区表
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json));

    // 结束对账
    EXPECT_EQ(GMERR_OK, GmcEndAllPartitionCheck(stmt, normal_label_name, false));

    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, normal_label_name));

    DestroyConnectionAndStmt(conn, stmt);
}
#endif
#endif
