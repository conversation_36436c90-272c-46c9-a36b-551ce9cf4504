#include "storage_st_common.h"

#define MAX_POOL_ID 0xFFFF
#define MAX_POOL_START_IDX 0xFFFFFFFF
const char *g_optLabelName = "OP_T0";

void SetVertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void SetVertexProperty(GmcStmtT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

static void CLientInit(GmcConnT **connection, GmcStmtT **stmt)
{
    CreateSyncConnectionAndStmt(connection, stmt);
}

static void CLientFinal(GmcConnT *connection, GmcStmtT *stmt)
{
    GmcFreeStmt(stmt);
    int32_t ret = GmcDisconnect(connection);
    EXPECT_EQ(ret, GMERR_OK);
}

static int32_t InsertVertex(GmcStmtT *stmt, int32_t value, uint16_t poolId, uint16_t count, uint32_t startIndex)
{
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t resourceId = 0;
    EXPECT_EQ(GMERR_OK, GmcSetCountResource(count, &resourceId));
    EXPECT_EQ(GMERR_OK, GmcSetPoolIdResource(poolId, &resourceId));
    EXPECT_EQ(GMERR_OK, GmcSetStartIdxResource(startIndex, &resourceId));
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_RESOURCE, &resourceId, sizeof(resourceId));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    return ret;
}

const char *kvTableName = "student";
const char *kvConfig = R"({"max_record_count":1000000})";
const char *kvConfig_no_lite = R"({"max_record_count":1000000, "isFastReadUncommitted":false})";

int32_t InsertKv(GmcStmtT *stmt, const char *key, int32_t value)
{
    return GmcKvSet(stmt, (void *)key, strlen(key) + 1, &value, sizeof(int32_t));
}

void *SetKvLabelData(void *arg)
{
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    int32_t ret;

    ret = GmcKvPrepareStmtByLabelName(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(connection, &config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = InsertKv(stmt, "zhangsan1", 10);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_TRANSACTION_ROLLBACK);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {  // 超时是正常预期
        EXPECT_EQ(GMERR_OK, SeStCheckLastError(connection, ret, LK_NO_AVA_WAIT_TOO_LONG));
    } else if (ret == GMERR_TRANSACTION_ROLLBACK) {  // QE检测需要执行回滚
        ret = GmcTransRollBack(connection);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcTransStart(connection, &config);  // 重新开启事务
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = InsertKv(stmt, "zhangsan2", 20);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {  // 超时是正常预期
        EXPECT_EQ(GMERR_OK, SeStCheckLastError(connection, ret, LK_NO_AVA_WAIT_TOO_LONG));
    } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
        ret = GmcTransRollBack(connection);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcTransStart(connection, &config);  // 重新开启事务
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = InsertKv(stmt, "zhangsan3", 30);
    if (ret == GMERR_LOCK_NOT_AVAILABLE) {  // 超时是正常预期
        EXPECT_EQ(GMERR_OK, SeStCheckLastError(connection, ret, LK_NO_AVA_WAIT_TOO_LONG));
    } else if (ret == GMERR_TRANSACTION_ROLLBACK) {
        ret = GmcTransRollBack(connection);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcTransStart(connection, &config);  // 重新开启事务
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    DbSleep(300);  // 让事务持有锁3秒，触发死锁检测，测试是否会误报死锁（预期无死锁）

    // 提交事务
    ret = GmcTransCommit(connection);
    if (ret == GMERR_TRANSACTION_ROLLBACK) {
        ret = GmcTransRollBack(connection);
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        EXPECT_EQ(GMERR_OK, ret);
    }

    CLientFinal(connection, stmt);
    return NULL;
}

// 场景：创建8个线程，每个线程开启事务，SET同样Key的5个key-value，提交事务
// 测试：测试并发加锁不会造成死锁，或造成死锁误检测
TEST_F(StStorage, MultiSetKvAndTestLock)
{

    // system("ipcrm -a"); // 清理共享内存
    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    int32_t ret;

    ret = GmcKvCreateTable(stmt, kvTableName, kvConfig_no_lite);
    EXPECT_EQ(GMERR_OK, ret);

    int threadNum = 8;
    pthread_t kvTabel[threadNum];
    for (int i = 0; i < threadNum; i++) {
        ret = pthread_create(&kvTabel[i], NULL, SetKvLabelData, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < threadNum; i++) {
        pthread_join(kvTabel[i], NULL);
    }

    int cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL");
    EXPECT_EQ(0, cmdRet);
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_LOCK_CONFLICT_INFO_STAT");
    EXPECT_EQ(0, cmdRet);
    ret = GmcKvDropTable(stmt, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);

    CLientFinal(connection, stmt);
    // system("ipcrm -a"); // 清理共享内存
}

#ifndef RTOSV2X
#define LOCK_CNT 1000

#ifndef ASAN
// 场景：创建LOCK_CNT+1个表，一个事务内，每个表插入一个数据，触发锁资源不足，回滚
// 测试：测试并发加锁不会造成死锁，或造成死锁误检测
static const char *g_optLabel = R"(
    [{
    "type":"record",
    "name":"OP_T0",
    "fields":[
        {"name":"F0", "type":"int64","nullable":false},
        {"name":"F1", "type":"uint64","nullable":true},
        {"name":"F2", "type":"int32","nullable":true},
        {"name":"F3", "type":"uint32","nullable":true},
        {"name":"F4", "type":"int16","nullable":true},
        {"name":"F5", "type":"uint16","nullable":true},
        {"name":"F6", "type":"int8","nullable":true},
        {"name":"F7", "type":"uint8","nullable":true},
        {"name":"F8", "type":"boolean","nullable":true},
        {"name":"F9", "type":"float","nullable":true},
        {"name":"F10", "type":"double","nullable":true},
        {"name":"F11", "type":"time","nullable":true},
        {"name":"F12", "type":"char","nullable":true},
	    {"name":"F13", "type":"uchar","nullable":true},
        {"name":"F14", "type":"string", "size":100,"nullable":true}
    ],
    "keys":[
       {
            "node":"OP_T0",
            "name":"OP_PK",
            "fields":["F0"],
            "index":{"type":"primary"},
            "constraints":{"unique":true}
        }
    ]

}]
)";
TEST_F(StStorage, OutOfLockRes)
{
    static const char *labelSchemaFormat =
        R"([{
        "type":"record",
        "name":"OFLRlabel%d",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"OFLRlabel%d",
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    GmcConnT *connection = NULL;
    GmcStmtT *stmt = NULL;
    CLientInit(&connection, &stmt);
    int32_t ret;

    const char *labelConfig_no_lite = R"({"max_record_count":1000, "isFastReadUncommitted":false})";

    // 建LOCK_CNT+1个表
    for (int32_t i = 0; i < LOCK_CNT + 1; i++) {
        char labelSchema[1000] = "";
        ret = snprintf(labelSchema, sizeof(labelSchema), labelSchemaFormat, i, i);
        EXPECT_EQ(true, ret > 0);
        // printf("%s\n", labelSchema);
        ret = GmcCreateVertexLabel(stmt, labelSchema, labelConfig_no_lite);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(connection, &config);
    EXPECT_EQ(GMERR_OK, ret);
    int successCnt = 0;  // 连续成功计数

    // 每个表插入1条数据
    for (int32_t i = 0; i < LOCK_CNT + 1; i++) {
        char labelName[20] = "";
        ret = snprintf(labelName, sizeof(labelName), "OFLRlabel%d", i);
        EXPECT_EQ(true, ret > 0);

        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_TRANSACTION_ROLLBACK);
        if (ret == GMERR_TRANSACTION_ROLLBACK) {
            ret = GmcTransRollBack(connection);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcTransStart(connection, &config);  // 重新开启事务
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
        }

        int32_t value = 10;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_LOCK_NOT_AVAILABLE || ret == GMERR_TRANSACTION_ROLLBACK);
        if (ret == GMERR_TRANSACTION_ROLLBACK || ret == GMERR_LOCK_NOT_AVAILABLE) {
            successCnt = 0;  // 清零
            if (ret == GMERR_LOCK_NOT_AVAILABLE) {
                // 应该有上千把锁
                int cmdRet = GetViewFieldResultWithCustomCmd(
                    "V\\$STORAGE_TRX_DETAIL", NULL, "| grep '  LOCK_TYPE: ' -c | grep '[1-9][0-9]\\{3,3\\}'");
                EXPECT_EQ(0, cmdRet);
                // 超过1024的至少分两段展示
                cmdRet = GetViewFieldResultWithCustomCmd(
                    "V\\$STORAGE_TRX_DETAIL", NULL, "| grep 'HOLD_LOCK_INFO' -c | grep -v '[0-1]'");
                EXPECT_EQ(0, cmdRet);
                printf("out of lock resourcem, label:%d\n", i);
            }
            ret = GmcTransRollBack(connection);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcTransStart(connection, &config);  // 重新开启事务
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            successCnt++;
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    if (successCnt >= LOCK_CNT) {
        // 应该有上千把锁
        int cmdRet = GetViewFieldResultWithCustomCmd(
            "V\\$STORAGE_TRX_DETAIL", NULL, "| grep '  LOCK_TYPE: ' -c | grep '[1-9][0-9]\\{3,3\\}'");
        EXPECT_EQ(0, cmdRet);
        // 超过1024的至少分两段展示
        cmdRet = GetViewFieldResultWithCustomCmd(
            "V\\$STORAGE_TRX_DETAIL", NULL, "| grep 'HOLD_LOCK_INFO' -c | grep -v '[0-1]'");
        EXPECT_EQ(0, cmdRet);
    }
    ret = GmcTransCommit(connection);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_TRANSACTION_ROLLBACK);
    if (ret == GMERR_TRANSACTION_ROLLBACK) {
        ret = GmcTransRollBack(connection);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 删除LOCK_CNT+1个表
    for (int32_t i = 0; i < LOCK_CNT + 1; i++) {
        char labelName[20] = "";
        ret = snprintf(labelName, sizeof(labelName), "OFLRlabel%d", i);
        EXPECT_EQ(true, ret > 0);
        ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    CLientFinal(connection, stmt);
}

// 为验证STORAGE_TRX_DETAIL统一创建表
const char *trxDetailLabelJson =
    R"([{
            "type":"record",
            "name":"TRXDETAIL_TEST_LABEL",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3}
                ],
            "keys":
                [
                    {
                        "node":"TRXDETAIL_TEST_LABEL",
                        "name":"TRXDETAIL_TEST_LABEL_INDEX",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

const char *trxDetailLabelName = "TRXDETAIL_TEST_LABEL";
const char *trxDetailIndexName = "TRXDETAIL_TEST_LABEL_INDEX";

// 验证STORAGE_TRX_DETAIL在两个事务操作的正确性
TEST_F(StStorage, TestTrxDetailView)
{
    // 创建表
    StUtilCreateTbl(trxDetailLabelName, trxDetailLabelJson, kvConfig_no_lite);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    GmcTxConfigT config;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret;
    int cmdRet;
    // 排除掉视图的事务锁事务，应该没有其他事务
    cmdRet = GetViewFieldResultWithCustomCmd(
        "V\\$STORAGE_TRX_DETAIL", NULL, "| grep -v 'HOLD_LOCK_NUM: 0' | grep 'HOLD_LOCK_NUM'");
    EXPECT_NE(0, cmdRet);

    // 开启第一个事务
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));
    // 有两个事务，包括刚开启的事务
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", NULL, "| grep 'index = 0'");
    EXPECT_EQ(0, cmdRet);
    // 插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, trxDetailLabelName, GMC_OPERATION_INSERT));

    char *value_F0 = (char *)"lll";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 事务插入一行，会获取表IX锁，行X锁，那么拥有两把锁的事务应该只有1个，且拥有IX锁1把，X锁1把
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=2",
        "| grep 'HOLD_X_LOCK_NUM: 1\\|HOLD_IX_LOCK_NUM: 1' -c | grep '^2$'");
    EXPECT_EQ(0, cmdRet);

    // 新连接开启事务
    // 先进行一次查询操作，让事务获取对应表的IS锁。
    GmcConnT *conn2;
    ASSERT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn2));
    GmcStmtT *stmt2;
    GmcAllocStmt(conn2, &stmt2);
    GmcPrepareStmtByLabelName(stmt2, trxDetailLabelName, GMC_OPERATION_SCAN);
    // 开启事务
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn2, &config));
    // 有3个事务，包括刚开启的事务
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", NULL, "| grep 'index = 1'");
    EXPECT_EQ(0, cmdRet);

    // 扫描一下，以获取表的IX锁
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt2, trxDetailIndexName));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_STRING, value_F0, strlen(value_F0)));
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    bool isEnd;
    EXPECT_EQ(GMERR_OK, GmcFetch(stmt2, &isEnd));

    // 上面的查询操作使第2个事务获取了IS锁
    cmdRet =
        GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=1", "| grep 'HOLD_IS_LOCK_NUM: 1'");
    EXPECT_EQ(0, cmdRet);

    // 事务1再插入一条数据
    value_F0 = (char *)"ggg";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务1获取了一把表IX锁，两把行X锁
    // 没有事务持有两把锁，因为事务1有用三个锁
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=2", "| grep 'index ='");
    EXPECT_NE(0, cmdRet);
    // 事务1拥有3把锁
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=3",
        "| grep 'HOLD_X_LOCK_NUM: 2\\|HOLD_IX_LOCK_NUM: 1' -c | grep '^2$'");
    EXPECT_EQ(0, cmdRet);

    // 事务2插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, trxDetailLabelName, GMC_OPERATION_INSERT));
    value_F0 = (char *)"sss";
    ret = GmcSetVertexProperty(stmt2, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    // 事务2持有两把锁，IX和X各一把
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=2",
        "| grep 'HOLD_X_LOCK_NUM: 1\\|HOLD_IX_LOCK_NUM: 1' -c | grep '^2$'");
    EXPECT_EQ(0, cmdRet);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交后没有事务
    cmdRet =
        system("gmsysview -q V\\$STORAGE_TRX_DETAIL | grep -v 'HOLD_LOCK_NUM: 0' | grep 'HOLD_LOCK_NUM' > /dev/null");
    cmdRet = GetViewFieldResultWithCustomCmd(
        "V\\$STORAGE_TRX_DETAIL", NULL, "| grep -v 'HOLD_LOCK_NUM: 0' | grep 'HOLD_LOCK_NUM'");
    EXPECT_NE(0, cmdRet);

    ret = GmcDropVertexLabel(stmt, trxDetailLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
    GmcFreeStmt(stmt2);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn2));
}

// 验证STORAGE_TRX_DETAIL在单个事务操作的正确性
TEST_F(StStorage, TestTrxDetailView1)
{
    StUtilCreateTbl(trxDetailLabelName, trxDetailLabelJson, kvConfig_no_lite);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    GmcTxConfigT config;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int cmdRet;
    Status ret;
    // 排除掉视图的事务锁事务，应该没有其他事务
    cmdRet = GetViewFieldResultWithCustomCmd(
        "V\\$STORAGE_TRX_DETAIL", NULL, "| grep -v 'HOLD_LOCK_NUM: 0' | grep 'HOLD_LOCK_NUM'");
    EXPECT_NE(0, cmdRet);

    // 开启一个事务
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));
    // 有两个事务，包括刚开启的事务
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", NULL, "| grep 'index = 0'");
    EXPECT_EQ(0, cmdRet);
    // 插入一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, trxDetailLabelName, GMC_OPERATION_INSERT));

    char *value_F0 = (char *)"lll";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value_F0, strlen(value_F0));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 事务插入一行，会获取表IX锁，行X锁，那么拥有两把锁的事务应该只有1个，且拥有IX锁1把，X锁1把
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=2",
        "| grep 'HOLD_X_LOCK_NUM: 1\\|HOLD_IX_LOCK_NUM: 1' -c | grep '^2$'");
    EXPECT_EQ(0, cmdRet);

    // 事务再插入20条数据
    char value[3] = {'a', 'a', '\0'};
    for (int i = 0; i < 20; i++) {
        value[1] = value[1] + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, value, strlen(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 事务拥有21 + 1 = 22 把锁
    cmdRet = GetViewFieldResultWithCustomCmd("V\\$STORAGE_TRX_DETAIL", "HOLD_LOCK_NUM=22",
        "| grep 'HOLD_X_LOCK_NUM: 21\\|HOLD_IX_LOCK_NUM: 1' -c | grep '^2$'");
    EXPECT_EQ(0, cmdRet);

    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    // 提交后没有事务
    cmdRet = GetViewFieldResultWithCustomCmd(
        "V\\$STORAGE_TRX_DETAIL", NULL, "| grep -v 'HOLD_LOCK_NUM: 0' | grep 'HOLD_LOCK_NUM'");
    EXPECT_NE(0, cmdRet);

    ret = GmcDropVertexLabel(stmt, trxDetailLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
}

TEST_F(StStorage, TestGetLockFromLockEntryPool)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    const char *pkName = "OP_PK";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int32_t ret = 0;
    ret = GmcCreateVertexLabel(stmt, g_optLabel, NULL);
    ASSERT_EQ(GMERR_OK, ret);
    uint startNum = 0;
    uint endNum = 300;

    // 插入顶点
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_optLabelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint i = startNum; i < endNum; i++) {
        SetVertexProperty_PK(stmt, i);
        SetVertexProperty(stmt, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(endNum, totalNum);
    ASSERT_EQ(endNum, successNum);

    // 更新顶点
    ret = GmcPrepareStmtByLabelName(stmt, g_optLabelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    for (uint i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        SetVertexProperty(stmt, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(stmt, pkName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        ASSERT_EQ(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ASSERT_EQ(endNum, totalNum);
    ASSERT_EQ(endNum, successNum);

    ret = GmcDropVertexLabel(stmt, g_optLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
}

void *ExecuteGmsysview(void *arg)
{
    int cmdRet = -1;
    int cnt = 0;
    while (cmdRet != 0 && cnt < 5) {
        cmdRet = GetViewFieldResultWithCustomCmd(
            "V\\$STORAGE_TRX_DETAIL", NULL, "| grep -v 'LOCK_NOTIFY_ID: 4294967295' | grep 'LOCK_NOTIFY_ID'");
        cnt++;
        sleep(1);
    }
    EXPECT_EQ(0, cmdRet);
    return NULL;
}

void *ParallelTrxWorker(void *arg)
{
    // 开启事务2
    GmcConnT *conn2;
    EXPECT_EQ(GMERR_OK, ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn2));
    GmcStmtT *stmt2;
    GmcAllocStmt(conn2, &stmt2);
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn2, &config));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt2, "vertex1", GMC_OPERATION_DELETE));
    Status ret = GmcDropVertexLabel(stmt2, "vertex1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTransCommit(conn2);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmt2);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn2));

    return NULL;
}

TEST_F(StStorage, TestTrxDetailView2)
{
    const char *vertexLabelName = "vertex1";
    const char *vertexJson =
        R"([{
            "type":"record",
            "name":"vertex1",
            "fields":
                [
                    {"name":"F0", "type":"string", "size":3},
                    {"name":"F1", "type":"uint32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"vertex1",
                        "name":"vertex1_PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 创建表
    StUtilCreateTbl(vertexLabelName, vertexJson, kvConfig_no_lite);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt;
    GmcTxConfigT config;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    Status ret;

    // 隐式事务先插一条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_INSERT));
    char *f0 = (char *)"lll";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, f0, strlen(f0));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1 = 0;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 开启第一个事务
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    EXPECT_EQ(GMERR_OK, GmcTransStart(conn, &config));

    // 更新那条数据
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_UPDATE));
    char *keyValue = (char *)"lll";
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, keyValue, strlen(keyValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "vertex1_PK");
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newValue = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &newValue, sizeof(newValue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建查询线程，验证视图有效
    pthread_t thread1;
    if (pthread_create(&thread1, NULL, ExecuteGmsysview, NULL) != 0) {
        ASSERT_EQ(0, 1);
    }

    // // 创建线程执行事务2，删除表
    pthread_t thread2;
    if (pthread_create(&thread2, NULL, ParallelTrxWorker, NULL) != 0) {
        ASSERT_EQ(0, 1);
    }

    sleep(1);
    ret = GmcTransCommit(conn);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    GmcFreeStmt(stmt);
    EXPECT_EQ(GMERR_OK, GmcDisconnect(conn));
}

#endif
#endif
