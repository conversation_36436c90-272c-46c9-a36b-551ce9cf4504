#include "client_common_st.h"
#include "tools_st_common.h"

class StClientVertex : public StClient {};

static const char *g_label_name = "T39";
static const char *g_label_config = R"(
    {
        "max_record_count":1000,
        "auto_increment":100
    })";
static const char *g_label_schema =
    R"([{
        "type":"record",
        "name":"T39",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"T39",
                    "name":"T39_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{ "unique":true}
                }
            ]
        }])";

static const char *read_g_label_name = "T35";
static const char *read_g_label_config = R"({"max_record_count":1000, "delta_store_name":"xtds1", "writers":"XXuser"})";
static const char *read_g_label_schema =
    R"([{
        "type":"record",
        "name":"T35",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"T35",
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *catalog_g_label_name = "catalog";
static const char *catalog_g_label_config = R"({"max_record_count":1000})";
static const char *catalog_g_label_schema =
    R"([{
        "type":"record",
        "name":"catalog",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"catalog",
                    "name":"catalog_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *g_sysModel_label_name = "sysModel";
static const char *g_sysModel_json_config = R"(
{
    "max_record_count":100
})";
static const char *g_test_sysModel_json =
    R"([{
  "version": "2.0", "type": "record", "name": "sysModel",
  "fields": [
    { "name": "c0", "type": "uint32" },
    { "name": "c1", "type": "record",
      "fields": [
        { "name": "f1", "type": "uint32", "nullable":true},
        { "name": "f2", "type": "float", "nullable":true },
        { "name": "f3", "type": "time", "nullable":true}
      ]
    },
    { "name": "c3", "type": "string", "size":20, "nullable":true },
    { "name": "c4", "type": "record",
      "vector": true, "size": 1024,
      "fields": [
        { "name": "b1", "type": "float", "nullable":true },
        { "name": "b2", "type": "boolean", "nullable":true }
      ]
    },
    { "name": "c5", "type": "bytes", "size": 12, "nullable":true },
    { "name": "c6", "type": "record",
      "fields": [
        { "name": "t1", "type": "fixed", "size": 12, "nullable":true},
        { "name": "t2", "type": "record",
          "fixed_array": true, "size": 2,
          "fields": [
            { "name": "h1", "type": "string", "size":20, "nullable":true},
            { "name": "h2", "type": "uint32", "nullable":true}
          ]
        },
        { "name": "t3", "type": "uint32", "nullable":true}
      ]
    }
  ],
  "keys": [
    { "name": "table_pk", "index": { "type": "primary" },
      "node": "sysModel",
      "fields": [ "c0" ],
      "constraints": { "unique": true }
    }
  ]
}])";

static const char *g_test_sysModel_json1 =
    R"([{
  "version": "2.0", "type": "record", "name": "sysModel",
  "schema_version":3,
  "fields": [
    { "name": "c0", "type": "uint32" },
    { "name": "c1", "type": "record",
      "fields": [
        { "name": "f1", "type": "uint32", "nullable":true},
        { "name": "f2", "type": "float", "nullable":true },
        { "name": "f3", "type": "time", "nullable":true}
      ]
    },
    { "name": "c3", "type": "string", "size":20, "nullable":true },
    { "name": "c4", "type": "record",
      "vector": true, "size": 1024,
      "fields": [
        { "name": "b1", "type": "float", "nullable":true },
        { "name": "b2", "type": "boolean", "nullable":true }
      ]
    },
    { "name": "c5", "type": "bytes", "size": 12, "nullable":true },
    { "name": "c6", "type": "record",
      "fields": [
        { "name": "t1", "type": "fixed", "size": 12, "nullable":true},
        { "name": "t2", "type": "record",
          "fixed_array": true, "size": 2,
          "fields": [
            { "name": "h1", "type": "string", "size":20, "nullable":true},
            { "name": "h2", "type": "uint32", "nullable":true}
          ]
        },
        { "name": "t3", "type": "uint32", "nullable":true}
      ]
    },
    { "name": "c7", "type": "uint32", "nullable":false, "default": 10}
  ],
  "keys": [
    { "name": "table_pk", "index": { "type": "primary" },
      "node": "sysModel",
      "fields": [ "c0" ],
      "constraints": { "unique": true }
    }
  ]
}])";

const char *g_sysModel_json_data =
    R"({"c0": 1, "c3": "abc", "c5": "0x313300000000000000000000", "c1": {"f1": 2, "f2": 3.0, "f3": "2021-03-24 00:00:00"}, "c4": [{"b1": 1.0, "b2": true}, {"b1": 2.0, "b2": false}], "c6": {"t1": "0x313300000000000000000000", "t3": 1, "t2": [{"h1": "abc", "h2": null}, {"h1": "def", "h2": 2}]}})";

const char *g_sysModel_json_data1 =
    R"({"c0": 1, "c3": "abc", "c5": "0x313300000000000000000000", "c7": 10, "c1": {"f1": 2, "f2": 3.0, "f3": "2021-03-24 00:00:00"}, "c4": [{"b1": 1.0, "b2": true}, {"b1": 2.0, "b2": false}], "c6": {"t1": "0x313300000000000000000000", "t3": 1, "t2": [{"h1": "abc", "h2": null}, {"h1": "def", "h2": 2}]}})";

const char *g_sysModel_json_invalid_data = R"({"a0": 1, "a2": "abc", "a3": "0x313300000000000000000000"})";

static const char *g_ip4route_status_label_name = "ip4route_status_255";
static const char *g_test_ip4route_status_255_json =
    R"([{
    "version": "2.0",
    "name": "ip4route_status_255",
    "type": "record",
    "fields": [
        {
            "name": "id",
            "type": "uint64"
        },
        {
            "name": "table",
            "type": "uint32",
            "nullable":true
        },
        {
            "name": "scope",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "tos",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "protocol",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "priority",
            "type": "uint32",
            "nullable":true
        },
        {
            "name": "family",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "type",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "flags",
            "type": "uint32",
            "nullable":true
        },
        {
            "name": "iif",
            "type": "uint32",
            "nullable":true
        },
        {
            "name": "dst_prefix",
            "type": "uint8",
            "nullable":true
        },
        {
            "name": "dst_ip4",
            "type": "string", "size": 128,
            "nullable":true
        },
        {
            "name": "src_ip4",
            "type": "string", "size": 128,
            "nullable":true
        },
        {
            "name": "prefsrc_ip4",
            "type": "string", "size": 128,
            "nullable":true
        },
        {
            "name": "nh_count",
            "type": "uint32",
            "nullable":true
        },
        {
            "name": "nhs",
            "type": "record",
            "nullable": false,
            "fixed_array": true,
            "size": 8,
            "fields": [
                {
                    "name": "ifindex",
                    "type": "uint32",
                    "nullable":true
                },
                {
                    "name": "weight",
                    "type": "uint32",
                    "nullable":true
                },
                {
                    "name": "flags",
                    "type": "uint8",
                    "nullable":true
                },
                {
                    "name": "realms",
                    "type": "uint32",
                    "nullable":true
                },
                {
                    "name": "gateway_ip4",
                    "type": "string", "size": 128,
                    "nullable":true
                }
            ]
        }
    ],
    "keys": [
        {
            "node": "ip4route_status_255",
            "name": "ip4route_status_255_pk",
            "index": {
                "type": "primary"
            },
            "fields": [
                "id"
            ]
        }
    ]
}])";

const char *g_ip4route_status_255_json_data =
    R"({
    "id": "18446744072668362273",
    "table": 255,
    "scope": 254,
    "tos": 0,
    "protocol": 2,
    "priority": 0,
    "family": 2,
    "type": 2,
    "flags": 0,
    "iif": 0,
    "dst_prefix": 32,
    "dst_ip4": "*************",
    "src_ip4": "",
    "prefsrc_ip4": "*************",
    "nh_count": 1,
    "nhs": [
        {
            "ifindex": 3,
            "weight": 0,
            "flags": 0,
            "realms": 0,
            "gateway_ip4": ""
        }
    ]
})";

void *truncate_createEdgeLabel_thread(void *arg)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *cfgJson = "{\"max_record_count\":1000}";
    const char *labelJson = R"([{
        "name":"truncateEdgeLabel",
        "source_vertex_label":"TRUN",
        "comment":"the edge 7 to 8",
        "dest_vertex_label":"TRUN2",
        "constraint":{
            "operator_type":"and",
            "conditions":[
                {"source_property":"F0", "dest_property":"F0"},
                {"source_property":"F1","dest_property":"F1"}
            ]
        }
    }])";

    status_t ret = GmcCreateEdgeLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StClientVertex, testCreateLabelPropertySameName)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {"name":"F0", "type":"char","default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar","default":"f", "nullable":false},
                    {"name":"F1", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testPrepareStmt)
{
    int32_t ret = GmcCreateVertexLabel(syncStmt, g_label_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(syncStmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(syncStmt);

    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    ASSERT_STREQ(g_label_name, MEMBER_PTR(&vertexLabel->metaCommon, metaName));

    ret = GmcDropVertexLabel(syncStmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientVertex, testInsertVertexMSALL)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    const char *vertexLikeName = "T39Like";
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabelWithName(stmt, g_label_schema, g_label_config, vertexLikeName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // test point: 顶点句柄待插入的值(int32_t)，与实际类型符合, 预期成功
    int32_t int32_tmp = -131313;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    vertex = CltGetVertexInStmt(stmt);
    EXPECT_NE(nullptr, vertex);

    ret = GmcPrepareStmtByLabelName(stmt, vertexLikeName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    ASSERT_STREQ(vertexLikeName, MEMBER_PTR(&vertexLabel->metaCommon, metaName));

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, vertexLikeName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testInsertVertexMSErr)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // test point: 顶点句柄待插入的值(int32_t)，与实际类型符合, 预期成功
    int32_t int32_tmp = -131313;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);

    vertex = CltGetVertexInStmt(stmt);
    EXPECT_NE(nullptr, vertex);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testReplaceVertexMSALL)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcDropVertexLabel(stmt, g_label_name);

    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    int32_t int32_tmp = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    int32_tmp = 1003;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // query
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        uint32_t f1 = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        if (!isNull) {
            EXPECT_EQ(1001u, f1);
        }
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    int32_tmp = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 2001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 2002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    // 存在vertex先删再插入，affectRows会统计删除的行
    EXPECT_EQ(2, affectRows);

    // query vertex
    tmp = 1000;
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        uint32_t f1 = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2001u, f1);

        int f2;
        isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2002, f2);
        // f3字段replace后为空字段
        int f3;
        isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_TRUE(isNull);
    }

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

int CheckSysview(const char *view)
{
    // 检查缩容任务是否执行
    char cmdOutput[64] = {0};
    FILE *pf = popen(view, "r");
    EXPECT_NE((void *)NULL, pf);
    while (fgets(cmdOutput, 64, pf) != NULL) {
    };
    pclose(pf);
    return atoi(cmdOutput);
}

TEST_F(StClientVertex, testDirectReadWithoutHeapInit)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcDropVertexLabel(stmt, g_label_name);

    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // STORAGE_HEAP_STAT去掉了HEAP_SEGID和HEAP_OFFSET字段，不能再使用该字段校验
    // 延迟初始化heap，此时heap的共享内存地址应该为invalid

    // 全表扫
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    int32_t recordCnt = 0;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        recordCnt++;
    }
    EXPECT_EQ(0, recordCnt);

    // 主键读
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    recordCnt = 0;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        recordCnt++;
    }
    EXPECT_EQ(0, recordCnt);

    // 直连读操作不触发heap创建
    // STORAGE_HEAP_STAT去掉了HEAP_SEGID和HEAP_OFFSET字段，不能再使用该字段校验

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    int32_t int32_tmp = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    int32_tmp = 1003;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // replace操作触发创建heap，记录数为1
    // STORAGE_HEAP_STAT去掉了HEAP_SEGID和HEAP_OFFSET字段，不能再使用该字段校验
    int item =
        CheckSysview("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=\'T39\' | grep CUR_ITEM_NUM | awk '{print $2}'");
    EXPECT_EQ(1, item);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);

    int32_tmp = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 2001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 2002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    // 存在vertex先删再插入，affectRows会统计删除的行
    EXPECT_EQ(2, affectRows);

    // query vertex
    tmp = 1000;
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        uint32_t f1 = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2001u, f1);

        int f2;
        isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2002, f2);
        // f3字段replace后为空字段
        int f3;
        isNull = false;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_TRUE(isNull);
    }

    // 删表时heap处于被创建状态，记录数为1
    // STORAGE_HEAP_STAT去掉了HEAP_SEGID和HEAP_OFFSET字段，不能再使用该字段校验
    item =
        CheckSysview("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=\'T39\' | grep CUR_ITEM_NUM | awk '{print $2}'");
    EXPECT_EQ(1, item);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

/*
 * merge not support in DS Mode
 */
TEST_F(StClientVertex, testMergeVertexNotSupportDsMode)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    GmcDropVertexLabel(stmt, g_label_name);

    int ret = 0;
    ret = GmcCreateVertexLabel(stmt, g_label_schema, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // vertex不存在时插入顶点
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t int32_tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    int32_tmp = 1003;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    TestLastError(conn, "Feature is not supported. ds does not support merge operation");
    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testMergeVertexMSALL)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    GmcDropVertexLabel(stmt, g_label_name);

    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // vertex不存在时插入顶点
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_t int32_tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(ret, GMERR_OK);
    int32_tmp = 1003;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // query
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    int tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t f1 = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(f1), &isNull);
        EXPECT_EQ(ret, GMERR_OK);

        if (!isNull) {
            EXPECT_EQ(1001u, f1);
        }
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    int32_tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    int32_tmp = 2001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    // 如果没有vertex, merge进去相当于insert 1行，affect rows为1
    // 存在已有vertex,表示当前的vertex被更新，affect rows为2
    EXPECT_EQ(2, affectRows);

    // query vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    tmp = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &tmp, sizeof(int));
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcSetIndexKeyName(stmt, "T39_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_OK);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t f1 = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &f1, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(2001u, f1);

        int f2Val;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &f2Val, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1002, f2Val);

        int f3Val;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &f3Val, sizeof(int), &isNull);
        EXPECT_EQ(ret, GMERR_OK);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1003, f3Val);
    }

    char cmd[1024];
    uint32_t len = 1024;
    char const *viewName = "V\\$QRY_DML_OPER_STATIS";
    snprintf_s(cmd, len, len - 1, "gmsysview -q %s -f LABEL_NAME=T39", viewName);
    ret = executeCommand(cmd, "DML_TYPE: MERGE_VERTEX", "SUCCESS_COUNT: 2", "FAIL_COUNT: 0", "TOTAL_COUNT: 2");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteVertexByMSAll)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *g_label_name2 = "DXTT46";
    const char *test_mainS_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"DXTT46",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"DXTT46",
                        "name":"DXTT46_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // test point: 创建vertex lable (不带deltaS)
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F1Value = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int F0Value1 = 14;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value1, sizeof(F0Value1));
    EXPECT_EQ(GMERR_OK, ret);

    int F1Value1 = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value1, sizeof(F1Value1));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value1 = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value1, sizeof(F2Value1));
    EXPECT_EQ(GMERR_OK, ret);

    int F3Value1 = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value1, sizeof(F3Value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT46_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // 顶点已被删除，查询失败
    value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT46_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    value = 13;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT46_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 删除不存在的顶点 预期成功(server侧对删除不存在的顶点不报错)
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=\'DXTT46\'");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteVertexByDSAll)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *g_label_name2 = "DXTT47";
    const char *test_mainS_config_json = R"({"max_record_count":1000, "delta_store_name":"xtds1", "writers":"XXuser"})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"DXTT47",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"DXTT47",
                        "name":"DXTT47_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // test point: 创建vertex lable (带deltaS)
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int F0Value1 = 14;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value1, sizeof(F0Value1));
    EXPECT_EQ(GMERR_OK, ret);
    int F1Value1 = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value1, sizeof(F1Value1));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value1 = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value1, sizeof(F2Value1));
    EXPECT_EQ(GMERR_OK, ret);
    int F3Value1 = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value1, sizeof(F3Value1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int cnt = 0;
    for (;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
    }
    std::cout << "vertex count: " << cnt << std::endl;

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT47_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 主键删除顶点
    int32_t value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT47_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 再次查询顶点，预计查询失败
    value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    // test point: 删除不存在的顶点 预期成功(server侧对删除不存在的顶点不报错)
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DXTT47_K0");
    ASSERT_EQ(GMERR_OK, ret);
    value = 13;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "DXTT47");
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testCreateLabelWithoutPrimaryKey)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"T38",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32"}
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config);
    ret = GmcPrepareStmtByLabelName(stmt, "T38", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int F0Value = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    char *metaName = MEMBER_PTR(&cltCataLabel->vertexLabel->metaCommon, metaName);
    ret = strcmp(metaName, "T38");
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1u, cltCataLabel->cltRefCount);

    ret = GmcDropVertexLabel(stmt, "T38");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

#if (defined RTOSV2 || defined RTOSV2X)
#else
TEST_F(StClientVertex, testCreateAndDrop2048)
{
    const char *test_mainS_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"V1_V5_CD",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"V1_V5_CD",
                        "name":"XTT42_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    uint32_t count = 0;
    uint32_t ret;
    for (unsigned int i = 0; i < 2048; i++) {
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
        if (ret == GMERR_OK) {
            count++;
        }
        ret = GmcAllocStmt(conn, &stmt);
        if (ret == GMERR_OK) {
            count++;
        }
        ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
        if (ret == GMERR_OK) {
            count++;
        }
        GmcFreeStmt(stmt);
        ret = GmcDisconnect(conn);
        if (ret == GMERR_OK) {
            count++;
        }
        ret = ConnectWrapper(GMC_CONN_TYPE_SYNC, serverLocator, userName, NULL, &conn);
        if (ret == GMERR_OK) {
            count++;
        }
        ret = GmcAllocStmt(conn, &stmt);
        if (ret == GMERR_OK) {
            count++;
        }
        ret = GmcDropVertexLabel(stmt, "V1_V5_CD");
        if (ret == GMERR_OK) {
            count++;
        }
        GmcFreeStmt(stmt);
        ret = GmcDisconnect(conn);
        if (ret == GMERR_OK) {
            count++;
        }
    }
    EXPECT_EQ(16384u, count);
}
#endif

TEST_F(StClientVertex, testTruncateNormal)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    GmcStmtT *stmtToTestTruncated;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, GmcAllocStmt(conn, &stmtToTestTruncated));

    const char *g_truncateLabelName = "TRUN";
    const char *test_mainS_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"TRUN",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"TRUN",
                        "name":"TRUN_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_truncateLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtToTestTruncated, g_truncateLabelName, GMC_OPERATION_INSERT));
    GmcResetStmt(stmtToTestTruncated);

    for (int32_t i = 0; i < 20; i++) {
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=\'TRUN\'");
    EXPECT_EQ(ret, GMERR_OK);

    ret = GmcPrepareStmtByLabelName(stmt, g_truncateLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            EXPECT_EQ(20, cnt);
            break;
        }
    }

    ret = GmcTruncateVertexLabel(stmt, g_truncateLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_truncateLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isFinish);
        break;
    }

    ret = system("gmsysview -q V\\$QRY_DML_OPER_STATIS -f LABEL_NAME=\'TRUN\'");
    EXPECT_EQ(ret, GMERR_OK);

    for (int32_t i = 0; i < 100; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_truncateLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_truncateLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (int cnt = 0;; ++cnt) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            EXPECT_EQ(100, cnt);
            break;
        }
    }

    ret = GmcDropVertexLabel(stmt, g_truncateLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtToTestTruncated);
    DestroyConnectionAndStmt(conn, stmt);
}

void *truncate_thread(void *arg)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *g_truncateLabelName = "TRUN";
    GmcTruncateVertexLabel(stmt, g_truncateLabelName);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

void *truncate_drop_thread(void *arg)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *g_truncateLabelName = "TRUN";
    status_t ret = GmcDropVertexLabel(stmt, g_truncateLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    const char *g_truncateLabelName2 = "TRUN2";
    ret = GmcDropVertexLabel(stmt, g_truncateLabelName2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
    return NULL;
}

TEST_F(StClientVertex, testTruncateMultThread)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *g_truncateLabelEdge = "truncateEdgeLabel";
    const char *test_mainS_config_json = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"TRUN",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"TRUN",
                        "name":"TRUN_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    const char *test_normal_label_json2 =
        R"([{
            "type":"record",
            "name":"TRUN2",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"TRUN2",
                        "name":"TRUN_K02",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, test_normal_label_json2, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_t tid[4];
    ret = pthread_create(&tid[0], NULL, truncate_createEdgeLabel_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, truncate_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);

    ret = GmcDropEdgeLabel(stmt, g_truncateLabelEdge);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&tid[2], NULL, truncate_drop_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = pthread_create(&tid[3], NULL, truncate_thread, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 等待线程结束
    pthread_join(tid[2], NULL);
    pthread_join(tid[3], NULL);

    // truncate_drop_thread先将label从缓存删除，然后向服务端发送删除操作，
    // 但在服务端处理删除操作前，truncate_thread可能又会从服务端重新获取label，导致用例完成后缓存不为空。
    // 对于同步连接，可以通过服务端完成删除操作后再将label从缓存删除的方式解决这个问题，
    // 但是治标不治本，因为对于异步连接无法立刻得到操作结果，仍然可能存在处理删除操作前先获取label的问题。
    // 对于本用例，可以通过最终手动执行一次drop保证缓存为空。
    const char *g_truncateLabelName = "TRUN";  // truncate_thread使用的label
    ret = GmcDropVertexLabel(stmt, g_truncateLabelName);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, test_veretx_by_name_base)
{
    // 创建一个stmt
    GmcStmtT *stmt = NULL;
    int32_t ret = GmcAllocStmt(g_conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 创建vertex lable (带deltaS)
    ret = GmcCreateVertexLabel(stmt, catalog_g_label_schema, catalog_g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 第一次获取元数据，从cs获取，且保存
    ret = GmcPrepareStmtByLabelName(stmt, catalog_g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    EXPECT_EQ((uint32_t)1u, cltCataLabel->cltRefCount);

    // 第二次获取元数据，从catalog获取
    ret = GmcPrepareStmtByLabelName(stmt, catalog_g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1u, cltCataLabel->cltRefCount);

    // 第三次获取元数据，从catalog获取
    ret = GmcPrepareStmtByLabelName(stmt, catalog_g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ((uint32_t)1u, cltCataLabel->cltRefCount);

    GmcFreeStmt(stmt);
    EXPECT_EQ((uint32_t)0u, cltCataLabel->cltRefCount);

    ret = GmcAllocStmt(g_conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // drop
    ret = GmcDropVertexLabel(stmt, catalog_g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    // test new
    ret = GmcCreateVertexLabel(stmt, catalog_g_label_schema, catalog_g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, catalog_g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    CltCataLabelT *cltCataLabel2 = CltGetCltCataLabelInStmt(stmt);
    EXPECT_EQ((uint32_t)1u, cltCataLabel2->cltRefCount);

    ret = GmcDropVertexLabel(stmt, catalog_g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    ASSERT_NE(nullptr, vertex);

    GmcFreeStmt(stmt);
}

TEST_F(StClientVertex, test_veretx_with_partition_abnormal)
{
    // 创建一个stmt
    GmcStmtT *stmt = NULL;
    int32_t ret = GmcAllocStmt(g_conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 分区字段 不能 作为索引
    const char *label_schema_partition =
        R"([{
        "type":"record",
        "name":"catalog_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"partition", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"catalog_with_partition",
                    "name":"catalog_K0",
                    "fields":["F0", "F3"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
}])";
    // test point: 创建vertex lable (带deltaS)
    ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);

    const char *lastErrorStr1 = GmcGetLastError();
    const char *result =
        "Inv table definition. The property F3 cannot be defined as an index field because of the field type.";
    EXPECT_STREQ(result, lastErrorStr1);

    // 分区字段 不能 nullable
    label_schema_partition =
        R"([{
        "type":"record",
        "name":"catalog_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"partition", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"catalog_with_partition",
                    "name":"catalog_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
}])";
    ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);

    const char *lastErrorStr = GmcGetLastError();
    EXPECT_STREQ(
        "Null value is not allowed. Property F3 is null in resource type.vertexLabel name:catalog_with_partition.",
        lastErrorStr);

    // 分区字段 不能 有 多个
    label_schema_partition =
        R"([{
        "type":"record",
        "name":"catalog_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"partition", "nullable":false},
                {"name":"F4", "type":"partition", "nullable":false}

            ],
        "keys":
            [
                {
                    "node":"catalog_with_partition",
                    "name":"catalog_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
}])";
    ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);

    lastErrorStr1 = GmcGetLastError();
    result = (char *)"Inv table definition. A label can only have a partition field, property F4 is incorrect.";
    EXPECT_STREQ(result, lastErrorStr1);

    // 分区字段 不能 有 支持非根节点
    label_schema_partition =
        R"([{
        "type":"record",
        "name":"catalog_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"int32", "nullable":false},
                {
                "name": "a3",
                "type": "record",
                "vector": true,
                "size": 128,
                "fields": [
                  {
                    "name": "b5",
                    "type": "partition", "nullable":false
                  }
                ]
              }
            ],
        "keys":
            [
                {
                    "node":"catalog_with_partition",
                    "name":"catalog_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
}])";
    ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_INVALID_TABLE_DEFINITION, ret);

    lastErrorStr1 = GmcGetLastError();
    result = (char *)"Inv table definition. Property:b5,datatype:19,autoinc:0";
    EXPECT_STREQ(result, lastErrorStr1);

    GmcFreeStmt(stmt);
}

static void PrepareVertexFor_test_veretx_with_partition_normal(GmcStmtT *stmt)
{
    int32_t value = 16;
    int32_t ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_BITFIELD32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
}

TEST_F(StClientVertex, test_veretx_with_partition_normal)
{
    // 创建一个stmt
    GmcStmtT *stmt = NULL;
    int32_t ret = GmcAllocStmt(g_conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    static const char *label_name_partition = "catalog_with_partition";
    static const char *label_schema_partition =
        R"([{
        "type":"record",
        "name":"catalog_with_partition",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F3", "type":"uint32: 8", "nullable":false},
                {"name":"F1", "type":"partition", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"catalog_with_partition",
                    "name":"catalog_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    // test point: 创建vertex lable
    ret = GmcCreateVertexLabel(stmt, label_schema_partition, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取元数据
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    EXPECT_EQ((uint32_t)1u, cltCataLabel->cltRefCount);

    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = (VertexLabelCommonInfoT *)DbShmPtrToAddr(vertexLabel->commonInfoShmPtr);
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)DbShmPtrToAddr(commonInfo->accCheckShm);
    EXPECT_EQ(8U, accCheckAddr->partitionPropeOffset);  // 分区字段在定长属性中的偏移

    PrepareVertexFor_test_veretx_with_partition_normal(stmt);
    // 分区字段 不设置(空值)
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);
    // 设置一个非法值
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    PrepareVertexFor_test_veretx_with_partition_normal(stmt);
    uint8_t F1Value = 16;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);
    F1Value = 0;  // 设置正常范围的值
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    // 插入成功
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 分区字段 更新不一样的值, 预期失败
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value = 16;
    F1Value = 10;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "catalog_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INTERNAL_ERROR, ret);

    // 分区字段 更新一样的值, 预期OK
    ret = GmcPrepareStmtByLabelName(stmt, label_name_partition, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 0;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_PARTITION, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "catalog_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, label_name_partition);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlInvalidStmt)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_INSERT);
    EXPECT_NE(GMERR_OK, ret);

    ret = GmcSetVertexByJson(NULL, GMC_JSON_REJECT_DUPLICATES, g_sysModel_json_data);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, g_sysModel_json_data);
    EXPECT_NE(GMERR_OK, ret);

    char *itemJsonOutput = NULL;
    ret = GmcDumpVertexToJson(NULL, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJsonOutput);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJsonOutput);
    EXPECT_NE(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlInvalidJsonPara)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_test_sysModel_json, g_sysModel_json_config);
    ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, NULL);
    EXPECT_NE(GMERR_OK, ret);
    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, g_sysModel_json_invalid_data);
    EXPECT_NE(GMERR_OK, ret);

    ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), NULL);
    EXPECT_NE(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, g_sysModel_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlValidOperation)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_test_sysModel_json, g_sysModel_json_config);
    ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, g_sysModel_json_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c0>0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    if (!isFinish) {
        char *itemJsonOutput = NULL;
        ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJsonOutput);
        EXPECT_EQ(GMERR_OK, ret);
        int ret = strncmp(g_sysModel_json_data, itemJsonOutput, strlen(itemJsonOutput));
        EXPECT_EQ(0, ret);
    }

    ret = GmcDropVertexLabel(stmt, g_sysModel_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlValidOperation2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_test_sysModel_json, g_sysModel_json_config);
    // 升级
    ret = GmcAlterVertexLabelWithName(stmt, g_test_sysModel_json1, true, g_sysModel_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, g_sysModel_json_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // prepare低版本，设值高版本的过滤条件，报错
    ret = GmcPrepareStmtByLabelName(stmt, g_sysModel_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7>5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    // prepare高版本
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_sysModel_label_name, 3, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7>5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    if (!isFinish) {
        char *itemJsonOutput = NULL;
        ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJsonOutput);
        EXPECT_EQ(GMERR_OK, ret);
        int ret = strncmp(g_sysModel_json_data1, itemJsonOutput, strlen(itemJsonOutput));
        EXPECT_EQ(0, ret);
    }

    // prepare高版本
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_sysModel_label_name, 3, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7<5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(isFinish);

    // 删除
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_sysModel_label_name, 0, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7>5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_sysModel_label_name, 3, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7>5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 数据被删除
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, g_sysModel_label_name, 3, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "c7>5");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_TRUE(isFinish);

    ret = GmcDropVertexLabel(stmt, g_sysModel_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 单层定长node，升级新增变长字段
TEST_F(StClientVertex, testExecUpdateVertexWithFixNode)
{
    const char *cfgJson = R"({"max_record_count":100,
    "support_undetermined_length": true})";
    const char *labelJsonT0 =
        R"([
        {"name":"T0",
         "type":"record",
         "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"},
            { "name":"ifm", "type":"record",
            "fields": [
                { "name":"simple_name", "type":"uint32", "nullable":true},
                { "name":"description", "type":"fixed", "size": 6, "nullable":true}
                ]
            }
            ],
            "keys":
                [
                    {
                        "node":"T0",
                        "name":"T0_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
        }
    ])";
    const char *labelJsonT01 =
        R"([
        {"name":"T0",
         "type":"record",
         "schema_version":1,
         "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"},
            { "name":"ifm", "type":"record",
            "fields": [
                { "name": "simple_name", "type":"uint32", "nullable":true},
                { "name": "description", "type":"fixed", "size": 6, "nullable":true},
                { "name": "upgradeField01", "type": "uint32", "nullable":true},
                { "name": "upgradeField02", "type": "string", "nullable":true}
                ]
            }
            ],
            "keys":
                [
                    {
                        "node":"T0",
                        "name":"T0_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
        }
    ])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int32_t ret = GmcCreateVertexLabel(stmt, labelJsonT0, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "T0", 0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t F0Value, F1Value;
    for (uint32_t i = 0; i < 3; i++) {
        F0Value = 10 + i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = 100 + 100 * i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcAlterVertexLabelWithName(stmt, labelJsonT01, true, "T0");
    EXPECT_EQ(GMERR_OK, ret);

    // 更新
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "T0", 1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    F1Value = 666;
    GmcNodeT *rootNode;
    GmcNodeT *childNode;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(rootNode, (char *)"F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootNode, "ifm", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t f1 = F1Value + 1;
    ret = GmcNodeSetPropertyByName(childNode, (char *)"simple_name", GMC_DATATYPE_UINT32, &f1, sizeof(f1));
    EXPECT_EQ(GMERR_OK, ret);
    char c3Value[] = "huawei";
    ret = GmcNodeSetPropertyByName(childNode, "upgradeField02", GMC_DATATYPE_STRING, c3Value, strlen(c3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 低版本进行扫描
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "T0", 0, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    uint32_t f1GetValue;
    bool isNull;
    ret = GmcGetRootNode(stmt, &rootNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(rootNode, "F1", &f1GetValue, sizeof(f1GetValue), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(F1Value, f1GetValue);
    ret = GmcNodeGetChild(rootNode, "ifm", &childNode);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetPropertyByName(childNode, "simple_name", &f1GetValue, sizeof(f1GetValue), &isNull);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    EXPECT_EQ(F1Value + 1, f1GetValue);

    ret = GmcDropVertexLabel(stmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlValidOperationForNewType)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *sysModel_label_name = "sysModel";

    const char *test_sysModel_json =
        R"([{
      "version": "2.0", "type": "record", "name": "sysModel",
      "fields": [
        {"name":"F0", "type":"uint32", "nullable":false},
        {"name":"F1", "type":"uint8: 4", "nullable":true},
        {"name":"F2", "type":"uint8: 4", "nullable":true},
        {"name":"F3", "type":"uint16: 10", "nullable":true},
        {"name":"F4", "type":"uint16: 6", "nullable":true},
        {"name":"F5", "type":"uint32: 10", "nullable":true},
        {"name":"F6", "type":"uint32: 22", "nullable":true},
        {"name":"F7", "type":"uint64: 44", "nullable":true},
        {"name":"F8", "type":"uint64: 20", "nullable":true},
        {"name":"F9", "type":"partition", "nullable":false},
        {"name":"F10", "type":"bitmap", "size":96},
        {"name":"T1", "type": "record",
            "fields": [
            {"name":"F1", "type":"uint8: 4", "nullable":true},
            {"name":"F2", "type":"uint8: 4", "nullable":true},
            {"name":"F3", "type":"uint16: 10", "nullable":true},
            {"name":"F4", "type":"uint16: 6", "nullable":true},
            {"name":"F5", "type":"uint32: 10", "nullable":true},
            {"name":"F6", "type":"uint32: 22", "nullable":true},
            {"name":"F7", "type":"uint64: 44", "nullable":true},
            {"name":"F8", "type":"uint64: 20", "nullable":true},
            {"name":"F9", "type":"bitmap", "size":96, "nullable":true}]}
      ],
      "keys": [
        { "name": "table_pk", "index": { "type": "primary" },
          "node": "sysModel",
          "fields": [ "F0" ],
          "constraints": { "unique": true }
        }
      ]
    }])";

    const char *sysModel_json_config = R"(
    {
        "max_record_count":100
    })";

    const char *sysModel_json_data =
        R"({"F0": 1, "F1": 1, "F2": 2, "F3": 3, "F4": 4, "F5": 5, "F6": 6, "F7": 7, "F8": 8, "F9": 5, "F10": "0x313300000000000000000000", "T1": {"F1": 1, "F2": 2, "F3": 3, "F4": 4, "F5": 5, "F6": 6, "F7": 7, "F8": 8, "F9": "0x313300000000000000000000"}})";

    const char *sysModel_json_data_bin =
        R"({"F0": 1, "F1": 1, "F2": 2, "F3": 3, "F4": 4, "F5": 5, "F6": 6, "F7": "7", "F8": "8", "F9": 5, "F10": "1000 1100 1100 1100 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000", "T1": {"F1": 1, "F2": 2, "F3": 3, "F4": 4, "F5": 5, "F6": 6, "F7": "7", "F8": "8", "F9": "1000 1100 1100 1100 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000"}})";
    status_t ret = GmcCreateVertexLabel(stmt, test_sysModel_json, sysModel_json_config);

    ret = GmcPrepareStmtByLabelName(stmt, sysModel_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, sysModel_json_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, sysModel_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "F0>0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    if (!isFinish) {
        char *itemJsonOutput = NULL;
        ret = GmcDumpVertexToJson(stmt, GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0), &itemJsonOutput);
        printf("Json in %s \n", sysModel_json_data);
        printf("Json out %s \n", itemJsonOutput);
        EXPECT_EQ(GMERR_OK, ret);
        int ret = strncmp(sysModel_json_data_bin, itemJsonOutput, strlen(itemJsonOutput));
        EXPECT_EQ(0, ret);
    }

    ret = GmcDropVertexLabel(stmt, g_sysModel_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteGraphVertexloop)
{
    /*
        ## vertex ###########################
                   F0        F1        F2        F3

        [src01]    12        13        132       321

        [dst02]    22        23        132       321

        ## edgelabe info: ###################
            edgelabel01     src01 -> dst02   (F2 && F3)

        ## relationship: ####################
                 01_1
                 02_1

    */

    // edgeLabel  --01
    const char *edgeLabelName01 = "autoDelEdgelable04";
    const char *cfgJson01 = R"({"max_record_count":1000})";
    const char *labelJson01 =
        R"([{
            "name":"autoDelEdgelable04",
            "source_vertex_label":"edgeAutoDelSrcVertex04","comment":"the edge 01 to 02",
            "dest_vertex_label":"edgeAutoDelDstVertex05",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // edgeAutoDelSrcVertex04
    const char *edgeSrcVertexLabelName = "edgeAutoDelSrcVertex04";
    const char *srccfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeSrcVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDelSrcVertex04",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDelSrcVertex04",
                        "name":"edgeAutoDelSrcVertex04_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // edgeAutoDelDstVertex05
    const char *edgeDstVertexLabelName = "edgeAutoDelDstVertex05";
    const char *dstcfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeDstVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDelDstVertex05",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDelDstVertex05",
                        "name":"edgeAutoDelDstVertex05_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    int32_t ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建 edgeAutoDelSrcVertex04
    ret = GmcCreateVertexLabel(stmt, edgeSrcVertexLabel, srccfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeAutoDelDstVertex05
    ret = GmcCreateVertexLabel(stmt, edgeDstVertexLabel, dstcfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeLabel  --01
    ret = GmcCreateEdgeLabel(stmt, labelJson01, cfgJson01);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);
    int32_t round;
    if (IsEulerEnv()) {
        round = 50;
    } else {
        round = 40;
    }
    for (int32_t i = 0; i < round; i++) {
        // set [ edgeAutoDelSrcVertex04 ] for one row data
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int F0Value = 12;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        int F1Value = 13;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        int F2Value = 132;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        int F3Value = 321;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int value = 12;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex04_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // set [ edgeAutoDelDstVertex05 ] for one row data
        ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);

        F0Value = 22;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = 23;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = 132;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        F3Value = 321;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        value = 22;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex05_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        GmcFreeStmt(stmt);

        // 关联删除图顶点
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        value = 12;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex04_PK0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcDeleteGraphVertex(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(2, affectRows);

        GmcFreeStmt(stmt);

        // 验证 关联顶点已删除
        ret = GmcAllocStmt(conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        value = 12;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex04_PK0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);  // 不存在

        ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        value = 22;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex05_PK0");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);  // 不存在
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, true);
        GmcFreeStmt(stmt);
        if (i % 10 == 0) {
            printf("wait %d ... \n", i);
        }
        DbUsleep(200000);
    }

    // 删除元数据
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(stmt, edgeLabelName01);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, edgeSrcVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, edgeDstVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteGraphVertex)
{
    /*
        ## vertex ###########################
                   F0        F1        F2        F3

        [src01]    12        13        132       321
                   13        14        142       351

        [dst02]    22        23        132       321
                   23        234       142       321

        [third03]  32        33        332       321
                   33        334       342       321
                   34        334       345       321

        ## edgelabe info: ###################
            edgelabel01     src01 -> dst02   (F2 && F3)
            edgelabel02     dst02 -> third03 (F3)

        ## relationship: ####################
                 01_1
                 02_1                         02_2
        03_1   03_2   03_3             03_1  03_2   03_3
    */

    // edgeLabel  --01
    const char *edgeLabelName01 = "autoDelEdgelable01";
    const char *cfgJson01 = R"({"max_record_count":1000})";
    const char *labelJson01 =
        R"([{
            "name":"autoDelEdgelable01",
            "source_vertex_label":"edgeAutoDelSrcVertex01","comment":"the edge 01 to 02",
            "dest_vertex_label":"edgeAutoDelDstVertex02",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // edgeLabel  --02
    const char *edgeLabelName02 = "autoDelEdgelable02";
    const char *cfgJson02 = R"({"max_record_count":1000})";
    const char *labelJson02 =
        R"([{
            "name":"autoDelEdgelable02",
            "source_vertex_label":"edgeAutoDelDstVertex02","comment":"the edge 02 to 03",
            "dest_vertex_label":"edgeAutoDelThirdVertex03",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // edgeAutoDelSrcVertex01
    const char *edgeSrcVertexLabelName = "edgeAutoDelSrcVertex01";
    const char *srccfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeSrcVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDelSrcVertex01",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDelSrcVertex01",
                        "name":"edgeAutoDelSrcVertex01_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // edgeAutoDelDstVertex02
    const char *edgeDstVertexLabelName = "edgeAutoDelDstVertex02";
    const char *dstcfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeDstVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDelDstVertex02",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDelDstVertex02",
                        "name":"edgeAutoDelDstVertex02_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // edgeAutoDelThirdVertex03
    const char *edgeThirdVertexLabelName = "edgeAutoDelThirdVertex03";
    const char *ThirdCfgJson1 = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    const char *edgeThirdVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDelThirdVertex03",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDelThirdVertex03",
                        "name":"edgeAutoDelThirdVertex03_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 建立连接
    GmcConnT *conn;
    GmcStmtT *stmt;
    int32_t ret;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 创建 edgeAutoDelSrcVertex01
    ret = GmcCreateVertexLabel(stmt, edgeSrcVertexLabel, srccfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeAutoDelDstVertex02
    ret = GmcCreateVertexLabel(stmt, edgeDstVertexLabel, dstcfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeAutoDelThirdVertex03
    ret = GmcCreateVertexLabel(stmt, edgeThirdVertexLabel, ThirdCfgJson1);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeLabel  --01
    ret = GmcCreateEdgeLabel(stmt, labelJson01, cfgJson01);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建 edgeLabel  --02
    ret = GmcCreateEdgeLabel(stmt, labelJson02, cfgJson02);
    EXPECT_EQ(GMERR_OK, ret);

    // set [ edgeAutoDelSrcVertex01 ] for tow rows data
    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int F0Value = 12;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex01_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 13;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 14;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 142;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 351;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 13;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex01_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    // set [ edgeAutoDelDstVertex02 ] for tow rows data
    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 22;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 23;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 132;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 23;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 234;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 142;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 23;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    // set [ edgeAutoDelThirdVertex03 ] for three rows data
    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = 32;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 33;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 332;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 32;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 33;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 334;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 342;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 33;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 34;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 334;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 345;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    F3Value = 321;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 34;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    GmcResetVertex(stmt, false);

    GmcFreeStmt(stmt);

    // 查询已建的边   edgelabel01     src01 -> dst02  (01_1 -- 02_1)
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "edgeAutoDelSrcVertex01", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, "autoDelEdgelable01");
    ASSERT_EQ(GMERR_OK, ret);

    int valueTmp;
    bool isNull;
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(22, valueTmp);
    }

    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 查询已建的边   edgelabel02     dst02 -> third03  (02_1 -- 03_1 | 02_1 -- 03_2 | 02_1 -- 03_3)
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "edgeAutoDelDstVertex02", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, "autoDelEdgelable02");
    ASSERT_EQ(GMERR_OK, ret);

    valueTmp = 0;
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (valueTmp == 32) {
            EXPECT_EQ(32, valueTmp);
        } else if (valueTmp == 33) {
            EXPECT_EQ(33, valueTmp);
        } else if (valueTmp == 34) {
            EXPECT_EQ(34, valueTmp);
        } else {
            EXPECT_EQ(999, valueTmp);  // 用于排除异常数据
        }
    }

    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 查询已建的边   edgelabel02     dst02 -> third03  (02_2 -- 03_1 | 02_2 -- 03_2 | 02_2 -- 03_3)
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, "edgeAutoDelDstVertex02", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 23;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDirectFetchNeighborBegin(stmt, "autoDelEdgelable02");
    ASSERT_EQ(GMERR_OK, ret);

    valueTmp = 0;
    while (true) {
        bool isEof;
        ret = GmcFetch(stmt, &isEof);
        EXPECT_EQ(GMERR_OK, ret);
        if (isEof) {
            break;
        }
        ret = GmcGetVertexPropertyByName(stmt, "F0", &valueTmp, sizeof(valueTmp), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        if (valueTmp == 32) {
            EXPECT_EQ(32, valueTmp);
        } else if (valueTmp == 33) {
            EXPECT_EQ(33, valueTmp);
        } else if (valueTmp == 34) {
            EXPECT_EQ(34, valueTmp);
        } else {
            EXPECT_EQ(999, valueTmp);  // 用于排除异常数据
        }
    }

    ret = GmcDirectFetchNeighborEnd(stmt);
    ASSERT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 关联删除图顶点
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex01_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDeleteGraphVertex(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(5, affectRows);

    GmcFreeStmt(stmt);

    // 验证 关联顶点已删除
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, edgeSrcVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 12;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelSrcVertex01_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 不存在
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeDstVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 不存在
    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
    GmcResetVertex(stmt, false);
    value = 23;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 存在
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 32;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 不存在
    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 33;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 不存在
    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
    GmcResetVertex(stmt, false);

    ret = GmcPrepareStmtByLabelName(stmt, edgeThirdVertexLabelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 34;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);  // 不存在
    isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);
    GmcResetVertex(stmt, false);

    GmcFreeStmt(stmt);

    // 验证删除的边，已被删除
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    void *edgeLabelGet = NULL;
    ret = GmcOpenEdgeLabelByName(stmt, edgeLabelName01, &edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, "edgeAutoDelSrcVertex01_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    value = 12;
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    value = 22;
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDeleteEdgeByIndexKey(stmt, edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    edgeLabelGet = NULL;
    ret = GmcOpenEdgeLabelByName(stmt, edgeLabelName02, &edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetEdgeSrcVertexIndexName(stmt, "edgeAutoDelDstVertex02_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, "edgeAutoDelThirdVertex03_PK0");
    EXPECT_EQ(GMERR_OK, ret);
    value = 23;
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);
    value = 32;
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDeleteEdgeByIndexKey(stmt, edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);
    affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, affectRows);

    ret = GmcCloseEdgeLabel(stmt, edgeLabelGet);
    EXPECT_EQ(GMERR_OK, ret);
    GmcFreeStmt(stmt);

    // 删除元数据
    ret = GmcAllocStmt(conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, edgeSrcVertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testInsertVertexDsALL)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, read_g_label_schema, read_g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, read_g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // test
    int32_t int32_tmp = -121212;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &int32_tmp, sizeof(int32_tmp));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    unsigned int autoFlag = 0;
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_AUTO_DELETE_EDGE, &autoFlag, sizeof(autoFlag));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, read_g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDropVertex1)
{
    const char *g_label_name2 = "XTT60";
    const char *test_delat_config_json = R"({"max_record_count":1000, "delta_store_name":"xtds1", "writers":"XXuser"})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"XTT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XTT60",
                        "name":"XTT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 删除vertex lable
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // test point: 再次删除vertex lable
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDropVertex2)
{
    const char *g_label_name2 = "XTT60";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    ;
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"XTT60",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},{"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"XTT60",
                        "name":"XTT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 删除vertex lable
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);

    // test point: 再次删除vertex lable
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDropVertex3)
{
    const char *g_label_name2 = "XTT60";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"XTT60",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32","default":2, "nullable":false},
                    {"name":"F3", "type":"string","size":200,"default":"10.20.30.40", "nullable":false}],
            "keys":
                [
                    {
                        "node":"XTT60",
                        "name":"XTT60_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcStmtT *stmt = syncStmt;

    // test point: 创建vertex lable (带deltaS)
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // test point:设置属性值
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // test point: 删除vertex lable
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
}

TEST_F(StClientVertex, testUpdateVertex1)
{
    const char *g_label_name2 = "updateVlabel";
    int insertNum = 1;
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"updateVlabel",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}],
            "keys":
                [
                    {
                        "node":"updateVlabel",
                        "name":"updateVlabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    int i;
    int F0Value, F1Value, F2Value;
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        F0Value = i;
        F1Value = i;
        F2Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int value;
    unsigned int propSize;
    bool isNull;

    // update vertex
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
        ASSERT_EQ(GMERR_OK, ret);
        value = i + 1;
        GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
        GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
        ret = GmcSetIndexKeyName(stmt, "updateVlabel_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        int affectRows;
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, affectRows);
    }
    // qry vertex
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "updateVlabel_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            ASSERT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(stmt, "F0", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(i, value);
            int expect = i + 1;
            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(expect, value);

            ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(expect, value);
        }
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateVertex2)
{
    const char *g_label_name2 = "UpdateVertex2";
    const char *test_mainS_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateVertex2",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32","default":2, "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateVertex2",
                        "name":"UpdateVertex2_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 123456712;
    F2Value = 123;
    F3Value = (char *)"10.12.12.13";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateVertex2_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateVertex2_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        long long F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(long long), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        int F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        char F3V[500];
        ret = GmcGetVertexPropertyByName(stmt, "F3", F3V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F3Value, F3V);
    }

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateEmptyVertex)
{
    const char *g_label_name2 = "updateEmptyVertex";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"updateEmptyVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}
                ],
            "keys":
                [
                    {
                        "node":"updateEmptyVertex",
                        "name":"updateEmptyVertex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json));
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT));

    // insert vertex
    int oriValue = 1;
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &oriValue, sizeof(int)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &oriValue, sizeof(int)));
    EXPECT_EQ(GMERR_OK, GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &oriValue, sizeof(int)));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE));
    // update vertex
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &oriValue, sizeof(int)));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "updateEmptyVertex_K0"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    // qry vertex
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN));
    EXPECT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &oriValue, sizeof(int)));
    ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "updateEmptyVertex_K0"));
    EXPECT_EQ(GMERR_OK, GmcExecute(stmt));

    while (true) {
        bool isFinish;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        bool isNull;
        int F0V;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(int), &isNull));
        EXPECT_EQ(F0V, oriValue);

        int F1V;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(int), &isNull));
        EXPECT_EQ(F1V, oriValue);

        int F2V;
        EXPECT_EQ(GMERR_OK, GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(int), &isNull));
        EXPECT_EQ(F2V, oriValue);
    }
    int ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateVertex3)
{
    const char *g_label_name2 = "updateVlabe3";
    int insertNum = 1;
    const char *test_delat_config_json = R"({"max_record_count":1000, "delta_store_name":"xtds1", "writers":"XXuser"})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"updateVlabe3",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}
                ],
            "keys":
                [
                    {
                        "node":"updateVlabe3",
                        "name":"updateVlabe3_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);
    // insert vertex
    int i;
    int F0Value, F1Value, F2Value;
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        F1Value = i;
        F2Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int value;
    unsigned int propSize;
    bool isNull;

    // update vertex
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        value = i + 1;
        GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
        GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "updateVlabe3_K0");
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // qry vertex
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "updateVlabe3_K0");
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        while (true) {
            bool isFinish;
            ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
            if (isFinish) {
                break;
            }
            ret = GmcGetVertexPropertySizeByName(stmt, "F0", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(sizeof(int), propSize);

            ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(i, value);
            int expect = i + 1;
            ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(expect, value);

            ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_FALSE(isNull);
            EXPECT_EQ(expect, value);
        }
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateVertex4)
{
    const char *g_label_name2 = "UpdateVertex4";
    const char *test_mainS_config_json = R"({"max_record_count":1000, "delta_store_name":"xtds1", "writers":"XXuser"})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateVertex4",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32","default":2, "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateVertex4",
                        "name":"UpdateVertex4_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_mainS_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 123456712;
    F2Value = 123;
    F3Value = (char *)"10.12.12.13";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateVertex4_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateVertex4_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }

        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        long long F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(long long), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        int F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        char F3V[500];
        ret = GmcGetVertexPropertyByName(stmt, "F3", F3V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F3Value, F3V);
    }

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateUniqueSecondIndex)
{
    const char *g_label_name2 = "UpdateSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"uint32","nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32","default":2, "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateSecondIndex",
                        "name":"UpdateSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert vertex1
    uint32_t F0Value = 2;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    long long F1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value = 1314;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update unique index
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 1313;
    F3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    // update vertex
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t F0V;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &F0V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F0Value, F0V);

        long long F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(long long), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        int F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        char F3V[500];
        ret = GmcGetVertexPropertyByName(stmt, "F3", F3V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F3Value, F3V);
    }

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateNonUniqueSecondIndex)
{
    const char *g_label_name2 = "UpdateNonUniqueSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex2
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = (char *)"huawe";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 1313;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    F3Value = (char *)"10.12.12.13";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update non unique index duplicate
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 1311;
    F3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_UNIQUE_VIOLATION, ret);

    // update non unique index
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 1313;
    F3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        long long F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(long long), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        int F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        char F3V[500];
        ret = GmcGetVertexPropertyByName(stmt, "F3", F3V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F3Value, F3V);
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUpdateVertexWithCondition)
{
    const char *g_label_name = "updateVlabelWithCondition";
    int insertNum = 2;
    const char *test_delta_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"updateVlabelWithCondition",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}],
            "keys":
                [
                    {
                        "node":"updateVlabelWithCondition",
                        "name":"updateVlabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带delta json)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delta_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int i;
    int F0Value, F1Value, F2Value;
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        F1Value = i + 1;
        F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int value;
    unsigned int propSize;
    bool isNull;

    // update vertex condition is "F1=1"
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    value = 3;
    GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    ret = GmcSetFilter(stmt, "updateVlabelWithCondition.F1=1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // qry vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "updateVlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = GmcGetVertexPropertySizeByName(stmt, "F0", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sizeof(int), propSize);

        ret = GmcGetVertexPropertySizeByName(stmt, "F1", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sizeof(int), propSize);

        ret = GmcGetVertexPropertySizeByName(stmt, "F2", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sizeof(int), propSize);

        ret = GmcGetVertexPropertySizeByName(stmt, "F3", &propSize);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(sizeof(int), propSize);

        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0, value);
        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(1, value);
        ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(3, value);
        ret = GmcGetVertexPropertyByName(stmt, "F3", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(3, value);
    }

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteVertexUniqueSecondIndex)
{
    const char *g_label_name2 = "DeleteSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"DeleteSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteSecondIndex",
                        "name":"DeleteSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 构造filter值查询，filter值正确，预期查询到
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 查询顶点，顶点存在
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    // 删除顶点
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 再次查询顶点，预计查询失败

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteVertexNonUniqueSecondIndex)
{
    const char *g_label_name2 = "DeleteNonUniqueSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"DeleteNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K1",
                        "fields":["F1"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"DeleteNonUniqueSecondIndex",
                        "name":"DeleteNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 构造filter值查询，filter值正确，预期查询到
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    // 查询顶点，顶点存在
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除顶点

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 再次查询顶点，预计查询失败
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "DeleteNonUniqueSecondIndex_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDeleteVertexWithCondition)
{
    const char *g_label_name = "deleteVlabelWithCondition";
    int insertNum = 2;
    const char *test_delta_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"deleteVlabelWithCondition",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}],
            "keys":
                [
                    {
                        "node":"deleteVlabelWithCondition",
                        "name":"deleteVlabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delta_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex
    int i;
    int F0Value, F1Value, F2Value;
    for (i = 0; i < insertNum; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        F1Value = i + 1;
        F2Value = i + 2;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    int value;

    // query vertex where F0 = 0 by index
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "deleteVlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // delete vertex, condition is "F1=2"
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "deleteVlabelWithCondition.F1=1");
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // qry vertex where F0 = 0 by index after delete the vertex
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    value = 0;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value, sizeof(unsigned int));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "deleteVlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(isFinish, true);

    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testPropertyId)
{
    const char *g_label_name2 = "testPropertyId";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testPropertyId",
            "fields":
                [
                    {"name":"F3", "type":"int32","nullable":false},
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32","nullable":true}
                    ],
            "keys":
                [
                    {
                        "node":"testPropertyId",
                        "name":"testPropertyId_K0",
                        "fields":["F3"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    DmVertexLabelT *cltVertexLabel = CltGetCltCataLabelInStmt(stmt)->vertexLabel;
    DmSchemaT *schema = MEMBER_PTR5(cltVertexLabel->metaVertexLabel, schema);
    DmVlIndexLabelT *pkIndex = MEMBER_PTR5(cltVertexLabel->metaVertexLabel, pkIndex);
    for (unsigned int i = 0; i < schema->propeNum; i++) {
        ASSERT_EQ(i, MEMBER_PTR5(schema, properties)[i].propeId);
    }

    ASSERT_EQ(0u, MEMBER_PTR5(pkIndex, propIds)[0]);

    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testMultiplePrimaryKeyCheck)
{
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJson =
        R"([
        {"name":"MultiplePrimaryKeyCheck",
         "type":"record",
         "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ],
            "keys":
                [
                    {
                        "node":"MultiplePrimaryKeyCheck",
                        "name":"T0_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"MultiplePrimaryKeyCheck",
                        "name":"T0_K1",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
        }
    ])";
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int32_t ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);

    const char *lastErrorStr1 = GmcGetLastError();
    const char *result = "Duplicate object. Multiple primary keys in vertexLabel MultiplePrimaryKeyCheck.";
    EXPECT_STREQ(result, lastErrorStr1);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testVertexNoPk)
{
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJsonT0 =
        R"([
        {"name":"T0",  "type":"record",  "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ]
        }
    ])";

    const char *labelJsonT1 =
        R"([
        {"name":"T1", "type":"record", "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ],
        "keys":
        [
            {"node":"T1", "name":"T1_IDX", "fields":["F1"], "index":{"type":"localhash"}}
        ]
        }
    ])";

    const char *labelJsonT2 =
        R"([{
        "type":"record",
        "name":"T2",
        "fields":[
            {"name":"F0", "type":"uint32"},
            {"name":"F1", "type":"uint32"},
            {"name":"F2", "type":"uint32"}
        ],
        "keys":
        [
            {"node":"T2", "name":"T2_UIDX", "fields":["F2"], "index":{"type":"localhash"}, "constraints":{"unique":true}}
        ]
    }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    //  创建 T0 no idx
    int32_t ret = GmcCreateVertexLabel(stmt, labelJsonT0, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    //  创建 T1 localidx
    ret = GmcCreateVertexLabel(stmt, labelJsonT1, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    //  创建 T2 local unique idx
    ret = GmcCreateVertexLabel(stmt, labelJsonT2, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入 T0 数据
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int F0Value = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F1Value = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 全量删除 T0
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    // 获取T0查询结果
    ret = GmcExecute(stmt);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isFinish);
        break;
    }
    GmcResetStmt(stmt);

    // 插入 T3 数据
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int T0Value = 1000;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &T0Value, sizeof(T0Value));
    EXPECT_EQ(GMERR_OK, ret);
    int T1Value = 1001;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &T1Value, sizeof(T1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int T2Value = 1002;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &T2Value, sizeof(T2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // T1 插入数据
    ret = GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int K0Value = 10;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &K0Value, sizeof(K0Value));
    EXPECT_EQ(GMERR_OK, ret);

    int K1Value = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &K1Value, sizeof(K0Value));
    EXPECT_EQ(GMERR_OK, ret);

    int K2Value = 12;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &K2Value, sizeof(K0Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 全量删除 T1
    ret = GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 获取T1查询结果
    ret = GmcPrepareStmtByLabelName(stmt, "T1", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isFinish);
        break;
    }
    GmcResetStmt(stmt);

    // T2 插入数据	1条
    ret = GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int H0Value = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &H0Value, sizeof(H0Value));
    EXPECT_EQ(GMERR_OK, ret);

    int H1Value = 101;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &H1Value, sizeof(H1Value));
    EXPECT_EQ(GMERR_OK, ret);

    int H2Value = 102;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &H2Value, sizeof(H2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 全量删除 T2
    ret = GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "T2", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_TRUE(isFinish);
        break;
    }
    GmcResetStmt(stmt);

    ret = GmcDropVertexLabel(stmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "T1");
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, "T2");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testVertexNoPkAllUpdate)
{
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJsonT0 =
        R"([
        {"name":"T0",  "type":"record",  "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ]
        }
    ])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    //  创建 T0 no idx
    int32_t ret = GmcCreateVertexLabel(stmt, labelJsonT0, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入 T0 数据
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int F0Value = 0;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F1Value = 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    int F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 10;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 11;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 12;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 更新T0数据
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = 100;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 101;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    F2Value = 102;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int value = 0;
    bool isNull;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        int expectF0 = 100;
        int expectF1 = 101;
        int expectF2 = 102;
        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(expectF0, value);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(expectF1, value);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(expectF2, value);
    }

    ret = GmcDropVertexLabel(stmt, "T0");
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testVertexNoPkAllUpdate2)
{
    const char *cfgJson = R"({"max_record_count":100})";
    const char *labelJsonT0 =
        R"([
        {"name":"T0",  "type":"record",  "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ]
        }
    ])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 打桩最多允许发10条
    conn->flowControl.msgSentLimit = 5;
    conn->flowControl.nextUpdate = DB_MAX_UINT64;
    conn->flowControl.nextPassiveUpdate = DB_MAX_UINT64;
    conn->flowControl.flowControlLevel = GMC_DB_FLOW_CTRL_LEVEL_1;

    // 这里发送了第一条信息
    int32_t ret = GmcCreateVertexLabel(stmt, labelJsonT0, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入 T0 数据
    // 发送到插入第9条消息时候发不过去了，break出来，返回流控错误码
    uint32_t i = 0;
    int F0Value = 0;
    int F1Value = 0;
    int F2Value = 0;
    for (i = 0; i < 20; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            break;
        }
    }
    EXPECT_EQ(4u, i);
    // 打桩这时候周期到了
    conn->flowControl.nextUpdate = 0;
    for (i = 0; i < 20; i++) {
        // 这时候又可以发10条了
        ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i + 1;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (ret != GMERR_OK) {
            break;
        }
    }
    EXPECT_EQ(5u, i);
    // scan 也发不过去
    ret = GmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(ret, GMERR_COMMON_STREAM_OVERLOAD);

    ret = GmcDropVertexLabel(stmt, "T0");
    EXPECT_EQ(ret, GMERR_COMMON_STREAM_OVERLOAD);
    // 打桩这时候周期到了
    conn->flowControl.nextUpdate = 0;
    ret = GmcDropVertexLabel(stmt, "T0");
    EXPECT_EQ(ret, GMERR_OK);
    DestroyConnectionAndStmt(conn, stmt);
}

void CreateSingleLabel(uint32_t maxLabelNum, GmcStmtT *stmt)
{
    const char *labelJsonT00 =
        R"([
        {"name":"T",  "type":"record",  "fields":[
            {"name":"F0", "type":"uint32"},
            { "name":"F1", "type":"uint32"},
            { "name":"F2", "type":"uint32"}
            ]
        }
    ])";
    const char *cfgJson = R"({"max_record_count":300})";
    const char *pattern = R"("name":"T)";
    char labelNumString[10];
    (void)snprintf(labelNumString, 10, "%d", maxLabelNum);
    uint32_t labelNumStringLen = strlen(labelNumString);
    uint32_t jsonLen = strlen(labelJsonT00) + labelNumStringLen + 1;
    char *labelString = (char *)malloc(jsonLen);
    (void)memcpy(labelString, labelJsonT00, strlen(labelJsonT00) + 1);
    char *add = (char *)strstr(labelString, pattern) + strlen(pattern);
    (void)memmove(add + labelNumStringLen, add, strlen(add) + 1);

    uint32_t i = 0;
    for (char *a = add; i < labelNumStringLen; a++, i++) {
        *a = labelNumString[i];
    }

    int32_t ret = GmcCreateVertexLabel(stmt, labelString, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(labelString);
    i = 0;
    int F0Value = 0;
    int F1Value = 0;
    int F2Value = 0;
    char labelName[10];
    snprintf(labelName, 10, "T%d", maxLabelNum);
    for (i = 0; i < 200; i++) {
        ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        EXPECT_EQ(GMERR_OK, ret);
        F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        F2Value = i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F1Value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
#define CREATE_LABEL_NUM_FOR_EXPAND_MEM 32
GmcStmtT **CreateMultipleTable(GmcConnT *conn)
{
    uint32_t tableNum = CREATE_LABEL_NUM_FOR_EXPAND_MEM;
    GmcStmtT **stmtList = (GmcStmtT **)malloc(tableNum * sizeof(GmcStmtT *));
    for (uint32_t i = 0; i < tableNum; i++) {
        GmcAllocStmt(conn, &stmtList[i]);
        CreateSingleLabel(i, stmtList[i]);
    }
    return stmtList;
}

void ScanMultipleTable(GmcStmtT **stmtList)
{
    uint32_t tableNum = CREATE_LABEL_NUM_FOR_EXPAND_MEM;
    GmcStmtT *stmt;
    for (uint32_t i = 0; i < tableNum; i++) {
        char labelName[10];
        snprintf(labelName, 10, "T%d", i);
        stmt = stmtList[i];
        int32_t ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void DropMultipleTabel(GmcStmtT **stmtList)
{
    uint32_t tableNum = CREATE_LABEL_NUM_FOR_EXPAND_MEM;
    GmcStmtT *stmt;
    for (uint32_t i = 0; i < tableNum; i++) {
        char labelName[10];
        snprintf(labelName, 10, "T%d", i);
        stmt = stmtList[i];
        int ret = GmcDropVertexLabel(stmt, labelName);
        EXPECT_EQ(ret, GMERR_OK);
        GmcFreeStmt(stmt);
    }
    free(stmtList);
}

TEST_F(StClientVertex, testShmExpand)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    GmcStmtT **stmtList = CreateMultipleTable(conn);
    ScanMultipleTable(stmtList);
    DropMultipleTabel(stmtList);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcJsonDmlForip4route)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    status_t ret = GmcCreateVertexLabel(stmt, g_test_ip4route_status_255_json, g_sysModel_json_config);
    ret = GmcPrepareStmtByLabelName(stmt, g_ip4route_status_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, g_ip4route_status_255_json_data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_ip4route_status_label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "id>0");
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_FALSE(isFinish);

    if (!isFinish) {
        uint64_t id = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "id", &id, sizeof(uint64_t), &isNull);
        EXPECT_EQ(18446744072668362273ull, id);
    }

    ret = GmcDropVertexLabel(stmt, g_ip4route_status_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testCreateDupTableName)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // 1.1 创建vertex
    status_t ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 1.2 创建同名kv
    ret = GmcKvCreateTable(stmt, g_label_name, g_label_config);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    // 1.3 删表失败
    ret = GmcKvDropTable(stmt, g_label_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    // 1.4 删表成功
    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);

    // 2.1 创建kv
    ret = GmcKvCreateTable(stmt, g_label_name, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 2.2 创建vertex
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_label_config);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    // 2.3 删表失败
    ret = GmcDropVertexLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    // 2.4 删表成功
    ret = GmcKvDropTable(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testcataLog14)
{
    ASSERT_TRUE(CltCataIsEmpty());
}

TEST_F(StClientVertex, testUpdateUniqueNullableLocalhash)
{
    const char *g_label_name2 = "UpdateNonUniqueSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"int64", "nullable":true},
                    {"name":"F2", "type":"int32", "nullable":false},
                    { "name": "F4", "type": "uint32", "nullable":true },
                    { "name": "F5", "type": "uint32", "nullable":true },
                    { "name": "F6", "type": "uint32", "nullable":true },
                    { "name": "F7", "type": "fixed",  "size":8, "nullable":true },
                    { "name": "F8", "type": "string", "size":8, "nullable":true },
                    {"name":"F3", "type":"string","size":200, "nullable":false}],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1", "F4", "F5", "F6", "F7", "F8"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    long long F1Value = 1311;

    int F2Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    char *F3Value = (char *)"10.12.12.12";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 记录，第一个唯一localhash冲突，插入失败
    F0Value = (char *)"local";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 1313;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_DATA_EXCEPTION, ret);

    // insert vertex2
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    F0Value = (char *)"huawe";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F1Value = 1312;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 1313;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    F3Value = (char *)"10.12.12.13";
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update non unique index duplicate
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 1311;
    F3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // update non unique index
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    F1Value = 1313;
    F3Value = (char *)"10.12.12.14";
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_STRING, F3Value, strlen(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K2");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F4Value;
    uint32_t F5Value;
    uint32_t F6Value;
    char F7Value[4];
    char F8Value[4];

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_NULL, &F1Value, sizeof(long long));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_NULL, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_NULL, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_NULL, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_NULL, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_NULL, F8Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = (char *)"huawei";
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        long long F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(long long), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F4V;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &F4V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F6V;
        ret = GmcGetVertexPropertyByName(stmt, "F6", &F6V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F7V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F7", F7V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F8V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F8", F8V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testUniqueNullableHashCluster)
{
    const char *g_label_name2 = "UpdateNonUniqueSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":true},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    { "name": "F3", "type": "uint32", "nullable":true },
                    { "name": "F4", "type": "uint32", "nullable":true },
                    { "name": "F5", "type": "uint32", "nullable":true },
                    { "name": "F6", "type": "uint32", "nullable":true },
                    { "name": "F7", "type": "fixed",  "size":8, "nullable":true },
                    { "name": "F8", "type": "string", "size":8, "nullable":true },
                    { "name": "F9", "type": "uint32", "nullable":true }],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1", "F3", "F4", "F5", "F6", "F7", "F9"],
                        "index":{"type":"hashcluster"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F2Value = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 记录
    F0Value = (char *)"local";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F1Value = 0;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F3Value = 0;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F4Value = 0;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F5Value = 0;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F6Value = 0;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    char F7Value[8];
    memset_s(F7Value, 8, 0x00, 8);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);

    const char *F8Value = "ignored";
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_NULL, F8Value, 0);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F9Value = 0;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_UINT32, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_UINT32, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_FIXED, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_UINT32, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = (char *)"local";
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        uint32_t F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        uint32_t F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        uint32_t F3V;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F3Value, F3V);

        uint32_t F4V;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &F4V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F4Value, F4V);

        uint32_t F5V;
        ret = GmcGetVertexPropertyByName(stmt, "F5", &F5V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F5Value, F5V);

        uint32_t F6V;
        ret = GmcGetVertexPropertyByName(stmt, "F6", &F6V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F6Value, F6V);

        char F7V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F7", F7V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F7Value, F7V);

        char F8V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F8", F8V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F9V;
        ret = GmcGetVertexPropertyByName(stmt, "F9", &F9V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F9Value, F9V);
    }

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_NULL, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_NULL, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_NULL, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_NULL, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_NULL, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_NULL, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_NULL, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = (char *)"huawei";
    F2Value = 1;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        uint32_t F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        uint32_t F3V;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F4V;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &F4V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F5V;
        ret = GmcGetVertexPropertyByName(stmt, "F5", &F5V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F6V;
        ret = GmcGetVertexPropertyByName(stmt, "F6", &F6V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F7V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F7", F7V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F8V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F8", F8V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F9V;
        ret = GmcGetVertexPropertyByName(stmt, "F9", &F9V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testNonUniqueNullableLocal)
{
    const char *g_label_name2 = "UpdateNonUniqueSecondIndex";
    const char *test_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"UpdateNonUniqueSecondIndex",
            "fields":
                [
                    {"name":"F0", "type":"string","size":8, "nullable":false},
                    {"name":"F1", "type":"uint32", "nullable":true},
                    {"name":"F2", "type":"uint32", "nullable":false},
                    { "name": "F3", "type": "uint32", "nullable":true },
                    { "name": "F4", "type": "uint32", "nullable":true },
                    { "name": "F5", "type": "uint32", "nullable":true },
                    { "name": "F6", "type": "uint32", "nullable":true },
                    { "name": "F7", "type": "fixed",  "size":8, "nullable":true },
                    { "name": "F8", "type": "string", "size":8, "nullable":true },
                    { "name": "F9", "type": "uint32", "nullable":true }],
            "keys":
                [
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K1",
                        "fields":["F1", "F3", "F4", "F5", "F6", "F7", "F9"],
                        "index":{"type":"local"},
                        "constraints":{"unique":false}
                    },
                    {
                        "node":"UpdateNonUniqueSecondIndex",
                        "name":"UpdateNonUniqueSecondIndex_K2",
                        "fields":["F2"],
                        "index":{"type":"localhash"},
                        "constraints":{"unique":false}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    // test point: 创建vertex lable (带deltaS)
    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    char *F0Value = (char *)"huawei";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F2Value = 1;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 记录
    F0Value = (char *)"local";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F1Value = 0;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F3Value = 0;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F4Value = 0;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F5Value = 0;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F6Value = 0;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    char F7Value[8];
    memset_s(F7Value, 8, 0x00, 8);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_FIXED, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);

    char F8Value[3];
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_NULL, F8Value, 0);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t F9Value = 0;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_UINT32, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_UINT32, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_FIXED, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_UINT32, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    F0Value = (char *)"local";
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F0Value, F0V);

        uint32_t F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F1Value, F1V);

        uint32_t F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F2Value, F2V);

        uint32_t F3V;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F3Value, F3V);

        uint32_t F4V;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &F4V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F4Value, F4V);

        uint32_t F5V;
        ret = GmcGetVertexPropertyByName(stmt, "F5", &F5V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F5Value, F5V);

        uint32_t F6V;
        ret = GmcGetVertexPropertyByName(stmt, "F6", &F6V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F6Value, F6V);

        char F7V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F7", F7V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_STREQ(F7Value, F7V);

        char F8V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F8", F8V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F9V;
        ret = GmcGetVertexPropertyByName(stmt, "F9", &F9V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(F9Value, F9V);
    }

    // test point: 获取vertex lable，并设置值
    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert vertex1
    F0Value = (char *)"huawei2";
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_STRING, F0Value, strlen(F0Value));
    EXPECT_EQ(GMERR_OK, ret);

    F2Value = 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, g_label_name2, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_NULL, &F1Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_NULL, &F3Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_NULL, &F4Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_NULL, &F5Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_NULL, &F6Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_NULL, F7Value, 8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_NULL, &F9Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "UpdateNonUniqueSecondIndex_K1");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    char *F0Value1 = (char *)"huawei";
    uint32_t F2Value1 = 1;

    char *F0Value2 = (char *)"huawei2";
    uint32_t F2Value2 = 2;

    uint32_t i = 0;
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char F0V[500];
        bool isNull;
        ret = GmcGetVertexPropertyByName(stmt, "F0", F0V, 500, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        if (i == 0) {
            EXPECT_STREQ(F0Value1, F0V);
        } else {
            EXPECT_STREQ(F0Value2, F0V);
        }

        uint32_t F1V;
        ret = GmcGetVertexPropertyByName(stmt, "F1", &F1V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F2V;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &F2V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        if (i == 0) {
            EXPECT_EQ(F2Value1, F2V);
        } else {
            EXPECT_EQ(F2Value2, F2V);
        }

        uint32_t F3V;
        ret = GmcGetVertexPropertyByName(stmt, "F3", &F3V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F4V;
        ret = GmcGetVertexPropertyByName(stmt, "F4", &F4V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F5V;
        ret = GmcGetVertexPropertyByName(stmt, "F5", &F5V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F6V;
        ret = GmcGetVertexPropertyByName(stmt, "F6", &F6V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F7V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F7", F7V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        char F8V[8];
        ret = GmcGetVertexPropertyByName(stmt, "F8", F8V, 8, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);

        uint32_t F9V;
        ret = GmcGetVertexPropertyByName(stmt, "F9", &F9V, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, true);
        i++;
    }
    ret = GmcDropVertexLabel(stmt, g_label_name2);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testGmcGetLabelTypeByName)
{
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    auto ret = GmcCreateVertexLabel(syncStmt, test_normal_label_json, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    auto vertexLabelName = "T39";
    defer
    {
        GmcDropVertexLabel(syncStmt, vertexLabelName);
    };
    uint32_t labelType = -1;
    ret = GmcGetLabelTypeByName(syncStmt, vertexLabelName, &labelType);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_LABEL_TYPE_VERTEX, labelType);
    GmcVertexLabelTypeE vertexLabelType = GMC_VERTEX_TYPE_BUTT;
    EXPECT_EQ(GMERR_OK, GmcGetVertexLabelTypeByName(syncStmt, vertexLabelName, &vertexLabelType));
    EXPECT_EQ(GMC_VERTEX_TYPE_NORMAL, vertexLabelType);
    GmcDtlLabelTypeE dtlType = GMC_DTL_TYPE_BUTT;
    EXPECT_EQ(GMERR_DATA_EXCEPTION, GmcGetDatalogLabelTypeByName(syncStmt, vertexLabelName, &dtlType));

    auto kvTableName = "student";
    uint32_t labelType1 = -1;
    ret = GmcKvCreateTable(syncStmt, kvTableName, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetLabelTypeByName(syncStmt, kvTableName, &labelType1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMC_LABEL_TYPE_KV, labelType1);
    vertexLabelType = GMC_VERTEX_TYPE_BUTT;
    EXPECT_EQ(GMERR_DATA_EXCEPTION, GmcGetVertexLabelTypeByName(syncStmt, kvTableName, &vertexLabelType));
    dtlType = GMC_DTL_TYPE_BUTT;
    EXPECT_EQ(GMERR_DATA_EXCEPTION, GmcGetDatalogLabelTypeByName(syncStmt, kvTableName, &dtlType));
    defer
    {
        GmcKvDropTable(syncStmt, kvTableName);
    };

    // edgeLabel
    const char *edgeLabelName = "autoEdgelable";
    const char *labelJson =
        R"([{
            "name":"autoEdgelable",
            "source_vertex_label":"edgeAutoSrcVertex","comment":"the edge 7 to 8",
            "dest_vertex_label":"edgeAutoDstVertex",
            "constraint":
            {
                "operator_type":"and",
                "conditions":
                    [
                        {
                            "source_property": "F2",
                            "dest_property": "F2"
                        },
                        {
                            "source_property": "F3",
                            "dest_property": "F3"
                        }
                    ]
            }
        }])";

    // srcVertexLabel
    const char *edgeSrcVertexLabelName = "edgeAutoSrcVertex";
    const char *edgeSrcVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoSrcVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoSrcVertex",
                        "name":"edgeAutoSrcVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";
    // destVertexLabel
    const char *edgeDstVertexLabelName = "edgeAutoDstVertex";
    const char *edgeDstVertexLabel =
        R"([{
            "type":"record",
            "name":"edgeAutoDstVertex",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"int32", "nullable":false},
                    {"name":"F2", "type":"int32", "nullable":false},
                    {"name":"F3", "type":"int32", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"edgeAutoDstVertex",
                        "name":"edgeAutoDstVertex_PK0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    // 创建 src vertexLable
    const char *cfgJson = R"({"max_record_count":1000, "isFastReadUncommitted":false})";
    ret = GmcCreateVertexLabel(syncStmt, edgeSrcVertexLabel, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        GmcDropVertexLabel(syncStmt, edgeSrcVertexLabelName);
    };
    // 创建 dest vertexLable
    ret = GmcCreateVertexLabel(syncStmt, edgeDstVertexLabel, cfgJson);
    EXPECT_EQ(GMERR_OK, ret);
    defer
    {
        GmcDropVertexLabel(syncStmt, edgeDstVertexLabelName);
    };
    // 创建 edgeLable
    ret = GmcCreateEdgeLabel(syncStmt, labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // auto edgeLabelName = "edgeAutoDstVertex";
    defer
    {
        GmcDropEdgeLabel(syncStmt, edgeLabelName);
    };

    // 服务端只会搜索VertexLabel和KV表，因此返回表未定义
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(syncStmt, edgeLabelName, &labelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetVertexLabelTypeByName(syncStmt, edgeLabelName, &vertexLabelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetDatalogLabelTypeByName(syncStmt, edgeLabelName, &dtlType));

    // 传入不存在的表名
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(syncStmt, "testGmcGetLabelTypeByName", &labelType));

    // 视图不支持查询表类型
    const char *view_name = "V\\$STORAGE_MEMDATA_STAT";
    system("gmsysview -q V\\$STORAGE_MEMDATA_STAT");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetLabelTypeByName(syncStmt, view_name, &labelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetVertexLabelTypeByName(syncStmt, view_name, &vertexLabelType));
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, GmcGetDatalogLabelTypeByName(syncStmt, view_name, &dtlType));

    // 入参校验
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetLabelTypeByName(syncStmt, "", &labelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetLabelTypeByName(syncStmt, NULL, &labelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetLabelTypeByName(NULL, edgeLabelName, &labelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetLabelTypeByName(syncStmt, edgeLabelName, NULL));

    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetVertexLabelTypeByName(NULL, vertexLabelName, &vertexLabelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetVertexLabelTypeByName(syncStmt, "", &vertexLabelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetVertexLabelTypeByName(syncStmt, NULL, &vertexLabelType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetVertexLabelTypeByName(syncStmt, vertexLabelName, NULL));

    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetDatalogLabelTypeByName(NULL, vertexLabelName, &dtlType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetDatalogLabelTypeByName(syncStmt, "", &dtlType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetDatalogLabelTypeByName(syncStmt, NULL, &dtlType));
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, GmcGetDatalogLabelTypeByName(syncStmt, vertexLabelName, NULL));
}

TEST_F(StClientVertex, testGmcGetLabelEstiMemInfo)
{
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"T39",
            "max_record_count": 1000,
            "fields":
                [
                    {"name":"F0", "type":"char","default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar","default":"f", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    GmcMemSizeInfoT memSizeInfoT = {0};
    auto ret = GmcGetLabelEstiMemInfo(syncStmt, test_normal_label_json, g_label_config, &memSizeInfoT);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetLabelEstiMemInfo(syncStmt, test_normal_label_json, NULL, &memSizeInfoT);
    EXPECT_EQ(GMERR_OK, ret);
}

// 设置顶点普通属性 (除主键外的其它属性)
int GtSetNormalVertexProperty(GmcStmtT *stmt, int32_t F1Val, uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    int ret;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Val, sizeof(F1Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Val, sizeof(F2Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Val, sizeof(F4Val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT32, &F4Val, sizeof(F4Val));
    EXPECT_EQ(GMERR_OK, ret);
    return ret;
}

int GtSetInsertVertexProperyty(
    GmcStmtT *stmt, int32_t F0Val, int32_t F1Val, uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    // 设置主键属性
    int ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Val, sizeof(F0Val));
    EXPECT_EQ(GMERR_OK, ret);

    return GtSetNormalVertexProperty(stmt, F1Val, F2Val, F3Val, F4Val);
}

int GtCheckAffectRows(GmcStmtT *stmt, int32_t expect)
{
    int32_t affect;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affect, sizeof(affect));
    return ret;
}

// 设置用于更新的顶点属性
int GtSetUpdateVertexProperyty(GmcStmtT *stmt, int32_t F1Val, uint32_t F2Val, uint32_t F3Val, uint32_t F4Val)
{
    return GtSetNormalVertexProperty(stmt, F1Val, F2Val, F3Val, F4Val);
}

#ifndef ASAN
static const char *gLabelName = "VertexLabel";
static const char *gLabelConfigJson = R"({"max_record_num":100000})";
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";

TEST_F(StClientVertex, slowoplog1)
{
    int ret;
    int32_t startPrimaryKey = 10;
    int32_t properyVal = 8;
    int32_t insertNum = 1000;

    do {
        ret = GmcCreateVertexLabel(syncStmt, gLabelSchemaJson, gLabelConfigJson);
        EXPECT_EQ(GMERR_OK, ret);

        // 写入数据
        for (int i = startPrimaryKey; i < startPrimaryKey + insertNum; i++) {
            ret = GmcPrepareStmtByLabelName(syncStmt, gLabelName, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GtSetInsertVertexProperyty(syncStmt, i, properyVal, properyVal, properyVal, properyVal);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(syncStmt);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GtCheckAffectRows(syncStmt, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(GMERR_OK, ret);

        // 通过 hashcluster key 更新数据
        ret = GmcPrepareStmtByLabelName(syncStmt, gLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t updateVal = 128;
        ret = GtSetUpdateVertexProperyty(syncStmt, updateVal, updateVal, updateVal, updateVal);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t keyVal = properyVal;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &keyVal, sizeof(keyVal));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "HashClusterKey");
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t starttime = DbRdtsc();
        ret = GmcExecute(syncStmt);
        uint64_t endtime = DbRdtsc();
        printf("execute time %" PRIu64 "\n", DbToUseconds(endtime - starttime));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtCheckAffectRows(syncStmt, insertNum);
        EXPECT_EQ(GMERR_OK, ret);

        // 打开日志开关
        syncConn->cltOpSegTime.timeThreshold = 0;
        printf("open the time cost log threshold");

        // 通过 hashcluster key 更新数据
        ret = GmcPrepareStmtByLabelName(syncStmt, gLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t updateVal1 = 129;
        ret = GtSetUpdateVertexProperyty(syncStmt, updateVal1, updateVal1, updateVal1, updateVal1);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t keyVal1 = properyVal;
        ret = GmcSetIndexKeyValue(syncStmt, 0, GMC_DATATYPE_UINT32, &keyVal1, sizeof(keyVal1));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(syncStmt, "HashClusterKey");
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t starttime1 = DbRdtsc();
        ret = GmcExecute(syncStmt);
        uint64_t endtime1 = DbRdtsc();
        printf("execute time %" PRIu64 "\n", DbToUseconds(endtime1 - starttime1));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GtCheckAffectRows(syncStmt, insertNum);
        EXPECT_EQ(GMERR_OK, ret);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcDropVertexLabel(syncStmt, gLabelName);
}
#endif

TEST_F(StClientVertex, slowoplog2)
{
    // 请移动到UT
    GmcConnOptionsT options = {};

    int ret = GmcConnOptionsSetSlowOpLogThreshold(&options, -2);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcConnOptionsSetSlowOpLogThreshold(&options, 100);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetSlowOpLogThreshold(&options, DB_MAX_INT64);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcConnOptionsSetSlowOpLogThreshold(&options, DB_MAX_INT64 + 1ull);
    EXPECT_EQ(GMERR_INVALID_PARAMETER_VALUE, ret);

    ret = GmcConnOptionsSetSlowOpLogThreshold(&options, 0);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcConnOptionsSetSlowOpLogThreshold(&options, -1);
    EXPECT_EQ(GMERR_OK, ret);
}

/**
 * 幂等创建点标签
 */
TEST_F(StClientVertex, testCreateVertexLabelIdempotent)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    /**
     * 正常创建VertexLabel
     */
    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"VertexLabel1",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel2",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel3",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel4",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel5",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel6",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            }
    ])";
    Status ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);

    /*
     * 幂等创建VertexLabel
     */
    const char *testIdempotentLabelJson =
        R"([{
            "type":"record",
            "name":"VertexLabel1",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel2",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel3",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel4",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            },
            {
            "type":"record",
            "name":"VertexLabel5",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ]
            }
            ])";

    ret = GmcCreateVertexLabel(stmt, testIdempotentLabelJson, g_label_config);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);

    const char *vertexLabelName = "VertexLabel1";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    vertexLabelName = "VertexLabel2";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    vertexLabelName = "VertexLabel3";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    vertexLabelName = "VertexLabel4";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    vertexLabelName = "VertexLabel5";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    vertexLabelName = "VertexLabel6";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    DestroyConnectionAndStmt(conn, stmt);
}

/**
 * 创建点标签中属性名称填写错误
 */
TEST_F(StClientVertex, testCreateLabelPropertySameNameErr)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *testNormalLabelJson =
        R"([{
            "type":"record",
            "name":"T39",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T39",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T40",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T40",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T41",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T41",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T42",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T42",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T43",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T43",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T44",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T44",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            },
            {
            "type":"record",
            "name":"T45",
            "fields":
                [
                    {"name":"F0", "type":"char", "default":"a", "nullable":false},
                    {"name":"F1", "type":"uchar", "default":"f", "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false},
                    {"name":"F3", "type":"int8", "default":2, "nullable":false},
                    {"name":"F2", "type":"int8", "default":2, "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"T45",
                        "name":"T39_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
    }])";
    status_t ret = GmcCreateVertexLabel(stmt, testNormalLabelJson, g_label_config);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_label_config_dlr = R"(
    {
        "data_sync_label":true,
        "max_record_count":1000,
        "auto_increment":100
    })";
TEST_F(StClientVertex, testDlrCreateLabel)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testDlrCreateLabel",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testDlrCreateLabel",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config_dlr);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDlrCreateLabel2)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testDlrCreateLabel2",
            "fields":
                [
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testDlrCreateLabel2",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config_dlr);
    EXPECT_EQ(GMERR_OK, ret);
    const char *vertexLabelName = "testDlrCreateLabel2";
    ret = GmcDropVertexLabel(stmt, vertexLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_label_config_dlr1 = R"(
    {
        "data_sync_label":"true",
        "max_record_count":1000
    })";

TEST_F(StClientVertex, testDlrCreateLabel3)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testDlrCreateLabel3",
            "fields":
                [
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testDlrCreateLabel3",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config_dlr1);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const char *g_label_kv_config = R"(
    {
        "data_sync_label":true,
        "max_record_count":1000
    })";

TEST_F(StClientVertex, testDlrCreateLabel4)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    static const char *g_kvTableName = "student";

    Status ret = GmcKvCreateTable(stmt, g_kvTableName, g_label_kv_config);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDlrCreateLabel5)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testDlrCreateLabel5",
            "fields":
                [
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testDlrCreateLabel5",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config_dlr);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDlrCreateLabel6)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([
        {
            "type":"record",
            "name":"Con_Con_root",
            "fields":[
                {"name":"ID", "type":"uint32", "nullable":false},
                {"name":"F0", "type":"uint32", "nullable":false},
                {"name":"F1", "type":"uint32", "nullable":true}
            ],
            "keys":[
                {
                    "node":"Con_Con_root",
                    "name":"PK",
                    "fields":["ID"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        },

        {
            "type":"record",
            "name":"Con_Con_Child_01",
            "fields":[
                {"name":"ID", "type":"uint32", "nullable":false, "auto_increment":true},
                {"name":"PID", "type":"uint32", "nullable":false},
                {"name":"F0", "type":"uint32", "nullable":false}
            ],
            "keys":[
                {
                    "node":"Con_Con_Child_01",
                    "name":"PK",
                    "fields":["PID"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }
    ]
    )";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config_dlr);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, testDlrCreateLabel7)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *labelName = "testDlrCreateLabel7";

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"testDlrCreateLabel7",
            "fields":
                [
                    {"name":"F0", "type":"uint32"},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"testDlrCreateLabel7",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, g_label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLAY);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

// 表名长度513建表报错
TEST_F(StClientVertex, testCreateVertexLabelByLongName)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *vertexLabel =
        R"(
        [{
    "name" : "labellllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllll",
    "type" : "record",
    "version" : "2.0",
    "fields" : [
        {"name" : "F0", "type" : "int32"},
        {"name" : "F1", "type" : "int32"}
    ],
    "keys" : [
        {
        "fields" : [ "F0" ],
        "index" : {
            "type" : "primary"
        },
        "name" : "pk",
        "node" : "labellllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll)"
        R"(llllllllllllllllllllllllllllllll",
        "constraints" : {"unique" : true}
        }]
    }]
    )";
    status_t ret = GmcCreateVertexLabel(stmt, vertexLabel, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = GmcDropVertexLabel(stmt,
        "labellllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll"
        "llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll"
        "llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll"
        "llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll"
        "llllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllllll"
        "llllllllllllllllllllllllllllllll");
    EXPECT_EQ(GMERR_INVALID_VALUE, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

static const char *gLabelConfigClusterhash = R"(
    {
        "support_undetermined_length":false,
        "max_record_count":1000,
        "auto_increment":100
    })";

TEST_F(StClientVertex, testClusterHash)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"enable_clusterhash",
            "fields":
                [
                    {"name":"F0", "type":"uint32", "auto_increment":true},
                    {"name":"F1", "type":"char", "default":"a", "nullable":false}
                ],
            "keys":
                [
                    {
                        "node":"enable_clusterhash",
                        "name":"T39_K0",
                        "fields":["F1"],
                        "index":{"type":"primary"},
                        "constraints":{ "unique":true}
                    }
                ]
            }])";
    status_t ret = GmcCreateVertexLabel(stmt, test_normal_label_json, gLabelConfigClusterhash);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, TestMergeUpdateSetPKProperty)
{
    const char *label_name = "Vlabel";
    const char *test_delat_config_json = R"({"max_record_count":1000})";
    const char *test_normal_label_json =
        R"([{
            "type":"record",
            "name":"Vlabel",
            "fields":
                [
                    {"name":"F0", "type":"int32"},
                    {"name":"F1", "type":"int32"},
                    {"name":"F2", "type":"int32","nullable":true},
                    {"name":"F3", "type":"int32","nullable":true}],
            "keys":
                [
                    {
                        "node":"Vlabel",
                        "name":"Vlabel_K0",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    int ret = GmcCreateVertexLabel(stmt, test_normal_label_json, test_delat_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert 插入值
    int F0Value, F1Value, F2Value, F3Value;
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    F0Value = 0;
    F1Value = 1;
    F2Value = 2;
    F3Value = 3;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    int value, pkValue;
    bool isNull;

    // update vertex
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);
    value = 10;
    pkValue = 0;
    ret = GmcSetIndexKeyName(stmt, "Vlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点：设置f0失败
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int affectRows;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(1, affectRows);

    // qry vertex: (0, 10, 10, 3)
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(unsigned int));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "Vlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0, value);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(10, value);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(10, value);

        ret = GmcGetVertexPropertyByName(stmt, "F3", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(3, value);
    }

    // merge vertex
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    value = 20;
    pkValue = 0;
    ret = GmcSetIndexKeyName(stmt, "Vlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(value));
    EXPECT_EQ(GMERR_OK, ret);

    // 测试点：设置f0失败
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    EXPECT_EQ(GMERR_INVALID_OBJECT, ret);

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // qry vertex (0, 20, 10, 3)
    ret = GmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(unsigned int));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "Vlabel_K0");
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }

        ret = GmcGetVertexPropertyByName(stmt, "F0", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(0, value);

        ret = GmcGetVertexPropertyByName(stmt, "F1", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(20, value);

        ret = GmcGetVertexPropertyByName(stmt, "F2", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(10, value);

        ret = GmcGetVertexPropertyByName(stmt, "F3", &value, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_FALSE(isNull);
        EXPECT_EQ(3, value);
    }

    ret = GmcDropVertexLabel(stmt, label_name);
    EXPECT_EQ(GMERR_OK, ret);
    DestroyConnectionAndStmt(conn, stmt);
}

class StClientVertexEnableCataLimit : public StClient {
public:
    static void SetUpTestCase()
    {
        StartDbServerWithConfig("\"udfEnable=1\" \"enableDmlPerfStat=1\" \"auditLogEnableDML=0\" "
                                "\"maxNormalTableNum=10000\" ");
        uint32_t cataNum = 5;
        GmcSetCltCfg("clientCataLimitEnable", GMC_DATATYPE_INT32, &cataNum, sizeof(int32_t));
        st_clt_init();
        CreateAndStartEpoll(&responseEpollThreadId, &responseEpollFd);
        CreateAndStartEpoll(&timeoutEpollThreadId, &timeoutEpollFd);
        EXPECT_EQ(GMERR_OK, GmcRegTimeoutEpollFunc(TimeoutEpollReg));
        // 此处心跳定时器与TimeOut定时器使用同一个Epoll，可设置为不同Epoll
        EXPECT_EQ(GMERR_OK, GmcRegHeartBeatEpollFunc(TimeoutEpollReg));
        if (IsEulerEnv()) {
            DbSleep(1000);
        } else {
            st_check_hpe_server_running();
        }
        printf("start response epoll and timeout epoll thread\n");
        printf("response epoll fd: %d, timeout epoll fd: %d\n", responseEpollFd, timeoutEpollFd);
    }
};

#include "clt_heartbeat.h"
// DTS2023082604711补充用例
TEST_F(StClientVertexEnableCataLimit, CreateAndPrepareWithCataLimit)
{
    uint32_t cataNum = 5;
    GmcSetCltCfg("clientCataLimitEnable", GMC_DATATYPE_INT32, &cataNum, sizeof(int32_t));
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *vConfig = "{\"max_record_num\":1000,\"isFastReadUncommitted\":0,\"auto_increment\":10}";
    int commandSize = 1024;
    char normalSchemas[commandSize] = {0};
    int nameSize = 128;
    char name[nameSize] = {0};
    snprintf(normalSchemas, commandSize,
        "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
        "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
        "\"type\":\"uint32\"}],"
        "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        1, 1);
    snprintf(name, nameSize, "Normal%d", 1);
    EXPECT_EQ(GMERR_OK, GmcCreateVertexLabel(stmt, normalSchemas, vConfig));

    snprintf(name, nameSize, "Normal%d", 1);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, name, GMC_OPERATION_SCAN));

    CltCataRemoveAllDeletedLabels();
    snprintf(name, nameSize, "Normal%d", 1);
    EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, name));

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertexEnableCataLimit, CreateAndPrepareWithCataLimitThreshold)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);
    const char *vConfig = "{\"max_record_num\":1000,\"isFastReadUncommitted\":0,\"auto_increment\":10}";
    int commandSize = 1024;
    char normalSchemas[commandSize] = {0};
    int nameSize = 128;
    char name[nameSize] = {0};
    snprintf(normalSchemas, commandSize,
        "[{\"type\":\"record\", \"name\":\"Normal%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"uint32\"},"
        "{\"name\":\"F1\", \"type\":\"uint32\"},{\"name\":\"F2\", \"type\":\"uint32\"},{\"name\":\"F3\", "
        "\"type\":\"uint32\"}],"
        "\"keys\":[{\"node\":\"Normal%d\", \"name\":\"Normal_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        1, 1);
    for (int i = 0; i < 10; i++) {
        snprintf(name, nameSize, "Normal%d", i);
        EXPECT_EQ(GMERR_OK, GmcCreateVertexLabelWithName(stmt, normalSchemas, vConfig, name));

        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, name, GMC_OPERATION_SCAN));
    }

    for (int i = 0; i < 10; i++) {
        EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, name, GMC_OPERATION_SCAN));
    }

    CltCataRemoveAllDeletedLabels();

    GmcClearCache(stmt, NULL, 0, GMC_VERTEX_LABEL_TYPE);

    for (int i = 0; i < 10; i++) {
        snprintf(name, nameSize, "Normal%d", i);
        EXPECT_EQ(GMERR_OK, GmcDropVertexLabel(stmt, name));
    }

    DestroyConnectionAndStmt(conn, stmt);
}

TEST_F(StClientVertex, CreateYangTableWithoutYangFeature)
{
    GmcConnT *conn;
    GmcStmtT *stmt;
    CreateSyncConnectionAndStmt(&conn, &stmt);

    const char *labelJson = R"([
    {
        "type": "container",
        "name": "table7594",
        "fields": [
            {
                "name": "F0",
                "type": "int32",
                "nullable": false
            }
        ],
        "keys": [
            {
                "node": "table7594",
                "name": "table7594.F0",
                "fields": [
                    "F0"
                ],
                "index": {
                    "type": "primary"
                },
                "constraints": {
                    "unique": true
                }
            }
        ]
    }])";
    const char *cfgJson = R"({"max_record_count":1000, "auto_increment": 1, "isFastReadUncommitted": 0})";
    Status ret = GmcCreateVertexLabel(stmt, labelJson, cfgJson);
#ifdef FEATURE_YANG_CLI
    EXPECT_EQ(ret, GMERR_OK);
    ret = GmcDropVertexLabel(stmt, "table7594");
    EXPECT_EQ(ret, GMERR_OK);
#else
    EXPECT_EQ(ret, GMERR_FEATURE_NOT_SUPPORTED);
#endif
    DestroyConnectionAndStmt(conn, stmt);
}
