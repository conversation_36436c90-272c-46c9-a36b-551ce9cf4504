[{"name": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "tunnel-selector_PK", "node": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "conditions", "type": "container", "fields": [{"name": "match-rd-filter", "type": "container", "fields": [{"name": "rd-filter-ref", "type": "string"}]}, {"name": "match-community-filters", "type": "container", "fields": []}, {"name": "match-ipv4-destination", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ipv4-nexthop", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-nexthop-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ipv6-nexthop", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-nexthop-ipv6-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv6-prefix"}]}, {"name": "match-ipv4-route-source", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-route-source-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}]}, {"name": "actions", "type": "container", "fields": [{"name": "apply-tunnel-policy", "type": "container", "fields": [{"name": "tunnel-policy-ref", "type": "string"}]}, {"name": "apply-segment-routing-ipv6", "type": "container", "fields": [{"name": "segment-routing-ipv6-type", "type": "string"}]}]}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector::nodes::node::conditions::match-community-filters::match-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-filter-ref", "type": "string"}, {"name": "match-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "whole-match", "type": "string"}], "type": "case", "name": "whole-match"}, {"fields": [{"name": "sort-match", "type": "string"}], "type": "case", "name": "sort-match"}]}], "keys": [{"name": "match-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::tunnel-selectors::tunnel-selector::nodes::node::conditions::match-community-filters::match-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "community-filter-ref"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "address-family-mismatch-deny", "type": "string", "default": "false"}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "policy-definition_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "description", "type": "string"}, {"name": "match-count", "type": "uint32", "is_config": false}, {"name": "conditions", "type": "container", "fields": [{"name": "match-tag", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}, {"name": "match-ext-community-soo", "type": "container", "fields": [{"name": "ext-community-soo-filter-ref", "type": "string"}]}, {"name": "match-ext-community-filters", "type": "container", "fields": []}, {"name": "match-community-filters", "type": "container", "fields": []}, {"name": "match-as-path-filters", "type": "container", "fields": []}, {"name": "match-protocols", "type": "container", "fields": []}, {"name": "match-cost", "type": "container", "fields": [{"name": "cost-set", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "value", "type": "uint32"}], "type": "case", "name": "value"}, {"fields": [{"name": "lower", "type": "uint32"}, {"name": "upper", "type": "uint32"}], "type": "case", "name": "range"}]}]}, {"name": "match-preference", "type": "container", "fields": [{"name": "value", "type": "uint8"}]}, {"name": "match-ext-community-segmented-nexthop-filter", "type": "container", "fields": [{"name": "ext-community-segmented-nexthop-filter-ref", "type": "string"}]}, {"name": "match-interfaces", "type": "container", "fields": []}, {"name": "match-origin-as-validation", "type": "container", "fields": [{"name": "origin-as-validation", "type": "string"}]}, {"name": "match-route-types", "type": "container", "fields": []}, {"name": "match-mpls-label", "type": "container", "fields": [{"name": "mpls-label", "type": "string"}, {"name": "mpls-label2", "type": "string"}]}, {"name": "match-rd-filter", "type": "container", "fields": [{"name": "rd-filter-ref", "type": "string"}]}, {"name": "match-mac-filter", "type": "container", "fields": [{"name": "mac-filter-ref", "type": "string"}]}, {"name": "match-l3vni-filter", "type": "container", "fields": [{"name": "match-type", "type": "string"}, {"name": "l3vni-filter-ref", "type": "string"}]}, {"name": "match-l2vni-filter", "type": "container", "fields": [{"name": "match-type", "type": "string"}, {"name": "l2vni-filter-ref", "type": "string"}]}, {"name": "match-eth-tag-filter", "type": "container", "fields": [{"name": "eth-tag-filter-ref", "type": "string"}]}, {"name": "match-large-community-filters", "type": "container", "fields": []}, {"name": "match-as-path-length", "type": "container", "fields": [{"name": "as-path-length-set", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "value", "type": "uint32"}], "type": "case", "name": "value"}, {"fields": [{"name": "lower", "type": "uint32"}, {"name": "upper", "type": "uint32"}], "type": "case", "name": "range"}]}]}, {"name": "match-prefix-mod", "type": "container", "fields": [{"name": "mod-value", "type": "uint8"}, {"name": "mod-result", "type": "uint8"}]}, {"name": "match-ext-community-encapsulation-filter", "type": "container", "fields": [{"name": "ext-community-encapsulation-list-ref", "type": "string"}]}, {"name": "match-ipv4-destination", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ipv6-destination", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-ipv6-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv6-prefix"}]}, {"name": "match-ipv4-nexthop", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-nexthop-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ipv6-nexthop", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-nexthop-ipv6-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv6-prefix"}]}, {"name": "match-ipv4-route-source", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-route-source-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ipv6-route-source", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-route-source-ipv6-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv6-prefix"}]}, {"name": "match-route-originator", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-originator-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-group-address", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "match-group-address-ipv4-prefix", "type": "container", "fields": [{"name": "prefix-filter-ref", "type": "string"}]}], "type": "case", "name": "ipv4-prefix"}]}, {"name": "match-ext-community-priority-color", "type": "container", "fields": [{"name": "not-match-priority", "type": "string", "default": "false"}, {"name": "ext-community-priority-color-filter-ref", "type": "string"}]}, {"name": "match-ext-community-bandwidth", "type": "container", "fields": [{"name": "ext-community-bandwidth-filter-ref", "type": "string"}]}]}, {"name": "actions", "type": "container", "fields": [{"name": "apply-local-preference", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "value", "type": "uint32"}]}, {"name": "apply-cost", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "value", "type": "uint32"}]}, {"name": "apply-community", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "set-community", "type": "choice", "nullable": true, "fields": [{"fields": [], "type": "case", "name": "inline"}, {"fields": [{"name": "value", "type": "string"}], "type": "case", "name": "inline-string"}, {"fields": [{"name": "community-list-ref", "type": "string"}], "type": "case", "name": "reference"}]}]}, {"name": "apply-ext-community-soo", "type": "container", "fields": [{"name": "operation", "type": "string"}]}, {"name": "apply-mpls-label", "type": "container", "fields": [{"name": "mpls-label", "type": "string"}]}, {"name": "apply-preferred", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}, {"name": "apply-aigp", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "value", "type": "uint32"}]}, {"name": "apply-tag", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}, {"name": "apply-ipv4-nexthop", "type": "container", "fields": [{"name": "ipv4-nexthop-set", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "nexthop", "type": "string"}], "type": "case", "name": "nexthop"}, {"fields": [{"name": "peer-address", "type": "string"}], "type": "case", "name": "peer-address"}, {"fields": [{"name": "blackhole", "type": "string"}], "type": "case", "name": "blackhole"}]}]}, {"name": "apply-ipv6-nexthop", "type": "container", "fields": [{"name": "ipv6-nexthop-set", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "ipv6-nexthop", "type": "string"}], "type": "case", "name": "ipv6-nexthop"}, {"fields": [{"name": "peer-address", "type": "string"}], "type": "case", "name": "peer-address"}, {"fields": [{"name": "blackhole", "type": "string"}], "type": "case", "name": "blackhole"}]}]}, {"name": "apply-cost-type", "type": "container", "fields": [{"name": "value", "type": "string"}]}, {"name": "apply-origin", "type": "container", "fields": [{"name": "origin-type", "type": "string"}, {"name": "value", "type": "string"}]}, {"name": "apply-preference", "type": "container", "fields": [{"name": "value", "type": "uint8"}]}, {"name": "apply-route-type", "type": "container", "fields": [{"name": "value", "type": "string"}]}, {"name": "apply-traffic", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}, {"name": "apply-priority", "type": "container", "fields": [{"name": "value", "type": "uint16"}]}, {"name": "apply-ipv4-gateway", "type": "container", "fields": [{"name": "gateway-type", "type": "string"}, {"name": "address", "type": "string"}]}, {"name": "apply-ipv6-gateway", "type": "container", "fields": [{"name": "gateway-type", "type": "string"}, {"name": "address", "type": "string"}]}, {"name": "apply-ext-community", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "ext-community-members", "type": "container", "fields": []}]}, {"name": "apply-community-filter-delete", "type": "container", "fields": [{"name": "name", "type": "string"}]}, {"name": "apply-dampening", "type": "container", "fields": [{"name": "half-life-value", "type": "uint32"}, {"name": "reuse-value", "type": "uint32"}, {"name": "suppress-value", "type": "uint32"}, {"name": "ceiling-value", "type": "uint32"}]}, {"name": "apply-qos-parameter", "type": "container", "fields": [{"name": "set-qos-parameter", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "qos-local-id", "type": "uint32"}, {"name": "ip-precedence", "type": "uint32"}], "type": "case", "name": "local-id-ip-precedence"}]}]}, {"name": "apply-as-path", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "as-path-string", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "value", "type": "string"}], "type": "case", "name": "as-string"}]}]}, {"name": "apply-ext-community-redirect-ip", "type": "container", "fields": [{"name": "value", "type": "string"}]}, {"name": "apply-ext-community-redirect-vpn", "type": "container", "fields": [{"name": "value", "type": "string"}]}, {"name": "apply-ext-community-color", "type": "container", "fields": [{"name": "value", "type": "string"}]}, {"name": "apply-entropy-label-none", "type": "container", "fields": [{"name": "entropy-label-none", "type": "string"}]}, {"name": "apply-peer-id", "type": "container", "fields": [{"name": "value", "type": "uint16"}]}, {"name": "apply-large-community", "type": "container", "fields": [{"name": "operation", "type": "string"}, {"name": "set-large-community", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "large-community-members", "type": "container", "fields": []}], "type": "case", "name": "inline"}, {"fields": [{"name": "large-community-list-ref", "type": "string"}], "type": "case", "name": "reference"}]}]}, {"name": "apply-ext-community-priority-color", "type": "container", "fields": [{"name": "operation", "type": "string"}]}, {"name": "apply-vn-id", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}, {"name": "apply-ext-community-bandwidth", "type": "container", "fields": [{"name": "link-bandwidth-mode", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "operation", "type": "string"}, {"name": "value", "type": "string"}], "type": "case", "name": "set-bandwidth"}, {"fields": [{"name": "enable", "type": "string"}, {"name": "limit-bandwidth", "type": "uint32"}], "type": "case", "name": "aggregate-bandwidth"}]}]}, {"name": "apply-flex-algo-id", "type": "container", "fields": [{"name": "value", "type": "uint32"}]}]}, {"name": "next-node-choice", "type": "container", "fields": [{"name": "is-goto-next-node", "type": "string", "default": "false"}, {"name": "next-node-sequence", "type": "int32"}]}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community-priority-color::communities", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "communities", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community-priority-color::communities", "name": "communities_PK", "fields": ["PID", "communities"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-large-community::set-large-community::inline::large-community-members::large-community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-large-community::set-large-community::inline::large-community-members::large-community-member", "name": "large-community-member_PK", "fields": ["PID", "value"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community::ext-community-members::ext-community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "ext-community-member_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community::ext-community-members::ext-community-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community-soo::communities", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "communities", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-ext-community-soo::communities", "name": "communities_PK", "fields": ["PID", "communities"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-community::set-community::inline::communities", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "communities", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::actions::apply-community::set-community::inline::communities", "name": "communities_PK", "fields": ["PID", "communities"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-large-community-filters::match-large-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-filter-ref", "type": "string"}, {"name": "match-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "whole-match", "type": "string"}], "type": "case", "name": "whole-match"}]}], "keys": [{"name": "match-large-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-large-community-filters::match-large-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "community-filter-ref"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-route-types::match-route-type", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "route-type", "type": "string"}], "keys": [{"name": "match-route-type_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-route-types::match-route-type", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "route-type"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-interfaces::match-interface", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "interface-name", "type": "string"}], "keys": [{"name": "match-interface_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-interfaces::match-interface", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "interface-name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-protocols::match-protocol", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "protocol", "type": "string"}], "keys": [{"name": "match-protocol_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-protocols::match-protocol", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "protocol"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-as-path-filters::match-as-path-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "filter-name", "type": "string"}], "keys": [{"name": "match-as-path-filter_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-as-path-filters::match-as-path-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "filter-name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-community-filters::match-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-filter-ref", "type": "string"}, {"name": "match-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "whole-match", "type": "string"}], "type": "case", "name": "whole-match"}, {"fields": [{"name": "sort-match", "type": "string"}], "type": "case", "name": "sort-match"}]}], "keys": [{"name": "match-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-community-filters::match-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "community-filter-ref"]}]}, {"name": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-ext-community-filters::match-ext-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "ext-community-filter-ref", "type": "string"}], "keys": [{"name": "match-ext-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::policy-definitions::policy-definition::nodes::node::conditions::match-ext-community-filters::match-ext-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "ext-community-filter-ref"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "basic-nodes", "type": "container", "fields": []}], "type": "case", "name": "basic"}, {"fields": [{"name": "advanced-nodes", "type": "container", "fields": []}], "type": "case", "name": "advanced"}]}], "keys": [{"name": "ext-community-encapsulation-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::advanced::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::advanced::advanced-nodes::advanced-node", "name": "advanced-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::basic::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::basic::basic-nodes::basic-node", "name": "basic-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::basic::basic-nodes::basic-node::community-member", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-member", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-encapsulation-filters::ext-community-encapsulation-filter::filter-type::basic::basic-nodes::basic-node::community-member", "name": "community-member_PK", "fields": ["PID", "community-member"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "basic-nodes", "type": "container", "fields": []}], "type": "case", "name": "basic"}, {"fields": [{"name": "advanced-nodes", "type": "container", "fields": []}], "type": "case", "name": "advanced"}]}], "keys": [{"name": "ext-community-bandwidth-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::advanced::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::advanced::advanced-nodes::advanced-node", "name": "advanced-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::basic::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::basic::basic-nodes::basic-node", "name": "basic-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::basic::basic-nodes::basic-node::community-member", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-member", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-bandwidth-filters::ext-community-bandwidth-filter::filter-type::basic::basic-nodes::basic-node::community-member", "name": "community-member_PK", "fields": ["PID", "community-member"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "choice", "nullable": true, "fields": [{"fields": [{"name": "basic-nodes", "type": "container", "fields": []}], "type": "case", "name": "basic"}, {"fields": [{"name": "advanced-nodes", "type": "container", "fields": []}], "type": "case", "name": "advanced"}]}], "keys": [{"name": "ext-community-priority-color-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::advanced::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::advanced::advanced-nodes::advanced-node", "name": "advanced-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::basic::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::basic::basic-nodes::basic-node", "name": "basic-node_PK", "fields": ["PID", "sequence"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::basic::basic-nodes::basic-node::community-member", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-member", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-priority-color-filters::ext-community-priority-color-filter::filter-type::basic::basic-nodes::basic-node::community-member", "name": "community-member_PK", "fields": ["PID", "community-member"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-lists::large-community-list", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "large-community-members", "type": "container", "fields": []}], "keys": [{"name": "large-community-list_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-lists::large-community-list", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-lists::large-community-list::large-community-members::large-community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "large-community-member_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-lists::large-community-list::large-community-members::large-community-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "string"}, {"name": "basic-nodes", "type": "container", "fields": []}, {"name": "advanced-nodes", "type": "container", "fields": []}], "keys": [{"name": "large-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "advanced-node_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::advanced-nodes::advanced-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "large-community-members", "type": "container", "fields": []}], "keys": [{"name": "basic-node_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::basic-nodes::basic-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::basic-nodes::basic-node::large-community-members::large-community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "large-community-member_PK", "node": "root::huawei-routing-policy:routing-policy::large-community-filters::large-community-filter::basic-nodes::basic-node::large-community-members::large-community-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv6-prefix-lists::ipv6-prefix-list", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "prefix-members", "type": "container", "fields": []}], "keys": [{"name": "ipv6-prefix-list_PK", "node": "root::huawei-routing-policy:routing-policy::ipv6-prefix-lists::ipv6-prefix-list", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv6-prefix-lists::ipv6-prefix-list::prefix-members::prefix-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "ipv6-address", "type": "string"}, {"name": "masklength", "type": "uint8"}], "keys": [{"name": "prefix-member_PK", "node": "root::huawei-routing-policy:routing-policy::ipv6-prefix-lists::ipv6-prefix-list::prefix-members::prefix-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "ipv6-address", "masklength"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv4-prefix-lists::ipv4-prefix-list", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "prefix-members", "type": "container", "fields": []}], "keys": [{"name": "ipv4-prefix-list_PK", "node": "root::huawei-routing-policy:routing-policy::ipv4-prefix-lists::ipv4-prefix-list", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv4-prefix-lists::ipv4-prefix-list::prefix-members::prefix-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "ip-address", "type": "string"}, {"name": "masklength", "type": "uint8"}], "keys": [{"name": "prefix-member_PK", "node": "root::huawei-routing-policy:routing-policy::ipv4-prefix-lists::ipv4-prefix-list::prefix-members::prefix-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "ip-address", "masklength"]}]}, {"name": "root::huawei-routing-policy:routing-policy::eth-tag-filters::eth-tag-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "eth-tag-members", "type": "container", "fields": []}], "keys": [{"name": "eth-tag-filter_PK", "node": "root::huawei-routing-policy:routing-policy::eth-tag-filters::eth-tag-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::eth-tag-filters::eth-tag-filter::eth-tag-members::eth-tag-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "uint32"}], "keys": [{"name": "eth-tag-member_PK", "node": "root::huawei-routing-policy:routing-policy::eth-tag-filters::eth-tag-filter::eth-tag-members::eth-tag-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::mac-filters::mac-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "mac-members", "type": "container", "fields": []}], "keys": [{"name": "mac-filter_PK", "node": "root::huawei-routing-policy:routing-policy::mac-filters::mac-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::mac-filters::mac-filter::mac-members::mac-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "mac-member_PK", "node": "root::huawei-routing-policy:routing-policy::mac-filters::mac-filter::mac-members::mac-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::vni-filters::vni-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "vni-members", "type": "container", "fields": []}], "keys": [{"name": "vni-filter_PK", "node": "root::huawei-routing-policy:routing-policy::vni-filters::vni-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::vni-filters::vni-filter::vni-members::vni-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "uint32"}], "keys": [{"name": "vni-member_PK", "node": "root::huawei-routing-policy:routing-policy::vni-filters::vni-filter::vni-members::vni-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "rd-filter_PK", "node": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "rd-strings", "type": "container", "fields": []}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter::nodes::node::rd-strings::rd-string", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "rd-string_PK", "node": "root::huawei-routing-policy:routing-policy::rd-filters::rd-filter::nodes::node::rd-strings::rd-string", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-lists::community-list", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "community-attributes", "type": "container", "fields": []}], "keys": [{"name": "community-list_PK", "node": "root::huawei-routing-policy:routing-policy::community-lists::community-list", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-lists::community-list::community-attributes::community-attribute", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community", "type": "string"}, {"name": "description", "type": "string"}], "keys": [{"name": "community-attribute_PK", "node": "root::huawei-routing-policy:routing-policy::community-lists::community-list::community-attributes::community-attribute", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "community"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-lists::community-list::community-member", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-member", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::community-lists::community-list::community-member", "name": "community-member_PK", "fields": ["PID", "community-member"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"name": "root::huawei-routing-policy:routing-policy::as-path-filters::as-path-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "as-path-filter_PK", "node": "root::huawei-routing-policy:routing-policy::as-path-filters::as-path-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::as-path-filters::as-path-filter::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::as-path-filters::as-path-filter::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "string"}, {"name": "basic-nodes", "type": "container", "fields": []}, {"name": "advanced-nodes", "type": "container", "fields": []}], "keys": [{"name": "ext-community-segmented-nexthop-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "advanced-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::advanced-nodes::advanced-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "ext-community-segmented-nexthop-members", "type": "container", "fields": []}], "keys": [{"name": "basic-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::basic-nodes::basic-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::basic-nodes::basic-node::ext-community-segmented-nexthop-members::ext-community-segmented-nexthop-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "ext-community-segmented-nexthop-member_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-segmented-nexthop-filters::ext-community-segmented-nexthop-filter::basic-nodes::basic-node::ext-community-segmented-nexthop-members::ext-community-segmented-nexthop-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv6-prefix-filters::ipv6-prefix-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "permit-count", "type": "uint32", "default": 0, "is_config": false}, {"name": "deny-count", "type": "uint32", "default": 0, "is_config": false}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "ipv6-prefix-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ipv6-prefix-filters::ipv6-prefix-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv6-prefix-filters::ipv6-prefix-filter::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "ipv6-address", "type": "string"}, {"name": "masklength", "type": "uint8"}, {"name": "match-network", "type": "string", "default": "false"}, {"name": "masklength-lower", "type": "uint8"}, {"name": "masklength-upper", "type": "uint8"}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::ipv6-prefix-filters::ipv6-prefix-filter::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv4-prefix-filters::ipv4-prefix-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "permit-count", "type": "uint32", "default": 0, "is_config": false}, {"name": "deny-count", "type": "uint32", "default": 0, "is_config": false}, {"name": "nodes", "type": "container", "fields": []}], "keys": [{"name": "ipv4-prefix-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ipv4-prefix-filters::ipv4-prefix-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ipv4-prefix-filters::ipv4-prefix-filter::nodes::node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "ip-address", "type": "string"}, {"name": "masklength", "type": "uint8"}, {"name": "match-network", "type": "string", "default": "false"}, {"name": "masklength-lower", "type": "uint8"}, {"name": "masklength-upper", "type": "uint8"}], "keys": [{"name": "node_PK", "node": "root::huawei-routing-policy:routing-policy::ipv4-prefix-filters::ipv4-prefix-filter::nodes::node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "string"}, {"name": "basic-nodes", "type": "container", "fields": []}, {"name": "advanced-nodes", "type": "container", "fields": []}], "keys": [{"name": "ext-community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "advanced-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::advanced-nodes::advanced-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "ext-community-members", "type": "container", "fields": []}], "keys": [{"name": "basic-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::basic-nodes::basic-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::basic-nodes::basic-node::ext-community-members::ext-community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}], "keys": [{"name": "ext-community-member_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-filters::ext-community-filter::basic-nodes::basic-node::ext-community-members::ext-community-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-filters::community-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "string"}, {"name": "basic-nodes", "type": "container", "fields": []}, {"name": "advanced-nodes", "type": "container", "fields": []}], "keys": [{"name": "community-filter_PK", "node": "root::huawei-routing-policy:routing-policy::community-filters::community-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "advanced-node_PK", "node": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::advanced-nodes::advanced-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "community-members", "type": "container", "fields": []}], "keys": [{"name": "basic-node_PK", "node": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::basic-nodes::basic-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::basic-nodes::basic-node::community-members::community-member", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "value", "type": "string"}, {"name": "internet-strict-match", "type": "string", "default": "false"}], "keys": [{"name": "community-member_PK", "node": "root::huawei-routing-policy:routing-policy::community-filters::community-filter::basic-nodes::basic-node::community-members::community-member", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "value"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "name", "type": "string"}, {"name": "filter-type", "type": "string"}, {"name": "basic-nodes", "type": "container", "fields": []}, {"name": "advanced-nodes", "type": "container", "fields": []}], "keys": [{"name": "ext-community-soo-filter_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "name"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::advanced-nodes::advanced-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}, {"name": "regular", "type": "string"}], "keys": [{"name": "advanced-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::advanced-nodes::advanced-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::basic-nodes::basic-node", "type": "list", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "sequence", "type": "uint32"}, {"name": "match-mode", "type": "string"}], "keys": [{"name": "basic-node_PK", "node": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::basic-nodes::basic-node", "index": {"type": "primary"}, "constraints": {"unique": true}, "fields": ["PID", "sequence"]}]}, {"name": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::basic-nodes::basic-node::community-member", "type": "list", "nullable": true, "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "community-member", "type": "string"}], "keys": [{"node": "root::huawei-routing-policy:routing-policy::ext-community-soo-filters::ext-community-soo-filter::basic-nodes::basic-node::community-member", "name": "community-member_PK", "fields": ["PID", "community-member"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}]