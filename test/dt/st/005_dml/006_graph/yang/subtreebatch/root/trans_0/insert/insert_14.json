{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4010", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4010000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4010001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4010002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4010003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4010004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4010005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4010006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4010007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4010008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4010009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4010010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4010011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4010012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4010013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4010014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4010015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4010016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4010017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4010018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4010019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4010020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4010021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4010022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4010023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4010024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4010025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4010026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4010027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4010028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4010029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4010030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4010031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4010032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4010033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4010034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4010035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4010036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4010037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4010038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4010039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4010040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4010041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4010042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4010043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4010044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4010045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4010046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4010047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4010048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4010049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4010050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4010051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4010052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4010053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4010054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4010055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4010056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4010057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4010058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4010059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4010060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4010061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4010062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4010063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4010064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4010065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4010066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4010067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4010068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4010069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4010070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4010071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4010072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4010073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4010074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4010075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4010076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4010077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4010078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4010079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4010080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4010081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4010082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4010083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4010084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4010085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4010086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4010087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4010088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4010089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4010090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4010091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4010092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4010093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4010094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4010095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4010096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4010097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4010098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4010099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4010100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4010101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4010102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4010103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4010104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4010105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4010106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4010107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4010108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4010109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4010110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4010111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4010112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4010113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4010114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4010115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4010116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4010117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4010118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4010119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4010120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4010121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4010122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4010123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4010124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4010125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4010126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4010127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6010", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6010000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6010001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6010002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6010003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6010004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6010005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6010006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6010007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6010008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6010009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6010010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6010011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6010012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6010013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6010014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6010015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6010016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6010017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6010018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6010019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6010020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6010021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6010022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6010023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6010024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6010025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6010026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6010027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6010028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6010029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6010030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6010031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6010032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6010033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6010034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6010035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6010036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6010037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6010038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6010039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6010040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6010041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6010042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6010043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6010044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6010045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6010046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6010047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6010048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6010049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6010050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6010051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6010052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6010053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6010054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6010055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6010056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6010057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6010058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6010059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6010060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6010061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6010062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6010063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6010064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6010065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6010066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6010067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6010068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6010069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6010070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6010071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6010072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6010073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6010074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6010075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6010076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6010077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6010078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6010079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6010080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6010081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6010082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6010083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6010084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6010085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6010086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6010087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6010088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6010089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6010090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6010091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6010092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6010093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6010094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6010095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6010096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6010097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6010098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6010099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6010100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6010101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6010102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6010103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6010104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6010105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6010106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6010107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6010108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6010109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6010110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6010111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6010112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6010113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6010114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6010115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6010116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6010117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6010118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6010119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6010120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6010121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6010122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6010123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6010124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6010125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6010126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6010127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3011", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3011000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3011001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3011002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3011003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3011004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3011005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3011006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3011007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3011008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3011009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3011010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3011011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3011012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3011013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3011014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3011015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3011016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3011017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3011018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3011019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3011020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3011021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3011022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3011023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3011024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3011025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3011026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3011027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3011028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3011029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3011030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3011031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3011032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3011033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3011034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3011035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3011036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3011037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3011038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3011039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3011040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3011041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3011042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3011043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3011044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3011045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3011046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3011047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3011048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3011049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3011050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3011051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3011052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3011053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3011054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3011055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3011056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3011057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3011058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3011059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3011060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3011061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3011062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3011063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3011064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3011065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3011066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3011067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3011068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3011069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3011070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3011071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3011072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3011073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3011074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3011075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3011076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3011077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3011078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3011079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3011080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3011081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3011082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3011083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3011084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3011085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3011086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3011087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3011088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3011089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3011090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3011091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3011092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3011093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3011094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3011095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3011096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3011097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3011098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3011099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3011100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3011101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3011102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3011103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3011104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3011105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3011106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3011107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3011108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3011109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3011110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3011111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3011112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3011113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3011114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3011115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3011116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3011117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3011118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3011119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3011120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3011121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3011122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3011123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3011124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3011125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3011126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3011127, "action": "permit", "protocol": 0}]}}]}}}]}]}