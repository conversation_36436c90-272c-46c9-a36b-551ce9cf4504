{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "3029", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3029000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3029001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3029002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3029003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3029004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3029005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3029006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3029007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3029008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3029009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3029010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3029011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3029012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3029013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3029014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3029015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3029016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3029017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3029018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3029019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3029020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3029021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3029022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3029023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3029024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3029025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3029026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3029027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3029028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3029029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3029030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3029031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3029032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3029033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3029034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3029035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3029036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3029037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3029038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3029039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3029040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3029041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3029042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3029043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3029044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3029045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3029046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3029047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3029048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3029049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3029050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3029051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3029052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3029053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3029054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3029055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3029056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3029057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3029058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3029059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3029060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3029061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3029062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3029063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3029064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3029065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3029066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3029067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3029068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3029069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3029070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3029071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3029072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3029073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3029074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3029075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3029076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3029077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3029078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3029079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3029080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3029081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3029082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3029083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3029084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3029085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3029086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3029087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3029088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3029089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3029090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3029091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3029092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3029093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3029094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3029095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3029096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3029097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3029098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3029099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3029100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3029101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3029102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3029103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3029104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3029105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3029106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3029107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3029108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3029109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3029110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3029111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3029112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3029113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3029114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3029115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3029116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3029117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3029118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3029119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3029120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3029121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3029122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3029123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3029124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3029125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3029126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3029127, "action": "permit", "protocol": 0}]}}, {"identity": "2029", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2029000, "action": "permit"}, {"name": "rule1", "id": 2029001, "action": "permit"}, {"name": "rule2", "id": 2029002, "action": "permit"}, {"name": "rule3", "id": 2029003, "action": "permit"}, {"name": "rule4", "id": 2029004, "action": "permit"}, {"name": "rule5", "id": 2029005, "action": "permit"}, {"name": "rule6", "id": 2029006, "action": "permit"}, {"name": "rule7", "id": 2029007, "action": "permit"}, {"name": "rule8", "id": 2029008, "action": "permit"}, {"name": "rule9", "id": 2029009, "action": "permit"}, {"name": "rule10", "id": 2029010, "action": "permit"}, {"name": "rule11", "id": 2029011, "action": "permit"}, {"name": "rule12", "id": 2029012, "action": "permit"}, {"name": "rule13", "id": 2029013, "action": "permit"}, {"name": "rule14", "id": 2029014, "action": "permit"}, {"name": "rule15", "id": 2029015, "action": "permit"}, {"name": "rule16", "id": 2029016, "action": "permit"}, {"name": "rule17", "id": 2029017, "action": "permit"}, {"name": "rule18", "id": 2029018, "action": "permit"}, {"name": "rule19", "id": 2029019, "action": "permit"}, {"name": "rule20", "id": 2029020, "action": "permit"}, {"name": "rule21", "id": 2029021, "action": "permit"}, {"name": "rule22", "id": 2029022, "action": "permit"}, {"name": "rule23", "id": 2029023, "action": "permit"}, {"name": "rule24", "id": 2029024, "action": "permit"}, {"name": "rule25", "id": 2029025, "action": "permit"}, {"name": "rule26", "id": 2029026, "action": "permit"}, {"name": "rule27", "id": 2029027, "action": "permit"}, {"name": "rule28", "id": 2029028, "action": "permit"}, {"name": "rule29", "id": 2029029, "action": "permit"}, {"name": "rule30", "id": 2029030, "action": "permit"}, {"name": "rule31", "id": 2029031, "action": "permit"}, {"name": "rule32", "id": 2029032, "action": "permit"}, {"name": "rule33", "id": 2029033, "action": "permit"}, {"name": "rule34", "id": 2029034, "action": "permit"}, {"name": "rule35", "id": 2029035, "action": "permit"}, {"name": "rule36", "id": 2029036, "action": "permit"}, {"name": "rule37", "id": 2029037, "action": "permit"}, {"name": "rule38", "id": 2029038, "action": "permit"}, {"name": "rule39", "id": 2029039, "action": "permit"}, {"name": "rule40", "id": 2029040, "action": "permit"}, {"name": "rule41", "id": 2029041, "action": "permit"}, {"name": "rule42", "id": 2029042, "action": "permit"}, {"name": "rule43", "id": 2029043, "action": "permit"}, {"name": "rule44", "id": 2029044, "action": "permit"}, {"name": "rule45", "id": 2029045, "action": "permit"}, {"name": "rule46", "id": 2029046, "action": "permit"}, {"name": "rule47", "id": 2029047, "action": "permit"}, {"name": "rule48", "id": 2029048, "action": "permit"}, {"name": "rule49", "id": 2029049, "action": "permit"}, {"name": "rule50", "id": 2029050, "action": "permit"}, {"name": "rule51", "id": 2029051, "action": "permit"}, {"name": "rule52", "id": 2029052, "action": "permit"}, {"name": "rule53", "id": 2029053, "action": "permit"}, {"name": "rule54", "id": 2029054, "action": "permit"}, {"name": "rule55", "id": 2029055, "action": "permit"}, {"name": "rule56", "id": 2029056, "action": "permit"}, {"name": "rule57", "id": 2029057, "action": "permit"}, {"name": "rule58", "id": 2029058, "action": "permit"}, {"name": "rule59", "id": 2029059, "action": "permit"}, {"name": "rule60", "id": 2029060, "action": "permit"}, {"name": "rule61", "id": 2029061, "action": "permit"}, {"name": "rule62", "id": 2029062, "action": "permit"}, {"name": "rule63", "id": 2029063, "action": "permit"}, {"name": "rule64", "id": 2029064, "action": "permit"}, {"name": "rule65", "id": 2029065, "action": "permit"}, {"name": "rule66", "id": 2029066, "action": "permit"}, {"name": "rule67", "id": 2029067, "action": "permit"}, {"name": "rule68", "id": 2029068, "action": "permit"}, {"name": "rule69", "id": 2029069, "action": "permit"}, {"name": "rule70", "id": 2029070, "action": "permit"}, {"name": "rule71", "id": 2029071, "action": "permit"}, {"name": "rule72", "id": 2029072, "action": "permit"}, {"name": "rule73", "id": 2029073, "action": "permit"}, {"name": "rule74", "id": 2029074, "action": "permit"}, {"name": "rule75", "id": 2029075, "action": "permit"}, {"name": "rule76", "id": 2029076, "action": "permit"}, {"name": "rule77", "id": 2029077, "action": "permit"}, {"name": "rule78", "id": 2029078, "action": "permit"}, {"name": "rule79", "id": 2029079, "action": "permit"}, {"name": "rule80", "id": 2029080, "action": "permit"}, {"name": "rule81", "id": 2029081, "action": "permit"}, {"name": "rule82", "id": 2029082, "action": "permit"}, {"name": "rule83", "id": 2029083, "action": "permit"}, {"name": "rule84", "id": 2029084, "action": "permit"}, {"name": "rule85", "id": 2029085, "action": "permit"}, {"name": "rule86", "id": 2029086, "action": "permit"}, {"name": "rule87", "id": 2029087, "action": "permit"}, {"name": "rule88", "id": 2029088, "action": "permit"}, {"name": "rule89", "id": 2029089, "action": "permit"}, {"name": "rule90", "id": 2029090, "action": "permit"}, {"name": "rule91", "id": 2029091, "action": "permit"}, {"name": "rule92", "id": 2029092, "action": "permit"}, {"name": "rule93", "id": 2029093, "action": "permit"}, {"name": "rule94", "id": 2029094, "action": "permit"}, {"name": "rule95", "id": 2029095, "action": "permit"}, {"name": "rule96", "id": 2029096, "action": "permit"}, {"name": "rule97", "id": 2029097, "action": "permit"}, {"name": "rule98", "id": 2029098, "action": "permit"}, {"name": "rule99", "id": 2029099, "action": "permit"}, {"name": "rule100", "id": 2029100, "action": "permit"}, {"name": "rule101", "id": 2029101, "action": "permit"}, {"name": "rule102", "id": 2029102, "action": "permit"}, {"name": "rule103", "id": 2029103, "action": "permit"}, {"name": "rule104", "id": 2029104, "action": "permit"}, {"name": "rule105", "id": 2029105, "action": "permit"}, {"name": "rule106", "id": 2029106, "action": "permit"}, {"name": "rule107", "id": 2029107, "action": "permit"}, {"name": "rule108", "id": 2029108, "action": "permit"}, {"name": "rule109", "id": 2029109, "action": "permit"}, {"name": "rule110", "id": 2029110, "action": "permit"}, {"name": "rule111", "id": 2029111, "action": "permit"}, {"name": "rule112", "id": 2029112, "action": "permit"}, {"name": "rule113", "id": 2029113, "action": "permit"}, {"name": "rule114", "id": 2029114, "action": "permit"}, {"name": "rule115", "id": 2029115, "action": "permit"}, {"name": "rule116", "id": 2029116, "action": "permit"}, {"name": "rule117", "id": 2029117, "action": "permit"}, {"name": "rule118", "id": 2029118, "action": "permit"}, {"name": "rule119", "id": 2029119, "action": "permit"}, {"name": "rule120", "id": 2029120, "action": "permit"}, {"name": "rule121", "id": 2029121, "action": "permit"}, {"name": "rule122", "id": 2029122, "action": "permit"}, {"name": "rule123", "id": 2029123, "action": "permit"}, {"name": "rule124", "id": 2029124, "action": "permit"}, {"name": "rule125", "id": 2029125, "action": "permit"}, {"name": "rule126", "id": 2029126, "action": "permit"}, {"name": "rule127", "id": 2029127, "action": "permit"}]}}, {"identity": "4029", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4029000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4029001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4029002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4029003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4029004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4029005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4029006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4029007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4029008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4029009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4029010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4029011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4029012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4029013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4029014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4029015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4029016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4029017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4029018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4029019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4029020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4029021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4029022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4029023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4029024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4029025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4029026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4029027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4029028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4029029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4029030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4029031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4029032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4029033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4029034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4029035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4029036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4029037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4029038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4029039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4029040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4029041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4029042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4029043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4029044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4029045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4029046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4029047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4029048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4029049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4029050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4029051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4029052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4029053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4029054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4029055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4029056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4029057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4029058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4029059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4029060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4029061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4029062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4029063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4029064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4029065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4029066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4029067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4029068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4029069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4029070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4029071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4029072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4029073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4029074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4029075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4029076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4029077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4029078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4029079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4029080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4029081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4029082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4029083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4029084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4029085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4029086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4029087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4029088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4029089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4029090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4029091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4029092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4029093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4029094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4029095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4029096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4029097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4029098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4029099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4029100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4029101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4029102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4029103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4029104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4029105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4029106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4029107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4029108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4029109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4029110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4029111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4029112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4029113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4029114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4029115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4029116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4029117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4029118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4029119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4029120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4029121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4029122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4029123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4029124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4029125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4029126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4029127, "action": "permit", "vlan-id": 1}]}}]}}}]}]}