{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "3032", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3032000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3032001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3032002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3032003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3032004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3032005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3032006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3032007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3032008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3032009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3032010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3032011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3032012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3032013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3032014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3032015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3032016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3032017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3032018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3032019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3032020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3032021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3032022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3032023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3032024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3032025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3032026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3032027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3032028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3032029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3032030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3032031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3032032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3032033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3032034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3032035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3032036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3032037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3032038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3032039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3032040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3032041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3032042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3032043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3032044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3032045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3032046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3032047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3032048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3032049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3032050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3032051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3032052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3032053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3032054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3032055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3032056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3032057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3032058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3032059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3032060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3032061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3032062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3032063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3032064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3032065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3032066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3032067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3032068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3032069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3032070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3032071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3032072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3032073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3032074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3032075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3032076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3032077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3032078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3032079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3032080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3032081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3032082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3032083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3032084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3032085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3032086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3032087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3032088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3032089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3032090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3032091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3032092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3032093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3032094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3032095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3032096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3032097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3032098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3032099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3032100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3032101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3032102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3032103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3032104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3032105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3032106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3032107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3032108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3032109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3032110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3032111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3032112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3032113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3032114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3032115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3032116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3032117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3032118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3032119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3032120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3032121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3032122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3032123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3032124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3032125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3032126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3032127, "action": "permit", "protocol": 0}]}}, {"identity": "2032", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2032000, "action": "permit"}, {"name": "rule1", "id": 2032001, "action": "permit"}, {"name": "rule2", "id": 2032002, "action": "permit"}, {"name": "rule3", "id": 2032003, "action": "permit"}, {"name": "rule4", "id": 2032004, "action": "permit"}, {"name": "rule5", "id": 2032005, "action": "permit"}, {"name": "rule6", "id": 2032006, "action": "permit"}, {"name": "rule7", "id": 2032007, "action": "permit"}, {"name": "rule8", "id": 2032008, "action": "permit"}, {"name": "rule9", "id": 2032009, "action": "permit"}, {"name": "rule10", "id": 2032010, "action": "permit"}, {"name": "rule11", "id": 2032011, "action": "permit"}, {"name": "rule12", "id": 2032012, "action": "permit"}, {"name": "rule13", "id": 2032013, "action": "permit"}, {"name": "rule14", "id": 2032014, "action": "permit"}, {"name": "rule15", "id": 2032015, "action": "permit"}, {"name": "rule16", "id": 2032016, "action": "permit"}, {"name": "rule17", "id": 2032017, "action": "permit"}, {"name": "rule18", "id": 2032018, "action": "permit"}, {"name": "rule19", "id": 2032019, "action": "permit"}, {"name": "rule20", "id": 2032020, "action": "permit"}, {"name": "rule21", "id": 2032021, "action": "permit"}, {"name": "rule22", "id": 2032022, "action": "permit"}, {"name": "rule23", "id": 2032023, "action": "permit"}, {"name": "rule24", "id": 2032024, "action": "permit"}, {"name": "rule25", "id": 2032025, "action": "permit"}, {"name": "rule26", "id": 2032026, "action": "permit"}, {"name": "rule27", "id": 2032027, "action": "permit"}, {"name": "rule28", "id": 2032028, "action": "permit"}, {"name": "rule29", "id": 2032029, "action": "permit"}, {"name": "rule30", "id": 2032030, "action": "permit"}, {"name": "rule31", "id": 2032031, "action": "permit"}, {"name": "rule32", "id": 2032032, "action": "permit"}, {"name": "rule33", "id": 2032033, "action": "permit"}, {"name": "rule34", "id": 2032034, "action": "permit"}, {"name": "rule35", "id": 2032035, "action": "permit"}, {"name": "rule36", "id": 2032036, "action": "permit"}, {"name": "rule37", "id": 2032037, "action": "permit"}, {"name": "rule38", "id": 2032038, "action": "permit"}, {"name": "rule39", "id": 2032039, "action": "permit"}, {"name": "rule40", "id": 2032040, "action": "permit"}, {"name": "rule41", "id": 2032041, "action": "permit"}, {"name": "rule42", "id": 2032042, "action": "permit"}, {"name": "rule43", "id": 2032043, "action": "permit"}, {"name": "rule44", "id": 2032044, "action": "permit"}, {"name": "rule45", "id": 2032045, "action": "permit"}, {"name": "rule46", "id": 2032046, "action": "permit"}, {"name": "rule47", "id": 2032047, "action": "permit"}, {"name": "rule48", "id": 2032048, "action": "permit"}, {"name": "rule49", "id": 2032049, "action": "permit"}, {"name": "rule50", "id": 2032050, "action": "permit"}, {"name": "rule51", "id": 2032051, "action": "permit"}, {"name": "rule52", "id": 2032052, "action": "permit"}, {"name": "rule53", "id": 2032053, "action": "permit"}, {"name": "rule54", "id": 2032054, "action": "permit"}, {"name": "rule55", "id": 2032055, "action": "permit"}, {"name": "rule56", "id": 2032056, "action": "permit"}, {"name": "rule57", "id": 2032057, "action": "permit"}, {"name": "rule58", "id": 2032058, "action": "permit"}, {"name": "rule59", "id": 2032059, "action": "permit"}, {"name": "rule60", "id": 2032060, "action": "permit"}, {"name": "rule61", "id": 2032061, "action": "permit"}, {"name": "rule62", "id": 2032062, "action": "permit"}, {"name": "rule63", "id": 2032063, "action": "permit"}, {"name": "rule64", "id": 2032064, "action": "permit"}, {"name": "rule65", "id": 2032065, "action": "permit"}, {"name": "rule66", "id": 2032066, "action": "permit"}, {"name": "rule67", "id": 2032067, "action": "permit"}, {"name": "rule68", "id": 2032068, "action": "permit"}, {"name": "rule69", "id": 2032069, "action": "permit"}, {"name": "rule70", "id": 2032070, "action": "permit"}, {"name": "rule71", "id": 2032071, "action": "permit"}, {"name": "rule72", "id": 2032072, "action": "permit"}, {"name": "rule73", "id": 2032073, "action": "permit"}, {"name": "rule74", "id": 2032074, "action": "permit"}, {"name": "rule75", "id": 2032075, "action": "permit"}, {"name": "rule76", "id": 2032076, "action": "permit"}, {"name": "rule77", "id": 2032077, "action": "permit"}, {"name": "rule78", "id": 2032078, "action": "permit"}, {"name": "rule79", "id": 2032079, "action": "permit"}, {"name": "rule80", "id": 2032080, "action": "permit"}, {"name": "rule81", "id": 2032081, "action": "permit"}, {"name": "rule82", "id": 2032082, "action": "permit"}, {"name": "rule83", "id": 2032083, "action": "permit"}, {"name": "rule84", "id": 2032084, "action": "permit"}, {"name": "rule85", "id": 2032085, "action": "permit"}, {"name": "rule86", "id": 2032086, "action": "permit"}, {"name": "rule87", "id": 2032087, "action": "permit"}, {"name": "rule88", "id": 2032088, "action": "permit"}, {"name": "rule89", "id": 2032089, "action": "permit"}, {"name": "rule90", "id": 2032090, "action": "permit"}, {"name": "rule91", "id": 2032091, "action": "permit"}, {"name": "rule92", "id": 2032092, "action": "permit"}, {"name": "rule93", "id": 2032093, "action": "permit"}, {"name": "rule94", "id": 2032094, "action": "permit"}, {"name": "rule95", "id": 2032095, "action": "permit"}, {"name": "rule96", "id": 2032096, "action": "permit"}, {"name": "rule97", "id": 2032097, "action": "permit"}, {"name": "rule98", "id": 2032098, "action": "permit"}, {"name": "rule99", "id": 2032099, "action": "permit"}, {"name": "rule100", "id": 2032100, "action": "permit"}, {"name": "rule101", "id": 2032101, "action": "permit"}, {"name": "rule102", "id": 2032102, "action": "permit"}, {"name": "rule103", "id": 2032103, "action": "permit"}, {"name": "rule104", "id": 2032104, "action": "permit"}, {"name": "rule105", "id": 2032105, "action": "permit"}, {"name": "rule106", "id": 2032106, "action": "permit"}, {"name": "rule107", "id": 2032107, "action": "permit"}, {"name": "rule108", "id": 2032108, "action": "permit"}, {"name": "rule109", "id": 2032109, "action": "permit"}, {"name": "rule110", "id": 2032110, "action": "permit"}, {"name": "rule111", "id": 2032111, "action": "permit"}, {"name": "rule112", "id": 2032112, "action": "permit"}, {"name": "rule113", "id": 2032113, "action": "permit"}, {"name": "rule114", "id": 2032114, "action": "permit"}, {"name": "rule115", "id": 2032115, "action": "permit"}, {"name": "rule116", "id": 2032116, "action": "permit"}, {"name": "rule117", "id": 2032117, "action": "permit"}, {"name": "rule118", "id": 2032118, "action": "permit"}, {"name": "rule119", "id": 2032119, "action": "permit"}, {"name": "rule120", "id": 2032120, "action": "permit"}, {"name": "rule121", "id": 2032121, "action": "permit"}, {"name": "rule122", "id": 2032122, "action": "permit"}, {"name": "rule123", "id": 2032123, "action": "permit"}, {"name": "rule124", "id": 2032124, "action": "permit"}, {"name": "rule125", "id": 2032125, "action": "permit"}, {"name": "rule126", "id": 2032126, "action": "permit"}, {"name": "rule127", "id": 2032127, "action": "permit"}]}}, {"identity": "4032", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4032000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4032001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4032002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4032003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4032004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4032005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4032006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4032007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4032008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4032009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4032010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4032011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4032012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4032013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4032014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4032015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4032016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4032017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4032018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4032019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4032020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4032021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4032022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4032023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4032024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4032025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4032026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4032027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4032028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4032029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4032030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4032031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4032032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4032033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4032034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4032035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4032036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4032037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4032038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4032039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4032040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4032041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4032042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4032043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4032044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4032045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4032046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4032047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4032048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4032049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4032050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4032051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4032052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4032053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4032054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4032055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4032056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4032057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4032058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4032059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4032060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4032061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4032062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4032063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4032064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4032065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4032066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4032067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4032068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4032069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4032070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4032071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4032072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4032073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4032074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4032075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4032076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4032077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4032078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4032079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4032080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4032081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4032082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4032083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4032084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4032085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4032086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4032087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4032088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4032089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4032090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4032091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4032092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4032093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4032094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4032095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4032096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4032097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4032098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4032099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4032100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4032101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4032102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4032103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4032104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4032105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4032106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4032107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4032108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4032109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4032110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4032111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4032112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4032113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4032114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4032115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4032116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4032117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4032118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4032119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4032120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4032121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4032122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4032123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4032124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4032125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4032126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4032127, "action": "permit", "vlan-id": 1}]}}]}}}]}]}