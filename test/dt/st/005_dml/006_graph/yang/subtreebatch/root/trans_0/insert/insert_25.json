{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "2019", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2019000, "action": "permit"}, {"name": "rule1", "id": 2019001, "action": "permit"}, {"name": "rule2", "id": 2019002, "action": "permit"}, {"name": "rule3", "id": 2019003, "action": "permit"}, {"name": "rule4", "id": 2019004, "action": "permit"}, {"name": "rule5", "id": 2019005, "action": "permit"}, {"name": "rule6", "id": 2019006, "action": "permit"}, {"name": "rule7", "id": 2019007, "action": "permit"}, {"name": "rule8", "id": 2019008, "action": "permit"}, {"name": "rule9", "id": 2019009, "action": "permit"}, {"name": "rule10", "id": 2019010, "action": "permit"}, {"name": "rule11", "id": 2019011, "action": "permit"}, {"name": "rule12", "id": 2019012, "action": "permit"}, {"name": "rule13", "id": 2019013, "action": "permit"}, {"name": "rule14", "id": 2019014, "action": "permit"}, {"name": "rule15", "id": 2019015, "action": "permit"}, {"name": "rule16", "id": 2019016, "action": "permit"}, {"name": "rule17", "id": 2019017, "action": "permit"}, {"name": "rule18", "id": 2019018, "action": "permit"}, {"name": "rule19", "id": 2019019, "action": "permit"}, {"name": "rule20", "id": 2019020, "action": "permit"}, {"name": "rule21", "id": 2019021, "action": "permit"}, {"name": "rule22", "id": 2019022, "action": "permit"}, {"name": "rule23", "id": 2019023, "action": "permit"}, {"name": "rule24", "id": 2019024, "action": "permit"}, {"name": "rule25", "id": 2019025, "action": "permit"}, {"name": "rule26", "id": 2019026, "action": "permit"}, {"name": "rule27", "id": 2019027, "action": "permit"}, {"name": "rule28", "id": 2019028, "action": "permit"}, {"name": "rule29", "id": 2019029, "action": "permit"}, {"name": "rule30", "id": 2019030, "action": "permit"}, {"name": "rule31", "id": 2019031, "action": "permit"}, {"name": "rule32", "id": 2019032, "action": "permit"}, {"name": "rule33", "id": 2019033, "action": "permit"}, {"name": "rule34", "id": 2019034, "action": "permit"}, {"name": "rule35", "id": 2019035, "action": "permit"}, {"name": "rule36", "id": 2019036, "action": "permit"}, {"name": "rule37", "id": 2019037, "action": "permit"}, {"name": "rule38", "id": 2019038, "action": "permit"}, {"name": "rule39", "id": 2019039, "action": "permit"}, {"name": "rule40", "id": 2019040, "action": "permit"}, {"name": "rule41", "id": 2019041, "action": "permit"}, {"name": "rule42", "id": 2019042, "action": "permit"}, {"name": "rule43", "id": 2019043, "action": "permit"}, {"name": "rule44", "id": 2019044, "action": "permit"}, {"name": "rule45", "id": 2019045, "action": "permit"}, {"name": "rule46", "id": 2019046, "action": "permit"}, {"name": "rule47", "id": 2019047, "action": "permit"}, {"name": "rule48", "id": 2019048, "action": "permit"}, {"name": "rule49", "id": 2019049, "action": "permit"}, {"name": "rule50", "id": 2019050, "action": "permit"}, {"name": "rule51", "id": 2019051, "action": "permit"}, {"name": "rule52", "id": 2019052, "action": "permit"}, {"name": "rule53", "id": 2019053, "action": "permit"}, {"name": "rule54", "id": 2019054, "action": "permit"}, {"name": "rule55", "id": 2019055, "action": "permit"}, {"name": "rule56", "id": 2019056, "action": "permit"}, {"name": "rule57", "id": 2019057, "action": "permit"}, {"name": "rule58", "id": 2019058, "action": "permit"}, {"name": "rule59", "id": 2019059, "action": "permit"}, {"name": "rule60", "id": 2019060, "action": "permit"}, {"name": "rule61", "id": 2019061, "action": "permit"}, {"name": "rule62", "id": 2019062, "action": "permit"}, {"name": "rule63", "id": 2019063, "action": "permit"}, {"name": "rule64", "id": 2019064, "action": "permit"}, {"name": "rule65", "id": 2019065, "action": "permit"}, {"name": "rule66", "id": 2019066, "action": "permit"}, {"name": "rule67", "id": 2019067, "action": "permit"}, {"name": "rule68", "id": 2019068, "action": "permit"}, {"name": "rule69", "id": 2019069, "action": "permit"}, {"name": "rule70", "id": 2019070, "action": "permit"}, {"name": "rule71", "id": 2019071, "action": "permit"}, {"name": "rule72", "id": 2019072, "action": "permit"}, {"name": "rule73", "id": 2019073, "action": "permit"}, {"name": "rule74", "id": 2019074, "action": "permit"}, {"name": "rule75", "id": 2019075, "action": "permit"}, {"name": "rule76", "id": 2019076, "action": "permit"}, {"name": "rule77", "id": 2019077, "action": "permit"}, {"name": "rule78", "id": 2019078, "action": "permit"}, {"name": "rule79", "id": 2019079, "action": "permit"}, {"name": "rule80", "id": 2019080, "action": "permit"}, {"name": "rule81", "id": 2019081, "action": "permit"}, {"name": "rule82", "id": 2019082, "action": "permit"}, {"name": "rule83", "id": 2019083, "action": "permit"}, {"name": "rule84", "id": 2019084, "action": "permit"}, {"name": "rule85", "id": 2019085, "action": "permit"}, {"name": "rule86", "id": 2019086, "action": "permit"}, {"name": "rule87", "id": 2019087, "action": "permit"}, {"name": "rule88", "id": 2019088, "action": "permit"}, {"name": "rule89", "id": 2019089, "action": "permit"}, {"name": "rule90", "id": 2019090, "action": "permit"}, {"name": "rule91", "id": 2019091, "action": "permit"}, {"name": "rule92", "id": 2019092, "action": "permit"}, {"name": "rule93", "id": 2019093, "action": "permit"}, {"name": "rule94", "id": 2019094, "action": "permit"}, {"name": "rule95", "id": 2019095, "action": "permit"}, {"name": "rule96", "id": 2019096, "action": "permit"}, {"name": "rule97", "id": 2019097, "action": "permit"}, {"name": "rule98", "id": 2019098, "action": "permit"}, {"name": "rule99", "id": 2019099, "action": "permit"}, {"name": "rule100", "id": 2019100, "action": "permit"}, {"name": "rule101", "id": 2019101, "action": "permit"}, {"name": "rule102", "id": 2019102, "action": "permit"}, {"name": "rule103", "id": 2019103, "action": "permit"}, {"name": "rule104", "id": 2019104, "action": "permit"}, {"name": "rule105", "id": 2019105, "action": "permit"}, {"name": "rule106", "id": 2019106, "action": "permit"}, {"name": "rule107", "id": 2019107, "action": "permit"}, {"name": "rule108", "id": 2019108, "action": "permit"}, {"name": "rule109", "id": 2019109, "action": "permit"}, {"name": "rule110", "id": 2019110, "action": "permit"}, {"name": "rule111", "id": 2019111, "action": "permit"}, {"name": "rule112", "id": 2019112, "action": "permit"}, {"name": "rule113", "id": 2019113, "action": "permit"}, {"name": "rule114", "id": 2019114, "action": "permit"}, {"name": "rule115", "id": 2019115, "action": "permit"}, {"name": "rule116", "id": 2019116, "action": "permit"}, {"name": "rule117", "id": 2019117, "action": "permit"}, {"name": "rule118", "id": 2019118, "action": "permit"}, {"name": "rule119", "id": 2019119, "action": "permit"}, {"name": "rule120", "id": 2019120, "action": "permit"}, {"name": "rule121", "id": 2019121, "action": "permit"}, {"name": "rule122", "id": 2019122, "action": "permit"}, {"name": "rule123", "id": 2019123, "action": "permit"}, {"name": "rule124", "id": 2019124, "action": "permit"}, {"name": "rule125", "id": 2019125, "action": "permit"}, {"name": "rule126", "id": 2019126, "action": "permit"}, {"name": "rule127", "id": 2019127, "action": "permit"}]}}, {"identity": "4019", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4019000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4019001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4019002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4019003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4019004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4019005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4019006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4019007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4019008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4019009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4019010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4019011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4019012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4019013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4019014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4019015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4019016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4019017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4019018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4019019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4019020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4019021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4019022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4019023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4019024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4019025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4019026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4019027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4019028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4019029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4019030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4019031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4019032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4019033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4019034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4019035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4019036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4019037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4019038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4019039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4019040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4019041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4019042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4019043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4019044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4019045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4019046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4019047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4019048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4019049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4019050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4019051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4019052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4019053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4019054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4019055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4019056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4019057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4019058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4019059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4019060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4019061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4019062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4019063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4019064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4019065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4019066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4019067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4019068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4019069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4019070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4019071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4019072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4019073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4019074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4019075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4019076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4019077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4019078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4019079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4019080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4019081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4019082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4019083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4019084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4019085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4019086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4019087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4019088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4019089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4019090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4019091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4019092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4019093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4019094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4019095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4019096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4019097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4019098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4019099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4019100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4019101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4019102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4019103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4019104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4019105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4019106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4019107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4019108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4019109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4019110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4019111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4019112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4019113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4019114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4019115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4019116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4019117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4019118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4019119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4019120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4019121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4019122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4019123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4019124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4019125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4019126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4019127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6019", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6019000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6019001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6019002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6019003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6019004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6019005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6019006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6019007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6019008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6019009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6019010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6019011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6019012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6019013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6019014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6019015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6019016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6019017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6019018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6019019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6019020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6019021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6019022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6019023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6019024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6019025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6019026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6019027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6019028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6019029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6019030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6019031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6019032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6019033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6019034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6019035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6019036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6019037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6019038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6019039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6019040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6019041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6019042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6019043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6019044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6019045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6019046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6019047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6019048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6019049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6019050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6019051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6019052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6019053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6019054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6019055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6019056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6019057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6019058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6019059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6019060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6019061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6019062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6019063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6019064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6019065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6019066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6019067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6019068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6019069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6019070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6019071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6019072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6019073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6019074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6019075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6019076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6019077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6019078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6019079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6019080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6019081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6019082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6019083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6019084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6019085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6019086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6019087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6019088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6019089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6019090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6019091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6019092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6019093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6019094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6019095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6019096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6019097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6019098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6019099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6019100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6019101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6019102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6019103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6019104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6019105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6019106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6019107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6019108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6019109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6019110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6019111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6019112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6019113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6019114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6019115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6019116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6019117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6019118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6019119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6019120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6019121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6019122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6019123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6019124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6019125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6019126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6019127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}]}}}]}]}