{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "2002", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2002000, "action": "permit"}, {"name": "rule1", "id": 2002001, "action": "permit"}, {"name": "rule2", "id": 2002002, "action": "permit"}, {"name": "rule3", "id": 2002003, "action": "permit"}, {"name": "rule4", "id": 2002004, "action": "permit"}, {"name": "rule5", "id": 2002005, "action": "permit"}, {"name": "rule6", "id": 2002006, "action": "permit"}, {"name": "rule7", "id": 2002007, "action": "permit"}, {"name": "rule8", "id": 2002008, "action": "permit"}, {"name": "rule9", "id": 2002009, "action": "permit"}, {"name": "rule10", "id": 2002010, "action": "permit"}, {"name": "rule11", "id": 2002011, "action": "permit"}, {"name": "rule12", "id": 2002012, "action": "permit"}, {"name": "rule13", "id": 2002013, "action": "permit"}, {"name": "rule14", "id": 2002014, "action": "permit"}, {"name": "rule15", "id": 2002015, "action": "permit"}, {"name": "rule16", "id": 2002016, "action": "permit"}, {"name": "rule17", "id": 2002017, "action": "permit"}, {"name": "rule18", "id": 2002018, "action": "permit"}, {"name": "rule19", "id": 2002019, "action": "permit"}, {"name": "rule20", "id": 2002020, "action": "permit"}, {"name": "rule21", "id": 2002021, "action": "permit"}, {"name": "rule22", "id": 2002022, "action": "permit"}, {"name": "rule23", "id": 2002023, "action": "permit"}, {"name": "rule24", "id": 2002024, "action": "permit"}, {"name": "rule25", "id": 2002025, "action": "permit"}, {"name": "rule26", "id": 2002026, "action": "permit"}, {"name": "rule27", "id": 2002027, "action": "permit"}, {"name": "rule28", "id": 2002028, "action": "permit"}, {"name": "rule29", "id": 2002029, "action": "permit"}, {"name": "rule30", "id": 2002030, "action": "permit"}, {"name": "rule31", "id": 2002031, "action": "permit"}, {"name": "rule32", "id": 2002032, "action": "permit"}, {"name": "rule33", "id": 2002033, "action": "permit"}, {"name": "rule34", "id": 2002034, "action": "permit"}, {"name": "rule35", "id": 2002035, "action": "permit"}, {"name": "rule36", "id": 2002036, "action": "permit"}, {"name": "rule37", "id": 2002037, "action": "permit"}, {"name": "rule38", "id": 2002038, "action": "permit"}, {"name": "rule39", "id": 2002039, "action": "permit"}, {"name": "rule40", "id": 2002040, "action": "permit"}, {"name": "rule41", "id": 2002041, "action": "permit"}, {"name": "rule42", "id": 2002042, "action": "permit"}, {"name": "rule43", "id": 2002043, "action": "permit"}, {"name": "rule44", "id": 2002044, "action": "permit"}, {"name": "rule45", "id": 2002045, "action": "permit"}, {"name": "rule46", "id": 2002046, "action": "permit"}, {"name": "rule47", "id": 2002047, "action": "permit"}, {"name": "rule48", "id": 2002048, "action": "permit"}, {"name": "rule49", "id": 2002049, "action": "permit"}, {"name": "rule50", "id": 2002050, "action": "permit"}, {"name": "rule51", "id": 2002051, "action": "permit"}, {"name": "rule52", "id": 2002052, "action": "permit"}, {"name": "rule53", "id": 2002053, "action": "permit"}, {"name": "rule54", "id": 2002054, "action": "permit"}, {"name": "rule55", "id": 2002055, "action": "permit"}, {"name": "rule56", "id": 2002056, "action": "permit"}, {"name": "rule57", "id": 2002057, "action": "permit"}, {"name": "rule58", "id": 2002058, "action": "permit"}, {"name": "rule59", "id": 2002059, "action": "permit"}, {"name": "rule60", "id": 2002060, "action": "permit"}, {"name": "rule61", "id": 2002061, "action": "permit"}, {"name": "rule62", "id": 2002062, "action": "permit"}, {"name": "rule63", "id": 2002063, "action": "permit"}, {"name": "rule64", "id": 2002064, "action": "permit"}, {"name": "rule65", "id": 2002065, "action": "permit"}, {"name": "rule66", "id": 2002066, "action": "permit"}, {"name": "rule67", "id": 2002067, "action": "permit"}, {"name": "rule68", "id": 2002068, "action": "permit"}, {"name": "rule69", "id": 2002069, "action": "permit"}, {"name": "rule70", "id": 2002070, "action": "permit"}, {"name": "rule71", "id": 2002071, "action": "permit"}, {"name": "rule72", "id": 2002072, "action": "permit"}, {"name": "rule73", "id": 2002073, "action": "permit"}, {"name": "rule74", "id": 2002074, "action": "permit"}, {"name": "rule75", "id": 2002075, "action": "permit"}, {"name": "rule76", "id": 2002076, "action": "permit"}, {"name": "rule77", "id": 2002077, "action": "permit"}, {"name": "rule78", "id": 2002078, "action": "permit"}, {"name": "rule79", "id": 2002079, "action": "permit"}, {"name": "rule80", "id": 2002080, "action": "permit"}, {"name": "rule81", "id": 2002081, "action": "permit"}, {"name": "rule82", "id": 2002082, "action": "permit"}, {"name": "rule83", "id": 2002083, "action": "permit"}, {"name": "rule84", "id": 2002084, "action": "permit"}, {"name": "rule85", "id": 2002085, "action": "permit"}, {"name": "rule86", "id": 2002086, "action": "permit"}, {"name": "rule87", "id": 2002087, "action": "permit"}, {"name": "rule88", "id": 2002088, "action": "permit"}, {"name": "rule89", "id": 2002089, "action": "permit"}, {"name": "rule90", "id": 2002090, "action": "permit"}, {"name": "rule91", "id": 2002091, "action": "permit"}, {"name": "rule92", "id": 2002092, "action": "permit"}, {"name": "rule93", "id": 2002093, "action": "permit"}, {"name": "rule94", "id": 2002094, "action": "permit"}, {"name": "rule95", "id": 2002095, "action": "permit"}, {"name": "rule96", "id": 2002096, "action": "permit"}, {"name": "rule97", "id": 2002097, "action": "permit"}, {"name": "rule98", "id": 2002098, "action": "permit"}, {"name": "rule99", "id": 2002099, "action": "permit"}, {"name": "rule100", "id": 2002100, "action": "permit"}, {"name": "rule101", "id": 2002101, "action": "permit"}, {"name": "rule102", "id": 2002102, "action": "permit"}, {"name": "rule103", "id": 2002103, "action": "permit"}, {"name": "rule104", "id": 2002104, "action": "permit"}, {"name": "rule105", "id": 2002105, "action": "permit"}, {"name": "rule106", "id": 2002106, "action": "permit"}, {"name": "rule107", "id": 2002107, "action": "permit"}, {"name": "rule108", "id": 2002108, "action": "permit"}, {"name": "rule109", "id": 2002109, "action": "permit"}, {"name": "rule110", "id": 2002110, "action": "permit"}, {"name": "rule111", "id": 2002111, "action": "permit"}, {"name": "rule112", "id": 2002112, "action": "permit"}, {"name": "rule113", "id": 2002113, "action": "permit"}, {"name": "rule114", "id": 2002114, "action": "permit"}, {"name": "rule115", "id": 2002115, "action": "permit"}, {"name": "rule116", "id": 2002116, "action": "permit"}, {"name": "rule117", "id": 2002117, "action": "permit"}, {"name": "rule118", "id": 2002118, "action": "permit"}, {"name": "rule119", "id": 2002119, "action": "permit"}, {"name": "rule120", "id": 2002120, "action": "permit"}, {"name": "rule121", "id": 2002121, "action": "permit"}, {"name": "rule122", "id": 2002122, "action": "permit"}, {"name": "rule123", "id": 2002123, "action": "permit"}, {"name": "rule124", "id": 2002124, "action": "permit"}, {"name": "rule125", "id": 2002125, "action": "permit"}, {"name": "rule126", "id": 2002126, "action": "permit"}, {"name": "rule127", "id": 2002127, "action": "permit"}]}}, {"identity": "4002", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4002000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4002001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4002002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4002003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4002004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4002005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4002006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4002007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4002008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4002009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4002010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4002011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4002012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4002013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4002014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4002015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4002016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4002017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4002018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4002019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4002020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4002021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4002022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4002023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4002024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4002025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4002026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4002027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4002028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4002029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4002030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4002031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4002032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4002033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4002034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4002035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4002036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4002037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4002038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4002039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4002040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4002041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4002042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4002043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4002044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4002045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4002046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4002047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4002048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4002049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4002050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4002051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4002052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4002053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4002054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4002055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4002056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4002057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4002058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4002059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4002060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4002061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4002062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4002063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4002064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4002065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4002066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4002067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4002068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4002069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4002070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4002071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4002072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4002073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4002074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4002075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4002076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4002077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4002078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4002079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4002080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4002081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4002082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4002083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4002084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4002085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4002086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4002087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4002088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4002089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4002090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4002091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4002092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4002093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4002094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4002095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4002096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4002097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4002098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4002099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4002100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4002101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4002102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4002103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4002104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4002105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4002106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4002107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4002108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4002109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4002110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4002111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4002112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4002113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4002114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4002115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4002116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4002117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4002118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4002119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4002120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4002121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4002122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4002123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4002124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4002125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4002126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4002127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6002", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6002000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6002001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6002002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6002003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6002004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6002005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6002006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6002007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6002008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6002009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6002010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6002011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6002012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6002013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6002014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6002015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6002016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6002017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6002018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6002019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6002020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6002021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6002022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6002023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6002024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6002025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6002026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6002027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6002028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6002029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6002030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6002031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6002032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6002033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6002034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6002035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6002036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6002037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6002038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6002039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6002040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6002041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6002042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6002043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6002044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6002045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6002046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6002047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6002048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6002049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6002050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6002051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6002052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6002053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6002054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6002055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6002056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6002057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6002058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6002059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6002060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6002061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6002062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6002063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6002064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6002065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6002066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6002067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6002068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6002069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6002070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6002071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6002072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6002073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6002074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6002075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6002076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6002077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6002078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6002079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6002080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6002081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6002082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6002083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6002084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6002085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6002086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6002087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6002088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6002089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6002090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6002091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6002092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6002093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6002094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6002095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6002096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6002097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6002098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6002099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6002100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6002101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6002102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6002103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6002104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6002105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6002106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6002107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6002108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6002109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6002110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6002111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6002112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6002113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6002114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6002115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6002116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6002117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6002118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6002119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6002120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6002121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6002122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6002123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6002124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6002125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6002126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6002127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}]}}}]}]}