{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "6047", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6047000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6047001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6047002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6047003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6047004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6047005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6047006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6047007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6047008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6047009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6047010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6047011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6047012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6047013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6047014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6047015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6047016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6047017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6047018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6047019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6047020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6047021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6047022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6047023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6047024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6047025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6047026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6047027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6047028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6047029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6047030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6047031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6047032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6047033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6047034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6047035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6047036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6047037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6047038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6047039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6047040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6047041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6047042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6047043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6047044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6047045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6047046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6047047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6047048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6047049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6047050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6047051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6047052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6047053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6047054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6047055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6047056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6047057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6047058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6047059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6047060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6047061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6047062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6047063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6047064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6047065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6047066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6047067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6047068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6047069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6047070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6047071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6047072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6047073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6047074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6047075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6047076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6047077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6047078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6047079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6047080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6047081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6047082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6047083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6047084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6047085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6047086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6047087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6047088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6047089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6047090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6047091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6047092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6047093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6047094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6047095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6047096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6047097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6047098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6047099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6047100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6047101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6047102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6047103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6047104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6047105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6047106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6047107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6047108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6047109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6047110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6047111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6047112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6047113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6047114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6047115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6047116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6047117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6047118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6047119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6047120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6047121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6047122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6047123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6047124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6047125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6047126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6047127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3048", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3048000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3048001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3048002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3048003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3048004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3048005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3048006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3048007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3048008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3048009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3048010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3048011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3048012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3048013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3048014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3048015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3048016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3048017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3048018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3048019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3048020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3048021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3048022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3048023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3048024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3048025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3048026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3048027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3048028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3048029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3048030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3048031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3048032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3048033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3048034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3048035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3048036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3048037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3048038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3048039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3048040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3048041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3048042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3048043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3048044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3048045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3048046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3048047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3048048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3048049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3048050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3048051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3048052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3048053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3048054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3048055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3048056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3048057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3048058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3048059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3048060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3048061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3048062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3048063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3048064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3048065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3048066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3048067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3048068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3048069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3048070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3048071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3048072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3048073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3048074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3048075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3048076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3048077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3048078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3048079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3048080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3048081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3048082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3048083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3048084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3048085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3048086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3048087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3048088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3048089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3048090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3048091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3048092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3048093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3048094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3048095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3048096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3048097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3048098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3048099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3048100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3048101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3048102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3048103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3048104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3048105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3048106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3048107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3048108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3048109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3048110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3048111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3048112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3048113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3048114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3048115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3048116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3048117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3048118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3048119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3048120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3048121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3048122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3048123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3048124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3048125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3048126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3048127, "action": "permit", "protocol": 0}]}}, {"identity": "2048", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2048000, "action": "permit"}, {"name": "rule1", "id": 2048001, "action": "permit"}, {"name": "rule2", "id": 2048002, "action": "permit"}, {"name": "rule3", "id": 2048003, "action": "permit"}, {"name": "rule4", "id": 2048004, "action": "permit"}, {"name": "rule5", "id": 2048005, "action": "permit"}, {"name": "rule6", "id": 2048006, "action": "permit"}, {"name": "rule7", "id": 2048007, "action": "permit"}, {"name": "rule8", "id": 2048008, "action": "permit"}, {"name": "rule9", "id": 2048009, "action": "permit"}, {"name": "rule10", "id": 2048010, "action": "permit"}, {"name": "rule11", "id": 2048011, "action": "permit"}, {"name": "rule12", "id": 2048012, "action": "permit"}, {"name": "rule13", "id": 2048013, "action": "permit"}, {"name": "rule14", "id": 2048014, "action": "permit"}, {"name": "rule15", "id": 2048015, "action": "permit"}, {"name": "rule16", "id": 2048016, "action": "permit"}, {"name": "rule17", "id": 2048017, "action": "permit"}, {"name": "rule18", "id": 2048018, "action": "permit"}, {"name": "rule19", "id": 2048019, "action": "permit"}, {"name": "rule20", "id": 2048020, "action": "permit"}, {"name": "rule21", "id": 2048021, "action": "permit"}, {"name": "rule22", "id": 2048022, "action": "permit"}, {"name": "rule23", "id": 2048023, "action": "permit"}, {"name": "rule24", "id": 2048024, "action": "permit"}, {"name": "rule25", "id": 2048025, "action": "permit"}, {"name": "rule26", "id": 2048026, "action": "permit"}, {"name": "rule27", "id": 2048027, "action": "permit"}, {"name": "rule28", "id": 2048028, "action": "permit"}, {"name": "rule29", "id": 2048029, "action": "permit"}, {"name": "rule30", "id": 2048030, "action": "permit"}, {"name": "rule31", "id": 2048031, "action": "permit"}, {"name": "rule32", "id": 2048032, "action": "permit"}, {"name": "rule33", "id": 2048033, "action": "permit"}, {"name": "rule34", "id": 2048034, "action": "permit"}, {"name": "rule35", "id": 2048035, "action": "permit"}, {"name": "rule36", "id": 2048036, "action": "permit"}, {"name": "rule37", "id": 2048037, "action": "permit"}, {"name": "rule38", "id": 2048038, "action": "permit"}, {"name": "rule39", "id": 2048039, "action": "permit"}, {"name": "rule40", "id": 2048040, "action": "permit"}, {"name": "rule41", "id": 2048041, "action": "permit"}, {"name": "rule42", "id": 2048042, "action": "permit"}, {"name": "rule43", "id": 2048043, "action": "permit"}, {"name": "rule44", "id": 2048044, "action": "permit"}, {"name": "rule45", "id": 2048045, "action": "permit"}, {"name": "rule46", "id": 2048046, "action": "permit"}, {"name": "rule47", "id": 2048047, "action": "permit"}, {"name": "rule48", "id": 2048048, "action": "permit"}, {"name": "rule49", "id": 2048049, "action": "permit"}, {"name": "rule50", "id": 2048050, "action": "permit"}, {"name": "rule51", "id": 2048051, "action": "permit"}, {"name": "rule52", "id": 2048052, "action": "permit"}, {"name": "rule53", "id": 2048053, "action": "permit"}, {"name": "rule54", "id": 2048054, "action": "permit"}, {"name": "rule55", "id": 2048055, "action": "permit"}, {"name": "rule56", "id": 2048056, "action": "permit"}, {"name": "rule57", "id": 2048057, "action": "permit"}, {"name": "rule58", "id": 2048058, "action": "permit"}, {"name": "rule59", "id": 2048059, "action": "permit"}, {"name": "rule60", "id": 2048060, "action": "permit"}, {"name": "rule61", "id": 2048061, "action": "permit"}, {"name": "rule62", "id": 2048062, "action": "permit"}, {"name": "rule63", "id": 2048063, "action": "permit"}, {"name": "rule64", "id": 2048064, "action": "permit"}, {"name": "rule65", "id": 2048065, "action": "permit"}, {"name": "rule66", "id": 2048066, "action": "permit"}, {"name": "rule67", "id": 2048067, "action": "permit"}, {"name": "rule68", "id": 2048068, "action": "permit"}, {"name": "rule69", "id": 2048069, "action": "permit"}, {"name": "rule70", "id": 2048070, "action": "permit"}, {"name": "rule71", "id": 2048071, "action": "permit"}, {"name": "rule72", "id": 2048072, "action": "permit"}, {"name": "rule73", "id": 2048073, "action": "permit"}, {"name": "rule74", "id": 2048074, "action": "permit"}, {"name": "rule75", "id": 2048075, "action": "permit"}, {"name": "rule76", "id": 2048076, "action": "permit"}, {"name": "rule77", "id": 2048077, "action": "permit"}, {"name": "rule78", "id": 2048078, "action": "permit"}, {"name": "rule79", "id": 2048079, "action": "permit"}, {"name": "rule80", "id": 2048080, "action": "permit"}, {"name": "rule81", "id": 2048081, "action": "permit"}, {"name": "rule82", "id": 2048082, "action": "permit"}, {"name": "rule83", "id": 2048083, "action": "permit"}, {"name": "rule84", "id": 2048084, "action": "permit"}, {"name": "rule85", "id": 2048085, "action": "permit"}, {"name": "rule86", "id": 2048086, "action": "permit"}, {"name": "rule87", "id": 2048087, "action": "permit"}, {"name": "rule88", "id": 2048088, "action": "permit"}, {"name": "rule89", "id": 2048089, "action": "permit"}, {"name": "rule90", "id": 2048090, "action": "permit"}, {"name": "rule91", "id": 2048091, "action": "permit"}, {"name": "rule92", "id": 2048092, "action": "permit"}, {"name": "rule93", "id": 2048093, "action": "permit"}, {"name": "rule94", "id": 2048094, "action": "permit"}, {"name": "rule95", "id": 2048095, "action": "permit"}, {"name": "rule96", "id": 2048096, "action": "permit"}, {"name": "rule97", "id": 2048097, "action": "permit"}, {"name": "rule98", "id": 2048098, "action": "permit"}, {"name": "rule99", "id": 2048099, "action": "permit"}, {"name": "rule100", "id": 2048100, "action": "permit"}, {"name": "rule101", "id": 2048101, "action": "permit"}, {"name": "rule102", "id": 2048102, "action": "permit"}, {"name": "rule103", "id": 2048103, "action": "permit"}, {"name": "rule104", "id": 2048104, "action": "permit"}, {"name": "rule105", "id": 2048105, "action": "permit"}, {"name": "rule106", "id": 2048106, "action": "permit"}, {"name": "rule107", "id": 2048107, "action": "permit"}, {"name": "rule108", "id": 2048108, "action": "permit"}, {"name": "rule109", "id": 2048109, "action": "permit"}, {"name": "rule110", "id": 2048110, "action": "permit"}, {"name": "rule111", "id": 2048111, "action": "permit"}, {"name": "rule112", "id": 2048112, "action": "permit"}, {"name": "rule113", "id": 2048113, "action": "permit"}, {"name": "rule114", "id": 2048114, "action": "permit"}, {"name": "rule115", "id": 2048115, "action": "permit"}, {"name": "rule116", "id": 2048116, "action": "permit"}, {"name": "rule117", "id": 2048117, "action": "permit"}, {"name": "rule118", "id": 2048118, "action": "permit"}, {"name": "rule119", "id": 2048119, "action": "permit"}, {"name": "rule120", "id": 2048120, "action": "permit"}, {"name": "rule121", "id": 2048121, "action": "permit"}, {"name": "rule122", "id": 2048122, "action": "permit"}, {"name": "rule123", "id": 2048123, "action": "permit"}, {"name": "rule124", "id": 2048124, "action": "permit"}, {"name": "rule125", "id": 2048125, "action": "permit"}, {"name": "rule126", "id": 2048126, "action": "permit"}, {"name": "rule127", "id": 2048127, "action": "permit"}]}}]}}}]}]}