{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4014", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4014000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4014001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4014002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4014003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4014004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4014005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4014006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4014007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4014008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4014009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4014010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4014011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4014012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4014013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4014014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4014015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4014016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4014017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4014018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4014019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4014020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4014021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4014022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4014023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4014024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4014025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4014026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4014027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4014028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4014029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4014030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4014031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4014032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4014033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4014034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4014035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4014036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4014037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4014038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4014039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4014040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4014041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4014042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4014043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4014044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4014045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4014046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4014047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4014048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4014049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4014050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4014051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4014052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4014053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4014054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4014055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4014056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4014057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4014058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4014059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4014060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4014061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4014062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4014063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4014064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4014065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4014066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4014067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4014068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4014069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4014070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4014071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4014072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4014073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4014074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4014075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4014076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4014077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4014078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4014079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4014080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4014081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4014082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4014083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4014084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4014085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4014086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4014087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4014088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4014089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4014090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4014091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4014092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4014093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4014094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4014095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4014096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4014097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4014098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4014099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4014100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4014101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4014102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4014103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4014104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4014105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4014106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4014107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4014108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4014109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4014110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4014111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4014112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4014113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4014114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4014115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4014116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4014117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4014118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4014119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4014120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4014121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4014122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4014123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4014124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4014125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4014126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4014127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6014", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6014000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6014001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6014002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6014003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6014004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6014005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6014006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6014007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6014008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6014009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6014010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6014011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6014012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6014013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6014014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6014015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6014016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6014017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6014018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6014019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6014020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6014021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6014022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6014023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6014024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6014025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6014026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6014027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6014028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6014029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6014030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6014031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6014032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6014033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6014034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6014035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6014036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6014037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6014038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6014039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6014040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6014041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6014042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6014043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6014044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6014045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6014046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6014047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6014048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6014049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6014050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6014051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6014052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6014053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6014054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6014055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6014056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6014057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6014058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6014059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6014060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6014061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6014062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6014063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6014064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6014065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6014066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6014067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6014068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6014069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6014070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6014071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6014072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6014073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6014074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6014075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6014076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6014077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6014078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6014079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6014080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6014081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6014082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6014083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6014084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6014085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6014086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6014087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6014088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6014089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6014090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6014091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6014092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6014093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6014094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6014095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6014096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6014097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6014098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6014099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6014100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6014101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6014102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6014103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6014104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6014105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6014106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6014107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6014108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6014109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6014110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6014111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6014112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6014113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6014114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6014115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6014116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6014117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6014118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6014119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6014120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6014121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6014122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6014123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6014124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6014125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6014126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6014127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3015", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3015000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3015001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3015002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3015003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3015004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3015005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3015006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3015007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3015008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3015009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3015010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3015011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3015012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3015013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3015014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3015015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3015016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3015017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3015018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3015019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3015020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3015021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3015022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3015023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3015024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3015025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3015026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3015027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3015028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3015029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3015030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3015031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3015032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3015033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3015034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3015035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3015036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3015037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3015038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3015039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3015040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3015041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3015042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3015043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3015044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3015045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3015046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3015047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3015048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3015049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3015050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3015051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3015052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3015053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3015054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3015055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3015056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3015057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3015058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3015059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3015060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3015061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3015062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3015063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3015064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3015065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3015066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3015067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3015068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3015069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3015070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3015071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3015072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3015073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3015074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3015075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3015076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3015077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3015078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3015079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3015080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3015081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3015082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3015083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3015084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3015085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3015086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3015087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3015088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3015089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3015090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3015091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3015092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3015093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3015094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3015095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3015096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3015097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3015098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3015099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3015100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3015101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3015102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3015103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3015104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3015105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3015106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3015107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3015108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3015109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3015110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3015111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3015112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3015113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3015114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3015115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3015116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3015117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3015118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3015119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3015120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3015121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3015122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3015123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3015124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3015125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3015126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3015127, "action": "permit", "protocol": 0}]}}]}}}]}]}