{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4063", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4063000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4063001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4063002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4063003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4063004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4063005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4063006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4063007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4063008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4063009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4063010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4063011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4063012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4063013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4063014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4063015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4063016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4063017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4063018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4063019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4063020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4063021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4063022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4063023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4063024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4063025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4063026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4063027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4063028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4063029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4063030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4063031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4063032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4063033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4063034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4063035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4063036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4063037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4063038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4063039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4063040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4063041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4063042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4063043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4063044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4063045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4063046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4063047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4063048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4063049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4063050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4063051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4063052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4063053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4063054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4063055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4063056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4063057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4063058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4063059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4063060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4063061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4063062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4063063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4063064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4063065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4063066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4063067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4063068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4063069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4063070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4063071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4063072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4063073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4063074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4063075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4063076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4063077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4063078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4063079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4063080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4063081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4063082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4063083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4063084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4063085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4063086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4063087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4063088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4063089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4063090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4063091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4063092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4063093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4063094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4063095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4063096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4063097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4063098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4063099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4063100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4063101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4063102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4063103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4063104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4063105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4063106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4063107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4063108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4063109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4063110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4063111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4063112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4063113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4063114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4063115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4063116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4063117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4063118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4063119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4063120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4063121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4063122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4063123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4063124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4063125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4063126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4063127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6063", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6063000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6063001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6063002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6063003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6063004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6063005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6063006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6063007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6063008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6063009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6063010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6063011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6063012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6063013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6063014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6063015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6063016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6063017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6063018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6063019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6063020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6063021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6063022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6063023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6063024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6063025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6063026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6063027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6063028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6063029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6063030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6063031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6063032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6063033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6063034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6063035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6063036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6063037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6063038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6063039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6063040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6063041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6063042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6063043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6063044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6063045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6063046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6063047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6063048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6063049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6063050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6063051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6063052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6063053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6063054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6063055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6063056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6063057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6063058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6063059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6063060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6063061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6063062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6063063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6063064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6063065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6063066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6063067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6063068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6063069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6063070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6063071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6063072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6063073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6063074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6063075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6063076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6063077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6063078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6063079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6063080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6063081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6063082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6063083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6063084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6063085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6063086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6063087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6063088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6063089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6063090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6063091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6063092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6063093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6063094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6063095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6063096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6063097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6063098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6063099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6063100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6063101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6063102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6063103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6063104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6063105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6063106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6063107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6063108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6063109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6063110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6063111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6063112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6063113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6063114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6063115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6063116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6063117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6063118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6063119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6063120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6063121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6063122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6063123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6063124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6063125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6063126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6063127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3065", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3065000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3065001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3065002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3065003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3065004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3065005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3065006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3065007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3065008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3065009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3065010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3065011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3065012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3065013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3065014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3065015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3065016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3065017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3065018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3065019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3065020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3065021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3065022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3065023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3065024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3065025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3065026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3065027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3065028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3065029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3065030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3065031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3065032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3065033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3065034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3065035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3065036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3065037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3065038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3065039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3065040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3065041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3065042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3065043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3065044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3065045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3065046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3065047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3065048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3065049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3065050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3065051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3065052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3065053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3065054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3065055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3065056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3065057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3065058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3065059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3065060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3065061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3065062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3065063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3065064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3065065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3065066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3065067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3065068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3065069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3065070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3065071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3065072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3065073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3065074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3065075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3065076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3065077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3065078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3065079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3065080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3065081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3065082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3065083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3065084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3065085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3065086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3065087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3065088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3065089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3065090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3065091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3065092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3065093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3065094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3065095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3065096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3065097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3065098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3065099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3065100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3065101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3065102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3065103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3065104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3065105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3065106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3065107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3065108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3065109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3065110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3065111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3065112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3065113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3065114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3065115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3065116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3065117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3065118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3065119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3065120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3065121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3065122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3065123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3065124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3065125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3065126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3065127, "action": "permit", "protocol": 0}]}}]}}}]}]}