{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4001", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4001000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4001001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4001002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4001003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4001004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4001005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4001006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4001007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4001008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4001009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4001010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4001011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4001012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4001013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4001014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4001015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4001016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4001017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4001018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4001019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4001020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4001021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4001022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4001023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4001024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4001025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4001026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4001027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4001028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4001029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4001030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4001031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4001032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4001033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4001034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4001035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4001036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4001037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4001038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4001039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4001040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4001041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4001042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4001043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4001044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4001045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4001046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4001047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4001048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4001049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4001050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4001051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4001052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4001053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4001054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4001055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4001056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4001057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4001058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4001059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4001060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4001061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4001062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4001063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4001064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4001065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4001066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4001067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4001068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4001069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4001070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4001071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4001072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4001073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4001074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4001075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4001076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4001077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4001078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4001079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4001080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4001081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4001082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4001083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4001084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4001085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4001086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4001087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4001088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4001089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4001090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4001091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4001092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4001093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4001094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4001095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4001096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4001097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4001098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4001099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4001100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4001101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4001102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4001103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4001104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4001105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4001106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4001107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4001108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4001109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4001110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4001111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4001112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4001113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4001114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4001115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4001116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4001117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4001118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4001119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4001120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4001121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4001122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4001123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4001124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4001125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4001126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4001127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6001", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6001000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6001001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6001002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6001003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6001004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6001005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6001006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6001007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6001008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6001009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6001010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6001011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6001012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6001013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6001014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6001015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6001016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6001017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6001018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6001019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6001020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6001021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6001022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6001023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6001024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6001025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6001026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6001027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6001028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6001029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6001030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6001031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6001032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6001033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6001034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6001035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6001036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6001037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6001038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6001039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6001040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6001041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6001042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6001043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6001044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6001045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6001046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6001047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6001048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6001049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6001050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6001051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6001052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6001053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6001054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6001055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6001056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6001057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6001058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6001059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6001060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6001061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6001062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6001063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6001064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6001065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6001066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6001067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6001068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6001069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6001070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6001071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6001072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6001073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6001074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6001075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6001076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6001077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6001078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6001079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6001080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6001081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6001082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6001083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6001084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6001085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6001086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6001087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6001088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6001089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6001090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6001091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6001092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6001093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6001094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6001095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6001096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6001097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6001098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6001099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6001100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6001101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6001102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6001103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6001104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6001105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6001106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6001107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6001108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6001109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6001110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6001111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6001112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6001113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6001114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6001115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6001116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6001117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6001118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6001119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6001120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6001121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6001122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6001123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6001124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6001125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6001126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6001127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3002", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3002000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3002001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3002002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3002003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3002004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3002005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3002006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3002007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3002008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3002009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3002010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3002011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3002012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3002013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3002014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3002015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3002016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3002017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3002018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3002019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3002020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3002021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3002022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3002023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3002024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3002025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3002026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3002027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3002028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3002029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3002030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3002031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3002032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3002033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3002034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3002035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3002036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3002037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3002038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3002039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3002040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3002041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3002042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3002043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3002044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3002045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3002046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3002047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3002048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3002049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3002050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3002051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3002052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3002053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3002054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3002055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3002056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3002057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3002058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3002059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3002060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3002061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3002062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3002063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3002064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3002065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3002066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3002067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3002068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3002069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3002070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3002071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3002072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3002073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3002074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3002075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3002076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3002077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3002078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3002079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3002080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3002081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3002082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3002083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3002084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3002085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3002086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3002087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3002088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3002089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3002090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3002091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3002092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3002093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3002094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3002095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3002096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3002097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3002098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3002099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3002100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3002101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3002102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3002103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3002104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3002105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3002106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3002107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3002108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3002109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3002110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3002111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3002112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3002113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3002114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3002115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3002116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3002117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3002118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3002119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3002120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3002121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3002122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3002123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3002124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3002125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3002126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3002127, "action": "permit", "protocol": 0}]}}]}}}]}]}