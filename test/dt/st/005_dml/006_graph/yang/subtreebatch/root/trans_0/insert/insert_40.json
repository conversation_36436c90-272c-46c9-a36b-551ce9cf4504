{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "4030", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4030000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4030001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4030002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4030003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4030004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4030005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4030006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4030007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4030008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4030009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4030010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4030011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4030012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4030013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4030014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4030015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4030016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4030017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4030018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4030019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4030020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4030021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4030022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4030023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4030024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4030025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4030026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4030027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4030028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4030029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4030030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4030031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4030032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4030033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4030034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4030035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4030036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4030037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4030038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4030039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4030040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4030041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4030042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4030043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4030044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4030045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4030046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4030047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4030048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4030049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4030050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4030051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4030052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4030053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4030054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4030055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4030056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4030057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4030058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4030059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4030060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4030061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4030062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4030063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4030064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4030065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4030066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4030067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4030068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4030069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4030070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4030071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4030072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4030073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4030074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4030075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4030076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4030077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4030078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4030079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4030080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4030081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4030082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4030083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4030084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4030085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4030086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4030087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4030088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4030089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4030090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4030091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4030092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4030093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4030094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4030095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4030096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4030097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4030098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4030099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4030100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4030101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4030102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4030103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4030104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4030105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4030106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4030107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4030108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4030109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4030110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4030111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4030112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4030113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4030114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4030115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4030116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4030117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4030118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4030119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4030120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4030121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4030122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4030123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4030124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4030125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4030126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4030127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6030", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6030000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6030001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6030002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6030003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6030004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6030005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6030006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6030007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6030008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6030009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6030010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6030011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6030012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6030013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6030014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6030015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6030016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6030017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6030018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6030019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6030020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6030021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6030022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6030023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6030024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6030025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6030026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6030027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6030028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6030029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6030030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6030031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6030032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6030033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6030034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6030035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6030036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6030037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6030038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6030039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6030040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6030041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6030042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6030043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6030044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6030045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6030046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6030047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6030048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6030049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6030050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6030051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6030052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6030053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6030054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6030055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6030056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6030057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6030058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6030059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6030060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6030061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6030062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6030063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6030064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6030065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6030066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6030067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6030068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6030069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6030070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6030071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6030072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6030073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6030074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6030075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6030076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6030077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6030078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6030079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6030080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6030081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6030082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6030083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6030084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6030085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6030086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6030087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6030088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6030089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6030090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6030091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6030092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6030093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6030094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6030095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6030096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6030097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6030098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6030099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6030100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6030101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6030102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6030103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6030104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6030105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6030106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6030107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6030108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6030109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6030110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6030111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6030112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6030113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6030114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6030115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6030116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6030117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6030118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6030119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6030120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6030121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6030122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6030123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6030124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6030125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6030126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6030127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3031", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3031000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3031001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3031002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3031003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3031004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3031005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3031006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3031007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3031008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3031009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3031010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3031011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3031012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3031013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3031014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3031015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3031016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3031017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3031018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3031019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3031020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3031021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3031022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3031023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3031024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3031025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3031026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3031027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3031028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3031029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3031030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3031031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3031032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3031033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3031034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3031035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3031036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3031037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3031038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3031039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3031040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3031041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3031042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3031043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3031044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3031045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3031046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3031047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3031048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3031049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3031050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3031051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3031052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3031053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3031054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3031055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3031056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3031057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3031058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3031059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3031060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3031061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3031062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3031063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3031064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3031065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3031066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3031067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3031068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3031069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3031070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3031071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3031072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3031073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3031074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3031075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3031076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3031077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3031078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3031079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3031080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3031081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3031082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3031083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3031084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3031085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3031086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3031087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3031088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3031089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3031090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3031091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3031092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3031093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3031094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3031095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3031096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3031097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3031098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3031099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3031100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3031101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3031102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3031103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3031104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3031105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3031106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3031107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3031108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3031109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3031110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3031111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3031112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3031113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3031114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3031115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3031116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3031117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3031118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3031119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3031120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3031121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3031122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3031123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3031124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3031125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3031126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3031127, "action": "permit", "protocol": 0}]}}]}}}]}]}