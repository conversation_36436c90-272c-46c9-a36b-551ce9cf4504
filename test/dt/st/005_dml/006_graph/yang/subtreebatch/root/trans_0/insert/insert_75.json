{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "6056", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6056000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6056001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6056002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6056003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6056004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6056005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6056006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6056007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6056008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6056009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6056010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6056011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6056012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6056013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6056014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6056015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6056016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6056017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6056018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6056019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6056020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6056021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6056022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6056023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6056024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6056025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6056026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6056027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6056028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6056029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6056030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6056031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6056032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6056033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6056034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6056035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6056036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6056037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6056038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6056039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6056040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6056041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6056042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6056043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6056044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6056045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6056046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6056047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6056048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6056049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6056050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6056051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6056052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6056053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6056054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6056055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6056056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6056057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6056058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6056059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6056060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6056061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6056062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6056063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6056064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6056065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6056066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6056067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6056068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6056069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6056070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6056071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6056072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6056073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6056074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6056075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6056076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6056077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6056078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6056079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6056080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6056081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6056082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6056083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6056084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6056085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6056086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6056087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6056088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6056089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6056090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6056091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6056092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6056093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6056094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6056095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6056096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6056097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6056098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6056099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6056100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6056101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6056102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6056103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6056104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6056105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6056106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6056107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6056108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6056109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6056110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6056111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6056112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6056113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6056114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6056115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6056116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6056117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6056118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6056119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6056120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6056121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6056122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6056123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6056124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6056125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6056126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6056127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3057", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3057000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3057001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3057002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3057003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3057004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3057005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3057006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3057007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3057008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3057009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3057010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3057011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3057012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3057013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3057014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3057015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3057016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3057017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3057018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3057019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3057020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3057021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3057022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3057023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3057024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3057025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3057026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3057027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3057028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3057029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3057030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3057031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3057032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3057033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3057034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3057035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3057036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3057037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3057038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3057039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3057040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3057041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3057042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3057043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3057044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3057045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3057046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3057047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3057048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3057049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3057050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3057051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3057052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3057053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3057054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3057055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3057056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3057057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3057058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3057059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3057060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3057061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3057062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3057063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3057064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3057065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3057066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3057067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3057068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3057069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3057070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3057071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3057072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3057073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3057074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3057075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3057076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3057077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3057078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3057079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3057080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3057081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3057082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3057083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3057084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3057085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3057086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3057087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3057088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3057089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3057090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3057091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3057092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3057093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3057094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3057095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3057096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3057097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3057098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3057099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3057100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3057101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3057102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3057103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3057104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3057105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3057106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3057107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3057108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3057109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3057110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3057111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3057112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3057113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3057114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3057115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3057116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3057117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3057118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3057119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3057120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3057121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3057122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3057123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3057124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3057125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3057126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3057127, "action": "permit", "protocol": 0}]}}, {"identity": "2057", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2057000, "action": "permit"}, {"name": "rule1", "id": 2057001, "action": "permit"}, {"name": "rule2", "id": 2057002, "action": "permit"}, {"name": "rule3", "id": 2057003, "action": "permit"}, {"name": "rule4", "id": 2057004, "action": "permit"}, {"name": "rule5", "id": 2057005, "action": "permit"}, {"name": "rule6", "id": 2057006, "action": "permit"}, {"name": "rule7", "id": 2057007, "action": "permit"}, {"name": "rule8", "id": 2057008, "action": "permit"}, {"name": "rule9", "id": 2057009, "action": "permit"}, {"name": "rule10", "id": 2057010, "action": "permit"}, {"name": "rule11", "id": 2057011, "action": "permit"}, {"name": "rule12", "id": 2057012, "action": "permit"}, {"name": "rule13", "id": 2057013, "action": "permit"}, {"name": "rule14", "id": 2057014, "action": "permit"}, {"name": "rule15", "id": 2057015, "action": "permit"}, {"name": "rule16", "id": 2057016, "action": "permit"}, {"name": "rule17", "id": 2057017, "action": "permit"}, {"name": "rule18", "id": 2057018, "action": "permit"}, {"name": "rule19", "id": 2057019, "action": "permit"}, {"name": "rule20", "id": 2057020, "action": "permit"}, {"name": "rule21", "id": 2057021, "action": "permit"}, {"name": "rule22", "id": 2057022, "action": "permit"}, {"name": "rule23", "id": 2057023, "action": "permit"}, {"name": "rule24", "id": 2057024, "action": "permit"}, {"name": "rule25", "id": 2057025, "action": "permit"}, {"name": "rule26", "id": 2057026, "action": "permit"}, {"name": "rule27", "id": 2057027, "action": "permit"}, {"name": "rule28", "id": 2057028, "action": "permit"}, {"name": "rule29", "id": 2057029, "action": "permit"}, {"name": "rule30", "id": 2057030, "action": "permit"}, {"name": "rule31", "id": 2057031, "action": "permit"}, {"name": "rule32", "id": 2057032, "action": "permit"}, {"name": "rule33", "id": 2057033, "action": "permit"}, {"name": "rule34", "id": 2057034, "action": "permit"}, {"name": "rule35", "id": 2057035, "action": "permit"}, {"name": "rule36", "id": 2057036, "action": "permit"}, {"name": "rule37", "id": 2057037, "action": "permit"}, {"name": "rule38", "id": 2057038, "action": "permit"}, {"name": "rule39", "id": 2057039, "action": "permit"}, {"name": "rule40", "id": 2057040, "action": "permit"}, {"name": "rule41", "id": 2057041, "action": "permit"}, {"name": "rule42", "id": 2057042, "action": "permit"}, {"name": "rule43", "id": 2057043, "action": "permit"}, {"name": "rule44", "id": 2057044, "action": "permit"}, {"name": "rule45", "id": 2057045, "action": "permit"}, {"name": "rule46", "id": 2057046, "action": "permit"}, {"name": "rule47", "id": 2057047, "action": "permit"}, {"name": "rule48", "id": 2057048, "action": "permit"}, {"name": "rule49", "id": 2057049, "action": "permit"}, {"name": "rule50", "id": 2057050, "action": "permit"}, {"name": "rule51", "id": 2057051, "action": "permit"}, {"name": "rule52", "id": 2057052, "action": "permit"}, {"name": "rule53", "id": 2057053, "action": "permit"}, {"name": "rule54", "id": 2057054, "action": "permit"}, {"name": "rule55", "id": 2057055, "action": "permit"}, {"name": "rule56", "id": 2057056, "action": "permit"}, {"name": "rule57", "id": 2057057, "action": "permit"}, {"name": "rule58", "id": 2057058, "action": "permit"}, {"name": "rule59", "id": 2057059, "action": "permit"}, {"name": "rule60", "id": 2057060, "action": "permit"}, {"name": "rule61", "id": 2057061, "action": "permit"}, {"name": "rule62", "id": 2057062, "action": "permit"}, {"name": "rule63", "id": 2057063, "action": "permit"}, {"name": "rule64", "id": 2057064, "action": "permit"}, {"name": "rule65", "id": 2057065, "action": "permit"}, {"name": "rule66", "id": 2057066, "action": "permit"}, {"name": "rule67", "id": 2057067, "action": "permit"}, {"name": "rule68", "id": 2057068, "action": "permit"}, {"name": "rule69", "id": 2057069, "action": "permit"}, {"name": "rule70", "id": 2057070, "action": "permit"}, {"name": "rule71", "id": 2057071, "action": "permit"}, {"name": "rule72", "id": 2057072, "action": "permit"}, {"name": "rule73", "id": 2057073, "action": "permit"}, {"name": "rule74", "id": 2057074, "action": "permit"}, {"name": "rule75", "id": 2057075, "action": "permit"}, {"name": "rule76", "id": 2057076, "action": "permit"}, {"name": "rule77", "id": 2057077, "action": "permit"}, {"name": "rule78", "id": 2057078, "action": "permit"}, {"name": "rule79", "id": 2057079, "action": "permit"}, {"name": "rule80", "id": 2057080, "action": "permit"}, {"name": "rule81", "id": 2057081, "action": "permit"}, {"name": "rule82", "id": 2057082, "action": "permit"}, {"name": "rule83", "id": 2057083, "action": "permit"}, {"name": "rule84", "id": 2057084, "action": "permit"}, {"name": "rule85", "id": 2057085, "action": "permit"}, {"name": "rule86", "id": 2057086, "action": "permit"}, {"name": "rule87", "id": 2057087, "action": "permit"}, {"name": "rule88", "id": 2057088, "action": "permit"}, {"name": "rule89", "id": 2057089, "action": "permit"}, {"name": "rule90", "id": 2057090, "action": "permit"}, {"name": "rule91", "id": 2057091, "action": "permit"}, {"name": "rule92", "id": 2057092, "action": "permit"}, {"name": "rule93", "id": 2057093, "action": "permit"}, {"name": "rule94", "id": 2057094, "action": "permit"}, {"name": "rule95", "id": 2057095, "action": "permit"}, {"name": "rule96", "id": 2057096, "action": "permit"}, {"name": "rule97", "id": 2057097, "action": "permit"}, {"name": "rule98", "id": 2057098, "action": "permit"}, {"name": "rule99", "id": 2057099, "action": "permit"}, {"name": "rule100", "id": 2057100, "action": "permit"}, {"name": "rule101", "id": 2057101, "action": "permit"}, {"name": "rule102", "id": 2057102, "action": "permit"}, {"name": "rule103", "id": 2057103, "action": "permit"}, {"name": "rule104", "id": 2057104, "action": "permit"}, {"name": "rule105", "id": 2057105, "action": "permit"}, {"name": "rule106", "id": 2057106, "action": "permit"}, {"name": "rule107", "id": 2057107, "action": "permit"}, {"name": "rule108", "id": 2057108, "action": "permit"}, {"name": "rule109", "id": 2057109, "action": "permit"}, {"name": "rule110", "id": 2057110, "action": "permit"}, {"name": "rule111", "id": 2057111, "action": "permit"}, {"name": "rule112", "id": 2057112, "action": "permit"}, {"name": "rule113", "id": 2057113, "action": "permit"}, {"name": "rule114", "id": 2057114, "action": "permit"}, {"name": "rule115", "id": 2057115, "action": "permit"}, {"name": "rule116", "id": 2057116, "action": "permit"}, {"name": "rule117", "id": 2057117, "action": "permit"}, {"name": "rule118", "id": 2057118, "action": "permit"}, {"name": "rule119", "id": 2057119, "action": "permit"}, {"name": "rule120", "id": 2057120, "action": "permit"}, {"name": "rule121", "id": 2057121, "action": "permit"}, {"name": "rule122", "id": 2057122, "action": "permit"}, {"name": "rule123", "id": 2057123, "action": "permit"}, {"name": "rule124", "id": 2057124, "action": "permit"}, {"name": "rule125", "id": 2057125, "action": "permit"}, {"name": "rule126", "id": 2057126, "action": "permit"}, {"name": "rule127", "id": 2057127, "action": "permit"}]}}]}}}]}]}