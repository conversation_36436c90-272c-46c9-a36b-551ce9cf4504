{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "2040", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2040000, "action": "permit"}, {"name": "rule1", "id": 2040001, "action": "permit"}, {"name": "rule2", "id": 2040002, "action": "permit"}, {"name": "rule3", "id": 2040003, "action": "permit"}, {"name": "rule4", "id": 2040004, "action": "permit"}, {"name": "rule5", "id": 2040005, "action": "permit"}, {"name": "rule6", "id": 2040006, "action": "permit"}, {"name": "rule7", "id": 2040007, "action": "permit"}, {"name": "rule8", "id": 2040008, "action": "permit"}, {"name": "rule9", "id": 2040009, "action": "permit"}, {"name": "rule10", "id": 2040010, "action": "permit"}, {"name": "rule11", "id": 2040011, "action": "permit"}, {"name": "rule12", "id": 2040012, "action": "permit"}, {"name": "rule13", "id": 2040013, "action": "permit"}, {"name": "rule14", "id": 2040014, "action": "permit"}, {"name": "rule15", "id": 2040015, "action": "permit"}, {"name": "rule16", "id": 2040016, "action": "permit"}, {"name": "rule17", "id": 2040017, "action": "permit"}, {"name": "rule18", "id": 2040018, "action": "permit"}, {"name": "rule19", "id": 2040019, "action": "permit"}, {"name": "rule20", "id": 2040020, "action": "permit"}, {"name": "rule21", "id": 2040021, "action": "permit"}, {"name": "rule22", "id": 2040022, "action": "permit"}, {"name": "rule23", "id": 2040023, "action": "permit"}, {"name": "rule24", "id": 2040024, "action": "permit"}, {"name": "rule25", "id": 2040025, "action": "permit"}, {"name": "rule26", "id": 2040026, "action": "permit"}, {"name": "rule27", "id": 2040027, "action": "permit"}, {"name": "rule28", "id": 2040028, "action": "permit"}, {"name": "rule29", "id": 2040029, "action": "permit"}, {"name": "rule30", "id": 2040030, "action": "permit"}, {"name": "rule31", "id": 2040031, "action": "permit"}, {"name": "rule32", "id": 2040032, "action": "permit"}, {"name": "rule33", "id": 2040033, "action": "permit"}, {"name": "rule34", "id": 2040034, "action": "permit"}, {"name": "rule35", "id": 2040035, "action": "permit"}, {"name": "rule36", "id": 2040036, "action": "permit"}, {"name": "rule37", "id": 2040037, "action": "permit"}, {"name": "rule38", "id": 2040038, "action": "permit"}, {"name": "rule39", "id": 2040039, "action": "permit"}, {"name": "rule40", "id": 2040040, "action": "permit"}, {"name": "rule41", "id": 2040041, "action": "permit"}, {"name": "rule42", "id": 2040042, "action": "permit"}, {"name": "rule43", "id": 2040043, "action": "permit"}, {"name": "rule44", "id": 2040044, "action": "permit"}, {"name": "rule45", "id": 2040045, "action": "permit"}, {"name": "rule46", "id": 2040046, "action": "permit"}, {"name": "rule47", "id": 2040047, "action": "permit"}, {"name": "rule48", "id": 2040048, "action": "permit"}, {"name": "rule49", "id": 2040049, "action": "permit"}, {"name": "rule50", "id": 2040050, "action": "permit"}, {"name": "rule51", "id": 2040051, "action": "permit"}, {"name": "rule52", "id": 2040052, "action": "permit"}, {"name": "rule53", "id": 2040053, "action": "permit"}, {"name": "rule54", "id": 2040054, "action": "permit"}, {"name": "rule55", "id": 2040055, "action": "permit"}, {"name": "rule56", "id": 2040056, "action": "permit"}, {"name": "rule57", "id": 2040057, "action": "permit"}, {"name": "rule58", "id": 2040058, "action": "permit"}, {"name": "rule59", "id": 2040059, "action": "permit"}, {"name": "rule60", "id": 2040060, "action": "permit"}, {"name": "rule61", "id": 2040061, "action": "permit"}, {"name": "rule62", "id": 2040062, "action": "permit"}, {"name": "rule63", "id": 2040063, "action": "permit"}, {"name": "rule64", "id": 2040064, "action": "permit"}, {"name": "rule65", "id": 2040065, "action": "permit"}, {"name": "rule66", "id": 2040066, "action": "permit"}, {"name": "rule67", "id": 2040067, "action": "permit"}, {"name": "rule68", "id": 2040068, "action": "permit"}, {"name": "rule69", "id": 2040069, "action": "permit"}, {"name": "rule70", "id": 2040070, "action": "permit"}, {"name": "rule71", "id": 2040071, "action": "permit"}, {"name": "rule72", "id": 2040072, "action": "permit"}, {"name": "rule73", "id": 2040073, "action": "permit"}, {"name": "rule74", "id": 2040074, "action": "permit"}, {"name": "rule75", "id": 2040075, "action": "permit"}, {"name": "rule76", "id": 2040076, "action": "permit"}, {"name": "rule77", "id": 2040077, "action": "permit"}, {"name": "rule78", "id": 2040078, "action": "permit"}, {"name": "rule79", "id": 2040079, "action": "permit"}, {"name": "rule80", "id": 2040080, "action": "permit"}, {"name": "rule81", "id": 2040081, "action": "permit"}, {"name": "rule82", "id": 2040082, "action": "permit"}, {"name": "rule83", "id": 2040083, "action": "permit"}, {"name": "rule84", "id": 2040084, "action": "permit"}, {"name": "rule85", "id": 2040085, "action": "permit"}, {"name": "rule86", "id": 2040086, "action": "permit"}, {"name": "rule87", "id": 2040087, "action": "permit"}, {"name": "rule88", "id": 2040088, "action": "permit"}, {"name": "rule89", "id": 2040089, "action": "permit"}, {"name": "rule90", "id": 2040090, "action": "permit"}, {"name": "rule91", "id": 2040091, "action": "permit"}, {"name": "rule92", "id": 2040092, "action": "permit"}, {"name": "rule93", "id": 2040093, "action": "permit"}, {"name": "rule94", "id": 2040094, "action": "permit"}, {"name": "rule95", "id": 2040095, "action": "permit"}, {"name": "rule96", "id": 2040096, "action": "permit"}, {"name": "rule97", "id": 2040097, "action": "permit"}, {"name": "rule98", "id": 2040098, "action": "permit"}, {"name": "rule99", "id": 2040099, "action": "permit"}, {"name": "rule100", "id": 2040100, "action": "permit"}, {"name": "rule101", "id": 2040101, "action": "permit"}, {"name": "rule102", "id": 2040102, "action": "permit"}, {"name": "rule103", "id": 2040103, "action": "permit"}, {"name": "rule104", "id": 2040104, "action": "permit"}, {"name": "rule105", "id": 2040105, "action": "permit"}, {"name": "rule106", "id": 2040106, "action": "permit"}, {"name": "rule107", "id": 2040107, "action": "permit"}, {"name": "rule108", "id": 2040108, "action": "permit"}, {"name": "rule109", "id": 2040109, "action": "permit"}, {"name": "rule110", "id": 2040110, "action": "permit"}, {"name": "rule111", "id": 2040111, "action": "permit"}, {"name": "rule112", "id": 2040112, "action": "permit"}, {"name": "rule113", "id": 2040113, "action": "permit"}, {"name": "rule114", "id": 2040114, "action": "permit"}, {"name": "rule115", "id": 2040115, "action": "permit"}, {"name": "rule116", "id": 2040116, "action": "permit"}, {"name": "rule117", "id": 2040117, "action": "permit"}, {"name": "rule118", "id": 2040118, "action": "permit"}, {"name": "rule119", "id": 2040119, "action": "permit"}, {"name": "rule120", "id": 2040120, "action": "permit"}, {"name": "rule121", "id": 2040121, "action": "permit"}, {"name": "rule122", "id": 2040122, "action": "permit"}, {"name": "rule123", "id": 2040123, "action": "permit"}, {"name": "rule124", "id": 2040124, "action": "permit"}, {"name": "rule125", "id": 2040125, "action": "permit"}, {"name": "rule126", "id": 2040126, "action": "permit"}, {"name": "rule127", "id": 2040127, "action": "permit"}]}}, {"identity": "4040", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4040000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4040001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4040002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4040003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4040004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4040005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4040006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4040007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4040008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4040009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4040010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4040011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4040012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4040013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4040014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4040015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4040016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4040017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4040018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4040019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4040020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4040021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4040022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4040023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4040024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4040025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4040026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4040027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4040028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4040029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4040030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4040031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4040032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4040033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4040034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4040035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4040036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4040037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4040038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4040039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4040040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4040041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4040042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4040043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4040044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4040045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4040046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4040047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4040048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4040049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4040050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4040051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4040052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4040053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4040054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4040055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4040056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4040057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4040058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4040059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4040060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4040061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4040062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4040063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4040064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4040065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4040066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4040067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4040068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4040069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4040070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4040071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4040072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4040073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4040074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4040075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4040076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4040077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4040078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4040079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4040080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4040081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4040082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4040083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4040084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4040085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4040086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4040087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4040088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4040089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4040090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4040091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4040092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4040093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4040094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4040095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4040096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4040097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4040098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4040099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4040100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4040101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4040102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4040103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4040104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4040105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4040106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4040107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4040108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4040109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4040110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4040111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4040112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4040113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4040114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4040115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4040116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4040117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4040118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4040119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4040120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4040121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4040122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4040123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4040124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4040125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4040126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4040127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6040", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6040000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6040001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6040002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6040003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6040004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6040005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6040006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6040007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6040008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6040009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6040010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6040011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6040012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6040013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6040014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6040015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6040016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6040017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6040018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6040019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6040020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6040021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6040022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6040023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6040024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6040025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6040026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6040027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6040028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6040029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6040030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6040031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6040032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6040033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6040034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6040035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6040036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6040037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6040038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6040039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6040040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6040041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6040042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6040043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6040044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6040045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6040046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6040047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6040048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6040049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6040050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6040051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6040052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6040053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6040054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6040055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6040056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6040057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6040058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6040059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6040060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6040061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6040062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6040063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6040064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6040065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6040066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6040067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6040068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6040069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6040070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6040071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6040072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6040073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6040074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6040075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6040076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6040077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6040078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6040079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6040080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6040081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6040082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6040083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6040084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6040085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6040086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6040087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6040088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6040089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6040090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6040091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6040092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6040093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6040094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6040095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6040096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6040097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6040098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6040099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6040100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6040101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6040102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6040103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6040104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6040105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6040106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6040107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6040108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6040109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6040110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6040111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6040112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6040113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6040114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6040115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6040116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6040117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6040118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6040119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6040120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6040121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6040122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6040123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6040124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6040125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6040126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6040127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}]}}}]}]}