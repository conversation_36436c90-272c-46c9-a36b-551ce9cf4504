{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "2022", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2022000, "action": "permit"}, {"name": "rule1", "id": 2022001, "action": "permit"}, {"name": "rule2", "id": 2022002, "action": "permit"}, {"name": "rule3", "id": 2022003, "action": "permit"}, {"name": "rule4", "id": 2022004, "action": "permit"}, {"name": "rule5", "id": 2022005, "action": "permit"}, {"name": "rule6", "id": 2022006, "action": "permit"}, {"name": "rule7", "id": 2022007, "action": "permit"}, {"name": "rule8", "id": 2022008, "action": "permit"}, {"name": "rule9", "id": 2022009, "action": "permit"}, {"name": "rule10", "id": 2022010, "action": "permit"}, {"name": "rule11", "id": 2022011, "action": "permit"}, {"name": "rule12", "id": 2022012, "action": "permit"}, {"name": "rule13", "id": 2022013, "action": "permit"}, {"name": "rule14", "id": 2022014, "action": "permit"}, {"name": "rule15", "id": 2022015, "action": "permit"}, {"name": "rule16", "id": 2022016, "action": "permit"}, {"name": "rule17", "id": 2022017, "action": "permit"}, {"name": "rule18", "id": 2022018, "action": "permit"}, {"name": "rule19", "id": 2022019, "action": "permit"}, {"name": "rule20", "id": 2022020, "action": "permit"}, {"name": "rule21", "id": 2022021, "action": "permit"}, {"name": "rule22", "id": 2022022, "action": "permit"}, {"name": "rule23", "id": 2022023, "action": "permit"}, {"name": "rule24", "id": 2022024, "action": "permit"}, {"name": "rule25", "id": 2022025, "action": "permit"}, {"name": "rule26", "id": 2022026, "action": "permit"}, {"name": "rule27", "id": 2022027, "action": "permit"}, {"name": "rule28", "id": 2022028, "action": "permit"}, {"name": "rule29", "id": 2022029, "action": "permit"}, {"name": "rule30", "id": 2022030, "action": "permit"}, {"name": "rule31", "id": 2022031, "action": "permit"}, {"name": "rule32", "id": 2022032, "action": "permit"}, {"name": "rule33", "id": 2022033, "action": "permit"}, {"name": "rule34", "id": 2022034, "action": "permit"}, {"name": "rule35", "id": 2022035, "action": "permit"}, {"name": "rule36", "id": 2022036, "action": "permit"}, {"name": "rule37", "id": 2022037, "action": "permit"}, {"name": "rule38", "id": 2022038, "action": "permit"}, {"name": "rule39", "id": 2022039, "action": "permit"}, {"name": "rule40", "id": 2022040, "action": "permit"}, {"name": "rule41", "id": 2022041, "action": "permit"}, {"name": "rule42", "id": 2022042, "action": "permit"}, {"name": "rule43", "id": 2022043, "action": "permit"}, {"name": "rule44", "id": 2022044, "action": "permit"}, {"name": "rule45", "id": 2022045, "action": "permit"}, {"name": "rule46", "id": 2022046, "action": "permit"}, {"name": "rule47", "id": 2022047, "action": "permit"}, {"name": "rule48", "id": 2022048, "action": "permit"}, {"name": "rule49", "id": 2022049, "action": "permit"}, {"name": "rule50", "id": 2022050, "action": "permit"}, {"name": "rule51", "id": 2022051, "action": "permit"}, {"name": "rule52", "id": 2022052, "action": "permit"}, {"name": "rule53", "id": 2022053, "action": "permit"}, {"name": "rule54", "id": 2022054, "action": "permit"}, {"name": "rule55", "id": 2022055, "action": "permit"}, {"name": "rule56", "id": 2022056, "action": "permit"}, {"name": "rule57", "id": 2022057, "action": "permit"}, {"name": "rule58", "id": 2022058, "action": "permit"}, {"name": "rule59", "id": 2022059, "action": "permit"}, {"name": "rule60", "id": 2022060, "action": "permit"}, {"name": "rule61", "id": 2022061, "action": "permit"}, {"name": "rule62", "id": 2022062, "action": "permit"}, {"name": "rule63", "id": 2022063, "action": "permit"}, {"name": "rule64", "id": 2022064, "action": "permit"}, {"name": "rule65", "id": 2022065, "action": "permit"}, {"name": "rule66", "id": 2022066, "action": "permit"}, {"name": "rule67", "id": 2022067, "action": "permit"}, {"name": "rule68", "id": 2022068, "action": "permit"}, {"name": "rule69", "id": 2022069, "action": "permit"}, {"name": "rule70", "id": 2022070, "action": "permit"}, {"name": "rule71", "id": 2022071, "action": "permit"}, {"name": "rule72", "id": 2022072, "action": "permit"}, {"name": "rule73", "id": 2022073, "action": "permit"}, {"name": "rule74", "id": 2022074, "action": "permit"}, {"name": "rule75", "id": 2022075, "action": "permit"}, {"name": "rule76", "id": 2022076, "action": "permit"}, {"name": "rule77", "id": 2022077, "action": "permit"}, {"name": "rule78", "id": 2022078, "action": "permit"}, {"name": "rule79", "id": 2022079, "action": "permit"}, {"name": "rule80", "id": 2022080, "action": "permit"}, {"name": "rule81", "id": 2022081, "action": "permit"}, {"name": "rule82", "id": 2022082, "action": "permit"}, {"name": "rule83", "id": 2022083, "action": "permit"}, {"name": "rule84", "id": 2022084, "action": "permit"}, {"name": "rule85", "id": 2022085, "action": "permit"}, {"name": "rule86", "id": 2022086, "action": "permit"}, {"name": "rule87", "id": 2022087, "action": "permit"}, {"name": "rule88", "id": 2022088, "action": "permit"}, {"name": "rule89", "id": 2022089, "action": "permit"}, {"name": "rule90", "id": 2022090, "action": "permit"}, {"name": "rule91", "id": 2022091, "action": "permit"}, {"name": "rule92", "id": 2022092, "action": "permit"}, {"name": "rule93", "id": 2022093, "action": "permit"}, {"name": "rule94", "id": 2022094, "action": "permit"}, {"name": "rule95", "id": 2022095, "action": "permit"}, {"name": "rule96", "id": 2022096, "action": "permit"}, {"name": "rule97", "id": 2022097, "action": "permit"}, {"name": "rule98", "id": 2022098, "action": "permit"}, {"name": "rule99", "id": 2022099, "action": "permit"}, {"name": "rule100", "id": 2022100, "action": "permit"}, {"name": "rule101", "id": 2022101, "action": "permit"}, {"name": "rule102", "id": 2022102, "action": "permit"}, {"name": "rule103", "id": 2022103, "action": "permit"}, {"name": "rule104", "id": 2022104, "action": "permit"}, {"name": "rule105", "id": 2022105, "action": "permit"}, {"name": "rule106", "id": 2022106, "action": "permit"}, {"name": "rule107", "id": 2022107, "action": "permit"}, {"name": "rule108", "id": 2022108, "action": "permit"}, {"name": "rule109", "id": 2022109, "action": "permit"}, {"name": "rule110", "id": 2022110, "action": "permit"}, {"name": "rule111", "id": 2022111, "action": "permit"}, {"name": "rule112", "id": 2022112, "action": "permit"}, {"name": "rule113", "id": 2022113, "action": "permit"}, {"name": "rule114", "id": 2022114, "action": "permit"}, {"name": "rule115", "id": 2022115, "action": "permit"}, {"name": "rule116", "id": 2022116, "action": "permit"}, {"name": "rule117", "id": 2022117, "action": "permit"}, {"name": "rule118", "id": 2022118, "action": "permit"}, {"name": "rule119", "id": 2022119, "action": "permit"}, {"name": "rule120", "id": 2022120, "action": "permit"}, {"name": "rule121", "id": 2022121, "action": "permit"}, {"name": "rule122", "id": 2022122, "action": "permit"}, {"name": "rule123", "id": 2022123, "action": "permit"}, {"name": "rule124", "id": 2022124, "action": "permit"}, {"name": "rule125", "id": 2022125, "action": "permit"}, {"name": "rule126", "id": 2022126, "action": "permit"}, {"name": "rule127", "id": 2022127, "action": "permit"}]}}, {"identity": "4022", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4022000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4022001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4022002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4022003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4022004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4022005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4022006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4022007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4022008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4022009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4022010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4022011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4022012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4022013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4022014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4022015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4022016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4022017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4022018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4022019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4022020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4022021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4022022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4022023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4022024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4022025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4022026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4022027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4022028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4022029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4022030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4022031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4022032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4022033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4022034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4022035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4022036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4022037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4022038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4022039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4022040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4022041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4022042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4022043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4022044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4022045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4022046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4022047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4022048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4022049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4022050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4022051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4022052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4022053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4022054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4022055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4022056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4022057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4022058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4022059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4022060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4022061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4022062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4022063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4022064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4022065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4022066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4022067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4022068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4022069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4022070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4022071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4022072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4022073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4022074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4022075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4022076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4022077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4022078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4022079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4022080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4022081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4022082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4022083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4022084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4022085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4022086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4022087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4022088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4022089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4022090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4022091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4022092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4022093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4022094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4022095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4022096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4022097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4022098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4022099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4022100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4022101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4022102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4022103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4022104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4022105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4022106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4022107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4022108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4022109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4022110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4022111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4022112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4022113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4022114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4022115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4022116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4022117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4022118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4022119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4022120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4022121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4022122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4022123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4022124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4022125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4022126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4022127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6022", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6022000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6022001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6022002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6022003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6022004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6022005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6022006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6022007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6022008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6022009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6022010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6022011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6022012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6022013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6022014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6022015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6022016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6022017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6022018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6022019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6022020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6022021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6022022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6022023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6022024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6022025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6022026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6022027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6022028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6022029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6022030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6022031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6022032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6022033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6022034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6022035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6022036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6022037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6022038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6022039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6022040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6022041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6022042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6022043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6022044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6022045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6022046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6022047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6022048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6022049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6022050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6022051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6022052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6022053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6022054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6022055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6022056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6022057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6022058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6022059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6022060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6022061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6022062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6022063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6022064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6022065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6022066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6022067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6022068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6022069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6022070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6022071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6022072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6022073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6022074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6022075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6022076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6022077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6022078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6022079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6022080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6022081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6022082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6022083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6022084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6022085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6022086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6022087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6022088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6022089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6022090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6022091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6022092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6022093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6022094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6022095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6022096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6022097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6022098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6022099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6022100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6022101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6022102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6022103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6022104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6022105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6022106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6022107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6022108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6022109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6022110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6022111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6022112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6022113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6022114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6022115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6022116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6022117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6022118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6022119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6022120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6022121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6022122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6022123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6022124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6022125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6022126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6022127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}]}}}]}]}