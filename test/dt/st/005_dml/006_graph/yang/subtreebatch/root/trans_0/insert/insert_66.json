{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "3050", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3050000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3050001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3050002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3050003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3050004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3050005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3050006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3050007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3050008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3050009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3050010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3050011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3050012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3050013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3050014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3050015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3050016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3050017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3050018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3050019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3050020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3050021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3050022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3050023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3050024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3050025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3050026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3050027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3050028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3050029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3050030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3050031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3050032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3050033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3050034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3050035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3050036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3050037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3050038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3050039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3050040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3050041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3050042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3050043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3050044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3050045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3050046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3050047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3050048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3050049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3050050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3050051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3050052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3050053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3050054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3050055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3050056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3050057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3050058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3050059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3050060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3050061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3050062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3050063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3050064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3050065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3050066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3050067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3050068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3050069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3050070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3050071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3050072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3050073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3050074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3050075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3050076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3050077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3050078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3050079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3050080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3050081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3050082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3050083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3050084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3050085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3050086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3050087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3050088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3050089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3050090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3050091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3050092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3050093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3050094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3050095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3050096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3050097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3050098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3050099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3050100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3050101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3050102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3050103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3050104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3050105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3050106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3050107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3050108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3050109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3050110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3050111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3050112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3050113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3050114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3050115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3050116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3050117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3050118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3050119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3050120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3050121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3050122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3050123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3050124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3050125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3050126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3050127, "action": "permit", "protocol": 0}]}}, {"identity": "2050", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2050000, "action": "permit"}, {"name": "rule1", "id": 2050001, "action": "permit"}, {"name": "rule2", "id": 2050002, "action": "permit"}, {"name": "rule3", "id": 2050003, "action": "permit"}, {"name": "rule4", "id": 2050004, "action": "permit"}, {"name": "rule5", "id": 2050005, "action": "permit"}, {"name": "rule6", "id": 2050006, "action": "permit"}, {"name": "rule7", "id": 2050007, "action": "permit"}, {"name": "rule8", "id": 2050008, "action": "permit"}, {"name": "rule9", "id": 2050009, "action": "permit"}, {"name": "rule10", "id": 2050010, "action": "permit"}, {"name": "rule11", "id": 2050011, "action": "permit"}, {"name": "rule12", "id": 2050012, "action": "permit"}, {"name": "rule13", "id": 2050013, "action": "permit"}, {"name": "rule14", "id": 2050014, "action": "permit"}, {"name": "rule15", "id": 2050015, "action": "permit"}, {"name": "rule16", "id": 2050016, "action": "permit"}, {"name": "rule17", "id": 2050017, "action": "permit"}, {"name": "rule18", "id": 2050018, "action": "permit"}, {"name": "rule19", "id": 2050019, "action": "permit"}, {"name": "rule20", "id": 2050020, "action": "permit"}, {"name": "rule21", "id": 2050021, "action": "permit"}, {"name": "rule22", "id": 2050022, "action": "permit"}, {"name": "rule23", "id": 2050023, "action": "permit"}, {"name": "rule24", "id": 2050024, "action": "permit"}, {"name": "rule25", "id": 2050025, "action": "permit"}, {"name": "rule26", "id": 2050026, "action": "permit"}, {"name": "rule27", "id": 2050027, "action": "permit"}, {"name": "rule28", "id": 2050028, "action": "permit"}, {"name": "rule29", "id": 2050029, "action": "permit"}, {"name": "rule30", "id": 2050030, "action": "permit"}, {"name": "rule31", "id": 2050031, "action": "permit"}, {"name": "rule32", "id": 2050032, "action": "permit"}, {"name": "rule33", "id": 2050033, "action": "permit"}, {"name": "rule34", "id": 2050034, "action": "permit"}, {"name": "rule35", "id": 2050035, "action": "permit"}, {"name": "rule36", "id": 2050036, "action": "permit"}, {"name": "rule37", "id": 2050037, "action": "permit"}, {"name": "rule38", "id": 2050038, "action": "permit"}, {"name": "rule39", "id": 2050039, "action": "permit"}, {"name": "rule40", "id": 2050040, "action": "permit"}, {"name": "rule41", "id": 2050041, "action": "permit"}, {"name": "rule42", "id": 2050042, "action": "permit"}, {"name": "rule43", "id": 2050043, "action": "permit"}, {"name": "rule44", "id": 2050044, "action": "permit"}, {"name": "rule45", "id": 2050045, "action": "permit"}, {"name": "rule46", "id": 2050046, "action": "permit"}, {"name": "rule47", "id": 2050047, "action": "permit"}, {"name": "rule48", "id": 2050048, "action": "permit"}, {"name": "rule49", "id": 2050049, "action": "permit"}, {"name": "rule50", "id": 2050050, "action": "permit"}, {"name": "rule51", "id": 2050051, "action": "permit"}, {"name": "rule52", "id": 2050052, "action": "permit"}, {"name": "rule53", "id": 2050053, "action": "permit"}, {"name": "rule54", "id": 2050054, "action": "permit"}, {"name": "rule55", "id": 2050055, "action": "permit"}, {"name": "rule56", "id": 2050056, "action": "permit"}, {"name": "rule57", "id": 2050057, "action": "permit"}, {"name": "rule58", "id": 2050058, "action": "permit"}, {"name": "rule59", "id": 2050059, "action": "permit"}, {"name": "rule60", "id": 2050060, "action": "permit"}, {"name": "rule61", "id": 2050061, "action": "permit"}, {"name": "rule62", "id": 2050062, "action": "permit"}, {"name": "rule63", "id": 2050063, "action": "permit"}, {"name": "rule64", "id": 2050064, "action": "permit"}, {"name": "rule65", "id": 2050065, "action": "permit"}, {"name": "rule66", "id": 2050066, "action": "permit"}, {"name": "rule67", "id": 2050067, "action": "permit"}, {"name": "rule68", "id": 2050068, "action": "permit"}, {"name": "rule69", "id": 2050069, "action": "permit"}, {"name": "rule70", "id": 2050070, "action": "permit"}, {"name": "rule71", "id": 2050071, "action": "permit"}, {"name": "rule72", "id": 2050072, "action": "permit"}, {"name": "rule73", "id": 2050073, "action": "permit"}, {"name": "rule74", "id": 2050074, "action": "permit"}, {"name": "rule75", "id": 2050075, "action": "permit"}, {"name": "rule76", "id": 2050076, "action": "permit"}, {"name": "rule77", "id": 2050077, "action": "permit"}, {"name": "rule78", "id": 2050078, "action": "permit"}, {"name": "rule79", "id": 2050079, "action": "permit"}, {"name": "rule80", "id": 2050080, "action": "permit"}, {"name": "rule81", "id": 2050081, "action": "permit"}, {"name": "rule82", "id": 2050082, "action": "permit"}, {"name": "rule83", "id": 2050083, "action": "permit"}, {"name": "rule84", "id": 2050084, "action": "permit"}, {"name": "rule85", "id": 2050085, "action": "permit"}, {"name": "rule86", "id": 2050086, "action": "permit"}, {"name": "rule87", "id": 2050087, "action": "permit"}, {"name": "rule88", "id": 2050088, "action": "permit"}, {"name": "rule89", "id": 2050089, "action": "permit"}, {"name": "rule90", "id": 2050090, "action": "permit"}, {"name": "rule91", "id": 2050091, "action": "permit"}, {"name": "rule92", "id": 2050092, "action": "permit"}, {"name": "rule93", "id": 2050093, "action": "permit"}, {"name": "rule94", "id": 2050094, "action": "permit"}, {"name": "rule95", "id": 2050095, "action": "permit"}, {"name": "rule96", "id": 2050096, "action": "permit"}, {"name": "rule97", "id": 2050097, "action": "permit"}, {"name": "rule98", "id": 2050098, "action": "permit"}, {"name": "rule99", "id": 2050099, "action": "permit"}, {"name": "rule100", "id": 2050100, "action": "permit"}, {"name": "rule101", "id": 2050101, "action": "permit"}, {"name": "rule102", "id": 2050102, "action": "permit"}, {"name": "rule103", "id": 2050103, "action": "permit"}, {"name": "rule104", "id": 2050104, "action": "permit"}, {"name": "rule105", "id": 2050105, "action": "permit"}, {"name": "rule106", "id": 2050106, "action": "permit"}, {"name": "rule107", "id": 2050107, "action": "permit"}, {"name": "rule108", "id": 2050108, "action": "permit"}, {"name": "rule109", "id": 2050109, "action": "permit"}, {"name": "rule110", "id": 2050110, "action": "permit"}, {"name": "rule111", "id": 2050111, "action": "permit"}, {"name": "rule112", "id": 2050112, "action": "permit"}, {"name": "rule113", "id": 2050113, "action": "permit"}, {"name": "rule114", "id": 2050114, "action": "permit"}, {"name": "rule115", "id": 2050115, "action": "permit"}, {"name": "rule116", "id": 2050116, "action": "permit"}, {"name": "rule117", "id": 2050117, "action": "permit"}, {"name": "rule118", "id": 2050118, "action": "permit"}, {"name": "rule119", "id": 2050119, "action": "permit"}, {"name": "rule120", "id": 2050120, "action": "permit"}, {"name": "rule121", "id": 2050121, "action": "permit"}, {"name": "rule122", "id": 2050122, "action": "permit"}, {"name": "rule123", "id": 2050123, "action": "permit"}, {"name": "rule124", "id": 2050124, "action": "permit"}, {"name": "rule125", "id": 2050125, "action": "permit"}, {"name": "rule126", "id": 2050126, "action": "permit"}, {"name": "rule127", "id": 2050127, "action": "permit"}]}}, {"identity": "4050", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4050000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4050001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4050002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4050003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4050004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4050005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4050006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4050007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4050008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4050009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4050010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4050011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4050012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4050013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4050014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4050015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4050016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4050017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4050018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4050019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4050020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4050021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4050022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4050023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4050024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4050025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4050026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4050027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4050028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4050029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4050030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4050031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4050032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4050033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4050034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4050035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4050036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4050037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4050038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4050039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4050040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4050041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4050042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4050043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4050044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4050045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4050046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4050047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4050048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4050049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4050050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4050051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4050052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4050053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4050054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4050055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4050056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4050057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4050058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4050059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4050060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4050061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4050062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4050063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4050064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4050065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4050066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4050067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4050068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4050069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4050070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4050071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4050072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4050073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4050074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4050075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4050076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4050077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4050078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4050079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4050080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4050081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4050082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4050083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4050084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4050085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4050086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4050087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4050088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4050089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4050090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4050091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4050092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4050093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4050094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4050095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4050096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4050097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4050098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4050099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4050100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4050101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4050102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4050103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4050104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4050105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4050106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4050107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4050108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4050109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4050110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4050111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4050112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4050113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4050114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4050115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4050116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4050117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4050118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4050119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4050120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4050121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4050122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4050123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4050124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4050125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4050126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4050127, "action": "permit", "vlan-id": 1}]}}]}}}]}]}