{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "2043", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2043000, "action": "permit"}, {"name": "rule1", "id": 2043001, "action": "permit"}, {"name": "rule2", "id": 2043002, "action": "permit"}, {"name": "rule3", "id": 2043003, "action": "permit"}, {"name": "rule4", "id": 2043004, "action": "permit"}, {"name": "rule5", "id": 2043005, "action": "permit"}, {"name": "rule6", "id": 2043006, "action": "permit"}, {"name": "rule7", "id": 2043007, "action": "permit"}, {"name": "rule8", "id": 2043008, "action": "permit"}, {"name": "rule9", "id": 2043009, "action": "permit"}, {"name": "rule10", "id": 2043010, "action": "permit"}, {"name": "rule11", "id": 2043011, "action": "permit"}, {"name": "rule12", "id": 2043012, "action": "permit"}, {"name": "rule13", "id": 2043013, "action": "permit"}, {"name": "rule14", "id": 2043014, "action": "permit"}, {"name": "rule15", "id": 2043015, "action": "permit"}, {"name": "rule16", "id": 2043016, "action": "permit"}, {"name": "rule17", "id": 2043017, "action": "permit"}, {"name": "rule18", "id": 2043018, "action": "permit"}, {"name": "rule19", "id": 2043019, "action": "permit"}, {"name": "rule20", "id": 2043020, "action": "permit"}, {"name": "rule21", "id": 2043021, "action": "permit"}, {"name": "rule22", "id": 2043022, "action": "permit"}, {"name": "rule23", "id": 2043023, "action": "permit"}, {"name": "rule24", "id": 2043024, "action": "permit"}, {"name": "rule25", "id": 2043025, "action": "permit"}, {"name": "rule26", "id": 2043026, "action": "permit"}, {"name": "rule27", "id": 2043027, "action": "permit"}, {"name": "rule28", "id": 2043028, "action": "permit"}, {"name": "rule29", "id": 2043029, "action": "permit"}, {"name": "rule30", "id": 2043030, "action": "permit"}, {"name": "rule31", "id": 2043031, "action": "permit"}, {"name": "rule32", "id": 2043032, "action": "permit"}, {"name": "rule33", "id": 2043033, "action": "permit"}, {"name": "rule34", "id": 2043034, "action": "permit"}, {"name": "rule35", "id": 2043035, "action": "permit"}, {"name": "rule36", "id": 2043036, "action": "permit"}, {"name": "rule37", "id": 2043037, "action": "permit"}, {"name": "rule38", "id": 2043038, "action": "permit"}, {"name": "rule39", "id": 2043039, "action": "permit"}, {"name": "rule40", "id": 2043040, "action": "permit"}, {"name": "rule41", "id": 2043041, "action": "permit"}, {"name": "rule42", "id": 2043042, "action": "permit"}, {"name": "rule43", "id": 2043043, "action": "permit"}, {"name": "rule44", "id": 2043044, "action": "permit"}, {"name": "rule45", "id": 2043045, "action": "permit"}, {"name": "rule46", "id": 2043046, "action": "permit"}, {"name": "rule47", "id": 2043047, "action": "permit"}, {"name": "rule48", "id": 2043048, "action": "permit"}, {"name": "rule49", "id": 2043049, "action": "permit"}, {"name": "rule50", "id": 2043050, "action": "permit"}, {"name": "rule51", "id": 2043051, "action": "permit"}, {"name": "rule52", "id": 2043052, "action": "permit"}, {"name": "rule53", "id": 2043053, "action": "permit"}, {"name": "rule54", "id": 2043054, "action": "permit"}, {"name": "rule55", "id": 2043055, "action": "permit"}, {"name": "rule56", "id": 2043056, "action": "permit"}, {"name": "rule57", "id": 2043057, "action": "permit"}, {"name": "rule58", "id": 2043058, "action": "permit"}, {"name": "rule59", "id": 2043059, "action": "permit"}, {"name": "rule60", "id": 2043060, "action": "permit"}, {"name": "rule61", "id": 2043061, "action": "permit"}, {"name": "rule62", "id": 2043062, "action": "permit"}, {"name": "rule63", "id": 2043063, "action": "permit"}, {"name": "rule64", "id": 2043064, "action": "permit"}, {"name": "rule65", "id": 2043065, "action": "permit"}, {"name": "rule66", "id": 2043066, "action": "permit"}, {"name": "rule67", "id": 2043067, "action": "permit"}, {"name": "rule68", "id": 2043068, "action": "permit"}, {"name": "rule69", "id": 2043069, "action": "permit"}, {"name": "rule70", "id": 2043070, "action": "permit"}, {"name": "rule71", "id": 2043071, "action": "permit"}, {"name": "rule72", "id": 2043072, "action": "permit"}, {"name": "rule73", "id": 2043073, "action": "permit"}, {"name": "rule74", "id": 2043074, "action": "permit"}, {"name": "rule75", "id": 2043075, "action": "permit"}, {"name": "rule76", "id": 2043076, "action": "permit"}, {"name": "rule77", "id": 2043077, "action": "permit"}, {"name": "rule78", "id": 2043078, "action": "permit"}, {"name": "rule79", "id": 2043079, "action": "permit"}, {"name": "rule80", "id": 2043080, "action": "permit"}, {"name": "rule81", "id": 2043081, "action": "permit"}, {"name": "rule82", "id": 2043082, "action": "permit"}, {"name": "rule83", "id": 2043083, "action": "permit"}, {"name": "rule84", "id": 2043084, "action": "permit"}, {"name": "rule85", "id": 2043085, "action": "permit"}, {"name": "rule86", "id": 2043086, "action": "permit"}, {"name": "rule87", "id": 2043087, "action": "permit"}, {"name": "rule88", "id": 2043088, "action": "permit"}, {"name": "rule89", "id": 2043089, "action": "permit"}, {"name": "rule90", "id": 2043090, "action": "permit"}, {"name": "rule91", "id": 2043091, "action": "permit"}, {"name": "rule92", "id": 2043092, "action": "permit"}, {"name": "rule93", "id": 2043093, "action": "permit"}, {"name": "rule94", "id": 2043094, "action": "permit"}, {"name": "rule95", "id": 2043095, "action": "permit"}, {"name": "rule96", "id": 2043096, "action": "permit"}, {"name": "rule97", "id": 2043097, "action": "permit"}, {"name": "rule98", "id": 2043098, "action": "permit"}, {"name": "rule99", "id": 2043099, "action": "permit"}, {"name": "rule100", "id": 2043100, "action": "permit"}, {"name": "rule101", "id": 2043101, "action": "permit"}, {"name": "rule102", "id": 2043102, "action": "permit"}, {"name": "rule103", "id": 2043103, "action": "permit"}, {"name": "rule104", "id": 2043104, "action": "permit"}, {"name": "rule105", "id": 2043105, "action": "permit"}, {"name": "rule106", "id": 2043106, "action": "permit"}, {"name": "rule107", "id": 2043107, "action": "permit"}, {"name": "rule108", "id": 2043108, "action": "permit"}, {"name": "rule109", "id": 2043109, "action": "permit"}, {"name": "rule110", "id": 2043110, "action": "permit"}, {"name": "rule111", "id": 2043111, "action": "permit"}, {"name": "rule112", "id": 2043112, "action": "permit"}, {"name": "rule113", "id": 2043113, "action": "permit"}, {"name": "rule114", "id": 2043114, "action": "permit"}, {"name": "rule115", "id": 2043115, "action": "permit"}, {"name": "rule116", "id": 2043116, "action": "permit"}, {"name": "rule117", "id": 2043117, "action": "permit"}, {"name": "rule118", "id": 2043118, "action": "permit"}, {"name": "rule119", "id": 2043119, "action": "permit"}, {"name": "rule120", "id": 2043120, "action": "permit"}, {"name": "rule121", "id": 2043121, "action": "permit"}, {"name": "rule122", "id": 2043122, "action": "permit"}, {"name": "rule123", "id": 2043123, "action": "permit"}, {"name": "rule124", "id": 2043124, "action": "permit"}, {"name": "rule125", "id": 2043125, "action": "permit"}, {"name": "rule126", "id": 2043126, "action": "permit"}, {"name": "rule127", "id": 2043127, "action": "permit"}]}}, {"identity": "4043", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4043000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4043001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4043002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4043003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4043004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4043005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4043006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4043007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4043008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4043009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4043010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4043011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4043012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4043013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4043014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4043015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4043016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4043017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4043018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4043019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4043020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4043021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4043022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4043023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4043024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4043025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4043026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4043027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4043028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4043029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4043030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4043031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4043032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4043033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4043034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4043035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4043036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4043037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4043038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4043039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4043040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4043041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4043042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4043043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4043044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4043045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4043046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4043047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4043048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4043049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4043050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4043051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4043052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4043053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4043054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4043055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4043056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4043057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4043058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4043059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4043060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4043061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4043062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4043063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4043064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4043065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4043066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4043067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4043068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4043069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4043070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4043071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4043072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4043073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4043074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4043075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4043076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4043077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4043078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4043079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4043080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4043081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4043082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4043083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4043084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4043085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4043086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4043087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4043088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4043089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4043090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4043091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4043092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4043093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4043094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4043095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4043096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4043097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4043098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4043099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4043100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4043101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4043102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4043103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4043104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4043105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4043106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4043107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4043108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4043109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4043110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4043111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4043112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4043113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4043114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4043115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4043116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4043117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4043118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4043119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4043120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4043121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4043122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4043123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4043124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4043125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4043126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4043127, "action": "permit", "vlan-id": 1}]}}, {"identity": "6043", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6043000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6043001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6043002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6043003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6043004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6043005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6043006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6043007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6043008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6043009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6043010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6043011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6043012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6043013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6043014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6043015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6043016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6043017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6043018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6043019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6043020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6043021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6043022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6043023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6043024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6043025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6043026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6043027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6043028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6043029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6043030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6043031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6043032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6043033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6043034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6043035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6043036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6043037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6043038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6043039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6043040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6043041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6043042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6043043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6043044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6043045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6043046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6043047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6043048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6043049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6043050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6043051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6043052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6043053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6043054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6043055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6043056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6043057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6043058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6043059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6043060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6043061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6043062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6043063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6043064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6043065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6043066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6043067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6043068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6043069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6043070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6043071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6043072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6043073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6043074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6043075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6043076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6043077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6043078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6043079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6043080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6043081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6043082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6043083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6043084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6043085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6043086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6043087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6043088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6043089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6043090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6043091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6043092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6043093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6043094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6043095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6043096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6043097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6043098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6043099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6043100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6043101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6043102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6043103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6043104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6043105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6043106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6043107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6043108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6043109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6043110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6043111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6043112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6043113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6043114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6043115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6043116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6043117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6043118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6043119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6043120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6043121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6043122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6043123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6043124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6043125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6043126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6043127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}]}}}]}]}