{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "6050", "type": "UCL", "huawei-acl-ucl:rule-ucls": {"rule-ucl": [{"name": "rule0", "id": 6050000, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule1", "id": 6050001, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule2", "id": 6050002, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule3", "id": 6050003, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule4", "id": 6050004, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule5", "id": 6050005, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule6", "id": 6050006, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule7", "id": 6050007, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule8", "id": 6050008, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule9", "id": 6050009, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule10", "id": 6050010, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule11", "id": 6050011, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule12", "id": 6050012, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule13", "id": 6050013, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule14", "id": 6050014, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule15", "id": 6050015, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule16", "id": 6050016, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule17", "id": 6050017, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule18", "id": 6050018, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule19", "id": 6050019, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule20", "id": 6050020, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule21", "id": 6050021, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule22", "id": 6050022, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule23", "id": 6050023, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule24", "id": 6050024, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule25", "id": 6050025, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule26", "id": 6050026, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule27", "id": 6050027, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule28", "id": 6050028, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule29", "id": 6050029, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule30", "id": 6050030, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule31", "id": 6050031, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule32", "id": 6050032, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule33", "id": 6050033, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule34", "id": 6050034, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule35", "id": 6050035, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule36", "id": 6050036, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule37", "id": 6050037, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule38", "id": 6050038, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule39", "id": 6050039, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule40", "id": 6050040, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule41", "id": 6050041, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule42", "id": 6050042, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule43", "id": 6050043, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule44", "id": 6050044, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule45", "id": 6050045, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule46", "id": 6050046, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule47", "id": 6050047, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule48", "id": 6050048, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule49", "id": 6050049, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule50", "id": 6050050, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule51", "id": 6050051, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule52", "id": 6050052, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule53", "id": 6050053, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule54", "id": 6050054, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule55", "id": 6050055, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule56", "id": 6050056, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule57", "id": 6050057, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule58", "id": 6050058, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule59", "id": 6050059, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule60", "id": 6050060, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule61", "id": 6050061, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule62", "id": 6050062, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule63", "id": 6050063, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule64", "id": 6050064, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule65", "id": 6050065, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule66", "id": 6050066, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule67", "id": 6050067, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule68", "id": 6050068, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule69", "id": 6050069, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule70", "id": 6050070, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule71", "id": 6050071, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule72", "id": 6050072, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule73", "id": 6050073, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule74", "id": 6050074, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule75", "id": 6050075, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule76", "id": 6050076, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule77", "id": 6050077, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule78", "id": 6050078, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule79", "id": 6050079, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule80", "id": 6050080, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule81", "id": 6050081, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule82", "id": 6050082, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule83", "id": 6050083, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule84", "id": 6050084, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule85", "id": 6050085, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule86", "id": 6050086, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule87", "id": 6050087, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule88", "id": 6050088, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule89", "id": 6050089, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule90", "id": 6050090, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule91", "id": 6050091, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule92", "id": 6050092, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule93", "id": 6050093, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule94", "id": 6050094, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule95", "id": 6050095, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule96", "id": 6050096, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule97", "id": 6050097, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule98", "id": 6050098, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule99", "id": 6050099, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule100", "id": 6050100, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule101", "id": 6050101, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule102", "id": 6050102, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule103", "id": 6050103, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule104", "id": 6050104, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule105", "id": 6050105, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule106", "id": 6050106, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule107", "id": 6050107, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule108", "id": 6050108, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule109", "id": 6050109, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule110", "id": 6050110, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule111", "id": 6050111, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule112", "id": 6050112, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule113", "id": 6050113, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule114", "id": 6050114, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule115", "id": 6050115, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule116", "id": 6050116, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule117", "id": 6050117, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule118", "id": 6050118, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule119", "id": 6050119, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule120", "id": 6050120, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule121", "id": 6050121, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule122", "id": 6050122, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule123", "id": 6050123, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule124", "id": 6050124, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule125", "id": 6050125, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule126", "id": 6050126, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}, {"name": "rule127", "id": 6050127, "action": "permit", "protocol": 0, "passthrough-domain": "0.login.huawei.com"}]}}, {"identity": "3051", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3051000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3051001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3051002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3051003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3051004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3051005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3051006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3051007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3051008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3051009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3051010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3051011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3051012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3051013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3051014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3051015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3051016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3051017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3051018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3051019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3051020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3051021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3051022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3051023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3051024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3051025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3051026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3051027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3051028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3051029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3051030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3051031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3051032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3051033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3051034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3051035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3051036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3051037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3051038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3051039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3051040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3051041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3051042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3051043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3051044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3051045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3051046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3051047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3051048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3051049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3051050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3051051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3051052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3051053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3051054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3051055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3051056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3051057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3051058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3051059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3051060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3051061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3051062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3051063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3051064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3051065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3051066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3051067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3051068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3051069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3051070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3051071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3051072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3051073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3051074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3051075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3051076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3051077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3051078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3051079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3051080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3051081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3051082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3051083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3051084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3051085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3051086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3051087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3051088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3051089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3051090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3051091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3051092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3051093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3051094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3051095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3051096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3051097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3051098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3051099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3051100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3051101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3051102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3051103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3051104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3051105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3051106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3051107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3051108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3051109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3051110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3051111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3051112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3051113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3051114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3051115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3051116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3051117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3051118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3051119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3051120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3051121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3051122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3051123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3051124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3051125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3051126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3051127, "action": "permit", "protocol": 0}]}}, {"identity": "2051", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2051000, "action": "permit"}, {"name": "rule1", "id": 2051001, "action": "permit"}, {"name": "rule2", "id": 2051002, "action": "permit"}, {"name": "rule3", "id": 2051003, "action": "permit"}, {"name": "rule4", "id": 2051004, "action": "permit"}, {"name": "rule5", "id": 2051005, "action": "permit"}, {"name": "rule6", "id": 2051006, "action": "permit"}, {"name": "rule7", "id": 2051007, "action": "permit"}, {"name": "rule8", "id": 2051008, "action": "permit"}, {"name": "rule9", "id": 2051009, "action": "permit"}, {"name": "rule10", "id": 2051010, "action": "permit"}, {"name": "rule11", "id": 2051011, "action": "permit"}, {"name": "rule12", "id": 2051012, "action": "permit"}, {"name": "rule13", "id": 2051013, "action": "permit"}, {"name": "rule14", "id": 2051014, "action": "permit"}, {"name": "rule15", "id": 2051015, "action": "permit"}, {"name": "rule16", "id": 2051016, "action": "permit"}, {"name": "rule17", "id": 2051017, "action": "permit"}, {"name": "rule18", "id": 2051018, "action": "permit"}, {"name": "rule19", "id": 2051019, "action": "permit"}, {"name": "rule20", "id": 2051020, "action": "permit"}, {"name": "rule21", "id": 2051021, "action": "permit"}, {"name": "rule22", "id": 2051022, "action": "permit"}, {"name": "rule23", "id": 2051023, "action": "permit"}, {"name": "rule24", "id": 2051024, "action": "permit"}, {"name": "rule25", "id": 2051025, "action": "permit"}, {"name": "rule26", "id": 2051026, "action": "permit"}, {"name": "rule27", "id": 2051027, "action": "permit"}, {"name": "rule28", "id": 2051028, "action": "permit"}, {"name": "rule29", "id": 2051029, "action": "permit"}, {"name": "rule30", "id": 2051030, "action": "permit"}, {"name": "rule31", "id": 2051031, "action": "permit"}, {"name": "rule32", "id": 2051032, "action": "permit"}, {"name": "rule33", "id": 2051033, "action": "permit"}, {"name": "rule34", "id": 2051034, "action": "permit"}, {"name": "rule35", "id": 2051035, "action": "permit"}, {"name": "rule36", "id": 2051036, "action": "permit"}, {"name": "rule37", "id": 2051037, "action": "permit"}, {"name": "rule38", "id": 2051038, "action": "permit"}, {"name": "rule39", "id": 2051039, "action": "permit"}, {"name": "rule40", "id": 2051040, "action": "permit"}, {"name": "rule41", "id": 2051041, "action": "permit"}, {"name": "rule42", "id": 2051042, "action": "permit"}, {"name": "rule43", "id": 2051043, "action": "permit"}, {"name": "rule44", "id": 2051044, "action": "permit"}, {"name": "rule45", "id": 2051045, "action": "permit"}, {"name": "rule46", "id": 2051046, "action": "permit"}, {"name": "rule47", "id": 2051047, "action": "permit"}, {"name": "rule48", "id": 2051048, "action": "permit"}, {"name": "rule49", "id": 2051049, "action": "permit"}, {"name": "rule50", "id": 2051050, "action": "permit"}, {"name": "rule51", "id": 2051051, "action": "permit"}, {"name": "rule52", "id": 2051052, "action": "permit"}, {"name": "rule53", "id": 2051053, "action": "permit"}, {"name": "rule54", "id": 2051054, "action": "permit"}, {"name": "rule55", "id": 2051055, "action": "permit"}, {"name": "rule56", "id": 2051056, "action": "permit"}, {"name": "rule57", "id": 2051057, "action": "permit"}, {"name": "rule58", "id": 2051058, "action": "permit"}, {"name": "rule59", "id": 2051059, "action": "permit"}, {"name": "rule60", "id": 2051060, "action": "permit"}, {"name": "rule61", "id": 2051061, "action": "permit"}, {"name": "rule62", "id": 2051062, "action": "permit"}, {"name": "rule63", "id": 2051063, "action": "permit"}, {"name": "rule64", "id": 2051064, "action": "permit"}, {"name": "rule65", "id": 2051065, "action": "permit"}, {"name": "rule66", "id": 2051066, "action": "permit"}, {"name": "rule67", "id": 2051067, "action": "permit"}, {"name": "rule68", "id": 2051068, "action": "permit"}, {"name": "rule69", "id": 2051069, "action": "permit"}, {"name": "rule70", "id": 2051070, "action": "permit"}, {"name": "rule71", "id": 2051071, "action": "permit"}, {"name": "rule72", "id": 2051072, "action": "permit"}, {"name": "rule73", "id": 2051073, "action": "permit"}, {"name": "rule74", "id": 2051074, "action": "permit"}, {"name": "rule75", "id": 2051075, "action": "permit"}, {"name": "rule76", "id": 2051076, "action": "permit"}, {"name": "rule77", "id": 2051077, "action": "permit"}, {"name": "rule78", "id": 2051078, "action": "permit"}, {"name": "rule79", "id": 2051079, "action": "permit"}, {"name": "rule80", "id": 2051080, "action": "permit"}, {"name": "rule81", "id": 2051081, "action": "permit"}, {"name": "rule82", "id": 2051082, "action": "permit"}, {"name": "rule83", "id": 2051083, "action": "permit"}, {"name": "rule84", "id": 2051084, "action": "permit"}, {"name": "rule85", "id": 2051085, "action": "permit"}, {"name": "rule86", "id": 2051086, "action": "permit"}, {"name": "rule87", "id": 2051087, "action": "permit"}, {"name": "rule88", "id": 2051088, "action": "permit"}, {"name": "rule89", "id": 2051089, "action": "permit"}, {"name": "rule90", "id": 2051090, "action": "permit"}, {"name": "rule91", "id": 2051091, "action": "permit"}, {"name": "rule92", "id": 2051092, "action": "permit"}, {"name": "rule93", "id": 2051093, "action": "permit"}, {"name": "rule94", "id": 2051094, "action": "permit"}, {"name": "rule95", "id": 2051095, "action": "permit"}, {"name": "rule96", "id": 2051096, "action": "permit"}, {"name": "rule97", "id": 2051097, "action": "permit"}, {"name": "rule98", "id": 2051098, "action": "permit"}, {"name": "rule99", "id": 2051099, "action": "permit"}, {"name": "rule100", "id": 2051100, "action": "permit"}, {"name": "rule101", "id": 2051101, "action": "permit"}, {"name": "rule102", "id": 2051102, "action": "permit"}, {"name": "rule103", "id": 2051103, "action": "permit"}, {"name": "rule104", "id": 2051104, "action": "permit"}, {"name": "rule105", "id": 2051105, "action": "permit"}, {"name": "rule106", "id": 2051106, "action": "permit"}, {"name": "rule107", "id": 2051107, "action": "permit"}, {"name": "rule108", "id": 2051108, "action": "permit"}, {"name": "rule109", "id": 2051109, "action": "permit"}, {"name": "rule110", "id": 2051110, "action": "permit"}, {"name": "rule111", "id": 2051111, "action": "permit"}, {"name": "rule112", "id": 2051112, "action": "permit"}, {"name": "rule113", "id": 2051113, "action": "permit"}, {"name": "rule114", "id": 2051114, "action": "permit"}, {"name": "rule115", "id": 2051115, "action": "permit"}, {"name": "rule116", "id": 2051116, "action": "permit"}, {"name": "rule117", "id": 2051117, "action": "permit"}, {"name": "rule118", "id": 2051118, "action": "permit"}, {"name": "rule119", "id": 2051119, "action": "permit"}, {"name": "rule120", "id": 2051120, "action": "permit"}, {"name": "rule121", "id": 2051121, "action": "permit"}, {"name": "rule122", "id": 2051122, "action": "permit"}, {"name": "rule123", "id": 2051123, "action": "permit"}, {"name": "rule124", "id": 2051124, "action": "permit"}, {"name": "rule125", "id": 2051125, "action": "permit"}, {"name": "rule126", "id": 2051126, "action": "permit"}, {"name": "rule127", "id": 2051127, "action": "permit"}]}}]}}}]}]}