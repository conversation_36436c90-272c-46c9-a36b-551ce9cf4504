{"op": "merge", "key_name": "k0", "key_value": [1], "ds": [{"op": "merge", "pos": "remain", "key_name": "k0", "key_value": ["NULL", "running"], "huawei-acl": [{"op": "merge", "huawei-acl:acl": {"op": "merge", "groups": {"op": "merge", "group": [{"identity": "3038", "type": "advance", "rule-advances": {"rule-advance": [{"name": "rule0", "id": 3038000, "action": "permit", "protocol": 0}, {"name": "rule1", "id": 3038001, "action": "permit", "protocol": 0}, {"name": "rule2", "id": 3038002, "action": "permit", "protocol": 0}, {"name": "rule3", "id": 3038003, "action": "permit", "protocol": 0}, {"name": "rule4", "id": 3038004, "action": "permit", "protocol": 0}, {"name": "rule5", "id": 3038005, "action": "permit", "protocol": 0}, {"name": "rule6", "id": 3038006, "action": "permit", "protocol": 0}, {"name": "rule7", "id": 3038007, "action": "permit", "protocol": 0}, {"name": "rule8", "id": 3038008, "action": "permit", "protocol": 0}, {"name": "rule9", "id": 3038009, "action": "permit", "protocol": 0}, {"name": "rule10", "id": 3038010, "action": "permit", "protocol": 0}, {"name": "rule11", "id": 3038011, "action": "permit", "protocol": 0}, {"name": "rule12", "id": 3038012, "action": "permit", "protocol": 0}, {"name": "rule13", "id": 3038013, "action": "permit", "protocol": 0}, {"name": "rule14", "id": 3038014, "action": "permit", "protocol": 0}, {"name": "rule15", "id": 3038015, "action": "permit", "protocol": 0}, {"name": "rule16", "id": 3038016, "action": "permit", "protocol": 0}, {"name": "rule17", "id": 3038017, "action": "permit", "protocol": 0}, {"name": "rule18", "id": 3038018, "action": "permit", "protocol": 0}, {"name": "rule19", "id": 3038019, "action": "permit", "protocol": 0}, {"name": "rule20", "id": 3038020, "action": "permit", "protocol": 0}, {"name": "rule21", "id": 3038021, "action": "permit", "protocol": 0}, {"name": "rule22", "id": 3038022, "action": "permit", "protocol": 0}, {"name": "rule23", "id": 3038023, "action": "permit", "protocol": 0}, {"name": "rule24", "id": 3038024, "action": "permit", "protocol": 0}, {"name": "rule25", "id": 3038025, "action": "permit", "protocol": 0}, {"name": "rule26", "id": 3038026, "action": "permit", "protocol": 0}, {"name": "rule27", "id": 3038027, "action": "permit", "protocol": 0}, {"name": "rule28", "id": 3038028, "action": "permit", "protocol": 0}, {"name": "rule29", "id": 3038029, "action": "permit", "protocol": 0}, {"name": "rule30", "id": 3038030, "action": "permit", "protocol": 0}, {"name": "rule31", "id": 3038031, "action": "permit", "protocol": 0}, {"name": "rule32", "id": 3038032, "action": "permit", "protocol": 0}, {"name": "rule33", "id": 3038033, "action": "permit", "protocol": 0}, {"name": "rule34", "id": 3038034, "action": "permit", "protocol": 0}, {"name": "rule35", "id": 3038035, "action": "permit", "protocol": 0}, {"name": "rule36", "id": 3038036, "action": "permit", "protocol": 0}, {"name": "rule37", "id": 3038037, "action": "permit", "protocol": 0}, {"name": "rule38", "id": 3038038, "action": "permit", "protocol": 0}, {"name": "rule39", "id": 3038039, "action": "permit", "protocol": 0}, {"name": "rule40", "id": 3038040, "action": "permit", "protocol": 0}, {"name": "rule41", "id": 3038041, "action": "permit", "protocol": 0}, {"name": "rule42", "id": 3038042, "action": "permit", "protocol": 0}, {"name": "rule43", "id": 3038043, "action": "permit", "protocol": 0}, {"name": "rule44", "id": 3038044, "action": "permit", "protocol": 0}, {"name": "rule45", "id": 3038045, "action": "permit", "protocol": 0}, {"name": "rule46", "id": 3038046, "action": "permit", "protocol": 0}, {"name": "rule47", "id": 3038047, "action": "permit", "protocol": 0}, {"name": "rule48", "id": 3038048, "action": "permit", "protocol": 0}, {"name": "rule49", "id": 3038049, "action": "permit", "protocol": 0}, {"name": "rule50", "id": 3038050, "action": "permit", "protocol": 0}, {"name": "rule51", "id": 3038051, "action": "permit", "protocol": 0}, {"name": "rule52", "id": 3038052, "action": "permit", "protocol": 0}, {"name": "rule53", "id": 3038053, "action": "permit", "protocol": 0}, {"name": "rule54", "id": 3038054, "action": "permit", "protocol": 0}, {"name": "rule55", "id": 3038055, "action": "permit", "protocol": 0}, {"name": "rule56", "id": 3038056, "action": "permit", "protocol": 0}, {"name": "rule57", "id": 3038057, "action": "permit", "protocol": 0}, {"name": "rule58", "id": 3038058, "action": "permit", "protocol": 0}, {"name": "rule59", "id": 3038059, "action": "permit", "protocol": 0}, {"name": "rule60", "id": 3038060, "action": "permit", "protocol": 0}, {"name": "rule61", "id": 3038061, "action": "permit", "protocol": 0}, {"name": "rule62", "id": 3038062, "action": "permit", "protocol": 0}, {"name": "rule63", "id": 3038063, "action": "permit", "protocol": 0}, {"name": "rule64", "id": 3038064, "action": "permit", "protocol": 0}, {"name": "rule65", "id": 3038065, "action": "permit", "protocol": 0}, {"name": "rule66", "id": 3038066, "action": "permit", "protocol": 0}, {"name": "rule67", "id": 3038067, "action": "permit", "protocol": 0}, {"name": "rule68", "id": 3038068, "action": "permit", "protocol": 0}, {"name": "rule69", "id": 3038069, "action": "permit", "protocol": 0}, {"name": "rule70", "id": 3038070, "action": "permit", "protocol": 0}, {"name": "rule71", "id": 3038071, "action": "permit", "protocol": 0}, {"name": "rule72", "id": 3038072, "action": "permit", "protocol": 0}, {"name": "rule73", "id": 3038073, "action": "permit", "protocol": 0}, {"name": "rule74", "id": 3038074, "action": "permit", "protocol": 0}, {"name": "rule75", "id": 3038075, "action": "permit", "protocol": 0}, {"name": "rule76", "id": 3038076, "action": "permit", "protocol": 0}, {"name": "rule77", "id": 3038077, "action": "permit", "protocol": 0}, {"name": "rule78", "id": 3038078, "action": "permit", "protocol": 0}, {"name": "rule79", "id": 3038079, "action": "permit", "protocol": 0}, {"name": "rule80", "id": 3038080, "action": "permit", "protocol": 0}, {"name": "rule81", "id": 3038081, "action": "permit", "protocol": 0}, {"name": "rule82", "id": 3038082, "action": "permit", "protocol": 0}, {"name": "rule83", "id": 3038083, "action": "permit", "protocol": 0}, {"name": "rule84", "id": 3038084, "action": "permit", "protocol": 0}, {"name": "rule85", "id": 3038085, "action": "permit", "protocol": 0}, {"name": "rule86", "id": 3038086, "action": "permit", "protocol": 0}, {"name": "rule87", "id": 3038087, "action": "permit", "protocol": 0}, {"name": "rule88", "id": 3038088, "action": "permit", "protocol": 0}, {"name": "rule89", "id": 3038089, "action": "permit", "protocol": 0}, {"name": "rule90", "id": 3038090, "action": "permit", "protocol": 0}, {"name": "rule91", "id": 3038091, "action": "permit", "protocol": 0}, {"name": "rule92", "id": 3038092, "action": "permit", "protocol": 0}, {"name": "rule93", "id": 3038093, "action": "permit", "protocol": 0}, {"name": "rule94", "id": 3038094, "action": "permit", "protocol": 0}, {"name": "rule95", "id": 3038095, "action": "permit", "protocol": 0}, {"name": "rule96", "id": 3038096, "action": "permit", "protocol": 0}, {"name": "rule97", "id": 3038097, "action": "permit", "protocol": 0}, {"name": "rule98", "id": 3038098, "action": "permit", "protocol": 0}, {"name": "rule99", "id": 3038099, "action": "permit", "protocol": 0}, {"name": "rule100", "id": 3038100, "action": "permit", "protocol": 0}, {"name": "rule101", "id": 3038101, "action": "permit", "protocol": 0}, {"name": "rule102", "id": 3038102, "action": "permit", "protocol": 0}, {"name": "rule103", "id": 3038103, "action": "permit", "protocol": 0}, {"name": "rule104", "id": 3038104, "action": "permit", "protocol": 0}, {"name": "rule105", "id": 3038105, "action": "permit", "protocol": 0}, {"name": "rule106", "id": 3038106, "action": "permit", "protocol": 0}, {"name": "rule107", "id": 3038107, "action": "permit", "protocol": 0}, {"name": "rule108", "id": 3038108, "action": "permit", "protocol": 0}, {"name": "rule109", "id": 3038109, "action": "permit", "protocol": 0}, {"name": "rule110", "id": 3038110, "action": "permit", "protocol": 0}, {"name": "rule111", "id": 3038111, "action": "permit", "protocol": 0}, {"name": "rule112", "id": 3038112, "action": "permit", "protocol": 0}, {"name": "rule113", "id": 3038113, "action": "permit", "protocol": 0}, {"name": "rule114", "id": 3038114, "action": "permit", "protocol": 0}, {"name": "rule115", "id": 3038115, "action": "permit", "protocol": 0}, {"name": "rule116", "id": 3038116, "action": "permit", "protocol": 0}, {"name": "rule117", "id": 3038117, "action": "permit", "protocol": 0}, {"name": "rule118", "id": 3038118, "action": "permit", "protocol": 0}, {"name": "rule119", "id": 3038119, "action": "permit", "protocol": 0}, {"name": "rule120", "id": 3038120, "action": "permit", "protocol": 0}, {"name": "rule121", "id": 3038121, "action": "permit", "protocol": 0}, {"name": "rule122", "id": 3038122, "action": "permit", "protocol": 0}, {"name": "rule123", "id": 3038123, "action": "permit", "protocol": 0}, {"name": "rule124", "id": 3038124, "action": "permit", "protocol": 0}, {"name": "rule125", "id": 3038125, "action": "permit", "protocol": 0}, {"name": "rule126", "id": 3038126, "action": "permit", "protocol": 0}, {"name": "rule127", "id": 3038127, "action": "permit", "protocol": 0}]}}, {"identity": "2038", "type": "basic", "rule-basics": {"rule-basic": [{"name": "rule0", "id": 2038000, "action": "permit"}, {"name": "rule1", "id": 2038001, "action": "permit"}, {"name": "rule2", "id": 2038002, "action": "permit"}, {"name": "rule3", "id": 2038003, "action": "permit"}, {"name": "rule4", "id": 2038004, "action": "permit"}, {"name": "rule5", "id": 2038005, "action": "permit"}, {"name": "rule6", "id": 2038006, "action": "permit"}, {"name": "rule7", "id": 2038007, "action": "permit"}, {"name": "rule8", "id": 2038008, "action": "permit"}, {"name": "rule9", "id": 2038009, "action": "permit"}, {"name": "rule10", "id": 2038010, "action": "permit"}, {"name": "rule11", "id": 2038011, "action": "permit"}, {"name": "rule12", "id": 2038012, "action": "permit"}, {"name": "rule13", "id": 2038013, "action": "permit"}, {"name": "rule14", "id": 2038014, "action": "permit"}, {"name": "rule15", "id": 2038015, "action": "permit"}, {"name": "rule16", "id": 2038016, "action": "permit"}, {"name": "rule17", "id": 2038017, "action": "permit"}, {"name": "rule18", "id": 2038018, "action": "permit"}, {"name": "rule19", "id": 2038019, "action": "permit"}, {"name": "rule20", "id": 2038020, "action": "permit"}, {"name": "rule21", "id": 2038021, "action": "permit"}, {"name": "rule22", "id": 2038022, "action": "permit"}, {"name": "rule23", "id": 2038023, "action": "permit"}, {"name": "rule24", "id": 2038024, "action": "permit"}, {"name": "rule25", "id": 2038025, "action": "permit"}, {"name": "rule26", "id": 2038026, "action": "permit"}, {"name": "rule27", "id": 2038027, "action": "permit"}, {"name": "rule28", "id": 2038028, "action": "permit"}, {"name": "rule29", "id": 2038029, "action": "permit"}, {"name": "rule30", "id": 2038030, "action": "permit"}, {"name": "rule31", "id": 2038031, "action": "permit"}, {"name": "rule32", "id": 2038032, "action": "permit"}, {"name": "rule33", "id": 2038033, "action": "permit"}, {"name": "rule34", "id": 2038034, "action": "permit"}, {"name": "rule35", "id": 2038035, "action": "permit"}, {"name": "rule36", "id": 2038036, "action": "permit"}, {"name": "rule37", "id": 2038037, "action": "permit"}, {"name": "rule38", "id": 2038038, "action": "permit"}, {"name": "rule39", "id": 2038039, "action": "permit"}, {"name": "rule40", "id": 2038040, "action": "permit"}, {"name": "rule41", "id": 2038041, "action": "permit"}, {"name": "rule42", "id": 2038042, "action": "permit"}, {"name": "rule43", "id": 2038043, "action": "permit"}, {"name": "rule44", "id": 2038044, "action": "permit"}, {"name": "rule45", "id": 2038045, "action": "permit"}, {"name": "rule46", "id": 2038046, "action": "permit"}, {"name": "rule47", "id": 2038047, "action": "permit"}, {"name": "rule48", "id": 2038048, "action": "permit"}, {"name": "rule49", "id": 2038049, "action": "permit"}, {"name": "rule50", "id": 2038050, "action": "permit"}, {"name": "rule51", "id": 2038051, "action": "permit"}, {"name": "rule52", "id": 2038052, "action": "permit"}, {"name": "rule53", "id": 2038053, "action": "permit"}, {"name": "rule54", "id": 2038054, "action": "permit"}, {"name": "rule55", "id": 2038055, "action": "permit"}, {"name": "rule56", "id": 2038056, "action": "permit"}, {"name": "rule57", "id": 2038057, "action": "permit"}, {"name": "rule58", "id": 2038058, "action": "permit"}, {"name": "rule59", "id": 2038059, "action": "permit"}, {"name": "rule60", "id": 2038060, "action": "permit"}, {"name": "rule61", "id": 2038061, "action": "permit"}, {"name": "rule62", "id": 2038062, "action": "permit"}, {"name": "rule63", "id": 2038063, "action": "permit"}, {"name": "rule64", "id": 2038064, "action": "permit"}, {"name": "rule65", "id": 2038065, "action": "permit"}, {"name": "rule66", "id": 2038066, "action": "permit"}, {"name": "rule67", "id": 2038067, "action": "permit"}, {"name": "rule68", "id": 2038068, "action": "permit"}, {"name": "rule69", "id": 2038069, "action": "permit"}, {"name": "rule70", "id": 2038070, "action": "permit"}, {"name": "rule71", "id": 2038071, "action": "permit"}, {"name": "rule72", "id": 2038072, "action": "permit"}, {"name": "rule73", "id": 2038073, "action": "permit"}, {"name": "rule74", "id": 2038074, "action": "permit"}, {"name": "rule75", "id": 2038075, "action": "permit"}, {"name": "rule76", "id": 2038076, "action": "permit"}, {"name": "rule77", "id": 2038077, "action": "permit"}, {"name": "rule78", "id": 2038078, "action": "permit"}, {"name": "rule79", "id": 2038079, "action": "permit"}, {"name": "rule80", "id": 2038080, "action": "permit"}, {"name": "rule81", "id": 2038081, "action": "permit"}, {"name": "rule82", "id": 2038082, "action": "permit"}, {"name": "rule83", "id": 2038083, "action": "permit"}, {"name": "rule84", "id": 2038084, "action": "permit"}, {"name": "rule85", "id": 2038085, "action": "permit"}, {"name": "rule86", "id": 2038086, "action": "permit"}, {"name": "rule87", "id": 2038087, "action": "permit"}, {"name": "rule88", "id": 2038088, "action": "permit"}, {"name": "rule89", "id": 2038089, "action": "permit"}, {"name": "rule90", "id": 2038090, "action": "permit"}, {"name": "rule91", "id": 2038091, "action": "permit"}, {"name": "rule92", "id": 2038092, "action": "permit"}, {"name": "rule93", "id": 2038093, "action": "permit"}, {"name": "rule94", "id": 2038094, "action": "permit"}, {"name": "rule95", "id": 2038095, "action": "permit"}, {"name": "rule96", "id": 2038096, "action": "permit"}, {"name": "rule97", "id": 2038097, "action": "permit"}, {"name": "rule98", "id": 2038098, "action": "permit"}, {"name": "rule99", "id": 2038099, "action": "permit"}, {"name": "rule100", "id": 2038100, "action": "permit"}, {"name": "rule101", "id": 2038101, "action": "permit"}, {"name": "rule102", "id": 2038102, "action": "permit"}, {"name": "rule103", "id": 2038103, "action": "permit"}, {"name": "rule104", "id": 2038104, "action": "permit"}, {"name": "rule105", "id": 2038105, "action": "permit"}, {"name": "rule106", "id": 2038106, "action": "permit"}, {"name": "rule107", "id": 2038107, "action": "permit"}, {"name": "rule108", "id": 2038108, "action": "permit"}, {"name": "rule109", "id": 2038109, "action": "permit"}, {"name": "rule110", "id": 2038110, "action": "permit"}, {"name": "rule111", "id": 2038111, "action": "permit"}, {"name": "rule112", "id": 2038112, "action": "permit"}, {"name": "rule113", "id": 2038113, "action": "permit"}, {"name": "rule114", "id": 2038114, "action": "permit"}, {"name": "rule115", "id": 2038115, "action": "permit"}, {"name": "rule116", "id": 2038116, "action": "permit"}, {"name": "rule117", "id": 2038117, "action": "permit"}, {"name": "rule118", "id": 2038118, "action": "permit"}, {"name": "rule119", "id": 2038119, "action": "permit"}, {"name": "rule120", "id": 2038120, "action": "permit"}, {"name": "rule121", "id": 2038121, "action": "permit"}, {"name": "rule122", "id": 2038122, "action": "permit"}, {"name": "rule123", "id": 2038123, "action": "permit"}, {"name": "rule124", "id": 2038124, "action": "permit"}, {"name": "rule125", "id": 2038125, "action": "permit"}, {"name": "rule126", "id": 2038126, "action": "permit"}, {"name": "rule127", "id": 2038127, "action": "permit"}]}}, {"identity": "4038", "type": "link", "rule-ethernets": {"rule-ethernet": [{"name": "rule0", "id": 4038000, "action": "permit", "vlan-id": 1}, {"name": "rule1", "id": 4038001, "action": "permit", "vlan-id": 1}, {"name": "rule2", "id": 4038002, "action": "permit", "vlan-id": 1}, {"name": "rule3", "id": 4038003, "action": "permit", "vlan-id": 1}, {"name": "rule4", "id": 4038004, "action": "permit", "vlan-id": 1}, {"name": "rule5", "id": 4038005, "action": "permit", "vlan-id": 1}, {"name": "rule6", "id": 4038006, "action": "permit", "vlan-id": 1}, {"name": "rule7", "id": 4038007, "action": "permit", "vlan-id": 1}, {"name": "rule8", "id": 4038008, "action": "permit", "vlan-id": 1}, {"name": "rule9", "id": 4038009, "action": "permit", "vlan-id": 1}, {"name": "rule10", "id": 4038010, "action": "permit", "vlan-id": 1}, {"name": "rule11", "id": 4038011, "action": "permit", "vlan-id": 1}, {"name": "rule12", "id": 4038012, "action": "permit", "vlan-id": 1}, {"name": "rule13", "id": 4038013, "action": "permit", "vlan-id": 1}, {"name": "rule14", "id": 4038014, "action": "permit", "vlan-id": 1}, {"name": "rule15", "id": 4038015, "action": "permit", "vlan-id": 1}, {"name": "rule16", "id": 4038016, "action": "permit", "vlan-id": 1}, {"name": "rule17", "id": 4038017, "action": "permit", "vlan-id": 1}, {"name": "rule18", "id": 4038018, "action": "permit", "vlan-id": 1}, {"name": "rule19", "id": 4038019, "action": "permit", "vlan-id": 1}, {"name": "rule20", "id": 4038020, "action": "permit", "vlan-id": 1}, {"name": "rule21", "id": 4038021, "action": "permit", "vlan-id": 1}, {"name": "rule22", "id": 4038022, "action": "permit", "vlan-id": 1}, {"name": "rule23", "id": 4038023, "action": "permit", "vlan-id": 1}, {"name": "rule24", "id": 4038024, "action": "permit", "vlan-id": 1}, {"name": "rule25", "id": 4038025, "action": "permit", "vlan-id": 1}, {"name": "rule26", "id": 4038026, "action": "permit", "vlan-id": 1}, {"name": "rule27", "id": 4038027, "action": "permit", "vlan-id": 1}, {"name": "rule28", "id": 4038028, "action": "permit", "vlan-id": 1}, {"name": "rule29", "id": 4038029, "action": "permit", "vlan-id": 1}, {"name": "rule30", "id": 4038030, "action": "permit", "vlan-id": 1}, {"name": "rule31", "id": 4038031, "action": "permit", "vlan-id": 1}, {"name": "rule32", "id": 4038032, "action": "permit", "vlan-id": 1}, {"name": "rule33", "id": 4038033, "action": "permit", "vlan-id": 1}, {"name": "rule34", "id": 4038034, "action": "permit", "vlan-id": 1}, {"name": "rule35", "id": 4038035, "action": "permit", "vlan-id": 1}, {"name": "rule36", "id": 4038036, "action": "permit", "vlan-id": 1}, {"name": "rule37", "id": 4038037, "action": "permit", "vlan-id": 1}, {"name": "rule38", "id": 4038038, "action": "permit", "vlan-id": 1}, {"name": "rule39", "id": 4038039, "action": "permit", "vlan-id": 1}, {"name": "rule40", "id": 4038040, "action": "permit", "vlan-id": 1}, {"name": "rule41", "id": 4038041, "action": "permit", "vlan-id": 1}, {"name": "rule42", "id": 4038042, "action": "permit", "vlan-id": 1}, {"name": "rule43", "id": 4038043, "action": "permit", "vlan-id": 1}, {"name": "rule44", "id": 4038044, "action": "permit", "vlan-id": 1}, {"name": "rule45", "id": 4038045, "action": "permit", "vlan-id": 1}, {"name": "rule46", "id": 4038046, "action": "permit", "vlan-id": 1}, {"name": "rule47", "id": 4038047, "action": "permit", "vlan-id": 1}, {"name": "rule48", "id": 4038048, "action": "permit", "vlan-id": 1}, {"name": "rule49", "id": 4038049, "action": "permit", "vlan-id": 1}, {"name": "rule50", "id": 4038050, "action": "permit", "vlan-id": 1}, {"name": "rule51", "id": 4038051, "action": "permit", "vlan-id": 1}, {"name": "rule52", "id": 4038052, "action": "permit", "vlan-id": 1}, {"name": "rule53", "id": 4038053, "action": "permit", "vlan-id": 1}, {"name": "rule54", "id": 4038054, "action": "permit", "vlan-id": 1}, {"name": "rule55", "id": 4038055, "action": "permit", "vlan-id": 1}, {"name": "rule56", "id": 4038056, "action": "permit", "vlan-id": 1}, {"name": "rule57", "id": 4038057, "action": "permit", "vlan-id": 1}, {"name": "rule58", "id": 4038058, "action": "permit", "vlan-id": 1}, {"name": "rule59", "id": 4038059, "action": "permit", "vlan-id": 1}, {"name": "rule60", "id": 4038060, "action": "permit", "vlan-id": 1}, {"name": "rule61", "id": 4038061, "action": "permit", "vlan-id": 1}, {"name": "rule62", "id": 4038062, "action": "permit", "vlan-id": 1}, {"name": "rule63", "id": 4038063, "action": "permit", "vlan-id": 1}, {"name": "rule64", "id": 4038064, "action": "permit", "vlan-id": 1}, {"name": "rule65", "id": 4038065, "action": "permit", "vlan-id": 1}, {"name": "rule66", "id": 4038066, "action": "permit", "vlan-id": 1}, {"name": "rule67", "id": 4038067, "action": "permit", "vlan-id": 1}, {"name": "rule68", "id": 4038068, "action": "permit", "vlan-id": 1}, {"name": "rule69", "id": 4038069, "action": "permit", "vlan-id": 1}, {"name": "rule70", "id": 4038070, "action": "permit", "vlan-id": 1}, {"name": "rule71", "id": 4038071, "action": "permit", "vlan-id": 1}, {"name": "rule72", "id": 4038072, "action": "permit", "vlan-id": 1}, {"name": "rule73", "id": 4038073, "action": "permit", "vlan-id": 1}, {"name": "rule74", "id": 4038074, "action": "permit", "vlan-id": 1}, {"name": "rule75", "id": 4038075, "action": "permit", "vlan-id": 1}, {"name": "rule76", "id": 4038076, "action": "permit", "vlan-id": 1}, {"name": "rule77", "id": 4038077, "action": "permit", "vlan-id": 1}, {"name": "rule78", "id": 4038078, "action": "permit", "vlan-id": 1}, {"name": "rule79", "id": 4038079, "action": "permit", "vlan-id": 1}, {"name": "rule80", "id": 4038080, "action": "permit", "vlan-id": 1}, {"name": "rule81", "id": 4038081, "action": "permit", "vlan-id": 1}, {"name": "rule82", "id": 4038082, "action": "permit", "vlan-id": 1}, {"name": "rule83", "id": 4038083, "action": "permit", "vlan-id": 1}, {"name": "rule84", "id": 4038084, "action": "permit", "vlan-id": 1}, {"name": "rule85", "id": 4038085, "action": "permit", "vlan-id": 1}, {"name": "rule86", "id": 4038086, "action": "permit", "vlan-id": 1}, {"name": "rule87", "id": 4038087, "action": "permit", "vlan-id": 1}, {"name": "rule88", "id": 4038088, "action": "permit", "vlan-id": 1}, {"name": "rule89", "id": 4038089, "action": "permit", "vlan-id": 1}, {"name": "rule90", "id": 4038090, "action": "permit", "vlan-id": 1}, {"name": "rule91", "id": 4038091, "action": "permit", "vlan-id": 1}, {"name": "rule92", "id": 4038092, "action": "permit", "vlan-id": 1}, {"name": "rule93", "id": 4038093, "action": "permit", "vlan-id": 1}, {"name": "rule94", "id": 4038094, "action": "permit", "vlan-id": 1}, {"name": "rule95", "id": 4038095, "action": "permit", "vlan-id": 1}, {"name": "rule96", "id": 4038096, "action": "permit", "vlan-id": 1}, {"name": "rule97", "id": 4038097, "action": "permit", "vlan-id": 1}, {"name": "rule98", "id": 4038098, "action": "permit", "vlan-id": 1}, {"name": "rule99", "id": 4038099, "action": "permit", "vlan-id": 1}, {"name": "rule100", "id": 4038100, "action": "permit", "vlan-id": 1}, {"name": "rule101", "id": 4038101, "action": "permit", "vlan-id": 1}, {"name": "rule102", "id": 4038102, "action": "permit", "vlan-id": 1}, {"name": "rule103", "id": 4038103, "action": "permit", "vlan-id": 1}, {"name": "rule104", "id": 4038104, "action": "permit", "vlan-id": 1}, {"name": "rule105", "id": 4038105, "action": "permit", "vlan-id": 1}, {"name": "rule106", "id": 4038106, "action": "permit", "vlan-id": 1}, {"name": "rule107", "id": 4038107, "action": "permit", "vlan-id": 1}, {"name": "rule108", "id": 4038108, "action": "permit", "vlan-id": 1}, {"name": "rule109", "id": 4038109, "action": "permit", "vlan-id": 1}, {"name": "rule110", "id": 4038110, "action": "permit", "vlan-id": 1}, {"name": "rule111", "id": 4038111, "action": "permit", "vlan-id": 1}, {"name": "rule112", "id": 4038112, "action": "permit", "vlan-id": 1}, {"name": "rule113", "id": 4038113, "action": "permit", "vlan-id": 1}, {"name": "rule114", "id": 4038114, "action": "permit", "vlan-id": 1}, {"name": "rule115", "id": 4038115, "action": "permit", "vlan-id": 1}, {"name": "rule116", "id": 4038116, "action": "permit", "vlan-id": 1}, {"name": "rule117", "id": 4038117, "action": "permit", "vlan-id": 1}, {"name": "rule118", "id": 4038118, "action": "permit", "vlan-id": 1}, {"name": "rule119", "id": 4038119, "action": "permit", "vlan-id": 1}, {"name": "rule120", "id": 4038120, "action": "permit", "vlan-id": 1}, {"name": "rule121", "id": 4038121, "action": "permit", "vlan-id": 1}, {"name": "rule122", "id": 4038122, "action": "permit", "vlan-id": 1}, {"name": "rule123", "id": 4038123, "action": "permit", "vlan-id": 1}, {"name": "rule124", "id": 4038124, "action": "permit", "vlan-id": 1}, {"name": "rule125", "id": 4038125, "action": "permit", "vlan-id": 1}, {"name": "rule126", "id": 4038126, "action": "permit", "vlan-id": 1}, {"name": "rule127", "id": 4038127, "action": "permit", "vlan-id": 1}]}}]}}}]}]}